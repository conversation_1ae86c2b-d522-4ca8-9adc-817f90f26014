define('PUBLIC_ROOT/modules/common/core', function(require, exports, module) {
    // 商品页公用变量
    var g = {}
    var cfg = pageConfig.product

    // 常用变量
    g.sku = cfg.skuid

    g.cat = cfg.cat
    g.url = cfg.href
    g.src = cfg.src
    g.pType = cfg.pType
    g.name = cfg.name
    g.mdPerfix = cfg.pType == 1 ? 'CR' : 'GR'
    g.mbPerfix = cfg.pType == 1 ? '3C' : 'GM'
    g.mp = cfg.mp
    g.jp = cfg.jp

    g.orginSku = pageConfig.product.orginSkuid || g.sku

    g.wideVersion = pageConfig.wideVersion && pageConfig.compatible

    //上下柜状态 state:0、1、2、10；0:下柜，1：上柜，2：预发布，10：pop删除
    //g.itemDisabled = typeof pageConfig.product.warestatus !== 'undefined'
    //    && (pageConfig.product.warestatus === 0 || pageConfig.product.warestatus === 10);

    g.itemDisabled = cfg.isOver

    g.isPOPSku = function(sku) {
        return (
            (sku >= 1000000000 && sku <= 1999999999) ||
            (sku >= 10000000001 && sku <= 99999999999)
        )
    }

    // 商品类型判断
    var sku = g.sku
    g.isPop = g.isPOPSku(sku) //POP
    g.isFCS = cfg.fcs; //fcs
    g.isJd = !!g.isFCS || !g.isPop //自营
    g.isSelf = g.isJd //自营
    g.is3C =
        g.cat[0] == 652 ||
        g.cat[0] == 670 ||
        g.cat[0] == 737 ||
        g.cat[0] == 9987 //3C
    g.isCellphone = g.cat[2] == 655 || g.cat[2] == 6881 || g.cat[2] == 6882 //手机
    g.isCompleteMachine = g.cat[0] == 670 //电脑整机
    g.isVideo = g.cat[0] == 652 //摄影摄像
    g.isPopBook = g.isPop && g.cat[0] == 1713 //pop图书
    g.isSelfBook =
        (sku >= 10000000 && sku <= 19999999) ||
        (sku >= 110000000001 && sku <= 139999999999) //自营图书
    g.isBook = g.isPopBook || g.isSelfBook //图书
    g.isPopMvd =
        g.isPop && (g.cat[0] == 4051 || g.cat[0] == 4052 || g.cat[0] == 4053) //POP MVD
    g.isSelfMvd =
        (sku >= 20000000 && sku <= 29999999) ||
        (sku >= 140000000001 && sku <= 149999999999) //自营MVD
    g.isMvd = g.isPopMvd || g.isSelfMvd

    g.disableAddToCart = $('#InitCartUrl').hasClass('btn-disable')

    g.isArray = function(obj) {
        return Object.prototype.toString.call(obj) === '[object Array]'
    }
    g.isObject = function(obj) {
        return Object.prototype.toString.call(obj) === '[object Object]'
    }
    g.isEmptyObject = function(obj) {
        var name
        for (name in obj) {
            return false
        }
        return true
    }

    // 字段为空判断，支持null, undefined, "", []
    g.isNothing = function(obj) {
        if (g.isArray(obj)) {
            return obj.length < 1
        } else {
            return !obj
        }
    }
    g.getRandomArray = function(m, len) {
        len = len || 1
        m.sort(function() {
            return Math.random() - 0.5
        })
        return m.slice(0, len)
    }

    g.toFixed = function(number, precision) {
        var multiplier = Math.pow(10, precision + 1)
        var wholeNumber = Math.round(number * multiplier).toString()
        var length = wholeNumber.length - 1

        wholeNumber = wholeNumber.substr(0, length)
        return [
            wholeNumber.substr(0, length - precision),
            wholeNumber.substr(-precision)
        ].join('.')
    }

    g.originBuyUrl = $('#InitCartUrl').attr('href')

    g.formatPrice = function(price) {
        var priceNum = parseFloat(price)
        if (isNaN(priceNum) || priceNum <= 0) {
            return '暂无报价'
        } else {
            return priceNum.toFixed(2)
        }
    }

    g.discount = function(jp, mp, tpl) {
        tpl = tpl || '[{num}折]'
        if (typeof jp === 'undefined' || typeof mp === 'undefined') {
            return
        }
        jp = parseFloat(jp)
        mp = parseFloat(mp)

        if (!jp || !mp) {
            return ''
        }
        //float精度16
        var discount = Math.ceil((jp / mp * 100).toFixed(1))
        return discount ? tpl.replace('{num}', parseFloat(discount / 10)) : ''
    }

    g.triggerLazyImg = function($el, prefix) {
        $el.find('img').each(function() {
            var $this = $(this)
            var originSrc = $this.attr(prefix)

            if (!$this.attr('src')) {
                $this.attr('src', originSrc)
                $this.removeAttr(prefix)
                $this.removeClass('err-product loading-style2')
            }
        })
    }

    // 获取用户等级,原名getCustomerLevel
    g.getNewUserLevel = function(level) {
        switch (level) {
        case 50:
            return '注册用户'
        case 56:
            return '铜牌用户'
        case 59:
            return '注册用户'
        case 60:
            return '银牌用户'
        case 61:
            return '银牌用户'
        case 62:
            return '金牌用户'
        case 63:
            return '钻石用户'
        case 64:
            return '经销商'
        case 110:
            return 'VIP'
        case 66:
            return '京东员工'
        case -1:
            return '未注册'
        case 88:
            return '钻石用户'
        case 90:
            return '企业用户'
        case 103:
            return '钻石用户'
        case 104:
            return '钻石用户'
        case 105:
            return '钻石用户'
        }
        return '未知'
    }

    // 模拟发送请求
    g.sendRequest = function(src) {
        var img = new Image()
        img.setAttribute('src', src)

        img = null
    }
    // 手动添加新埋点
    g.clsLog = function(id, action, sku, csku, index) {
        var img = new Image()
        var src =
            '//mercury.jd.com/log.gif?t=rec.' +
            id +
            '&v=' +
            encodeURIComponent(
                'src=rec$action=' +
                    action +
                    '$enb=1$sku=' +
                    sku +
                    '$csku=' +
                    csku +
                    '$index=' +
                    (index || 0) +
                    '$expid=0'
            ) +
            '&m=UA-J2011-1&ref=' +
            encodeURIComponent(document.referrer) +
            '&random=' +
            Math.random()
        img.setAttribute('src', src)
    }

    // 触发某个特殊属性
    g.onAttr = function onAttr(attr, exist, notExist) {
        var attrs = cfg.specialAttrs
        var isStr = typeof attr === 'string'
        var len = attrs.length

        exist = exist || function() {}
        notExist = notExist || function() {}

        if (attrs && attrs.length) {
            for (var i = 0; i < len; i++) {
                // 字符串精准匹配，正则匹配
                if (isStr) {
                    if (attrs[i] === attr) {
                        exist(attrs[i])
                        return true
                    }
                } else {
                    if (attr.test(attrs[i])) {
                        exist(attrs[i])
                        return true
                    }
                }
            }
            notExist()
            return false
        }
        notExist()
        return false
    }

    /**
     * 计算出购买组合价格
     */
    g.calPrice = function(opts) {
        var sku = opts.sku
        var input = opts.input
        var inputs = opts.$el.find('input[type="checkbox"]')
        var len = inputs.length
        var $el = opts.$el

        var targetJP = opts.targetJP
        var targetMP = opts.targetMP

        var jp = 0, mp = 0, count = 0, skus = []

        var callback = opts.callback || function() {}

        for (var i = 0; i < len; i++) {
            var currentInput = inputs.eq(i)
            var currSku = currentInput.attr('data-sku')
            var currJP = currentInput.attr('data-jp')
            var currMP = currentInput.attr('data-mp')

            currJP = parseFloat(currJP)
            currMP = parseFloat(currMP)

            if (currentInput.attr('checked')) {
                if (!isNaN(currJP)) {
                    jp += currJP
                } else {
                    return
                }
                if (!isNaN(currMP)) {
                    mp += currMP
                } else {
                    return
                }
                skus.push(currSku)
                count++
            }
        }

        targetJP.html('￥' + (jp > 0 ? jp.toFixed(2) : '暂无报价'))
        targetMP.html('￥' + (mp > 0 ? mp.toFixed(2) : '暂无报价'))

        callback(count, skus)
    }

    // 初始化小图滚动
    g.setScroll = function(selector) {
        var parent = typeof selector == 'string' ? $(selector) : $('body')

        parent.find('.p-scroll').each(function() {
            var scroll = $(this).find('.p-scroll-wrap'),
                next = $(this).find('.p-scroll-next'),
                prev = $(this).find('.p-scroll-prev')

            if (scroll.find('li').length > 4 && $.fn.imgScroll) {
                scroll.imgScroll({
                    showControl: true,
                    width: 30,
                    height: 30,
                    visible: 4,
                    step: 1,
                    prev: prev,
                    next: next
                })
                next.add(prev).show()
            }
        })
    }

    // 缩略图切换变大图
    g.thumbnailSwitch = function(
        thumbnails,
        desImg,
        resSize,
        currClass,
        evtType
    ) {
        var thumbnailList = thumbnails.find('img')
        var eType = evtType || 'mouseover'

        thumbnailList.bind(eType, function() {
            var $this = $(this)
            var $thisSrc = $this.attr('src')
            var resSrc = $thisSrc.replace(/\/n\d\//, resSize)

            desImg.attr('src', resSrc)
            thumbnails.removeClass('curr')
            $this.parent().addClass('curr')
        })
    }

    // 序列化url参数成JSON
    g.serializeUrl = function(url) {
        var sep = url.indexOf('?'),
            link = url.substr(0, sep),
            params = url.substr(sep + 1),
            paramArr = params.split('&'),
            len = paramArr.length,
            i,
            res = {},
            curr,
            key,
            val

        for (i = 0; i < len; i++) {
            curr = paramArr[i].split('=')
            key = curr[0]
            val = curr[1]

            res[key] = val
        }

        return {
            url: link,
            param: res
        }
    }

    // 收集 skus
    g.collectSkus = function($el, targetSelector, prefix) {
        var skus = []

        targetSelector = targetSelector || '.p-price strong'
        prefix = prefix || 'J-p-'

        $el.find(targetSelector).each(function() {
            var cName = $(this).attr('class')

            if (cName) {
                skus.push(cName.replace(prefix, ''))
            }
        })
        return skus
    }

    g.getNum = function() {
        return Number($('#buy-num').val())
    }

    g.Countdown = {
        init: function(seconds, callback) {
            this.seconds = seconds
            this.timer = null

            this.callback = callback || function() {}

            this.loopCount()
        },
        loopCount: function() {
            var _this = this
            var first = _this.formatSeconds(_this.seconds)

            _this.callback(first)
            this.timer = setInterval(function() {
                var res = _this.formatSeconds(_this.seconds)

                if (res.d === 0 && res.h === 0 && res.m === 0 && res.s === 0) {
                    clearInterval(_this.timer)
                } else {
                    _this.seconds--
                }

                _this.callback(res)
            }, 1000)
        },
        formatSeconds: function(seconds) {
            var days = Math.floor(seconds / 86400)
            var hours = Math.floor(seconds % 86400 / 3600)
            var minutes = Math.floor(seconds % 86400 % 3600 / 60)
            var seconds = seconds % 86400 % 3600 % 60

            return {
                d: days,
                h: hours,
                m: minutes,
                s: seconds
            }
        }
    }

    // 添加赠品池的url拼装逻辑
    g.getHrefWithGift = function(href, skus) {
        var result = ''

        if (!skus) {
            return href
        }

        // 如果已经选择过
        if (/gids=/.test(href)) {
            var gids = g.serializeUrl(href).param.gids
            result = href.replace('gids=' + gids, 'gids=' + skus.join(','))
        } else {
            result = href + '&gids=' + skus.join(',')
        }

        return result
    }

    // 添加延保url拼装逻辑
    g.getHrefWithYB = function(href, skus) {
        var result = ''

        if (!skus) {
            return href
        }

        // 如果已经选择过
        if (/ybId=/.test(href)) {
            var ybId = g.serializeUrl(href).param.ybId
            if (ybId == '') {
                result = href + skus.join(',')
            } else {
                result = href.replace('ybId=' + ybId, 'ybId=' + skus.join(','))
            }
        } else {
            result = href + '&ybId=' + skus.join(',')
        }
        return result
    }

    // 日志上报
    g.log = function(type, filename, msg) {
        type = type || 'log'

        if (typeof errortracker !== 'undefined') {
            errortracker[type]({
                filename: filename,
                message: msg
            })
        }
    }

    module.exports = g
})
