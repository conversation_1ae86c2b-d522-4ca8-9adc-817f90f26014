define('MOD_ROOT/baitiao/baitiao', function(require, exports, module) {
    var G = require('MOD_ROOT/common/core')
    var Event = require('MOD_ROOT/common/tools/event').Event
    var login = require('JDF_UNIT/login/1.0.0/login')
    var Tools = require('MOD_ROOT/common/tools/tools')
    var GiftPool = require('MOD_ROOT/gift/gift');
    var Conf = require('PUBLIC_ROOT/conf');
    var isExpand = false // 是否展开
    // console.log(element,1818);
    // setTimeout(() =>{
    //
    //     // var height = element.offsetHeight;
    //     console.log(element,1818);
    // },2000)

    require('MOD_ROOT/ETooltips/ETooltips')
    require('JDF_UNIT/trimPath/1.0.0/trimPath')

    function log(msg) {
        if (console && typeof console.log === "function") {
            console.log(msg);
        }
        var element = document.getElementById("dd");
        var height = element.offsetHeight;
        console.log(height);
    }

    var BaiTiao = function(opts, onSelected) {
        this.$el = opts.$el || $('#choose-baitiao')
        this.$btn = opts.$btn || $('#btn-baitiao')
        this.price = ''

        this.$jc = $('#choose-jincai')
        this.$jcBtn = $('#btn-jincai')

        this.cfg = opts.cfg

        this.onSelected = onSelected || function() {}

        this.sku = opts.sku
        this.cat = opts.cat
        this.shopId = opts.cfg.shopId

        this.enable = false
        this.isAva = null

        this.giftParam = '';  // 赠品池数据
        this.giftType  = '';  // 赠品池类型

        // 落地配 id
        this.did = ''

        // 白条购切量标识
        this.ab = false

        // 白条购接口入参
        this.bestPromotion = {}

        this.disabledBT =
            G.onAttr('isXnzt') ||
            // G.onAttr('YuShou') ||
            this.cfg.isYuShou ||
            this.cfg.isBiGouMa ||
            this.cfg.isKO

        //this.URL = '//show.baitiao.jd.com/queryBtPlanInfo.do';
        this.URL = '//btshow.jd.com/queryBtPlanInfo.do'
        this.JSONP_CALLBACK_NAME = 'queryBtPlanInfo'

        window.queryBtPlanInfo = function() {}


        this.hideJCItem()
        this.hideJCBtn()
        this.init()

    }

    BaiTiao.TEMPLATE =
        '\
    <div style="margin-top: 30px" class="dt">白条分期</div>\
    <div id="dd" class="dd">\
        <div id="baitiao-list" class="baitiao-list J-baitiao-list">\
            {for item in planInfos}\
            <div id="a_test" style="margin-bottom: 6px;margin-top: 6px;margin-right: 1px" class="item disabled" \
                clstag="shangpin|keycount|product|baitiaofenqi_${item.plan}_${pageConfig.product.cat.join(\'_\')}" \
                data-snum="${item.plan}">\
                <b></b>\
                <a style="margin-bottom: 0" class="item a_test" href="#none">\
                    <strong class="strong_test">\
                    ${item.mainTitle}\
                    </strong>\
                    <strong class="strong_tests">\
                    {if item.planFee > 0}${item.feeText}：￥${item.planFee}/期{/if}\
                    \{if item.planFee <= 0}${item.feeText}{/if}\
                    </strong>\
                    <span style="display:none;">\
                        {if item.isDiscount}<em>惠</em>{/if} \
                        ${item.feeText}\
                    </span>\
                </a>\
            </div>\
            {/for}\
            {if isDiscountAll}\
            <div class="bt-info-tips hide">\
                <a class="J-icon-hui prom icon fl" href="#none">　</a>\
            </div>\
            {/if}\
            <div class="bt-info-tips hide">\
                <a class="J-bt-tips question icon fl" href="#none">　</a>\
            </div>\
        </div>\
        <div class="baitiao-text-wrap"><div class="baitiao-text J-baitiao-text"></div></div>\
    </div>\
    \<div id="bt-opens" class="bt-opens">\
                <span id="bt-opens-span" class="bt-opens-span">\
                {if isExpand}收起全部\
                {else}\
                展开全部 \
                {/if}\
                </span>\
                <img class="open-open" style="margin-left: 8px" width="10" height="10" src="//img11.360buyimg.com/imagetools/jfs/t1/264116/11/14470/583/6790c6b0F446ef23c/4ae3a1af1d4ac2d4.png" />\
            </div>'

    BaiTiao.prototype = {
        init: function() {
            this.bindEvent()
            this.get()
        },
        bindEvent: function() {
            var _this = this;
            //地址变化price更新后 会重复添加事件，此次undelegate
            this.$el.undelegate('.item', 'click');
            this.$el.delegate('.item', 'click', function() {
                var selected = $(this).hasClass('selected')
                var disabled = $(this).hasClass('disabled')

                if (!disabled) {
                    // _this.$el.find('.item').removeClass('selected')
                    if (selected) {
                        $(this).removeClass('selected')
                        Tools.showTradeUrl(_this) // 隐藏白条按钮需要看看立即展示逻辑
                    } else {
                        $(this).addClass("selected").siblings().removeClass("selected")
                        // $(this).addClass('selected')
                        $("#InitTradeUrl").hide()
                    }

                    _this.select($(this), !selected)
                } else {
                    $(this).remove('selected')
                }
            })
            this.$el.delegate('.J-login', 'click', function() {
                _this.loginIframe()
            })

            // this.$el.delegate('.item', 'mouseenter', function() {
            //     $(this).addClass('hover')
            // })
            // this.$el.delegate('.item', 'mouseleave', function() {
            //     $(this).removeClass('hover')
            // })

            this.$jc.undelegate('click').delegate('.J-jincai-list .item', 'click', $.proxy(this.handleJC, this))

            // Event.addListener('onStockReady', function() {
            //     _this.showItem()
            // })
            // Event.addListener('onNumChange', function() {
            //     if (!_this.disabledBT) {
            //         _this.get()
            //     }
            // })
            
            Event.addListener('onLDPSelected', function(data) {
                _this.did = data.did
            });

            Event.addListener('onHeYueReady', function() {
                _this.showItem()
            })

            Event.addListener('onStockReady', function () {
                // var isZfbt = "" 
                _this.showItem()

                if (_this.isHWAdd()) {
                    _this.$jcBtn.hide()
                    _this.hideJCItem()
                }
                // 白条按钮点击
                _this.$btn.unbind("click").bind('click', function(event){
                    event.preventDefault();
                    // login({
                    //     modal: true,
                    //     complete: function() {
                    //         handleIssueIOU();
                    //     }
                    // });
                     handleIssueIOU();
                });
            });

            ///////////////////// 打白条逻辑处理 /////////////////////

            /// 核查赠品池信息
            function verifyGiftPoolInfo() {
                if ( GiftPool.model && GiftPool.model.hasGift() ) {
                    var result = GiftPool.model.getSelectedResult();
                    if (result) {
                        if (result.giftPoolType == 0) {
                            _this.giftParam = result.gids;
                        } else if (result.giftPoolType == 1) {
                            _this.giftParam = result.gids;
                            _this.giftType = 1;
                        } else {
                            _this.giftParam = '';
                        }
                     } else {
                        GiftPool.view.$el.addClass('item-hl-bg');
                        return false;
                    }
                }
                return true;
            }

            /// 获取用户是否激活白条的信息
            function getUserIouInfo(params, onSuccess, onError) {
                return $.ajax({
                    url: "//btshow.jd.com/queryBtPlanInfo.do",
                    data: $.extend({
                        sku: _this.sku,
                        cId: _this.cat.join(','),
                        num: _this.getNum(),
                        amount: _this.price.p * _this.getNum(),
                        sourceType: 'PC-XQ',
                        shopId: pageConfig.product.venderId,
                        ver: 1,
                        areaId: Tools.getAreaId().areaIds[0],
                        isJd: !G.isPop
                    }, params),
                    dataType: 'jsonp',
                    scriptCharset: 'utf-8',
                    jsonpCallback: _this.JSONP_CALLBACK_NAME,
                    success: function() {
                        if (typeof onSuccess === 'function') {
                            onSuccess.apply(null, arguments);
                        }
                    },
                    error: function() {
                        if (typeof onError === 'function') {
                            onError.apply(null, arguments);
                        }
                    }
                });
            }

            // 加入购物车
            function addToCart(params, onSuccess, onError) {
                return $.ajax({
                    url: "//cart.jd.com/gate.action",
                    data: $.extend({}, params),
                    dataType: 'jsonp',
                    success: function() {
                        if (typeof onSuccess === 'function') {
                            onSuccess.apply(null, arguments);
                        }
                    },
                    error: function() {
                        if (typeof onError === 'function') {
                            onError.apply(null, arguments);
                        }
                    }
                });
            }

            // 收集加车服务的请求参数
            function collectQueryParams() {
                var params = {
                    btg: 1,
                    pid: _this.sku,
                    pcount: _this.getNum(),
                    ptype : 1,
                    did: _this.did
                };

                if (_this.giftType) {
                    params.giftPoolType = _this.giftType;
                }
                
                if (_this.giftParam) {
                    params.gids = _this.giftParam;
                }

                return params;
            }

            // 处理加车动作
            function handleAddToCart() {
                if (!verifyGiftPoolInfo()) {
                    return;
                }
                var source = $("#btn-baitiao").attr("data-source") || "btg"
                if(_this.ab){// 白条购新老接口切量标识
                    var carUrl = $("#InitCartUrl").attr("href")
                    var did = ""
                    var jd3csid =  ""
                    var ybId = ""
                    var jdhsid = ""
                    var bestPromotion = _this.bestPromotion

                    if (/did=/.test(carUrl)) { // 送装服务
                        did =  G.serializeUrl(carUrl).param.did
                    }
                    if (/jd3csid=/.test(carUrl)) { // 平生各种服务
                        jd3csid =  G.serializeUrl(carUrl).param.jd3csid
                    }

                    if (/ybId=/.test(carUrl)) { // 延保服务
                        ybId =  G.serializeUrl(carUrl).param.ybId
                    }

                    if (/jdhsid=/.test(carUrl)) { // 京东服务
                        jdhsid =  G.serializeUrl(carUrl).param.jdhsid
                    }

                    var extFlag = {
                        "did" : did,
                        "fq" : _this.snum,
                    }

                    var relationSkus = {
                        "jd3csid" : jd3csid,
                        "ybId" : ybId,
                        "jdhsid": jdhsid,
                        "giftPoolType":  _this.giftType,// 赠品池类型
                        "gids":  _this.giftParam,// 赠品
                    }

                    // var bestCoupon = bestPromotion && bestPromotion.bestCoupon
                    // var canGetCoupon = {// 可领券入参
                    //     "encryptedKey":bestCoupon && bestCoupon.encryptedKey || "",
                    //     "ruleId": bestCoupon && bestCoupon.ruleId || "",
                    //     "resultType": bestCoupon && bestCoupon.resultType || "",
                    // }

                    // var ext = { // 加车拓展参数
                    //     "purchasePrice": bestPromotion && bestPromotion.finalPrice || "", // 到手价入参
                    //     "canGetCoupon": bestPromotion && bestPromotion.bestCoupon || "", // 可领券入参
                    // }
                    
                    // 入参 文档：https://joyspace.jd.com/pages/LlqSuThwCeYndznpLaMU
                    var paramJson = {
                        "serInfo": {
                            "area": Tools.getAreaId().areaIds.join('_'),
                            "user-key": Tools.getCookieNew("user-key") || '',
                        },
                        "directOperation": {
                            "source": source,
                            "gateType": 6,// 白条购加车类型：6
                            "theSkus": [{
                                "skuId": pageConfig.product && pageConfig.product.skuid,
                                "num":  $("#buy-num").val() || "1", // 商品数量 虚拟组套传1，其他传期数*件数，本期pc不支持虚拟组套类型
                                "itemType": 1,// 商品类型， 等待确认
                                "extFlag": filterObject(extFlag),
                                "relationSkus": filterObject(relationSkus)
                            }],
                            "ext": filterObject(bestPromotion)
                        }
                        
                    }
                    var url = '//api.m.jd.com'
                    if(pageConfig.product && pageConfig.product.colorApiDomain){
                        url = pageConfig.product && pageConfig.product.colorApiDomain
                    }
                    var time = new Date().getTime()
                    var body = JSON.stringify(paramJson);
                    // 加固start
                    var colorParm = {
                        appid: 'item-v3',
                        functionId: 'pcCart_jc_gate',
                        client: 'pc',
                        clientVersion: '1.0.0',
                        t: time,//生成当前时间毫秒数
                        body: body,
                    }
                    try{
                        var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                        colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                        window.PSign.sign(colorParmSign).then(function(signedParams){
                            colorParm['h5st']  = encodeURI(signedParams.h5st)
                            try{
                                getJsToken(function (res) {
                                    if(res && res.jsToken){
                                        colorParm['x-api-eid-token'] = res.jsToken;
                                    }
                                    colorParm['loginType'] = '3';
                                    colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                                    getCarDataList(colorParm);
                                }, 600);
                            }catch(e){
                                colorParm['loginType'] = '3';
                                colorParm['uuid'] = '';
                                getCarDataList(colorParm);
                                //烛龙上报
                                Tools.getJmfe(colorParm, e, "白条加入购物车接口设备指纹异常",752)
                            }
                        })
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getCarDataList(colorParm);
                        //烛龙上报
                        Tools.getJmfe(colorParm, e, "白条加入购物车接口加固异常",752)
                    } 
                    // 加固end
                    
                    function getCarDataList(colorParm){
                        // var num = $('#buy-num').val() || "1"
                        // var newCfg = pageConfig && pageConfig.product
                        $.ajax({
                            url: url,
                            data: colorParm,
                            dataType: 'json',
                            xhrFields: {
                                withCredentials: true,
                            }, 
                            success: function(r) {
                                var url = r && r.url
                                if(r && r.success && url){
                                    window.location.href = url;
                                }else if(r && !r.success && r.code == 1){
                                    var locname = window.location.hostname
                                    window.location.href = '//passport'+locname.split("item")[1]+'/new/login.aspx?ReturnUrl=' + encodeURIComponent(location.href);
                                }else if(r && !r.success && r.code == -881 && url){ // 其他跳转
                                    window.location.href = url;
                                }else{
                                    // 加车失败
                                    Tools.showAddCartFailDialog(_this.sku)
                                }
                            },
                            error: function (e) {
                                //烛龙上报
                                Tools.getJmfe(colorParm, e, "白条加入购物车接口错误异常",752)
                            }
                        })
                    }
                }else{
                    addToCart(collectQueryParams()).
                    done(function(res){
                        var res = res || {};
                        if (res.flag) {
                            // 加车成功跳转到结算页
                            window.location.href = "//trade.jd.com/shopping/order/getOrderInfo.action?rid=" + (+new Date()) + "&fq=" + _this.snum;
                        } else {
                            // 加车失败
                            Tools.showAddCartFailDialog(_this.sku)
                        }
                    }).
                    fail(function(){
                        log("打白条调用加车服务失败~")
                    });
                }
                
            }

            // 判断是否为空对象
            function isEmptyObject(obj) {
                return Object.keys(obj).length === 0; 
            } 

            // 过滤一个对象里面没有值的字段并返回过滤后的对象
            function filterObject(originalObj){  
                try {
                    var filteredObj = {}; 
                    $.each(originalObj, function(key, value) 
                    { 
                        if (value) { 
                            filteredObj[key] = value; 
                        } 
                    });
                    return filteredObj; 
                } catch(e) {
                    return originalObj; 
                }
            } 

            /// 处理打白条逻辑
            function handleIssueIOU() {
                if (getUserIouInfo.status == 1) {
                    handleAddToCart();
                    return;
                }
                getUserIouInfo().
                    done(function(res){
                        var status = res && res.isBtUser;
                        if ( !status ) {
                            window.location.href = "//bt.jd.com/v3/activity/open?rid=" + (+new Date());
                            return;
                        }
                        getUserIouInfo.status = 1;
                        handleAddToCart();
                    }).
                    fail(function(){
                        log("账户是否激活接口调用失败~");
                    });
            }

        },
        isHWAdd: function () {
            var ids = Tools.getAreaId().areaIds
            return ids[0] === 53283 || ids[0] === 52993 || ids[0] === 32
        },
        log: function(msg) {
            if (typeof errortracker !== 'undefined') {
                errortracker.log({ filename: 'reservation.js', message: msg })
            }
        },
        loginIframe: function() {
            login({
                modal: true,
                complete: function() {
                    window.location.reload(true)
                }
            })
        },
        getNum: function() {
            var amount = $('#buy-num').val()
            var num = Number(amount)
            return isNaN(num) ? 1 : num
        },
        get: function() {
            var _this = this
            Event.addListener('onStockReady', function (data) {
                if (/debug=bt/.test(location.href)) {
                    r.isDiscountAll = true
                    r.marketingText = '测试文字'
                }
                var r = data.stock && data.stock.data
                if (r && r.whiteBarInfo ) {
                    _this.set(r.whiteBarInfo)
                    _this.price = r.price // 价格对象
                    _this.ab= r.price && r.price.ab // 白条购按钮接口切量标识 true走新接口，false走老加车接口
                    _this.bestPromotion = r.bestPromotion
                    // _this.bestPromotion = {
                    //     "finalPrice":"131.00",
                    //     "bestCoupon":{
                    //         "encryptedKey":"1", 
                    //         "ruleId":"1",
                    //         "resultType":"1",
                    //     }
                    // }

                } else {
                    G.log(null, 'baitiao.js', 'Baitiao service error.')
                }
            });
        },
        destory: function () {
            this.hide()
            this.hideBtn()
            this.$el.html('')
        },
        set: function(data) {
            // 支持打白条时显示白条分期
            if (pageConfig.hasCarGift) { // 如果有车管家赠品，屏蔽白条，因为结算页中不能识别车管家赠品
                return;
            }
            if (data.planInfos && data.planInfos.length) {
                this.key = data.key
                this.isAva = data.isAva
                data.isExpand = isExpand
                // 商品不支持打白条
                if (!data.isAva) {
                    this.destory()
                    return false
                }
                if(!data.isDiscountAll){
                    data.isDiscountAll = false;
                }
                this.$el.html(BaiTiao.TEMPLATE.process(data))
                this.showItem()
                var arrList = []
                data.planInfos && data.planInfos.map(function (item){
                  arrList.push(item.plan)
                })

                this.hideBtn()
                this.enabled()

                // this.setTips(data)
                Event.fire({type:'onBaiTiaoRender'});
                var element = document.getElementById("baitiao-list");
                var opens = document.getElementById("bt-opens")
                
                if (element.offsetHeight < 100 ) {

                    this.$el.find('.bt-opens').addClass('baitiao-disabled')
                }else {
                    !isExpand && this.$el.find('.baitiao-list').addClass('bt-dd')
                    $('#bt-opens').click(function() {
                        if (element.classList.contains("bt-dd")) {
                            $('#bt-opens-span').text('收起全部');
                            isExpand = true
                            element.classList.remove('bt-dd');
                            element.classList.add('bt-dd-hidden')
                            $('.open-open').addClass('bt-opens-span-rotate')
                        }else {
                            $('#bt-opens-span').text('展开全部');
                            isExpand = false
                            element.classList.remove('bt-dd-hidden');
                            element.classList.add('bt-dd')
                            $('.open-open').removeClass('bt-opens-span-rotate')
                        }

                    });
                }

                // var height = element.offsetHeight;
                try {
                    Tools.exposure({
                        functionName: 'PC_Productdetail_Baitiao_Expo',
                        exposureData: ['mainskuid'],
                        extraData: {
                            BaiTiao_period:arrList
                        },
                        errorTips: '白条分期曝光-异常'
                    })
                }catch (error){
                    console.log(error)
                }
            }
        },
        // setTips: function(data) {
        //     var content =
        //         '\
        //     <div id="J-bt-tips">\
        //         <div class="g-tips-inner">\
        //             <i></i><em></em>\
        //             <ul>\
        //                 <li>1、白条的实际分期金额、分期服务费、可用额度及分期优惠以收银台展示为准。</li>\
        //                 <li>2、什么是白条分期？<br />\
        //                 白条是一种“先消费，后付款”的支付方式，使用白条可以享受先用后付，以及最长36期的分期还款。</li>\
        //             </ul>\
        //         </div>\
        //     </div>'

        //     if (this.$el.find('.J-bt-tips').length) {
        //         this.$el.find('.J-bt-tips').show().ETooltips({
        //             close: false,
        //             content: content,
        //             width: 300,
        //             pos: 'bottom',
        //             zIndex: 10
        //         })
        //     }

        //     if (this.$el.find('.J-icon-hui').length) {
        //         this.$el.find('.J-icon-hui').show().ETooltips({
        //             close: false,
        //             content: data.marketingText,
        //             pos: 'bottom',
        //             width: 200,
        //             zIndex: 10
        //         })
        //     }
        // },
        disabled: function() {
            this.$el.find('.item').addClass('disabled').removeClass('selected')

            this.showTips('yb')
            this.hideBtn()
        },
        enabled: function() {
            this.enable = true
            // 防止延保服务切换的时候调用直接设置禁用状态
            if (this.enable) {
                this.$el.find('.item').removeClass('disabled')
            }
            this.showTips('none')
        },
        select: function($ele, selected) {
            var snum = $ele.attr('data-snum')

            this.hasSelectedItem = !selected

            if (selected) {
                this.snum = snum
                this.showBtn()
                this.clearYbService()
            } else {
                this.snum = null
                this.hideBtn()
            }
            //this.showTips('note');

            Event.fire({
                type: 'onBaiTiaoSelect',
                isSelect: selected
            })
            this.onSelected(selected)
        },
        clearYbService: function() {
            $('#choose-service .item').each(function() {
                $(this).removeClass('selected')
            })
        },
        // 和白条逻辑排斥的关系
        isDisabledToShow: function(p) {
            return (
                p.isHeYue ||
                p.isYuShou ||
                p.isBiGouMa ||
                !p.havestock ||
                (p.__chooseShop && p.__chooseShop.selected) ||
                this.isHWAdd() ||
                p.isJinCai ||
                pageConfig.hasCarGift
            )
        },
        showItem: function() {
            if (!this.isDisabledToShow(this.cfg)) {
                this.show()
                // if (this.isAva) {
                    // this.getJinCai()
                // }
            } else {
                this.hide()
            }
        },
        // setJinCai: function (r) {
        //     var isJC = this.cfg.isJinCai = r && r.userLevel && r.userLevel === 90
        //     if (isJC) {
        //         this.$jc.show()
        //         this.$jc.find('.J-bt-tips').ETooltips({
        //             close: false,
        //             content: '<strong>什么是企业金釆？</strong>企业金釆是为优质企业客户推出的一款“先采购、后付款”的信用支付产品。企业金釆客户可享受“周结21天免息”或“月结低息分期”的延期付款体验。',
        //             width: 300,
        //             pos: 'bottom',
        //             zIndex: 10
        //         })
        //         this.hide()
        //     } else {
        //         this.hideJCItem()
        //     }
        // },
        hideJCItem: function () {
            this.$jc.hide()
            this.$jc.find('.item').removeClass('selected')
        },
        showJCBtn: function () {
            this.$jcBtn.show()
        },
        handleJC: function (e) {
            var $this = $(e.currentTarget)
            $this.toggleClass('selected')

            var isSelected = $this.hasClass('selected')
            this.cfg.isJinCaiSelected = isSelected

            if (isSelected) {
                this.showJCBtn()
            } else {
                this.hideJCBtn()
            }
        },
        hideJCBtn: function () {
            this.$jcBtn.hide()
        },
        show: function() {
            this.$el.show()
        },
        hide: function() {
            this.$el.hide()
        },
        hideBtn: function() {
            this.$btn.hide()
        },
        showBtn: function() {
            var chooseShopShowIndex = $('#choose-shop-show .selected').attr('data-index');
            if (!this.isDisabledToShow(this.cfg) && (chooseShopShowIndex != '0')) {
                this.$btn.show()
            }
            if(chooseShopShowIndex == 0){
                pageConfig.product.__chooseShop.showShopTips()
            }
        },
        setBTLink: function() {
            var url = '//bttrade.jd.com/shopping/order/getOrderInfo.action?'

            var params = {
                pid: this.sku,
                cid: this.cat[2],
                num: this.getNum(),
                snum: this.snum,
                key: this.key,
                gids: this.giftParam,
                did: this.did
            };

            if (this.giftType) {
                params.giftPoolType = this.giftType;
            }

            // 给延保判断使用
            //pageConfig.isDBT = !!this.snum;

            if (this.snum) {
                this.$btn.attr('href', url + $.param(params))
            } else {
                this.$btn.attr('href', '#none')
            }
        },
        showTips: function(type) {
            var btNote =
                '<a clstag="shangpin|keycount|product|fenqijieshao" href="//help.jr.jd.com/show/helpcenter/148.html" target="_blank">什么是白条分期？</a>'
            var ybTip = pageConfig.hasYbService ? '<em>增值保障不支持一键打白条 </em>' : ''
            var message = {
                yb: ybTip,
                none: ''
                //note     : btNote + ybTip + '<em>实际分期金额及手续费以白条剩余额度及收银台优惠为准</em>',
                //login    : '<a href="#none" class="J-login">登录</a> <em>后确认是否享有白条服务 </em>' + btNote,
                //overLimit: '<em>您的额度不足，暂无法使用。</em> <a clstag="shangpin|keycount|product|zengjiaedu" href="//help.jr.jd.com/show/helpcenter/148.html" target="_blank">如何增加额度？</a>',
                //active   : '<a href="//baitiao.jd.com/v3/activity/open" target="_blank">激活</a> <em>白条享最长24期白条分期</em>' + btNote,
                //service  : '<em>暂不支持购买京东服务</em> <a href="#none">什么是白条分期？</a>'
            }

            this.$el.find('.J-baitiao-text').html(message[type])
        }
    }

    function init(cfg) {
        var _this = this;
        var $baiTiao = $('#choose-baitiao')
        if (!$baiTiao.length) return false
        // Event.addListener('onStockReady', function(data) {
            if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && cfg.skuMarkJson && cfg.skuMarkJson.pg) return
            // var p = data.stock && data.stock.data
            cfg.baiTiaoFenQi = new BaiTiao({
                $el: $baiTiao,
                sku: cfg.skuid,
                // data: p,
                // price: p.price,
                cat: cfg.cat,
                cfg: cfg
            })
           
        // })
        // Event.addListener('onPriceReady', function(data) {
        //     //拼购不显示其他价格
        //     if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && cfg.skuMarkJson && cfg.skuMarkJson.pg) return
        //     var p = data.price
        //     cfg.baiTiaoFenQi = new BaiTiao({
        //         $el: $baiTiao,
        //         price: p,
        //         sku: cfg.skuid,
        //         cat: cfg.cat,
        //         cfg: cfg
        //     })
        // })

       
    }

    module.exports.__id = 'baitiao'
    module.exports.init = init
})
