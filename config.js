'use strict';

module.exports = {
  name: 'unite',
  version: '1.1.12',
  // production: '//static.360buyimg.com/item/',
  production: '//storage.jd.com/retail-mall/item2025/pc/',
  sprites: {
    cssName: '__sprite.scss',
    imgName: 'i/__sprite.png',
    items: [
      'app/components/default/common/i/sprite-*.png',
      'app/components/default/o2o/i/sprite-*.png',
      'app/components/default/colorsize/i/sprite-*.png',
      'app/components/default/contact/i/sprite-*.png',
      'app/components/default/popbox/i/sprite-*.png',
      'app/components/default/detail/i/sprite-*.png',
      'app/components/default/preview/i/sprite-*.png',
      'app/components/default-soa/common/i/sprite-*.png',
      'app/components/default-soa/o2o/i/sprite-*.png',
      'app/components/default-soa/colorsize/i/sprite-*.png',
      'app/components/default-soa/contact/i/sprite-*.png',
      'app/components/default-soa/popbox/i/sprite-*.png',
      'app/components/default-soa/detail/i/sprite-*.png',
      'app/components/default-soa/preview/i/sprite-*.png',
      'app/components/book/widget/contact/i/sprite-*.png',
      'app/components/book/widget/popbox/i/sprite-*.png',
      'app/components/public/modules/contact/i/sprite-*.png',
      'app/components/public-soa/modules/contact/i/sprite-*.png',
    ],
  },
  images: [
    'app/components/default/**/i/*.+(|png|gif|svg|jpg)',
    'app/components/default/**/fonts/*.+(|eot|ttf|woff|svg)',
    'app/components/default-soa/**/i/*.+(|png|gif|svg|jpg)',
    'app/components/default-soa/**/fonts/*.+(|eot|ttf|woff|svg)',
    'app/components/book/**/i/*.+(|png|gif|svg)',
    'app/components/book/**/**/i/*.+(|png|gif|svg)',
    'app/components/public/**/i/*.+(|png|gif|svg)',
    'app/components/public/**/**/i/*.+(|png|gif|svg)',
    'app/components/public-soa/**/i/*.+(|png|gif|svg)',
    'app/components/public-soa/**/**/i/*.+(|png|gif|svg)',
  ],
  uglify: {
    mangle: {
      except: ['define', 'require', 'module', 'exports'],
    },
    compress: {
      hoist_funs: false,
    },
  },

  // 部署远程 ftp 测试服务器
  deploy: {
    host: '***************',
    user: 'galaxy',
    password: 'galaxy238',
    parallel: 10,
    src: 'build/**',
    dest: '/var/sites/static.360buyimg.com/item',
  },
};
