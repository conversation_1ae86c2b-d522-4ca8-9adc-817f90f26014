@import '../common/lib';

.choose-baitiao,.choose-jincai {
    .item {
        position: relative;
        a {
            text-align: center;
        }
    }
    .hover .baitiao-tips {
        display: block;
    }
    .icon {
        @include icons(16px, 16px);
        margin-top: 8px;
        margin-right: 5px;
    }
    .prom {
        background-image: url(i/hui.png);
    }
    .question {
        background-image: url(i/question.png);
    }
}

.baitiao-list {
    float: left;
    strong {
        font-weight: normal;
    }
    span {
        color:#999;
        em {
            display: inline-block;
            *zoom: 1;
            padding:1px 2px;
            color:#fff;
            background:#fc1d3c;
        }

    }
    .disabled em {
        background:#ccc;
    }
    .baitiao-tips {
        position: absolute;
        z-index: 6;
        top: 32px;
        left: 0px;
        width: 250px;
        padding: 5px 10px;
        background:#fefff7;
        border: 1px solid #ccc;
        color: #666;
        ul {
            padding-bottom: 3px;
        }
        li {
            margin-top: 5px;
            line-height: 18px;
        }
    }
}
.baitiao-text {
    clear: both;
    em {
        color:#999;
    }
    a {
        color:#005aa0;
        display: inline;
    }
}
.bt-info-tips {
    float: left;
    height: 39px;
}
