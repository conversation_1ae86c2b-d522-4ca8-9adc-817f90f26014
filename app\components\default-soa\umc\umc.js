define('MOD_ROOT/umc/umc', function (require, exports, module) {
  var Tools = require('MOD_ROOT/common/tools/tools');

  function start(cfg) {
    var umcRes = {
      max: getUmcMax()
    };
    var modal = null;
    var timer = null;

    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var d = date.getDate();
    var expires = new Date(y, m, d, 23, 59, 59);

    var ResTypeToBpType = { 1: 1, 2: 0 };

    function insertModal() {
      // 默认是现金模板
      var tpl =
        '<div class="umc-equity-content" id="umc-equity-content">\
                    <div class="umc-equity-reward uer-cash">\
                      <div class="umc-equity-title"></div>\
                      </br> \
                      <div class="umc-equity-cash">\
                        <div class="umc-equity-monery">\
                          <span class="umc-equity-unit">￥</span>\
                          <span class="umc-equity-value">' +
        umcRes.discount +
        '</span>\
                        </div>\
                        </br> \
                        <div class="umc-equity-threshold">' +
        umcRes.cashDesc +
        '</div>\
                      </div>\
                      </br> \
                      <div class="umc-equity-use">立即使用</div>\
                      </br> \
                      <div class="umc-equity-time">' +
        umcRes.endTime +
        '</div>\
                    </div>\
                    <img class="umc-equity-close" src="https://img11.360buyimg.com/imagetools/jfs/t1/249124/2/20867/609/670d0df0Fd3fe6bc3/9f5b9f0c981d7adc.png" />\
                    </div>\
                </div>';

      // 优惠券模板
      if (umcRes.type == 1) {
        tpl =
          '<div class="umc-equity-content" id="umc-equity-content">\
            <div class="umc-equity-reward uer-coupon">\
              <div class="umc-equity-title"></div>\
              </br> \
              <div class="umc-equity-coupon">\
                <div class="umc-equity-monery">\
                  <span class="umc-equity-unit">￥</span>\
                  <span class="umc-equity-value">' +
          umcRes.value +
          '</span>\
              </div>\
              <div class="umc-equity-block">\
                <div class="umc-equity-threshold">满' +
          umcRes.threshold +
          '可用</div>\
                            <div class="umc-equity-desc">' +
          umcRes.desc +
          '</div>\
                          </div>\
                        </div>\
                        </br> \
                        <div class="umc-equity-use">立即使用</div>\
                        </br> \
                        <div class="umc-equity-time">' +
          umcRes.endTime +
          '</div>\
                      </div>\
                      <img class="umc-equity-close" src="https://img11.360buyimg.com/imagetools/jfs/t1/249124/2/20867/609/670d0df0Fd3fe6bc3/9f5b9f0c981d7adc.png" />\
                      </div>\
                  </div>';
      }

      modal = $('<div class="umc-equity">').html(tpl);

      $('body').prepend(modal);
    }

    // 生产规则：毫秒级时间戳+32bit无符号随机数，即最长23位的数字字符串 埋点用
    function getRid() {
      return Date.now() + '' + Math.floor(Math.random() * 0x100000000); // 生成rid
    }

    function clickModal(e) {
      var target = $(e.target);

      modal.remove();

      if (target.hasClass('umc-equity-use')) {
        // 点击埋点上报
        clickBp(
          'UMC_Tc',
          'TcClick',
          getBpParams({ source: 1, type: ResTypeToBpType[umcRes.type], et: 1, clickAreaType: 0, eid: 'UMC_Tc|TcClick' })
        );

        umcRes.link && window.open(umcRes.link);

        if (umcRes.type == 1) {
          
        }

        if (umcRes.type == 2) {
          
        }
      }

      if (target.hasClass('umc-equity-close')) {
        clearTimeout(timer);

        // 关闭埋点上报
        clickBp(
          'UMC_Tc',
          'TcCancelClick',
          getBpParams({ source: 1, type: ResTypeToBpType[umcRes.type], et: 1, clickAreaType: 1, eid: 'UMC_Tc|TcCancelClick' })
        );
      }
    }

    function getBpParams(options) {
      var params = {
        touchstone_expids: [umcRes.ab || ''], // 棱镜，试金石适用；包含实验标识和实验组
        source: options.source, // '0-首页、1-商详',
        type: options.type, // '0-红包、1-优惠券',
        click_area_type: options.clickAreaType, // '0-图片、1-关闭按钮',
        mkt_comp_un: {
          fid: umcRes.fid,
          cid: umcRes.cid,
          rid: getRid(), // 请求ID 前端生成
          et: options.et, // 事件类型 1 点击 2 曝光
          ext: {
            eid: options.eid, // ext为自定义，上报参数当前仅可确认eid，其他参数为自定义
            type: 'enter' // enter or back
          },
          det: {
            recomInfo: umcRes.recomInfo // 接口返回
          }
        }
      };
      console.log(params);
      return params;
    }

    function exposureBp(business, fn, params) {
      window.expLogJSON && window.expLogJSON(business, fn, JSON.stringify(params));
    }

    function clickBp(business, fn, params) {
      window.log && window.log(business, fn, JSON.stringify(params));
    }

    function getUmcMax() {
      if (window.floorConfig) {
        return window.floorConfig.umcCountLimit;
      }

      if (window.pageConfig && window.pageConfig.product) {
        return window.pageConfig.product.umcPopUpNum;
      }

      return 0;
    }

    function getCookieCount() {
      var count = document.cookie.split('umc_count=')[1];
      if (!count) {
        return -1;
      }

      return (count = count.slice(0, 1) * 1);
    }

    function show() {
      if (!umcRes.endTime) {
        return;
      }

      if (umcRes.max) {
        var count = getCookieCount();

        if (count === -1) {
          document.cookie = 'umc_count=1;expires=' + expires.toUTCString() + ';domain=.jd.com;';
        } else {
          if (count >= umcRes.max) {
            return;
          } else {
            document.cookie = 'umc_count=' + (count + 1) + ';expires=' + expires.toUTCString() + ';domain=.jd.com;';
          }
        }
      }

      return true;
    }

    function umcExposure() {
      var host = '//api.m.jd.com';
      if (pageConfig.product && pageConfig.product.colorApiDomain) {
        host = pageConfig.product && pageConfig.product.colorApiDomain;
      }

      $.ajax({
        url: host,
        type: 'post',
        data: {
          appid: 'pc-item-soa',
          functionId: 'deliveryReportExposure',
          t: Date.now(),
          body: JSON.stringify(JSON.parse(umcRes.bodyParam))
        },
        dataType: 'json',
        xhrFields: {
          withCredentials: true
        },
        success: function (res) {},
        error: function (e) {}
      });
    }

    function getModalConfig(callback) {
      var count = getCookieCount();

      if (count >= umcRes.max) {
        return;
      }

      Tools.getJsTokenSign({
        appid: 'pc-item-soa',
        body: JSON.stringify({
          area: Tools.getAreaId().areaIds.join('_')
        }),
        functionid: 'pc_item_getPopUpInfo',
        render: function (colorParm) {
          var host = '//api.m.jd.com';
          if (pageConfig.product && pageConfig.product.colorApiDomain) {
            host = pageConfig.product && pageConfig.product.colorApiDomain;
          }

          $.ajax({
            url: host,
            data: colorParm,
            dataType: 'json',
            xhrFields: {
              withCredentials: true
            },
            success: function (res) {
              res.title = '可与店铺券叠加使用';
              res.cashDesc = res.cashDesc || '无门槛，满0.01可用';
              res.type = 2; // 1 优惠券 2 红包
              res.max = getUmcMax();
              res.ab = res.touchstone_expids;

              callback(res);
            },
            error: function (e) {}
          });
        }
      });
    }

    if (['item.jd.com'].indexOf(location.hostname) === -1) {
      return;
    }

    Tools.checkLogin(function (loginRes) {
      if (loginRes.IsAuthenticated) {
        getModalConfig(function (res) {
          umcRes = res;

          if (show()) {
            umcExposure();

            exposureBp('UMC_Tc', 'TcExpo', getBpParams({ source: 1, type: ResTypeToBpType[umcRes.type], et: 2, eid: 'UMC_Tc|TcExpo' }));

            insertModal();

            modal.click(clickModal);

            timer = setTimeout(function () {
              modal.remove();
            }, 5000);
          }
        });
      }
    });
  }

  function init() {}

  module.exports.start = start;
  module.exports.init = init;
});
