.product-intro {
    position: relative;
    // *z-index: 1;
    padding-top: 24px;
    margin-bottom: 52px;
    .preview-wrap {
        // width: 302px;
        // float: left;
        // padding-bottom: 15px;
        *position: relative;
        *zoom: 1;
        *z-index: 7;
    }
    .itemInfo-wrap {
        // _overflow:hidden;
        width: 648px;
        float: right;

        .clothing & {
            min-height: 590px;
            _height: 590px;
            width: 624px;
        }
        // 家电三栏右侧最多两个商品
        .cat-1-737 & {
            min-height: 479px;
            _height: 479px;
        }
        .ebook & {
            // margin-right: 24px;
            // width: 600px;
        }
    }
}

.root61 {
    .product-intro {
        .preview-wrap {
          float: left;
          width: 850px;
          .pro-detail-hd-fixed{ 
            margin-top: 16px; // 左侧吸顶需要，右侧不需要
          }
        }
        .itemInfo-wrap {
          width: 686px !important;
          height: 720px;
          background: url(https://img12.360buyimg.com/imagetools/jfs/t1/316723/8/6968/21139/6842524fF976ecf4c/fd5b5e450adc7bf1.png) left top / 100% 100% no-repeat;
          .information-wrap{
            width: 686px !important;
            border-radius: 8px;
            overflow: visible; // 右侧内容过少时，前往手机购买二维码会被遮挡
            // display: flex;
            flex-direction: column;
            scrollbar-width: none;
            -ms-overflow-style: none;

            &::-webkit-scrollbar {
              display: none;
            }

            opacity: 0;
          }

          .infomation{
            display: flex;
            flex-direction: column;
            padding: 0 16px 10px 16px;
            background: #fff;
            // border-bottom-left-radius: 8px;
            // border-bottom-right-radius: 8px;
            border-radius: 8px;
          }
        }
        .pro-detail-hd-fixed{
        //   position: fixed;
        //   top: 0;
        //   margin-top: 16px;
          z-index: 30;
        }
        .fix-bottom {
            position: fixed;
            bottom: 0;
        }
        .bottom-fixed {
          position: absolute;
          bottom: 0;
        }
    }
    .clothing{
        .product-intro{
            .itemInfo-wrap{
                width: 686px;
                float: right;
            }
        } 
    }
}

/*book*/
.ebook{
    .product-intro{
        .itemInfo-wrap{
            // width:580px;
            // width: 750px; 
            width: 686px !important;
            border-radius: 8px;
            .information-wrap {
                background: #fff; 
                padding: 0 16px;
            }
            .sku-name{
              padding-top: 20px;
              .sku-name-title{
                font-family: JDZhengHeiVHeavy2;
              }
            }
            .information-wrap {
                width: 686px !important;
                border-radius: 8px;
                overflow: visible;
                flex-direction: column;
                background: #fff;
                // padding: 16px;
            }
      
        }
    }
}

#J-global-toolbar {
    display: none;
    .jdm-tbar-tab-qrcode,.jdm-tbar-tab-qrcode:hover {
        .tab-ico {
            background: url(//img10.360buyimg.com/devfe/jfs/t7507/196/1481278321/1134/d2c81887/599d3334Nf16495a3.gif) 8px center no-repeat;
        }
        .tab-text {
            left: 35px;
        }
    }

    .jdm-toolbar-tabs {
        margin-top: -100px;
    }
    .toolbar-qrcode {
        padding: 12px;
        border-radius: 5px;
        position: absolute;
        left: -145px;
        top: -152px;
        background: #e93536;
        color: #fff;
        font-size: 14px;

        p {
            padding-bottom:10px;
        }

        .close {
            position:absolute;
            border: 1px solid #fff;
            color:#fff;
            background:#e93536;
            border-radius: 10px;
            right:-5px;
            top:-5px;
            width:17px;
            height:17px;
            text-align: center;
            font: 15px/15px simsun;
            line-height: 15px;
        }
    }
    .jdm-tbar-tab-xianshitehui{
      .tab-ico{
        background: url(i/_sprite_9_1.png) 8px 8px no-repeat;
      }
      .toolbar-ext-xianshitehui{
        position: absolute;
        right: 0;
        bottom: -0.2px;
        color: #fff;
        font-size: 12px;
        z-index: 10;
        visibility: visible;
        .xianshitehui-bg{
          display:none;
        }
        .xianshitehui-bg-animate{
          display: inline;
          img{
            display: none;
          }
        }
        .toolbar-ext-tips{
          display: none;
          min-width: 160px;
          border-radius: 50%/50%;
          border:#7a6e6e solid 1px;
          background: no-repeat 0 0 scroll #ffffff;
          position: absolute;
          right: 40px;
          bottom:65px;
          text-align:center;
          .ext-tip-text{
            padding: 20px;
            line-height: 20px;
            color: #666666;
            .ext-price{
              color: #df3033;
            }
          }
          .ext-tip-arrow{
            background-repeat: no-repeat;
            position: absolute;
            background-image: url(i/_sprite_9_1.png);
            background-position: 0 -40px;
            height: 14px;
            right: 15px;
            bottom: -5px;
            width: 28px;
          }
          &.xianshi-ext-lg{
            .ext-tip-arrow{
              bottom:-2px;
            }
          }
        }
      }
      &.z-jdm-tbar-tab-hover{
        .tab-text{
          left:35px;
        }
        .toolbar-ext-xianshitehui{
          .xianshitehui-bg{
            display:inline;
          }
          .xianshitehui-bg-animate{
            display: none;
          }
        }
        .toolbar-ext-tips{
          display: block;
        }
      }
    }
    .jdm-tbar-tab-contact .tab-ico {
        background-image: url(./i/communication-icon.png);
        background-position: center;
    }
}
html {
    -webkit-overflow-scrolling: touch; /* 启用原生滚动效果 */
    overscroll-behavior: contain; /* 防止滚动溢出时的回弹效果 */
}
#choose-service .service-type-yb-zz, #choose-serviceyc .service-type-yb, #choose-serviceF .service-type-yb-ff, #choose-service\+ .service-type-yb-puls{ // 增值保障/京选服务等/增值服务/京东服务
   .yb-item-cat .yb-item .name-tip a {
    padding-right: 14px;
    background: url(https://img13.360buyimg.com/imagetools/jfs/t1/259181/16/14667/489/6790bd3aF60de783b/8ed74ff7b6ef4ed6.png) no-repeat;
    background-size: 10px 10px;
    background-position: right center;
    color: #888b94;
    &:hover {
        color: #ff0f23;
        background-image: url(https://img14.360buyimg.com/imagetools/jfs/t1/276452/7/17223/329/67f37bd2F5d6b429e/cb4f837e6c161ee5.png);
    }
  }
}


#searchBar {
  height: 68px;
  background: url(https://img14.360buyimg.com/imagetools/jfs/t1/319563/24/11509/3947/685a6f45Fb36f65ad/227a0cb852f280b7.png) center top / 1552px 68px no-repeat;
}

#searchBar-root {
  height: 68px;
}

@media (max-width: 1679px) {
    #searchBar {
      background: url(https://img14.360buyimg.com/imagetools/jfs/t1/319563/24/11509/3947/685a6f45Fb36f65ad/227a0cb852f280b7.png) center top / 1200px 68px no-repeat;
    }
}