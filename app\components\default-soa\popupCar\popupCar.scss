.layer-con{
    font-size: 12px;
}
.ui-dialog .ui-dialog-content{
    padding-bottom: 0;
    padding-top: 20px;
}
.ui-dialog .ui-dialog-title{
    font: 12px/30px 'Microsoft Yahei';
}
.car-list{
    overflow: hidden;
    zoom:1;
    margin-top: 8px;
}
.car-list li{
    position: relative;
    width: 154px;
    height: 32px;
    margin: 0 8px 0 0;
    padding: 8px 8px 10px;
    float: left;
    border: 1px solid #f2f2f2;
    cursor: pointer;
}
.car-list .hover{
    border-color: #666;
}
.car-list .current{
    border-color: #e4393c;
}
.car-list img{
    width: 32px;
    height: 32px;
    float: left;
    margin-top: 0;
    margin-right: 8px;
}
.car-list .car-info{
    margin-left: 30px;
    line-height: 140%;
}
.car-list .car-info h4{
    color: #666;
    margin-bottom: 3px;
    height: 16px;
    overflow: hidden;
}
.car-list .car-info p{
    color: #9e9e9e;
    height: 16px;
    overflow: hidden;
    white-space:nowrap; overflow:hidden; text-overflow:ellipsis;
}
.car-list .item-op{
    display: none;
    float: right;
    color: #e4393c;
}
.car-list .hover:hover .item-op{
    display: block;
}
.car-list .item-layer{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.7);
    filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#9F000000, endcolorstr=#9F000000);
    text-align: center;
}
.car-list .item-layer p{
    padding: 10px 0;
    color: #fff;
}
.car-list .item-bt a{
    display: inline-block;
    line-height: 25px;
    padding: 0 20px;
    background: #fff;
    color: #666;
    margin: 0 5px;
}
.car-list .item-add{
    display: block;
    line-height: 33px;
    color: #968b8b;
    text-align: center;
}
.car-list .item-add:hover{
    color: #e4393c;
}
.car-layer-con,.no-car-layer-con{
    min-height: 521px;
    _height: expression(this.height > 521 ? this.height: 521);
}
.car-layer-con .tips{
    color: #c8c8c8;
    padding:10px 0 0;
}
.car-layer-con .layer-bt{
    padding: 10px 0;
    text-align: center;
}
.car-layer-con .layer-bt a{
    display: inline-block;
    line-height: 28px;
    padding: 0 30px;
    color: #fff;
    border: 1px solid #ccc;
    margin: 0 10px;
}
.car-layer-con .layer-bt .ok-btn{
    background: #e4393c;
    border-color: #e4393c;
}
.car-layer-con .layer-bt .cancel-btn{
    color: #666;
}
.car-layer-con .layer-bt .disabled{
    background: #ccc;
    border-color: #ccc;
}
.car-layer-con .add-car{
    padding: 30px 0 20px;
    text-align: center;
}
.car-layer-con .icon-car{
    display: block;
    width: 79px;
    height: 55px;
    margin: 0 auto 30px;
    background: url(i/car.png) no-repeat;
}
.car-layer-con h5,.car-layer-con h6{
    font-weight: normal;
}
.car-layer-con h5{
    padding-left: 3px;
}
.car-layer-con .add-bt{
    display: inline-block;
    width: 98px;
    height: 28px;
    line-height: 28px;
    border: 1px solid #e4393c;
    color: #e4393c;
}
.car-list li.null i{
    float: left;
    width: 12px;
    height: 12px;
    background: url(i/add-bt-gray.gif) no-repeat;
    margin-top: 10px;
    margin-left: 41px;
}
.car-list li.null.current i{
    background: url(i/add-bt.gif) no-repeat;
}

.car-list li.null em{
    float: left;
    padding-left: 10px;
    color: #666;
}
.car-layer-con .add-bt em{
    float: left;
}
.love-car{
    width: 100%;
    overflow: hidden;
    padding-bottom: 27px;
    display: none;
}
.match-goods h5{
 color: #9e9e9e;
 margin-bottom: 6px;
}
.match-goods-choose{
    width: 100%;
    padding-top: 54px;
    display: none;
}
.match-goods-choose i,.match-goods-choose em{
    display: block;
    margin: 0 auto;
}
.match-goods-choose i{
    width:84px;
    height: 89px;
    background: url(i/joy2.gif);
}
.match-goods-choose em{
    width: 100%;
    overflow: hidden;
    padding-top: 16px;
    text-align: center;
    color: #666;
}
.match-goods-loading{
    width: 100%;
    padding-top: 104px;
    display: none;
}
.match-goods-loading i{
   display: block;
   width:29px;
   height: 28px;
   background:url(i/loading.gif) no-repeat -10px -10px;
   margin: 0 auto;
}
.match-goods-loading em{
    display: block;
    width: 100%;
    overflow: hidden;
    margin: 0 auto;
    font:14px/14px 'Microsoft Yahei';
    padding-top: 16px;
    color: #666;
    text-align: center;
}
.no-match-goods,.deleteError,.addError,.matchError{
    width: 100%;
    height: 250px;
    padding-top: 54px;
    display: none;
}
.no-match-goods i,.no-match-goods em,.deleteError i,.deleteError em,.addError i,.addError em,.matchError i,.matchError em{
   display: block;
   margin: 0 auto;
   text-align: center;
   padding-top: 16px;
}
.no-match-goods i,.deleteError i,.addError i,.matchError i{
    width: 96px;
    height: 78px;
    background: url(i/joy.gif) no-repeat;
}
.no-match-goods em,.deleteError em,.addError em,.matchError em{
    width: 100%;
    overflow: hidden;
    color: #666;
    text-align: center;
    font:14px/14px 'Microsoft Yahei';
}
.no-match-goods em a{
    color: #989898;
}
.no-match-goods em a strong{
    color: #0133fd;
    font:normal 14px/14px 'Microsoft Yahei';
}
.no-match-goods em a:hover,.no-match-goods em a:hover strong{
    color: #0133fd;
}
.match-goods-list{
    width: 748px;
    overflow: hidden;
    padding: 0 3px;
    display: none;
    overflow-y: auto;
    height: 392px;
    display: none;
}
.match-goods-list li{
    width: 100%;
    border-top: #f2f2f2 1px solid;
    padding: 8px 0 12px 0;
}
.match-goods-list li.active{
    background: #f5f5f5;
}
.match-goods-list li dl{
    position: relative;
}  
.match-goods-list dt,.match-goods-list dd{
    float: left;
}
.match-goods-list dt{
    width:58px;
    height: 58px;
    margin: 0 12px 0 2px;
}
.match-goods-list dt img{
    width:58px;
    height:58px;
    border: 1px solid #f2f2f2;
    position: absolute;
    margin-top: -29px;
    top: 50%;
}
.match-goods-list .gift-title{
    width:210px;
    text-overflow : ellipsis; 
    white-space : nowrap; 
    overflow : hidden; 
    display: inline-block;
}
.match-goods-list .J-gift-num{
    margin: 0 17px;
}
.match-goods-list .J-gift span,.match-goods-list .J-gift img,.match-goods-list .J-gift a,{
    vertical-align: middle;
}
.match-goods-list .car-gift-price{
    color: #727cb7;
}
.match-goods-list .no-goods{
    margin-right: 8px;
}
.match-goods-list .go-buy{
    border: 1px solid #e54548;
    width: 61px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 2px;
    margin-top:8px;
    display: block;
    font-weight: normal;
}
.match-goods-list .go-buy:hover{
    background: red;
    color: white;
}
.match-goods-list .car-gift-price:hover{
    color:#e65254;
}
.match-goods-list .goods-price{
    font-weight: bold;
}
.match-goods-list dd.goods-detail{
    width:455px;
    margin-right: 90px;
    overflow: hidden;
    font:12px/16px 'Microsoft Yahei';
    color: #666;
}
.match-goods-list dd.goods-detail h6{
    height: 30px;
    overflow: hidden;
    display: table-cell;
    vertical-align: middle;
}
.match-goods-list dd.goods-detail h6 span{
    font: 12px/16px 'Microsoft Yahei';
    color: #666;
    vertical-align: middle;
    display: inline-block;
    width: 100%;
    letter-spacing: -0.8px;
}
.match-goods-list dd.goods-detail p{
    padding-top: 5px;
}
.match-goods-list dd.goods-detail .color999{
    color:#999;
}
.match-goods-list dd.goods-price{
    width: 112px;
    overflow: hidden;
    color: #e65254;
    font-weight: bold;
}
.match-goods-list li .goods-detail img{
    width: 28px;
    height: 28px;
    border: 1px solid #e8e8e8;
}
.match-goods-list li.active a dd.goods-detail h6 span{
    color: #C81623;
}
.choose-car{
    height: 32px;
    line-height: 32px;
    margin-bottom: 17px;
    display: none;
    .warning-tips{
        float: left;
        color: #e4393c;
        font-weight: normal;
        margin-right: 10px;
    }
    .tips {
        float: left;
        width: 192px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
    }
    .choose-op{
        float: left;
        height: 27px;
        border-radius: 20px;
        border: #ced2e6 1px solid;
        margin-left: 0;
        color: #6671b1;
        background: url(i/choose-op-car.gif) no-repeat 12px 8px #fff;
        padding:0 8px 0 33px;
        line-height: 27px;
    }
    .choose-op a{
        color: #6671b1;
    }
    .choose-op a:hover{
        color: #6671b1;
    }
    .car-type{
        width: 134px;
    }
    .car-type .car-text{
        width: 115px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
    }
    .car-type span{
        display: inline-block;
        width: 15px;
        overflow: hidden;
    }
    .choose-op:hover{
        background: url(i/choose-op-car-hover.gif) no-repeat 12px 8px #f0f1f7;
    }
    .car-gift-icon{
        width: 16px;
        height: 16px;
        float: left;
        background-image: url(i/car-gift-icon.png);
        margin: 5px 0 0 7px;
        display: none;
    }
    .selcancel-op {
        display: none;
        float: right;
        margin-right: 10px;
        color: #005ea7;
        *margin-top:-30px;
    }
    .selcancel-op:hover{
        color: #e4393c;
    }
}
.show{
    display: block;
}

/*我的爱车*/
.item-bt{
    padding-top: 16px;
}
.car-list .item-bt a.J-qr-del-btn{
    padding:0 8px;
    background: #e74649;
    color: #fff;
    height: 19px;
    line-height: 19px;
}
.car-list .item-bt a.J-qr-cancel-btn{
    padding: 0 10px;
    background: #f2f2f2;
    color: #999;
    height: 19px;
    line-height: 19px;
}
.J-del-btn{
    display: none;
    float:none;
    color: #e4393c;
    position: absolute;
    right: 0;
    top: -1px;
    width: 16px;
    height: 16px;
    background: url(i/J-del-btn.gif) no-repeat;
}
.car-list .item-layer{
    background: rgba(0,0,0,0.5);
}

/*添加我的爱车*/
#iscgj2{
    display: none;
    padding-bottom: 20px;
}
#iscgj2.show{
    display: block;
}
#iscgj2 .car-filter-bd{
    padding: 10px 0 0 0;
    border:none;
}
.root61 #iscgj2 .car-filter-bd .car-filter-item1, .root61 #iscgj2 .car-filter-bd .car-filter-item2, .root61 #iscgj2 .car-filter-bd .car-filter-item3, .root61 #iscgj2 .car-filter-bd .car-filter-item4{
    width: 155px;
}
.root61 #iscgj2 .car-filter-bd .menu-drop .trigger{
    width: 135px;
}
.root61 #iscgj2 .car-filter-bd .menu-drop .trigger .curr{
    width: 98px;
}
#iscgj2 .car-filter-bd .menu-drop{
    height: 40px;
}
#iscgj2 .car-filter-bd .menu-drop .trigger{
    height: 40px;
    line-height: 40px;
}
#iscgj2 .car-filter-bd .menu-drop .trigger .curr{
    height: 40px;
}
#iscgj2 .car-filter-bd .menu-drop .menu-drop-arrow{
    top:9px;
}
#iscgj2 .car-filter-btn{
    width: 90px;
    height: 42px;
    line-height: 42px;
    padding: 0;
}
#iscgj2 .car-filter-bd .menu-drop .menu-drop-main{
    top: 41px;
}
.root61 #iscgj2 .car-filter-item2 .menu-drop-main{
    width: 548px;
}
.root61 #iscgj2 .car-filter-item2.z-menu-drop-open .menu-drop-main{
    width: 548px;
    left: -165px;
}
.root61 #iscgj2 .car-filter-item2 .menu-drop-main{

}
.root61 #iscgj2 .car-filter-bd .car-filter-item3 .menu-drop-main{
    width:154px;
}
#iscgj2 .car-filter-bd .car-filter-item4 .menu-drop-main{
    width: 320px;
}
#iscgj2 .car-filter-bd .car-filter-item4.z-menu-drop-open .menu-drop-main{
    width: 320px;
    left: -166px;
}
.root61 #iscgj2 .car-filter-item2.z-menu-drop-open .menu-drop-main{
    padding-top: 8px;
}





