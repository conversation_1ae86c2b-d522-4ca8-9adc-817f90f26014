define('PUBLIC_ROOT/modules/common/verify/verify', function(require, exports, module) {
    require('JDF_UI/dialog/1.0.0/dialog');
    require('PUBLIC_ROOT/modules/common/verify/verify.css');

    /**
    seajs.use('WDG_ROOT/verify/verify', function (Verify) {
        var verify = new Verify({
            onError: function() { },
            onComplete: function (res) { }
        });
    });
    */

    function Verify(opts) {
        var _this = this;
        this.onComplete = opts.onComplete || function() {};
        this.onError = opts.onError || function() {};

        this.popUp(function () {
            _this.bindEvent();
            _this.getVerifyCode();
        });
    }
    Verify.prototype = {
        bindEvent: function () {
            var _this = this;
            this.$el = $('#verify-pop');
            this.$authCode = $('.J-auth-code');
            this.$msg = this.$el.find('.J-verify-res');

            this.$el.undelegate();

            this.$el.delegate('.J-ok', 'click', function () {
                _this.requestVerify();
            });
            this.$el.delegate('.J-change', 'click', function () {
                _this.getVerifyCode();
            });
        },
        getVerifyCode: function () {
            var _this = this;

            $.ajax({
                url: '//pcp.jd.com/captcha/registry',
                dataType: 'jsonp',
                timeout: 3000,
                error: function () {
                    $.closeDialog();
                    _this.onError.call(_this);
                },
                success: function (r) {
                    if (r && r.imageUrl) {
                        _this.setVerifyCode(r);
                    }
                }
            })
        },
        setVerifyCode: function (r) {
            var $code = this.$el.find('.J-verify-code');
            this.acId = r.acId;

            $code.html('<img src="//'+ r.imageUrl +'" width="56" height="30" />');
        },
        requestVerify: function () {
            var _this = this;
            var authCode = $.trim(this.$authCode.val());

            $.ajax({
                url: '//pcp.jd.com/verify/cp',
                dataType: 'jsonp',
                timeout: 3000,
                data: {
                    acId: this.acId,
                    authCode: authCode
                },
                error: function () {
                    $.closeDialog();
                    _this.onError.call(_this);
                },
                success: function (r) {
                    _this.responseVerify(r);
                }
            })
        },
        responseVerify: function (r) {
            r = 601;
            if (r === 602) {
                this.showMessage();
            }
            if (r === 605) {
                $.closeDialog();
                window.location.reload();
            }
            this.onComplete.call(this, r);
        },
        showMessage: function (text) {
            text = text || '验证码不正确，请重新输入！';
            this.$msg.show().html(text);
        },
        hideMessage: function () {
            this.$msg.hide().html();
        },
        popUp: function (cb) {
            var html = '\
            <div id="verify-pop" class="verify-pop">\
                <dl>\
                    <dt class="fl share-ico"></dt>\
                    <dd class="lh">\
                        <h5>加油！您和宝贝只有一个验证码的距离啦！</h5>\
                        <div class="verify">\
                            <input type="text" class="J-auth-code" placeholder="请输入后方显示的验证码" />\
                            <span class="verify-code J-verify-code J-change">\
                                <img width="56" height="30" src="//misc.360buyimg.com/lib/img/e/blank.gif" />\
                            </span>\
                            <a href="#none" class="J-change">换一换</a>\
                        </div>\
                        <div class="verify-res J-verify-res hide"></div>\
                        <div class="verify-btn">\
                            <a href="#none" class="s-btn s-btn-gray J-ok">确定</a>\
                        </div>\
                    </dd>\
                </dl>\
            </div>';

            if (pageConfig.__verifyPOPInited) {
                return false;
            }
            
            pageConfig.__verifyPOPInited = true;

            $('body').dialog({
                width: 540,
                height: 130,
                title: '提示',
                type: 'html',
                source: html,
                onReady: cb
            });
        }
    };

    module.exports = Verify;
});
