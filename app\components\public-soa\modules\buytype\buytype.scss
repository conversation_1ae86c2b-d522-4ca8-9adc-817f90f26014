@import '../common/lib';
.buytype { text-align: left; }
#choose-type-hy {
    .icon {
        @include icons(16px, 16px);
        margin-top: 8px;
        margin-right: 5px;
    }
    .question {
        background-image: url(i/question.png);
    }
}

// 加价购弹层
#shf-feetype {
    padding: 0 30px;
    dt, .dl-1 dd {
        font-size: 14px;
    }
    dl {
        margin-bottom: 10px;
    }
    dd {
        width: 410px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    ul {
        margin-bottom: 5px;
    }
    .lh li {
        float: none;
        width: 400px;
        background: #f3f3f3;
        margin-bottom: 1px;
        padding: 0 5px;
        height: 2.5em;
        line-height: 2.5em;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        em {
            color: #999;
        }
    }
    table {
        background: #f3f3f3;
        border-collapse: collapse;
        margin-top: 5px;
        td {
            width: 193px;
            height: 2em;
            border: 1px solid #ddd;
            padding: 5px;
        }
    }
}
.shf-buy-now {
    padding-top: 10px;
    a {
        display: inline-block;
        *zoom: 1;
        height: 30px;
        padding: 0 20px;
        font: bold 14px/30px simsum;
        &:hover {
            text-decoration: none;
        }
    }
}
