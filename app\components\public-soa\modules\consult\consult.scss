@import '../common/pager';
@import '../contact/__sprite.scss';

.consult {
    .extra {
        .jimi, .im {
            cursor: pointer;
            float: left;
            margin-top: 2px;
            margin-right: 10px;
            white-space: nowrap;

            i {
                @include inline-block;
                vertical-align: middle;
                margin-right: 5px;
            }
            &:hover {
                color: #E3393C;
            }
        }

        .jimi {
            .sprite-jimi {
                @include sprite-jimi;
                margin-top: -3px;
            }
        }

        .im {
            .sprite-im {
                width: 16px;
                height: 20px;
                @include inline-block;
            }

            &.gys-im {
                .sprite-im {
                    background: url(i/sprite-gys-im.gif) no-repeat 0 0;
                }
            }

            &.pop-im {
                .sprite-im {
                    background: url(i/sprite-pop-im.gif) no-repeat 0 0;
                }
            }

            &.newjd-im {
                .sprite-im {
                    background: url(i/sprite-jd-im.gif) no-repeat 0 0;
                }
            }

            &.jd-im-offline {
                .sprite-im {
                    @include sprite-im-offline;
                }
            }
        }
    }

    .item:hover {
        color: #E3393C;
    }
}
.consult{
    .search{
        padding: 20px 30px 0 30px;
        p {
            color: #999;
            padding-bottom: 10px;
        }
    }

    .search-from{
        .s-text{
            float: left;
            width: 449px;
            height: 32px;
            border: #ccc 1px solid;
            border-right: none;
            font:12px/34px 'simsun';
            color: #999;
            padding: 0 10px;
        }

        button{
            float: left;
            width: 82px;
            height: 34px;
            background: #e4393c;
            border: none;
            line-height: 1;
            color: #fff;
            font-family: "Microsoft YaHei";
            font-size: 16px;
            cursor: pointer;
        }
    }

    .search-list{
        width: 100%;
        overflow:hidden;

        li{
            //background: url(i/search-list-li.gif) repeat-x left bottom;
            font:12px/18px 'simsun';
            border-bottom:1px dotted #eee;

            @mixin displayStyle{
                float: left;
            }

            dl{
                color: #666;
                overflow: hidden;

                dt{

                    @include displayStyle;
                    width: 64px;
                }
                dd{
                    width: 690px;
                    @include displayStyle;

                }
                dd.u-date{
                    float: right;
                    width: 114px;

                }
            }

            dl.ask{
                padding: 15px 0 7px;
            }

            dl.answer{
                color: #ff6500;
                padding-bottom: 15px;
            }
            .u-name {
                color: #999;
            }
        }

        .c-total {
            text-align: right;
            line-height: 42px;
            color: #999;
        }
    }

    #pager{
        text-align: right;
        margin: 0;

        @mixin pageAS{

            min-width: 35px;
            _width:35px;
            height: 27px;
            border-radius: 1px;
            border: #ddd 1px solid;
            background: #f6f6f6;
            padding: 0;
            text-align: center;
            line-height: 27px;
            margin-right: 3px;
        }

        a{
            @include pageAS;
        }

        a.ui-pager-prev,a.ui-pager-next{
            width:65px;
        }

        a.ui-pager-current{
            background: #fff;
            border: #fff 1px solid;
            color: #e33b3d;
            font-weight: bold;
        }

        span{
            @include pageAS;
        }
    }

    .ETab .tab-main li {
        padding: 5px 20px 5px 0;
    }

}
/*二手*/
.ershou{
    .consult{
        .search-from{
            button{
                background: $baseColorErshou;
            }
        }
    }
}

