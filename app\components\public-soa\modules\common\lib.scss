@charset "utf-8";
/**
 * Variables
 */
/* 颜色 */
// 价格红
$colorPriceRed: #e4393c;
// 拍拍二手绿色
$baseColorErshou: #01c30b;

// 链接蓝
$colorLinkBlue: #005aa0;

// 废弃 车管家在用
$color01: #e4393c;
$color02: #005aa0;
$color03: #005aa0;

/* 字体 */
$font-st: 'simsun';
$font-yahei: 'microsoft yahei';
$font-vernada: vernada;


//部分参考了: https://github.com/marvin1023/sassCore/blob/master/core/_css3.scss

// 各个浏览器的属性前缀，true表示开启，false表示不开启
//-----------------------------------------------------
$prefixForWebkit:             true !default;
$prefixForMozilla:            true !default;
$prefixForMicrosoft:          true !default;
$prefixForOpera:              false !default; //opera从版本15开始转向webkit，所以默认为false，不输出o前缀
$prefixNo:                    true !default;

// prefixer
// 用于在属性上加前缀
// 默认这里将只输出webkit前缀和标准（如果要开启opera的可以将$prefixForOpera设置为true）
//-----------------------------------------------------
@mixin prefixer($property, $value, $prefixes: o webkit moz) {
  @each $prefix in $prefixes {
    @if $prefix == webkit and $prefixForWebkit == true {
      -webkit-#{$property}: $value;
    }
    @else if $prefix == moz and $prefixForMozilla == true {
      -moz-#{$property}: $value;
    }
    @else if $prefix == ms and $prefixForMicrosoft == true {
      -ms-#{$property}: $value;
    }
    @else if $prefix == o and $prefixForOpera == true {
      -o-#{$property}: $value;
    }
  }
  @if $prefixNo {
    #{$property}: $value;
  }
}

// disable prefix
// 设置所有前缀和标准为false，用于@keyframes
@mixin disable-prefix-for-all() {
  $prefixForWebkit:      false;
  $prefixForMozilla:     false;
  $prefixForMicrosoft:   false;
  $prefixForOpera:       false;
  $prefixNo:             false;
}

// 在各自的@if判断里面，先禁用所有的前缀，然后开启对应的前缀
// 最后输出标准的时候，直接禁用所有的前缀，开启标准
// example:
// @include keyframes(pulse, webkit moz){
//     0% {
//         opacity: 1;
//         @include transform(scale(1));
//     }
//     50% {
//         opacity: 0.7;
//         @include transform(scale(0.9));
//     }
//     100% {
//         opacity: 1;
//         @include transform(scale(1));
//     }
// };
@mixin keyframes($name, $prefixes: webkit o moz) {
  $originalPrefixForWebkit: $prefixForWebkit;
  $originalPrefixForMozilla: $prefixForMozilla;
  $originalPrefixForMicrosoft: $prefixForMicrosoft;
  $originalPrefixForOpera: $prefixForOpera;
  $originalPrefixNo: $prefixNo;

  @each $prefix in $prefixes {
    @if $prefix == webkit and $originalPrefixForWebkit == true {
      @include disable-prefix-for-all();
      $prefixForWebkit: true;
      @-webkit-keyframes #{$name} {
        @content;
      }
    }
    @if $prefix == moz and $originalPrefixForMozilla == true {
      @include disable-prefix-for-all();
      $prefixForMozilla: true;
      @-moz-keyframes #{$name} {
        @content;
      }
    }
    @if $prefix == ms and $originalPrefixForMicrosoft == true {
      @include disable-prefix-for-all();
      $prefixForMicrosoft: true;
      @-ms-keyframes #{$name} {
        @content;
      }
    }
    @if $prefix == o and $originalPrefixForOpera == true {
      @include disable-prefix-for-all();
      $prefixForOpera: true;
      @-o-keyframes #{$name} {
        @content;
      }
    }
  }

  @include disable-prefix-for-all();
  $prefixNo: true;
  @keyframes #{$name} {
    @content;
  }

  $prefixForWebkit:       $originalPrefixForWebkit;
  $prefixForMozilla:      $originalPrefixForMozilla;
  $prefixForMicrosoft:    $originalPrefixForMicrosoft;
  $prefixForOpera:        $originalPrefixForOpera;
  $prefixNo:              $originalPrefixNo;
}

// Transform
//-----------------------------------------------------
@mixin transform($property...) {
  @include prefixer(transform, $property);
}

@mixin transform-origin($axes) {
  // x-axis - left | center | right  | length | %
  // y-axis - top  | center | bottom | length | %
  // z-axis -                          length
  @include prefixer(transform-origin, $axes);
}

@mixin transform-style ($style: preserve-3d) {
  // flat | preserve-3d
  @include prefixer(transform-style, $style);
}

// animation
//
// example:
// @include animation(large 6s infinite);
@mixin animation ($animations...) {
  @include prefixer(animation, $animations);
}


@mixin animation-name ($names...) {
  @include prefixer(animation-name, $names);
}

@mixin animation-duration ($times...) {
  @include prefixer(animation-duration, $times);
}

// animation-delay
//
// example:
// @include animation-delay(0.2s);
@mixin animation-delay ($times...) {
  @include prefixer(animation-delay, $times);
}

// transition
//
// example:
// @include transition(all 0.2s ease-in-out, opacity 1s);
@mixin transition($properties...) {
  @if length($properties) >= 1 {
    @include prefixer(transition, $properties);
  }

  @else {
    $properties: all 0.15s ease-out;
    @include prefixer(transition, $properties);
  }
}

//Ext
//-----------------------------------------------------

@mixin clearfix {
	&:after {
	    content: ".";
	    display: block;
	    height: 0;
	    clear: both;
	    visibility:hidden
	  }
  display: inline-block;
  * html & {height:1%}
  display:block;
  *+html &{min-height:1%}
}

// @ inline-block
@mixin inline-block {
  display: inline-block;
  *display: inline;*zoom:1;
}

// @ 圆角
@mixin border-radius ($r...){
	-webkit-border-radius: $r;
	-moz-border-radius: $r;
	border-radius: $r;
}

// @ 垂直颜色渐变
// @ type{String:开始颜色，结束颜色}
@mixin gradient-vertical($s:#f8f8f8,$e:#ebebeb){
	background-color:$s;
	background-image: -moz-linear-gradient(top, $s,$e);
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, $s), color-stop(1, $e));
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{$s}', endColorstr='#{$e}', GradientType='0');
	-ms-filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#{$s}', endColorstr='#{$e}');
	background-image: linear-gradient(to bottom, $s 0%, $e 100%);
}

//
// @ 方块阴影 Box Shadow
// @ type{String:最小宽，最大宽，类名}
//
@mixin box-shadow($shadow...) {
  @if length($shadow) >= 1 {
    @include prefixer(box-shadow, $shadow);
  }

  @else {
    $shadow: 2px 2px 3px rgba(0, 0, 0, 0.12);
    @include prefixer(box-shadow, $shadow);
  }
}
//
// @ 半透明 alpha
// @ type{String: 百分比}
//
@mixin alpha($o:70) {
  filter: alpha(opacity=$o);
  -moz-opacity: $o/100;
  opacity: $o/100;
}

//------------------------------------
// @ 响应式 最小宽
// @ type{String:最小宽，类名}
//
// example:
// @include mediaMin(480, root480) {
//     #mytestDiv{
//         text-align: center;
//         font-size: 20px;
//     }
// }

@mixin mediaMin ($w,$class){
	@media screen and (min-width: #{$w}){
		@content;
	}
	.#{$class}{
		@content;
	}
}

//
// @ 响应式 最大宽
// @ type{String:最大宽，类名}
//
//@include mediaMax(480, root480) {
//    #mytestDiv{
//        text-align: center;
//        font-size: 20px;
//    }
//}

@mixin mediaMax ($w,$class){
	@media screen and (max-width: #{$w}){
		@content;
	}
	.#{$class}{
		@content;
	}
}

//
// @ 响应式 区间宽
// @ type{String:最小宽，最大宽，类名}
//
//@include mediaMinMax(480, root480) {
//    #mytestDiv{
//        text-align: center;
//        font-size: 20px;
//    }
//}
@mixin mediaMinMax ($minw,$maxw,$class){
	@media screen and (min-width: #{$minw}) and (max-width: #{$maxw}){
		@content;
	}
	.#{$class}{
		@content;
	}
}

/**
 * Buttons mixins & functions
 */

// Button base mixins
@mixin btn-size($size...){
  @if length($size) > 1{
    width : nth($size,2);
    height : nth($size,1);
    line-height : nth($size,1);
  }@else if length($size) == 1{
    height: $size;
    line-height: $size;
  }
}

@mixin btn-gradient($s,$e){
  border: 1px solid $e;
  @include gradient-vertical($s,$e);
}

// icons
@mixin icons($width, $height){
    width: $width;
    height: $height;
    overflow:hidden;
    line-height: 1000px;
}

/**
 * 边栏模块
 */
@mixin mAside() {
    .m-aside {
        margin-bottom: 15px;
        h3 {
            font: bold 14px $font-yahei;
        }
        .mt {
            background-color: #f7f7f7;
            border: 1px solid #eee;
            padding: 10px;
        }
        .mc {
            padding: 10px 10px 0;
            border: 1px solid #eee;
            border-top: none;
            overflow: visible;
        }
    }
    .m-aside .no-padding,
    .no-padding {
        padding: 0;
    }
}
/**
 * 内容模块
 */
@mixin mContent() {
    .m-content {
        margin-bottom: 15px;
        h3 {
            font: bold 14px $font-yahei;
        }
        .mt {
            // 修复 mini 加入购物车遮挡问题
            position: relative;
            background-color: #f7f7f7;
            border: 1px solid #eee;
            padding: 10px;
            _zoom: 1;

            .extra {
                .item {
                }
                position: absolute;
                top: 0;
                right: 0;

                .item {
                    float: right;
                    *display: inline;
                    margin-right: 10px;
                    padding-top: 8px;
                }
                .btn-primary {
                    margin-top: -3px;
                }
            }
        }
        .mc {
            overflow: visible;
        }
    }
}

