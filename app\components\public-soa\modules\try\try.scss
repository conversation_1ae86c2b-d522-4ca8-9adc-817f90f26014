@import '../common/lib';

.try-report {
    .com-try-title {
        margin: 12px 0;
        padding-left: 10px;
        border-left: 2px solid #e4393c;
        color: #e4393c;
        font-size: 16px;
        font-family: 'microsoft yahei';
        font-weight: bold;
    }

    td.com-i-column {
        .p-comment {
            margin-bottom: 0;

            strong {
                cursor: pointer;
            }

            .content {
                cursor: pointer;
            }

            .try-report-detail {
                margin-bottom: 20px;

                .thumb-content {
                    max-height: 110px;
                    overflow: hidden;
                }

                .all-descriptions {
                    display: none;

                    p a {
                        margin: 5px 0;
                    }
                }
            }

            .try-report-show-number {
                margin-top: 10px;
                display: none;

                .show-number {
                    color: #999;
                }

                .view-show {
                    color: #005aa0;
                }
            }
        }

        .p-show-img td {
            cursor: pointer;
        }
    }

    .ui-page {
        padding-top: 15px;
        padding-bottom: 5px;
        float: right;
    }

    //鍩烘湰缁撴瀯 涓轰簡鍏煎鏂拌瘎浠风粨鏋�
    .com-table-header {
        height: 30px;
        line-height: 30px;
        border: 1px solid #ddd;
        overflow: hidden;
        background-color: #f7f7f7;

        .item {
            float: left;
            font-weight: bold;
        }

        .column1 {
            width: 428px;
            text-align: center;
        }
        .column2 {
            width: 75px;
            padding-right: 10px;
            text-align: center;
        }
        .column3 {
            width: 110px;
            padding-right: 10px;
            text-align: center;
        }
        // .column4{
        // 	padding-left: 10px;
        // 	width: 95px;
        // }
        .column5 {
            width: 135px;
            text-align: center;
            _width: 132px;
        }

    }

    .com-table-main {
        width: 100%;
    }

    .comments-item {
        margin-top: -1px;
        padding: 10px;
        border: 1px solid #ddd;
    }

    div.com-item-main {
        _zoom: 1;
        .column1 {
            width: 408px;
            margin-right: 10px;
        }
        .column2 {
            width: 75px;
            margin-right: 10px;
        }
        .column3 {
            padding-right: 10px;
            width: 110px;
        }
        // .column4{
        // 	padding-right: 5px;
        // 	width: 100px;
        // }
        .column5 {
            width: 125px;
        }
    }
    table.com-item-main {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        .column1 {
            width: 418px;
        }
        .column2 {
            width: 85px;
        }
        .column3 {
            width: 120px;
        }
        .column5 {
            width: 125px;
        }
    }
    td.com-i-column {
        padding: 0;
        .p-comment {
            margin-bottom: 5px;
            line-height: 22px;
            word-break: break-all;
            word-wrap: break-word;

            .desc {
            }
            .time {
                display: inline-block;
                font-weight: normal;
                color: #999;
            }
        }

        .p-show-img {
            margin-bottom: 10px;
            table {
                float: left;
                margin-right: 5px;
            }
            td {
                width: 80px;
                height: 80px;
                border: 1px solid #ddd;
                text-align: center;
                vertical-align: middle;
            }
            .comment-show-pic-wrap {
                //			display: inline-block;
                //			width: 80px;
                //			height: 80px;
                cursor: url(i/big.cur), auto;
            }
            .show-number {
                display: inline-block;
                margin-top: 64px;
                color: #999;
            }

            .view-show {
                display: inline-block;
                margin-top: 64px;
                color: #005aa0;
            }
        }

        .p-tabs {
            margin-bottom: 5px;

            .item {
                display: inline-block;
                height: 22px;
                line-height: 22px;
                margin-right: 3px;
                padding: 0 5px;
                background-color: #fff1e4;
            }
            .num {
                color: #999;
            }

            .skin1 {
                background-color: #ecf9ff;
            }
        }

        .p-operate {
            .reply, .nice {
                color: #999;
                cursor: pointer;
            }
            .reply {
                margin-right: 20px;
            }
        }

        .grade-star {
            width: 75px;
            height: 14px;
            background: url(i/newicon20140910.png) no-repeat -183px -239px;

            &.g-star1 {
                background-position: -169px -239px;
            }
            &.g-star2 {
                background-position: -154px -239px;
            }
            &.g-star3 {
                background-position: -139px -239px;
            }
            &.g-star4 {
                background-position: -124px -239px;
            }
            &.g-star5 {
                background-position: -109px -239px;
            }
        }

        .type-item {
            line-height: 22px;
            .label {
                color: #999;
            }
            .text {
            }
        }

        .user-item {
            //padding-left: 22px;
            line-height: 22px;

            .user-ico {
                width: 16px;
                height: 16px;
                float: left;
                //margin-left: -22px;
                margin-right: 5px;
                margin-top: 3px;
                //display: inline;
            }

            a.user-name {
                color: #005aa0;
            }

            .u-vip-level {
                margin-right: 5px;
            }

            .u-addr {
                color: #999;
            }
            .user-access {
                color: #005aa0;
            }
            .buy-time {
                white-space: nowrap;
            }
        }

        .user-item-pop {
            position: relative;
            *z-index: 10;

            div.comment-tooltips {
                position: absolute;
                width: 438px;
                height: 209px;
                left: -330px;
                top: 25px;
                z-index: 1000;
            }

            div.comment-tooltips i { left: 354px; }
            div.comment-tooltips em { left: 355px; }
        }

    }
}
.root61 {
    .try-report {
        .com-table-header {
            padding: 0 20px;
            .column1 {
                width: 528px;
            }
            .column2 {
                width: 75px;
                padding-right: 30px;
            }
            .column3 {
                width: 120px;
                padding-right: 60px;
            }
            // .column4{
            // 	padding-left: 0;
            // 	width: 150px;
            // }
            .column5 {
                width: 135px;
                _width: 132px;
            }
        }
        .comments-item {
            padding: 20px;
        }
        div.com-item-main {
            .column1 {
                width: 508px;
                margin-right: 20px;
            }
            .column2 {
                width: 85px;
                margin-right: 20px;
            }
            .column3 {
                padding-right: 20px;
                width: 160px;
            }
            // .column4{
            // 	padding-right: 10px;
            // 	width: 140px;
            // }
            .column5 {
                width: 135px;
            }
        }
        table.com-item-main {
            .column1 {
                width: 528px;
            }
            .column2 {
                width: 105px;
            }
            .column3 {
                width: 180px;
            }
            .column5 {
                width: 135px;
            }
        }

        .reply-textarea {
            .reply-input { width: 894px; }
        }

        .comment-reply-item {
            .comment-operate {
                .reply-textarea {
                    .reply-input {
                        width: 870px;
                    }
                }
            }
        }
    }
}

.try-entry {
    .try-product,.try-btn {
        padding: 15px 0;
    }
    .try-product {
        float:left;
        width: 590px;
        .root61 & {
            width: 810px;
        }
        .p-img {
            float:left;
            margin: 0 15px
        }
        .p-name {
            height: 1em;
            line-height: 1em;
            min-height: 0;
            overflow:hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .p-price {
            padding: 15px 0 25px 0;
        }
        .p-apply {
            font-size: 14px;
            strong {
                color: $colorPriceRed;
                font-size: 20px;
            }
        }
    }
    .try-btn {
        float: right;
        width: 160px;
        margin-right:15px;
        strong {
            color: $colorPriceRed;
        }
        .time-left {
            white-space: nowrap;
            padding-bottom:20px;
            padding-top:30px;
        }
        .btn-primary {
            width: 115px;
        }
    }
}