define('PUBLIC_ROOT/modules/detail/nav', function(require, exports, module) {

    var G = require('PUBLIC_ROOT/modules/common/core');
    require('PUBLIC_ROOT/plugins/jQuery.scroller');
    require('JDF_UNIT/trimPath/1.0.0/trimPath');

    var template = '\
    {for item in navItems}\
    <li data-id="detail-tag-id-${item.id}" \
        clstag="shangpin|keycount|product|maodian-${item.text}" \
        data-text="${item.text}" {if item_index==0} class="current"{/if}>\
        <a href="#none">\
            <i class="arrow-l">\
                <b class="layer1"></b>\
                <b class="layer2"></b>\
            </i>\
            <i class="arrow-r"></i>${item.text}\
        </a>\
    </li>\
    {/for}';

    var Nav = function (opts) {
        this.$el = opts.$el || $('<div></div>');
        this.$wrap = opts.$wrap || $('#detail');
        this.template = opts.template || template;

        this.onStart = opts.onStart || function () {};
        this.onEnd = opts.onEnd || function () {};

        this.targetSelector = opts.targetSelector || '[name^="detail-tag-id-"]';
        this.items = this.$wrap.find(this.targetSelector);

        this.init();
    };

    Nav.prototype = {
        init: function () {
            var detailContent = $('.detail-content');

            if ( this.items.length > 0 ) {
                this.$el.show();
                detailContent.addClass('z-have-detail-nav');
                // this.setNavContent();
                this.bindEvent();
            } else {
                detailContent.removeClass('z-have-detail-nav');
            }
        },
        onScroll: function () {
            var allInWindow = [];

            // 元素是否在窗口内
            function inWindow($el) {
                var wHeight = $(window).height();
                var bTop = $('body').scrollTop() || $('html').scrollTop();
                var eTop = $el.offset().top;

                return wHeight + bTop > eTop && bTop < eTop;
            }

            this.tags.each(function(i) {
                var $this = $(this);
                if ( inWindow($this) ) {
                    allInWindow.push( $this );
                }
            });

            // 如果有n个标签在可视范围内高亮第一个
            if ( allInWindow.length > 0 ) {
                var id = allInWindow[0].attr('name').replace('detail-tag-id-', '');

                this.listItems.removeClass('current');
                this.listItems.filter('[data-id="detail-tag-id-'+ id +'"]').addClass('current');
            } else {
//                this.listItems.removeClass('current');
            }
        },
        bindEvent: function () {
            var _this = this;

            $('body').scroller({
                onScroll: function() {
                    _this.onScroll();
                }
            });

            this.$el.delegate('li', 'click', function() {
                var id = $(this).attr('data-id').replace('detail-tag-id-', '');
                var text = $(this).attr('data-text');
                var targetTop = _this.$wrap.find('[name=detail-tag-id-' + id + ']').offset().top;

                //修正.tab-main未脱离文档流时导致定位不准
                if(!$("#detail .tab-main").hasClass("pro-detail-hd-fixed")) {
                    targetTop -= 39;
                }

                $('body,html').animate({
                    scrollTop: targetTop - 50
                });

                var area = pageConfig._CURR_AREA;
                log('skupage', 'cateid', id, G.cat[2], G.cat[1], G.cat[0], G.sku, area && area.provinceName, area && area.cityName, text);
            });

            if ( !$.browser.isIE6() ) {
                this.$el.parent().scroller({
                    delay: 10,
                    stopThreshold: $('#J-detail-nav').outerHeight(),
                    onStart: function() {
                        _this.$el.addClass('fixed-top');
                        _this.onStart(_this);
                    },
                    onEnd: function() {
                        _this.$el.removeClass('fixed-top');
                        _this.onEnd(_this);
                    }
                });
            }

        },
        collectTags: function () {
            var tags = this.items;
            var len = tags.length, i = 0;
            var value, id;
            var text = '导航';

            var result = {
                navItems: []
            };

            this.tags = tags;

            if ( len > 0 ) {
                for( i; i < len; i++ ) {
                    value = tags.eq(i).attr('name');
                    text = tags.eq(i).attr('text');

                    if ( value && value != '' && text ) {
                        id = value.replace('detail-tag-id-', '');

                        result.navItems.push({
                            id: id,
                            text: text
                        });
                    }
                }
            }

            return result;
        },
        setNavContent: function () {
            var data = this.collectTags();

            this.$el.find('#J-detail-content-tab').html(this.template.process(data));
            this.listItems = this.$el.find('li');
        }
    };

    module.exports = Nav;
});
