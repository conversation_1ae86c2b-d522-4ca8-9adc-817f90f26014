/**
 * 脚印模块初始化
 */
define('MOD_ROOT/footmark/footmark', function(require, exports, module) {
    var Recommend     = require('MOD_ROOT/common/tools/recommend');
    var tools         = require('MOD_ROOT/common/tools/tools');

    require('JDF_UNIT/trimPath/1.0.0/trimPath');
    require('PLG_ROOT/jQuery.imgScroll');

    var TPL_maybe_like = '\
        <div class="mt clearfix">\
            <h3 class="title">猜你喜欢</h3>\
            <div class="extra">\
                <div class="item">\
                    <span class="change" clstag="personal|keycount|myhistory|hyp407" class="txt">1/1</span>\
                </div>\
            </div>\
        </div>\
        <div class="mc">\
            <a href="#none" class="guess-ctl guess-prev" id="guess-prev" clstag="shangpin|keycount|product|hyp407"></a>\
            <a href="#none" class="guess-ctl guess-next" id="guess-next" clstag="shangpin|keycount|product|hyp407"></a>\
            <div id="guess-scroll">\
            <ul class="may-like-list clearfix"\
                clstag="shangpin|keycount|product|cainixihuan">\
                {for item in data}\
                <li data-push="${pageConfig[skuHooks].push(item.sku)}" \
                    data-clk="${pageConfig.getABTestClk(item.clk)}" clstag="shangpin|keycount|product|hyp1">\
                    <div class="p-img">\
                        <a href="//item.jd.com/${item.sku}.html" title="${item.t}" target="_blank">\
                            <img src="${pageConfig.FN_GetImageDomain(item.sku)}n2/${item.img}" alt="${item.t}" width="160" height="160" />\
                        </a>\
                        <div class="p-name"><a href="//item.jd.com/${item.sku}.html" target="_blank" title="${item.t}">${item.t}</a></div>\
                    </div>\
                    <div class="p-price J-p2-${item.sku}">￥${item.jp}</div>\
                </li>\
                {/for}\
            </ul>\
            </div>\
        </div>';

    pageConfig.getABTestClk = function (clk) {
        var version = pageConfig.guessABVersion || '';
        var shopId = pageConfig.product.shopId || '';
        return clk.replace(/enb=1/g, 'enb=1$test=' + version + '$shopid=' + shopId);
    };

    function init($el, cfg) {
      return;
        var sku = cfg.skuid;
        var lim = pageConfig.wideVersion && pageConfig.compatible ? 23: 19;
        var isBook = document.body.id == 'book'
            || cfg.pType === 3
            || cfg.pType === 4;

        var html = '\
            <div class="m m-content hide may-like">\
                <div class="loading-style1"><b></b>加载中，请稍候...</div>\
            </div>\
            <div class="m m-content hide recent-view">\
                <div class="loading-style1"><b></b>加载中，请稍候...</div>\
            </div>';

        $el.html(html);

        var ridGuess = 0;
        var url = '//api.m.jd.com?'

        if (isBook) {
            ridGuess = 202000;
            url = '//api.m.jd.com'
        } else if(cfg.isEBook) {
            ridGuess = 619054;
        }

        if (cfg.isRLShop) {
            ridGuess = 902029
            url = '//api.m.jd.com?'
        }

        var ridRecent = 202001;

        var ck = isBook ? 'pinId,lighting,pin,bview' : 'pinId,lighting,pin,ipLocation,atw,aview';

        if(!pageConfig.isOversea) {
            // 全站脚印猜你喜欢
            new Recommend({
                url: url,
                $el: $el.find('.may-like'),
                skuHooks: 'SKUS_may_like',
                template: TPL_maybe_like,
                param: {
                    p: ridGuess,
                    sku: sku,
                    ck: ck,
                    lim: lim,
                    // pin: ''
                },
                isNewMixer: isBook,
                // loadPrice: !cfg.isEBook,
                callback: function(hasData, r) {
                    bindHover.call(this);
                    bindScroll.call(this);
                    if(cfg.isEBook && r.success) {
                        var arr = [];
                        $.each(r.data, function (i, n) {
                            arr.push(n.sku);
                        });

                        // var priceNum = require('MOD_ROOT/ebook/ebook').priceNum;
                        // var opts = {
                        //     skus: arr,
                        //     $el: $el.find('.may-like'),
                        // }

                        // priceNum(opts);
                    }
                }
            });
        }

        function bindHover() {
            this.$el.delegate('li .p-img', 'mouseover', function () {
                $(this).addClass('hover');
            });
            this.$el.delegate('li .p-img', 'mouseout', function () {
                $(this).removeClass('hover');
            });
        }
        function bindScroll() {
            var $scroller = this.$el.find('#guess-scroll');
            var $prev = this.$el.find('#guess-prev');
            var $next = this.$el.find('#guess-next');

            var num = pageConfig.compatible&&pageConfig.wideVersion ? 6 : 5;

            $scroller.imgScroll({
                disableClass: 'disable',
                disableClassPerfix: 'guess',
                prev: $prev,
                next: $next,
                visible: num,
                step: num,
                status: true,
                statusWrapSelector: '.change'
            });
        }
    }

    return init;
});
