@import "../common/lib";
.itemInfo-wrap {
    #local-tips {
        a {
            color: $colorLinkBlue;
            &:hover {
                color: $colorPriceRed;
            }
        }

    }
    .summary-tips,
    .local-tips {
        // height: 15px;
        // margin-bottom: 15px; // 温馨提示多3px
        .tips-list {
            li {
                font-size: $baseFontSize;
                float: left;
                // margin-right: 2px;
                // font-family: simsun;
                color: #1a1a1a;
                a:hover{
                    color: #e3393c;
                }
                em{
                    margin: 0 5px;
                    color: #eee; 
                }
            }
        }
    }
    .summary-tips { // 默认显示一行，多行折叠，暂时只处理温馨提示
        display: flex;
        margin-top: 10px;
        dt {
            width: 80px;
            flex-shrink: 0;
            font-size: 14px;
            line-height: 28px;
            color: rgba(136, 139, 147, 1);
        }
        // height: 18px;
        .tips-list { 
            line-height: 28px;
            height: 28px;
            overflow: hidden;
            background: #fff;
            // position: relative;
            // z-index: 9; // hover 时 z-index 高于标题
            &:hover {
                height: auto;
            }
        }
    }
}

/* PC 专业享商品显示二维码 */
.mobile-only {
    height: 80px;
    width: 245px;
    background: #f8f8f8;
    margin-left: 60px;
    .text {
        padding: 24px 5px 0;
    }
}
/*ebook*/
.ebook{
    .tips-list a{
        color: #5e69ad;
    }
}
.special-service{
    padding-top: 8px;
    span{
        display: inline-block;
        padding: 0 4px;
        line-height: 20px;
        margin-right: 7px;
        background: #ccc;
        color: #666;
    }
}

.er-same {
    .er-same-tip {
        @include inline-block;
        border: 1px solid #ccc;
        padding: 5px 10px;
        margin-left:10px;
        margin-top:10px;
    }
}
// 百亿补贴限购温馨提示
.bybt-detail{
   padding: 20px; 
   .goods-info{
        background: rgba(255, 248, 242, 1);
        padding: 10px;
        text-align: center;
        border-radius: 4px;
        border-top: 1px solid rgba(255, 230, 186, 1);
        font-size: 14px;
        color: rgba(255, 128, 0, 1);
        font-weight: 400;
        i{
            display: inline-block;
            margin-right: 6px;
            vertical-align: -3px;
            width: 14px;
            height: 16px;
            background-image: url(https://img10.360buyimg.com/imagetools/jfs/t1/232466/27/23544/444/66ebcd80F8daf7a63/e3da899c1da72fe2.png);
            background-size: 100%;
        }
   }
   .goods-list{
        margin-top: 10px;
        height: 350px;
        overflow-x: scroll;
        li{
            margin-bottom: 10px;
            display: inline-block;
            .good-img{
                width: 48px;
                height: 48px;
                float: left;
                margin-right: 6px;
                border-radius: 4px;
           }
           .good-content{
                width: 360px;
                float: right;
                .good-title{
                    float: left;
                    margin-bottom: 8px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    width: 350px;
                    color: rgba(26, 26, 26, 1);
                }
                .good-date{
                    color: rgba(128, 128, 128, 1);
                    .good-time{
                        float: left;
                    }
                    .good-num{
                        float: right;
                    }
                }
           }
        }
       
   }
}

.cfy-tips{
    font-size: 15px;
    color: #888B93;
    margin-bottom: 12px;
    span{
        color: #1988FA;
        margin-right: 6px;
    }
}
