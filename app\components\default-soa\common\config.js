module.exports = {
    _layout: 'views/layout/layout.html',
    _blocks: {
        style: '\
        {% block style %}\
            {{ super() }}\
            <!--after-->\
            {{ Tag("link", "./common.css") }}\
        {% endblock %}',
        script: '\
        {% block script %}\
            <!--before-->\
            {{ super() }}\
            {{ Tag("script", "./common.js") }}\
        {% endblock %}'
    },
    data: {
        'id': 'mod-default',
        'class': 'mod-def'
    },
    mock: {
        proxy: '//cd.jd.com',
        result: {

        }
    }
};
