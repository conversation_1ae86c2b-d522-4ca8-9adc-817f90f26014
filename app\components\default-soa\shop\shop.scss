.shop-information {
  padding-bottom: 24px;
  border-bottom: 1px solid #dfdfdf;
}

.detail-shop {
  display: flex;
  align-items: center;
  line-height: 1;

  .year-five-star {
    height: 16px;
    margin-top: 8px;
  }

  .five-star {
    height: 16px;
    margin-top: 8px;
  }

  .star-level {
    display: flex;
    align-items: center;
    margin-top: 8px;

    .star {
      margin-right: 4px;
    }

    .star-half {
      width: 11px;
      height: 11px;
      display: block;
      background: url(https://img13.360buyimg.com/imagetools/jfs/t1/264521/10/14668/488/6790f80cF20e9e146/07e6123c15c59d33.png)  left top / 100% 100% no-repeat;
    }

    .star-gray {
      width: 11px;
      height: 11px;
      display: block;
      background: url(https://img14.360buyimg.com/imagetools/jfs/t1/258695/37/14778/677/6790ecf2Fd68f9ba4/6bab2238d061a650.png)  left top / 100% 100% no-repeat;
    }

    .star-light {
      width: 11px;
      height: 11px;
      display: block;
      background: url(https://img11.360buyimg.com/imagetools/jfs/t1/259360/29/14690/614/6790ecedF52e86c8e/4e94a8290a59443a.png)  left top / 100% 100% no-repeat;
    }

    .star-num {
      font-size: 12px;
      color: rgba(250, 44, 25, 1);
      margin-left: 4px;
      font-family: JDZhengHeiVRegular2-1;
    }
  }

  .border-img {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border: 1px solid rgba(0, 0, 0, 0.06);
    margin-right: 12px;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
  }

 .logo {
    width: 100%;
    display: block;
  }
  
  .name-tag {
    margin-right: 24px;
  }
  
  .name {
    font-size: 16px;
    font-weight: 600;
    color: rgba(12, 12, 36, 1);
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 240px;
  }
  
  .name-tag .flex {
    margin-top: 8px;
  }
  
  .flex {
    display: flex;
    align-items: center;
  
  }
  
  .tag {
    font-size: 12px;
    color: rgba(196, 113, 39, 1);
    margin: 3px;
  }
  
  .self-support {
    background: linear-gradient(135deg, #ff475d 0%, #ff1a2d 100%);
    padding: 3px 2px;
    border-radius: 2px;
    font-size: 12px;
    color: #fff;
    margin-right: 6px;
  }
  
  .tag-list .line {
    height: 12px;
    border-right: 0.5px dashed rgba(196, 113, 39, 0.3);
  }
  
  .tag-list {
    display: flex;
    border-radius: 2px;
    align-items: center;
    background: rgba(255, 130, 0, 0.08);
    color: rgba(181, 105, 26, 1);
    border: 1px solid rgba(181, 105, 26, 0.2);
  }
  
  .advantage {
    display: flex;
    align-items: center;
    flex: 1;
  }
  
  .advantage .line {
    width: 1px;
    height: 24px;
    background: rgba(0, 0, 0, 0.06);
    margin: 0 16px;
  }
  
  .advantage .item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .advantage .text {
    font-size: 14px;
    color: rgba(136, 139, 148, 1);
  }
  
  .value {
    margin-top: 8px;
    font-size: 14px;
    color: rgba(26, 26, 26, 1);
    font-family: JDZhengHeiVRegular2-1;
  }
  
  .level {
    color: rgba(255, 15, 35, 1);
    font-size: 14px;
    margin-left: 2px;
  }
  
  .btns > div:last-child {
    margin-left: 12px;
  }
  
  .shop-btn {
    display: flex;
    border: 1px solid rgba(136, 139, 148, 1);
    width: 98px;
    height: 36px;
    border-radius: 4px;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    cursor: pointer;
  
    .im {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
  
  .shop-icon {
    width: 14px;
    height: 14.5px;
    display: block;
    margin-right: 6px;
  }
  
  .shop-btn .text {
    font-size: 14px;
    font-weight: 500;
    color: rgba(26, 26, 26, 1);
  }
}

.detail-goods {
  display: flex;
  background: #fff;
  line-height: 1;
  height: 156px;
  margin-top: 16px;

  .left-arrow,
  .right-arrow {
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    cursor: pointer;
  }
  
  .arrow {
    display: block;
    width: 12px;
    height: 12px;
  }
  
  .left-arrow {
    margin-right: 8px;
  }
  
  .shop-goods-wrap {
    position: relative;
    overflow: hidden;
    flex: 1;
  }
  
  .shop-goods-list {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
  }
  
  .shop-goods-list > div:last-child {
    margin-right: 0;
  }
  
  .item {
    width: 117px;
    border-radius: 4px;
    position: relative;
    margin-right: 8px;
    border-radius: 4px;
    overflow: hidden;
    box-sizing: border-box;
  }
  
  .goods-img-wrap {
    width: 117px;
    height: 117px;
    background: #fafafa;
  }
  
  .goods-img {
    height: 100%;
    display: block;
    margin: 0 auto;
    cursor: pointer;
  }
  
  .add-cart {
    width: 24px;
    height: 24px;
    display: block;
    cursor: pointer;
  }
  
  .tag {
    width: 32px;
    height: 20px;
    border-radius: 4px 0px 4px 0px;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    color: #fff;
    font-size: 12px;
  }
  
  .price {
    font-size: 16px;
    color: #fa2c19;
    font-family: JDZhengHeiVRegular2-1;
  }
  
  .name {
    font-size: 14px;
    color: #1a1a1a;
    margin: 8px 0 3px 0;
  }
  
  .flex {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    padding-bottom: 5px;
    padding-top: 10px;
  }
}

.detail-classification {
  display: flex;
  height: 147px;
  margin-top: 16px;

  .left-arrow,
  .right-arrow {
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    cursor: pointer;
  }
  
  .left-arrow {
    margin-right: 8px;
  }
  
  .arrow {
    display: block;
    width: 12px;
    height: 12px;
  }
  
  .shop-cate-wrap {
    position: relative;
    overflow: hidden;
    flex: 1;
  }
  
  .shop-cate-list {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
  }
  
  .shop-cate-list > div:last-child {
    margin-right: 0;
  }
  
  .item {
    width: 117px;
    border-radius: 4px;
    position: relative;
    margin-right: 8px;
    border: 1px solid rgba(0, 0, 0, 0.02);
    cursor: pointer;
    box-sizing: border-box;
    height: 147px;
    overflow: hidden;
  }
  
  .cate-img-wrap {
    width: 117px;
    height: 117px;
    background: #fafafa;
  }
  
  .cate-img {
    height: 100%;
    display: block;
    margin: 0 auto;
  }
  
  .name {
    font-size: 14px;
    color: #1a1a1a;
    margin: 6px 0 0 0;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 0 8px;
  }
}

