/**
 * scroller插件
 */
;(function ($) {
    var scroller = function (that, options) {
        this.opts = $.extend({
            delay: 50,
            start: null,
            end: null,
            startThreshold: 0,
            stopThreshold: 0,
            onStart: function() {},
            onEnd: function() {},
            onScroll: function() {}
        }, options);

        this.$el = $(that);

        this.init();
    };

    scroller.prototype = {
        init: function() {
            this.bindEvent();
            $(window).bind('scroll', function() {
                $(this).trigger('onscroller');
            });
        },
        bindEvent: function() {
            var _this = this;
            var timer;
            $(window).bind('onscroller', function() {
                clearTimeout(timer);
                timer = setTimeout(function() {
                    _this.onScroll();
                    clearTimeout(timer);
                }, _this.opts.delay);
            });
        },
        onScroll: function(e) {
            var top = $(document).scrollTop();
            var start = this.opts.start || this.$el.offset().top;
            var end = this.opts.end || start + this.$el.outerHeight();
            var wHeight = $(window).height();
            
            if ( !!this.opts.end && typeof this.opts.end === 'object' ) {
                end = this.opts.end.offset().top;
            }

            if ( top > start + this.opts.startThreshold && top < end - this.opts.stopThreshold ) {
                this.opts.onStart.apply(this);
            } else {
                this.opts.onEnd.apply(this);
            }
            this.opts.onScroll.apply(this);
        }
    };

    $.fn.scroller = function (options, callback) {
        return this.each(function () {
            // 实例化插件对象
            var plugin = new scroller(this, options, callback);

            $(this).data('scroller', plugin);
        });
    };
})(jQuery);

