/* dropdown */
@import '../common/lib';
@import './__sprite';

#stock-address.hover {
    z-index: 3;
}
.stock-address {
    float: left;
    margin-top: -3px;
    margin-left: -5px;
    .head {
        white-space: nowrap;
        .text {
            float: left;
        }
    }
    dl {
        margin:0 10px;
        padding-bottom: 15px;
    }
    dd {
        padding-top: 10px;
        a {
            margin-right: 10px;
        }
    }
    .line {
        margin: 0 10px 10px;
        height: 0;
        overflow:hidden;
        border-bottom: 1px dotted #eee;
    }
    .content {
        background-color:#fff;
        width: 380px;
        padding-top: 20px;
    }
    .address-used {
        cursor: pointer;
        .arrow {
            float: right;
            margin-top: 5px;
            background: url(../common/i/arr-open.png) no-repeat 0 0;
            background: url(../common/i/arr-close.png) no-repeat 0 0\9;
        }

        &.more {
            .arrow {
                background: url(../common/i/arr-close.png) no-repeat 0 0;
                background: url(../common/i/arr-open.png) no-repeat 0 0\9;
            }
        }
        li a {
            width: auto;
        }
    }
    .address-select {
        cursor: pointer;
        .arrow {
            float: right;
            margin-top: 5px;
            background: url(../common/i/arr-open.png) no-repeat 0 0;
            background: url(../common/i/arr-close.png) no-repeat 0 0\9;
        }

        &.clicked {
            .arrow {
                background: url(../common/i/arr-close.png) no-repeat 0 0;
                background: url(../common/i/arr-open.png) no-repeat 0 0\9;
            }
        }
    }
    .hw-tab-con {
        position: relative;
        .hw-letters {
            li {
                padding: 0 2px 0 0;
                margin-right:0;
                display: inline;
                a:hover, a.clicked {
                    background: none;
                    color:$colorPriceRed
                }
            }
        }
        div.area-letter {
            padding-top:10px;
            padding-bottom:10px;
            color: #999;
        }
        div.area-letter-curr {
            color: $colorPriceRed;
        }
        ul {
            padding-bottom:10px;
            border-bottom: 1px solid #eee;
        }
    }
    .hw-area-wrap {
        height: 300px;
        overflow-y: scroll;
    }
}
.stock-address-list {
    li {
        @include inline-block;
    }
    a {
        @include inline-block;
        padding: 0 2px;
    }
    .clicked & {
        display: block;
    }
    a:hover,a.clicked {
        background: $colorPriceRed;
        color: #fff;
    }
}

.address-used {
    &.auto .stock-address-list ul {
        height: auto;
    }
    &.clicked .stock-address-list ul {
        height: 9em;
        overflow: auto;
        .address {
            width:238px;
        }
    }
    //div.more ul.less {
    //    height: 1.5em;
    //    overflow:hidden;
    //}
    .stock-address-list {
        ul {
            height: 1.5em;
            overflow:hidden;
        }
        li {
            display: block;
        }
        a {
            display: block;
        }
        a:hover, li.selected a {
            background-color: transparent;
            color: $colorPriceRed;
        }
        .name {
            display: inline-block;
            width: 4em;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 15px;
        }
        .address {
            display: inline-block;
            width: 248px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

.address-tab {
    .tab li {
        @include inline-block;
        margin-right: 5px;
        border: 1px solid #ddd;
        padding: 2px 10px;
        background:#fff;
        color: #005aa0;
        font: bold 14px 'microsoft yahei';
        border-bottom: none;
    }
    .tab .current {
        border: 2px solid #e4393c;
        border-bottom: 2px solid #fff;
    }
    .tab-con {
        border-top: 2px solid #e4393c;
        margin-top: -2px;
        padding-top: 10px;
        li {
            width: 6.4em;
            margin-right: 5px;
            margin-bottom: 5px;
            a {
                margin-right: 0;
            }
            &.long-area {
                width: 13em;
            }
            &.longer-area {
                width: 26em;
            }
        }
    }
}

.summary-service {
    color: #1A1A1A;
    i {
        @include inline-block;
        vertical-align: top;
        margin-left: 2px;
    }
    a {
        color:#999;
        margin: 0 5px;

        .clothing & {
            color: $colorPriceRed;
        }
    }
}
#summary-supply {
    clear: both;
    margin-top:-10px;
}
#summary-supply.stand-alone {
    margin-top: 0;
}
.summary-line {
    height: 0;
    overflow:hidden;
    border-bottom: 1px dotted #dfdfdf;
    margin-bottom:15px;
}
.summary-stock {
    z-index: 2;
    .store {
        margin-bottom: 5px;
        .store-selector {
            margin-right: 10px;
            float: left;
            div {
                float: left;
                z-index: 1;
            }
            .head {
                z-index: 2;
            }
            .sprite-arr-close {
                margin-left: 5px;
                display: inline-block;
                vertical-align: middle;
            }
        }

        .store-prompt {
            margin-right: 10px;
            color: #999;
            height: 24px;
            float: left;
            strong {
                margin-right: 5px;
                float: left;
                font-size: 12px;
            }
            .store-prompt-num {
                float: left;
                margin-right: 10px;
            }
            .support {
                float: left;
                .support-name {
                    color: #666;
                }
                img {
                    margin: 0 3px;
                }
            }
            .tip {
                float: left;
            }
        }
    }

    .dcashDesc i {
        @include inline-block;
        @include icons(16px, 16px);
        vertical-align: middle;
        background-image: url(i/question.png);
    }
}

.choose-support {
    li {
        margin-right: 10px;
        float: left;
        *position: relative;
    }

    a {
        display: inline-block;
        line-height: 18px;
        span {
            position: relative;
            *left: 5px;
            z-index: -1;
            display: inline-block;
            border: 1px solid #ced2e6;
            line-height: 16px;
            line-height: 14px \9;
            border-radius:8px;
            padding: 0 5px 0 18px;
            *padding-left: 15px;
            color: #5e69ad;
            margin-left: -18px;
            margin-left: -15px \9;
            *margin-left: 5px;
        }

        .sprite-jnbt {
            @include sprite-jnbt;
        }
        .sprite-old2new {
            @include sprite-old2new;
        }
        .sprite-question {
            @include sprite-question;
        }
        .sprite-sjwx {
            @include sprite-sjwx;
        }
        .sprite-tcbg {
            @include sprite-tcbg;
        }
        .sprite-giftbuy {
            @include sprite-giftbuy;
        }
        .sprite-baina {
            @include sprite-baina;
        }
        .sprite-zengzhi {
            @include sprite-zengzhi;
        }

        i {
            @include inline-block;
            //margin-right: 5px;
            vertical-align: -4px;
            *position: absolute;
            *z-index: 1;
        }
    }
}

.promise-icon {
    a:hover{
        color: #666;
    }
    height: 24px;
    .title {
        margin-right: 5px;
    }
    .icon-list {
        // @include inline-block;
        float: none;
        position: relative;
        //width: 228px;
        height: 20px;
        margin-left: 28px;
        overflow:hidden;

        img {
            margin-right: 2px;
        }
    }

    ul {
        background-image: none;
        float: left;
        @include clearfix;

        a {
            color: #646FB0;
            float: left;
            overflow:hidden;
            *display: inline;
            margin-bottom: 5px;
            padding: 0 10px;
            border-right: 1px solid #EAEAEA;
            white-space: nowrap;
            &:hover {
                color: #E4393C;
            }
        }

        .noborder {
            border-right: none;
        }
    }
}
.promise-icon-more {
    padding-left:5px;
    .icon-list {
        position: relative;
        padding-right: 20px;
        padding: 0 26px 0 4px;
        i {
            display: block;
            position: absolute;
            top: 6px;
            right: 5px;
            width: 13px;
            height: 7px;
            background: url(../common/i/arr-close.png) 0 0 no-repeat;
        }
    }
}
.hover .icon-list {
    background-color: #fff;
    z-index: 3;
    border: 1px solid #ccc;
    padding: 5px 25px 5px 3px;
    overflow: visible;
    height: auto;
    top: -5px;
    left: -3px;
}
.hover .icon-list i {
    top: 10px;
    right: 9px;
    background: url(../common/i/arr-open.png) 0 0 no-repeat;
}
/*book*/
.ebook{
    .summary-stock{
        display: none;
    }
}

.DJD-tips {
    padding: 5px 10px;
    background: #fefff7;
    border: 1px solid #ccc;
    color: #e4393c;
    box-shadow: 1px 1px 1px 1px rgba(0, 0, 0, 0.1);
    i {
        @include inline-block;
        width: 16px;
        height: 16px;
        vertical-align: middle;
    }
    .sprite-close {
        position: absolute;
        top: 8px;
        right: 10px;
        cursor: pointer;
        font: 14px $font-st;
    }
    .sprite-info {
        margin-right: 5px;
        @include sprite-info;
    }
}

/*增值浮层*/
.zengzhi-layer{
    padding: 20px;
    h3{
        font-family: "Microsoft YaHei";
        font-size: 14px;
        color: #5e69ad;
        padding-bottom: 8px;
        .icon-zengzhi{
            display: inline-block;
            width: 22px;
            height: 22px;
            background: url(i/icon-zengzhi.png) no-repeat;
            margin-right: 5px;
            vertical-align: -4px;
        }
    }
    .zengzhi-intr{
        font-family: $font-st;
        font-size: 12px;
        color: #000;
        line-height: 180%;
    }
    .zengzhi-info{
        font-family: $font-st;
        background: #f7f7f7;
        border: 1px solid #ddd;
        margin-top: 15px;
        padding: 14px 20px 20px;
        font-size: 12px;
        color: #666;
        line-height: 180%;
        li{
            padding: 4px 0;
            list-style-position: inside;
        }
    }
    .process{
        margin: 0 16px;
        .arrow{
            display: block;
            width: 16px;
            height: 15px;
            background: url(i/icon-arrow.png) no-repeat;
            margin: 0 auto;
        }
    }
    .process-item{
        background: #fff;
        border: 1px solid #ddd;
        margin: 8px 0;
        text-align: center;
        line-height: 26px;
        span{
            color: #e33c3f;
        }
    }
    .btn-confirm{
        margin:0 auto;
        display: block;
        width: 122px;
        line-height: 36px;
        background: #e33c3f;
        color: #fff;
        border-radius: 2px;
        font-size: 14px;
        text-align: center;
        font-weight: bold;
        &:hover{
            background: #ee484b;
        }
    }
}




//预售金融券接口优惠券样式
#summary-presale-wellfare {
    padding-bottom: 2px;

    .dt{
        line-height: 32px;
    }

    .dd{
        padding-top: 8px;
    }
    .quan-item {
        position: relative;
        float: left;
        height: 16px;
        padding-left: 2px;
        line-height: 15px;
        text-align: center;
        border-top: 1px solid #df3033;
        border-bottom: 1px solid #df3033;
        background: #ffdedf;
        font-size: 14px;
        white-space: nowrap;
        margin-right: 13px;
        cursor: pointer;
    }
    .text {
        padding: 0 10px;
        color: #df3033;
        font-size: 12px;
    }
    .quan-item s, .quan-item b {
        position: absolute;
        top: -1px;
        display: block;
        height: 18px;
        width:2px;
        overflow:hidden;
        background: url(i/quan-arr.gif) 0 0 no-repeat;
    }
    .quan-item s {
        left: 0;
        background-position: -2px 0;
    }
    .quan-item b {
        right: -2px;
    }
    .quan-item:hover{
        background: #fff4f4;
        s,b{
            background: url(i/quan-arr-hover.gif) no-repeat;
        }
        s{
            background-position: -2px 0;
        }
    }
    .more-btn {
        font-family: SimSun;
        color: #999;
        position: relative;
        float: left;
        height: 16px;
        line-height: 18px;
        cursor: pointer;
    }
}