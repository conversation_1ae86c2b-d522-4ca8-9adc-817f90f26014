@import "lib";

/**
 * product 商品列表基础元素样式
 */
.p-price {
    em {
        color: #999;
    }
    .price {
        color: $colorPriceRed;
    }
    strong {
        color: $colorPriceRed;
        font-size: 14px;
        font-family: Verdana;
    }
}
.p-img {
    padding-bottom: 5px;
}
.p-name {
    max-height: 3em;
    min-height: 36px;
    _height: 3em;
    line-height: 1.5em;
    overflow: hidden;
    margin-bottom: 5px;
} /*ebook*/
.ebook {
    .aside {
        .p-name {
            min-height: auto;
            _height: auto;
        }
        .plist {
            padding: 0 9px;
        }
    }
    .m-aside {
        .mc {
            .plist {
                li {
                    padding: 10px 0;
                }
            }
        }
    }
}
.plist {
    margin-bottom: 10px;
}
//面包屑下拉框样式
.plist-1 {
    padding: 0 10px;

    .fore1 .p-num, .fore2 .p-num, .fore3 .p-num {
        background: $colorPriceRed;
    }

    li {
        clear: both;
        height: 85px;
        position: relative;
        .p-num {
            position: absolute;
            left: 0;
            top: 0;
            width: 20px;
            height: 20px;
            line-height: 20px;
            border-radius: 10px;
            background-color: #d5d5d5;
            color: #fff;
            text-align: center;
        }
    }
    .p-img {
        float: left;
        margin-right: 5px;
    }
}

:root .plist-2 li .p-name {
    filter: none;
}
//文字在图的内部最下面
.plist-2 {
    li {
        position: relative;
        overflow: hidden;
        .p-name {
            position: absolute;
            left: 0;
            right: 0;
            bottom: -42px;
            background: rgba(0, 0, 0, .7);
            filter: progid:DXImageTransform.Microsoft.gradient(
                    startcolorstr=#B2000000,
                    endcolorstr=#B2000000
                );
            padding-left: 10px;
            a {
                color: #fff;
            }
        }

        &.hover, &:hover {
            .p-name {
                bottom: 19px;
            }
        }
    }
}

.pop-hot, #sp-new {
    .plist-2 {
        li {
            .p-num {
                bottom: 5px;
                top: auto;
            }

            .p-count {
                margin-left: 22px;
            }

            .p-price {
                text-align: center;
            }

            .p-name {
                width: 140px;
                left: 50%;
                margin-left: -80px;
                padding: 0 10px;
            }

            &.hover, &:hover {
                .p-name {
                    bottom: 24px;
                }
            }
        }
    }
}
