define('MOD_ROOT/suits/suits', function(require, exports, module) {
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Tools = require('MOD_ROOT/common/tools/tools');
    var G = require('MOD_ROOT/common/core');
    //var Area = require('MOD_ROOT/address/area');
    require('//static.360buyimg.com/item/assets/address/area');
    var Area =  common_getAreaMap();
    require('MOD_ROOT/EDropdown/EDropdown');
    require('PLG_ROOT/jQuery.imgScroll');
    require('MOD_ROOT/ETooltips/ETooltips');
    
    

    function getTPL(type) {
        var TPL = {
            item: '\
            <div class="item" data-pid="{0}">\
                <a href="#none" class="title" \
                    data-drop="head" \
                    data-pid="{0}" \
                    clstag="shangpin|keycount|product|xuanzetaozhuang_{1}">{2}</a>\
                <div class="suits-panel J-suits-panel"></div>\
            </div>',
            colorAttr: '\
            {for attr in list}\
                {if attr.attr=="color" && attr.data.length>0}\
                <div class="p-pic J-p-pic" data-type="${attr.attr}" data-value="${attr.data[0]}">\
                    <a href="#none" class="sprite-arrowL J-thumb-prev"></a>\
                    <a href="#none" class="sprite-arrowR J-thumb-next"></a>\
                    <div class="J-thumb-scroll thumb-scroll-wrap">\
                        <ul class="pic-list">\
                            {for color in attr.data}\
                            <li class="inner-list {if Number(color_index)==0} current{/if}" data-attr="${color}" data-imgsrc="${colorMap[color]}">\
                                <a href="#none"><img title="${color}" width="20" height="20" src="//img10.360buyimg.com/n1/s20x20_${colorMap[color]}" alt="${color}"/></a>\
                            </li>\
                            {/for}\
                        </ul>\
                    </div>\
                </div>\
                {else}\
                    {if attr.data.length}\
                    <select class="J-attr-check yb-item-cat" data-open="false" data-type="${attr.attr}" data-value="${attr.data[0]}">\
                        {for item in attr.data}\
                            <option value="${item}" title="${item}">${item}</option>\
                        {/for}\
                    </select>\
                    {/if}\
                {/if}\
            {/for}',
            packList: '\
            <div class="suits-box"">\
                <div class="J-scroll">\
                <ul class="lh clearfix">\
                    {for product in productList}\
                    {var isLastItem = Number(product_index)+1==productList.length}\
                    {if product.colorList.length>0}\
                    {var currItem = product.colorList[0]}\
                    <li {if product.colorList.length>3} data-scroll="true"{/if}  class="J-sku-item sku-item {if isLastItem} last{/if} "\
                        data-push="${pageConfig.SKU_suits.push(currItem.skuId)}" data-sku="${currItem.skuId}">\
                        <div class="p-img J-p-img">\
                            <a href="//item.jd.com/${currItem.skuId}.html" target="_blank">\
                                {if currItem.skuPicUrl}\
                                <img width="100" height="100" src="${pageConfig.FN_GetImageDomain(currItem.skuId)}n1/s100x100_${currItem.skuPicUrl}">\
                                {/if}\
                            </a>\
                            <div class="no-stock J-no-stock hide">${areaName}无货</div>\
                            <div class="no-item J-no-item hide">无此组合商品</div>\
                        </div>\
                        <div class="J-attrs"></div>\
                        <div class="p-name J-p-name">\
                            <a href="//item.jd.com/${currItem.skuId}.html" target="_blank">${currItem.skuName}</a>\
                        </div>\
                        <div class="p-check-price-num">\
                            {if typeof product.selectState!="undefined"&&product.selectState!=1}<input type="checkbox" class="p-checkbox J-choose"/>{/if}\
                            {if typeof packType!="undefined"&&packType=="1"&&pageConfig.product.cat[2]==798}\
                                <strong class="p-price">￥${currItem.finalPrice}</strong>\
                            {/if}\
                            {if typeof currItem.count!="undefined"}<span class="J-count p-count">×${currItem.count}</span>{/if}\
                            {if typeof currItem.num!="undefined"&&currItem.num!="1"}<span class="J-count p-count">×${currItem.num}</span>{/if}\
                        </div>\
                        <i class="plus">+</i>\
                    </li>\
                    {/if}\
                    {/for}\
                </ul>\
                </div>\
                {if productList.length>3}\
                <a href="javascript:;" class="J-arrow arrow-prev disabled"><i class="sprite-arrow-prev"></i></a>\
                <a href="javascript:;" class="J-arrow arrow-next disabled"><i class="sprite-arrow-next"></i></a>\
                {/if}\
            </div>\
            <div class="suits-detail">\
                <div class="price-box">\
                    <div class="suits-price J-suits-price">\
                        <span class="text">套装价：</span>\
                        <span class="p-price">\
                            <strong {if suitType==1} class="J-p-${packId}"{/if}>${packPrice}</strong>\
                        </span>\
                    </div>\
                    {var isHideSavePrice = typeof packType=="undefined" || packType=="0"}\
                    <div class="suits-save-price J-suits-save-price {if isHideSavePrice} hide {/if}">\
                        <span class="text">节省</span><span class="p-price">\
                            <strong>{if typeof discount!="undefined"}${discount}{/if}</strong>\
                        </span>\
                    </div>\
                </div>\
                <div class="btns">\
                    <a href="#none" class="btn-primary J-btn">购买套装</a>\
                </div>\
                <div class="suits-tips fr hl_red hide">套装内部分商品无货</div>\
            </div>',
            packList2: '\
            <div class="suits-box"">\
                <div class="J-scroll">\
                <ul class="lh clearfix">\
                    {for product in productList}\
                    {var isLastItem = Number(product_index)+1==productList.length}\
                    {var currItem = product}\
                    <li class="J-sku-item sku-item {if isLastItem} last{/if} "\
                        data-push="${pageConfig.SKU_suits.push(currItem.skuId)}" data-sku="${currItem.skuId}">\
                        <div class="p-img J-p-img">\
                            <a href="//item.jd.com/${currItem.skuId}.html" target="_blank">\
                                {if currItem.skuPicUrl}\
                                <img width="100" height="100" src="${pageConfig.FN_GetImageDomain(currItem.skuId)}n1/s100x100_${currItem.skuPicUrl}">\
                                {/if}\
                            </a>\
                            <div class="no-stock J-no-stock hide">${areaName}无货</div>\
                            <div class="no-item J-no-item hide">无此组合商品</div>\
                        </div>\
                        <div class="J-attrs"></div>\
                        <div class="p-name J-p-name">\
                            <a href="//item.jd.com/${currItem.skuId}.html" target="_blank">${currItem.skuName}</a>\
                        </div>\
                        <div class="p-check-price-num">\
                            {if typeof currItem.selectState!="undefined"&&currItem.selectState!=1}<input type="checkbox" class="p-checkbox J-choose"/>{/if}\
                            {if typeof packType!="undefined"&&packType=="1"&&pageConfig.product.cat[2]==798}\
                                <strong class="p-price">￥${currItem.finalPrice}</strong>\
                            {/if}\
                            {if typeof currItem.count!="undefined"}<span class="J-count p-count">×${currItem.count}</span>{/if}\
                            {if typeof currItem.num!="undefined"&&currItem.num!="1"}<span class="J-count p-count">×${currItem.num}</span>{/if}\
                        </div>\
                        <i class="plus">+</i>\
                    </li>\
                    {/for}\
                </ul>\
                </div>\
                {if productList.length>3}\
                <a href="javascript:;" class="J-arrow arrow-prev disabled"><i class="sprite-arrow-prev"></i></a>\
                <a href="javascript:;" class="J-arrow arrow-next disabled"><i class="sprite-arrow-next"></i></a>\
                {/if}\
            </div>\
            <div class="suits-detail">\
                <div class="price-box">\
                    <div class="suits-price J-suits-price">\
                        <span class="text">套装价：</span>\
                        <span class="p-price">\
                            <strong {if suitType==1} class="J-p-${packId}"{/if}>${packPrice}</strong>\
                        </span>\
                    </div>\
                    {var isHideSavePrice = typeof packType=="undefined" || packType=="0"}\
                    <div class="suits-save-price J-suits-save-price {if isHideSavePrice} hide {/if}">\
                        <span class="text">节省</span><span class="p-price">\
                            <strong>{if typeof discount!="undefined"}${discount}{/if}</strong>\
                        </span>\
                    </div>\
                </div>\
                <div class="btns">\
                    <a href="#none" class="btn-primary J-btn">购买套装</a>\
                </div>\
                <div class="suits-tips fr hl_red hide">套装内部分商品无货</div>\
            </div>'
        };

        return TPL[type];
    }

    // 节省字段判断条件 {if typeof suitDiscount=="undefined"||suitDiscount<=0} hide{/if}
    // http://jsonpmock.xyz/7d2928563b7325804e189c690e72efaf
    // iPhone 7 http://jsonpmock.xyz/e93663c36d0ad1af2e9bd6b459262ae0
    // 多颜色 http://jsonpmock.xyz/220076d00068ea1e19f805d08aa54547
    var Suits = {
        init: function($el, cfg) {
            var _this = this;
            _this.$el = $el || $('#choose-suits');

            _this.packList = [];
            _this.cfg = cfg;

            // 缓存后加载数据，点击时再渲染
            _this.packData = {};
            _this.suitSkuMap = {};
            if (_this.$el.length) {
                Event.addListener('onStockReady', function (r) {
                    _this.packList = [];
                    // _this.packData = {};
                    // _this.suitSkuMap={};
                    var result = r.stock && r.stock.data;
                    // _this.getData();
                    if(result && result.suit) {
                        if (result.price.p){
                            result.suit.mainSkuPrice = result.price.p
                        }
                        var suitSkuMap = result.suitSkuMap;
                        result.suit["suitSkuMap"] = suitSkuMap;
                        if (result.suit.itemList){
                            var itemList = result.suit.itemList;
                            for (var i = 0; i < itemList.length; i++) {
                                if(!itemList[i].suitType || itemList[i].suitType == null){
                                    itemList[i].suitType = 2;
                                }
                                var productList = itemList[i].productList;
                                for(var j = 0; j< productList.length;j++){
                                    var skuId = productList[j].skuId;
                                    if(suitSkuMap && suitSkuMap[skuId]!=null){
                                        productList[j].state = suitSkuMap[skuId].state
                                        productList[j].stock = suitSkuMap[skuId].stock
                                    }
                                }
                            }
                        }
                        _this.handleData(result.suit);
                        _this.bindEvent();
                        _this.isInited = true;
                    }else{
                        _this.$el.find('.dd').empty();
                        _this.$el.hide();
                        return false;
                    }
                });

                // _this.$el.delegate('.item [data-pid]', 'click', function(e){
                //     console.log("pidmmmmm",$(e.target).data('pid'))
                //     // $.proxy(_this.handleClick, _this)
                //     console.log("_this",_this)
                //     console.log("this",this)
                //     _this.handleClick(e)
                // });
            }
            
            return this
        },
//         getData: function() { //调用处已经原本删除了
//             var _this = this;
//             var host = '//api.m.jd.com'
//             if(pageConfig.product && pageConfig.product.colorApiDomain){
//             host = pageConfig.product && pageConfig.product.colorApiDomain
//         }
//             $.ajax({
// //                 url: 'http://jsonpmock.xyz/74c57575451732db0a0bb5bb212d903f',
// //                 url: 'http://jsonpmock.xyz/74c57575451732db0a0bb5bb212d903f',
// //                 url: 'http://jsonpmock.xyz/f7bcb01a57e73f0c4b1e3595ef661bd3',
// //                url: 'http://jsonpmock.xyz/0dd83bed89e85ec4cad9883fc847ac75',//colorList里有多个
// //                url: '//cd.jd.com/recommend/suit/v2',
//                 url: host +'/recommend',
//                 data: {
//                     sku: _this.cfg.skuid,
//                     cat: _this.cfg.cat.join(','),
//                     area: Tools.getAreaId().areaIds.join('_'),
//                     methods: 'suitv2',
//                     count: 6,
//                     appid: 'item-v3',
//                     functionId: "pc_recommend"
//                 },
//                 scriptCharset: 'gbk',
//                 dataType: 'jsonp',
//                 success: $.proxy(_this.handleData, _this)
//             });
//         },
        rePos: function ($item) {//计算面板展现位置
            var $suitsPanel = $item.find(".J-suits-panel");
            $suitsPanel.css("top", $item.position().top + $item.height() - 1);
        },
        bindEvent: function() {
            var _this = this;
            if(!_this.isInited) {
                _this.$el.undelegate();
                $(document).bind('click.suit', $.proxy(_this.handleDocumentClick, _this));
                _this.$el.delegate('.item [data-pid]', 'click', $.proxy(_this.handleClick, _this));
                _this.$el.delegate('.J-thumb-scroll li', 'click', $.proxy(_this.handleColorClick, _this));
                _this.$el.delegate('.J-attr-check', 'change', $.proxy(_this.handleAttrClick, _this));
                _this.$el.delegate('.J-choose', 'change', $.proxy(_this.handleChooseClick, _this));
                Event.addListener('onHeYueReady', function () {
                    var isHeYue = _this.cfg.isHeYue;
                    // 如果是合约机sku隐藏选择套装
                    if (isHeYue) {
                        _this.$el.hide();
                    } else {
                        if (_this.$el.find('.item').length > 0) {
                            _this.$el.show();
                        }
                    }
                });
                // Event.addListener('onAreaChange', function () {
                //     Suits.init(_this.$el, _this.cfg);
                // });
            }
        },
        handleData: function(suit) {
            //console.log(JSON.stringify(data));
            var _this = this;
            if (!suit || !suit.itemList || !suit.itemList.length) {
                _this.$el.find('.dd').empty();
                _this.$el.hide();
                return false;
            }
            _this.renderItem(suit);
        },
        handleDocumentClick: function (e) {//点击文档，隐藏套装面板
            var _this = this;
            if ( $(e.target).parents('.choose-suits').length < 1 ) {
                if(!$.browser.isIE7()) {
                    _this.$el.find('.item').removeClass('open');
                }
            }
        },
        handleClick: function(e) {//点击套装按钮侦听函数
            var _this = this;
            var $this = $(e.target);
            var pid = $this.data('pid');
            var $item = $this.parents('.item');
            if($item.hasClass('open')) {
                $item.removeClass('open');
                return;
            }

            this.$el.find('.item').removeClass('open');
            $item.addClass('open');
            this.$currSuit = $this.next('.J-suits-panel');

            if (!$this.data('loaded')) {
                
                _this.setSuitContent(pid);
                $this.data('loaded', true);
            }
            _this.rePos($item);
        },
        renderItem: function(data) {//渲染整个套装数据按钮
            var _this = this;
            var itemHTML = '';
            var tpl = getTPL('item');

            _this.data = data;
            _this.suitSkuMap = data.suitSkuMap;
            var packList = data.itemList;

            var specialIndex = 0;
            for (var i = 0; i < packList.length; i++) {
                var item = packList[i];
                //汽车O2O自营实物+虚拟服务套装
                if(item.originalSuitType && item.originalSuitType == 7 && item.extType && item.extType == 1) {

                    if($('.instant-promotion-suite').length == 0){

                        var _title = '选择服务购买商品立享优惠，加入购物车后变价';
                        var _html = '<div class="instant-promotion-suite bt-info-tips" data-title="'+_title+'"><a class="J-icon-hui prom icon fl" href="#none">　</a></div>';
                        var $chooseshopItem = $('#choose-shop .dd .item');
                        if ($chooseshopItem.length && !$chooseshopItem.hasClass("addedService")) { // 如果是增值服务，没“惠”icon
                            $('#choose-shop > .dd > .item').after(_html);
                        }
                        
                        $('#choose-shop').find('.bt-info-tips').each(function() {
                            if ($(this).data('title')) {
                                $(this).ETooltips({
                                    pos: 'bottom',
                                    zIndex: 10,
                                    width: 300,
                                    defaultTitleAttr: 'data-title'
                                })
                            }
                        });

                    }

                }else{
                    /*展示非小类型的套装选项*/
                    var name = '';
                    specialIndex ++;
                    name = '优惠套装' + (i + 1);
                    itemHTML += tpl.format(item.packId, _this.cfg.cat[2], name);
                }


                var mainSkuPrice = "";
                if(item.packType == "1" && data.mainSkuPrice) {
                    mainSkuPrice = data.mainSkuPrice;
                }
                //添加主品信息
                if(item.suitType != 1) {
                    item.productList.unshift({
                        colorList: [
                        {
                            num:"1",
                            name:"",
                            salesPrice:mainSkuPrice,
                            selectState: 1,
                            skuPicUrl: data.mainSkuPicUrl,
                            skuName: data.mainSkuName,
                            skuId: data.mainSkuId,
                            finalPrice: mainSkuPrice
                        }
                        ]
                    });
                }

                // item.poolList.sort(function (a, b) {
                //     if(!a.selectState) {
                //         return 1;
                //     }
                //     return a.selectState - b.selectState;
                // });
                _this.packData[item.packId] = item;
               
//                _this.packData[item.packId] = {
//                    pList: item.poolList,
//                    suitDiscount: item.suitDiscount,
//                    packPromotionPrice: item.packPromotionPrice,
//                    onSale: 1,
//                    type: 'p'
//                }
            }

            //如果没有任何优惠套装,则隐藏套装整行
            if(specialIndex != 0){

                _this.$el.find('.dd').html(itemHTML);
                _this.$el.show();
            }
        },
        handleAttrData: function (poolList) {//组装成属性维度的Object
            function hasItem(arr, item) {
                var spliter = '\u2299';
                var res = spliter + arr.join(spliter) + spliter;
                var pattern = spliter + item + spliter
                return res.indexOf(pattern) > -1;
            }

            var attrSheme = poolList.attrSheme;
            var colorMap = poolList.colorMap;
            var skuData = poolList.colorList;
            for(var i = 0; i < skuData.length; i++) {
                var sku = skuData[i];//单条数据

                colorMap[sku.color] = sku.skuPicUrl;
                for (var j = 0; j < attrSheme.length; j++) {
                    var attrName = attrSheme[j].attr;//color size spec
                    var attrValue = sku[attrName];//单条数据里一项的值

                    if (!attrSheme[j].data) {
                        attrSheme[j].data = [];
                    }
                    if (attrValue && !hasItem(attrSheme[j].data, attrValue)) {
                        attrSheme[j].data.push(attrValue);
                    }
                }
            }
        },
        handleColorClick: function (e) {//选择颜色
            var _this = this;
            var $this = $(e.currentTarget);
            var attr = $this.data('attr');
            var imgSrc = $this.data('imgsrc');
            
            $this.parent().find('li').removeClass('current');
            $this.addClass('current');

            var $skuItem = $this.parents('.J-sku-item');
            var $currBigImg = $skuItem.find('.J-p-img img');
            $currBigImg.attr('src', '//img10.360buyimg.com/n1/s100x100_' + imgSrc);

            $this.parents('.J-p-pic').attr('data-value', attr);
            var $item = $this.parents('.item');
            var obj = _this.getEqualObjBySkuItem($skuItem);
            if(obj) {
                $skuItem.find(".J-p-img a").attr("href", '//item.jd.com/' + obj.skuId + '.html').attr('target', '_blank').removeClass('disabled');
                $skuItem.find(".J-p-name a").attr("href", '//item.jd.com/' + obj.skuId + '.html').attr('target', '_blank').removeClass('disabled');
                $skuItem.find(".J-p-name a").html(obj.skuName);
                _this.setBuyLink($item);
            } else {
                $skuItem.find(".J-p-img a").attr("href", '#none').removeAttr('target').addClass('disabled');
                $skuItem.find(".J-p-name a").attr("href", '#none').removeAttr('target').addClass('disabled');
                $skuItem.find(".J-p-name a").html("");
                _this.setBuyLink($item);
            }
        },
        handleAttrClick: function (e) {//点击属性下拉框
            var _this = this;
            var $this = $(e.currentTarget);
            var $skuItem = $this.parents('.J-sku-item');

            $this.attr('data-value', $this.val());
            var $item = $this.parents('.item');

            var obj = _this.getEqualObjBySkuItem($skuItem);
            if(obj) {
                $skuItem.find(".J-p-img a").attr("href", '//item.jd.com/' + obj.skuId + '.html').attr('target', '_blank').removeClass('disabled');
                $skuItem.find(".J-p-name a").attr("href", '//item.jd.com/' + obj.skuId + '.html').attr('target', '_blank').removeClass('disabled');
                $skuItem.find(".J-p-name a").html(obj.skuName);
                _this.setBuyLink($item);
            } else {
                $skuItem.find(".J-p-img a").attr("href", '#none').removeAttr('target').addClass('disabled');
                $skuItem.find(".J-p-name a").attr("href", '#none').removeAttr('target').addClass('disabled');
                $skuItem.find(".J-p-name a").html("");
                _this.setBuyLink($item);
            }
        },
        handleChooseClick: function (e) {//点击复选框
            var _this = this;
            var $this = $(e.currentTarget);
            var $item = $this.parents('.item');
            _this.setBuyLink($item);
        },
        getEqualObjBySkuItem: function ($skuItem) {//根据颜色尺码等属性查找对应的单个Sku商品
            var _this = this;
            var index = $skuItem.index();
            var $children = $skuItem.find(".J-attrs").children();
            var pid = $skuItem.parents('[data-pid]').attr('data-pid');
            var skuId = $skuItem.attr('data-sku');
            pid = +pid;
            var pObj = {};
            $.each(_this.data.itemList, function (i, n) {
                if(n.packId == pid) {
                    pObj = n;
                }
            });

            // var productList = pObj.productList;
            // var equalObj = pObj.productList[index];
            var colorList = pObj.productList[index].colorList;
            var colorListArr = colorList.slice();
            var equalObj = null;
            $.each(colorListArr, function (ii, nn) {
                var isEqual = true;
                $.each($children, function (i, n) {
                    var $n = $(n);
                    var prop = $n.attr('data-type');
                    var propValue = $n.attr('data-value');
                    if(nn[prop] != propValue) {
                        isEqual = false;
                    }
                });
                if(isEqual) {
                    equalObj = nn;
                }
            });

            return equalObj;
        },
        setSuitContent: function(pid) {//初始化单个套装面板
            var _this = this;
            pageConfig.SKU_suits = [];
            var pack = _this.packData[pid];
            var suitSkuMap = _this.suitSkuMap;
            var areaId = readCookie('areaId');
            pack.areaName = Area.provinceMap[areaId];
            if(pack.suitType === 1){
                var tpl = getTPL('packList2');
                var $html = $(tpl.process(pack));
                _this.$currSuit.html($html);
            }else if(pack.suitType === 2){
                var tpl = getTPL('packList');
                var $html = $(tpl.process(pack));
                _this.$currSuit.html($html);
                //颜色模板渲染
                for(var i = 0; i < pack.productList.length; i++) {
                    var pool = pack.productList[i];
                    if(pool.colorList.length > 1) {
    //                    pool.attrSheme = [{attr: 'color'},{attr: 'size'},{attr: 'spec'}];
                        // if(pool.poolId!=null && suitSkuMap[pool.poolId]!=null && suitSkuMap[pool.poolId].attrSheme!=null){
                        //     pool.attrSheme = suitSkuMap[pool.poolId].attrSheme
                        // }
                        // pool.attrSheme = [];
                        // for (var j = 0; j < pool.saleNames.length; j++) {
                        //     pool.attrSheme.push({attr: pool.saleNames[j]});
                        // }

                        // pool.colorMap = {};
                        // _this.handleAttrData(pool);
                        var colorAttrTPL = getTPL('colorAttr');
                        var $colorAttrHtml = $(colorAttrTPL.process({
                            list: pool.attrSheme,
                            colorMap: pool.colorMap
                        }));
                        $html.find('.J-sku-item').eq(i).find('.J-attrs').html($colorAttrHtml);
                    }
                }
            }
            

            //如果都是可选，就默认选中第一个
            var isAllCanSelect = _this.checkIsAllCanSelect(pack.productList);
            if(isAllCanSelect) {
                _this.$currSuit.find('.J-choose').eq(0).click();
            }

            this.initScroll();
            if (pack.productList.length > 3) {
                this.setScroll();
            }

            var $item = _this.$currSuit.parents('.item');
//            _this.setPackType6Price($item);
            _this.setBuyLink($item);

            // 虚拟组套的价格是组套sku的价格
            if (pack.suitType == 1) {
                Tools.priceNum({
                    skus: [pack.packId],
                    $el: this.$currSuit
                });
            }

            var maxHeight = 0;
            var $suitsPanel = $item.find('.J-suits-panel');
            var $skuItem =  $suitsPanel.find('.J-sku-item');
            $skuItem.each(function (i, n) {
                var $n = $(n);
                if($n.height() > maxHeight) {
                    maxHeight = $n.height();
                }
            });
            maxHeight += 10;
            $skuItem.parents('ul').height(maxHeight);
            $skuItem.parents('.J-scroll').height(maxHeight);
        },
        checkIsAllCanSelect: function (poolList) {//检测某个套装面板里的pool是否都为可选
            var pool;
            var isAllCanSelect = true;
            for (var i = 0; i < poolList.length; i++) {
                pool = poolList[i];
                if(pool.selectState == 1) {
                    isAllCanSelect = false;
                    break;
                }
            }
            return isAllCanSelect;
        },
        setBuyLink: function ($item) {//设置购买链接
            var _this = this;
            var pid = $item.attr("data-pid");
            var $suitsPanel = $item.find('.J-suits-panel');
            var $skuItem =  $suitsPanel.find('.J-sku-item');
            var isAllStock = true;//检测需要加入套装的sku是否都有货
            var canBuySkus = [_this.cfg.skuid];
            var allChooseSkus = [_this.cfg.skuid];
            var suitSkuMap = _this.data.suitSkuMap;
            var isV = _this.packData[pid].suitType == 1;//虚拟套装

            if(!isV) {//非虚拟套装执行库存检测逻辑
                for (var i = 1; i < $skuItem.length; i++) {//第一个是主商品不算，从第二个开始检测是否有货
                    var $n = $skuItem.eq(i);
                    var $choose = $n.find('.J-choose');
                    var isNeedBuy = false;//检测该商品的sku是否需要加入购买套装按钮链接中（可选商品没选中则不需要加入）
                    if($choose.length) {
                        if($choose.is(':checked')) {
                            isNeedBuy = true;
                        } else {
                        }
                    } else {
                        isNeedBuy = true;
                    }
                    var $noStock = $n.find('.J-no-stock');//无货文字提示
                    var $noItem = $n.find('.J-no-item');//无该组合文字提示
                    var obj = _this.getEqualObjBySkuItem($n);//根据颜色尺码等属性查找对应的单个Sku商品
                    if(obj) {
                        $noItem.hide();
                        if (obj.stock!=null && !obj.stock) {//没有库存
                            $noStock.show();
                            $choose.prop('disabled', 'disabled');
                            if(isNeedBuy) {
                                allChooseSkus.push(obj.skuId);
                                isAllStock = false;
                            }
                        } else {//有库存
//                            $n.find('.J-choose').attr('disabled');
                            $choose.removeProp('disabled');
                            $noStock.hide();
                            if(isNeedBuy) {
                                allChooseSkus.push(obj.skuId);
                                canBuySkus.push(obj.skuId);
                            }
                        }
                    } else {
                        $noItem.show();
                        $noStock.hide();
                        $choose.prop('disabled', 'disabled');
                        isAllStock = false;
                    }
                }
            }
            //根据选择的sku重新查询套装价
            if(!isV) {
                _this.calPrice($item, pid, allChooseSkus);
            }

            if(!isAllStock) {
                _this.setBtnDisable($item);
                return;
            }

            var $buyBtn = $item.find('.J-btn');
            $buyBtn.removeClass('btn-disable');

            var url;
            if (isV) {
                url = '//cart.jd.com/gate.action?pid=' + pid + '&pcount=1&ptype=1';
                // 埋点
                $buyBtn.attr('onclick', 'log("product", "xuanzetaozhuang_button",' + pid + ')');
            } else {
                var urlPrefix = '//cart.jd.com/gate.action?';
                if (_this.cfg.isOtc && !_this.cfg.isPop) {
                    urlPrefix = '//cart.yiyaojd.com/gate.action?';
                }
                if (_this.cfg.isCfy) {
                    canBuySkus.unshift(pid)
                    url = '//rx.yiyaojd.com/cart_addItem.action?pid='+ canBuySkus.join('_') +'&ptype=4&pcount=1';
                } else {
                    url = urlPrefix + $.param({
                        pid: pid,
                        pcount: 1,
                        ptype: 3,
                        sku: canBuySkus.join(',')
                    });
                }
                // 埋点
                $buyBtn.attr('onclick', 'log("product", "xuanzetaozhuang_button","' + canBuySkus.join(',') + '")');
            }
            $buyBtn.attr('href', url);
        },
        calPrice: function ($item, sid, skus) {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/suit/suitprice',
                data: {
                    suitId: sid,
                    skuIds: skus.join(','),
                    origin: 1,
                    webSite: 1,
                    appid: 'item-v3',
                    functionId: "pc_suit_suitprice"
                },
                dataType: 'jsonp',
                success: function (r) {
                    var $suitsPrice = $item.find('.J-suits-price strong');
                    var $suitsSavePrice = $item.find('.J-suits-save-price strong');
                    if (!!r && r.packPromotionPrice) {
                        $suitsPrice.html('￥ ' + r.packPromotionPrice.toFixed(2));
                        $suitsSavePrice.html('￥ ' + r.baseSuitDiscount.toFixed(2));
                    } else {
                        $suitsPrice.html('暂无报价');
                        $suitsSavePrice.html('暂无报价');
                    }
                }
            });
        },
        setBtnDisable: function ($item) {
            var $buyBtn = $item.find('.J-btn');
            $buyBtn.addClass('btn-disable');
            $buyBtn.attr('href', "#none");
        },
        initScroll: function () {
            var $items = this.$currSuit.find('[data-scroll="true"]');

            $items.each(function () {
                var $scroll = $(this).find('.J-thumb-scroll');
                var $prev = $(this).find('.J-thumb-prev');
                var $next = $(this).find('.J-thumb-next');

                $scroll.imgScroll({
                    visible: 3,
                    step: 3,
                    prev: $prev,
                    next: $next,
                    showControl: true
                });
            });
        },
        setScroll: function () {
            var $scroll = this.$currSuit.find('.J-scroll');
            var $prev = this.$currSuit.find('.arrow-prev');
            var $next = this.$currSuit.find('.arrow-next');

            $scroll.imgScroll({
                visible: 3,
                step: 3,
                prev: $prev,
                next: $next
            });
        }
    }

    function init(cfg) {
        var $chooseSuit = $('#choose-suits');
        window.suit = Suits.init($chooseSuit, cfg);
    }

    module.exports.__id = 'suits';
    module.exports.init = init;
});
