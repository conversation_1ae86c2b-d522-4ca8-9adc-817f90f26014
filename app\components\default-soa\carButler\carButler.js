define('MOD_ROOT/carButler/carButler', function(require, exports, module) {
    require('MOD_ROOT/ELazyload/ELazyload');
    require('JDF_UNIT/trimPath/1.0.0/trimPath');
    //三级类目    类目ID    对应保养项目  保养项目ID
    //机油        11849        小保养        1
    //添加剂      11850        小保养        1
    //滤清器      11852        大保养        2
    //----        ----        车辆检测       3
    //----        ----       换空调滤芯      4
    //火花塞      6767         换火花塞      5
    //轮胎        9248         换轮胎        6
    //----        ----        四轮定位      7
    //刹车片盘    11859      换刹车盘片      8
    //正时皮带    13244      换正时皮带      9
    //防冻液      6756        换防冻液      10
    //蓄电池      9971        换蓄电池      11
    //车灯        6768         换车灯       12
    //雨刷        6766         换雨刷       13
    var accyCateIdMap = {
        "9248"  :  "6",
        "9971"  :  "11",
        "11849" :  "1",
        "11850" :  "1",
        "11852" :  "2",
        "6767"  :  "5",
        "6766"  :  "13",
        "11859" :  "8",
        "13244" :  "9",
        "6756"  :  "10",
        "6768"  :  "12"
    };
    var apiParams = {};
    //接口后端开发：张科强 京东商城-平台研发部-网站移动研发部-车管家开发组
    var apiConfig = {
        'brand': '//autobeta.jd.com/queryBrands',
        'series': '//autobeta.jd.com/querySeries',
        'year': '//autobeta.jd.com/querySeriesYear',
        'model': '//autobeta.jd.com/queryModel'
    };
    var brandListTemplate =   '{for brand in brandList}'
                            + '<li class="hide" data-initial="${brand.initial}" data-id="${brand.brandId}">'
                            + ' <img data-src="${pageConfig.FN_GetImageDomain(brand_index)}n1/s48x48_${brand.logoUrl}">'
                            + ' <a href="javascript:void(0)">${brand.brandName}</a>'
                            + '</li>'
                            + '{/for}'
                            + '{for brand in hotBrands}'
                            + '<li class="J-hot" data-id="${brand.brandId}">'
                            + ' <img data-src="${pageConfig.FN_GetImageDomain(brand_index)}n1/s48x48_${brand.logoUrl}">'
                            + ' <a href="javascript:void(0)">${brand.brandName}</a>'
                            + '</li>'
                            + '{/for}';

    var seriesListTemplate =  '{for seriesList in data}'
                            + '<div class="menu-brand-title"><i></i>${seriesList_index}</div>'
                            + '<ul class="menu-drop-list">'
                            + '   {for series in seriesList}'
                            + '   <li data-id="${series.seriesId}"><a href="javascript:void(0)">${series.seriesName}</a></li>'
                            + '   {/for}'
                            + '</ul>'
                            + '{/for}';

    var yearListTemplate ='{for series in data}'
                        + '<li data-id="${series.seriesYearId}">'
                        + ' <a href="javascript:void(0)">${series.seriesYear}</a>'
                        + '</li>'
                        + '{/for}';

    var modelListTemplate ='{for model in data}'
                        + '<li data-id="${model.modelId}">'
                        + ' <a href="javascript:void(0)">${model.modelName}</a>'
                        + '</li>'
                        + '{/for}';
    //缓存年款，车型render数据
    //var yearModelData = null;
    var currentYear = null;

    function CarButler (opts) {
        var _this=this;

        this.$el = $(opts.el);
        this.isGoToCarPage = opts.isGoToCarPage||false;
        this.$carBrand  = this.$el.find(".car-filter-item1");
        this.$carSeries = this.$el.find(".car-filter-item2");
        this.$carYear   = this.$el.find(".car-filter-item3");
        this.$carModel  = this.$el.find(".car-filter-item4");
        this.$searchButton = this.$el.find(".car-filter-btn");

        this.loadBrandList();
        this.bindEvent();
        this.carModelId = null;
        if (pageConfig.product.catName) {
            this.$searchButton.html('查找' + pageConfig.product.catName[2]);
        }
    }
    $.extend(CarButler.prototype, {
        loadBrandList: function () {
            var _this = this;

            _this._get(apiConfig.brand, function (response) {
                var hotBrands = getHotBrands(response.data);
                _this.$carBrand.find(".menu-drop-list").html(brandListTemplate.process({brandList: response.data, hotBrands: hotBrands}));
                _this.checkBrandFilterIsHidden();
            });
        },
        loadSeriesList: function () {
            var _this = this;

            if (! apiParams.brand) {
                return;
            }

            _this._get(apiConfig.series, apiParams, function (response) {
                var data = buildSeriesData(response);
                _this.$carSeries.find(".menu-drop-list-container").html(seriesListTemplate.process({data: data}));
            });
        },
        loadYearList: function () {
            var _this = this;

            if (! apiParams.series) {
                return;
            }

            _this._get(apiConfig.year, apiParams, function (response) {
                _this.$carYear.find('.menu-drop-list').html(yearListTemplate.process({data: response.data}));
            });
        },
        loadModelList: function () {
            var _this = this;

            _this._get(apiConfig.model, apiParams, function (response) {
                _this.$carModel.find(".menu-drop-list").html(modelListTemplate.process({data: response.data}));
            });
        },
        bindEvent: function () {
            var _this = this;
            var $brandList = this.$carBrand.find(".menu-drop-list");
            var $brandFilter = this.$carBrand.find(".menu-drop-letter-list");
            var $seriesList = this.$carSeries.find(".menu-drop-list-container");
            var $seriesTrigger = this.$carSeries.find(".trigger");
            var $yearList = this.$carYear.find(".menu-drop-list");
            var $yearTrigger = this.$carYear.find('.trigger');
            var $modelList = this.$carModel.find(".menu-drop-list");
            var $modelTrigger = this.$carModel.find(".trigger");
            var $brands;
            var $carFilterItems=this.$el.find(".car-filter-item1, .car-filter-item2, .car-filter-item3, .car-filter-item4");

            $carFilterItems.die('mouseenter').live('mouseenter',function() {
                $(this).addClass("z-menu-drop-open");
                $(this).ELazyload();
            });
            $carFilterItems.die('mouseleave').live('mouseleave',function() {
                $(this).removeClass("z-menu-drop-open");
            });
            this.$searchButton.die('click').live('click', function (event) {
                if (! _this.carModelId) {
                    event.preventDefault();
                    return false;
                }
            });

            //品牌列表选择、首字母过滤
            $brandList.delegate("li", "click", function ($event) {
                $event.preventDefault();
                var $this = $(this);

                if ($this.hasClass("curr")) {
                    return;
                }
                else {
                    $this.siblings().removeClass("curr").end().addClass("curr");
                    _this._setCurrentBrand($.trim($this.text()), $this.data('id'));
                    _this._setCurrentSeries();
                    _this.loadSeriesList();
                    _this.$el.find('.car-filter-item2 .menu-drop-hint').hide();
                }

                _this.$carBrand.removeClass("z-menu-drop-open");
            });
            $brandFilter.delegate("a", "mouseenter", function ($event) {
                var $this = $(this);
                var $parent = $this.parent();
                var initialLetter = $this.text();

                if ($parent.hasClass("curr")) {
                    return;
                }else {
                    $parent.siblings().removeClass("curr").end().addClass("curr");
                    $brands = $brandList.find("li");

                    if ($parent.hasClass("fore0")) {
                        $brands.hide().filter('.J-hot').show();
                    }else {
                        $brands.each(function () {
                            var $this = $(this);

                            if ($this.data("initial") === initialLetter) {
                                $this.show();
                            }else {
                                $this.hide();
                            }
                        });
                    }
                }
            });
            $brandFilter.delegate("a", "click", function ($event) {
                $event.preventDefault();
            });
            //车系列表选择、未选择品牌时提示信息
            $seriesList.delegate("li", "click", function ($event) {
                $event.preventDefault();
                var $this = $(this);

                if ($this.hasClass("curr")) {
                    return;
                }
                else {
                    $this.siblings().removeClass("curr").end().addClass("curr");
                    _this._setCurrentSeries($this.text(), $this.data('id'));
                    _this._setCurrentYear();
                    _this.loadYearList();
                    _this.$el.find('.car-filter-item3 .menu-drop-hint').hide();
                }

                _this.$carSeries.removeClass("z-menu-drop-open");
            });
            $seriesTrigger.bind("mouseenter", function () {
                if (! apiParams.brand) {
                    $seriesList.parent().addClass("z-menu-drop-hint");
                }
                else {
                    $seriesList.parent().removeClass("z-menu-drop-hint");
                }
            });
            //年款列表选择、未选择车系时提示信息
            $yearList.delegate('li', 'click', function ($event) {
                $event.preventDefault();
                var $this = $(this);

                if ($this.hasClass("curr")) {
                    return;
                } else {
                    $this.siblings().removeClass("curr").end().addClass("curr");
                    _this._setCurrentYear($this.text(), $this.data('id'));
                    _this._setCurrentModel();
                    _this.loadModelList();
                    _this.$el.find('.car-filter-item4 .menu-drop-hint').hide();
                }
                _this.$carYear.removeClass("z-menu-drop-open");
            });
            $yearTrigger.bind("mouseenter", function () {
                if (! apiParams.series) {
                    $yearList.parent().addClass("z-menu-drop-hint");
                }
                else {
                    $yearList.parent().removeClass("z-menu-drop-hint");
                }
            });
            //车型列表选择、未选择年款时提示信息
            $modelList.delegate("li", "click", function ($event) {
                $event.preventDefault();
                var $this = $(this);

                if ($this.hasClass("curr")) {
                    return;
                }
                else {
                    $this.siblings().removeClass("curr").end().addClass("curr");
                    _this._setCurrentModel($this.text());
                    _this.carModelId = $this.data("id");

                    if(_this.isGoToCarPage){                //跳转到车管家页面     
                        _this.updateLink();             
                    }else{      
                        _this.updateModelid();              //不跳转到车管家页面    
                    }
                    
                }

                _this.$carModel.removeClass("z-menu-drop-open");
            });
            $modelTrigger.bind("mouseenter", function () {
                if (! currentYear || currentYear.length == 0) {
                    $modelList.parent().addClass("z-menu-drop-hint");
                }
                else {
                    $modelList.parent().removeClass("z-menu-drop-hint");
                }
            });
        },
        //更新“添加爱车”按钮的URL
        updateLink: function () {
            var url = "//iche.jd.com/market.html?fore=0&referer=1&",
                cid3 = "";
            if (! this.carModelId) {
                return;
            }
            if(pageConfig.product && pageConfig.product.cat){
              cid3 = pageConfig.product.cat[2]||''
            }
            url += "cid3=" + cid3 + "&modelId=" + this.carModelId
            this.$searchButton.attr("href", url);
        },
        //更新“添加爱车”按钮的modeId
        updateModelid: function () {
            if (! this.carModelId) {
                return;
            }
            this.$searchButton.attr("choosedCarmodelid", this.carModelId);
        },
        //添加新的车型
        addNewCar:function(successCallB,errorCallB){
            //cd.jd.com/auto/addmodel?userPin=davidwei_001&modelId=24075
            var _this=this;
            this.$el.find('.car-filter-btn').die('click').live('click',function(){
                var $thisEl=$(this).parents('.car-filter'),
                    isChooseNewCarCompleted=_this.checkChooseNewCarCompleted($thisEl),
                    postData={
                        modelId:$(this).attr('choosedCarmodelid')
                    };
                if( isChooseNewCarCompleted ){
                    $.ajax({
                        url: '//cd.jd.com/auto/addmodel',
                        data: postData,
                        scriptCharset: 'gbk',
                        timeout:3000,
                        dataType: "jsonp",
                        success: function (response) {
                            successCallB && successCallB($thisEl,response);
                        },
                        error:function(){
                            errorCallB && errorCallB($thisEl);
                        }
                    });
                }
            });
        },
        //车型选项重置
        initOptionSate:function(){
            var _this = this;
            var $brandChoose = this.$carBrand.find('.trigger span');
            var $brandFilter = this.$carBrand.find(".menu-drop-letter-list");
            var $brandList= this.$carBrand.find(".menu-drop-list");

            var $seriesChoose = this.$carSeries.find('.trigger span');
            var $seriesTips = this.$carSeries.find(".menu-drop-hint");
            var $seriesList = this.$carSeries.find(".menu-drop-list-container");

            var $yearChoose = this.$carYear.find('.trigger span');
            var $yearTips = this.$carYear.find(".menu-drop-hint");
            var $yearList = this.$carYear.find(".menu-drop-list");

            var $modelChoose = this.$carModel.find('.trigger span');
            var $modelTips = this.$carModel.find(".menu-drop-hint");
            var $modelList = this.$carModel.find(".menu-drop-list");
            var $addNewCarBtn=this.$el.find('.car-filter-btn');

            //品牌重置
            $brandChoose.text('品牌');
            $brandFilter.find('li').removeClass('curr').end().find('li.fore0').addClass('curr');
            $brandList.find('li.hide').hide()
                .end().find('li.curr').removeClass('curr')
                .end().find('li.J-hot').show();
            //车系重置
            $seriesChoose.text('车系');
            $seriesList.html('');
            $seriesTips.show();
            //年款重置
            $yearChoose.text('年款');
            $yearList.html('');
            $yearTips.show();
            //车型重置
            $modelChoose.text('车型');
            $modelList.html('');
            $modelTips.show();
            //添加新车型按钮重置
            $addNewCarBtn.attr('choosedcarmodelid','');
        },
        //品牌首字母没有相关品牌车的隐藏
        checkBrandFilterIsHidden:function(){
            var $brandFilterLi = this.$carBrand.find(".menu-drop-letter-list li");
            var $brandListLi = this.$carBrand.find(".menu-drop-list li");
            var existBrandFilterArr=[];
            $brandListLi.each(function(){
                var letter=$(this).attr('data-initial');
                if( $.inArray(letter,existBrandFilterArr)==-1 ){
                    existBrandFilterArr.push(letter);
                }
            });
            $brandFilterLi.each(function(){
                var thisLetter= $.trim($(this).find('a').text());
                if( $.inArray(thisLetter,existBrandFilterArr)==-1 && (thisLetter!='热门') ){
                    $(this).hide();
                }
            });
        },
        //检查添加新车型是否选择完成
        checkChooseNewCarCompleted:function($thisEl){
            var $brandChoose = $thisEl.find('.car-filter-item1 .trigger span'),
                 $brandFilter = $thisEl.find(".car-filter-item1 .menu-drop-letter-list"),
                 $brandList = $thisEl.find(".car-filter-item1 .menu-drop-list"),
                 $seriesChoose = $thisEl.find('.car-filter-item2 .trigger span'),
                 $yearChoose = $thisEl.find('.car-filter-item3 .trigger span'),
                 $modelChoose = $thisEl.find('.car-filter-item4 .trigger span'),
                 $carFilterItems = $thisEl.find(".car-filter-item1, .car-filter-item2, .car-filter-item3, .car-filter-item4");

            $carFilterItems.removeClass("z-menu-drop-open");
            if( $brandChoose.text()=='品牌' ){                //品牌没有选择
                $brandFilter.find('li').removeClass('curr').end().find('li.fore0').addClass('curr');
                $brandList.find('li.hide').hide().end().find('li.J-hot').removeClass('curr').show();
                $carFilterItems.eq(0).trigger('mouseenter').addClass("z-menu-drop-open").ELazyload();

            }else if( $seriesChoose.text()=='车系' ){        //车系没有选择
                $carFilterItems.eq(1).trigger('mouseenter').addClass("z-menu-drop-open").ELazyload();

            }else if( $yearChoose.text()=='年款' ){          //年款没有选择
                $carFilterItems.eq(2).trigger('mouseenter').addClass("z-menu-drop-open").ELazyload();

            }else if( $modelChoose.text()=='车型' ){         //年款没有选择
                $carFilterItems.eq(3).trigger('mouseenter').addClass("z-menu-drop-open").ELazyload();
            }

            if( $thisEl.find('.z-menu-drop-open').length ){
                return false;
            }
            return true;
        },

        _get: function (url, data, callback) {
            if ($.isFunction(data)) {
                callback = data;
                data = {};
            }
            $.ajax({
                url: url,
                data: data,
                scriptCharset: 'utf-8',
                dataType: "jsonp",
                success: function (response) {
                    (callback || $.noop)(response);
                }
            });
        },
        _setCurrentBrand: function (brandName, brandId) {
            apiParams.brand = brandId;
            delete apiParams.series;
            this.$carBrand.find(".trigger span").text(brandName || "品牌");
        },
        _setCurrentSeries: function (seriesName, seriesId) {
            apiParams.series = seriesId;
            this.$carSeries.find(".trigger span").text(seriesName || "车系");
            if (!seriesName)  {
                this._setCurrentYear();
            }
        },
        _setCurrentYear: function (yearName, yearId) {
            apiParams.seriesYear = yearId;
            currentYear = $.trim(yearName);
            this.$carYear.find(".trigger span").text(yearName || "年款");
            if (! yearName) {
                this._setCurrentModel();
            }
        },
        _setCurrentModel: function (model) {
            this.$carModel.find(".trigger span").text(model || "车型");
            if (! model) {
                this.carModelId = null;
            }
        }
    });

    //Helper Method
    function buildSeriesData (response) {
        var result = {};

        $.each(response.data, function (index, series) {
            (result[series.factory] = result[series.factory] || []).push(series);
        });

        return result;
    }
    function getHotBrands (brands) {
        var tmp = [];
        $.each(brands, function (i, brand) {
            if (brand.hot > 0) {
                tmp.push(brand);
            }
        })

        tmp.sort(function (brandA, brandB) {
            return (brandA.hot - brandB.hot);
        });

        return tmp.slice(0, 20);
    }
    // function buildYearModelData (response) {
    //     yearModelData = {};
    //     $.each(response.data, function (index, model) {
    //         (yearModelData[model.listingYear] = yearModelData[model.listingYear] || []).push({id: model.carModelId, name: model.modelName});
    //     });
    // }

    module.exports = CarButler;
});
