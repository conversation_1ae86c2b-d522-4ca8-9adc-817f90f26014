define('MOD_ROOT/vehicle/vehicle', function(require, exports, module) {
    require('JDF_UI/dialog/1.0.0/dialog');
    require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var G = require('MOD_ROOT/common/core');
    var Login = require('JDF_UNIT/login/1.0.0/login');
    var Tools = require('MOD_ROOT/common/tools/tools');
    var Event = require('MOD_ROOT/common/tools/event').Event
    var ColorSize = require('MOD_ROOT/colorsize/colorsize');
    var addToCartBtn = require('MOD_ROOT/buybtn/buybtn').addToCartBtn;

    var REMINDER_TEMPLATE = '\
        <div class="reminder"> \
            <div class="guide" clstag="shangpin|keycount|product|xuanzechexing"> \
                <span class="icon-car"></span><span class="guide-text">{0}</span> \
            </div> \
            <div class="icon-present" title="选择车型，查看赠品。"></div> \
        </div>';

    var VEHICLES_TEMPLATE = '\
        {if data}\
        <div class="vehicles"> \
            {for item in data} \
            <div class="vehicle" data-id="${item.id}" data-modelId="${item.modelId}"> \
                <img class="vehicle-logo" src="//img30.360buyimg.com/car/${item.logoUrl}" title="${item.brandName}" /> \
                <div class="vehicle-inner"> \
                    <div class="vehicle-name" title="${item.brandName} ${item.seriesName}">${item.brandName} ${item.seriesName}</div> \
                    <div class="vehicle-model" title="${item.seriesYear}款${item.modelName}">${item.seriesYear}款${item.modelName}</div> \
                </div> \
            </div> \
            {/for} \
            <a class="other" href="javascript:void(0)">管理</a> \
        </div>{/if}';

    var VEHICLES_TEMPLATE2 = '\
        {if data}\
        <div class="vehicles"> \
            {for item in data} \
            <div class="vehicle" data-id="${item.id}" data-modelId="${item.model_id}"> \
                <img class="vehicle-logo" src="//img30.360buyimg.com/car/${item.brand_logo}" title="${item.brand_name}" /> \
                <div class="vehicle-inner"> \
                    <div class="vehicle-name" title="${item.brand_name} ${item.series_name}">${item.brand_name} ${item.series_name}</div> \
                    <div class="vehicle-model" title="${item.series_year_name}款${item.model_name}">${item.series_year_name}款${item.model_name}</div> \
                </div> \
            </div> \
            {/for} \
            <a class="other" href="javascript:void(0)">管理</a> \
        </div>{/if}';

        var GIFTS_TEMPLATE = '\
        <div class="J-prom-gift"> \
            <div class="prom-gifts clearfix"> \
                <span class="prom-gift-label"><em class="car_red_bg">赠品</em></span> \
                <div class="prom-gift-list"> \
                    {for item in data} \
                        {if item.hashStore} \
                            <div class="prom-gift-item" data-id="${item.sku}"> \
                                <a target="_blank" href="//item.jd.com/${item.sku}.html" title="${item.skuName}">\
                                    {if item.mainImage} \
                                    <img src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s25x25_${item.mainImage}" width="25" height="25" class="gift-img"/> \
                                    {else} \
                                    <img src="//img30.360buyimg.com/da/jfs/t1264/236/181850154/1105/14bba6c8/5509488cN2093c2a9.png" width="25" height="25" class="gift-img"/>\
                                    {/if} \
                                </a> \
                                <em class="gift-number">× ${item.giftCount}</em> \
                            </div> \
                        {/if} \
                    {/for} \
                    <div class="J-gift-limit gift-limit">（车型赠品）</div> \
                </div> \
            </div> \
        </div>';

    var NOLOGIN_GUIDE_TEXT = '添加爱车';
    var LOGIN_GUIDE_TEXT   = '添加爱车';
    var MATCH_TIP_TEXT     = '该商品可适配爱车，请放心购买';
    var NOMATCH_TIP_TEXT   = '当前页面所有商品与此车型均不适配，请谨慎购买';
    var vehicleModelId = G.serializeURL(location.href).query.modelId;
    var $root = $('#choose-car');
    var $container = $('.dd', $root);
    var currentItemSkuId = +pageConfig.product.skuid;
    var $giftContainer = $('#prom-car-gift');
    var colorSize = (function() {
        var dict = {};
        var colorSize = pageConfig.product.colorSize;
        if (G.is([], colorSize) && colorSize.length) {
            for (var i = 0, length = colorSize.length; i < length; i++) {
                dict[colorSize[i].skuId] = colorSize[i];
            }
        }
        return dict;
    })();

    var salePropertyKeys = (function() {
        var arr = [];
        var $attrs = $('#choose-attrs');
        if ($attrs.length) {
            $attrs.find('[data-type]').each(function () {
                var type = $(this).data('type');
                if (type) {
                    arr.push(type);
                }
            });
        }
        return arr;
    })();

    /**
     * 跳页辅助函数
     * @param {Number} skuId 
     * @param {Number} modelId 
     */
    function jumpTo(skuId, modelId) {
        var obj = {};
        if (G.is(1, skuId)) {
            obj.path = '/' + skuId + '.html';
        }
        if (G.is(1, modelId)) {
            obj.query = {
                modelId: modelId
            };
        }
        location.href = G.modifyURL(location.href, obj);
    }

    /**
     * 定制销售属性匹配时的跳转逻辑，根据modelId的值来确定是否携带modelId参数
     * @param {Number|Stirng} modelId
     */
    function goToPage(modelId) {
        return function(sku) {
            $('.crumb-wrap').attr('id', 'crumb-wrap');
            location.href = G.modifyURL(location.href, {
                path: '/' + sku + '.html',
                query: {
                    modelId: modelId 
                },
                fragment: '#crumb-wrap'
            }, true);
        }
    }

    function renderReminder($mount, text) {
        if ($mount && text) {
            $mount.html(REMINDER_TEMPLATE.format(text));
            $mount.closest('.choose-car').show();
        }
    }

    function renderVehicles($mount, data) {
        if ($mount && data) {
            $mount.html(VEHICLES_TEMPLATE.process({data: data}));
            $mount.closest('.choose-car').show();
            ensureBtnPosition()
            $(window).resize(ensureBtnPosition)
        }
    }

    function renderVehicles2($mount, data) {
        if ($mount && data) {
            $mount.html(VEHICLES_TEMPLATE2.process({data: data}));
            $mount.closest('.choose-car').show();
            ensureBtnPosition()
            $(window).resize(ensureBtnPosition)
        }
    }
    // 确保管理按钮始终在第一行
    function ensureBtnPosition() {
        var vehicleWrapper = $('.vehicles')
        var BtnWidth = 42 + 16 // 管理按钮宽 42， 管理按离最后一个按钮边距 16
        if (vehicleWrapper.height() > 46) { // 多行
            vehicleWrapper.css({ // 为管理按钮预留展示区域
                paddingRight: BtnWidth + 'px'
            })
            var last = findLastInFirstLine($('.vehicle').get()) 
            var lastRight = last.getBoundingClientRect().right
            var wrapperRight = vehicleWrapper.get(0).getBoundingClientRect().right
            
            $('.vehicles .other').css({
                position: 'absolute',
                right: wrapperRight - lastRight - BtnWidth,
                top: '2px'
            })
        } else { // 单行
            vehicleWrapper.css({ // 为管理按钮预留展示区域
                paddingRight: 0
            })
            $('.vehicles .other').css({
                position: 'static'
            })
        }
    }
    // 找到第一行的最后一个
    function findLastInFirstLine(allVehicle) {
        var top = allVehicle[0].getBoundingClientRect().top
        for (var index = 1; index < allVehicle.length; index++) {
            var element = allVehicle[index];
            if (element.getBoundingClientRect().top !== top) {
                return allVehicle[index - 1]
            }
            
        }
    }
    /**
     * 渲染赠品信息
     * @param {Array} data
     */
    function renderGifts($mount, data) {
        if ($mount.length) {
            if (G.is([], data) && data.length) {
                var __html = GIFTS_TEMPLATE.process({data: data});
                // 赠品可能无货，模版编译完后，反向查看容器是否有内容
                if ($(__html).find('.prom-gift-item').length <= 0) {
                    __html = '';
                }
            } else {
                var __html = '';
            }
            $mount.html(__html);
            
            var $summaryProm = $('#summary-promotion');
            if (__html) {
                $summaryProm.show();
            } else {
                if (!pageConfig.hasPromotion) {
                    $summaryProm.hide();
                }
            }
        }
    }

    function addTips($mount, text) {
        if ($mount.length && $("#J_vTips").length == 0) {
            $mount.before($('<div id="J_vTips">').css({
                color: '#e4393c',
                height: '25px',
                lineHeight: '25px',
                paddingLeft: '5px',
                marginBottom: '5px',
                backgroundColor: '#fdf0f0',
            }).html(text || NOMATCH_TIP_TEXT));
        }
    }

    function removeTips($mount) {
        if ($mount.length) {
            $('#J_vTips', $mount).remove();
        }
    }

    var gifts = [];

    /**
     * 获取当前主sku的赠品信息
     * @param {Array} skuList `getSkuList`方法调用接口的返回值
     */
    function getGiftInfo(skuList) {
        var arr = [];
        if (G.is([], skuList) && skuList.length) {
            for (var i = 0, length = skuList.length; i < length; i+=1) {
                var item = skuList[i];
                if (item.skuId == currentItemSkuId) {
                    if (G.is([], item.gifts) && item.gifts.length) {
                        arr = arr.concat(item.gifts);
                    }
                    break;
                }
            }
        }
        gifts = arr;
        setCartBtn();
        setCarGiftState();
        return arr;
    }

    function getGiftSkus() {
        if (G.is([], gifts) && gifts.length) {
            var arr = [];
            for (var i = 0, length = gifts.length; i < length; i++) {
                var item = gifts[i];
                if (item.hashStore) {
                    arr.push(item.sku);
                }
            }
            return arr.join(',');
        } else {
            return '';
        }
    }

    // 设置购物车链接
    function setCartBtn() {
        var $btn = addToCartBtn.$el;
        var href = $btn.attr('href');
        var gifts = getGiftSkus();
        if (href !== '#none') {
            if (!/^https?/.test(href)) {
                href = location.protocol + href;
            }
            try {
                href = G.modifyURL(href, {
                    query: {
                        gids: gifts
                    }
                }, true).replace(/^(?:https?:)?(?:\/\/)?/, '//');
                addToCartBtn.enabled(href);
            } catch (error) {
                console && console.log(error);
            }
        } 
    }

    function setCarGiftState() {
        var gifts = getGiftSkus();
        if (gifts) {
            pageConfig.hasCarGift = true;
            try {
                pageConfig.product.baiTiaoFenQi.hide();
            } catch (error) {
                console && console.log(error);
            }
        } else {
            pageConfig.hasCarGift = false;
            try {
                pageConfig.product.baiTiaoFenQi.showItem();
            } catch (error) {
                console && console.log(error);
            }
        }
    }

    function getVehicleInfo(params, onSuccess, onError) {
        var params = $.extend({}, params);

        return $.ajax({
            url: '//cargw.jd.com/mClient/model/queryById',
            data: params,
            dataType: 'jsonp',
            success: function() {
                if (typeof onSuccess === 'function') {
                    onSuccess.apply(null, arguments);
                }
            },
            error: function() {
                if (typeof onError === 'function') {
                    onError.apply(null, arguments);
                }
            }
        });
    }

    function getUserVehicleInfo(params, onSuccess, onError) {
        var params = $.extend({
            appid: 'carHousekeeper',
            functionId: 'getBindCarUserModel',
            body: '{"skuId":' + pageConfig.product.skuid + ',"source":"M商品详情页"}',
            jsonp: 'autoskubypin'
        }, params);

        return $.ajax({
            url: '//api.m.jd.com/api',
            data: params,
            dataType: 'jsonp',
            jsonpCallback: 'autoskubypin',
            success: function() {
                if (typeof onSuccess === 'function') {
                    onSuccess.apply(null, arguments);
                }
            },
            error: function() {
                if (typeof onError === 'function') {
                    onError.apply(null, arguments);
                }
            }
        });
    }

    function getSkuList(modelId) {
        var location = Tools.getAreaId().areaIds;
        var params = {
            modelId: modelId,
            skuId: pageConfig.product.skuid,
            provinceId: location[0],
            cityId: location[1],
            areaId: location[2],
            townId: location[3],
            appid: 'item-v3',
            functionId: "pc_auto_skubymodelidarea_v2"
        };
        var body = JSON.stringify(params);

        Tools.getJsTokenSign({
            body: body,
            appid: "item-v3",
            functionid: "pc_auto_skubymodelidarea_v2",
            render: function (colorParm) {
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }
                $.ajax({
                    url: host + '/auto/skubymodelidarea_v2',
                    data: Object.assign(colorParm, params),
                    scriptCharset: 'gbk',
                    dataType: "jsonp",
                    success: function (data) {
                        if (G.is([], data) && data.length) {
                            renderGifts($giftContainer, getGiftInfo(data));
                            var skus = [];
                            $.each(data, function(index, value){
                                if (value) {
                                    skus.push(+value.skuId);
                                }
                            });
                            blockSaleProperties(skus, modelId);
                            addTips($('.vehicles', $container), MATCH_TIP_TEXT);
                            ColorSize.overrideGoToPageMethod(goToPage(modelId));
                        } else {
                            if (isBlock) {
                                recoverSaleProperties();
                            }
                            addTips($('.vehicles', $container));
                            ColorSize.overrideGoToPageMethod(goToPage(''));
                            renderGifts($giftContainer, getGiftInfo([]));
                        }
                    },
                    error:function(err){
                        console && console.log(err);
                    }
                });
            }
        });
    }

    function toggleCurrentVehicleModel($items, modelId) {
        if (!($items.length && modelId)) {
            return;
        }
        var SELECTED_CLASS = 'selected';
        var $currentItem = $items.filter('[data-modelid="'+ modelId + '"]');
        
        if ($currentItem.length) {
            removeTips($container);
            if ($currentItem.hasClass(SELECTED_CLASS)) {
                $currentItem.removeClass(SELECTED_CLASS);
                recoverSaleProperties();
                ColorSize.overrideGoToPageMethod(goToPage(''));
                renderGifts($giftContainer, getGiftInfo([]));
            } else {
                $items.removeClass(SELECTED_CLASS); 
                $currentItem.addClass(SELECTED_CLASS);
                getSkuList(modelId)
            }
        }
    }

    /**
     * 获取sku的销售属性信息
     * @param {Array} skus 
     * @returns {Object}
     */
    function getSkuProperties(skus) {
        if (!(G.is([], skus) && skus.length)) {
            return;
        }
        // 构建返回值的数据结构
        var dict = {};
        $.each(salePropertyKeys, function(index, value) {
            dict[value] = [];
        });
        $.each(skus, function(index, value) {
            if (value in colorSize) {
                $.each(colorSize[value], function(key, value){
                    if (key in dict) {
                        if ($.inArray(value, dict[key]) == -1) {
                            dict[key].push(value);
                        }
                    }
                });
            }
        });
        return dict;
    }

    function filterColorSize(skus) {
        if (G.is([], skus) && skus.length) {
            var arr = [];
            $.each(skus, function(index, value){
                if (value in colorSize) {
                    arr.push(colorSize[value]);
                }
            });
            return arr;
        } else {
            return [];
        }
    }

    var isBlock = false;

    /**
     * selectCurrentSkuProperties
     * @param {Number} skuId
     */
    function selectCurrentSkuProperties(skuId) {
        var $attrs = $('#choose-attrs');
        var properties = getSkuProperties([skuId]);

        if (!($attrs.length && properties)) {
            return;
        }
        $attrs.find('.item').removeClass('selected');
        $.each(properties, function(key, attrs){
            $attrs.find('[data-type="' + key + '"]')
            .find('.item[data-value="' + attrs[0] + '"]')
            .addClass('selected');
        });
    }

    /**
     * 根据skus屏蔽销售属性
     * @param {Array} skus 
     */
    function blockSaleProperties(skus, modelId) {
        var $attrs = $('#choose-attrs');
        var properties = getSkuProperties(skus);

        if (!($attrs.length && properties)) {
            return;
        }
        // 屏蔽销售属性
        $.each(properties, function (key, attrs) {
            $attrs.find('[data-type="'+ key + '"] .item').each(function() {
                var $this = $(this);
                var value = $this.data('value');
                if ($.inArray(value, attrs) !== -1) {
                    $this.show();
                } else {
                    $this.hide();
                }
            });
        });
        // 移除选中状态
        $attrs.find('.item').removeClass('selected disabled').removeAttr('title');

        if ($.inArray(currentItemSkuId, skus) !== -1) {
            selectCurrentSkuProperties(currentItemSkuId);
        } else {
            jumpTo(skus[0], modelId);
        }
        // 重新初始化
        var data = filterColorSize(skus);
        if (data.length > 0) {
            ColorSize.setData(data);
        }
        ColorSize.initAttrSelection();
        isBlock = true;
    }

    /**
     * 恢复页面加初始的销售属性
     */
    function recoverSaleProperties() {
        var $attrs = $('#choose-attrs');
        var properties = getSkuProperties([currentItemSkuId]);

        if (!($attrs.length && properties)) {
            return;
        }
        // 显示当前单品sku的销售属性
        $attrs.find('.item').removeClass('selected disabled');
        $.each(properties, function(key, attrs){
            $attrs.find('[data-type="' + key + '"]')
            .find('.item[data-value="' + attrs[0] + '"]')
            .addClass('selected');
        });
        // 显示全部的销售属性
        $attrs.find('.item').show();
        // 重新初始化
        var data = pageConfig.product.colorSize;
        if (data.length > 0) {
            ColorSize.setData(data);
        }
        ColorSize.initAttrSelection();
        isBlock = false;
    }

    /**
     * 设置用户维护的车型信息
     */
    function setUserVehicleInfo() {
        getUserVehicleInfo({})
        .done(function(data) {
            if (data && data.model && data.model.bindCar) {
                var userModels = data.model.carUserModels;
                if (G.is([], userModels) && userModels.length) {
                    renderVehicles($container, userModels);
                    if (vehicleModelId) {
                        toggleCurrentVehicleModel($('.vehicle', $container), vehicleModelId);
                    }
                } else {
                    renderReminder($container, LOGIN_GUIDE_TEXT);
                }
            }
        })
        .fail(function(error) {
            console && console.log(error);
        });
    }
    /**
     * 根据车型id设置车型信息
     * @param {*} modelId 
     */
    function setVehicleInfoByModelId(modelId) {
        if (!modelId) { return; }
        getVehicleInfo({
            modelId: modelId
        })
        .done(function(res) {
            if (res && res.code == 0) {
                renderVehicles2($container, [res.data]);
                toggleCurrentVehicleModel($('.vehicle', $container), modelId);
            } else {
                renderReminder($container, NOLOGIN_GUIDE_TEXT);
            }
        })
        .fail(function(error) {
            renderReminder($container, NOLOGIN_GUIDE_TEXT);
            console && console.log(error);
        });
    }

    /**
     * 车管家车型管理服务
     */
    function openDialog() {
        $('body').dialog({
            type: 'iframe',
            title: '管理爱车',
            width: 804,
            source: '//iche.jd.com/itemBox.html',
            onBeforeClose: function() {
                setUserVehicleInfo();
                if (isBlock) {
                    recoverSaleProperties();
                }
            }
        });
    }

     // "赠品"提示数据捕捉（页面首次载入）
     Event.addListener('onPromReady', function setLabel(data){
        var carGift = data.prom && data.prom.needCarGift;
        $root.data('carGift', carGift);
    });

    function bindEvent(isLogin) {
        // 车型点击事件
        $root.delegate('.vehicle', 'click', function() {
            toggleCurrentVehicleModel($('.vehicle', $root), $(this).data('modelid'));
        });

        // “其他车型”点击事件
        $root.delegate('.other', 'click', function() {
            if (isLogin) {
                openDialog();
            } else {
                Login({
                    modal: true,
                    complete: function(data) {
                        if (
                            data !== null 
                            && data.Identity 
                            && data.Identity.IsAuthenticated
                        ) {
                            openDialog();
                            isLogin = true;
                        }
                    }
                });
            }
        });

        // ”引导语“ 点击事件
        $root.delegate('.guide', 'click', function() {
            if (isLogin) {
                openDialog();
            } else {
                Login({
                    modal: true,
                    complete: function(data) {
                        if (
                            data !== null 
                            && data.Identity 
                            && data.Identity.IsAuthenticated
                        ) {
                            setUserVehicleInfo();
                            isLogin = true;
                        }
                    }
                });
            }
        });

        // 地址联动
        Event.addListener('onAreaChange', function (data) {
            var $vehicle = $('.vehicle.selected', $root);
            if ($vehicle.length) {
                $vehicle.removeClass('selected').trigger('click');
            } else {
                recoverSaleProperties();
                ColorSize.overrideGoToPageMethod(goToPage(''));
            }
        });

        // "赠品"提示
        if ($root.data('carGift')) {
            $('.icon-present', $root).css('display', 'inline-block');
        } else {
            $('.icon-present', $root).hide();
        }

        Event.addListener('onPromReady', function setLabel(data){
            var carGift = data.prom && data.prom.needCarGift;
            if (carGift) {
                $('.icon-present', $root).css('display', 'inline-block');
            } else {
                $('.icon-present', $root).hide();
            }
        });
        $('.icon-present', $container).ETooltips({pos: 'bottom', zIndex: 10});
    }

    function init(cfg) {
        if ($root.length == 0) { return; } 
        getUserVehicleInfo({})
        .done(function(data) {
            // 是否显示车管家入口
            if (data && data.model && data.model.bindCar) {
                var userModels = data.model.carUserModels;
                Login.isLogin(function (isLogin) {
                    if (isLogin) {
                        if (G.is([], userModels) && userModels.length) {
                            renderVehicles($container, userModels);
                            if (vehicleModelId) {
                                toggleCurrentVehicleModel($('.vehicle', $container), vehicleModelId);
                            }
                        } else {
                            renderReminder($container, LOGIN_GUIDE_TEXT);
                        }
                    } else {
                        if (vehicleModelId) {
                            setVehicleInfoByModelId(vehicleModelId);
                        } else {
                            renderReminder($container, NOLOGIN_GUIDE_TEXT);
                        }
                    }
                    bindEvent(isLogin);
                });
            }
        })
        .fail(function(error) {
            console && console.log(error);
        });
    }

    module.exports.__id = 'vehicle';
    module.exports.getGiftSkus = getGiftSkus;
    module.exports.setCartBtn = setCartBtn;
    module.exports.init = init;
});