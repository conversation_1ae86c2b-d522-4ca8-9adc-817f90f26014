@charset "utf-8";

@import "__sprite";
// Variables & Mixin ..
@import "lib";
// elements
@import "button";
@import "product";
// components
@import "../ETab/ETab";
@import "../EDropdown/EDropdown";
@import "../ETooltips/ETooltips";
@import "../ELazyload/ELazyload";
@import "../sidereco/sidereco";

.clb {
    clear: both;
}
.invisible{ visibility:hidden; }
s {
    text-decoration: none;
}
blockquote, q {
    quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}
.lh {
    overflow: hidden;
    zoom: 1;
}
.lh li {
    float: left;
    *display: inline;
}
.arr {
    font-family: $font-st;
}
.hl_red {
    color: $colorPriceRed;
}
.hl_blue {
    color: $colorLinkBlue;
}
.loading-style1 {
    width: 126px;
    margin: 0 auto;
    text-align: center;
    color: #999;
     b {
        display: block;
        width: 43px;
        height: 24px;
        margin: 0 auto 5px;
        background: url(//misc.360buyimg.com/lib/skin/e/i/loading-jd.gif) no-repeat 50% 50%;
    }
}

// layout
.aside {
    float: left;
    display: inline;
    width: 210px;
    margin-bottom: 20px;
}
.detail {
    float: right;
    display: inline;
    width: 770px;
    min-height: 220px;
    //overflow:hidden;
}

.root61 {
    .detail {
        width: 990px;
    }
}
.building{
    .detail{
        width: 100%;
    }
}
@include mContent();
@include mAside();

.tag-list {
    li {
        @include inline-block;
        padding: 2px 4px;
        background-color: #eef1ff;
        margin: 0 10px 10px 0;
    }
}

//itemInfo
.itemInfo-wrap {
    ins {
        text-decoration: none;
    }

    .s-arrow {
        font-family: simsun;
        text-decoration: none;
    }

    .summary {
        position: relative;
        padding-bottom: 5px;
        _zoom: 1;
        .competition{
            border-bottom: 1px dotted #dfdfdf;
            padding-bottom: 5px;
            margin-bottom: 18px;
            .sprite-map{
                display: inline-block;
                @include sprite-map;
                margin-left: 5px;
                vertical-align: -2px;
                &:hover{
                    @include sprite-map-hover;
                }
            }
        }
    }

    .summary-first {
        *z-index: 5;
    }

    .dt {
        float: left;
        /*width: 50px;*/
        padding-left: 10px;
        font-family: simsun;
        color: #999;
        &.size-type {
            line-height:16px;
            margin-right:5px;
        }
    }

    .dd {
        margin-left: 70px;
    }
    .blue {
        color:#005aa0;
    }
}

.li {
    margin-bottom: 3px;
    line-height: 32px;

    @include clearfix;
    .hl_blue {
        cursor: pointer;
    }

    .item {
        float: left;
        color: #666;
        background-color: #FFF;
        margin-right: 7px;
        margin-bottom: 4px;
        position: relative;

        a {
            border: 1px solid #ccc;
            padding: 0 13px;
            display: block;
            white-space: nowrap;
            text-decoration: none;
            *display: inline;
            *zoom: 1;
        }

        &:hover, &.hover, &.selected {
            a {
                border: 1px solid #e3393c;
                color: #666;
            }
        }

        &.disabled {
            a {
                border: 1px dashed #ccc;
                //cursor: not-allowed;
                color: #999;
            }
        }
    }
}
.p-choose-wrap {
    .item-hl-bg {
        padding-top: 4px;
        background-color: #ffe6e6;
    }
    .open-plusgift {
        line-height: 32px;
        padding-bottom: 5px;
        .icon-plus {
            width:59px;
            height: 16px;
            display: inline-block;
            background: url(./i/icon-plusx1.png) center center no-repeat;
            vertical-align: top;
            margin: 8px 5px 0 0;
        }
        .a-topluspage {
            color: $colorLinkBlue;
        }
    }
}

/* 兼容新头热词 */
#hotwords {
    float: left;
    width: 462px;
    height: 20px;
    line-height: 20px;
    overflow: hidden;

    strong {
        display: none;
    }
    a {
        margin-right: 10px
    }
}
.root61 #hotwords {
    width: 518px
}
/*导航角标*/
#nav-2014{
    .new-tab{
        position: relative;
        .icon-new{
             position: absolute;
             min-width: 24px;
             height: 15px;
             line-height: 15px;
             text-align: center;
             padding: 0 3px;
             display: block;
             right: -10px;
             top:-3px;
             font-size: 12px;
             font-family: simsun;
             color: #fff;
             background: #529f57;
            span{
                width: 0;
                height: 0;
                border-top: 3px solid #529f57;
                border-right: 3px solid transparent;
                position: absolute;
                left: 0;
                bottom: -3px;
            }
         }
    }
}
/*二手*/
.ershou{
    .li{
        .item {

            &:hover, &.hover, &.selected {
                a {
                    border: 1px solid $baseColorErshou;
                    color: #666;
                }
             }

            &.disabled {
                a {
                    border: 1px dashed #ccc;
                    //cursor: not-allowed;
                    color: #999;
                }
            }
        }
    }
}

/*二手头尾*/
.ershou{
    #search-2014 .text{
        border-color: $baseColorErshou;
    }
    #search-2014 .button{
        background: $baseColorErshou;
    }
    #nav-2014{
        border-color: $baseColorErshou;
    }
    #categorys-2014{
        background: $baseColorErshou;
    }
    #categorys-2014 .dt a{
        background: $baseColorErshou;
    }
    #categorys-2014 .dd{
        background: $baseColorErshou;
        a:hover{
            color: $baseColorErshou;
        }
    }
    #categorys-2014 .dorpdown-layer{
        border-color: $baseColorErshou;
    }
    #categorys-2014 .dd-inner .item{
        border-color: $baseColorErshou;
    }
    #categorys-2014 .dd-inner .hover a{
        color: $baseColorErshou;
    }
    #service-2014 .slogen .item{
        display: none;
    }
    #service-2014 .slogen{
        background: #F5F5F5 url(i/service-bg.png) no-repeat center 25px;
    }
    #logo-2014 {
        height: 40px;
        padding: 30px 0;
    }
    #logo-2014 .logo{
        width: 82px;
        height: 36px;
        // background: url(//img30.360buyimg.com/devfe/jfs/t15118/224/857107058/3296/b5edc83/5a3b237cN12a3e7fb.png) 50% 50% no-repeat;
        background: url(i/paipai-logo.png) 50% 50% no-repeat;
        background-size: 82px 36px;
    }
    #settleup-content .smb a{
        background: $baseColorErshou;
    }
    #settleup-2014 .ci-count{
        background-color: $baseColorErshou;
    }
    #settleup-2014 .ci-left {
        background: url(i/cart.png) no-repeat;
        top: 9px;
        left: 18px;
        width: 18px;
        height: 16px;
        display: block;
        position: absolute;
        overflow: hidden;
    }
    #settleup-2014 .cw-icon a:hover {
        color: $baseColorErshou;
    }
}
.root61{
    .ershou{
        #service-2014 .slogen{
            background: #F5F5F5 url(i/service-bg-root61.png) no-repeat center 25px;
        }
    }
}

.car-filter {
    display: none;
}

.m-itemover-title {
    height: 38px;
    line-height: 38px;
    border: 1px solid #ddd;
    background: #f5f5f5;
    margin-bottom: 15px;
    h3 {
        padding-left: 10px;
    }
}
/*医药*/
.y-beian {
    margin-left: 10px;
    width: 147px;
}
.y-copyright {
    width: 337px;
}
.y-zige {
    width: 270px;
}
.y-beian, .y-copyright, .y-zige {
    float: left;
    margin-right: 20px;
    line-height: 1.8em;
}
.root61 {
    .y-beian {
        margin: 0 80px 0 80px;
    }
    .y-copyright {
        margin-right: 80px;
    }
}

/* 店铺星级 */
.contact,
.pop-score-summary {
    .star {
        padding-top:3px;
        .star-bg {
            width:94px;
            height:16px;
            border-radius: 8px;
            background:#e6e6e6;
            overflow:hidden;
        }
        .star-gray {
            position:relative;
            width: 80px;
            height:14px;
            margin: 1px 0 0 7px;
            background:url(./i/star-gray.png) left center repeat-x;
        }
        .star-light {
            position: absolute;
            top:0;
            left:0;
            height:14px;
            background:url(./i/star-light.png) left center repeat-x;
        }
    }
    // 左侧店铺的星级样式
    .dianpu-star-box {
        height:24px;
        line-height:24px;
        color:#666;
        margin-bottom: 8px;
    }
    .dianpu-star-tit {
        width:56px;
        font-size:12px;
        float:left;
    }
    .dianpu-star-box .star {
        margin-top: 2px;
        float:left;
    }

    // 颜色
    .red {
        color: #e2231a!important;
    }
    .orange {
        color: #ff6602!important;
    }
    .green {
        color: #33bb44!important;
    }
}
