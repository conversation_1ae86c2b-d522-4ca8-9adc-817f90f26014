define('MOD_ROOT/itemover/itemover', function(require, exports, module) {
    var Recommend  = require('MOD_ROOT/common/tools/recommend');
    var switchable = require('JDF_UI/switchable/1.0.0/switchable');

    var template_itemover = '\
        <div class="mt">\
            <h3 class="fl">相似商品推荐</h3>\
            <div class="page-num1 fr">\
                <span class="curr">1</span>/<span class="total">${data.length}</span>\
            </div>\
        </div>\
        <div class="mc">\
            <a href="javascript:;" class="ui-switchable-prev disabled"><i class="sprite-arrow-prev"></i></a>\
            <div class="lists">\
                <div class="list ui-switchable-panel-main">\
                    {for list in data}\
                    {var index = Number(list_index)}\
                    <ul class="plist ui-switchable-panel">\
                        {for item in list.tabs}\
                        {var index_tab = Number(item_index)}\
                        <li data-push="${pageConfig[skuHooks].push(item.sku)}" data-clk="${item.clk}" class="fore${index_tab+1}">\
                            <div class="p-img">\
                                <a target="_blank" href="//item.jd.com/${item.sku}.html">\
                                    <img height="150" width="150" alt="${item.t}" src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s150x150_${item.img}">\
                                </a>\
                            </div>\
                            <div class="p-name">\
                                <a target="_blank" href="//item.jd.com/${item.sku}.html" title="${item.t}">${item.t}</a>\
                            </div>\
                            <div class="p-price"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
                        </li>\
                        {/for}\
                    </ul>\
                    {/for}\
                </div>\
            </div>\
            <a href="javascript:;" class="ui-switchable-next disabled"><i class="sprite-arrow-next"></i></a>\
        </div>';

    var ItemOver = {
        init: function(cfg) {
            var scrollVisible_noitem = pageConfig.wideVersion&&pageConfig.compatible ? 7 : 5;
            if(!cfg.twoColumn) {
                scrollVisible_noitem = 5;
            }
            new Recommend({
                $el: $('#itemover'),
                skuHooks: 'SKUS_stockDisable',
                template: template_itemover,
                reBuildJSON: scrollVisible_noitem-1,
                param: {
                    p: 902011,
                    sku: cfg.skuid,
                    ck: 'pin',
                    lim: 12
                },
                callback: function(hasData, r) {
                    if(r.success) {
                        var _this = this;
                        _this.$el.switchable({
                            hasPage: true,
                            type: "slider",
                            callback: function (currPage) {
                                _this.$el.find(".page-num1 .curr").html(currPage + 1);
                            }
                        })
                        _this.$el.show();
                    }
                }
            });
        }
    }

    function init(cfg) {
        ItemOver.init(cfg);
    }

    module.exports.__id = 'itemover';
    module.exports.init = init;
});
