# 通用版单品页前端源代码

## 整体架构逻辑

前端代码整体基于 seajs+jQuery，打包工具 [wooo](https://www.npmjs.com/package/wooo)，公共组件（业务+UI）一部分使用了 [JDF1.0](http://jdf.jd.com/demo.html)，一部分按项目需求重写（app/components/E*）

* [单品页前端技术分享](https://docs.google.com/presentation/d/1jHLfAxi8qy6OX2kbdakvrAxGdFQkhz3hPIBL6BsFSnQ/edit?usp=sharing)
* [京东单品页前端开发那些不得不说的事儿](https://keelii.github.io/2016/07/31/something-have-to-say-with-JD-item/)

### 主要异步接口汇总

![单品页系统-异步接口](http://img20.360buyimg.com/devfe/jfs/t5680/193/8990806608/56603/d648fb5d/59817204N2d5f198a.png)

## 开发环境

* [Node.js >= 4.0](https://nodejs.org/en/) 推荐版本：v16.15.0 npm: 8.5.5
* [wooo 集成开发工具](https://www.npmjs.com/package/wooo) dartsass安装 可能会因为版本问题需要语法更新

## 使用 wooo 快速入门

更多信息 [keelii/wo](https://github.com/keelii/wo)，相关 [wiki](https://github.com/keelii/wo/wiki)

```bash
# 安装 wooo 打包工具
$ npm install wooo -g --registry=https://registry.npm.taobao.org --sass-binary-site=https://npm.taobao.org/mirrors/node-sass/
$ wo b + 文件路径    # 打包文件，wo b打包全部
$ wo deploy     # 上传静态文件到测试服务器（config.deploy）
$ wo start      # 启动本地开发服务器，自动编译 SCSS、template
```
```bash
# 上传OSS
$ 修改config.js：
$ -> version: '1.0.153'  # 根据需求版本迭代
$ -> production: '//storage.jd.com/retail-mall/item2025/pc/'
$ 上传最新版本命令：
$ node /Users/<USER>/pc.itemC/scripts/uploadOss.js 

# 上传static预发
$ 修改config.js：
$ -> version: '1.0.153'  # 根据需求版本迭代
$ -> production: '//static.360buyimg.com/item/',
$ 上传预发(************** static.360buyimg.com)命令：
$ node /Users/<USER>/pc.itemC/upload.js

# 测试页面访问
$ //item.jd.com/SKUID.html?version=版本（1.0.154）
```
## 相关干系人

* JDF - 巫耀恒（wuyaoheng）
* 单品前端 - 周琪力（bjzhouqili）、金锴锋（jinkaifeng5）、朱志荣（zhuzhirong）
* 单品后端 - 申浩亮（shenhaoliang1）、睢亚坤（suiyakun6）
