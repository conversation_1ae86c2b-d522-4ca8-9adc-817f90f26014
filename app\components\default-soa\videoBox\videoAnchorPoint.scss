/*节点容器*/
.st-video-dotCon{
    position: absolute;
    width: 100%;
    height: 100%;
    /*display: none;*/
}

/*tip容器*/
.st-video-dotTipWrap {
    position: absolute;
    width: 100%;
    height: 24px;
    top: -28px;
    left: 0;
    text-align: center;
    /*display: none;*/
}
.st-video-dotTipCon {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.6);
    /*color: #a9a9a8;*/
    height: 100%;
    border-radius: 14px;
    padding: 0 5px;
    cursor: pointer;
}
.st-video-dotTipCon .st-video-dottip {
    /*width: 50px;*/
    display: inline-block;
    min-height: 24px;
    padding: 0 6.5px;
    line-height: 24px;
    overflow-y: hidden;
    letter-spacing: 1px;
    color: #a9a9a8;
    z-index: 1;
    font-family: "Microsoft YaHei";
}
.st-video-dottip:before {
    content: '';
    width: 0;
    height: 0;
    border: solid 5px transparent;
    border-left: solid 5px #a9a9a8;
    border-right-width: 0;
    display: inline-block;
    margin-right: 4px;
}
.st-video-dottip.st-video-dottip-active {
    color: #cc181e;
}
.st-video-dottip.st-video-dottip-active:before {
    border-left: solid 5px #cc181e;
}
/*节点*/
.st-video-dot{
    width: 6px;
    height: 100%;
    background-color: #ececec;
    position: absolute;
    overflow: visible;
    border-radius: 50%;
    z-index: 1;
}
.st-video-dot:hover{
    cursor: pointer;
}

/*

.video-js {
    outline: none;
}
.vjs-progress-control:hover .vjs-play-progress:after, .vjs-progress-control:hover .vjs-mouse-display:after{

}
.vjs-progress-holder:hover .vjs-play-progress:after, .vjs-progress-holder:hover .vjs-mouse-display:after{
    display: block;
}
.vjs-paused .st-video-dotTipWrap, .vjs-paused .st-video-dotCon{
    display: block;
}
.vjs-user-active .st-video-dotTipWrap, .vjs-user-active .st-video-dotCon {
    display: block;
}
*/
.vjs-user-active .st-video-dotTipWrap, .vjs-user-active .st-video-dotCon {
    display: block;
}
.vjs-user-inactive .st-video-dotTipWrap, .vjs-user-inactive .st-video-dotCon {
    display: none;
}
.vjs-paused .st-video-dotTipWrap, .vjs-paused .st-video-dotCon{
    display: block;
}
.st-video-dottip:hover {
    color: #fff;
    text-shadow: 0 0 1em #fff, 0 0 1em #fff, 0 0 1em #fff;
    text-shadow: 0 0 1em #fff, 0 0 1em #fff, 0 0 1em #fff;
    -moz-text-shadow: 0 0 1em #fff, 0 0 1em #fff, 0 0 1em #fff;
    -o-text-shadow: 0 0 1em #fff, 0 0 1em #fff, 0 0 1em #fff;
    -ms-text-shadow: 0 0 1em #fff, 0 0 1em #fff, 0 0 1em #fff;
}
.st-video-dottip.st-video-dottip-active:hover {
    color: #cc181e;
    text-shadow: 0 0 1em #cc181e, 0 0 1em #cc181e, 0 0 1em #cc181e;
    -ms-text-shadow: 0 0 1em #cc181e, 0 0 1em #cc181e, 0 0 1em #cc181e;
    -moz-text-shadow: 0 0 1em #cc181e, 0 0 1em #cc181e, 0 0 1em #cc181e;
    -o-text-shadow: 0 0 1em #cc181e, 0 0 1em #cc181e, 0 0 1em #cc181e;
}
