@import "../common/lib";
/**
 * EDropdown 详情通用 dropdown 样式
 */
$EDropdownHeadHeight: 23px;
$EDropdownArrowHeight: 7px;
.EDropdown {
    position: relative;
    .head {
        //width: $EDropdownWidth;
        height: $EDropdownHeadHeight;
        line-height: $EDropdownHeadHeight;
        float:left;
        position: relative;
        z-index: 1;
        padding: 1px 1px 0;
        cursor: pointer;

        .text {
            margin-left: 5px;
        }
    }
    .content {
        position:absolute;
        z-index: 0;
        background-color: #fff;
        //width: $EDropdownWidth;
        top: $EDropdownHeadHeight;
        left: 0;
    }
    .arrow {
        @include inline-block;
        vertical-align: middle;
        width: 13px;
        height: $EDropdownArrowHeight;
        margin: 0 10px 0 5px;
        _font-size:0;
        //position: absolute;
        //right: 10px;
        //top: $EDropdownHeadHeight / 2 - $EDropdownArrowHeight;
        background-repeat: no-repeat;
        background: url(i/arr-close.png) no-repeat 0 0;

        transition: .2s ease;
        -ms-transition: .2s ease;
        -moz-transition: .2s ease;
        -webkit-transition: .2s ease;
        -o-transition: .2s ease;

    }
    .arr-open {
        background: url(i/arr-open.png) no-repeat 0 0\9;
    }

    .close {
        position: absolute;
        right: 15px;
        top: 10px;
        @include icons(12px, 12px);
        background:url(i/close.png);
    }
    .border {
        .head {
            border: 1px solid #ccc;
            border-bottom: none;
            padding: 0;
        }
        .content {
            border: 1px solid #ccc;
        }
    }
}

.hover {
    .content {
        display: block;
    }
    .head {
        background-color: #fff;
    }
    .arrow{
        transform:rotate(180deg);
        -ms-transform:rotate(180deg);
        -moz-transform:rotate(180deg);
        -webkit-transform:rotate(180deg);
        -o-transform:rotate(180deg);
    }
}

.root61 {
    .EDropdown .content {
        right: auto;
        left: 0;
    }
}
