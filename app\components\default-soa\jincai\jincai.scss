@import '../common/lib';

.choose-jincai {
    .dt{
        height: 32px;
        line-height: 32px;
    }
    .item {
        position: relative;
        width: 136px;
        margin-bottom: 22px;
        a {
            text-align: left;
            font-size: 14px;
            padding: 8px 12px;
            line-height: 23px;
            div:first-child {
                font-size: 16px;
            }
        }
    }
    .item.selected div{
        color: #FF5C67;
    }
    .item.selected div:first-child {
        color: #f30213;
        font-weight: 600;
    }
    .item1{
        a{
            color: #1a1a1a;
            font-size: 15px;
        }
        a:hover {
            color: #FF5C67;
        }
    }
    .hover .baitiao-tips {
        display: block;
    }
    .icon {
        @include icons(16px, 16px);
        margin-top: 8px;
        margin-right: 5px;
    }
    .prom {
        background-image: url(i/hui.png);
    }
    .question {
        background-image: url(i/question.png);
    }
}

.bt-info-tips {
    float: left;
    height: 39px;
    display: none;
}
