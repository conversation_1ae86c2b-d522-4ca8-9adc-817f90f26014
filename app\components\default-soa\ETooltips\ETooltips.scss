/**
 * ETooltips
 */
.ETooltips {
    position: absolute;
    display: none;

    .content {
        padding: 10px;
        background: #fff;
        border: 1px solid #cecbce;
        color: #666;
        -moz-box-shadow: 0 0 2px 2px #eee;
        -webkit-box-shadow: 0 0 2px 2px #eee;
        box-shadow: 0 0 2px 2px #eee;
    }

    .arrow {
        position: absolute;
        background-image: url(data:image/gif;base64,R0lGODlhCwALAJECAM7Lzv///////wAAACH/C1hNUCBEYXRhWE1QPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS4zLWMwMTEgNjYuMTQ1NjYxLCAyMDEyLzAyLzA2LTE0OjU2OjI3ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1wTU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOkFERDZFOTU0OTdEMTExRTU5OUEwOUI4MTEyQUM5QkQxIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjE2OEE0MkRBOTdENDExRTVCM0Y0RUU5QTQ1OUJEODA4IiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjE2OEE0MkQ5OTdENDExRTVCM0Y0RUU5QTQ1OUJEODA4IiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzYgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MEMyNERBMDhEMzk3RTUxMUFFRTBCREQ4QzU2QjU2OTciIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QURENkU5NTQ5N0QxMTFFNTk5QTA5QjgxMTJBQzlCRDEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4B//79/Pv6+fj39vX08/Lx8O/u7ezr6uno5+bl5OPi4eDf3t3c29rZ2NfW1dTT0tHQz87NzMvKycjHxsXEw8LBwL++vby7urm4t7a1tLOysbCvrq2sq6qpqKempaSjoqGgn56dnJuamZiXlpWUk5KRkI+OjYyLiomIh4aFhIOCgYB/fn18e3p5eHd2dXRzcnFwb25tbGtqaWhnZmVkY2JhYF9eXVxbWllYV1ZVVFNSUVBPTk1MS0pJSEdGRURDQkFAPz49PDs6OTg3NjU0MzIxMC8uLSwrKikoJyYlJCMiISAfHh0cGxoZGBcWFRQTEhEQDw4NDAsKCQgHBgUEAwIBAAAh+QQBAAACACwAAAAACwALAAACGpQNpxi5AoJ0J8pZr05WX+5hzLZ0lBKd5VIAADs=);
        background-repeat: no-repeat;
        overflow: hidden;
    }

    .close {
        position: absolute;
        color: #666;
        font: 12px "simsun";
        cursor: pointer;
    }
}

.ETooltips-top {
    padding-bottom: 5px;

    .arrow {
        left: 10px;
        bottom: 0;
        width: 11px;
        height: 6px;
        background-position: 0 -5px;
        _bottom: -1px;
        z-index: 2;
    }
    .close {
        right: 1px;
        top: 0px;
    }
}

.ETooltips-right {
    padding-left: 5px;
    .arrow {
        top: 10px;
        left: 0;
        width: 6px;
        height: 11px;
        background-position: 0 0;
    }
    .close {
        right: 1px;
        top: 1px;
    }
}

.ETooltips-bottom {
    padding-top: 5px;

    .arrow {
        top: 0;
        left: 10px;
        width: 11px;
        height: 6px;
        background-position: 0 0;
    }
    .close {
        right: 1px;
        top: 5px;
    }
}

.ETooltips-left {
    padding-right: 5px;
    .arrow {
        right: 0;
        top: 10px;
        width: 6px;
        height: 11px;
        background-position: -5px 0;
    }
    .close {
        right: 6px;
        top: 1px;
    }
}

.ETooltips-opposite {
    .arrow {
        left: 80%;
    }
}
