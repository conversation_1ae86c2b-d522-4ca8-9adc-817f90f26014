define('MOD_ROOT/combine/combine', function(require, exports, module) {
    var Recommend = require('MOD_ROOT/common/tools/recommend');
    var trimPath      = require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var Tools = require('MOD_ROOT/common/tools/tools');
    var G = require('MOD_ROOT/common/core');

    var combineBuy = $('#combine-con');
    var cfg = (window.pageConfig &&
        window.pageConfig.product) || {};

    var template = '\
        <ul class="lh">\
        {for item in data}\
        <li data-clk="${item.clk}" \
            data-push="${pageConfig[skuHooks].push(item.sku)}" \
            onclick=\'log("gz_item", "gz_detail","02","zjzh_sp_${item_index}",${item.sku},"main")\' \
            {var feedbacked = item.subsku[0]&&item.subsku[0].sku==cfg.skuid}\
            class="fore${item_index}{if Number(item_index)+1==data.length} last-item{/if} {if feedbacked} z-have-feedback{/if}">\
            <s></s>\
            <div class="p-img">\
                <a href="//item.jd.com/${item.sku}.html" target="_blank" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                    <img width="130" height="130" src="${pageConfig.FN_GetImageDomain(item.sku)}n3/${item.img}">\
                </a>\
            </div>\
            <div class="p-name ac">\
                <a href="//item.jd.com/${item.sku}.html" title="${item.t}" target="_blank">${item.t}</a>\
            </div>\
            <div class="p-price ac">\
                <strong class="J-p2-${item.sku}">￥${item.jp}</strong>\
            </div>\
            {if ext.pType===1}\
            <div class="p-match">\
                {if feedbacked}\
                    <span class="not-match">已反馈</span>\
                {else}\
                    <span class="not-match">已反馈</span>\
                    <i class="i-trash" data-sku="${item.sku}"></i>\
                    <span class="match-tip">与「${item.t}」不匹配？请点击反馈</span>\
                {/if}\
            </div>\
            {/if}\
        </li>\
        {/for}\
    </ul>';

    // 3C 商品添加反馈交互
    function addFeedback( reco ) {
        var hoverClass = 'li-hover';
        var feedbacked = 'feedbacked';

        reco.$el.delegate('li', 'mouseenter', function () {
            if ( !$(this).hasClass(feedbacked) ) {
                $(this).addClass(hoverClass);
            }
        });
        reco.$el.delegate('li', 'mouseleave', function () {
            if ( !$(this).hasClass('feedbacked') ) {
                $(this).removeClass(hoverClass);
            }
        });

        reco.$el.delegate('.i-trash', 'click', function () {
            setIgnore( $(this) );
        });
        reco.$el.delegate('.i-trash', 'mouseenter', function () {
            $(this).next().show();
        });
        reco.$el.delegate('.i-trash', 'mouseleave', function () {
            $(this).next().hide();
        });

        function setIgnore( $del ) {
            var delSku = $del.attr('data-sku');

            var params = {
                p: reco.param.p,
                uuid: reco.param.uuid,
                sku: reco.param.sku,
                lid: reco.param.lid,
                hi: 'bskus=' + delSku,
                securityToken: readCookie("shshshfpb") || '',
                clientChannel: '3',
                clientPageId: 'item.jd.com',
            };
            var body = JSON.stringify(params);
            var time = new Date().getTime()
            // 加固start
            var colorParm = {
                appid: 'item-v3',
                functionId: 'recDivinerApi',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                            getDivinerData(colorParm);
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getDivinerData(colorParm);
                        //烛龙上报
                        Tools.getJmfe(colorParm, e, "combine推荐接口设备指纹异常", 250)
                    }
                })
            }catch(e){
                colorParm['loginType'] = '3';
                colorParm['uuid'] = '';
                getDivinerData(colorParm);
                //烛龙上报
                Tools.getJmfe(colorParm, e, "combine推荐接口加固异常", 250)
            } 
        }

        function getDivinerData (colorParm) {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            $.ajax({
                url: host,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                headers: Tools.getUrlSdx(),
                success: function (r) {
                    if ( r && r.success ) {
                        $del.hide().parent().find('.not-match').show();
                        $del.parents('li').eq(0).addClass(hoverClass + ' ' + feedbacked);
                    }else{
                        try {
                            if(parseInt(r.code) < 10 && r.echo){
                                var echoCode = r.echo.length > 1000 ? r.echo.substring(0,999) : r.echo;
                                //烛龙上报
                                Tools.getJmfe(colorParm, echoCode, "combine推荐接口成功异常", 250)
                            }
                        } catch(e) {
                            console.log('上报recDivinerApi错误',e)
                        }
                    }
                },
                error: function (e) {
                    //烛龙上报
                    Tools.getJmfe(colorParm, e, "combine推荐接口错误异常", 250)
                }
            });
        }
    }

    function init(pType) {

        // 七日
        var $reco_rank_0 = $("#seven-con");
        // 新书热卖
        var $reco_rank_1 = $("#hotbook-con");
        var limit = pageConfig.wideVersion && pageConfig.compatible ? 6 : 5;
        var rId = null;

        if ( pType === 1 ) {
            rId = 103003;
        }

        if ( pType === 2 ) {
            rId = 102001;
        }

        var rank0Rid = null;
        var rank1Rid = null;

        if ( pType === 3 ) {
            rId = 104001;
            rank0Rid = 104004;
            rank1Rid = 104005;
        }

        if ( pType === 4 ) {
            rId = 104026;
            rank0Rid = 104020;
            rank1Rid = 104021;
        }

        if ( rId && !pageConfig.product.closeRecom ) {  // 人气单品
            var reco_combine = new Recommend({
                //url: '//mixer.jd.com/mixer?',
                $el: combineBuy,
                skuHooks: 'SKUS_combine',
                template: template,
                ext: {
                    pType: pType,
                    divId: "rqdp-"
                },
                param: {
                    // for test
                    // fe: 100,
                    p: rId,
                    sku: G.sku,
                    ck: 'pin,ipLocation,atw,aview',
                    lim: limit
                },
                callback: function(hasData, r) {
                    if ( hasData && pageConfig.fittingSuitTab.nav.eq(2).data('disabled') !== 1 ) {
                        // pageConfig.fittingSuitTab.loaded[2] = hasData;
                        pageConfig.fittingSuitTab.show(2);
                    }
                    if ( pType === 1 ) {
                        addFeedback(this);
                    }
                    try{
                        if(hasData){
                            // 主图浮层初始化渲染
                            // var skuArrs = []
                            // for(i = 0;i < r.data.length; i++){
                            //     skuArrs.push(r.data[i].sku)
                            // }
                            // Tools.getMainPic(130, 130,"rqdp-","2",skuArrs)
                            var layerList = r.floatLayerList
                            if(layerList.length > 0){
                                Tools.getPcSkuLayers(layerList, 130, 130, 'rqdp-', "2")
                            }
                        }
                    }catch(e){
                        console.log("主图浮层初始化渲染",e)
                    }
                }
            });
        }

        // 七日畅销榜
        if (rank0Rid) {
            new Recommend({
                $el: $reco_rank_0,
                skuHooks: 'SKUS_sevenDays',
                template: template,
                param: {
                    p: rank0Rid,
                    sku: cfg.skuid,
                    lim: 6,
                    ck: 'pin,ipLocation,atw'
                },
                ext: {
                    divId: "qrcxb-"
                },
                onPriceLoad: function (sku, r) {
                    $reco_rank_0.find('.p-discount-' + sku).html(G.discount(r.p, r.m)).attr('data-loaded', '1');
                    pageConfig.fittingSuitTab.show(3);
                },
                callback: function(hasData, r) {
                    try{
                        if(hasData){
                            // 主图浮层初始化渲染
                            // var skuArrs = []
                            // for(i = 0;i < r.data.length; i++){
                            //     skuArrs.push(r.data[i].sku)
                            // }
                            // Tools.getMainPic(130, 130,"qrcxb-","2",skuArrs)
                            var layerList = r.floatLayerList
                            if(layerList.length > 0){
                                Tools.getPcSkuLayers(layerList, 130, 130,"qrcxb-","2")
                            }
                        }
                    }catch(e){
                        console.log("主图浮层初始化渲染",e)
                    }
                }
            });
        }
        
        if (rank1Rid) { // 新书热卖榜
            new Recommend({
                $el: $reco_rank_1,
                skuHooks: 'SKUS_newbookHot',
                template: template,
                param: {
                    p: rank1Rid,
                    sku: G.sku,
                    lim: 6,
                    ck: 'pin,ipLocation,atw'
                },
                ext: {
                    divId: "xsrmb-"
                },
                onPriceLoad: function (sku, r) {
                    $reco_rank_1.find('.p-discount-' + sku).html(G.discount(r.p, r.m));
                    pageConfig.fittingSuitTab.show(4);
                },
                callback: function(hasData, r) {
                    try{
                        if(hasData){
                            // 主图浮层初始化渲染
                            // var skuArrs = []
                            // for(i = 0;i < r.data.length; i++){
                            //     skuArrs.push(r.data[i].sku)
                            // }
                            
                            // Tools.getMainPic(130, 130,"xsrmb-","2",skuArrs)
                            var layerList = r.floatLayerList
                            if(layerList.length > 0){
                                Tools.getPcSkuLayers(layerList, 130, 130, 'xsrmb-', "2")
                            }
                        }
                    }catch(e){
                        console.log("主图浮层初始化渲染",e)
                    }
                }
            });
        }
    }

    return init;
});
