/* footmark */
@import '../common/lib';

.footmark {
    //.m {
    //    padding: 0 9px;
    //    border: 1px solid #ddd;
    //    border-top: 2px solid #999;
    //}
    //.mt {
    //    height: 30px;
    //    line-height: 30px;
    //}
    .mt {
        .extra {
            line-height: 2em;
        }
    }
    .title {
        width: 50%;
        float: left;
    }
    .extra {
        width: 50%;
        float: right;
        text-align: right;
    }
    .may-like .change {
        display: inline-block;
    }
}

/* may-like */
.footmark {
    .may-like {
        .change .ico {
            width: 16px;
            height: 16px;
            margin-right: 5px;
            background: url(i/update.png) no-repeat;
        }
        .mc {
            position: relative;
            height: 225px;
            overflow: hidden;
        }
        #guess-scroll {
            margin-left: 30px;
        }
    }
    .may-like-list {
        padding-top: 15px;
        margin-right: -10px;
        li {
            width: 150px;
            height: 196px;
            float: left;
            padding: 0 18px 15px;
            text-align: center;
        }
        .p-img {
            position: relative;
        }
        .p-name {
            position: absolute;
            top: 172px;
            left: 0;
            right: 0;
            height: 45px;
            background: rgba(0, 0, 0, 0.7);
            *left: 0;
            filter:progid:DXImageTransform.Microsoft.gradient(startcolorstr=#9F000000,endcolorstr=#9F000000);
            *zoom: 1;
            *top: 210px;
            padding: 0 10px;

            -moz-transition: top 0.1s ease-in-out;
            -webkit-transition: top 0.1s ease-in-out;
            -o-transition: top 0.1s ease-in-out;
            transition: top 0.1s ease-in-out;

            a, a:hover {
                color: #fff;
                display: block;
                height: 34px;
                padding-top: 5px;
                text-align: left;
                line-height: 15px;
            }
        }
        .hover .p-name {
            top: 124px;
            *top: 120px;
            *display: block;
        }
        .p-price {
            color: #FF0F23;
            padding-top: 5px;
            font: 14px/14px Verdana;
        }
    }
    .guess-ctl {
        width: 16px;
        height: 26px;
        position: absolute;
        z-index: 1;
        top: 86px;
        cursor: pointer;
        background: url(i/footprint-arr.png) 0 0 no-repeat;
    }
    #guess-prev.guess-prev-disable {
        background-position: 0 -99px;
        cursor: default;
    }
    #guess-next.guess-next-disable {
        background-position: -16px -99px;
        cursor: default;
    }
    .guess-prev:hover {
        background-position: 0 -48px;
    }
    .guess-prev {
        left: 10px;
        background-position: 0 2px;
    }
    .guess-next:hover {
        background-position: -16px -48px;
    }
    .guess-next {
        right: 10px;
        background-position: -16px 2px;
    }
}

/* recent-view */
.footmark .recent-view .mc {
    height: 120px;
    overflow: hidden;
}

.footmark .recent-view-list {
    margin-right: -36px;
    padding-top: 14px;
}

.footmark .recent-view-list li {
    width: 86px;
    float: left;
    margin: 0 2px 0 0;
    padding-bottom: 14px;
    text-align: center;
}

.footmark .recent-view-list .p-img img {
    width: 70px;
    height: 70px;
}

.footmark .recent-view-list .p-price {
    color: #e3393c;
}

/* root61 */
.root61 .footmark .may-like-list li {
    padding-left: 20px;
}

.root61 .footmark .recent-view-list li {
    margin: 0 2px 0 3px;
    *display: inline;
}
