define('MOD_ROOT/prom/prom', function(require, exports, module) {
    // require('JDF_UNIT/trimPath/1.0.0/trimPath');
    // require('MOD_ROOT/ETooltips/ETooltips');
    
    var Conf = require('PUBLIC_ROOT/conf');
    var G = require('MOD_ROOT/common/core');
    var Tools = require('MOD_ROOT/common/tools/tools');
    var umc = require('MOD_ROOT/umc/umc');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    // var GiftPool = require('MOD_ROOT/gift/gift');
    var Countdown = Tools.Countdown;
    var exposure = Tools.exposure
    var landmine = Tools.landmine
    var initExposureData = Tools.initExposureData
    var callStock = require("MOD_ROOT/address/address").callStock;
    var Guobu=require('MOD_ROOT/buybtn/guobu');
    // var Global=require('MOD_ROOT/address/getGlobal');

    /// 赠品池功能定制，非通用代码 BEGIN///

    // 从Tools模块拷贝而来，进行部分定制
    // var throttle = function(fn, delay, atleast) {
    //     var timer = null
    //     var previous = null
    //     return function(data) {
    //         var now = +new Date()

    //         if (!previous) previous = now

    //         if (now - previous > atleast) {
    //             fn(data)
    //             previous = now
    //         } else {
    //             clearTimeout(timer)
    //             timer = setTimeout(function() {
    //                 fn(data)
    //             }, delay)
    //         }
    //     }
    // };
    // var giftPoolInit = throttle($.proxy(GiftPool.init, GiftPool), 800);

    /// 赠品池功能定制，非通用代码 END///


    var Promotions = {
        init: function(sku, init) {
            // 接口参数
            this.sku = sku || pageConfig.product.skuid
            this.shopId = pageConfig.product.shopId
            this.venderId = pageConfig.product.venderId
            this.ipLoc = Tools.getAreaId().areaIds.join('_')

            // 操作dom元素
            this.$el = $('#summary-promotion')
            this.$prom = $('#prom')
            this.$promOne = $('#prom-one')
            this.$gift = $('#prom-gift')
            this.$fj = $('#choose-additional')
            this.$fujian =  $('#prom-fujian')
            this.$darkBgEl = $('#J-summary-top')
            this.$promPhone = $('#prom-phone')

            this.$wrap = this.$el.find('.J-prom-wrap')
            this.$moreProm = this.$wrap.find('.J-prom-more')

            this.qrcodeLoaded = false

            // 促销个数
            this.promCount = 0
            this.maxPromCount = 2

            // 合约B值
            this.hasBvalue =
                pageConfig.product.proms && pageConfig.product.proms.length > 0
            if (this.hasBvalue) {
                this.promCount += pageConfig.product.proms.length
            }
            var _this = this;
            // 手机类合约机、加价购需要设置不自动初始化
            if ((typeof init) === 'undefined') {
                //this.get()
                //this.$prom.html('');
                // 清空手机促销内容
                //this.$promPhone.html('');
                Event.addListener('onStockReady', function(data){
                    var data = data.stock;
                    var price = data && data.data && data.data.price;
                    var zbxxDesc = data && data.data && data.data.promiseFxgInfo && data.data.promiseFxgInfo.zbxxDesc;
                    var askDoctorInfo = data && data.data && data.data.askDoctorInfo;
                    // 促销个数
                    _this.promCount = 0
                    Promotions.plusTags = price && price.plusTag;
                    Promotions.setJdPrice(price);
                    _this.handleData(data.data)
                    getSeckillFlash(data);
                    rank(data);
                    setTimeout(function() {
                        sendLog(data)
                    }, 500)
                    if(zbxxDesc){
                        $('#zhibaoqi').html('质保期: ' + zbxxDesc) // 售后保障卖家服务质保展示
                    }
                    // getBeltBanner(data);//接腰带系统数据
                    getBtnQrCode(data);//百亿补贴屏蔽按钮展示引导二维码

                    if(askDoctorInfo){//问医生入口数据
                        pageConfig.product.askDoctorInfo = askDoctorInfo
                        var doctorHtml = '<div class="J-doctor-btn" clstag="shangpin|keycount|product|wenyisheng">'+
                                '<div class="name">'+
                                '<i class="J-doctor-icon"></i><a href="' + pageConfig.product.askDoctorUrl + '" target="_blank" title="' + askDoctorInfo.pcEntranceText + '" onclick=\'log("Productdetail","product_wenyisheng",`{"skuId":"' + pageConfig.product.skuid + '"}`)\'>' + askDoctorInfo.pcEntranceText + '</a></div>'+
                                '</div>'
                        var doctorTip = '<div style="position: absolute;top: 21px;left: auto;z-index: 9999;" class="J-doctor-img"><img src="' + askDoctorInfo.pcBubble + '" width="100"></div>'
                        if(askDoctorInfo.pcBubble)
                        {
                            var doctorNewHtml = doctorHtml+doctorTip
                        }else{
                            var doctorNewHtml = doctorHtml
                        }
                        $(".J-doctor-item").html(doctorNewHtml).show();
                        setTimeout(function() {
                            $(".J-doctor-img").hide()
                        }, 5000);
                        try {
                            expLogJSON('Productdetail', 'product_exposure_wenyisheng', '{"skuId": ' + pageConfig.product.skuid + '}')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log('问医生曝光埋点错误');
                            }
                        } 
                    } 
                })
            }
            return this
        },
        renderCarGift: function() {
            if (!$("#choose-car").length) {
                return;
            }
            var _this = this;
            var ipLocDjd = Tools.getAreaId().areaIds,
                provinceId = ipLocDjd[0],
                cityId = ipLocDjd[1],
                areaId = ipLocDjd[2],
                townId = ipLocDjd[3],
                carModelId = pageConfig.jumpCarType;

            // 获取车管家赠品；接口提供者：王琨
            $.ajax({
                url: '//cd.jd.com/getCarSoaGift',
                data: {
                    skus: pageConfig.product.skuid,
                    provinceId:provinceId,
                    cityId:cityId,
                    areaId:areaId,
                    townId:townId,
                    carModelId:carModelId
                },
                dataType: 'jsonp',
                success: function(res) {
                    if (!res || $.isEmptyObject(res)) {
                        return;
                    }
                    for(var key in res){
                        var item = res[key];
                    }

                    $("#prom-car-gift").html("");  //每次添加车管家赠品时先清空容器
                    var $summaryProm = $('#summary-promotion');

                    if (item.hashStore) {
                        var carGiftTPL =
                                '\
                            <div class="J-prom-gift" data-id="${sku}">\
                                <div class="prom-gifts clearfix">\
                                    <span class="prom-gift-label"><em class="car_red_bg">赠品</em></span>\
                                    <div class="prom-gift-list">\
                                        <div class="prom-gift-item">\
                                            <a target="_blank" href="//item.jd.com/${sku}.html" title="${skuName}">\
                                                {if mainImage}\
                                                <img src="${pageConfig.FN_GetImageDomain(sku)}n1/s25x25_${mainImage}" width="25" height="25" class="gift-img" />\
                                                {else}\
                                                <img src="//img30.360buyimg.com/da/jfs/t1264/236/181850154/1105/14bba6c8/5509488cN2093c2a9.png" width="25" height="25" class="gift-img" />\
                                                {/if}\
                                            </a>\
                                            <em class="gift-number">× ${giftCount}</em>\
                                        </div>\
                                        <div class="J-gift-limit gift-limit">（车型赠品）</div>\
                                    </div>\
                                </div>\
                            </div>'
                        var result = carGiftTPL.process(item);
                        var errMsg = result.match(/\[ERROR:.+\]/g);

                        if (errMsg && errMsg[0]) {
                            console.error('[prom gift]Template Rendering error.\n')
                            console.error(errMsg[0])
                        }

                        $("#prom-car-gift").append(result).show();
                        // pageConfig.giftSelectedSkuids=[item.sku];
                        if (pageConfig.jumpCarTxt) {
                            $(".choose-op").addClass("car-type");
                            $(".car-type .car-text").text(pageConfig.jumpCarTxt);
                            $(".choose-op").attr("title",pageConfig.jumpCarTxt);
                            pageConfig.jumpCarTxt = "";
                        }

                        // 屏蔽“打白条”和“一键购”
                        $("#choose-baitiao,#btn-onkeybuy").hide();
                        pageConfig.hasCarGift = true;
                        $summaryProm.show();
                    } else {
                        pageConfig.hasCarGift = false;
                        if (!pageConfig.hasPromotion) {
                            $summaryProm.hide();
                        }
                    }
                }
            })
        },
        handleData: function(result) {
            if (!result) {
                return;
            }
            var _this = this;
            /**
             * repairLinkAddress
             * @param {Object|[Object]} obj
             * @param {String} field
             */
            // function repairLinkAddress(obj, field) {
            //     var rlink = /pro.m.jd.com\/mall\/active\/|item.m.jd.com\/product\//;
            //     var dict = {
            //         "item.m.jd.com/product/": "item.jd.com/",
            //         "pro.m.jd.com/mall/active/": "pro.jd.com/mall/active/"
            //     };

            //     var toString = ({}).toString;
            //     if (toString.call([]) === toString.call(obj)) {
            //         for (var i = 0; i < obj.length; i++) {
            //             repairLinkAddress(obj[i], field);
            //         }
            //     }

            //     if (toString.call({}) === toString.call(obj)) {
            //         for (var k in obj) {
            //             if (obj.hasOwnProperty(k) &&
            //                 k === field &&
            //                 rlink.test(obj[k])) {
            //                 obj[k] = obj[k].replace(rlink, function(_all){
            //                     return dict[_all];
            //                 });
            //             }
            //         }
            //     }

            //     return obj
            // }

            // 通用促销
            var promOneTip = ''
            if (result && result.promotion) {
                var prom = result.promotion || {};
                var activity = prom.activity || {};
                // 手机端专享(目前接口没下发)
                if (prom.mpt) {
                    //this.initMBuy(prom);
                    //this.handleMBuy(result)
                }

                // 促销标签(plus限购和plus限制)
                if (Promotions.plusTags) {
                    var plusTagInfo = $.parseJSON(Promotions.plusTags);
                    //var plusTagInfo = plusTagsObj && plusTagsObj.PLUS;
                    var isPlusLimit = plusTagInfo && plusTagInfo.limit || false; // plus限购
                    var isPlusOverlying = plusTagInfo && plusTagInfo.overlying || false; // plus限制

                    if (activity && activity.length) {
                        for (var i = 0; i < activity.length; i++) {
                            var current = activity[i];
                            if (current.activityType === '3') { // 普通限购
                                if (isPlusLimit) {
                                    //current.value = "非PLUS用户" + current.value;
                                }
                                var commonLimit = true;
                            } else if (current.activityType === '9') {
                                var commonContral = true; // 普通限制
                            }
                        }
                    }

                    if (plusTagInfo) { // plus限购和plus限制标签都是从stock接口里取的数据，需要拼装到促销数据
                        if (isPlusLimit && plusTagInfo.limit_text) {
                            var plusLimitObj = {
                                position: 2,
                                value: plusTagInfo.limit_text,
                                text:"PLUS限购"
                            }
                            activity.push(plusLimitObj);
                        }
                        if (!commonContral && !isPlusOverlying && plusTagInfo.confine_text) {
                            var plusLimitObj = {
                                position: 4,
                                value: plusTagInfo.confine_text,
                                text:"PLUS限制"
                            }
                            activity.push(plusLimitObj);
                        }
                    }
                }

                if (activity && activity.length) {
                    //repairLinkAddress(prom.tags, "adurl");
                    //this.setPromotion(prom.tags)
                    this.setProm({proms: activity,len: activity.length},this.$prom )
                } else {
                    // 促销为空清空赠品
                    this.emptyGift()
                }
                //prom.tip 赠品提示语,prom.tips 所有提示语数组 prom.activityTip 促销提示
                //多选一提示语
                if (prom && prom.activityTip) {
                    this.setPickOneTip(prom.activityTip)
                }
                // 二选一促销,app接口放到activity数组中
                /*if (prom.pickOneTag && prom.pickOneTag.length) {
                    repairLinkAddress(prom.pickOneTag, "adurl");
                    promOneTip = this.setOnePromotion(prom.pickOneTag)
                } else {
                    this.$promOne.html("");
                }*/
                var giftDate = [];
                if(prom.plusGiftInfo && prom.plusGiftInfo.plusGift && prom.plusGiftInfo.plusGift.length>0){
                    var gift = prom.plusGiftInfo.plusGift
                    var content = prom.plusGiftInfo.plusBottom;
                    var name = gift[0].text;
                    var code = gift[0].activityType;
                    giftDate.push({gifts:gift,name:name,content:content,code:code});
                }
                if(prom.gift && prom.gift.length>0){
                    var gift = prom.gift
                    var content = prom.giftTips;
                    var name = gift[0].text;
                    var code = gift[0].activityType;
                    giftDate.push({gifts:gift,name:name,content:content,code:code});
                }
                if(giftDate.length>0){
                    this.renderGift(giftDate, this.$gift)
                    this.promCount += giftDate.length
                }
                // 详情促销附件-显示在促销信息下面
                if (prom.attach && prom.attach.length) {
                    var accessory = [];
                    accessory.push({
                        code: '附件',
                        name: '附件',
                        gifts: prom.attach,
                        content: ''
                    });
                    this.renderGift(accessory, this.$fujian)
                    this.promCount++
                }
                /// PLUS品牌联盟9.5折
                var plusDiscountMap = prom.plusDiscountMap || {};
                if (plusDiscountMap && plusDiscountMap.plusDiscountFlag) {
                    var $promPlus = $("#prom-plus");
                    if ($promPlus.length === 0) {
                        $promPlus = $("<ins id='prom-plus' class='prom-plus'>");
                        this.$el.find(".p-promotions").prepend($promPlus);
                    }
                    this.setProm({
                        proms: [{
                            code: 40,
                            text: "<img src='//img30.360buyimg.com/devfe/jfs/t1/33216/32/12262/1857/5cece96dEd040853e/6223615cd83592b5.png' width='70' height='16'>",
                            value: plusDiscountMap.plusDiscountcw,
                            adurl: "//plus.jd.com/right/index#item-discount"
                        }],
                        len: 1
                    }, $promPlus);
                } else {
                    $("#prom-plus").remove();
                }

                // “车型”右侧是否显示“赠”icon
                if(prom.needCarGift) {

                    $(".car-gift-icon").show();
                    $('.car-gift-icon').ETooltips({
                        close: false,
                        content: "选择车型，查看对应赠品。",
                        width: 167,
                        pos: 'right',
                        zIndex: 10,
                        y: 0,
                        x:-6,
                        autoHide: true
                    });
                    if (!$(".choose-op").is(".car-type")) {
                        $(".choose-op a").text("选车型获取赠品");
                    }

                }

                // 商品详情包装清单附件,已无此逻辑
                /*if (prom.packing && prom.packing.length) {
                    this.setPackages(prom)
                }*/

                //赠品池促销,3C赠品池促销
                if (prom.giftPool3C) {
                    this.setGiftPool(prom)
                }
                // 升级购
                if (/debug=sjgo/.test(location.href)) {
                    prom.sjgo = {
                        url: '//channel.jd.com/sjgo.html',
                        targetId: '123456'
                    }
                }
                if (prom.sjgo) {
                    this.setSJG(prom.sjgo)
                } else {
                    $('#jjg-sjgo').remove()
                }
            }

            // 广告词
            if (result.ad) {
                this.setAdWords(result.ad)
            }

            // 满额返券，放到了activity中
            /*if (result.quanStatus === 200) {
                repairLinkAddress(result.quan, "actUrl");
                this.setExtraPromotions(result.quan);
            }*/

            // 优惠券和促销
            if (result.preferenceInfo) {
                this.setSkuCoupon(result)
            }
            // 拉新（获取新用户）
            if(result.laXinInfo) {
                this.setUserAcquisition(result.laXinInfo)
                const that = this
                $(window).resize(function() {
                    that.setUserAcquisition(result.laXinInfo)
                });
            }
            // 设置折叠弹层
            this.setFoldLayer()
            // 触发促销接口请求完成
            Event.fire({
                type: 'onPromReady',
                result: result,
                prom: result.promotion
            })
        },
        setSJG: function(data) {
            var $choose = $('.itemInfo-wrap')
            var url = getUrl(data)
            var html =
                '\
            <div id="jjg-sjgo" style="clear:both;padding-left:10px;display:none" clstag="shangpin|keycount|product|3chuodongbanner">\
                <a target="_blank" style="display:inline;" href="' +
                url +
                '">\
                    <img width="360" height="61" src="//img13.360buyimg.com/da/jfs/t2656/312/1769527897/13223/587f8601/574693acNa9a3ef54.png" />\
                </a>\
            </div>'

            function getUrl(r) {
                var gidStr = pageConfig.giftSelectedSkuids
                    ? pageConfig.giftSelectedSkuids.join(',')
                    : ''
                return (
                    r.url +
                    '?' +
                    $.param({
                        pid: pageConfig.product.skuid,
                        pcount: $('#buy-num').val(),
                        targetId: r.targetId,
                        ptype: 13,
                        gids: gidStr
                    })
                )
            }
            if (!$('#jjg-sjgo').length) {
                $choose.append(html)

                var $sjgo = $('#jjg-sjgo')

                function checkDisplay() {
                    if (pageConfig.product.havestock) {
                        $sjgo.show()
                    } else {
                        $sjgo.hide()
                    }
                }
                function changeNum() {
                    num = $('#buy-num').val()
                    var $tar = $sjgo.find('a')
                    var url = $tar.attr('href')

                    $tar.attr(
                        'href',
                        url.replace(
                            /(nums|pcount|buyNum)=\d+/g,
                            '$1' + '=' + num
                        )
                    )
                }

                checkDisplay()
                Event.addListener('onStockReady', checkDisplay)
                Event.addListener('onNumChange', changeNum)
                Event.addListener('onGiftSelected', function() {
                    $sjgo.find('a').attr('href', getUrl(data))
                })
            }
        },
        setGiftPool: function(data) {
            var obj = data && data.giftPool3C;
            if ( $.isPlainObject(obj) ) {
                require.async([
                    "MOD_ROOT/gift/gift"
                ], function (GiftPool) {
                    var throttle = function(fn, delay, atleast) {
                        var timer = null
                        var previous = null
                        return function(data) {
                            var now = +new Date()

                            if (!previous) previous = now

                            if (now - previous > atleast) {
                                fn(data)
                                previous = now
                            } else {
                                clearTimeout(timer)
                                timer = setTimeout(function() {
                                    fn(data)
                                }, delay)
                            }
                        }
                    };
                    var giftPoolInit = throttle($.proxy(GiftPool.init, GiftPool), 800);

                    if (GiftPool.model) {
                        // 数量高频点击优化
                        giftPoolInit(data);
                    } else {
                        // 页面首次加载时快速初始化
                        GiftPool.init(data);
                    }
                });
            }
        },
        setSkuCoupon: function(r) {

        //     {if typeof giftInfo!="undefined"}\
        //     <span class="quan-item" \
        //         title="${giftInfo.value}">\
        //         <span class="text">${giftInfo.text}</span>\
        //     </span>\
        // {/if}\
        /**
         * tag = 95 为国补
         * label = best 为命中实验
         * govTextSwitch 为国补开关，true 为后端拼接，false 为前端拼接 
         */
            var promotion = ''
            if (r.govTextSwitch) { // 开关
                promotion = '{if item.tag == 95}\
                    <span class="text" style="color:${item.shortTextColor || "#0AAD48"}">${item.shortText || item.text}</span>\
                {else}\
                    {if label == "best"}\
                        <span class="text" >${item.shortText || item.text}</span>\
                    {else}\
                        <span class="text" >${item.text}</span>\
                    {/if}\
                {/if}'
            } else {
                promotion = '<span class="text" style="color:${item.tag == 95 && (item.shortTextColor || "#0AAD48")}">${item.tag == 95 && item.gjBtText && item.verLine ? item.gjBtText + "<img src=" + item.verLine + "/>" + item.shortText : item.text}</span>'
            }
            var html =
            '\
            <dl>\
                <dt class="fl"></dt>\
                <dd class="lh">\
                    <a class="J-open-tb" href="#none">\
                        {var couponLen = (typeof coupons!="undefined") && coupons.length}\
                        {var promotionsLen =  (typeof promotions!="undefined") &&  promotions.length}\
                        {var otherAttrsLen =  (typeof otherAttrs!="undefined") &&  otherAttrs.length}\
                        {if otherAttrsLen > 0}\
                            {for item in otherAttrs}\
                                {if item_index < 3}\
                                    <span class="quan-item" title="${item.value}" promotionId="${item.logPromoId}" style="background:${item.textBackColor}">\
                                        <span class="text" style="color:${item.textColor}">${item.gjBtText && item.verLine ? item.gjBtText + "<img src=" + item.verLine + "/>" + item.text : "国家补贴"}</span>\
                                    </span>\
                                {/if}\
                            {/for}\
                        {/if}\
                        {if promotionsLen > 0}\
                            {for item in promotions}\
                                {if item_index < 3}\
                                    <span class="quan-item" title="${item.value}" promotionId="${item.logPromoId}" style="${item.tag == 95 && "background: " + (item.shortTextBackColor || "#F0FAF4")}">\
                                        ' + promotion + '\
                                    </span>\
                                {/if}\
                            {/for}\
                        {/if}\
                        {if couponLen > 0}\
                            {for item in coupons}\
                                {if item_index < 3}\
                                <span class="quan-item" \
                                    title="${item.desc}" batchId="${item.batchId}">\
                                    <span class="text">${item.desc}</span>\
                                </span>\
                                {/if}\
                            {/for}\
                        {/if}\
                        {if typeof fujianInfo!="undefined"}\
                            <span class="quan-item" \
                                title="${fujianInfo.value}">\
                                <span class="text">${fujianInfo.text}</span>\
                            </span>\
                        {/if}\
                        {if typeof zuTaoInfo!="undefined"}\
                            <span class="quan-item" \
                                title="${zuTaoInfo.value}">\
                                <span class="text">${zuTaoInfo.text}</span>\
                            </span>\
                        {/if}\
                        {if typeof plusGiftInfo!="undefined" || typeof giftInfo!="undefined"}\
                            <span class="quan-item">\
                                <span class="text">赠品</span>\
                            </span>\
                        {/if}\
                        <span class="more-btn"></span>\
                    </a>\
                </dd>\
            </dl>'

            var preferenceInfo = r.preferenceInfo
            var price = r.price
            var $quan = $('#summary-quan')
            if((typeof preferenceInfo !== "undefined") &&
                 ((preferenceInfo.coupons && preferenceInfo.coupons.length > 0) 
                 || (preferenceInfo.promotions && preferenceInfo.promotions.length > 0)
                 || (typeof preferenceInfo.fujianInfo !== "undefined")
                 || (typeof preferenceInfo.giftInfo !== "undefined")
                 || (typeof preferenceInfo.plusGiftInfo !== "undefined")
                 || (typeof preferenceInfo.zuTaoInfo !== "undefined"))
            ) {
                var abData = r.abData
                preferenceInfo.label = abData && abData.promotion_optimize && abData.promotion_optimize.label
                
                $quan.html(html.process(preferenceInfo)).show()
                this.exposureQuan($quan, preferenceInfo, price)
            }
        },
        exposureQuan: function($quan, data, price){
            try{
                var LabelList = {}
                $quan.find('.J-open-tb .quan-item').each(function(index, ele){
                    var text = $(ele).text().trim()
                    var batchId = $(ele).attr("batchId")
                    var promotionId = $(ele).attr("promotionId")
                    var promotionItem = data.promotions.find(function(item) {
                        return item.promoId == promotionId    
                    }) || {}

                    LabelList = {
                        "labelName": text || "",
                        "batchId":  batchId|| "-100",
                        "promotionId": promotionId || "-100",
                        firstPromoTag: promotionItem.tag ? String(promotionItem.tag) : '-100', //一级促销类型
                        PromoTags: promotionItem.promoTags || ["-100"]//二级促销类型
                    }
                    // 曝光
                    exposure({
                        functionName: 'PC_Productdetail_PromotionFloor_Expo',
                        exposureData: ['mainskuid'],
                        extraData: {
                            LabelList: LabelList
                        },
                        errorTips: '优惠券曝光报错'
                    })
                })
            } catch (error) {
                console.log("PC_Productdetail_PromotionFloor_Expo报错",error)
            }
            try{
                // 埋点
                $quan.find('.J-open-tb .quan-item').each(function(index, ele){
                    $(ele).unbind("click").click(function(){
                        try{
                            var text = $(this).text().trim()
                            var quan = data.promotions.find(function(item) {
                                return item.text == text
                            })
                            quan = quan || data.coupons.find(function(item) {
                                return item.desc == text
                            })
                            quan = quan || {}

                            landmine({
                                functionName: 'PC_Productdetail_PromotionFloor_Click',
                                exposureData: ['mainskuid'],
                                extraData: {
                                    LabelList: {
                                        "labelName": quan.text || quan.desc || "",
                                        "batchId": quan.activityId|| quan.batchId || "-100",
                                        "promotionId": quan.promoId ? String(quan.promoId) : "-100",  
                                        firstPromoTag: quan.tag ? String(quan.tag) : '-100', //一级促销类型
                                        PromoTags: quan.promoTags || ['-100'] //二级促销类型
                                    }
                                },
                                errorTips: '优惠券埋点报错'
                            })
                        } catch (error) {
                            console.log("优惠券埋点报错",error)
                        }
                    })
                })
            } catch (error) {
                console.log("PC_Productdetail_PromotionFloor_Click报错",error)
            }
            if(pageConfig.product.showcoupons){
                try{
                    window.itemEventBus.dispatchEmit('CouponDrawerClick',({
                        open: pageConfig.product.showcoupons || false,
                        promotions:data.promotions,
                        coupons:data.coupons,
                        expression:data.expression,
                        price:price,
                        giftInfo:data.giftInfo,
                        fujianInfo:data.fujianInfo,
                        plusGiftInfo:data.plusGiftInfo,
                        zuTaoInfo:data.zuTaoInfo
                    }))
                    pageConfig.product.showcoupons = false
                }catch(e){
                    console.error("点击劵促弹层报错CouponDrawerClick异常:",e)
                }
            }
            
            // 点击优惠券弹层重新监听融合接口
            try{
                $('#summary-quan .J-open-tb').unbind('click').click(function(){
                    try{
                        Event.fire({
                            type: 'onNumChange'
                        });
                        pageConfig.product.showcoupons  = true
                    } catch (error) {
                        console.log("CouponDrawerClick报错",error)
                    }
                })
            } catch (error) {
                console.log("点击劵促弹层报错",error)
            }
            
        },
        
        setUserAcquisition: function(laXinInfo) {
            var dom = $('#lachine .ddnew')
            var outer = $('#lachine')
            if (!laXinInfo.length || !outer.length) return
            outer.removeClass('expand')
            var html = laXinInfo.map(function(item){
                if (item.laXinJumpUrl) {
                    return '<li><img src="' + item.laXinIconUrl + '"><div><a target="_blank" href="' + item.laXinJumpUrl + '">' + item.laXinText + '</a></div></li>'
                } else {
                    return '<li><img src="' + item.laXinIconUrl + '"><div>' + item.laXinText + '</div></li>'
                }
            }).join('')
           
            // 埋点type "2=PLUS拉新/20=企业新人/4=山姆会员/9=学生"
            // 后端type 1：plus拉新 2：山姆拉新 3.校园拉新 4.企业(原企业购更优惠)  5.企业(高潜客户)
            var typeMap = {
                1: 2,
                2: 4,
                3: 9,
                4: 20,
                5: 20
            }
            // 曝光
            laXinInfo.forEach(function(item, index) {
                // console.log('曝光', data);
                exposure({
                    functionName: 'Productdetail_AttractFloor_Expo',
                    exposureData: ['mainskuid'],
                    extraData: {
                        newatypex: String(typeMap[item.laXinType])
                    },
                    errorTips: '拉新区曝光错误'
                })
                
            });
           
            dom.html(html)
            outer.show() // 显示整个楼层

            var parentWidth = outer.outerWidth()
            var parentLeft = outer.offset().left
            if (dom.outerWidth() > parentWidth) { // 拉新数据超过内容区域显示下拉箭头
                outer.append('<span class="arrow"></span>') 
            } else {
                outer.find('.arrow').remove()
            }
            
            const that = this
            outer.find('.arrow').click(function() {
                outer.toggleClass('expand')
            })
            // this.hideOrShowMore('hide')
           
            dom.children('li').hover(function() {
                var left = $(this).offset().left
                var width = $(this).outerWidth()
                if (width + left > parentWidth + parentLeft) { // 判断是否超出内容区域
                    dom.css({
                        right: '28px',
                    })
                }
            }, function() {
                dom.css({
                    right: 'auto',
                })
            })
            // 埋点
            dom.children('li').each(function(index) {
                const item = laXinInfo[index]
                $(this).click(function(){
                    // console.log('触发埋点', laXinInfo[index], JSON.stringify(data));
                    landmine({
                        functionName: 'Productdetail_AttractFloor_Click',
                        exposureData: ['mainskuid'],
                        extraData: {
                            newatypex: String(typeMap[item.laXinType])
                        },
                        errorTips: '拉新区埋点错误'

                    })
                })
                
            })
        },
        showJiaDianQuan: function() {
            // 家电类领券入口
            var $quan = $('#summary-jiadian-quan')
            if ($quan.length > 0) {
                $quan.show()
                $('body').addClass('lingquan')
            }
        },
        setTuanCD: function(prom) {
            var $tuanBanner = $('#tuan-banner .time-remain')
            var $tuanCD = $tuanBanner.find('.J-tuan-cd')

            if (prom.tr) {
                prom = Number(prom.tr)
                if (prom > 0) {
                    $tuanBanner.show()
                    G.Countdown.init(prom, function(r) {
                        $tuanCD.html(
                            r.d + '天' + r.h + '时' + r.m + '分' + r.s + '秒'
                        )
                    })
                }
            }
        },
        setPromotion: function(prom) {
            var proms = []
            //var len = activity.length
            //var giftData = []
            //团购场景
            /*for (var i = 0; i < len; i++) {
                var current = prom[i]

                // 团购倒计时,app没下发倒计时,和其他促销一块下发的。
                if (current.code === '20') {
                    this.setTuanCD(current)
                }

                if (current.code === '10' || current.code === '80') {
                    giftData.push(current)
                } else {
                    if (current.code === '3') {
                        current.position = 1;
                    } else if (current.code === '9') {
                        current.position = 3;
                    }
                    proms.push(current)
                }
            }*/

            /*if (giftData) {
                this.renderGift(giftData, this.$gift)
            } else {
                this.emptyGift()
            }*/

            // 根据数组中对象的position属性，升序排序
            /*function compare(property){
                return function(a,b){
                    var value1 = a[property];
                    var value2 = b[property];
                    return value1 - value2;
                }
            }
            proms.sort(compare('position'));*/

            /*this.setProm(
                {
                    proms: activity,
                    len: activity.length
                },
                this.$prom
            )*/
        },
        emptyGift: function() {
            this.$gift.html('').hide()
        },
        setOnePromotion: function(prom) {
            var proms = []
            var pickOneTips = ''

            proms = proms.concat(prom)

            var tipArr = []
            for (var i = 0; i < proms.length; i++) {
                if (proms[i].name != '活动预告') {
                    tipArr.push('“' + proms[i].name + '”')
                }
            }

            if (tipArr.length > 1) {
                pickOneTips = removeRepeat(tipArr).join(' ') + ' 仅可在购物车任选其一'
            }

            this.setProm(
                {
                    proms: proms,
                    len: prom.length
                },
                this.$promOne
            )

            return pickOneTips //返回多选一的提示

            // 节后移除
            function checkIsThreeMinusOneForProm() {
                var targetPromId = '2803834743'

                for (var i = 0; i < prom.length; i++) {
                    var promType = prom[i].pid
                    if (promType.indexOf(targetPromId) > -1) {
                        pageConfig.__isThreeMinusOneForProm = true
                        break
                    } else {
                        pageConfig.__isThreeMinusOneForProm = false
                    }
                }
            }

            //去重
            function removeRepeat(arr) {
                var res = []
                var json = {}
                for (var i = 0; i < arr.length; i++) {
                    if (!json[arr[i]]) {
                        res.push(arr[i])
                        json[arr[i]] = 1
                    }
                }
                return res
            }
        },
        setAdWords: function(adword) {
            // 如果是分区商品可能请求sku与当前sku不一致，直接输出广告词内容到 #p-ad
            try {
                if (adword) {
                    $('#p-ad').html(adword).show()
                    var title = adword.replace(/<.*?>/g, '')
                    $('#p-ad').attr('title', title)
                }     
            } catch (error) {
               console.log("广告词setAdWords问题",error)
            }
            
        },
        setPackages: function(r) {
            var fjTPL =
                '\
            {for fj in packing}\
                <li>${fj.nm} × ${fj.num}</li>\
            {/for}'

            var $fj = $('#product-fj')

            if (!$fj.length) {
                $('#product-detail-3').append('<div id="product-fj"></div>')
                $fj = $('#product-fj')
            }

            $fj.html(fjTPL.process(r))
        },
        setExtraPromotions: function(r) {//满额返券
            var _this = this
            var target = $('#prom-quan')

            var text =
                '&nbsp;<a href="{href}" target="_blank">详情 <s class="s-arrow">&gt;&gt;</s></a>'
            if (!r || !r.title) {
                target.html('')
                return
            }
            text = r.actUrl ? text.replace('{href}', r.actUrl) : ''

            target.html(
                '<div class="J-prom-quan prom-quan"><em class="hl_red_bg">满额返券</em><em class="hl_red">' +
                    r.title +
                    '</em>' +
                    text +
                    '</div>'
            )

            _this.$el.show()

            _this.promCount++

            _this.setFoldLayer()
        },
        renderGift: function(r, $el) {
            var _this = this;
            var giftTPL =
                '\
            <div class="J-prom-gift">\
                <div class="prom-gifts clearfix">\
                    {if code == "80"}\
                    <span class="prom-plus-gift-label"><em class="yellow-bg">${name}</em></span>\
                    <div class="prom-plus-gift-list">\
                    {else}\
                    <span class="prom-gift-label"><em class="hl_red_bg">${name}</em></span>\
                    <div class="prom-gift-list">\
                    {/if}\
                        {for item in gifts}\
                        <div class="prom-gift-item" data-count="${pageConfig.isGiftProm = true}">\
                            <a target="_blank" href="//item.jd.com/${item.skuId}.html" title="${item.value}">\
                                {if item.mp}\
                                <img src="${pageConfig.FN_GetImageDomain(item.skuId)}n1/s25x25_${item.mp}" width="25" height="25" class="gift-img" />\
                                {else}\
                                <img src="//img30.360buyimg.com/da/jfs/t1264/236/181850154/1105/14bba6c8/5509488cN2093c2a9.png" width="25" height="25" class="gift-img" />\
                                {/if}\
                            </a>\
                            {if item.num}\
                            <em class="gift-number">× ${item.num}</em>\
                            {else}\
                                <em class="gift-number">× 1</em>\
                            {/if}\
                        </div>\
                        {/for}\
                        <div class="J-gift-limit gift-limit">${content}</div>\
                    </div>\
                </div>\
            </div>'
            /*if (r instanceof Array) {
                r.sort(this.arrObjectCompare("code"));
            }*/

            $el.html("");  //每次添加赠品时先清空容器

            $.each(r, function(i, o){
                o.css = (o.name && o.name.length>2)?"w":"";
              if ( $(".prom-plus-gift-list").length ) {
                  o.content = "（非PLUS会员可领，赠完即止）";
              }
              //plus会员文案修改
              // code :80   赠品促销，plus赠品
              // 具体code 文档见：http://cf.jd.com/pages/viewpage.action?pageId=74207344
              if(o.code == 80){
                  if(!_this.isPlus){
                      o.content = "<a href='//plus.jd.com/index?flow_entrance=gift&flow_channel=pc' target='_blank' style='width:auto;color: #cfae68;'>开通PLUS会员，立享专享赠品>></a>";
                  }else{
                      o.content = "（PLUS会员可领，赠完即止）";
                  }
              }
              //赠品自定义
            //     o.css = ''
            //    if(o.code == 10){
            //       if(typeof o.customtag == "object" && (o.customtag).hasOwnProperty('2')){
            //           //其实就是组套商品
            //           o.name = o.customtag['2']
            //           //为了优化宽度
            //           o.css = 'w'
            //       }else{
            //           o.name = '赠品'
            //       }
            //    }
              var result = giftTPL.process(o)
              var errMsg = result.match(/\[ERROR:.+\]/g)

              if (errMsg && errMsg[0]) {
                  console.error('[prom gift]Template Rendering error.\n')
                  console.error(errMsg[0])
              }

              $el.append(result).show()

            });

        },
        /**
         *   对象数组通过某一对象数学进行排序方法
         *    @param {string} propName  用来做比较的属性名称
         */
        arrObjectCompare: function (propName) {
          return function (object1, object2) {
              var value1 = object1[propName];
              var value2 = object2[propName];
              if (value1 < value2) {
                  return 1;
              }else if (value1 > value2) {
                  return -1;
              }else {
                  return 0;
              }
          };
        },
        setProm: function(r, $ele) {
            var promTPL =
                '\
            <div class="J-prom">\
                {for prom in proms}\
                <div class="prom-item" data-code="${prom.activityType}">\
                    {if prom.icon}\
                        ${prom.icon}\
                    {elseif prom.text && (prom.position == 2 || prom.position == 4)}\
                        <em class="hl_red_bg yellow">${prom.text}</em>\
                    {elseif prom.text}\
                        <em class="hl_red_bg">${prom.text}</em>\
                    {/if}\
                    {if prom.crossStoreFullCut}<em class="mj_red">{if prom.tips}<i class="sprite-tips"></i>{/if}${prom.value}</em>{else}<em class="hl_red">{if prom.tips}<i class="sprite-tips"></i>{/if}${prom.value}</em>{/if}\
                    {if prom.formal}<em class="mj_red mj-time"></em>{/if}\
                    {if prom.adurl}\
                    <a href="${prom.adurl}" clstag="shangpin|keycount|product|${prom.text}" target="_blank">\
                        {if prom.linkName}${prom.linkName}{else}详情{/if} <s class="s-arrow">&gt;&gt;</s>\
                    </a>\
                    {/if}\
                    {if prom.link}${prom.link}{/if}\
                </div>\
                {/for}\
            </div>'

            var result = promTPL.process(r)
            var errMsg = result.match(/\[ERROR:.+\]/g)

            if (errMsg && errMsg[0]) {
                console.error('[prom item]Template Rendering error.\n')
                console.error(errMsg[0])
            }

            

            this.promCount += r.len
            $ele.html(result)

            var $time = $('#prom .mj-time')
            for (var i = 0; i < r.proms.length; i++) {
                var mal = r.proms[i].formal
                var time = r.proms[i].remainTime
                if(mal && time > 0){
                    new Countdown(parseInt((+time) * 1000), function(res) {
                        if (res.d < 1) {
                            $time.html(res.h + '小时' + res.m + '分' + res.s + '秒')
                        } else {
                            $time.html(
                                res.d +
                                '天' +
                                res.h +
                                '小时' +
                                res.m +
                                '分' +
                                res.s +
                                '秒'
                            )
                        }
                    })
                }
            }
        },
        setPickOneTip: function(promOneTip) {
            var $pickOneTip = $('#pickOneTip')
            if ($pickOneTip.length > 0) {
                $pickOneTip.remove()
            }
            this.$el
                .find('.p-promotions')
                .append(
                    '<ins id="pickOneTip"><em class="hl_red"><i class="sprite-tips"></i>' +
                        promOneTip +
                        '</em></ins>'
                )
        },
        loadQrcode: function(url) {
            require.async('PLG_ROOT/jQuery.qrcode', function() {
                var qrcodeUrl =
                    url || '//m.jd.com/product/' + G.sku + '.html?from=qrcode'

                $('#summary-mbuy .qrcode').html('').jdQrcode({
                    render: 'image',
                    ecLevel: 'L',
                    size: 145,
                    text: qrcodeUrl
                })
            })
        },
        // handleMBuy: function(r) {
        //     var _this = this

        //     // 获取手机端专享价
        //     $.ajax({
        //         url: '//pm.3.cn/prices/pcpmgets',
        //         data: {
        //             skuids: this.sku,
        //             origin: 2,
        //             source: 1,
        //             area: this.ipLoc
        //         },
        //         dataType: 'jsonp',
        //         success: function(p) {
        //             var price = '暂无报价'
        //             if (p && p.length && p[0].p && p[0].pcp) {
        //                 if (Number(p[0].pcp) > Number(p[0].p)) {
        //                     price = p[0].p
        //                 } else {
        //                     price = null
        //                 }
        //                 _this.initMBuy(r, price)
        //             }
        //         }
        //     })
        // },
        initMBuy: function(r, price) {
            var _this = this
            var $mBuy = $('#prom-mbuy')
            var $mbuyContent = $('#summary-mbuy')
            var url = $mBuy.attr('data-url')

            if (!r.prom) {
                $mBuy.html('')
                $mbuyContent.remove()
                return false
            } else {
                this.promCount++
            }

            var html1 =
                '\
            <div class="mob-buy J-prom-mbuy">\
                <em class="hl_red_bg">手机专享价</em>\
                <span class="hl_red J-m-price">&#12288;￥</span>\
                <span class="qrcode-wrap">\
                    <span class="hl_blue">&#12288;&#12288;去手机购买</span>\
                    <span class="icon"><s></s><b></b></span>\
                </span>\
            </div>'

            var html2 =
                '\
            <div id="summary-mbuy" class="hide">\
                <i></i>\
                <div class="qrcode">\
                    <div class="loading-style1"><b></b>加载中，请稍候...</div>\
                </div>\
            </div>'

            $mBuy.html(html1)

            if (!$mbuyContent.length) {
                $('.summary').eq(0).append(html2)
                $mbuyContent = $('#summary-mbuy')
            }

            var $mbuyTrigger = $('.J-prom-mbuy .qrcode-wrap')
            var current = 'mob-buy-curr'
            var timer = null
            var timer1 = null

            var $mPrice = $('.J-m-price')
            if (price) {
                $mPrice.html('￥' + price)
            } else {
                return $mBuy.html('')
            }

            $mbuyTrigger
                .add($mbuyContent)
                .unbind('mouseenter mouseover mouseleave mouseout')
            $mbuyTrigger.hover(
                function() {
                    clearTimeout(timer1)
                    $mbuyTrigger.parent('.J-prom-mbuy').addClass(current)
                    $mbuyContent.show()

                    var mbuyOffsetTop = $('.J-prom-mbuy').offset().top
                    var summaryOffsetTop = $('.summary').offset().top
                    var resultTop = mbuyOffsetTop - summaryOffsetTop + 36

                    $mbuyContent.css('top', resultTop)

                    if (!_this.qrcodeLoaded) {
                        _this.loadQrcode(url)
                    }

                    return false
                },
                function() {
                    timer = setTimeout(function() {
                        $mbuyTrigger.parent('.J-prom-mbuy').removeClass(current)
                        $mbuyContent.hide()
                    }, 100)
                }
            )
            $mbuyContent.hover(
                function() {
                    clearTimeout(timer)
                },
                function() {
                    timer1 = setTimeout(function() {
                        $mbuyTrigger.parent('.J-prom-mbuy').removeClass(current)
                        $mbuyContent.hide()
                    }, 100)
                }
            )
            this.setFoldLayer()
        },
        showPromotion: function() {
            var hasPromotion = this.promCount > 0 || Promotions.haveProAdv
            var hasMorePromotion = this.promCount > this.maxPromCount
            // 灰色高亮区域添加class
            var hasPromotionClassName = 'z-has-more-promotion'

            // 显示促销信息
            pageConfig.hasPromotion = hasPromotion;
            if (hasPromotion || pageConfig.hasCarGift) {
                this.$el.show()
            } else {
                this.$el.hide()
            }

            if (hasMorePromotion) {
                this.$darkBgEl.addClass(hasPromotionClassName)
            } else {
                this.$darkBgEl.removeClass(hasPromotionClassName)
            }
        },
        setFoldLayer: function() {
            var cName = 'z-promotions-all-show'
            var _this = this
            var $summaryPromotion = $('#summary-promotion')
            // 促销信息大于2条显示【查看】并添加 hover 弹出层效果
            if (_this.promCount > 2) {
                _this.$moreProm.css('visibility', 'visible')
                _this.$wrap
                    .bind('mouseenter', function() {
                        $summaryPromotion.addClass(cName)
                    })
                    .bind('mouseleave', function() {
                        $summaryPromotion.removeClass(cName)
                    })
            } else {
                _this.$moreProm.css('visibility', 'hidden')
                _this.$wrap.unbind('mouseenter mouseleave')
            }

            _this.showPromotion()
            _this.appendTagsToPromTags()
        },
        appendTagsToPromTags: function() {
            //促销标签提取
            var _this = this
            var $promTags = $('.J-more-prom-ins')
            if (!$promTags.length) {
                $promTags = $(
                    '<div class="J-more-prom-ins more-prom-ins"></div>'
                )
            } else {
                $promTags.html('')
            }
            var tags = _this.$wrap.find('.hl_red_bg'),
                len = tags.length,
                tagsHtml = ''

            if (len <= 2) {
                return
            }

            for (var i = 1; i < len; i++) {
                // 有两种标签：红色标签和黄色标签
                if ($(tags[i]).hasClass("yellow")) {
                    tagsHtml +=
                    '<em class="hl_red_bg yellow">' + $(tags[i]).html() + '</em>'
                } else {
                    tagsHtml +=
                    '<em class="hl_red_bg">' + $(tags[i]).html() + '</em>'
                }
            }
            $promTags.append(tagsHtml)

            var $target = tags.eq(0).parents('.prom-item')
            if ($target.length == 0) {
                $target = tags.eq(0).parents('ins')
            }
            $target.after($promTags)
        },
        clear: function() {
            $('#product-gifts,#prom,#product-prom-ext,#product-tips').remove()
            $(
                '#summary-promotion,#summary-promotion-extra,#summary-gifts,#summary-tips'
            ).hide()
        },
        /**
         * 由于接口变动，需要传递实时价格
         * @param {any} stock 接口返回的数据
         */
        setJdPrice: function (price) {
            if (price && price.p) {
                this.jdPrice = price.p;
            } else {
                this.jdPrice = '';
            }
        }
    }

    // 价格防刷
    var handlePDDOS = function(r, cfg) {
        // 调用成功重新请求价格接口, pdbp 传 1
        function reloadPrice() {
            Tools.priceNum({
                skus: [cfg.skuid],
                text: '{NUM}',
                pdbp: 1
            })
            $.closeDialog()
        }

        require.async('MOD_ROOT/common/verify/verify', function(Verify) {
            var verify = new Verify({
                onError: function() {
                    reloadPrice()
                },
                onComplete: function(res) {
                    if (res == 601) {
                        reloadPrice()
                    } else if (res == 602) {
                        this.showMessage('验证码错误!')
                    } else {
                        this.hideMessage()
                    }
                }
            })
        })
    }
    //获得商品实时价格促销预约预售秒杀闪购拼购拼团白条延保等数据
    // var getWareBusiness = function (cfg){
    //     $.ajax({
    //         url: '//item-soa.jd.com/getWareBusiness',
    //         cache: true,
    //         dataType: 'jsonp',
    //         data: {
    //             skuId: pageConfig.product.skuid + '',
    //             cat: pageConfig.product.cat.join(','),
    //             area: Tools.getAreaId().areaIds.join('_'),
    //             shopId: pageConfig.product.shopId,
    //             venderId: pageConfig.product.venderId+'',
    //             paramJson: pageConfig.product.paramJson,
    //             num: 1
    //         },
    //         success: function (data) {
    //             Promotions.isPlus = false;
    //             Promotions.isRealPlus = false;
    //             if(data && data.isPlusMember){
    //                 var plusMember = data.isPlusMember;
    //                 Promotions.isRealPlus = plusMember == "1"||plusMember=="2"?true:false;
    //                 Promotions.isPlus = plusMember == "1"||plusMember=="2"?true:false;
    //                 Promotions.isRealzxPlus = plusMember == "1"?true:false;
    //             }
    //             Promotions.isSam = (data && data.isSamMember)?true:false;
    //             Promotions.isLogin = (data && data.isLogin )?true:false;

    //             //if(data && data.price){
    //             if(data){
    //                 // Promotions.plusTags = data.price && data.price.plusTag;
    //                 //data.type = 'onWareBusinessReady'
    //                 // data.isRealPlus = Promotions.isRealPlus
    //                 // data.isRealzxPlus = Promotions.isRealzxPlus
    //                 // data.isPlus = Promotions.isPlus
    //                 // data.isSam = Promotions.isSam
    //                 // data.discount = data.discountPriceInfo
    //                 // 触发价格加载完成事件
    //                 Event.fire({
    //                     type: 'onWareBusinessReady',
    //                     isRealPlus: Promotions.isRealPlus,
    //                     isRealzxPlus: Promotions.isRealzxPlus,
    //                     isPlus: Promotions.isPlus,
    //                     isSam: Promotions.isSam,
    //                     discount: data.discountPriceInfo,
    //                     data: data
    //                 })
                    
    //             }

    //         }
    //     });
        

    // }
    // 主商品价格
    var getPrice = function(cfg) {
        //对应拼购拼团场景,融合后需删除
        //拼购信息接口,先调用拼购价格接口获得拼购价格,得不到的话降级重新调用价格接口
        // var getPinGouInfo = function () {
        //     var loc = readCookie('ipLoc-djd') || '1_0_0';
        //     loc = loc.replace(/-/g, '_');

        //     $.ajax({
        //         url:'//wq.jd.com/pingou_api/BatGetPingouInfo',
        //         cache: true,
        //         dataType: 'jsonp',
        //         data:{
        //             skuids:pageConfig.product.skuid + '' ,//要查询的sku id列表，必填, 最多只能填20个sku id
        //             platform:6, //平台号，1，手Q，2，微信，必填，pc 6
        //             origin:1, //开普勒需要用到的来源，pc1
        //             area:loc,
        //             source:'pc'  //店铺填jShop, PC拼购用pc
        //         },
        //         success:function (data) {
        //             //迁移到soa
        //             if(data.errmsg != 'OK!' || (data.pingou_info && data.pingou_info[0] && !data.pingou_info[0].isPriceValid)) {
        //                 resetPinGouInfo(cfg)
        //                 //降级用，当做普通商品
        //                 loadPrice();
        //                 return;
        //             }
        //             // var data = {
        //             //     "iRet": [0], // 错误码: 0表示全部成功 1:表示参数错误 30001表示获取拼购活动信息失败 30003表示获取拼购价失败
        //             //     "errmsg":"[OK!]",
        //             //     "pingou_info":[{
        //             //         "lefttime":36453,
        //             //         "product_tuan_price":"0",
        //             //         "s_product_describe":"",
        //             //         "s_short_name":"",
        //             //         "sku_id":"15934276939",
        //             //         "status":1,
        //             //         "tuan_member_count":2,
        //             //         "isPriceValid": 1, // 是否拼购产品: true是,false不是
        //             //         "bp":"9.80",
        //             //         "p":"19.80"
        //             //     }]
        //             // }
        //             pageConfig.product.pinGouInfo = data.pingou_info[0];
        //             pageConfig.product.jp = pageConfig.product.pinGouInfo.bp = Number(pageConfig.product.pinGouInfo.bp)? (Number(pageConfig.product.pinGouInfo.bp)).toFixed(2):'暂无报价'
        //             pageConfig.product.pinGouInfo.p = Number(pageConfig.product.pinGouInfo.p)? (Number(pageConfig.product.pinGouInfo.p)).toFixed(2):'暂无报价'
        //             pageConfig.product.skuMarkJson.pg = !!data.pingou_info[0].isPriceValid;
        //             var arr = {
        //                 p:data.pingou_info[0].p,
        //                 bp:data.pingou_info[0].bp,
        //                 m:'',
        //                 id:pageConfig.product.skuid + ''
        //             };
        //             // 触发价格加载完成事件
        //             Event.fire({
        //                 type: 'onPriceReady',
        //                 price: arr
        //             })

        //             // 触发拼购信息获取完成事件
        //             /*pageConfig.eventTarget.fire({
        //                 type: 'onPinGouGoodsInfoReady'
        //             });*/
        //         },
        //         error:function () {
        //             resetPinGouInfo(cfg)
        //             //降级用，当做普通商品
        //             loadPrice();

        //         }
        //     })
        // }

        function loadPrice(data) {
            var priceData = [];
            var data=data.stock && data.stock.data;
            if(data.pinGouInfo){
                pageConfig.product.skuMarkJson.pg = true
            }else{
                pageConfig.product.skuMarkJson.pg = false
            }
             //拼购的使用拼购信息接口，不用价格接口,秒杀和预售优先级高
            if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && data.pinGouInfo){
                //getPinGouInfo();
                var pinGouInfo={}
                pinGouInfo.bp= data && data.pinGouInfo &&data.pinGouInfo.bp //data.joinBuyInfo.bpPrice
                pinGouInfo.p = data && data.pinGouInfo && data.pinGouInfo.p
                pinGouInfo.tuan_member_count = data && data.pinGouInfo && data.pinGouInfo.tuanMemberCount//拼购人数
                pageConfig.product.pinGouInfo=pinGouInfo
                pageConfig.product.jp = data.price.bp
                // 触发拼购信息获取完成事件
                pageConfig.eventTarget.fire({
                    type: 'onPinGouGoodsInfoReady'
                });
                return;
            }
            if(data && data.price){
                data.price.id = 'J_' + cfg.skuid;
                priceData.push(data.price);
            }
            Tools.priceNum({
                $el: $('.J-summary-price, .addcart-mini'),
                skus: [cfg.skuid],
                text: '{NUM}',
                priceData:priceData,
                callback: function(sku, arr) {
                    if (arr) {
                        ///////////////////////////////////////
                        //  业务名： paipai分期用分期价展示 功能已下线
                        //  创建日期： 2017-10-25
                        //  创建人erp：  xiechong1
                        //  //cd.jd.com/getStaging?skuId={}&area={}&price={}&source=2&buyNum=1
                        //////////////////////////////////////

                        // p: 该skuid的前台京东价 有可能返回小于0的价格 很大程度上是商品下柜造成，请调用者在程序中做好处理;
                        // m: 该skuid的市场价，注：该价格可能返回小于0的价格，如遇小于0的价格请重试;
                        // op: 该skuid的后台京东价（由采销录入的原始价格）有可能返回小于0的价格;
                        // l: 该skuid的划线价。注：若无划线价则不显示该字段;
                        var p = arr.p
                        var m = arr.m
                        var op = arr.op
                        var l = arr.l;
                        var lytypeDesc = arr.lytypeDesc;
                        var jp = Number(p)
                        var mp = Number(m)
                        pageConfig.product.jp = jp
                        pageConfig.product.mp = mp

                        /**
                         * 划线价二期逻辑  lqf-1109
                         * 实时价格接口新增 l 字段，为划线价，若无划线价 则不显示此字段
                         * 图书 秒杀、拼购 逻辑不变，吊牌价删除 家具分类显示零售价暂保留使用原有逻辑
                         * 闪购有 l 字段 使用 l 字段，如没有使用 op 字段
                         */
                        // 如果商品有价格
                        if (jp > 0) {
                            //服务端是否输出控制显示
                            $('#summary-price .p-discount').html(
                                G.discount(jp, mp)
                            )

                            // 家具分类会显示零售价
                            $('#page_dpprice').html(
                                '￥' + parseFloat(mp).toFixed(2)
                            )

                            // 判断是否有划线价数据
                            // 如果有划线价数据 则先判断是否存在 秒杀 | 零售价 的划线价节点，当没有这些节点时才增加划线价
                            // 划线价 > jp 时才使用划线价
                            if (l && Number(l) > 0 && lytypeDesc) {
                                // 判断是否是 秒杀预告阶段 秒杀 或者 是家具分类产品 如果是则保持原有逻辑，不使用 l 字段划线价
                                // 因为在模板文件里有对这两者的判断，从而显示不同的html节点，所以这里使用对应的节点去判断是否是以上两类
                                // 当有 koBeginTime > 0 时为秒杀商品的预告阶段 不会显示划线价
                                // #page_origin_price 节点为秒杀划线价，#page_dpprice 节点为家具分类零售价
                                // #page_opprice 为闪购商品的划线价节点
                                // var isKoPre = pageConfig.koBeginTime > 0;
                                // var hasKoHxDom = $('#page_origin_price').length > 0;
                                // var hasDpHxDom = $('#page_dpprice').length > 0;
                                // var hasFsHxDom = $('#page_opprice').length > 0;

                                if (Number(l) !== jp && !pageConfig.product.isYuShou && !pageConfig.product.isPinGou) {
                                    // 划线价html 添加到主价格后面
                                    var hxPriceHtml = '<del id="page_hx_price">￥' + l + '</del><span id="J_JdContent">' + lytypeDesc + '</span>';
                                    // 防止重新选择地址后价格重新获取后再次出发此函数，导致没变换一次地址，就多出一个划线价。
                                    // if ($('#page_hx_price').length > 0) {
                                    //     $('#page_hx_price').text('￥' + l);
                                    // } else {
                                        $('#page_opprice').html(hxPriceHtml);
                                    // }

                                } 
                                // else if (hasFsHxDom) {
                                //     // 闪购商品划线价优先使用 l 字段价格 如无 l 则使用 op;
                                //     $("#page_opprice").before(lytypeDesc+' ')
                                //     $('#page_opprice').html(
                                //         '￥' + parseFloat(l).toFixed(2)
                                //     );
                                // }
                            }else {
                                $('.p-discount').hide()//目前只有图书有折扣，根据京东价/划线价得到。无划线价时不会有折扣。
                                // 闪购商品的划线价当没有 l 时 使用 op 字段
                                // if (op) {
                                //     $('#page_opprice').html(
                                //         '￥' + parseFloat(op).toFixed(2)
                                //     )
                                // }
                            }

                        }

                        try {
                            // 触发价格加载完成事件
                            Event.fire({
                                type: 'onPriceReady',
                                price: arr
                            })
                          } catch (error) {
                             console.log("error",error)    
                          }
                      }
                      
                   
                },
                onReady: function(r) {
                    if (r && r.error && r.error == 'pdos_captcha') {
                        handlePDDOS(r, cfg)
                    }
                }
            })
        }

        Event.addListener('onStockReady', loadPrice)
        // loadPrice()
    }

    // 通信币
    var setTXB = function(cfg) {
        try {
            if (!cfg.isFeeType) {
                return
            }
    
            if (/debug=txb/.test(location.href)) {
                cfg.proms = [
                    '<ins id="prom-bvalue"><div class="J-prom-bvalue"><em class="hl_red_bg">通信B</em><em class="hl_red">京东通信用户送97个通信B</em><a href="//sale.jd.com/act/mA8nbHJMU2.html" target="_blank">&nbsp;&nbsp;详情 <s class="s-arrow">&gt;&gt;</s></a></div></ins>'
                ]
            }
    
            var proms = cfg.proms
            var hasProms = proms && proms.length
            // 最后一个位置
            var $quan = $('#prom-quan')
    
            if (hasProms) {
                for (var i = 0; i < proms.length; i++) {
                    $quan.after(proms[i])
                }
            }
        } catch (error) {
            //烛龙上报
            Tools.getJmfe({}, error, "通信币异常", 300) 
        }
       
    }

    // 累计评价数
    var getCommentMeta = function(cfg) {
        $('.comment-count a').attr('href', '#none').bind('click', function(){
            require.async([
                "MOD_ROOT/lazyinit/lazyinit"
            ], function (lazyinit) {
                lazyinit.goToComment();
            });
        });

        Tools.commentMeta({
            skus: [cfg.skuid],
            $el: $('.summary-price-wrap'),
            text: '{NUM}',
            callback: function(sku, r) {
                cfg.commentMeta = r
                Event.fire({
                    type: 'onCommentMeta',
                    commentMeta: r
                })

                // 领导人书籍屏蔽评价tab和楼层
                if (r && (r.SensitiveBook == 1)) {
                    $('#comment-count,.comment-count').remove();
                    $('#detail .tab-main [data-anchor="#comment"]').remove();
                    return;
                }

                $('#detail .tab-main [data-anchor="#comment"] s')
                    .html('(' + r.CommentCountStr + ')')
                    .show()

                $('.summary-price .count')
                    .unbind('click')
                    .bind('click', function() {
                        $('body,html').scrollTop(
                            $('#comment').offset().top - 50
                        )
                        return false
                    })
            }
        })
    }

    // 购买指数
    var getBuyRate = function(cfg) {
        var $buyRate = $('#buy-rate')
        var $rateCount = $buyRate.find('.count')

        function setTips(data) {
            var detailTPL =
                '\
            {macro rankIcon(rk)}\
                <i class="sprite-{if rk>0}up{else}down{/if}"></i>\
            {/macro}\
            <div class="rate-layer">\
                <div class="layer-tit">\
                    <h3>评分详细</h3>\
                    <span>与同类对比</span>\
                </div>\
                <div class="layer-con">\
                    <div class="total">\
                        <span class="label">总分：</span>\
                        <span class="score"><a href="#">${xgzs|toFixed}</a></span>\
                        <span class="trend">${avg_xgzs|toFixed}% ${rankIcon(rank)} </span>\
                    </div>\
                    <ul class="score-list">\
                        {for item in detail}\
                        <li>\
                            <span class="label">${item.name}：</span>\
                            <span class="score">${item.value|toFixed}</span>\
                            <span class="trend">${item.avg|toFixed}% ${rankIcon(item.rank)} </span>\
                        </li>\
                        {/for}\
                    </ul>\
                    <dl class="tips">\
                        <dt>什么是选购指数</dt>\
                        <dd>京东与中国家用电器协会等共同对商品参数进行评估计算出选购指数。指数最高为10。</dd>\
                    </dl>\
                </div>\
            </div>'

            data._MODIFIERS = {
                toFixed: function(n, num) {
                    num = num || 2
                    return n.toFixed(num)
                }
            }
            $rateCount.ETooltips({
                close: false,
                content: detailTPL.process(data),
                width: 240,
                pos: 'bottom',
                zIndex: 10
            })
        }
        function setRate(item) {
            $rateCount.html(item.xgzs)
        }
        function getRate() {
            $.ajax({
                url: '//cd.jd.com/product/tag?skuIds=' + cfg.skuid,
                dataType: 'jsonp',
                success: function(r) {
                    if (r && r.items && r.items.length) {
                        var item = r.items[0]
                        setTips(item)
                        setRate(item)
                        $buyRate.show()
                    } else {
                        $buyRate.hide()
                    }
                }
            })
        }

        if ($buyRate.length) {
            getRate()
        }
    }

    function onStockReadyHandler() {
        var $yijia = $('.J-yijia a')
        if (!pageConfig.product.havestock && !G.itemDisabled) {
            $yijia.hide()
        } else {
            $yijia.show()
        }
    }

    // plus双价格
    function setPlusPrice(cfg) {
        var $el = $('.J-plus-price');
        try {
            function handlePrice(data) {
                //拼购不显示其他价格
                if (!pageConfig.product.isYuShou
                    && $('#banner-miaosha').length <= 0
                    && cfg.skuMarkJson
                    && cfg.skuMarkJson.pg
                ) {
                    return;
                }
                var doublePrice = data && data.price && data.price.doublePrice;
                if (doublePrice && doublePrice.up == '2' && doublePrice.price) {
                    if (doublePrice.price <= 0) {
                        var text = '暂无报价';
                    } else {
                        var text = '￥' + doublePrice.price;
                    }
                    $('.J-p-p-' + cfg.skuid).text(text);
                    $el.show();
                    // 如果是闪购活动并且拥有Plus价格，屏蔽划线价格
                    if ($('#banner-shangou').length > 0) { // 闪购活动判断标志
                        $('.summary-price.J-summary-price .dt').html('京 东 价');
                        $('.summary-price.J-summary-price .pricing').remove();
                    }
                } else {
                    $el.hide();
                }
            }
    
            // Event.addListener('onPriceReady', handlePrice);
    
            /**
             * 价格处 Plus icon 在非会员时增加点击跳转到plus页面 20180924-lqf
             */
            function handlePlusIconEnter () {
                // 如果 img.plus-icon 外层没有 <a> 父元素标签包裹时才执行此 <a> 标签包裹
                // 防止在更改选择地址时 stock 变化执行此方法 造成套多个 <a> 标签。
                var iconLinkLen = $el.find('a.plus-icon-linkwrap').length;
                if (iconLinkLen === 0) {
                    $el.find('img.plus-icon').wrap('<a class="plus-icon-linkwrap" clstag="shangpin|keycount|product|plusicon" href="//plus.jd.com/index" target="_blank"></a>');
                }
                // plus会员不展示“银牌及以上用户开通PLUS可享限时特惠”引导文案
                $(".J-plus-price .text").next("a").remove();
            };
    
            Event.addListener('onStockReady', function(res) {
                var data = res && res.stock && res.stock.data
                handlePrice(data)
                //通过返回数据isPlus判断是否是会员，来展示促销plus文案
                var isPlus = (data && data.stockInfo && data.stockInfo.isPlus) || {};
                if (!isPlus) {
                    handlePlusIconEnter();
                }
            });
        } catch (error) {
           //烛龙上报
           Tools.getJmfe({}, error, "plus双价格异常", 300) 
        }
        
    }

    function setPlusPrice2(cfg) {
        var $el = $('.J-plus-price');

        function handlePrice(data) {
            //拼购不显示其他价格
            if (!pageConfig.product.isYuShou
                && $('#banner-miaosha').length <= 0
                && cfg.skuMarkJson
                && cfg.skuMarkJson.pg
            ) {
                return;
            }

            var price = data.price;

            if ((price.up === 'pp' || price.up === 'tpp') && (price.pp || price.tpp)) {

                if (price.pp <= 0 || price.tpp <= 0) {
                    var text = '暂无报价';
                } else {
                    var text = '￥' + (price.pp || price.tpp);
                }

                $('.J-p-p-' + cfg.skuid).text(text);
                $el.show();

                // 如果是闪购活动并且拥有Plus价格，屏蔽划线价格
                if ($('#banner-shangou').length > 0) { // 闪购活动判断标志
                    $('.summary-price.J-summary-price .dt').html('京 东 价');
                    $('.summary-price.J-summary-price .pricing').remove();
                }

            } else {
                $el.hide();
            }
        }

        Event.addListener('onPriceReady', handlePrice);

        /**
         * 价格处 Plus icon 在非会员时增加点击跳转到plus页面 20180924-lqf
         */
        function handlePlusIconEnter () {
            // 如果 img.plus-icon 外层没有 <a> 父元素标签包裹时才执行此 <a> 标签包裹
            // 防止在更改选择地址时 stock 变化执行此方法 造成套多个 <a> 标签。
            var iconLinkLen = $el.find('a.plus-icon-linkwrap').length;
            if (iconLinkLen === 0) {
                $el.find('img.plus-icon').wrap('<a class="plus-icon-linkwrap" clstag="shangpin|keycount|product|plusicon" href="//plus.jd.com/index" target="_blank"></a>');
            }
            // plus会员不展示“银牌及以上用户开通PLUS可享限时特惠”引导文案
            $(".J-plus-price .text").next("a").remove();
        };

        Event.addListener('onStockReady', function(data) {
            //通过返回数据isPlus判断是否是会员，来展示促销plus文案
            var isPlus = (data && data.stock && data.stock.data && data.stock.data.stockInfo && data.stock.data.stockInfo.isPlus) || {};
            if (!isPlus) {
                handlePlusIconEnter();
            }
        });
    }

    //企业价双价格
    function setFirmPrice(cfg) {
        var $el = $('.J-firm-price');
        try {
            Event.addListener('onStockReady', function(res) {
                var data = res && res.stock && res.stock.data && res.stock.data
                //拼购不显示其他价格
                if (!pageConfig.product.isYuShou
                    && $('#banner-miaosha').length <= 0
                    && cfg.skuMarkJson
                    && cfg.skuMarkJson.pg
                ) {
                    return;
                }
                //通过返回数据jdPrice.up和jdPrice.epp
                var doublePrice = data.price && data.price.doublePrice;
                var corpHighInfo = data.corpHighInfo;// 高潜用户信息
                if (doublePrice && doublePrice.up == '15' && doublePrice.price) {
                    var text = ''
                    if (doublePrice.price <= 0 ) {
                        text = '暂无报价';
                    } else {
                        text = '￥' + doublePrice.price;
                    }
    
                    $('.J-p-f-' + cfg.skuid).text(text);
                    if(corpHighInfo && corpHighInfo.tip){
                        $("#highInfoTip").length == 0 && $el.append('<a id="highInfoTip" href="' + corpHighInfo.link + '" target="_blank" onclick=\'log("smb_pc","Productdetail_PCMemberclick",`{"Materialcode":"' + corpHighInfo.statContent + '"}`)\' style="color: #666;">' + corpHighInfo.tip + ' > </a>');
                        try {
                            expLogJSON('smb_pc', 'Productdetail_PCMember', '{"Materialcode":' + corpHighInfo.statContent + ',"sku": ' + cfg.skuid + '}')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log('曝光埋点错误');
                            }
                        }  
                    }
                    $el.show();
    
                    // 如果是闪购活动并且拥有Plus价格，屏蔽划线价格
                    if ($('#banner-shangou').length > 0) { // 闪购活动判断标志
                        $('.summary-price.J-summary-price .dt').html('京 东 价');
                        $('.summary-price.J-summary-price .pricing').remove();
                    }
                }else if(corpHighInfo && corpHighInfo.tip){
                    $el.html('<a href="' + corpHighInfo.link + '" target="_blank" onclick=\'log("smb_pc","Productdetail_PCMemberclick",`{"Materialcode":"' + corpHighInfo.statContent + '"}`)\' style="color: #666;">' + corpHighInfo.tip + ' > </a>');
                    
                    try {
                        expLogJSON('smb_pc', 'Productdetail_PCMember', '{"Materialcode":' + corpHighInfo.statContent + ',"sku": ' + cfg.skuid + '}')
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.log('曝光埋点错误');
                        }
                    } 
                    $el.show(); 
                }else{
                    $el.hide();
                }
            });
        } catch (error) {
           //烛龙上报
           Tools.getJmfe({}, error, "企业价双价格异常", 300) 
        }
        
    }

    function setFirmPrice2(cfg) {
        var $el = $('.J-firm-price');
        Event.addListener('onStockReady', function(res) {
            var data = res && res.stock && res.stock.data && res.stock.data
            //拼购不显示其他价格
            if (!pageConfig.product.isYuShou
                && $('#banner-miaosha').length <= 0
                && cfg.skuMarkJson
                && cfg.skuMarkJson.pg
            ) {
                return;
            }
            //通过返回数据jdPrice.up和jdPrice.epp
            var price = data.price;
            var corpHighInfo = data.corpHighInfo;// 高潜用户信息
            if (price.up=="epp" && price.epp) {
                var text = ''
                if (price.epp <= 0 ) {
                    text = '暂无报价';
                } else {
                    text = '￥' + price.epp;
                }

                $('.J-p-f-' + cfg.skuid).text(text);
                if(corpHighInfo){
                    $("#highInfoTip").length == 0 && $el.append('<a id="highInfoTip" href="' + corpHighInfo.link + '" target="_blank" onclick=\'log("smb_pc","Productdetail_PCMemberclick",`{"Materialcode":"' + corpHighInfo.statContent + '"}`)\' style="color: #666;">' + corpHighInfo.tip + ' > </a>');
                    try {
                        expLogJSON('smb_pc', 'Productdetail_PCMember', '{"Materialcode":' + corpHighInfo.statContent + ',"sku": ' + cfg.skuid + '}')
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.log('曝光埋点错误');
                        }
                    }  
                }
                $el.show();

                // 如果是闪购活动并且拥有Plus价格，屏蔽划线价格
                if ($('#banner-shangou').length > 0) { // 闪购活动判断标志
                    $('.summary-price.J-summary-price .dt').html('京 东 价');
                    $('.summary-price.J-summary-price .pricing').remove();
                }
            }else if(corpHighInfo){
                $el.html('<a href="' + corpHighInfo.link + '" target="_blank" onclick=\'log("smb_pc","Productdetail_PCMemberclick",`{"Materialcode":"' + corpHighInfo.statContent + '"}`)\' style="color: #666;">' + corpHighInfo.tip + ' > </a>');
                
                try {
                    expLogJSON('smb_pc', 'Productdetail_PCMember', '{"Materialcode":' + corpHighInfo.statContent + ',"sku": ' + cfg.skuid + '}')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('曝光埋点错误');
                    }
                } 
                $el.show(); 
            }else{
                $el.hide();
            }
        });
    }

    //企业团购弹层
    function setFirmbuyPrice(cfg) {
        var $el = $('.J-firmbuy-price');
        try {
            Event.addListener('onStockReady', function(res) {
                var data = res && res.stock && res.stock.data && res.stock.data
                var corpPromotion = data && data.corpPromotion
                if (corpPromotion && corpPromotion.corpGroupActivity) {
                    var detailTPL =
                    '\
                    <div class="buy-tips">\
                        <div class="arrow"></div>\
                        <div class="buy-content">\
                            <div class="left">\
                                <img src="${corpGroupActivity.img}" width="36" height="36">\
                            </div>\
                            {if corpGroupBuy.length == 1}\
                                <div style="background: linear-gradient(110deg, rgba(0, 85, 253, 0.15) 0.59%, rgba(0, 83, 253, 0.54) 49.41%, rgba(0, 83, 253, 0.15) 98.24%);width: 1px;height: 36px;float: left;margin-left: 10px;"></div>\
                            {/if}\
                            <div class="right">\
                                {if corpGroupBuy.length == 1}\
                                    <div class="buy-one">\
                                        <div class="discount">\
                                            满<span>${corpGroupBuy[0].needNum}</span>件·享<span>${corpGroupBuy[0].rebate}</span>折\
                                        </div>\
                                        <i class="question" title="${corpGroupActivity.minTip}"></i>\
                                        {if corpGroupActivity.link && corpGroupActivity.tip }\
                                            <div class="go-bargaining">\
                                                <a href="${corpGroupActivity.link}" onclick=\'log("smb_pc","Productdetail_BulkorderBargain",`{"skuid":"' + cfg.skuid + '"}`)\'>${corpGroupActivity.tip}></a>\
                                            </div>\
                                        {/if}\
                                    </div>\
                                {/if}\
                                {if corpGroupBuy.length > 1}\
                                    <div class="buy-multiple">\
                                        <div class="top">\
                                            <div class="num">数量</div>\
                                            <div class="discount">\
                                            {for item in corpGroupBuy}\
                                                <span>≥${item.needNum}</span>\
                                            {/for}\
                                            </div>\
                                            {if corpGroupActivity.link && corpGroupActivity.tip }\
                                                <div class="more">更多</div>\
                                            {/if}\
                                        </div>\
                                        <div style="background:linear-gradient(90deg, rgba(0, 85, 253, 0.15) 0%, rgba(0, 83, 253, 0.54) 50%, rgba(0, 83, 253, 0.15) 100%);height: 1px;"></div>\
                                        <div class="bottom">\
                                            <div class="num">折扣<i class="question" title="${corpGroupActivity.minTip}"></i></div>\
                                            <div class="discount">\
                                                {for item in corpGroupBuy}\
                                                    <span><i>${item.rebate}</i>折</span>\
                                                {/for}\
                                            </div>\
                                            {if corpGroupActivity.link && corpGroupActivity.tip }\
                                                <div class="go-bargaining">\
                                                    <a href="${corpGroupActivity.link}"  onclick=\'log("smb_pc","Productdetail_BulkorderBargain",`{"skuid":"' + cfg.skuid + '"}`)\'>${corpGroupActivity.tip}></a>\
                                                </div>\
                                            {/if}\
                                        </div>\
                                    </div>\
                                {/if}\
                            </div>\
                        </div>\
                    </div>'
                    $el.html(detailTPL.process(corpPromotion))
                    // 商详企业团购楼层曝光埋点
                    try {
                        expLogJSON('smb_pc', 'Productdetail_BulkorderExpo', '{"skuid":' + cfg.skuid + '}')
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.log('商详企业团购楼层曝光埋点',e);
                        }
                    } 
                    //商详企业团购议价入口曝光
                    if(corpPromotion.corpGroupActivity.link && corpPromotion.corpGroupActivity.tip){
                        try {
                            expLogJSON('smb_pc', 'Productdetail_BulkorderBargainExpo', '{"skuid":' + cfg.skuid + '}')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log('商详企业团购议价入口曝光',e);
                            }
                        }
                    }
                    $el.show();
                }else{
                    $el.hide();
                }
            });
        } catch (error) {
            //烛龙上报
            Tools.getJmfe({}, error, "企业团购弹层异常", 300) 
        }
       
    }
    
    //是否屏蔽自营标
    function setSkuStrategy(cfg) {
        var $el = $('#selfIcon');
        Event.addListener('onStockReady', function(res) {
            var data = res && res.stock && res.stock.data && res.stock.data
            var isSkuStrategy = data.skuStrategy
            if(isSkuStrategy == "Y")//Y标识屏蔽自营标
            {
                $el.hide()
            }else if(isSkuStrategy == "N"){//N标识展示自营标
                $el.show()
            } 
        });
    }

    //设置拍拍二手价格
    function setPaiPaiPrice(cfg) {
        var $el = $('.J-summary-price');
        var paipaiTPL =
                '\
                {if price.spnp}\
                <div class="paipai-price J-paipai-priceOld">\
                    <div class="dt">${price.spnpText}</div>\
                        <div class="dd">\
                            <span class="p-price">\
                                <span>￥</span>\
                                <span class="price J-p-'+cfg.skuid+'">${price.spnp}</span>\
                            </span>\
                        <span class="pricing" id="page_opprice"></span>\
                        <a class="notice J-notify-sale" data-type="1" data-sku="'+cfg.skuid+'" href="#none" clstag="shangpin|keycount|product|jiangjia_2">降价通知</a>\
                    </div>\
                </div>\
                {/if}\
                {if price.spp}\
                <div class="paipai-price J-paipai-priceNew">\
                    <div class="dt">全新京东价</div>\
                    <div class="dd">\
                        <span class="p-price-color">\
                            <span>￥</span>\
                            <span class="price J-p-'+cfg.skuid+'">${price.spp}</span>\
                        </span>\
                        <div class="price-tips">\
                            <a href="#none"><i class="sprite-question"></i></a>\
                            <div class="tips">\
                                <div class="sprite-arrow"></div>\
                                <div class="content">\
                                    <dl>\
                                    <dd>全新京东价仅供参考，该价格可能是备件库商品对应的全新商品在京东平台上正在展示或曾经展示过的京东价、划线价、吊牌价、品牌专柜标价、供应商提供的建议零售价、指导价等，并非原价，且由于地区、时间的差异性和市场行情波动，该参考价可能会与您购物时全新商品页面实际展示的不一致，仅供您参考。</dd>\
                                    </dl>\
                                </div>\
                            </div>\
                        </div>\
                        <a class="notice" target="_blank" href="//item.jd.com/${wareInfo.wareInfoMap.bjkglxp}.html">\
                            全新商品 >\
                        </a>\
                    </div>\
                </div>{/if}'
                
                
            Event.addListener('onStockReady', function(res) {
                var data = res && res.stock && res.stock.data && res.stock.data
                var isBjk = data && data.wareInfo && data.wareInfo.wareInfoMap && data.wareInfo.wareInfoMap.UsedSPLX
                var spp = data && data.price && data.price.spp //全新京东价价格
                var spnp = data && data.price && data.price.spnp //几成新价格
                if(isBjk == 2){ //首先得命中备件库,UsedSPLX标识等于2
                    if(spnp){
                        $el.after(paipaiTPL.process(data))
                        $el.remove()  
                    }
                    if(spp){
                        var timeoutId = 0
                        $(".J-paipai-priceNew").delegate('.price-tips', 'mouseenter', function() {
                            clearTimeout(timeoutId)
                            $(this).addClass('hover')
                        })

                        $(".J-paipai-priceNew").delegate('.price-tips', 'mouseleave', function() {
                            var $this = $(this)
                            timeoutId = setTimeout(function() {
                                $this.removeClass('hover')
                            }, 300)
                        }); 
                    }
                }
            });
    }

    // 重逢双价价
    function setMeetPrice(cfg) {
        var $el = $('.J-meet-price');
        try {
            Event.addListener('onStockReady', function(res) {
                var data = res && res.stock && res.stock.data && res.stock.data
                //拼购不显示其他价格
                if (!pageConfig.product.isYuShou
                    && $('#banner-miaosha').length <= 0
                    && cfg.skuMarkJson
                    && cfg.skuMarkJson.pg
                ) {
                    return;
                }
                //通过返回数据jdPrice.up和jdPrice.epp
                // var jdPrice = (data && data.stock && data.stock.stock && data.stock.stock.jdPrice) || false;
                var doublePrice = data && data.price && data.price.doublePrice;
                if (doublePrice && doublePrice.up == '17' && doublePrice.price) {
                    if (doublePrice.price <= 0 ) {
                        var text = '暂无报价';
                    } else {
                        var text = '￥' + doublePrice.price;
                    }
                    $('.J-p-f-' + cfg.skuid).text(text);
                    $el.show();
                }else{
                    $el.hide();
                }
            });
        } catch (error) {
            //烛龙上报
            Tools.getJmfe({}, error, "重逢双价价异常", 300) 
        }
        
    }

    function setMeetPrice2(cfg) {
        var $el = $('.J-meet-price');

        Event.addListener('onStockReady', function(data) {
            //拼购不显示其他价格
            if (!pageConfig.product.isYuShou
                && $('#banner-miaosha').length <= 0
                && cfg.skuMarkJson
                && cfg.skuMarkJson.pg
            ) {
                return;
            }
            //通过返回数据jdPrice.up和jdPrice.epp
            var jdPrice = (data && data.stock && data.stock.stock && data.stock.stock.jdPrice) || false;
            if (jdPrice.up=="rup" && jdPrice.rup) {
                if (jdPrice.rup <= 0 ) {
                    var text = '暂无报价';
                } else {
                    var text = '￥' + jdPrice.rup;
                }
                $('.J-p-f-' + cfg.skuid).text(text);
                $el.show();
            }else{
                $el.hide();
            }
        });
    }


    // Sam双价格
    function setSamPrice(cfg) {
        var $el = $('.J-sam-price')
        try {
            function setTips() {
                var content =
                    '山姆会员店是沃尔玛旗下的高端会员制商店，将山姆会员卡与京东账号绑定后，即可在山姆会员商店官方旗舰店享受会员价购买商品'
                if ($el.find('i').length) {
                    $el.find('i').ETooltips({
                        close: false,
                        content: content,
                        width: 230,
                        pos: 'bottom',
                        zIndex: 10
                    })
                }
            }
            
            function handlePrice(data) {
                //拼购不显示其他价格
                if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && cfg.skuMarkJson && cfg.skuMarkJson.pg) return
    
                var doublePrice = data.price && data.price.doublePrice 
    
                if (doublePrice && doublePrice.up === '4' && doublePrice.price && !isNaN(doublePrice.price)) {
                    var text = '￥' + doublePrice.price
                    if (doublePrice.price <= 0) text = '暂无报价'
    
                    $('.J-p-s-' + cfg.skuid).text(text)
                    setTips()
                    $el.show()
                } else {
                    $el.hide()
                }
            }
    
            Event.addListener('onPriceReady', handlePrice)
        } catch (error) {
           //烛龙上报
           Tools.getJmfe({}, error, "Sam双价格异常", 300) 
        }
        
    }

    function setSamPrice2(cfg) {
        var $el = $('.J-sam-price')

        function setTips() {
            var content =
                '山姆会员店是沃尔玛旗下的高端会员制商店，将山姆会员卡与京东账号绑定后，即可在山姆会员商店官方旗舰店享受会员价购买商品'
            if ($el.find('i').length) {
                $el.find('i').ETooltips({
                    close: false,
                    content: content,
                    width: 230,
                    pos: 'bottom',
                    zIndex: 10
                })
            }
        }

        function handlePrice(data) {
            //拼购不显示其他价格
            if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && cfg.skuMarkJson && cfg.skuMarkJson.pg) return

            var price = data.price

            if (price.up === 'sp' && price.sp && !isNaN(price.sp)) {
                var text = '￥' + price.sp
                if (price.sp <= 0) text = '暂无报价'

                $('.J-p-s-' + cfg.skuid).text(text)
                setTips()
                $el.show()
            } else {
                $el.hide()
            }
        }

        Event.addListener('onPriceReady', handlePrice)
    }

    /**
     * 新人价
     * @param {Object} data
     */
    function setNewcomerPirce(data) {
        var __html = '<div class="newcomer-price" id="J_NewcomerPrice"> \
                <em>￥{0}</em><i></i><span>新用户可享多次新人价</span> \
            </div>';
        var data = data || {};
        if (data && data.up === '8' && data.price) {
            if ($('#J_NewcomerPrice').length > 0) {
                $('#J_NewcomerPrice').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.price));
        } else {
            $('#J_NewcomerPrice').remove();
        }
    }

    function setNewcomerPirce2(data) {
        var __html = '<div class="newcomer-price" id="J_NewcomerPrice"> \
                <em>￥{0}</em><i></i><span>新用户可享多次新人价</span> \
            </div>';
        var data = data || {};
        if (data.up === 'nup' && data.nup) {
            if ($('#J_NewcomerPrice').length > 0) {
                $('#J_NewcomerPrice').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.nup));
        } else {
            $('#J_NewcomerPrice').remove();
        }
    }


    /**
     * 首单新人价
     * @param {Object} data
     */
    function setFirstNewcomerPirce(data) {
        var __html = '<div class="newcomer-price" id="J_FirstNewcomerPirce"> \
                <em>￥{0}</em><i></i><span>仅限首单</span> \
            </div>';
        var data = data || {};
        if (data && data.up === '18' && data.price) {
            if ($('#J_FirstNewcomerPirce').length > 0) {
                $('#J_FirstNewcomerPirce').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.price));
        } else {
            $('#J_FirstNewcomerPirce').remove();
        }
    }

    function setFirstNewcomerPirce2(data) {
        var __html = '<div class="newcomer-price" id="J_FirstNewcomerPirce"> \
                <em>￥{0}</em><i></i><span>仅限首单</span> \
            </div>';
        var data = data || {};
        if (data.up === 'npp' && data.npp) {
            if ($('#J_FirstNewcomerPirce').length > 0) {
                $('#J_FirstNewcomerPirce').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.npp));
        } else {
            $('#J_FirstNewcomerPirce').remove();
        }
    }

     /**
     * 本地货币
     * @param {Object} data
     */
    function setbdhbPirce(data) {
        var __html = '<div class="bdhb-price" id="J_BdhbPirce"> \
                <em>{0}</em> \
            </div>';
        var data = data || {};
        if (data && data.foreignPriceText) {
            if ($('#J_BdhbPirce').length > 0) {
                $('#J_BdhbPirce').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.foreignPriceText));
        } else {
            $('#J_BdhbPirce').remove();
        }
    }
    

    /**
     * 企业新人价
     * @param {Object} data
     */
    function setEnterpriseNewcomerPirce(data) {
        var __html = '<div class="newcomer-price enterprise" id="J_EnterpriseNewcomerPirce"> \
                <em>￥{0}</em><i></i><span>企业新用户可享多次企业新人价</span> \
            </div>';
        var data = data || {};
        if (data && data.up === '20' && data.price) {
            if ($('#J_EnterpriseNewcomerPirce').length > 0) {
                $('#J_EnterpriseNewcomerPirce').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.price));
        } else {
            $('#J_EnterpriseNewcomerPirce').remove();
        }
    }

    function setEnterpriseNewcomerPirce2(data) {
        var __html = '<div class="newcomer-price enterprise" id="J_EnterpriseNewcomerPirce"> \
                <em>￥{0}</em><i></i><span>企业新用户可享多次企业新人价</span> \
            </div>';
        var data = data || {};
        if (data.up === 'nep' && data.nep) {
            if ($('#J_EnterpriseNewcomerPirce').length > 0) {
                $('#J_EnterpriseNewcomerPirce').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.nep));
        } else {
            $('#J_EnterpriseNewcomerPirce').remove();
        }
    }

    /**
     * 学生到手价（无到手价,只涉及双价格）
     * @param {Object} data
     */
    function setStudentPirce(data) {
        var __html = '<div class="student-price" id="J_StudentPrice"> \
                <em>￥{0}</em><i></i><span>仅限完成认证的京东校园学生会员可享受</span> \
            </div>';
        var data = data || {};
        // up === '9' 和 '10009'都会展示学生价 10009潜在学生价，但是前台只展示学生价，不能露出潜在二字
        if (data && data.price && (data.up === '9' || data.up === '10009')) {
            if ($('#J_StudentPrice').length > 0) {
                $('#J_StudentPrice').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.price));
        } else {
            $('#J_StudentPrice').remove();
        }
    }

    function setStudentPirce2(data) {
        var __html = '<div class="student-price" id="J_StudentPrice"> \
                <em>￥{0}</em><i></i><span>仅完成注册认证的京东校园用户可享受</span> \
            </div>';
        var data = data || {};
        if (data.up === 'sdp' && data.sdp) {
            if ($('#J_StudentPrice').length > 0) {
                $('#J_StudentPrice').remove();
            }
            $('.J-summary-price .dd').append(__html.format(data.sdp));
        } else {
            $('#J_StudentPrice').remove();
        }
    }

    /**
     * pingou price 拼购拼团场景
     * */
    function setPinGouPrice(cfg) {

        function setTips(title,id) {
            // var position = $(id).position()
            // var btnWidth = $(id).width();
            // var parentWidth = $(this).parent().width();
            // var qrcodeRight = parentWidth - position.left - btnWidth;
            // console.log("qrcodeRight",qrcodeRight)
            var content =
                '<div class="qrcode-pingou hide"><p>'+ title +'</p><img src="//qrimg.jd.com/'+ encodeURIComponent('https://wqs.jd.com/pingou/item.shtml?sku='+cfg.skuid +'&ptag=17009.11.1') +'-150-0-4-0.png" alt=""/></div>';
            if ($(id).length) {
                // $(id).ETooltips({
                //     close: false,
                //     content: content,
                //     width: 180,
                //     pos: 'top',
                //     zIndex: 10
                // })
                $(id).append(content)
                var timeoutId = 0
                $(id).mouseenter(function() {
                    clearTimeout(timeoutId)
                    $(".qrcode-pingou").show()
                })

                $(id).mouseleave(function() {
                    // var $this = $(this)
                    timeoutId = setTimeout(function() {
                        $(".qrcode-pingou").hide()
                    }, 300)
                }); 
            }
        }
        

        function handlePrice(data) {
            var data = data && data.stock && data.stock.data
            //拼购不显示其他价格
            if(cfg.isYuShou || $('#banner-miaosha').length > 0  || !data.pinGouInfo) return;

            $('.addcart-mini').hide()
            var pinGouInfo = data.pinGouInfo
            var isjx = data.jingXiInfo &&  data.jingXiInfo.jx
            //拼价
            $('.J-p-'+ cfg.skuid).text(pinGouInfo.bp)
            //拼价按钮
            if($('.J-p-b-' + cfg.skuid).length <= 0){
                $('#InitCartUrl').data('label',$('#InitCartUrl').html())
                var carBtn = ($('#InitCartUrl').length ? $('#InitCartUrl') : $('#btn-reservation'));
                carBtn.before('<a href="javascript:;" class="btn-special1 btn-lg btn-pingou J-p-b-' + cfg.skuid +'">拼团价<span></span><span></span></a>');
                setTips('手机扫码开团','.J-p-b-' + cfg.skuid +'')
                $('.btn-pingou').hover(function() { // button 区域有 overflow: hidden 样式，hover 时取消 hidden，离开时恢复
                    $('.J_choose_btn').css('overflow', 'visible')
                }, function() {
                    if ($('.more-btns').length && $('.more-btns').css('display') == 'flex') return // 合并按钮打开时
                    $('.J_choose_btn').css('overflow', 'hidden')
                })
            }
            $('.J-p-b-' + cfg.skuid + ' span').eq(0).text('￥');
            $('.J-p-b-' + cfg.skuid + ' span').eq(1).text(pinGouInfo.bp);
            // 曝光
            var serviceskuid = Tools.getServiceSkuid(data.warrantyInfo)
            Tools.otherBtnExpo($('.J-p-b-' + cfg.skuid).text(), serviceskuid)
            // 埋点
            $('.J-p-b-' + cfg.skuid).click(function() {
                var selectedSkuid = Tools.getSelectedSkuid()
                Tools.otherBtnClick($('.J-p-b-' + cfg.skuid).text(), selectedSkuid)
            })
            $('#yuyue-process').hide();
            //原价
            if(isjx || G.specialAttrs["isJxzy"] == '1'){
                $('#InitCartUrl').remove();
                $('.btn-pingou').after('<a href="javascript:;" id="InitCartUrl" class="btn-special2 btn-lg J-p-b-b-' + cfg.skuid + '">单独买<span></span><span></span></a>')
                setTips('手机扫码购买','.J-p-b-b-' + cfg.skuid +'')
            }else{
                $('#InitCartUrl').addClass('btn-pingou').html('单独买<span>￥</span><span>' + pinGouInfo.p + '</span>');
            }
            $('#InitCartUrl span').eq(0).text('￥');
            $('#InitCartUrl span').eq(1).text(pinGouInfo.p);

            // $('#page_origin_price, #page_opprice').parent().remove(); // 移除闪购或秒杀的遗留html标签

            if(!$('.J-o-'+ cfg.skuid).length){
                $('.summary-price .p-price').after('<span class="line-thro J-o-'+ cfg.skuid +'"></span>');
            }
            $('.J-o-' + cfg.skuid).text('￥'+ pinGouInfo.p);
            if($(".tuanMemberCount").length == 0){
                $('.summary-price .p-price').after('<span class="tuanMemberCount">'+ pinGouInfo.tuanMemberCount +'人拼</span>');
            }
            
            // $('.summary-price > .dt').html('<span>'+ pinGouInfo.tuanMemberCount +'</span>人  拼')
            
            
        }

        Event.addListener('onStockReady', handlePrice)
    }

    function resetPinGouInfo(cfg) {
        pageConfig.product.skuMarkJson.pg = false;
        $('#pingou-banner-new').remove();
        $('.summary-price > .dt').html('京 东 价');
        $('.J-o-'+ cfg.skuid).remove();
        $('#InitCartUrl').removeClass('btn-pingou').html($('#InitCartUrl').data('label') || '加入购物车');
        $('.line-thro').remove();
        $('.J-p-b-' + cfg.skuid).remove();
        $('.addcart-mini').show()
        $('#yuyue-process').show();
    }

    /// 粉丝双价格（后端评估已经下线）
    function fansPrice(cfg) {
        Event.addListener("onPriceReady", function(data) {
            var $el = $(".J-fans-price");
            var $price = $(".J-p-f-" + cfg.skuid, $el);
            var price = (data && data.price) || {};
            if (price.up === "sfp" && price.sfp) {
                $price.text(price.sfp > 0 ?
                    "￥" + price.sfp :
                    "暂无报价");
                $el.show();
            } else {
                $el.hide();
            }
        });
    }

    /// 粉丝价
    function fansPrice2(cfg) {
        Event.addListener("onPriceReady", function(data) {
            var $el = $(".J-fans-price");
            var $price = $(".J-p-f-" + cfg.skuid, $el);
            var price = (data && data.price) || {};
            if (price.up === "sfp" && price.sfp) {
                $price.text(price.sfp > 0 ?
                    "￥" + price.sfp :
                    "暂无报价");
                $el.show();
            } else {
                $el.hide();
            }
        });
    }

    // 专享价 专属双价格
    function setUserPrice(cfg) {
        try {
            var $el = $('.J-user-price')
            var isLogin = false
            var userPrice = null
    
            function setTips() {
                var content = '近期浏览、关注过该商品或关联商品的用户，有可能会被系统命中，被判断为该商品优惠价的专属用户。仅在商品展示“专属价”标签期间，用户能以低于京东价的专属价格购买该商品。'
                if ($el.find('i').length) {
                    $el.find('i').ETooltips({
                        close: false,
                        content: content,
                        width: 230,
                        pos: 'bottom',
                        zIndex: 10
                    })
                }
            }
    
            function handlePrice(price,data) {
                //拼购不显示其他价格
                if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && pageConfig.product.skuMarkJson && pageConfig.product.skuMarkJson.pg) return false;
                var text = '￥' + price.price
                if (price.price <= 0) text = '暂无报价'
    
                $('.J-p-s-' + cfg.skuid).text(text)
                setTips()
                $el.show()
            }
    
            function clear() {
                $el.hide()
            }
    
            Event.addListener('onStockReady', function (data) {
                var doublePrice = data && data.stock && data.stock.data && data.stock.data.price && data.stock.data.price.doublePrice
                if (doublePrice && doublePrice.up == '3' && doublePrice.price && !isNaN(doublePrice.price)) {
                    userPrice = doublePrice
                } else {
                    userPrice = null;
                }
                if (Promotions.isLogin && userPrice) {
                    handlePrice(userPrice)
                } else {
                    clear()
                }
            })
            Event.addListener('onLogin', function (data) {
                Promotions.isLogin = data.login
                if (data.login && userPrice) {
                    handlePrice(userPrice)
                } else {
                    clear()
                }
            })
        } catch (error) {
           //烛龙上报
           Tools.getJmfe({}, error, "专享价专属双价格异常", 300) 
        }
        
    }

    function setUserPrice2(cfg) {
        var $el = $('.J-user-price')
        var isLogin = false
        var userPrice = null

        function setTips() {
            var content = '近期浏览、关注过该商品或关联商品的用户，有可能会被系统命中，被判断为该商品优惠价的专属用户。仅在商品展示“专属价”标签期间，用户能以低于京东价的专属价格购买该商品。'
            if ($el.find('i').length) {
                $el.find('i').ETooltips({
                    close: false,
                    content: content,
                    width: 230,
                    pos: 'bottom',
                    zIndex: 10
                })
            }
        }

        function handlePrice(price,data) {
            //拼购不显示其他价格
            if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && pageConfig.product.skuMarkJson && pageConfig.product.skuMarkJson.pg) return false;
            var text = '￥' + price.tkp
            if (price.tkp <= 0) text = '暂无报价'

            $('.J-p-s-' + cfg.skuid).text(text)
            setTips()
            $el.show()
        }
        function clear() {
            $el.hide()
        }

        Event.addListener('onStockReady', function (data) {
            var price = data && data.stock && data.stock.data && data.stock.data.price
            if (price.up === 'tkp' && price.tkp && !isNaN(price.tkp)) {
                userPrice = price
            } else {
                userPrice = null;
            }
            if (Promotions.isLogin && userPrice) {
                handlePrice(userPrice)
            } else {
                clear()
            }
        })
        Event.addListener('onLogin', function (data) {
            Promotions.isLogin = data.login
            if (data.login && userPrice) {
                handlePrice(userPrice)
            } else {
                clear()
            }
        })
    }

    /**
     * 到手价需求---接入其他所有双价格
     */
    function setAllDoublePrice() {
        try {
            // var __html = '<div class="double-price" id="J_DoublePrice"> \
            //     <em>￥{0}</em><span>{1}</span> \
            // </div>';
             // 红色字到手价容器
             var __html1 = '<span class="finalPrice" id="J_FinalPrice"> \
             <span class="symbol">￥</span><span class="price">{0}</span> \
             <span class="priceContent">{1}</span> \
             <span class="priceNew">{2}</span> \
             <span class="priceNewContent">{3}</span> \
            </span>';
            // 蓝色字到手价容器
            var __html2 = '<span class="finalPrice finalPriceIcon finalPriceLs" id="J_FinalPrice"> \
                <span class="symbol">￥</span> \
                <span class="price">{0}</span> \
                <span class="priceContent"></span> \
            </span>';
            // plus到手价和山姆到手价容器
            var __html3 = '<span class="finalPrice finalPriceIcon" id="J_FinalPrice"> \
                <span class="symbol">￥</span> \
                <span class="price">{0}</span> \
                <span class="priceContent"></span> \
            </span>';
            Event.addListener('onStockReady', function(res) {
                try {
                    var data = res && res.stock && res.stock.data
                    var abData = data.abData
                    var corporatePriceLabel = abData && abData.enPlus_price && abData.enPlus_price.label // 企业价实验
                    var doublePrice = data && data.price && data.price.doublePrice
                    var ups = doublePrice &&  doublePrice.up // 内购价3_1、门店价13、进口新人价21、亲子价10、1号会员价19、店铺会员价14
                    var presalePrice = data && data.YuShouInfo && data.YuShouInfo.yuShouPrice && data.YuShouInfo.yuShouPrice.replace("¥","");
                    if (presalePrice == '' || presalePrice == '待发布') return // 预售待发布字段下屏蔽展示双价格
                    if(doublePrice && doublePrice.price){
                        // if(ups && ups === '4'){ // 山姆会员价
                        //     $('.J-summary-price .dd').prepend(__html2.format(doublePrice.price));
                        //     // $('#J_FinalPrice .priceContent').attr("style","background:url(//img30.360buyimg.com/da/jfs/t3256/92/1497886665/1562/5350edda/57ceb8eeN574b3cd0.png);width: 46px;height:16px")
                        // }else if(ups && ups === '2'){ // plus会员价
                        //     $('.J-summary-price .dd').prepend(__html2.format(doublePrice.price));
                        //     // $('#J_FinalPrice .priceContent').attr("style","background:url(//img10.360buyimg.com/da/jfs/t5731/317/890792506/848/391b9a15/59224a28N48552ed2.png);width: 40px;height:16px")
                        // }else 
                        if(ups && (ups === '15' || ups === '20') && corporatePriceLabel === 'pre'){ //企业价格
                            $('.J-summary-price .dd').prepend(__html2.format(doublePrice.price));
                            $('#J_FinalPrice .priceContent').attr("style","background:url(//img13.360buyimg.com/imagetools/jfs/t1/311865/6/85/2177/6821a1dbFb509a6fd/3834819f7fa64821.png);width: 73px;height:22px; background-size: 100%")
                        // }else if(ups && (ups === '9' || ups === '10009')){ //学生价格
                        //     $('.J-summary-price .dd').prepend(__html2.format(doublePrice.price));
                        //     // $('#J_FinalPrice .priceContent').attr("style","background:url(//img10.360buyimg.com/imagetools/jfs/t1/267311/7/6077/1137/6773c83eF6f835ad5/2a0b1dc0ef72e389.png);width: 46px;height:16px")
                        // }else if(ups && ups === '8' || ups && ups === '18'){ //新人价
                        //     $('.J-summary-price .dd').prepend(__html2.format(doublePrice.price));
                        //     // $('#J_FinalPrice .priceContent').attr("style","background:url(//img11.360buyimg.com/imagetools/jfs/t1/259135/23/6351/1656/6774ab44F26013bba/ba61a6e0094b7603.png);width: 44px;height:16px")
                        // }else if(ups && ups === '22'){ //企业新人价
                        //     $('.J-summary-price .dd').prepend(__html2.format(doublePrice.price));
                        //     // $('#J_FinalPrice .priceContent').attr("style","background:url(//img14.360buyimg.com/imagetools/jfs/t1/257701/33/6406/2092/6774ac37F7b8efd5f/11ff9ef79d71b5f1.png);width: 75px;height:16px")
                        }else{ // 红色字到手价
                            $('.J-summary-price .dd').prepend(__html1.format(doublePrice.price, doublePrice.priceContent, "", ""));
                            $('#J_FinalPrice .priceContent').addClass("priceContentText")
                        }
                       
                        $('.J-summary-price .dd').find(".p-price").addClass("jdPrice")
                        $(".qi-icon-linkwrap").addClass("qi-linkwarp-ys")
                    }

                    var isBjk = data && data.wareInfo && data.wareInfo.wareInfoMap && data.wareInfo.wareInfoMap.UsedSPLX
                    var spp = data && data.price && data.price.spp //全新京东价价格
                    var spnp = data && data.price && data.price.spnp //几成新价格
                    var spnpText = data && data.price && data.price.spnpText //几成新
                    if(isBjk == 2){ //首先得命中备件库,UsedSPLX标识等于2
                        if(spnp){
                            $('.J-summary-price .dd').prepend(__html1.format(spnp, spnpText,spp,"全新京东价"));
                            $('#J_FinalPrice .priceContent').addClass("priceContentText") 
                            $('.J-summary-price .dd').find(".p-price").hide()
                        }
                        // if(spp){
                        //     var timeoutId = 0
                        //     $(".J-paipai-priceNew").delegate('.price-tips', 'mouseenter', function() {
                        //         clearTimeout(timeoutId)
                        //         $(this).addClass('hover')
                        //     })

                        //     $(".J-paipai-priceNew").delegate('.price-tips', 'mouseleave', function() {
                        //         var $this = $(this)
                        //         timeoutId = setTimeout(function() {
                        //             $this.removeClass('hover')
                        //         }, 300)
                        //     }); 
                        // }
                    }
                    // if(ups && (ups === '3_1' || ups === '13' || ups === '21' || ups === '10' || ups === '19' || ups === '14' || ups === '20'|| ups === '8' || ups === '3' || ups === '18') ){
                    //     if (doublePrice.price && doublePrice.priceContent) {
                    //         if ($('#J_DoublePrice').length > 0) {
                    //             $('#J_DoublePrice').remove();
                    //         }
                    //         if(doublePrice.price){
                    //             $('.J-summary-price .dd').find(".p-price").append(__html.format(doublePrice.price,doublePrice.priceContent));
                    //         }
                    //     } else {
                    //         $('#J_DoublePrice').remove();
                    //     }
                    // }else if(ups && ups === '4'){ // 山姆会员价
                    //     if (doublePrice.price && doublePrice.priceContent) {
                    //         if ($('#J_DoublePrice').length > 0) {
                    //             $('#J_DoublePrice').remove();
                    //         }
                    //         if(doublePrice.price){
                    //             $('.J-summary-price .dd').find(".p-price").append(__html.format(doublePrice.price,'<img src="//img30.360buyimg.com/da/jfs/t3256/92/1497886665/1562/5350edda/57ceb8eeN574b3cd0.png" class="sam-icon">'));
                    //         }
                    //     } else {
                    //         $('#J_DoublePrice').remove();
                    //     }
                    // }else if(ups && ups === '2'){ // plus会员价
                    //     if (doublePrice.price && doublePrice.priceContent) {
                    //         if ($('#J_DoublePrice').length > 0) {
                    //             $('#J_DoublePrice').remove();
                    //         }
                    //         if(doublePrice.price){
                    //             $('.J-summary-price .dd').find(".p-price").append(__html.format(doublePrice.price,'<img src="//img10.360buyimg.com/da/jfs/t5731/317/890792506/848/391b9a15/59224a28N48552ed2.png" alt="plus" class="plus-icon">'));
                    //         }
                    //     } else {
                    //         $('#J_DoublePrice').remove();
                    //     }
                    // }else if(ups && ups === '15'){ //企业价格
                    //     if (doublePrice.price && doublePrice.priceContent) {
                    //         if ($('#J_DoublePrice').length > 0) {
                    //             $('#J_DoublePrice').remove();
                    //         }
                    //         if(doublePrice.price){
                    //             $('.J-summary-price .dd').find(".p-price").append(__html.format(doublePrice.price,'<img src="//img13.360buyimg.com/imagetools/jfs/t1/110281/5/12499/1441/5e97ccb1Ec6a0e0da/eb5c07ae3cb8647d.png" alt="企业价" class="firm-icon">'));
                    //         }
                    //     } else {
                    //         $('#J_DoublePrice').remove();
                    //     }
                    // }else if(ups && (ups === '9' || ups === '10009')){ //学生价格
                    //     if (doublePrice.price && doublePrice.priceContent) {
                    //         if ($('#J_DoublePrice').length > 0) {
                    //             $('#J_DoublePrice').remove();
                    //         }
                    //         if(doublePrice.price){
                    //             $('.J-summary-price .dd').find(".p-price").append(__html.format(doublePrice.price,'<img src="https://img10.360buyimg.com/imagetools/jfs/t1/267311/7/6077/1137/6773c83eF6f835ad5/2a0b1dc0ef72e389.png" alt="学生价" class="student-icon">'));
                    //         }
                    //     } else {
                    //         $('#J_DoublePrice').remove();
                    //     }
                    // }else {
                    //     $('#J_DoublePrice').remove();
                    // }
                } catch (error) {
                    //烛龙上报
                    Tools.getJmfe({}, error, "到手价需求接入其他所有双价格异常", 300) 
                }
            })  
        } catch (error) {
            //烛龙上报
            Tools.getJmfe({}, error, "统一双价格异常", 300) 
        }
          
    }

    /**
     * 到手价展示
     */
    function setFinalPrice(cfg) {
        try {
             // 红色字到手价容器
            var __html1 = '<span class="finalPrice" id="J_FinalPrice"> \
                 <span class="symbol">￥</span><span class="price">{0}</span> \
                 <span class="priceContent">{1}</span> \
            </span>';
            // 蓝色字到手价容器
            var __html2 = '<span class="finalPrice finalPriceLs" id="J_FinalPrice"> \
                <span class="symbol">￥</span> \
                <span class="price">{0}</span> \
                <span class="priceContent">{1}</span> \
            </span>';
            // plus到手价和山姆到手价容器
            var __html3 = '<span class="finalPrice finalPriceIcon" id="J_FinalPrice"> \
                <span class="symbol">￥</span><span class="price">{0}</span> \
                <span class="priceContent"></span> \
            </span>';

            // 日常价
            var __html4 = '<span class="dailyPrice" id="J_DailyPrice"> \
                <span class="symbol">￥</span><span class="price">{0}</span> \
                <span class="priceContent">日常价</span> \
            </span>';

            Event.addListener('onStockReady', function(data){
                // var finalPrice = data && data.stock && data.stock.data && data.stock.data.price && data.stock.data.price.finalPrice || {}// 到手价的对象
                var data = data && data.stock;
                var abData = data && data.data && data.data.abData
                var corporatePriceLabel = abData && abData.enPlus_price && abData.enPlus_price.label // 企业价实验
                var finalPrice = data && data.data && data.data.price && data.data.price.finalPrice || {}// 到手价的对象
                var regularPrice = data && data.data && data.data.price && data.data.price.regularPrice || ""// 日常价
                var bybtInfo =  data && data.data && data.data.bybtInfo// 百亿补贴对象
                var bybtInfoBybt = bybtInfo && bybtInfo.bybt;//百亿补贴标识
                var miaoshaInfoObj = data && data.data && data.data.miaoshaInfo;// 秒杀对象

                var darkMoonClothInfo =data && data.data && data.data.darkMoonClothInfo;//月黑风高服饰5折对象
                var bannerDarkMoonClothUrl = darkMoonClothInfo && darkMoonClothInfo.beltBannerUrl;//月黑风高服饰5折腰带链接

                if((miaoshaInfoObj && miaoshaInfoObj.state == 2) || bybtInfoBybt){ // 秒杀(state=2是秒杀期 state=1是预告期)或者百亿补贴
                    if ($('#J_DailyPrice').length > 0) { // 日常价容器
                        $('#J_DailyPrice').remove();
                    }
                    if ($('#J_JdContent').length > 0) { // 补贴价容器
                        $('#J_JdContent').remove();
                    } 
                    if(regularPrice){
                        $('.J-summary-price .dd').find(".p-price").after(__html4.format(regularPrice));
                        $('.J-summary-price .p-price').show()
                    }
                    if(bybtInfoBybt){// 百亿补贴和秒杀价格后文案
                        $('.J-summary-price .dd').find(".p-price").append('<span id="J_JdContent" class="priceContentText">补贴价</span>')
                    }else if(miaoshaInfoObj && miaoshaInfoObj.state == 2){
                        $('.J-summary-price .dd').find(".p-price").append('<span id="J_JdContent" class="priceContentText">秒杀价</span>')
                    }
                    
                    $('.J-summary-price .dd').find(".p-price").addClass("msbtPrice")
                    $('.summary-price.J-summary-price .dt').html('价格');
                }

                if(finalPrice && finalPrice.up){
                    if (finalPrice && finalPrice.price && finalPrice.priceContent) {
                        if ($('#J_FinalPrice').length > 0) { // 到手价容器
                            $('#J_FinalPrice').remove();
                        }
                        if ($('#J_JdContent').length > 0) { // 京东价容器
                            $('#J_JdContent').remove();
                        }
                        if(finalPrice.up == "2"){ // plus到手价
                            $('.J-summary-price .dd').prepend(__html3.format(finalPrice.price));
                            $('#J_FinalPrice .priceContent').attr("style","background:url(https://img11.360buyimg.com/imagetools/jfs/t1/258582/12/5200/2072/67714648F4c4178cb/e7e4c0c6fc38b558.png);background-size: 100%;width:82px;height:22px")
                        }else if(finalPrice.up == "4"){ // 山姆到手价
                            $('.J-summary-price .dd').prepend(__html3.format(finalPrice.price));
                            $('#J_FinalPrice .priceContent').attr("style","background:url(https://img10.360buyimg.com/imagetools/jfs/t1/254939/4/6212/2494/67714648Fc2dfd269/5d8c30218efaeb42.png);background-size: 100%;width:84px;height:22px")
                        }
                        else if((finalPrice.up == "15" || finalPrice.up == "20") && corporatePriceLabel === 'pre'){ // 蓝色字到手价\企业到手价（15）\企业新人到手价（20）
                            $('.J-summary-price .dd').prepend(__html2.format(finalPrice.price,finalPrice.priceContent));
                            $('#J_FinalPrice .priceContent').addClass("priceContentText")
                        }
                        else{ // 红色字到手价
                            $('.J-summary-price .dd').prepend(__html1.format(finalPrice.price,finalPrice.priceContent));
                            $('#J_FinalPrice .priceContent').addClass("priceContentText")
                        }
                       
                        $('.J-summary-price .dd').find(".p-price").addClass("jdPrice")
                        // $('.summary-price.J-summary-price .dt').html('价　　格');
                        if(regularPrice){
                            $('.J-summary-price .p-price').hide()
                        }else{
                            if(bybtInfoBybt){// 百亿补贴和秒杀价格后文案
                                $('.J-summary-price .dd').find(".p-price").append('<span id="J_JdContent">补贴价</span>')
                            }else if(miaoshaInfoObj && miaoshaInfoObj.state == 2){
                                $('.J-summary-price .dd').find(".p-price").append('<span id="J_JdContent">秒杀价</span>')
                            }else{
                                // $('.J-summary-price .dd').find(".p-price").append('<span id="J_JdContent">京东价</span>')
                                $('.J-summary-price .dd').find(".p-price>.price").css("text-decoration","line-through")
                                $("#J_DailyPrice").remove();// 命中到手价 替换京东价容器 不需要展示
                            }
                        }
                        
                        if(bannerDarkMoonClothUrl && bannerDarkMoonClothUrl.length > 0){// 命中月黑风高服饰5折腰带，京东价增加划线样式
                            $('.J-summary-price .dd').find(".p-price>.price").css("text-decoration","line-through")
                        }
                        pageConfig.product.fPrice = finalPrice.price // 全局设置主品到手价给人气配件等楼层使用
                        $(".qi-icon-linkwrap").addClass("qi-linkwarp-ys")
                       
                    } else {
                        $('#J_FinalPrice').remove();
                        $('.J-summary-price .dd').find(".p-price").removeClass("jdPrice")
                        // $(".qi-icon-linkwrap").addClass("qi-linkwarp-ys")
                    }
                }else {
                    $('#J_FinalPrice').remove();
                    $('.J-summary-price .dd').find(".p-price").removeClass("jdPrice")
                    $('.p-price').find('.price').css("text-decoration","none")
                    // $(".qi-icon-linkwrap").addClass("qi-linkwarp-ys")
                }
            }) 
        } catch (error) {
            //烛龙上报
            Tools.getJmfe({}, error, "到手价展示异常", 300) 
        }
           
    }

    /**
     * 埋点统一上报
     */
    function setProductdetailLog(data) {
        // var bybtInfoBybt = data.bybtInfo && data.bybtInfo.bybt || false //百亿补贴标识
        // var govSupport = data.govSupportInfo && data.govSupportInfo.govSupport  || false// 国补促销标识，true 国补，false：非国补
        // var nowBuyAb = data.nowBuyAb || true // 全流程支持立即购买开关和实验标识
        var isStock = data.stockInfo && data.stockInfo.isStock // 是否有货

        // 价格区上报价格参数
        exposure({
            functionName: 'Productdetail_priceExpo',
            exposureData: ['mainskuid', 'price'],
            errorTips: '价格区曝光埋点错误'
        })

        // 商详PC页面曝光埋点
        exposure({
            functionName: 'Productdetail_pageExpo',
            exposureData: ['mainskuid', 'touchstone_expids', 'type', 'from_page', 'SgroupId'],
            errorTips: '商详PC页面曝光埋点错误'
        })
        // 加入购物车曝光埋点
        // console.log('加入购物车曝光', bybtInfoBybt , govSupport , nowBuyAb);
        // if(bybtInfoBybt || govSupport || nowBuyAb){// 百补\国补\加入购物车曝光
        var carHref = $("#InitCartUrl").attr("href")// 获取加入购物车原链接地址
        var regex = /cart\.jd\.com\/gate\.action/;// 匹配原主流程加车链接
        var carName = $("#InitCartUrl").html()// 获取加入购物车名称
        var $InitTradeUrl =  $("#InitTradeUrl") //立即购买容器
        if(regex.test(carHref) && carName == "加入购物车" && !$("#InitCartUrl").hasClass("btn-disable")){
            // 购物车曝光埋点
            exposure({
                functionName: 'Productdetail_addcartExpo',
                exposureData: ['mainskuid', 'touchstone_expids', 'type', 'from_page', 'SgroupId'],
                extraData: {
                    servicesku: Tools.getServiceSkuid(data.warrantyInfo)
                },
                errorTips: '加入购物车曝光埋点错误'
            })
            if(isStock){ // 有库存
                if($InitTradeUrl.length > 0){
                    // 立即购买曝光埋点
                    exposure({
                        functionName: 'Productdetail_NowBuyExpo',
                        exposureData: ['mainskuid', 'touchstone_expids', 'type'],
                        errorTips: '立即购买曝光埋点错误'
                    })
                }
            }
        }
        // 点击购物车增加到手价上报埋点
        // $('#InitCartUrl,#InitCartUrl-mini').click(function () {
            // var num = $('#buy-num').val() || "1"
            // landmine({
            //     functionName: 'Productdetail_addcart',
            //     exposureData: ['mainskuid', 'touchstone_expids', 'type', 'from_page', 'SgroupId', 'price'],
            //     extraData: {
            //         addcart_num: num
            //     },
            //     errorTips: '点击购物车埋点错误'
            // })
        // });
           
    }

    

    /**
     * 秒杀闪购新接口
     */
    function getSeckillFlash(data){
        
        if( $('#banner-miaosha').length > 0  || $('#banner-shangou').length > 0 ) {
            
            setBannerNew(data)
        }      
    }

    /**
     * 接入腰带系统 优先级：秒杀月黑风高>百补美妆>百补腰带>plus店铺腰带>plus用户腰带>预售>预约>秒杀>闪购>便宜包邮>月黑风高服饰5折>大促腰带>企业专属
     */
    function getBeltBanner (){
        Event.addListener('onStockReady', function(data){
            var data = data.stock || {};
            var beltBannerInfo = data.data && data.data.beltBannerInfo // 通用腰带数据
            var countDownDarkMoonCloth = beltBannerInfo && beltBannerInfo.countDown;//通用倒计时
            var bannerLeftIcon = beltBannerInfo && beltBannerInfo.bannerLeftIcon;// 右侧按钮
            var bannerRightText = beltBannerInfo && beltBannerInfo.bannerRightText;// 右侧文案
            var leftArrowIcon = beltBannerInfo && beltBannerInfo.leftArrowIcon;//  左侧icon箭头
            var countdownTextColor = beltBannerInfo && beltBannerInfo.countdownTextColor;// 倒计时颜色
            var middleIcon = beltBannerInfo && beltBannerInfo.middleIcon;//  中间闪电icon
            var bannerRightTextColor = beltBannerInfo && beltBannerInfo.bannerRightTextColor;// 右侧文案颜色
            var leftIconLink = beltBannerInfo && beltBannerInfo.leftIconLink;// 点击跳转链接
            var beltType = beltBannerInfo && beltBannerInfo.type || "";// 埋点类型
            var abData = data.data && data.data.abData || {}; // 获取实验数据
            var govSupportInfo = data.data && data.data.govSupportInfo || {}; // 国补数据
            
            // 开关
            var govAb = data.data && data.data.ext && data.data.ext.govAb

            // 通用腰带
            if($('#common_banner').length > 0 && beltBannerInfo && beltBannerInfo.beltBannerUrl){ 
                // 左侧标题
                if($(".pay-icon-left").length > 0){
                    $(".pay-icon-left").remove()
                    // return false
                }

                // 左侧标题旁边icon
                if($(".leftArrowIcon").length > 0){
                    $(".leftArrowIcon").remove()
                    // return false
                }

                // 避免重复引入dom
                if($(".pay-icon-right").length > 0){
                    $(".pay-icon-right").remove()
                }

                // 右侧闪电icon
                if($(".pay-icon-right-Arrow").length > 0){
                    $(".pay-icon-right-Arrow").remove()
                }

                // 右侧倒计时信息
                if($(".left-message-info").length > 0){
                    $(".left-message-info").remove()
                }
                // if(beltType.length > 0){ // 命中腰带展示曝光埋点
                    exposure({
                        functionName: 'Productdetail_YaodaiExpo',
                        exposureData: ['mainskuid', 'belt_type'],
                        extraData: {
                            subsidytype: beltBannerInfo.subsidyType || "-100",
                            subsidyUserStatus:beltBannerInfo.subsidyUserStatus || "-100"
                        },
                        errorTips: '腰带曝光埋点错误'
                    })
                // }

                if(beltBannerInfo.beltBannerUrl && beltBannerInfo.beltBannerUrl.length > 0){
                    var baseStyle = "background:#fff url(" + beltBannerInfo.beltBannerUrl + ");background-size: cover;"
                    var style = $("#common_banner").css('position') == 'fixed' ? baseStyle + 'position: fixed' : baseStyle // 如果原先吸顶，页面刷新时（增减数量等）保持吸顶样式，防止页面抖动
                    $("#common_banner").attr("style", style)
                }

                // 中间icon
                if(middleIcon){
                    $('#common_banner').append("<span class='pay-icon-right-Arrow'><span class='pay-right-img'><img src=" + middleIcon + " height='43px'/></span></span>")
                }

                // 右侧文案
                if(middleIcon && bannerRightText && (!countDownDarkMoonCloth || countDownDarkMoonCloth == 0)){
                    // 右侧闪电icon
                    if($(".pay-icon-right-Arrow").length > 0){
                        $(".pay-icon-right-Arrow").remove()
                    }
                    $('#common_banner').append("<span class='pay-icon-right'><span class='pay-right-img'><img src=" + middleIcon + "  height='50px'/></span><span class='pay-right-text' style='color:" + bannerRightTextColor + "'>" + bannerRightText + "</span></span>")
                }

                // 左侧icon
                if(bannerLeftIcon){
                    if(leftIconLink && leftArrowIcon){
                        $('#common_banner').append("<span class='pay-icon-left'><img src=" + bannerLeftIcon + " height='20px'/></span><span class='leftArrowIcon'><img src=" + leftArrowIcon + " height='16px'/></span>")
                    } else{
                        $('#common_banner').append("<span class='pay-icon-left'><img src=" + bannerLeftIcon + " height='20px'/></span>")
                    }
                }

                // 当点击腰带支持跳转时，设置腰带鼠标形态
                if(leftIconLink){
                    $("#common_banner").css({
                        cursor: 'pointer'
                    })
                }

                // hit=1：代表需要调用资格领取弹层; hit=0：取消调用弹层，维持跳转链接逻辑
                var gbHit = abData
                    && abData.gov_iframe
                    && abData.gov_iframe.keyParamMap
                    && abData.gov_iframe.keyParamMap.hit;

                var noSubsidyUrl = govSupportInfo.noSubsidyUrl || '//gov-subsidy.jd.com'
                
                // 注销点击事件
                $("#common_banner").unbind('click');

                // 开关或命中实验
                if( gbHit == 1 || govAb ){ 
                    // 点击腰带
                    $("#common_banner").click(function(){
                        landmine({
                            functionName:'Productdetail_YaodaiClick',
                            exposureData: ['mainskuid', 'belt_type'],
                            extraData: {
                                subsidytype: beltBannerInfo.subsidyType || "-100",
                                subsidyUserStatus:beltBannerInfo.subsidyUserStatus || "-100"
                            },
                            errorTips: '腰带点击报错'
                        })
                        
                        Guobu.showDialog(noSubsidyUrl)
                    })
                } 
                
                //点击链接
                else if(leftIconLink){
                    $("#common_banner").click(function(){
                        landmine({
                            functionName:'Productdetail_YaodaiClick',
                            exposureData: ['mainskuid', 'belt_type'],
                            extraData: {
                                subsidytype: beltBannerInfo.subsidyType || "-100",
                                subsidyUserStatus:beltBannerInfo.subsidyUserStatus || "-100"
                            },
                            errorTips: '腰带点击报错'
                        })
                        
                        // window.location.href = leftIconLink;
                        window.open(leftIconLink)
                    })
                }

                // 腰带倒计时
                if(countDownDarkMoonCloth && countDownDarkMoonCloth > 0){
                    var $el = $('#common_banner')
                    var leftArrowIconHtml = middleIcon ? "<span class='pay-right-img hide'><img src=" + middleIcon + " height='43px'/></span>":""
                    // 右侧闪电icon
                    if($(".pay-icon-right-Arrow").length > 0){
                        $(".pay-icon-right-Arrow").remove()
                    }
                    $el.append("<div class='left-message-info'><div class='middleIcon hide'></div><div class='activity-message'></div></div>")
                    var $cd = $el.find('.activity-message')
                    // 防止定义多个定时器对DOM进行操作
                    var countdown = $cd.data('countdown');
                    if (!countdown) {
                        countdown = new Tools.Countdown( countDownDarkMoonCloth * 1000 , function(res) {
                            var stack =  bannerRightText ? [bannerRightText] : ['距结束']
                            if (res.d > 0) {
                                stack.push('<span style="color:'+countdownTextColor+'">' + res.d + '</span>天')
                            }
                            stack.push('<span style="color:'+countdownTextColor+'">' + Tools.prefix(2, res.h) + '</span>')
                            stack.push(':<span style="color:'+countdownTextColor+'">' + Tools.prefix(2, res.m) + '</span>')
                            stack.push(':<span style="color:'+countdownTextColor+'">' + Tools.prefix(2, res.s) + '</span>')
                            $cd.html(stack.join(''))
                            $el.find(".pay-right-img").show()
                            if(res.d == 0 && res.h == 0 && res.m == 0 && res.s == 0){ // 倒计时结束刷新页面
                                window.location.reload();
                            }
                        });
                        $cd.data('countdown', countdown);
                        setTimeout(function () {
                            $el.find('.middleIcon').html(leftArrowIconHtml).show()// 展示闪电
                        }, 900);                       
                    }
                   
                }
                // 国补箭头由中间调整为右侧
                if(beltType == '11' && leftIconLink && leftArrowIcon) {
                    $('.leftArrowIcon').hide()
                    $('.pay-icon-right').append('<img src="' + leftArrowIcon + '" style="height:12px; margin-left:2px"/>')
                }

                if($('.common-plan').is(':hidden')){// 没有tab切换
                    $(".infomation").css({"margin-top":"-8px"}) // 主图往上移动8px
                }else{
                    $(".common-plan").css("margin-top","-8px") // Tab切换模块往上移动8px
                }

                

            }else{
                $("#common_banner").hide()
            }

        })
    }

    /**
     * 百亿补贴屏蔽按钮展示引导二维码
     */
    function getBtnQrCode (data) {
        var cfg = (window.pageConfig &&
            window.pageConfig.product) || {};
        var bybtInfoBybt =data && data.data && data.data.bybtInfo && data.data.bybtInfo.bybt;//百亿补贴标识
        var shield =data && data.data && data.data.bybtInfo && data.data.bybtInfo.shield;//百亿补贴屏蔽标屏蔽店铺信息和购买按钮，展示二维码
        if(bybtInfoBybt){
            $('.summary-price.J-summary-price .dt').html('补贴价');
        }

        var bbtfVal = G.serializeUrl(location.href).param.bbtf // 获取URL后bbtf参数
        window.pageConfig.product.shield = shield || "" // 给其他屏蔽接口传递（比如评价问答）
        if(bbtfVal && bbtfVal.length > 0 && shield && cfg.bybtShieldSwitch){// 上游传的token=1并且屏蔽标为true并且打开开关
            $(".shieldShopInfo,#InitCartUrl-mini,#InitCartUrl").remove()
            $("#btn-goapp").show()
            $(".J_choose_btn").hide()
            // $("#choose-btns").after("<div id='choose-btns1'></div>").remove()
            // $("#choose-btns1").html('<div class="mobile-only clearfix J-mobile-only" style="margin-bottom: 20px;"><div class="qrcode fl"></div><div class="text lh">仅支持手机扫码购买<br/>扫一扫，立即下单</div>')
            // require.async('PLG_ROOT/jQuery.qrcode', function() {
            //     $('#choose-btns1 .qrcode').html('').jdQrcode({
            //         render: 'image',
            //         ecLevel: 'L',
            //         size: 80,
            //         text: 'https://cd.jd.com/qrcode?skuId='+ cfg.skuid +'&location=2&isWeChatStock='+ cfg.jumpChannel || '//m.jd.com/product/' + cfg.skuid + '.html?from=qrcode'
            //     })
            // })
        }
        
    }

    // pv现货率 埋点
    function sendLog(data) {
        var cfg = (window.pageConfig &&
            window.pageConfig.product) || {};
        var price = cfg.jp || ''
        var stock = data.data.stockInfo
        var pvstock = {
            webstate: 1,
            delivestate: 5,
            stock: 33
        }
        var state = stock.code == 2 ? -1 : stock.stockState
        var stateMap = {
            '33': 1,
            '34': 4,
            '0': 4,
            '39': 2,
            '40': 2,
            '36': 3
        }
        var deliveMap = { '-1': 6 }
        if (stateMap[state]) pvstock.webstate = stateMap[state]
        if (deliveMap[state]) pvstock.delivestate = deliveMap[state]
        pvstock.stock = state
        var ipLocDjd = Tools.getAreaId().areaIds;
        var param = {
            province_id :ipLocDjd[0], // pv现货率相关-省份id
            city_id :ipLocDjd[1], // pv现货率相关-城市id
            county_id :ipLocDjd[2], // pv现货率相关-地区/县id
            street_id :ipLocDjd[3], // pv现货率相关-街道id
            skuid :cfg.skuid, // pv现货率相关-商品id
            sku_jd_prc :price, // pv现货率相关-商品价格当前京东价，替换url参数skuPrice
            stockState :pvstock.webstate, // pv现货率相关-页面显示商品库存状态（如预订 12）
        }
        if (typeof logJSON === 'function') {
            logJSON('pv_stock', 'sku', JSON.stringify(param))
        }
    }


    function rank(data){
        var r = data && data.data;
        var isBybtInfo =data && data.data && data.data.bybtInfo && data.data.bybtInfo.bybt;//百亿补贴标识
        if ($("#day-sales-rank").length && (pageConfig.product.cat[2] == 655 || pageConfig.product.cat[2] == 6881)) {
            if (r && r.raceDetailInfo) {
                var rankText = '<div class="dt">销量榜</div>'+
                    '<div class="dd">'+ r.raceDetailInfo.desc + '<a target = "_blank" href="'+ r.raceDetailInfo.url +'" clstag="shangpin|keycount|product|pcjingsubang">查看榜单 ></a></div>';
                $('#day-sales-rank').html(rankText);
            }
        }
        //拼购不显示其他价格
        if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0  && r.pinGouInfo && !isBybtInfo) {
            // setPinGouBanner(r);
            return false;
        }
        pageConfig.product.xianshitehui = new xianshitehui(pageConfig.product);
    }

    function setBannerNew (r) {
        /**
         * 秒杀闪购价格判断 —— 朱志荣
         * seckillType === 7 为 PLUS会员专享秒杀标识
         */
        var seckill = r.data.miaoshaInfo
        var price = r.data.price // op 为划线价 p为京东秒杀价
        var flash = r.data.flashInfo
        var cfg = pageConfig.product
        if(cfg.skuMarkJson && cfg.skuMarkJson.pg){//拼购

        }else if (seckill && seckill.miaosha && $('#banner-miaosha').length > 0 ){
            if (seckill.seckillType !== "7") {
                set($('#banner-miaosha'), seckill.state, seckill.msTrailer, seckill.miaoshaRemainTime, seckill.promoPrice, seckill.benifitText, price, seckill);
            }
        }else if( flash && flash.state != 0 && $('#banner-shangou').length > 0) {
            setSg($('#banner-shangou'), flash.state, flash.desc, flash.cd, price);
            
        }
        function set($el, state, desc, d, promoPrice, benifitText, price, miaoshaInfo) {
            if (!$el.length) return

            var $cd = $el.find('.activity-message')
            var $cp = $el.find('.activity-price')
            var cName = state == 2 ? 'seckilling' : 'seckill-yg'
            $el.addClass(cName).find('i').addClass('sprite-' + cName)
            /// 防止定义多个定时器对DOM进行操作
            var countdown = $cd.data('countdown');
            if (!countdown) {
                countdown = new Tools.Countdown( d * 1000 , function(res) {
                    var stack = [desc]
                    if (res.d > 0) {
                        stack.push('<span>' + res.d + '</span>天')
                    }
                    stack.push('<span>' + Tools.prefix(2, res.h) + '</span>')
                    stack.push(':<span>' + Tools.prefix(2, res.m) + '</span>')
                    stack.push(':<span>' + Tools.prefix(2, res.s) + '</span>')
                    $cd.html(stack.join(''))
                });
                $cd.data('countdown', countdown);
                
            }
            if (state == 1) { //秒杀预告
                if(Number(price.p) > Number(promoPrice)){
                    var tpl = '<strong>'+benifitText+'</strong><span>秒杀价￥'+promoPrice+'</span>'
                }else{
                    var tpl = '<strong>'+benifitText+'</strong>'
                }
                $cp.html(tpl)
                $el.show()

                // PRD: https://joyspace.jd.com/pages/vECyVcZvBRkmMxiNkWg3
                if(miaoshaInfo.koYgDarkMoon){ //字段true不展示秒杀预告楼层，
                    $el.remove()
                }

            } else if (state == 2) { //秒杀中
                if(Number(price.p) < Number(price.op)){
                    $el.show();
                }else{
                    $el.show();
                    $('.J-summary-price .pricing').text('');
                }

                if(miaoshaInfo.koDarkMoon){ //true不展示普通秒杀前面icon和京东秒杀文案
                    $el.find('.activity-type').remove()
                }
                
            } else {
                $el.hide()
                $el.remove()
            }
        }
        function setSg($el, state, desc, d, price) {
            if (!$el.length) return

            var $cd = $el.find('.activity-message')
            var cName = state == 2 ? 'seckilling' : 'seckill-yg'
            $el.addClass(cName).find('i').addClass('sprite-' + cName)
            
            if (state == 1) { //闪购预告
                $cd.html(desc)
                $el.show()
            } else if (state == 2) { //闪购中
                // 商品前台京东价(p)＜ 划线价(l 或 op)，如果未满足商详则不展示秒杀样式
                var hxp = Number(price.l) > 0 ? Number(price.l) : Number(price.op);
                if (Number(price.p) < hxp) {
                    $el.show();
                    
                } else {
                    $el.show();
                    $('.J-summary-price .pricing').text('');
                }
                /// 防止定义多个定时器对DOM进行操作
                var countdown = $cd.data('countdown');
                if (!countdown) {
                    countdown = new Tools.Countdown( d * 1000 , function(res) {
                        var stack = [desc]
                        if (res.d > 0) {
                            stack.push('<span>' + res.d + '</span>天')
                        }
                        stack.push('<span>' + Tools.prefix(2, res.h) + '</span>')
                        stack.push(':<span>' + Tools.prefix(2, res.m) + '</span>')
                        stack.push(':<span>' + Tools.prefix(2, res.s) + '</span>')
                        $cd.html(stack.join(''))
                    });
                    $cd.data('countdown', countdown);
                }
                
            } else {
                $el.hide()
                $el.remove()
            }
        }
    }

    /**
     * 大促预告价
     * @param cfg
     */
    function setPreparePrice(cfg) {
        function handlePreparePrice(data) {
            var price = data.price;
            //  预告价优先级 秒杀 > 闪购 > 大促预告价
            if (!price || !price.np) return;
            if ($('body').hasClass('yyp')) return;
            if ($('#banner-miaosha').length) return;  //秒杀
            if (cfg.skuMarkJson && cfg.skuMarkJson.pg) return;  //拼购，优先级处理
            if ($('#banner-shangou').length) return;  //闪购
            if ($('#prepare-price-banner').length) {
                $('#prepare-price-banner').remove()
            }


            var TEMPLATE = '\
                <div id="prepare-price-banner" class="activity-banner" data-name="${leftText}" style="background:url(${img}) 0 0 no-repeat"> \
                    <div class="activity-type"> \
                        <i class="sprite-yy"></i><strong></strong> \
                    </div>\
                    {if leftText} \
                        <span class="item J-item-1" style="color: #fff; font-size: 14px; {if imgHasText}margin-left: 190px;{/if}"> \
                            ${leftText} {if price}￥${price}{/if} \
                        </span> \
                    {/if} \
                    <div class="activity-message"> \
                        <span class="item J-item-2 hide" style="display: inline;"> \
                            {if rightText} \
                                ${rightText} \
                            {else} \
                                <i class="sprite-time"></i> \
                                <span class="J-text">${cdPrefix}</span> \
                                <em class="J-time"></em> \
                            {/if} \
                        </span> \
                    </div> \
                </div>';

            var $choose = $('#summary,.summary').eq(0);
            var np = price.np;

            $choose.before(TEMPLATE.process(np));
            var time = (+np.countdown) * 1000;
            var $time = $('#prepare-price-banner .J-time');
            if (time && $time.length) {
                new Countdown(parseInt(time), function(res) {
                    if (res.d < 1) {
                        $time.html(res.h + '小时' + res.m + '分' + res.s + '秒')
                    } else {
                        $time.html(
                            res.d +
                                '天' +
                                res.h +
                                '小时' +
                                res.m +
                                '分' +
                                res.s +
                                '秒'
                        )
                    }
                });
            }
        }
        Event.addListener('onPriceReady', handlePreparePrice);
    }

    /**
     * 根据获取的拼购信息做一些事情
     */
    function pingGouInfoReadyHandle() {
        // 拼购曝光埋点是否已经上报过了标识 false: 未上报; true: 已经上报过了
        var pgExposureIsSend = false;
        // 设置拼购商品页面曝光埋点
        var setPinGouExposure = function () {
            // 拼购商品页面曝光埋点 秒杀 与 预售优先级高
            if (!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && pageConfig.product.skuMarkJson.pg && !pgExposureIsSend) {
                var skuid = pageConfig && pageConfig.product && pageConfig.product.skuid || '-';
                expLogJSON('PC_PINGOU1', 'PC_PINGOU2', '{"v1":"PC_PINGOUGoodsExposure", "skuid":"' + skuid + '"}');
                // 标识已经上报过了
                pgExposureIsSend = true;
            }
        }

        pageConfig.eventTarget.addListener('onPinGouGoodsInfoReady', setPinGouExposure);
    }

    pingGouInfoReadyHandle();

    ///////////////// 首屏促销条BEGIN //////////////////////

    /**
     * is
     * @param {any} type 期望的数据类型的值
     * @param {any} val  需要校验的值
     * @returns {Boolean}
     */
    function is(type, val) {
        var toString = ({}).toString;
        return toString.call(type) === toString.call(val);
    }

    var PB = {
        /**
         * @memberOf PB
         * getTagId
         * @param {Number} id 线上正式的标签ID
         * @returns {Number}
         */
        getTagId: function (id) {
            var arr = location.search.match(/__tagid__=([^&]*)/i);  // 浏览器地址栏手动输入参数，方便开发调试
            var tagId = arr ? arr[1] : 0;
            if (tagId) {
                return tagId;
            } else {
                return id;
            }
        },

        /**
         * @memberOf PB
         * hasSpecialBanner
         * @param {Object} cfg 页面中的配置对象
         * @returns {Boolean}
         */
        hasSpecialBanner: function (cfg) {
            return (
                $('#banner-miaosha').length > 0 || $('#banner-shangou').length > 0 ||
                cfg.isYuYue || cfg.isYuShou || (cfg.skuMarkJson && cfg.skuMarkJson.pg)
            );
        },

        /**
         * @memberOf PB
         * isWithinDate
         * @param {String} begin 时间字符串 格式：'2018-23-12 12:21:12'
         * @param {String} end   时间字符串
         * @returns {Boolean}
         */
        isWithinDate: function (begin, end) {
            var DATE_REGEXP = /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/;
            var getTimeStamp = function(y, m, d, h, min, s) {return +new Date(y, m-1, d, h, min, s);}
            if (DATE_REGEXP.test(begin) && DATE_REGEXP.test(end)) {
                begin = getTimeStamp.apply(null, DATE_REGEXP.exec(begin).slice(1, 7));
                end = getTimeStamp.apply(null, DATE_REGEXP.exec(end).slice(1, 7));
                var now = +new Date();
                if (now < begin || now > end) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return false;
            }
        },

        /**
         * @memberOf PB
         * isCategoryValid
         * @param {Object} map 分类字典
         * @param {Array}  arr 当前分类
         * @returns {Boolean}
         */
        isCategoryValid: function(map, arr) {
            if (is({}, map) && is([], arr)) {
                var tmp = map;
                for (var i = 0, size = arr.length; i < size; i++) {
                    var val = String(arr[i]);
                    if (val in tmp) {
                        if (is({}, tmp[val])) {
                            tmp = tmp[val];
                        } else {
                            return true;
                        }
                    } else {
                        return false;
                    }
                }
                return false;
            } else {
                return false;
            }
        },

         /**
         * @memberOf PB
         * hasAtmosphereBanner
         * @param {String} selector jQuery选择器
         * @returns {Boolean}
         */
        hasAtmosphereBanner: function(selector) {
            var $banner = $(selector);
            if ($banner.length > 0) {
                return true;
            } else {
                return false;
            }
        },

        /**
         * @memberOf PB
         * addAtmosphereBanner
         * @param {String} url 图片链接
         * @param {String} selector id选择器
         */
        addAtmosphereBanner: function(url, selector) {
            url = url || '';
            selector = selector || '#J_atmosphere_banner';
            if (!PB.hasAtmosphereBanner(selector)) {
                var $mount = $('#summary,.summary').eq(0);
                if (selector[0] === '#') {
                    var __html = '<div id="{0}" class="activity-banner" style="background:url({1}) 0 0 no-repeat"></div>';
                } else if (selector[0] === '.') {
                    var __html = '<div class="activity-banner {0}" style="background:url({1}) 0 0 no-repeat"></div>';
                }
                $mount.before(__html.format(selector.slice(1), url));
            }
        },

        /**
         * @memberOf PB
         * removeAtmosphereBanner
         * @param {String} selector jQuery选择器
         * @returns {Boolean}
         */
        removeAtmosphereBanner: function (selector) {
            if (PB.hasAtmosphereBanner(selector)) {
                $(selector).remove();
                return true;
            } else {
                return false;
            }
        },

        /**
         * @memberOf PB
         * removeAtmosphereBanner
         * @param {String} 图片链接
         * @param {String} 活动链接
         */
        addEntryLink: function (url, href) {
            $('body').append(
                '<a id="J_atmosphere_side" href="'+ href +'" target="_blank" style="position:fixed;right:0;top:0;z-index:10000">'
                + '<img src="'+ url +'">'
                + '</a>'
            );
        },

        /**
         * @memberOf PB
         * requestLabelSystem  获取活动标识的一种方式
         * @param {String} venderId
         * @param {Function} callback
         */
        requestLabelSystem: function (venderId, callback) {
            if (venderId) {
                $.ajax({
                    url: '//rms.shop.jd.com/json/vender/msmyDynValue.action?venderId=' + venderId,
                    dataType: 'jsonp',
                    success: function(res) {
                        is(function(){}, callback) && callback(res);
                    },
                    error: function(err) {
                        console && console.error('//rms.shop.jd.com/json/vender/msmyDynValue.action\n', err);
                    }
                });
            }
        }
    };

    /**
     * 2018年618大促期间活动，目前有三个
     * @param {Object} cfg
     */
    // function set618Skin(cfg) {
    //     if (PB.isWithinDate('2018-05-25 00:00:01', '2018-06-20 23:59:59')) {
    //         var cat = cfg.cat;
    //         // 侧边栏
    //         var cat1 = cat[0];
    //         if (PB.isCategoryValid({9855: 9855, 9847: 9847}, cat) && PB.isWithinDate('2018-06-14 00:00:00', '2018-06-20 23:59:59')) {
    //             // 618大促3免1
    //             var imageURL = '//newbuz.360buyimg.com/pcItem/default/618_3f1.png'
    //             if (cat1 == 9855) {
    //                 PB.addEntryLink(imageURL, '//sale.jd.com/act/2E3IfuONpS.html');
    //             } else if (cat1 == 9847) {
    //                 PB.addEntryLink(imageURL, '//sale.jd.com/act/nURXZ50dwNt8YqHu.html');
    //             }
    //         } else if (
    //             PB.isCategoryValid({
    //                 1672: {2599: 2599, 14226: 14226, 14227: 14227},
    //                 1620: 1620, 6196: 6196, 6728: 6728,
    //                 9192: 9192, 15248: 15248, 15126: 15126
    //             }, cat) && PB.isWithinDate('2018-06-13 00:00:00', '2018-06-20 23:59:59')
    //         ) {
    //             // 618大促3件7折
    //             var imageURL = '//newbuz.360buyimg.com/pcItem/default/618_3p7.png';
    //             PB.addEntryLink(imageURL, '//sale.jd.com/act/J0rOFYh2kxju8.html');
    //         }

    //         // 促销条
    //         if (!PB.hasSpecialBanner(cfg)) {
    //             Event.addListener('onPriceReady', function(data) {
    //                 var isPreparePrice = data && data.price && data.price.np;
    //                 if (isPreparePrice) {
    //                     PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //                 } else {
    //                     if (cfg.isPop) {
    //                         PB.requestLabelSystem(
    //                             cfg.venderId,
    //                             function(res) {
    //                                 var id = parseInt(res.value, 10);
    //                                 if (id === PB.getTagId(424283) && PB.isWithinDate('2018-06-14 00:00:00', '2018-06-20 23:59:59') && PB.isCategoryValid({9855: 9855, 9847: 9847}, cat) ) {
    //                                     // 618大促3免1促销条
    //                                     PB.addAtmosphereBanner('//newbuz.360buyimg.com/pcItem/default/618_3f1_banner.jpg');
    //                                 } else if (id === PB.getTagId(424282) && PB.isWithinDate('2018-06-13 00:00:00', '2018-06-20 23:59:59') && PB.isCategoryValid({
    //                                     1672: {2599: 2599, 14226: 14226, 14227: 14227},
    //                                     1620: 1620, 6196: 6196, 6728: 6728,
    //                                     9192: 9192, 15248: 15248, 15126: 15126
    //                                 }, cat)) {
    //                                     // 618大促3件7折促销条
    //                                     PB.addAtmosphereBanner('//newbuz.360buyimg.com/pcItem/default/618_3p7_banner.jpg');
    //                                 } else {
    //                                     // 618大促通用皮肤
    //                                     if (parseInt(cfg.pTag, 10) === PB.getTagId(424280)) {
    //                                         PB.addAtmosphereBanner('//newbuz.360buyimg.com/pcItem/default/618_banner.png');
    //                                     }
    //                                 }
    //                             }
    //                         );
    //                     } else {
    //                         if (parseInt(cfg.pTag, 10) === PB.getTagId(424280)) {
    //                             PB.addAtmosphereBanner('//newbuz.360buyimg.com/pcItem/default/618_banner.png');
    //                         }
    //                     }
    //                 }
    //             });
    //         }
    //     }
    // }

    /**
     * 2019年双11大促
     * @param {Object} cfg
     */
    // function setDoubleElevenSkin(cfg) {
    //     var cat = cfg.cat || [];
    //     // 医药品类侧边栏氛围渲染
    //     if (cat[0] == 9192 &&
    //         PB.isWithinDate('2019-10-25 00:00:01', '2019-10-31 23:59:59') ) {
    //         PB.addEntryLink(
    //             "//m.360buyimg.com/cc/jfs/t1/79785/26/13669/13336/5db04d87Eb1f9d867/5ab48565cdaac6d1.png",
    //             "//prodev.jd.com/mall/active/4RDGCZFdLJLyv12VCD37pqxiejyM/index.html"
    //         );
    //     }

    //     // 促销氛围渲染
    //     if (!PB.isWithinDate('2019-10-18 00:00:01', '2019-11-15 23:59:59')) {
    //         return;
    //     }

    //     var ptag = parseInt(cfg.pTag, 10);

    //     if (ptag == PB.getTagId(424461) &&
    //         !PB.isWithinDate('2019-11-01 00:00:01', '2019-11-11 23:59:59') ) {
    //         return;
    //     }

    //     if ( !(ptag == PB.getTagId(424458) ||
    //         ptag == PB.getTagId(424457) ||
    //         ptag == PB.getTagId(424456) ||
    //         ptag == PB.getTagId(424461)) ) {
    //         return;
    //     }

    //     if (!PB.hasSpecialBanner(cfg)) {
    //         Event.addListener('onPriceReady', function(data) {
    //             var isPreparePrice = data && data.price && data.price.np;
    //             if (isPreparePrice) {
    //                 PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //             } else {
    //                 PB.addAtmosphereBanner("//m.360buyimg.com/cc/jfs/t1/95166/19/2649/13444/5dd38a7dE60ff58c7/568f5a943d46c8ce.png");
    //             }
    //         });
    //     }

    // }

     /**
     * 2019年黑五大促
     * @param {Object} cfg
     */
    // function setBlackFridaySkin(cfg) {
    //     // 促销氛围渲染
    //     if ( !PB.isWithinDate("2019-11-22 00:00:01", "2019-12-06 00:00:01") ) {
    //         return;
    //     }

    //     var url = Conf.get("GLOBAL.blackFriImgUrl") ||
    //         "//m.360buyimg.com/cc/jfs/t1/95166/19/2649/13444/5dd38a7dE60ff58c7/568f5a943d46c8ce.png";

    //     if (!PB.hasSpecialBanner(cfg) &&
    //         G.specialAttrs["iscommerce"] == '1' ) {
    //         Event.addListener('onPriceReady', function(data) {
    //             var isPreparePrice = data && data.price && data.price.np;
    //             if (isPreparePrice) {
    //                 PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //             } else {
    //                 PB.addAtmosphereBanner(url);
    //             }
    //         });
    //     }

    // }

    /**
     * 2019年双12大促
     * @param {Object} cfg
     */
    // function setDoubleTwelveSkin(cfg) {
    //     var obj = {};
    //     obj[PB.getTagId(424466)] = PB.isWithinDate(
    //         "2019-12-01 00:00:01", "2019-12-12 23:59:59");
    //     obj[PB.getTagId(424467)] = PB.isWithinDate(
    //         "2019-12-10 00:00:01", "2019-12-12 23:59:59");

    //     if (!obj[parseInt(cfg.pTag, 10)] ||
    //         PB.hasSpecialBanner(cfg)) {
    //         return;
    //     }

    //     var url = Conf.get("GLOBAL.d1212") ||
    //         "//m.360buyimg.com/cc/jfs/t1/22917/35/209/4335/5c07dabbE3c7b4025/f0cd75fa269e40c9.png";

    //     Event.addListener('onPriceReady', function (data) {
    //         var isPreparePrice = data && data.price && data.price.np;
    //         if (isPreparePrice) {
    //             PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //         } else {
    //             PB.addAtmosphereBanner(url);
    //         }
    //     });
    // }

    /**
     * 2019年618家装节
     * @param {Object} cfg
     */
    // function setHomeDecorationSkin(cfg) {
    //     // 时间约束
    //     if (!PB.isWithinDate('2019-06-15 00:00:01', '2019-06-18 23:23:59')) {
    //         return;
    //     }
    //     // 侧边栏
    //     if (
    //         PB.isCategoryValid({
    //             6728: 6728,    // 汽车用品
    //             9192: 9192,    // 医药保健
    //             15083: 15083,  // 房地产
    //             12473: 12473,  // 农资园艺
    //             1672: {        // 礼品箱包
    //                 14227: 14227   // 鲜花速递
    //             }
    //         }, cfg.cat)
    //     ) {
    //         var imageURL = '//m.360buyimg.com/cc/jfs/t1/56820/9/1091/13515/5cecb214Ee52f6b0b/1c62a772c3deeed8.png';
    //         PB.addEntryLink(imageURL, '//pro.jd.com/mall/active/u6ogudtXbTifdm7pp7xTpb35YT5/index.html');
    //     }

    //     // 促销条
    //     if (
    //         !PB.hasSpecialBanner(cfg) &&
    //         (parseInt(cfg.pTag, 10) == PB.getTagId(424430))
    //     ) {
    //         Event.addListener('onPriceReady', function(data) {
    //             var isPreparePrice = data && data.price && data.price.np;
    //             if (isPreparePrice) {
    //                 PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //             } else {
    //                 PB.addAtmosphereBanner('//m.360buyimg.com/cc/jfs/t1/52282/39/1783/20123/5cf8c817E9d2b2877/6c93251686d8b130.jpg');
    //             }
    //         });
    //     }
    // }

    /**
     * 2019年货节
     * @param {Object} cfg
     */
    // function setNHJSkin(cfg) {
    //     if (PB.hasSpecialBanner(cfg)) { return; }

    //     var pTag = parseInt(cfg.pTag, 10),
    //         url = "";

    //     if (PB.isWithinDate("2019-12-30 00:00:01", "2020-01-31 23:23:59") &&
    //         (pTag === PB.getTagId(424472) || pTag === PB.getTagId(424476) || pTag === PB.getTagId(424475))) {
    //         url = "//m.360buyimg.com/cc/jfs/t1/90642/16/8184/38804/5e032d83E0386073b/2d1c3bbc9e1b73b5.png";
    //     } else if (PB.isWithinDate("2020-01-01 00:00:01", "2020-01-07 23:23:59") &&
    //         pTag === PB.getTagId(424469)) {
    //         url = "//m.360buyimg.com/cc/jfs/t1/101040/12/8814/15678/5e099157Eed81dcc4/f5d9adb190d5e3a1.jpg";
    //     } else {
    //         return;
    //     }

    //     Event.addListener('onPriceReady', function (data) {
    //         var isPreparePrice = data && data.price && data.price.np;
    //         if (isPreparePrice) {
    //             PB.removeAtmosphereBanner("#J_atmosphere_banner");
    //         } else {
    //             PB.addAtmosphereBanner(url);
    //         }
    //     });
    // }

     /**
     * 2018 超级品牌
     * @param {Object} cfg
     */
    // function set3CSkin(cfg) {
    //     if (PB.isWithinDate('2019-01-18 00:00:01', '2019-01-18 23:23:59')) {
    //         // 促销条
    //         if (
    //             !PB.hasSpecialBanner(cfg) &&
    //             (parseInt(cfg.pTag, 10) == PB.getTagId(424391))
    //         ) {
    //             Event.addListener('onPriceReady', function(data) {
    //                 var isPreparePrice = data && data.price && data.price.np;
    //                 if (isPreparePrice) {
    //                     PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //                 } else {
    //                     PB.addAtmosphereBanner('//img14.360buyimg.com/cms/jfs/t1/26186/25/4988/3447/5c36f45bE65674601/13f25a5294557701.png');
    //                 }
    //             });
    //         }
    //     }
    // }
    /**
     * 2019年春节不打烊
     * @param {Object} cfg
     */
    function setBDYSkin(cfg) {
        if (
            // PB.isWithinDate("2021-01-18 00:00:01", "2021-02-31 23:23:59")
            // && (parseInt(cfg.pTag, 10) == PB.getTagId(424473))
            cfg.isFestival
            && !PB.hasSpecialBanner(cfg)
        ) {
            // 通过函数重定义来阻止原‘大促预告价’的代码执行
            setPreparePrice = function (){};
            // 重新定义预告价功能以适应“春节不打烊”的需求
            var setPreviewPrice = function handlePreparePrice(data) {
                var price = data.price;
                //  预告价优先级 秒杀 > 闪购 > 大促预告价
                if (!price || !price.np) return;
                if ($('body').hasClass('yyp')) return;
                if ($('#banner-miaosha').length) return;  //秒杀
                if (cfg.skuMarkJson && cfg.skuMarkJson.pg) return;  //拼购，优先级处理
                if ($('#banner-shangou').length) return;  //闪购
                if ($('#prepare-price-banner').length) {
                    $('#prepare-price-banner').remove()
                }
    
    
                var TEMPLATE = '\
                    <div id="prepare-price-banner" class="activity-banner" data-name="${leftText}" style="background:url(${img}) 0 0 no-repeat"> \
                        <div class="activity-type"> \
                            <i class="sprite-yy"></i><strong></strong> \
                        </div>\
                        {if leftText} \
                            <span class="item J-item-1" style="color: #fff; font-size: 14px; {if imgHasText}margin-left: 190px;{/if}"> \
                                ${leftText} {if price}￥${price}{/if} \
                            </span> \
                        {/if} \
                        <div class="activity-message"> \
                            <span class="item J-item-2 hide" style="display: inline;"> \
                                {if rightText} \
                                    ${rightText} \
                                {else} \
                                    <i class="sprite-time"></i> \
                                    <span class="J-text">${cdPrefix}</span> \
                                    <em class="J-time"></em> \
                                {/if} \
                            </span> \
                        </div> \
                    </div>';
    
                var $choose = $('#summary,.summary').eq(0);
                var np = price.np;
    
                $choose.before(TEMPLATE.process(np));
                var time = (+np.countdown) * 1000;
                var $time = $('#prepare-price-banner .J-time');
                if (time && $time.length) {
                    new Countdown(parseInt(time), function(res) {
                        if (res.d < 1) {
                            $time.html(res.h + '小时' + res.m + '分' + res.s + '秒')
                        } else {
                            $time.html(
                                res.d +
                                    '天' +
                                    res.h +
                                    '小时' +
                                    res.m +
                                    '分' +
                                    res.s +
                                    '秒'
                            )
                        }
                    });
                }
            };
            var doBusiness = function (data) {
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }
                $.ajax({
                    url: host + '/festival',
                    data: {
                        ch: 1,
                        skuId: cfg.skuid,
                        area: Tools.getAreaId().areaIds.join('_'),
                        buyNum: $('#buy-num').val(),
                        appid: 'item-v3',
                        functionId: "pc_festival" 
                    },
                    dataType: 'jsonp'
                })
                .done(function (res) {
                    if (!!res.isfhsx) {
                        PB.addAtmosphereBanner(cfg.atmosphereBannerImg);
                    } else {
                        PB.removeAtmosphereBanner('#J_atmosphere_banner');
                        // if (data && data.price.np) {
                        //     setPreviewPrice(data);
                        // }
                    }
                })
                .fail(function(err) {
                    PB.removeAtmosphereBanner('#J_atmosphere_banner');
                    // if (data && data.price.np) {
                    //     setPreviewPrice(data);
                    // }
                });
            };
            Event.addListener('onPriceReady', doBusiness);
        }
    }

    // 设置“金机奖”信息
    function setGoldMachinePrize(skuid) {
        var $mount = $('.summary.summary-first');
        var __html = '\
            <div class="gold-machine-prize"> \
                {if title } <div class="dt">${title}</div> {/if} \
                {if content  } <div class="dd" style="font-family: Simsun;">${content} <a href="//sale.jd.com/act/0xzaney3lEqi5WAv.html" target="_blank" style="margin-left:10px;">查看&gt;&gt;</a> </div> {/if} \
            </div> \
        ';
        $.ajax({
            url: '//goldprize.jd.com/goldprize/isSelectedForPC/' + skuid,
            dataType: 'jsonp',
            success: function (res) {
                if ($.isPlainObject(res) && res.status == 200) {
                    if ($.isPlainObject(res.data)) {
                        $mount.after(__html.process(res.data));
                    }
                }
            },
            error: function (err) {
                console && console.log('//goldprize.jd.com/goldprize/isSelected/\n', err);
            }
        });
    }

    /**
     * PlusDay换肤
     * @param {Object} cfg
     */
    // function setPlusDaySkin(cfg) {
    //     if (!PB.hasSpecialBanner(cfg)) {
    //         Event.addListener('onPriceReady', function(data) {
    //             var price = data && data.price ? data.price : {};
    //             var hasPreparePrice = price.np;
    //             var hasPlusPrice = (price.up === 'pp' || price.up === 'tpp') && (price.pp || price.tpp);
    //             if (hasPreparePrice) {
    //                 PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //             } else {
    //                if (hasPlusPrice && parseInt(cfg.pTag, 10) === PB.getTagId(424295)) {
    //                     PB.addAtmosphereBanner('//m.360buyimg.com/cc/jfs/t26986/272/159888470/10590/29899fa7/5b87884cN6d1e54a7.jpg');
    //                }
    //             }
    //         });
    //     }
    // }

    /**
     * 蝴蝶结促销皮肤
     * 需求：https://cf.jd.com/pages/viewpage.action?pageId=161342547
     * 促销时间: 2019-02-25 00:00:00 - 2019-03-13 23:59:59
     * @param cfg
     */
    // function setButterflySkin(cfg) {
    //     if (!PB.hasSpecialBanner(cfg) && PB.isWithinDate('2019-02-25 00:00:01', '2019-03-13 23:59:59') && parseInt(cfg.pTag, 10) == PB.getTagId(424396)) {
    //         Event.addListener('onPriceReady', function(data) {
    //             var price = data && data.price ? data.price : {};
    //             var hasPreparePrice = price.np;
    //             if (hasPreparePrice) {
    //                 PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //             } else {
    //                 PB.addAtmosphereBanner('//m.360buyimg.com/cc/jfs/t1/17479/11/8210/37744/5c74eb58E9c9b6bb5/e679caf71afe256e.jpg');
    //             }
    //         });
    //     }
    // }

    /**
     * 2019年618大促氛围
     * @param {Object} cfg
     */
    // function set2020618Skin(cfg) {
    //     Event.addListener('onStockReady', function(data){
    //         var data=data.stock;
    //         var isPreparePrice =data && data.data && data.data.price && data.data.price.np;
    //         var atmosphereBannerImg =data && data.data && data.data.atmosphereBannerImg;
    //         var bybtInfoMgbp =data && data.data && data.data.bybtInfo && data.data.bybtInfo.mgbp;// 买贵双倍赔判断标
    //         var bybtInfoCompensateMultiple = data && data.data && data.data.bybtInfo && data.data.bybtInfo.compensateMultiple;// 买贵双倍赔腰带标签N倍
    //         var bybtInfoBybt = data && data.data && data.data.bybtInfo && data.data.bybtInfo.bybt;//百亿补贴标识

    //         var bybtmzjzInfoBybt =data && data.data && data.data.bybtMzjzInfo;// 百亿补贴美妆加赠对象
    //         var bannermzjzUrl = bybtmzjzInfoBybt && bybtmzjzInfoBybt.beltBannerUrl;// 百亿补贴美妆加赠腰带链接
    //         if(bybtmzjzInfoBybt && bannermzjzUrl && $('#banner-bybtmzjz').length > 0){ // 百亿补贴美妆优先级高于大促腰带
    //             return
    //         }else if(!PB.hasSpecialBanner(cfg) && atmosphereBannerImg && $("#J_atmosphere_banner_plus").length == 0 && !bybtInfoBybt){ // plus专项购的优先级比618腰带级别高,百亿补贴优先级大于大促买贵赔
    //             if (isPreparePrice) {
    //                 PB.removeAtmosphereBanner('#J_atmosphere_banner');
    //             } else {
    //                 PB.addAtmosphereBanner(atmosphereBannerImg);
    //                 if(bybtInfoMgbp && bybtInfoCompensateMultiple){ //命中买贵赔并下发2倍赔字段，后期可能会有N倍赔
    //                     var bName = "买贵赔"
    //                     switch (parseInt(bybtInfoCompensateMultiple)) {
    //                         case 1 :
    //                             bName = "买贵赔";
    //                             break;
    //                         case 2 :
    //                             bName = "买贵双倍赔";
    //                             break;
    //                         case 3 :
    //                             bName = "买贵三倍赔";
    //                             break;    
    //                         case 4 :
    //                             bName = "买贵四倍赔";
    //                             break;    
    //                         case 5 :
    //                             bName = "买贵五倍赔";
    //                             break;  
    //                         default :
    //                             bName = "买贵"+bybtInfoCompensateMultiple+"倍赔";
    //                             break;    
    //                     }
    //                     $("#J_atmosphere_banner").html("<span class='pay-icon'>" + bName + "</span>")
    //                 }
    //                 $('#banner-enterpriseBuy').hide()
    //             }
    //         }

    //     });
    // }

    ///////////////// 首屏促销条END //////////////////////

    /***
     * pingou banner
     * @param cfg
     */
    function setPinGouBanner(data) {
        //拼购不显示其他价格
        if(!data.pinGouInfo) return;
        // var time = (typeof cfg.pinGouInfo == 'object' && cfg.pinGouInfo.lefttime) ? parseInt(cfg.pinGouInfo.lefttime):0;
        var time = 0;
        // var $choose = $('#summary,.summary').eq(0)
        var $choose = $('#common_banner')
        var tpl =
            '\
        <div id="pingou-banner-new" class="activity-banner" data-name="${name}" style="background:url(${img}) 0 0 no-repeat">\
            <div class="activity-type">\
                <i class="sprite-yy sprite-pingou"></i><strong>${name}</strong>\
            </div>\
            {if timer>0}\
            <div class="activity-message">\
                <span class="item J-item-2 hide" style="display: inline;">\
                    <i class="sprite-time"></i>\
                    <span class="J-text">${cdText}</span>\
                    <em class="J-time"></em>\
            </span>\
            </div>\
            {/if}\
        </div>'

        //去掉时间
        $choose.html(
            tpl.process({
                name: data.jingXiInfo && data.jingXiInfo.jx ? '京喜' : '京东拼购',
                // name: data.jingXiInfo && data.jingXiInfo.jx || G.specialAttrs["isJxzy"] == '1' ? '京喜' : '京东拼购',
                img: '//img14.360buyimg.com/devfe/jfs/t17683/261/2561539025/7600/c5027c28/5afc357eN96b2ff34.png',
                cdText: '距结束',
                timer:0
            })
        ).show()
        var $time = $('#pingou-banner-new .J-time')
        if(time > 0){
            new Countdown(parseInt(time), function(res) {
                if (res.d < 1) {
                    $time.html(res.h + '小时' + res.m + '分' + res.s + '秒')
                } else {
                    $time.html(
                        res.d +
                        '天' +
                        res.h +
                        '小时' +
                        res.m +
                        '分' +
                        res.s +
                        '秒'
                    )
                }
            })
        }

    }
    /* pingou banner end */
    function stepPrice(cfg){
        var $stepPrice = $('span[node-tips=stepPrice]')
        if(!$stepPrice[0]) return
        stepPrice.bindEvent($stepPrice)
        stepPrice.getModel(cfg)
        .done(function(res){
            var $cont = $stepPrice.find('div[node-tips-content=stepPrice]')
            if(res && res.length>0){
                $cont.html(stepPrice.temp2html(res))
                $stepPrice.removeClass('hide')
            }
            // console.log(res)
        })
        .fail(function(err){
            console.warn(err && err.message || '阶梯价接口问题！')
        })
    }
    stepPrice.getModel = function(cfg){
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        return $.ajax({
            url: host + '/stepPrice?skuId='+ (cfg.skuid || '') +'&venderId='+ (cfg.venderId || '') + '&appid=item-v3&functionId=pc_stepPrice',
            dataType: 'jsonp'
        })
    }
    stepPrice.temp2html = function(model){
        var temp = ''
        for(var i=0,len=model.length;i<len;i++){
            var _data = model[i]
            temp += '<p><i>'+ _data['perMinNum'] +'~'+ _data['perMaxNum'] +'件</i>¥'+ _data['promoPrice'] +'</p>'
        }
        return temp
    }
    stepPrice.bindEvent = function(el){
        el.bind('mouseover', function(){
            el.addClass('zIndex')
        }).bind('mouseleave', function(){
            el.removeClass('zIndex')
        })
    }

    ///////////////////////////////////////
    //  业务名： 预告价腰带展示逻辑
    //  创建日期： 2017-09-22
    //  创建人erp：  xiechong1
    //////////////////////////////////////

    var BeltImg = {
        /**
        *  腰带加载初始化
        *  @param {object} pInfo  页面商品信息
        *  @param {object} data   商品价格接口返回的数据
        */
        init: function (pInfo, data) {
            this.getProductActivityInfo(pInfo, data);
        },
        /**
        *   调用中台接口，获取商品的促销活动信息
        *   @param {object} pInfo   页面商品信息
        *   @param {object} data    商品价格接口返回的数据
        */
        getProductActivityInfo: function (pInfo, data) {
            var _this = this;
            var skuId = pInfo.skuid.toString();
            var cId = pInfo.cat[pInfo.cat.length - 1].toString();
            var vId = pInfo.venderId.toString();
            var shopId = pInfo.shopId.toString();
            /**
             * skuId_x_y 参数，必填
             * skuId为商品编号
             * x为是否支持东券（1-不支持、2-支持，如果您无法获取该属性，请传0，我们将不走该逻辑，相关风险请咨询产品）
             * y为是否全球购（1-不是、2-是，如果您无法获取该属性，请传0，我们将不走该逻辑，相关风险请咨询产品）
             */
            var isCanUseDQ = 1,
                isOverseaPurchase = 0;

            if (pInfo && pInfo.specialAttrs) {
                if (/isCanUseDQ-0/.test(pInfo.specialAttrs)) { // 不支持东券
                    isCanUseDQ = 0;
                } else if (/isCanUseDQ-1/.test(pInfo.specialAttrs)) { // 支持东券
                    isCanUseDQ = 1;
                }

                if (/isOverseaPurchase-0/.test(pInfo.specialAttrs)) { // 不支持全球购
                    isOverseaPurchase = 1;
                } else if (/isOverseaPurchase-1/.test(pInfo.specialAttrs) || /isOverseaPurchase-2/.test(pInfo.specialAttrs) ||
                    /isOverseaPurchase-3/.test(pInfo.specialAttrs) || /isOverseaPurchase-4/.test(pInfo.specialAttrs)) { // 支持全球购
                    isOverseaPurchase = 2;
                }
            }
            var host = '//api.m.jd.com'
            if (pageConfig.product && pageConfig.product.colorApiDomain) {
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            $.ajax({
                //url: '//cd.jd.com/wbs/mgets',
                url: host + '/wbs/mgets',
                dataType: 'jsonp',
                data: {
                    search: skuId + "_" + isCanUseDQ + "_" + isOverseaPurchase + "," + cId + "," + vId + "," + shopId,
                    source: "pcitem",
                    appid: 'item-v3',
                    functionId: "pc_wbs_mgets"
                },
                success: function (res) {
                    // console.log(res[0]);
                    _this.businessForHaveActivityInfo(res, pInfo, data);

                },
                error: function (e) {
                    console.log("wbs/mgets have some error");
                }
            });
        },
        /**
        *   根据中台接口result字段有没有返回值分情况处理
        *   @param {object} res  中台接口返回值
        *   @param {object} data  价格接口返回值
        *   @param {object} pInfo  页面商品信息
        */
        businessForHaveActivityInfo: function (res, pInfo, data) {
            var _this = this;

            var result = {};
            if (res[0] && res[0].result) {
                result = res[0].result
            }
            var dataState = _this.checkActivityInfoAPIData(result);
            if (dataState) {
                //  中台接口返回数据正确
                _this.businessForControlBeltImg(data, true, result, pInfo);
            } else {
                // 中台接口返回数据不正确
                _this.businessForControlBeltImg(data, false, result, pInfo);// _this.getProductRealtimePrices(false, pInfo, result);
            }
        },
        /**
        *   腰带和预告价展示逻辑控制方法
        *   @param {bool} type  中台接口有没有返回值的标示  true 代表有返回值  false 代表没有返回值，或者返回值错误
        *   @param {object} res  预告价接口返回的数据
        *   @param {object} actInfo  预告价中台接口返回的数据
        *   @param {object} pInfo  页面商品信息
        */
        businessForControlBeltImg: function (res, type, actInfo, pInfo) {
            var _this = this;

            var advancePrice = res.price.np || {};
            //  预告价生效开时间
            var priceStartTime = _this.formatDate(advancePrice.s);
            //  预告价生效结束时间
            var priceEndTime = _this.formatDate(advancePrice.e);
            // 腰带图生效开始时间 同时也是  腰带图预告结束时间
            var beltStartTime = _this.formatDate(actInfo.start);
            // 腰带图生效结束时间
            var beltEndTime = _this.formatDate(actInfo.end);
            // 腰带图预告开始时间
            var beltForecastStartTime = _this.formatDate(actInfo.forecast);
            _this.printfMsg("预告价生效时间：" + priceStartTime + "-" + priceEndTime + "\n");
            _this.printfMsg("腰带图生效时间：" + beltStartTime + "-" + beltEndTime + "\n");
            _this.printfMsg("腰带图预告时间：" + beltForecastStartTime + "-" + beltStartTime + "\n");

            var dataState = _this.checkRealtimePricesAPIData(advancePrice);
            if (dataState) {
                if (type) {
                    //中台接口有数据，那么腰带肯定是要展示的
                    _this.setBeltImg(actInfo);
                    _this.printfMsg("只要中台接口有数据，那么腰带肯定是要展示的");

                    if (priceStartTime === beltStartTime && priceEndTime === beltEndTime) {
                        //中台接口有数据， 实时价格接口有数据，并且腰带生效时间和实时价格生效时间保持一致， 那么预告价肯定是要展示的
                        _this.setPriceImg(true);
                        _this.printfMsg("中台接口有数据， 实时价格接口有数据，并且腰带生效时间和实时价格生效时间保持一致， 那么预告价肯定是要展示的");
                    } else {
                        // 实时价格接口有数据，并且腰带生效时间和实时价格生效时间不一致时
                        _this.setPriceImg(false);
                        _this.printfMsg("中台接口有数据， 实时价格接口有数据，并且腰带生效时间和实时价格生效时间不一致， 优先显示腰带");
                    }
                } else {
                    //  中台接口没有数据，但是价格接口有数据时  只显示预告价信息
                    _this.setPriceImg(true);
                    _this.printfMsg("中台接口没有数据，但是价格接口有数据时  只显示预告价信息");
                }
            } else {
                // 实时价格接口没有数据时
                if (type) {
                    //中台接口有数据,但是价格接口没有数据时 只显示腰带图，不显示预告价贴片
                    _this.setBeltImg(actInfo);
                    _this.printfMsg("中台接口有数据,但是价格接口没有数据时 只显示腰带图，不显示预告价贴片");
                } else {
                    _this.printfMsg("中台接口和价格接口数据返回错误");
                }
            }
        },
        /**
        *   控制预告价切片是否展示
        *   @param {bool} type  是否要展示预告价切片  true 代表展示预告价  false 代表不展示预告价
        */
        setPriceImg: function (type) {
            var $priceBanner = $("#prepare-price-banner");
            if ($priceBanner[0]) {
                type ? $priceBanner.show() : $priceBanner.hide();
            }
        },
        /**
        *   显示腰带元素
        *   通过腰带后台（接口://cd.jd.com/wbs/mgets）配置腰带素材：1.腰带文案 2.活动起止时间 3.腰带背景图
        *   @param {object} actInfo  中台接口饭后的信息
        */
        setBeltImg: function (actInfo) {
            var _this = this;
            var $belt = $("#belt");
            var $specImg = $("#spec-img");

            var beltBgImg = "//img30.360buyimg.com/devfe/jfs/t19579/343/409400466/4499/ecd10130/5a7304f6N483d823b.png"; // 默认背景图
            var imgsArr = actInfo && actInfo.url && actInfo.url.split(",");
            if (imgsArr instanceof Array && imgsArr[2]) {
                beltBgImg = imgsArr[2];
            }

            var html =
                '\
              <div class="belt-bg" style="background: url({0}) no-repeat;">\
                  <p><span>{1}</span>&nbsp;<span>{2}</span></p>\
              </div>';

            if ($belt.length && !$(".belt-bg").length) {
                $belt.html(
                    html.format(
                        beltBgImg,
                        actInfo.content, // 腰带文案
                        _this.isSameDay(actInfo.start, actInfo.end) // 活动时间
                    ));
            }
            if ($specImg.length) {
                if ($specImg.width() === 450) {
                    $(".belt-bg").css("height", "66px").find("p").css("font-size", "20px");
                } else if ($specImg.width() === 350) {
                    $(".belt-bg").css("height", "51px").find("p").css("font-size", "17px");
                } else {
                    $(".belt-bg").css("height", "51px").find("p").css("font-size", "17px");
                }
            }
            if ($.browser.msie && $.browser.version == '8.0') {
                $(".belt-bg > p").css('left', ($specImg.width() - $(".belt-bg > p").width()) / 2);
            }
        },
        /**
        *   格式化接口里返回的时间戳
        *   @param {date}  time  时间戳
        *   @return  {string}  x.x
        */
        formatDate: function (time) {
            if (time) {
                time = parseInt(time.toString().substring(0, 10));
                var now = new Date(time * 1000);
                var month = now.getMonth() + 1;
                var date = now.getDate();
                return month + "." + date;
            }
        },
        /**
        *   判断开始日期和结束日期是不是同一天，如果是同一天则返回x月x日
        *   @param {date}  startTime  开始时间戳
        *   @param  {date}  endTime   结束时间戳
        *   @return  {string}   x月x日 或者 x.x-x.x
        */
        isSameDay: function (startTime, endTime) {
            if (startTime && endTime) {
                var rt = "";

                var s = parseInt(startTime.toString().substring(0, 10));
                var e = parseInt(endTime.toString().substring(0, 10));
                var d = e - s;
                var rt = "";

                var st = new Date(s * 1000);
                var et = new Date(e * 1000);

                var emonth = et.getMonth() + 1;
                var eday = et.getDate();
                var ehour = et.getHours();
                var eminutes = et.getMinutes();
                var esecond = et.getSeconds();

                var smonth = st.getMonth() + 1;
                var sday = st.getDate();

                // 10月26日0点0分0秒-10月27日0点0分59秒 显示10月26日
                // 10月26日0点0分0秒-10月27日0点1分0秒 显示10.26-10.27
                // 10月25日23点59分59秒-10月27日0点0分59秒 显示10.25-10.27
                //   if ( d <= 86459 ) {
                //     if (ehour == 0 && eminutes == 0) {
                //       eday -= 1;
                //     }
                //     if ( smonth === emonth && sday === eday) {
                //       rt = '('+smonth + '月' + sday + '日)';
                //     }else{
                //         rt = '('+smonth + '.' + sday + '-' + emonth + '.' + eday+')';
                //     }
                //   }else {
                //       rt = '('+smonth + '.' + sday + '-' + emonth + '.' + eday+')';
                //   }

                //最新逻辑 24小时=86400秒
                // 1、开始时间大于等于10月26日0点0分0秒、结束时间小于等于10月26日23点59分59秒或10月27日0点0分0秒 （显示10.26当天）
                // 2、10月31号 0点0分0秒 到11月1号 0点0分0秒（显示10.31当天）
                // 3、10月26日0点0分0秒-10月27日0点0分1秒 （显示10.26-10.27）
                // 4、10月25日23点59分59秒-10月27日0点0分0秒 （ 显示10.25-10.27）
                if (d <= 86400 && smonth == emonth && sday == eday) { //开始时间大于等于10月26日0点0分0秒、结束时间小于等于10月26日23点59分59秒（显示10.26当天）
                    rt = '(' + smonth + '.' + sday + '当天)';
                } else if (d <= 86400 && smonth == emonth && sday != eday && ehour == 0 && eminutes == 0 && esecond == 0) {//开始时间大于等于10月26日0点0分0秒、结束时间小于等于10月27日0点0分0秒 （显示10.26当天）  
                    rt = '(' + smonth + '.' + sday + '当天)';
                } else if (d <= 86400 && smonth != emonth && sday != eday && ehour == 0 && eminutes == 0 && esecond == 0) { //10月31号 0点0分0秒 到11月1号 0点0分0秒（显示10.31当天）
                    rt = '(' + smonth + '.' + sday + '当天)';
                } else {
                    rt = '(' + smonth + '.' + sday + '-' + emonth + '.' + eday + ')';
                }
                return rt;
            }
        },
        /**
        *   检测中台接口返回的数据是否正常
        *   @param {object}  data  中台接口result字段
        *   @return  {bool}
        */
        checkActivityInfoAPIData: function (data) {
            if (data && data.hasOwnProperty("start") && data.start && data.hasOwnProperty("end") && data.end && data.hasOwnProperty("forecast") && data.forecast) {
                //  中台接口需要用到的属性和值不为空
                if (parseInt(data.start) > parseInt(data.end) || parseInt(data.forecast) > parseInt(data.end)) {
                    // 腰带生效开始时间大于结束时间 或者 预告开始时间大于结束时间 返回false
                    return false;
                } else {
                    return true;
                }
            } else {
                // 中台接口需要用到的属性和值返回不正确
                return false;
            }
        },
        /**
        *   检测价格接口返回的数据是否正常
        *   @param {object}  data  价格接口np字段
        *   @return  {bool}
        */
        checkRealtimePricesAPIData: function (data) {
            if (data && data.hasOwnProperty("s") && data.s && data.hasOwnProperty("e") && data.e) {
                //  实时价格接口需要用到的属性和值不为空
                if (parseInt(data.s) > parseInt(data.e)) {
                    // 贴片预告价开始时间大于结束时间 或者 预告价小于等于0 返回false
                    return false;
                } else {
                    return true;
                }
            } else {
                return false;
            }
        },
        /**
        *   调试信息打印接口    根据debug的值可以控制打不打印信息
        *   @param {object}  msg  要打印的信息
        *   @return  {bool}
        */
        printfMsg: function (msg) {
            var debug = false;
            if (debug) {
                console.log(msg);
            }
        }
    }

    function setBeltImg(cfg) {
        if ($("#belt").length) {
            Event.addListener('onPriceReady', function(data) {
                BeltImg.init(cfg, data);
            });
        }
    }

    //限时特惠
    var xianshitehui = function(cfg){
        this.$el = $(".summary-price .J-xsth-sale");
        this.tips = {
            loginContext:"本商品为您限时优惠，请点击查看",
            loginWidth:204,
            noLoginContext:"本商品可能有优惠，请登录查看",
            noLoginWidth:194
        };
        this.init(cfg);
    };
    xianshitehui.prototype = {
        init:function(cfg){
            if(parseInt(cfg.cat[0]) == 737){
                var _this = this;
                G.onAttr(/gdsp-1/, function(attr) {
                    _this.existCallback(cfg);
                },function(){
                    _this.hideItem();
                });
            }
            else{
                this.hideItem();
            }
        },
        existCallback:function(cfg){
            var _this = this;
            this.checkLogin(function(data){
                if(data.IsAuthenticated){
                    _this.loginShowItem(data,cfg);
                }
                else{
                    _this.showItem(data);
                }
            });
        },
        showItem:function(data){
            this.$el.show();
            this.setTips(data);
            this.bindEvent();
        },
        loginShowItem:function(data,cfg){
            var _this = this;
            $.ajax({
                url: '//cd.jd.com/gdpPromotion',
                data: {
                    pin: data.Name,
                    skuId: cfg.skuid
                },
                dataType: 'jsonp',
                success: function(r) {
                    if(r && r.resultCode && r.resultCode == "SUCCESS"){
                        _this.showItem(data);
                    }
                    else{
                        _this.hideItem();
                    }
                }
            });
        },
        hideItem:function(){
            this.$el.hide();
        },
        bindEvent:function(){
            this.$el.delegate(".J-xsth-panel","click",function(){
                //控制右边栏促发点击事件
                var $xianshitehui = $("#J-global-toolbar .jdm-toolbar-tabs .jdm-tbar-tab-xianshitehui");
                if($xianshitehui.length){
                    $xianshitehui.click();
                }
            });
        },
        setTips:function(data){
            var _this = this;
            var tipsContent = this.tips.noLoginContext;
            var var_width = this.tips.noLoginWidth;
            if(data.IsAuthenticated){
                tipsContent = this.tips.loginContext;
                var_width = this.tips.loginWidth;
            }
            var $tipsObj = this.$el.find("i");
            if($tipsObj.length){
                $tipsObj.show().ETooltips({
                    close:false,
                    content : tipsContent,
                    width:var_width,
                    pos:"bottom",
                    zIndex:10,
                    onOpen:function(){
                        _this.tipOpenCallback(this);
                    }
                });
            }
        },
        tipOpenCallback:function(_this){
            var noLoginContext = this.tips.noLoginContext;
            var noLoginWidth = this.tips.noLoginWidth;
            var loginContext = this.tips.loginContext;
            var loginWidth = this.tips.loginWidth;
            this.checkLogin(function(data){
                if(data.IsAuthenticated){
                    _this.setContent(loginContext);
                    _this.$tooltips.css("width",loginWidth);
                }
                else{
                    _this.setContent(noLoginContext);
                    _this.$tooltips.css("width",noLoginWidth);
                }
            });
        },
        checkLogin:function(cb){
            cb = cb || function(data){};
            Tools.checkLogin(function(r){
                cb(r);
            });
        }
    };

    /**
     * 增加屈臣氏购卡、绑卡的引导
     * @param {Object} cfg
     */
    function setWatsonsCard(cfg) {
        try {
            var isSam = (cfg.specialAttrs.join('#').indexOf('isSamGoods') !== -1);

            function render(data) {
                var __html = '\
                    <div class="watsons" id="J_watsons">\
                        <span class="watsons__logo"></span>\
                        <span class="watsons__text">屈臣氏会员卡，</span>\
                        <a class="watsons__link watsons__link--mr18" href="//cartv.jd.com/item/200117505809.html" target="_blank">点击购卡&gt;&gt;</a>\
                        <span class="watsons__text">已有会员卡？请</span>\
                        <a class="watsons__link watsons__link--mr3" href="//bind.jd.com/watsons/watsonsIndex.html" target="_blank">点击绑定</a>\
                        <span class="sprite-question"></span>\
                    </div>';
                $('#J_watsons').remove();
                // var price = data.price;
                var doublePrice = data && data.price && data.price.doublePrice;
                if (doublePrice && doublePrice.up == '2' && doublePrice.price) {
                // if ((price.up === 'pp' || price.up === 'tpp') && (price.pp || price.tpp)) {
                    // jdPlus
                } else {
                    var shopId = cfg.shopId;
                    var catLevel3 = cfg.cat[2];
                    if (shopId == '41106') {
                        if (catLevel3 != 15655) {
                            var $mount = $('.J-summary-price .dd');
                            if ($mount.length > 0) {
                                var $watsons = $(__html);
                                $watsons.find('.sprite-question').ETooltips({
                                    autoHide: true,
                                    close: false,
                                    content: '屈臣氏电子会员卡，全国门店及京东屈臣氏官方旗舰店都可以使用，激活后可享受屈臣氏会员相关权益。',
                                    width: 250,
                                    pos: 'bottom',
                                    zIndex: 10
                                });
                                $mount.append($watsons);
                            } else {
                                console && console.log('挂载点不存在');
                            }
                        }
                    } else {
                        // pass
                    }
                }
            }

            if (isSam) {
                return;
            } else {
                // Event.addListener('onPriceReady', render);
                Event.addListener('onStockReady', function(res) {
                    var data = res && res.stock && res.stock.data
                    render(data)
                });
            }
        } catch (error) {
            //烛龙上报
            Tools.getJmfe({}, error, "屈臣氏电子会员卡异常", 300) 
        }

    }

    function setWatsonsCard2(cfg) {
        var isSam = (cfg.specialAttrs.join('#').indexOf('isSamGoods') !== -1);

        function render(data) {
            var __html = '\
                <div class="watsons" id="J_watsons">\
                    <span class="watsons__logo"></span>\
                    <span class="watsons__text">屈臣氏会员卡，</span>\
                    <a class="watsons__link watsons__link--mr18" href="//cartv.jd.com/item/200117505809.html" target="_blank">点击购卡&gt;&gt;</a>\
                    <span class="watsons__text">已有会员卡？请</span>\
                    <a class="watsons__link watsons__link--mr3" href="//bind.jd.com/watsons/watsonsIndex.html" target="_blank">点击绑定</a>\
                    <span class="sprite-question"></span>\
                </div>';
            $('#J_watsons').remove();
            var price = data.price;
            if ((price.up === 'pp' || price.up === 'tpp') && (price.pp || price.tpp)) {
                // jdPlus
            } else {
                var shopId = cfg.shopId;
                var catLevel3 = cfg.cat[2];
                if (shopId == '41106') {
                    if (catLevel3 != 15655) {
                        var $mount = $('.J-summary-price .dd');
                        if ($mount.length > 0) {
                            var $watsons = $(__html);
                            $watsons.find('.sprite-question').ETooltips({
                                autoHide: true,
                                close: false,
                                content: '屈臣氏电子会员卡，全国门店及京东屈臣氏官方旗舰店都可以使用，激活后可享受屈臣氏会员相关权益。',
                                width: 250,
                                pos: 'bottom',
                                zIndex: 10
                            });
                            $mount.append($watsons);
                        } else {
                            console && console.log('挂载点不存在');
                        }
                    }
                } else {
                    // pass
                }
            }
        }

        if (isSam) {
            return;
        } else {
            Event.addListener('onPriceReady', render);
        }

    }

    /**
     * 中介房源、企业房源的管家联系方式
     * @param {Object} cfg
     */
    function setButlerContactInfo(cfg) {
        var category3 = cfg.cat[2];
        if (
            category3 == 15118 ||
            category3 == 15119
        ) {
            $.ajax({
                url: '//api.m.jd.com/api',
                data: {
                    appid: 'nb_rent_house_yfb',
                    functionId: 'rent_getbrokerInfoBySkuId',
                    body: '{"skuId":'+ cfg.skuid + '}'
                },
                dataType: 'jsonp',
                jsonp: 'jsonp',
                success: function (res) {
                    var phoneNumber = res && res.data && res.data.extensionPhone;
                    if (phoneNumber) {
                        var __html = '<div style="font-size: 14px;color: #e4393c;padding: 10px;"> \
                            <span style="color: #666;font-weight: bold;margin-right: 10px;">联系管家</span>{0}</div>';
                        $('.summary.summary-first').append($(__html.format(phoneNumber)));
                    }
                },
                error: function () {
                    console && console.error('房产管家联系方式获取失败；');
                }
            });
        }
    }

    /// 设置药品的过期日期
    function setDrugExpiredDate(cfg) {
        /**
         * 渲染药品过期信息
         * @param {jQuery} $mount
         * @param {String} date
         */
        function setDrugExpiredInfo($mount, date) {
            if ($mount.length) {
                if (typeof date === 'string') {
                    $('.dd', $mount).html(date);
                    $mount.show();
                } else {
                    $mount.hide();
                }
            }
        }

        var $mount = $('#yiyao-expiryDate');

        if ($mount.length == 0 ) { return }

        // var DATE_REGEXP = /(\d+)-(\d+)-\d+\s+\d+:\d+:\d+/;

        Event.addListener('onStockReady', function(data) {
            var validityPeriod = data && data.stock && data.stock.data && data.stock.data.validityPeriod || ""

            if (validityPeriod) { // 药品过期信息
                setDrugExpiredInfo($mount, validityPeriod);
            } else {
                $mount.hide();
            }
        });
    }
    
    // 大家电议价,可议价
    function setYiJia (cfg) {
        try {
            function setTips($el) {
                var content =
                    '<ul>\
                <li>1、砍价是针对有“去砍价”标签的商品进行的交易，无“去砍价”标签商品不参与砍价；</li>\
                <li>2、可通过点击“去砍价”标识咨询在线客服或者JIMI机器人参与砍价；</li>\
                <li>3、砍价成功后，请您在半个小时内进行下单完成结算，否则砍价将失效。</li>\
                </ul>'
                if (cfg.isPop) {
                    content =
                        '<ul>\
                    <li>可通过点击“去砍价”标签，与在线客服进行沟通参与砍价</li>\
                    </ul>'
                }
    
                $el.find('.hy-btype-tips').ETooltips({
                    close: false,
                    content: content,
                    width: 270,
                    pos: 'bottom',
                    zIndex: 30, // 右侧吸顶时 z-index 为 30
                    x: -7
                })
            }
    
            function openContact() {
                // var $el = pageConfig.IMContact.$el.find('[data-seller]')
                var $el = $(".customer-service").find('[data-seller]')
                var domain = $el.attr('data-domain') || 'chat.jd.com'
                if (cfg.isPop) {
                    pageConfig.IMContact.open(domain, {
                        entry: 'pop_web_itemkanjia',
                        venderId: pageConfig.product.venderId || 0
                    })
                } else {
                    window.open(
                        '//jimi.jd.com/index.action?source=webproductkanjia&c3=' +
                        pageConfig.product.cat[2] +
                        '&productId=' +
                        pageConfig.product.skuid,
                        '咨询jimi'
                    )
                }
            }
            var $yijia = $('.J-yijia')
            if (!$yijia.length) {
                $yijia.hide()
                return false
            }
    
            Event.addListener('onStockReady', function(data) {
                var bargaining = false;
                var data = data && data.stock && data.stock.data;
                if(data.price){
                    bargaining = data.price.hagglePromotion;
                }
                if (cfg.isPop || bargaining) {
                    $yijia
                        .show()
                        .find('a')
                        .eq(0)
                        .unbind('click')
                        .bind('click', openContact)
    
                    setTips($yijia)
                } else {
                    $yijia.hide()
                }
            });
        } catch (error) {
            //烛龙上报
            Tools.getJmfe({}, error, "大家电议价,可议价异常", 300) 
        }
       
    }
    /// 51车金融项目引流
    function set51Tips(cfg) {
        /**
         * 获取商品扩展属性
         * @param {Object} params
         * @param {Function} onSuccess
         * @param {Function} onError
         */
        function getExtendInfo(params, onSuccess, onError) {
            var params = $.extend({
                appid: 'getDrugExpireInfo',
                functionId: 'car_finance_queryNewAttributeSetting',
                body: ''
            }, params);

            return $.ajax({
                url: '//api.m.jd.com/api',
                data: params,
                dataType: 'jsonp',
                jsonp: 'jsonp',
                timeout: 3000,
                success: function() {
                    if (typeof onSuccess === 'function') {
                        onSuccess.apply(null, arguments);
                    }
                },
                error: function() {
                    if (typeof onError === 'function') {
                        onError.apply(null, arguments);
                    }
                }
            });
        }

        cfg = cfg || {};

        var $mount = $("#J_51Tips");
        if ($mount.length == 0 ) {
            return;
        }

        var c1 = cfg.cat && cfg.cat[0] ?
            cfg.cat[0] : -1;

        if (c1 == 12379) {
            getExtendInfo({
                body: '{"skuId":' + cfg.skuid + '}'
            }).
            done(function(res){
                res = res || {};
                if (res.object == 124753) {
                    var img = '<img style="position:absolute; top:12px; left:18px; width:75px; height:75px" src="//qrimg.jd.com/'+
                        encodeURIComponent('https://wqs.jd.com/pingou/item.shtml?sku=' +
                        cfg.skuid +'&ptag=17009.11.1') +'-150-0-4-0.png" alt="二维码加载失败，请尝试刷新页面"/>';
                    var __html = "<div style='position:relative;display:block;width:400px;height:99px;background:url(//img13.360buyimg.com/imagetools/jfs/t1/102475/9/68/36147/5da6d533E91626f11/7d4b57f88439234d.png) no-repeat'>" + img + "</div>"
                    $(".dt", $mount).html(__html);
                    $mount.show();
                    $("#InitCartUrl,#InitCartUrl-mini,#choose-thmd,.choose-amount").remove();
                } else {
                    $mount.hide();
                }
            }).
            fail(function(xhr, status, error){
                $mount.hide();
            });
        } else {
            $mount.hide();
        }
    }

    // plus专享购 https://cf.jd.com/pages/viewpage.action?pageId=235979591
    function setPlusLimit(cfg) {
        /**
         * 添加提示条
         * @param {String} text
         */
        function addTipBar(text) {
            var $elem = $("#J_TipBar");
            var innerHTML = '{0} \
                <i onclick="$(\'#J_TipBar\').remove()" class="sprite-close"></i> \
           '.format(text);

            if ($elem.length == 0) {
                var __html = "<div class='DJD-tips' id='J_TipBar'>" + innerHTML + "</div>";
                $(".choose-btns-wrapper").prepend(__html);
            } else {
                $elem.html(innerHTML);
            }
        }

        /**
         * 移除提示条
         */
        function removeTipBar() {
            $("#J_TipBar").remove();
        }

        /**
         * 隐藏双价格楼层
         */
        function hideTheSecondPriceFloor() {
            var arr = [
                ".J-sam-price",
                ".J-plus-price",
                ".J-fans-price",
                "#J_NewcomerPrice",
                "#J_StudentPrice"
            ];
            $(arr.join()).hide();
        }

        var cfg = (window.pageConfig &&
            window.pageConfig.product) || {};


        /**
         * 增加“开通PLUS立即购买”按钮
         */
        function addJoinPlusButton() {
            // 增加“开通PLUS立即购买”按钮
            $('#InitCartUrl,#InitCartUrl-mini').each(function () {
                var $this = $(this);
                var id = $this.attr("id");
                var $button = $("<a class='J_JoinPlus' href='//plus.jd.com/index'>开通PLUS立即购买</a>").
                    addClass(id.indexOf("mini") == -1 ? "btn-buyplus btn-lg" : "btn-primary btn-buyplus");
                $this.after($button).hide();// 此处没有hide也隐藏加入购物车按钮
            });

            // 根据库存状态修改“开通PLUS立即购买”按钮的状态
            function toggleButtonState() {
                var havestock = cfg.havestock;
                $(".J_JoinPlus").toggleClass("btn-disable", !havestock).
                    attr("href", !!havestock ? "//plus.jd.com/index" : "#none");
                // $("#InitTradeUrl").hide()// 开通PLUS立即购买按钮不需要展示立即购买 
            }
            toggleButtonState();
            Event.addListener('onStockReady', toggleButtonState);
            require.async("JDF_UNIT/event/1.0.0/event.js", function (event) {
                event.on("loginSuccessByIframe", callStock);
            });
            //addJoinPlusButton = function() {}
        }

        /**
         * 切换“加入购物车”按钮和“开通PLUS立即购买”按钮
         */
        function toggleButton(flag) {
            var Cart = cfg.addToCartBtn;
            var $joinPlusButton = $(".J_JoinPlus");
            var $baitiao = $("#choose-baitiao");
            $joinPlusButton.toggle(!flag);
            // $("#InitTradeUrl").hide()
            if(flag){
                Cart.show()
                $("#InitTradeUrl").show()
            }else{
                Cart.hide()
                $("#InitTradeUrl").hide()
            }
            // flag ? Cart.show() : Cart.hide();
            setTimeout(function () {
                !flag && $baitiao.hide();
            }, 100);
            
            Event.fire({
                type: 'onBtnChange'
            })
        }

        /**
         * 增加Plus专项购氛围banner
         * @param {String} url
         * @param {String} selector
         */
        function handleBanner(url, selector) {
            /*if (cfg.isYuYue || cfg.isYuShou ) {
                window.location = G.modifyURL(
                    location.href,
                    { query: { yuyue: '0'}
                });
            }*/

            $(["#banner-miaosha",
                "#banner-shangou",
                "#J_atmosphere_banner",
                "#yuyue-banner",
                "#pingou-banner",
                "#pingou-banner-new",
                "#banner-enterpriseBuy"].join()).hide();
            PB.addAtmosphereBanner(url, selector);
            // handleBanner = function () {};
        }

        /**
         * 去掉Plus专项购氛围banner
         * @param {String} url
         * @param {String} selector
         */
        function hideBanner(url, selector) {
            $(["#banner-miaosha",
                "#banner-shangou",
                "#J_atmosphere_banner",
                "#yuyue-banner",
                "#pingou-banner",
                "#pingou-banner-new"].join()).show();

            $("#J_atmosphere_banner_plus").remove();
        }
        /**
         * 判定当前sku所属店铺是否是Plus会员店
         * @returns {Boolean}
         */
        function isPlusMemberStore() {
            var venderId = cfg.venderId + "";
            var arr = Conf.get("GLOBAL.PLUS.memberStore") || ["1000281625"];
            return $.inArray(venderId, arr) !== -1;
        }
        function onPromReady(data) {
            var data = data && data.stock && data.stock.data
            var isPlusLimit = false;
            try {
                isPlusLimit = (data.promotion.limitBuyInfo.resultExt.isPlusLimit == 1);
            } catch (err) {
                isPlusLimit = false;
            }
            var isPlusMember = isPlusMemberStore();
            // var isPlus = isPlusMember ? Promotions.isRealPlus : Promotions.isRealzxPlus;
            var isPlus = data.stockInfo.isPlus;
            var isBybtInfo = data && data.bybtInfo && data.bybtInfo.bybt;//百亿补贴标识
            if (isPlusLimit || isPlusMember) {// 专享购或者会员店
                if (!cfg.unSupportedArea && cfg.havestock && !isBybtInfo) {// 地区可售和有货 展示专享购banner条 无则失效以及有货状态 百亿补贴腰带优先级最高
                    var text=isPlusMember ?
                    "本商品为PLUS会员店商品，加入PLUS会员即可下单～" :
                    "加入PLUS会员即可获得本商品抢购资格，库存有限，抢完即止~";

                    if(data.beltBanner){//腰带系统下发优先展示
                        var url = data.beltBanner
                    }else{
                        var url = isPlusMember ?
                        "//m.360buyimg.com/cc/jfs/t1/117213/8/1469/27294/5e9825d1E08d290b5/fa81581681dba3a8.png" :
                        "//img14.360buyimg.com/imagetools/jfs/t1/113418/21/9492/25909/5ed8a23bE59c3eec6/6cd2c9282bf901c5.png";
                    }
                    
                    $(".J_JoinPlus").remove();
                    // handleBanner(url, "#J_atmosphere_banner_plus");     
                    hideTheSecondPriceFloor();
                    addJoinPlusButton();
                    Tools.checkLogin().done(function (res) {
                        res = res || {};
                        var data = res.Identity || {};
                        var isLogin = data.IsAuthenticated;
                        isLogin && isPlus ?
                            removeTipBar() :
                            addTipBar(text);
                        toggleButton(isLogin ? isPlus : false);
                    }).fail(function () {
                        addTipBar(text);
                        toggleButton(false);
                    });
                } else {
                    // 清理 + 还原
                    removeTipBar()
                    hideBanner();
                    $(".J_JoinPlus").remove();
                    $('#InitCartUrl,#InitCartUrl-mini').show();
                }
            }
        }
        // 不是预约走专享购和会员店
        if (!cfg.isYuYue) {
            Event.addListener("onStockReady", onPromReady);
        }
    };
    // 设置国补引导条
    function setStateSubsidyTips() {
        /**
         * 添加提示条
         * @param {String} text
         */
        function addTipBar(text, stateSubsidy) {
            var $elem = $("#J_TipBar");
            var goBtn = '' // 去领取按钮
            // stateSubsidy.rightLink = 'http://jd.com'
            if (stateSubsidy.rightLink) {
                goBtn = "<a href='" + stateSubsidy.rightLink + "' target='_blank'>立即领取<img src='https://img12.360buyimg.com/imagetools/jfs/t1/284952/20/27180/366/680f70cfF077f3af0/d1e80428664ebb09.png'></a>"
            }
            if ($elem.length == 0) {
                var cursorStyle = goBtn ? "style='cursor: pointer'" : ''
                var __html = "<div class='DJD-tips state-subsidy' id='J_TipBar' "+ cursorStyle +" >" + text + goBtn + "</div>";
                $(".choose-btns-wrapper").prepend(__html);
            } else {
                $elem.html(text);
            }
        }

        /**
         * 移除提示条
         */
        function removeTipBar() {
            $("#J_TipBar").remove();
        }
        Event.addListener("onStockReady", function(data) {
            var stock = data.stock;
            var beltInfo = stock && stock.data && stock.data.beltBannerInfo;
            var stateSubsidy = stock && stock.data && stock.data.govSupportInfo;
            var noBelt = !beltInfo || (beltInfo && (beltInfo.type != '11')) // 没国补腰带
            var tips = stateSubsidy && stateSubsidy.bottomText // 有国补引导条
            var govSupportInfo = stock.data && stock.data.govSupportInfo || {}; // 国补数据

            // 获取实验数据
            var abData = stock.data && stock.data.abData;
            // hit=1：代表需要调用资格领取弹层; hit=0：取消调用弹层，维持跳转链接逻辑
            var gbHit = abData
                && abData.gov_iframe
                && abData.gov_iframe.keyParamMap
                && abData.gov_iframe.keyParamMap.hit;
            // 开关
            var govAb = stock.data && stock.data.ext && stock.data.ext.govAb
            var noSubsidyUrl = govSupportInfo.noSubsidyUrl || '//gov-subsidy.jd.com'
            
            // 显示国补引导条，点击弹层领取
            if((gbHit == 1 || govAb)) {
                
                if(noBelt && tips){
                    removeTipBar()
                    Guobu.addTipBar('<div>' + tips + '</div>', stateSubsidy)
                    Guobu.bindTipBarEvent(noSubsidyUrl)
                } else {
                    removeTipBar()
                }
            } 
            // 执行原逻辑
            else {
                if(noBelt && tips) {
                    removeTipBar()
                    addTipBar('<div>' + tips + '</div>', stateSubsidy)
                }
            }

            
        });
    }
     // 大商超厂直商品定向企业销售PRD：https://joyspace.jd.com/pages/8NvsYSDkaVnsPSuPFEyF?applyId=2919944&jdme_router=jdme%3A%2F%2Frn%2F201909020601%3FrouteTag%3Dpages%26rnStandalone%3D2%26page_id%3D8NvsYSDkaVnsPSuPFEyF%26applyId%3D2919944
    function setEnterpriseLimit() {

        /**
         * 添加提示条
         * @param {String} text
         */
        function addTipBar(text) {
            var $elem = $("#J_TipBar");
            var innerHTML = '{0} \
                <i class="sprite-close"></i> \
            '.format(text);

            if ($elem.length == 0) {
                var __html = "<div class='DJD-tips' id='J_TipBar'>" + innerHTML + "</div>";
                $(".choose-btns-wrapper").prepend(__html);
            } else {
                $elem.html(innerHTML);
            }
        }

        Event.addListener('onStockReady', function(data){
            var data = data.stock;
            var limitBuyInfo = data && data.data && data.data.limitBuyInfo;

            if(limitBuyInfo && limitBuyInfo.enLimitBuy){ // true则展示黄条，屏蔽按钮等（只屏蔽采购清单按钮，其他逻辑包在isStock里面了）
                
                var enLaXinButtonJumpUrl = limitBuyInfo.enLaXinButtonJumpUrl || ''
                var enLaXinButtonText = limitBuyInfo.enLaXinButtonText || ''

                // 新版逻辑，引导企业注册按钮
                if(enLaXinButtonJumpUrl && enLaXinButtonText) {
                    $("#btn-inventory").remove() // 删除采购清单按钮
                    $(".J_choose_btn").html('<a href="'+ enLaXinButtonJumpUrl +'" class="btn-special2 btn-lg">'+ enLaXinButtonText +'</a>')
                    $('#InitCartUrl,#InitCartUrl-mini').remove(); // 屏蔽加入购车按钮
                } 
                
                // 兼容旧逻辑，支持代码回滚
                else {
                    addTipBar("本商品仅限企业会员购买")
                    $("#btn-inventory").remove() // 删除采购清单按钮
                    $("#InitCartUrl").after('<a href="#none" class="btn-special1 btn-lg btn-disable">加入购物车</a>')
                    $('#InitCartUrl,#InitCartUrl-mini').remove(); // 屏蔽加入购车按钮
                }
                
                
            }
        })
    }

    function setFixedNav($popbox) {
        // $('#GLOBAL_FOOTER').remove()
        var informationWrap = $(".information-wrap");
        var informationBottom = $('.p-choose-wrap') // 右侧底部区域，下架商品无此元素
        var leftMaskIsAppend = false;
        var $letMaskDom = $('.left-mask-line');
        if (!$letMaskDom.length) {
            $('body').append('<div class="left-mask-line" style="display:none"></div>');
            $letMaskDom = $('.left-mask-line');
        }
        if(informationWrap && informationWrap.length > 0){
            var offsetTop = 0
            var originHeight = 0
            $popbox.scroller({
                delay: 0,
                end: $('#footer-2024').children().length == 0 ? $('#GLOBAL_FOOTER') : $('#footer-2024'),// 医药使用 GLOBAL_FOOTER 作为 footer
                onStart: function() {

                    if (!leftMaskIsAppend) {
                      leftMaskIsAppend = true;
                      $letMaskDom.show();
                    }
                    
                    var footerTop = $('#footer-2024').offset().top - $(document).scrollTop();
                    if ($('#footer-2024').children().length == 0) { // 医药使用 GLOBAL_FOOTER 作为 footer
                        footerTop = $('#GLOBAL_FOOTER').offset().top - $(document).scrollTop();
                    }
                    var wrapHeight = informationWrap.height() > window.innerHeight ? window.innerHeight : informationWrap.height();
                    var left = $('.itemInfo-wrap')[0].getBoundingClientRect().left
                    
                    if (footerTop < wrapHeight + 68) { // 吸底
                        informationWrap.removeClass('pro-detail-hd-fixed'); // 内容吸顶开始
                        informationWrap.removeClass('fix-bottom'); // 右侧内容显示完全
                        informationWrap.addClass('bottom-fixed'); // 左侧内容展示完全
                        informationWrap.css('left', 'auto')
                        informationWrap.css('height', 'auto')
                    } else { // 吸顶
                        informationWrap.addClass('pro-detail-hd-fixed');
                        informationWrap.removeClass('bottom-fixed');
                        offsetTop = offsetTop || informationWrap.offset().top
                        originHeight = informationWrap.hasClass('fix-bottom') ? originHeight : informationWrap.height()
                        var scrollTop = informationWrap.scrollTop()
                        var diff = originHeight - scrollTop - informationWrap.height() // 右侧用户向上滚动的距离
                        informationWrap.css('left', left)
                        if($(document).scrollTop() + window.innerHeight + 1 > originHeight + offsetTop - diff) { // 右侧滚动到底部
                            
                            informationWrap.addClass('fix-bottom');
                            informationWrap.css('overflow', 'scroll') // 右侧滚动并且吸顶时，设置为可滚动，因为默认 overflow 是 visible
                            if (informationWrap.height() > window.innerHeight - 16) {// 内容区域大于屏幕高度
                                informationWrap.css('height', window.innerHeight - 16)
                                informationWrap.scrollTop(originHeight - window.innerHeight + 16)
                            }
                            // informationWrap.css('paddingTop', $(document).scrollTop() + window.innerHeight - informationWrap.height() - top)
                            // console.log('fix');
                            
                        } else {
                            // console.log('static');
                            
                            informationWrap.removeClass('fix-bottom');
                            informationWrap.css('height', 'auto')
                            informationWrap.css('overflow', 'visible')
                        }
                    }
                },
                onEnd: function() {
                    leftMaskIsAppend = false;
                    $letMaskDom.hide();

                    informationWrap.removeClass('pro-detail-hd-fixed');
                    informationWrap.removeClass('bottom-fixed');
                    informationWrap.css('height', 'auto')
                    informationWrap.removeClass('fix-bottom')
                }
            });
        }
        // 窗口变化时调整位置及高度
        $(window).resize(setLeftAndHeight)

        function setLeftAndHeight() {
            if (informationWrap.hasClass('bottom-fixed')) return
            var left = $('.itemInfo-wrap')[0].getBoundingClientRect().left
            informationWrap.css('left', left)
            if ($(".information-wrap").css('overflow') == 'scroll') { // 右侧吸顶时，窗口大小改变，动态改变右侧高度
                informationWrap.css('height', window.innerHeight - 16)
            }
        }
    }

    function init(cfg) {
        

        // if(!cfg.isFeeType || G.specialAttrs["isHyj"] == 1){//不是合约机类目 或 有合约机标的
        //     // 初始化促销
        //     Promotions.init();
        // }

        Promotions.init();
        document.getElementsByTagName("html")[0].className="root61";
        
        // plus专享购 https://cf.jd.com/pages/viewpage.action?pageId=235979591
        setPlusLimit(cfg);
        // 国补到手价黄条
        setStateSubsidyTips()
        // 到手价展示新
        setFinalPrice(cfg);
        // Event.addListener('onStockReady', function(res){
        //     var res = res.stock;
        //     var isPNew = res && res.data && res.data.isPNew;
            // 双价格新老逻辑开关：isPNew: false 表示新逻辑，按新结构处理 ，true表示老逻辑
        if(!cfg.isPNew){// 双价格新老数据切量
            // sam 双价格
            setSamPrice(cfg);
            // plus 会员价双价格
            setPlusPrice(cfg);
            // 企业价双价格
            // setFirmPrice(cfg);
            // 重逢双价价
            setMeetPrice(cfg);
            // 屈臣氏会员卡
            setWatsonsCard(cfg);
            // 粉丝价
            // fansPrice(cfg);
            // 专享价
            setUserPrice(cfg);
            if (!cfg.isYuShou) {
                Event.addListener('onPriceReady', function(data) {
                    setNewcomerPirce(data.price && data.price.doublePrice); // 新人价
                    setFirstNewcomerPirce(data.price && data.price.doublePrice); // 首单新人价
                    setStudentPirce(data.price && data.price.doublePrice);  // 学生价
                    setEnterpriseNewcomerPirce(data.price && data.price.doublePrice) //企业新人价
                    setbdhbPirce(data.price) //国际跨境本地货币展示
                });
            }
        }else{
            // sam 双价格
            setSamPrice2(cfg);
            // plus 会员价双价格
            setPlusPrice2(cfg);
            // 企业价双价格
            // setFirmPrice2(cfg);
            // 重逢双价价
            setMeetPrice2(cfg);
            // 屈臣氏会员卡
            setWatsonsCard2(cfg);
            // 粉丝价
            fansPrice2(cfg);
            // 专享价
            setUserPrice2(cfg);
            if (!cfg.isYuShou) {
                Event.addListener('onPriceReady', function(data) {
                    setNewcomerPirce2(data.price && data.price.doublePrice); // 新人价
                    setFirstNewcomerPirce2(data.price && data.price.doublePrice); // 首单新人价
                    setStudentPirce2(data.price && data.price.doublePrice);  // 学生价
                    setEnterpriseNewcomerPirce2(data.price && data.price.doublePrice) //企业新人价
                    setbdhbPirce(data.price) //国际跨境本地货币展示
                });
            }
        }
        
        // })

        
        


        // 其他双价格统一模版展示
        setAllDoublePrice();
        // 企业团购弹层
        setFirmbuyPrice(cfg);

        // 拼购价,拼购接口返回
        setPinGouPrice(cfg);
        
        //大家电议价,可议价
        setYiJia(cfg);
        // 通信币
        setTXB(cfg);

        // 获取价格
        if (cfg.cat[1] == 5276 || cfg.specialAttrs.join(',').indexOf('LeaseType-3') > -1 ) {
            //免费电子书     租赁商品 不获取价格
        }else{
            getPrice(cfg)
        }
        
        // 获取评论数量
        getCommentMeta(cfg);

        // 获取购买指数
        getBuyRate(cfg);

        Event.addListener('onStockReady', onStockReadyHandler);

        // 设置秒杀、闪购的Banner
        //setBanner(cfg);
        
        // 设置工业品批量阶梯价
        // stepPrice(cfg);
        // 2019春节不打烊
        // setBDYSkin(cfg);
        // 设置预告价
        setPreparePrice(cfg);
        // 设置主图腰带
        setBeltImg(cfg);
        // 中介房源、企业房源的管家联系方式
        setButlerContactInfo(cfg);
        setDrugExpiredDate(cfg);
        set51Tips(cfg);
        ///////////////// 首屏促销条 BEGIN /////////////////////
        // set618Skin(cfg);  //618氛围
        // setDoubleElevenSkin(cfg);
        // setBlackFridaySkin(cfg);
        // setDoubleTwelveSkin(cfg);
        // setNHJSkin(cfg);
        // set3CSkin(cfg);
        // // 蝴蝶结大促腰带 2019年2月25日00:00:00-2019年3月13日23:59:59
        // setButterflySkin(cfg);
        // set2020618Skin(cfg);
        // setHomeDecorationSkin(cfg);
        ///////////////// 首屏促销条 END //////////////////////

        //设置自营标
        setSkuStrategy(cfg)

        //拍拍二手价格
        // setPaiPaiPrice(cfg)

        if (G.onAttr('isLOC-1') || G.onAttr('isLOC')) {
            Event.addListener('onPriceReady', function(data) {
                if($("#J_JdContent").length) {
                    $('#J_JdContent').append('<div class="store-price-tips">具体价格请以最终选择的门店价格为准。</div>');
                    $('#J_JdContent').addClass('has-tips')
                }
            })
        }

        //大商超厂直商品定向企业销售
        setEnterpriseLimit(cfg);

        //调用实时大接口
        // getWareBusiness(cfg);
        // Global.init

        // 接腰带系统数据,统一处理腰带优先级，大促腰带，plus腰带比较特殊
        // 接入腰带系统 优先级：百补美妆>百补腰带>plus店铺腰带>plus用户腰带>预售>预约>秒杀>闪购>大促腰带>企业专属
        getBeltBanner();
        
        // 埋点统一上报
        // setProductdetailLog(cfg);
        initExposureData(cfg, setProductdetailLog)
        
        umc.start();

        setFixedNav($(".product-intro"));
        
    }

    module.exports.__id = 'prom'
    module.exports.init = init
    module.exports.Promotions = Promotions
    module.exports.getPrice = getPrice
})
