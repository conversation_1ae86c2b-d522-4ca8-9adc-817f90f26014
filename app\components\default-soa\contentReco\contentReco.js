define('MOD_ROOT/contentReco/contentReco', function(require, exports, module) {
    var Recommend = require('MOD_ROOT/common/tools/recommend');
    var trimPath      = require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var lazyload      = require('JDF_UI/lazyload/1.0.0/lazyload');
    var imgScroll     = require('MOD_ROOT/common/plugins/jQuery.imgScroll');
    var tools         = require('MOD_ROOT/common/tools/tools');
    var G = require('MOD_ROOT/common/core');

    var TPL_pop_book_hot = '\
         <div class="m-box-hd">\
            <h3 class="title">${ext.title}</h3>\
        </div>\
        <div class="m-box-bd">\
            <div class="m-switch book-rec-switch" >\
                <div class="m-switch-main">\
                    <ul class="m-switch-panel book-rec-list">\
                        {for item in data}\
                        <li class="m-sw-item fore${Number(item_index)+1}" data-clk="${item.clk}" data-push="${pageConfig[skuHooks].push(item.sku)}">\
                            <div class="p-img">\
                                <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">\
                                    <img height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" alt="${item.t}" data-lazy-img="${pageConfig.FN_GetImageDomain(item.sku)}{if ext.imgSize}${ext.imgSize}{else}n4{/if}/${item.img}">\
                                </a>\
                            </div>\
                            <div class="p-name ac"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
                            <div class="p-comment J-comm-${item.sku}">\
                                <a target="_blank" href="//item.jd.com/${item.sku}.html#comment" class="number"><em>0</em>条</a>\
                                <span class="good">(<em>97</em>%好评)</span>\
                            </div>\
                            <div class="p-price">\
                                <strong class="number J-p2-${item.sku}">￥${item.jp}</strong>\
                            </div>\
                        </li>\
                        {/for}\
                    </ul>\
                </div>\
                <div class="m-switch-page">\
                    <span class="m-switch-prev">上一页</span>\
                    <span class="m-switch-next">下一页</span>\
                </div>\
            </div>\
        </div>';

    function mLazyload($el, cb) {
        var $document = $(document);

        if ( $el.length > 0 ) {
            $document.lazyload({
                type: 'fn',
                source: $el,
                space: 200,
                onchange: cb
            });
        }
    }

    function init(pType) {
        return

        // 热门推荐
        var $hotReco = $('#J-hot-reco');

        // 热门关注
        var $hotFo = $('#J-hot-fo');

        var ridHotReco = null;
        var ridHotFo = null;

        if ( pType === 3 ) {
            ridHotReco = 104006;
            ridHotFo = 104007;
        }

        if ( pType === 4 ) {
            ridHotReco = 104024;
            ridHotFo = 104025;
        }

        mLazyload($hotReco, function () {
            var hotReco = new Recommend({
                $el: $hotReco,
                skuHooks: 'SKUS_hot_reco',
                template: TPL_pop_book_hot,
                ext: {
                    title: '热门推荐',
                    divId: "rmtjh-"
                },
                param: {
                    p: ridHotReco,
                    sku: pageConfig.product.skuid,
                    skus: pageConfig.product.skus || '',
                    lim: 18,
                    ck: 'pin,ipLocation,atw'
                },
                callback: function(hasData,r) {
                    var $target = this.$el;
                    var num = pageConfig.wideVersion ? 6 : 4;

                    if ( !hasData ) {
                        return this.$el.hide();
                    }

                    this.$el.lazyload({
                        type: 'img'
                    });

                    var $scroller = this.$el.find('.m-switch-main');
                    var $prev = this.$el.find('.m-switch-prev');
                    var $next = this.$el.find('.m-switch-next');
                    $scroller.imgScroll({
                        disableClass: 'disable',
                        disableClassPerfix: 'm-switch',
                        prev: $prev,
                        next: $next,
                        visible: num,
                        step: num
                    });

                    tools.commentMeta({
                        skus: pageConfig['SKUS_hot_reco'],
                        $el: $target,
                        onlyData: true,
                        callback: function(sku, r) {
                            $target.find('.J-comm-'+sku+' .number em').html(r.CommentCount);
                            $target.find('.J-comm-'+sku+' .good em').html(r.GoodRateShow);
                        }
                    });
                    try{
                        // 主图浮层初始化渲染
                        if(hasData){
                            var layerList = r.floatLayerList
                            if(layerList.length > 0){
                                tools.getPcSkuLayers(layerList, r.ext.imgWidth, r.ext.imgHeight, 'rmtjh-', "2")
                            }
                        }
                    }catch(e){
                        console.log("主图浮层初始化渲染",e)
                    }
                }
            });
        });

        mLazyload($hotFo, function () {
            var hotFo = new Recommend({
                $el: $hotFo,
                skuHooks: 'SKUS_hot_fo',
                template: TPL_pop_book_hot,
                ext: {
                    title: '热门关注',
                    divId: "rmgzh-"
                },
                param: {
                    p: ridHotFo,
                    sku: G.sku,
                    lim: 18,
                    ck: 'pin,ipLocation,atw'
                },
                callback: function(hasData) {
                    var $target = this.$el;
                    var num = pageConfig.wideVersion ? 6 : 4;

                    if ( !hasData ) {
                        return this.$el.hide();
                    }

                    this.$el.lazyload({
                        type: 'img'
                    });

                    var $scroller = this.$el.find('.m-switch-main');
                    var $prev = this.$el.find('.m-switch-prev');
                    var $next = this.$el.find('.m-switch-next');

                    $scroller.imgScroll({
                        disableClass: 'disable',
                        disableClassPerfix: 'm-switch',
                        prev: $prev,
                        next: $next,
                        visible: num,
                        step: num
                    });

                    tools.commentMeta({
                        skus: pageConfig['SKUS_hot_fo'],
                        $el: $target,
                        onlyData: true,
                        callback: function(sku, r) {
                            $target.find('.J-comm-'+sku+' .number em').html(r.CommentCount);
                            $target.find('.J-comm-'+sku+' .good em').html(r.GoodRateShow);
                        }
                    });
                    try{
                        // 主图浮层初始化渲染
                        if(hasData){
                            var layerList = r.floatLayerList
                            if(layerList.length > 0){
                                tools.getPcSkuLayers(layerList, r.ext.imgWidth, r.ext.imgHeight, 'rmgzh-', "2")
                            }
                        }
                    }catch(e){
                        console.log("主图浮层初始化渲染",e)
                    }
                }
            });
        });

    }

    return init;
});