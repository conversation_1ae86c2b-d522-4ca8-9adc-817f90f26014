define('MOD_ROOT/worldbuy/worldbuy', function(require, exports, module) {
    var G = require('MOD_ROOT/common/core');
    var cfg = (window.pageConfig &&
        window.pageConfig.product) || {};
    var promTips = function (cfg) {
        $('.d-tip').hover(function () {
            $(this).addClass('active')
        }, function () {
            $(this).removeClass('active')
        })
    }    
    /*全球购优化
     * 1.全球京选
     * 2.国旗icon和国家
     * 3.根据接口返回值，展示服务icon，文案为“服务”，hover后有具体描述
     *
     */
    function globalOptimization(cfg) {

        var task = {};
        task.showSeviceInfo = function (data){

            var $serviceTips = $('#serviceInfo');//
            var jingXuanTag = 'NOT JINGXUAN!';
            if(data.jingXuanVo) {
                jingXuanTag = 'JINGXUAN';
            }
            var result = (data.jingXuanVo) ? data.jingXuanVo.serviceList : data.iconInfoVoList;


            var _html = '';
            //_html +='<div class="service-info" id="serviceInfo">';
            _html +='<div class="dt">服 务</div>';
            _html +='<div class="dd clearfix">';
            _html +='    <ol class="tips-list">';

            var maxLength = (result.length > 4) ? 4 : result.length;

            for(var i=0; i<maxLength; i++){

                var content = result[i].content;
                var imgUrl = result[i].imgUrl;
                var title = result[i].title;

                _html += '<li class="global-service" data-tag="'+jingXuanTag+'">';
                _html += '<img src="'+ imgUrl +'" alt="">&nbsp;'+title;
                _html += '<div class="tips" style="display:none">';
                _html += '<i class="sprite-arrow"></i>';
                _html += '<div class="content">';
                _html += content;
                _html += '</div>';
                _html += '</div>';


            /*<div class="tips">
                    <i class="sprite-arrow"></i>
                    <div class="content">
                    <dl>
                    <dd>通过“定期购”购买商品，享<span class="J-discount">9.8</span>折优惠，免运费</dd>
                </dl>
                </div>
                </div>*/
                _html += '</li>';
            }

            //_html +='    <li><img src="//img13.360buyimg.com/cms/jfs/t5425/166/1057861151/1384/6289642c/590ad1abN8e9d4a14.png" alt="" data-info="京东全球购商家承诺该商品为100%海外正品，并提供“正品保障”服务，假一赔十。">正品保障</li>';
            _html +='    </ol>';
            // _html +='    <div class="tips-more">';
            // _html +='    <a href="//pro.jd.com/mall/active/HP79oZiLqnGKv5yBq9Ku6GkacGQ/index.html" class="view-more">查看详情<i class="more-arrow"></i></a>';
            // _html +='</div>';
            _html +='</div>';
            //_html +='</div>';


            $serviceTips.html(_html);


            $serviceTips.delegate('.global-service', 'mouseenter', function() {
                $(this).addClass('hover');
            });

            $serviceTips.delegate('.global-service', 'mouseleave', function() {
                var $this = $(this);
                $this.removeClass('hover')
            })

        };


        /*国旗icon和国家
        {
         "nationImgMap": {
         "m": "//img11.360buyimg.com/cms/jfs/t4765/105/1083155553/3095/50786ba9/58ec9befN64cad552.jpg"
         },
         "nationName": "澳大利亚"}*/
        task.showCountryFlag = function(data){
            var $countryFlagSpot = $('#countryFlagSpot');//<div id="countryFlagSpot" class="country-sign">
            if($countryFlagSpot.length === 0 || !data.nationImgMap){
                return;
            }
            var _html = '';
            _html += '<div class="country-sign">';
            _html += '<img class="country-map-icon" src="' + data.nationImgMap.m + '" />';
            _html += '<span class="country-name">&nbsp;' + data.nationName + '</span>';
            _html += '</div>';

            $countryFlagSpot.html(_html);

            //$('.itemInfo-wrap').prepend('<div>'+_html+'</div>');

        };

        //全球京选
        task.showGlobalJDRecommendTag = function(data){


            var $globalJDRecommend = $('#globalJDRecommendTag');

            if(!data.jingXuanVo || $globalJDRecommend.length === 0) {
                return;
            }

            $globalJDRecommend.addClass('global-jd-recommend').text('京选全球');

        };

        //税费信息
        task.showTaxInfo = function(data){

            // if(!data.taxTxt) return;

            var $globalTaxInfo = $('#summaryTallage');
            if($globalTaxInfo.length === 0) return;

            var $title = $('#summaryTallageTitle');
            var $content = $('#tax');
            var $detail = $('#summaryTallageDetail');

            var title = data.taxTitle.replace(/：/g,'');

            if(/[^\u0000-\u00FF]*/.test(title)){

                if(title.length === 2){
                    title = title.replace(/([^\u0000-\u00FF])([^\u0000-\u00FF])/,'$1'+'  '+'$2');
                }else{
                    title = title.replace(/([^\u0000-\u00FF])([^\u0000-\u00FF])([^\u0000-\u00FF])/,'$1'+' '+'$2'+' '+'$3');
                }

            }

            var content = data.taxContent;
            var detail = data.taxDetail + '<a href="//www.jd.hk/service/taxrate.html" target="_blank">查看详情&gt;&gt;</a>';

            detail = detail.replace(/\r\n/g,'<br>');

            $title.html(title);
            $content.html(content);
            $detail.html(detail);


        };




        //this.url = '//c.3.cn/globalBuy?skuId='+ pageConfig.product.skuid;
        //var _url = '//3.cn/globalBuy.txt?skuId='+ cfg.skuid;

        //全球购优化项目,如果税费的div不存在,则不做异步数据加载
        if($('#summaryTallage').length === 0) {
            return;
        }
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        var _url = host + '/globalBuy_v2?skuId='+ cfg.skuid + '&countryId=1' + '&platformId=1';

        $.ajax({
            url: _url,
            timeout: 1000,
            dataType: 'jsonp',
            data: {
                appid: 'item-v3',
                functionId: "pc_globalBuy_v2"
            },
            charset:'utf-8',
            cache: true,
            success: function(data){
                if (!data) {
                    return false
                }
                task.showCountryFlag(data);
                task.showTaxInfo(data);
                task.showGlobalJDRecommendTag(data);
                task.showSeviceInfo(data);
            },
            error: function(err){
                console.log(err);
            }
        });
    }  
    
    function userNotice(cfg) {
        var isOverseaPurchase = 0;
        if (G.onAttr("isOverseaPurchase-1")) {
            isOverseaPurchase = 1;
        } else if (G.onAttr("isOverseaPurchase-2")) {
            isOverseaPurchase = 2;
        } else if (G.onAttr("isOverseaPurchase-3")) {
            isOverseaPurchase = 3;
        }
        $('#nav-shop').after('<div class="w hide"><div class="notice-msg"><s></s><strong></strong></div></div>')
        $('.detail-content').before('<div class="notices"></div>');

        $.ajax({
            url: '//www.jd.hk/notice/getInfo.do',
            data: {
                platform: 1,
                type: isOverseaPurchase,
                category: cfg.cat[0]
            },
            dataType: 'jsonp',
            cache: true,
            scriptCharset: 'UTF-8',
            success: function(r) {
                var $worldbuyActivity = $("#worldbuy-activity");
                var $userNotice = $("#userNotice");
                if(r.resultCode == 1 && r.notices && r.notices.length) {
                    // if ($(".detail-bread-crumb").find(".fl").html().replace(/\s+/g,'') == "") {
                    //     $(".detail-bread-crumb").height(21);
                    // }
                    var crumbsNoticeResult = '';
                        // htmlNoticeResult = '';
                    for(var i in r.notices) {
                        if(r.notices[i].crumbsNotice) {
                            crumbsNoticeResult = r.notices[i].crumbsNotice;
                        }
                        // htmlNoticeResult += r.notices[i].htmlNotice;
                        if(r.notices[i].noticeType==0){// 如果为告消费者书(noticeType==0)放在商品介绍上方
                            $userNotice.append(r.notices[i].htmlNotice)
                        }else if(r.notices[i].noticeType==1){// 全球购活动(noticeType==1)
                            $worldbuyActivity.append(r.notices[i].htmlNotice)
                        }
                    }
                    if(crumbsNoticeResult) {
                        $(".notice-msg").parent(".w").removeClass("hide");
                        $(".notice-msg").find("strong").html(crumbsNoticeResult);
                    }
                    // $("#item-detail .notices").html(htmlNoticeResult);
                }
            }
        });
    }
    function init(cfg) {
        promTips()
        /*全球购优化
        * 1.全球京选
        * 2.国旗icon和国家
        * 3.根据接口返回值，展示服务icon，文案为“服务”，hover后有具体描述
        */
       globalOptimization(cfg)
       userNotice(cfg)
    }
    module.exports.init = init
})
