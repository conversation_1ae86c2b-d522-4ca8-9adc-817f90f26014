define('MOD_ROOT/common/core', function(require, exports, module) {
    // 商品页公用变量
    var g = {}
    var cfg = pageConfig.product

    // 常用变量
    g.sku = cfg.skuid

    g.cat = cfg.cat
    g.url = cfg.href
    g.src = cfg.src
    g.pType = cfg.pType
    g.name = cfg.name
    g.mdPerfix = cfg.pType == 1 ? 'CR' : 'GR'
    g.mbPerfix = cfg.pType == 1 ? '3C' : 'GM'
    g.mp = cfg.mp
    g.jp = cfg.jp

    g.orginSku = pageConfig.product.orginSkuid || g.sku

    g.wideVersion = pageConfig.wideVersion && pageConfig.compatible

    //上下柜状态 state:0、1、2、10；0:下柜，1：上柜，2：预发布，10：pop删除
    //g.itemDisabled = typeof pageConfig.product.warestatus !== 'undefined'
    //    && (pageConfig.product.warestatus === 0 || pageConfig.product.warestatus === 10);

    g.itemDisabled = cfg.isOver

    g.isPOPSku = function(sku) {
        return (
            (sku >= 1000000000 && sku <= 1999999999) ||
            (sku >= 10000000001 && sku <= 99999999999)
        )
    }

    // 商品类型判断
    var sku = g.sku
    g.isPop = g.isPOPSku(sku) //POP
    g.isFCS = cfg.fcs; //fcs
    g.isJd = !!g.isFCS || !g.isPop //自营
    g.isSelf = g.isJd //自营
    g.is3C =
        g.cat[0] == 652 ||
        g.cat[0] == 670 ||
        g.cat[0] == 737 ||
        g.cat[0] == 9987 //3C
    g.isCellphone = g.cat[2] == 655 || g.cat[2] == 6881 || g.cat[2] == 6882 //手机
    g.isCompleteMachine = g.cat[0] == 670 //电脑整机
    g.isVideo = g.cat[0] == 652 //摄影摄像
    g.isPopBook = g.isPop && g.cat[0] == 1713 //pop图书
    g.isSelfBook =
        (sku >= 10000000 && sku <= 19999999) ||
        (sku >= 110000000001 && sku <= 139999999999) //自营图书
    g.isBook = g.isPopBook || g.isSelfBook //图书
    g.isPopMvd =
        g.isPop && (g.cat[0] == 4051 || g.cat[0] == 4052 || g.cat[0] == 4053) //POP MVD
    g.isSelfMvd =
        (sku >= 20000000 && sku <= 29999999) ||
        (sku >= 140000000001 && sku <= 149999999999) //自营MVD
    g.isMvd = g.isPopMvd || g.isSelfMvd

    g.disableAddToCart = $('#InitCartUrl').hasClass('btn-disable')

    g.isArray = function(obj) {
        return Object.prototype.toString.call(obj) === '[object Array]'
    }
    g.isObject = function(obj) {
        return Object.prototype.toString.call(obj) === '[object Object]'
    }
    g.isEmptyObject = function(obj) {
        var name
        for (name in obj) {
            return false
        }
        return true
    }

    // 字段为空判断，支持null, undefined, "", []
    g.isNothing = function(obj) {
        if (g.isArray(obj)) {
            return obj.length < 1
        } else {
            return !obj
        }
    }
    g.getRandomArray = function(m, len) {
        len = len || 1
        m.sort(function() {
            return Math.random() - 0.5
        })
        return m.slice(0, len)
    }

    g.toFixed = function(number, precision) {
        var multiplier = Math.pow(10, precision + 1)
        var wholeNumber = Math.round(number * multiplier).toString()
        var length = wholeNumber.length - 1

        wholeNumber = wholeNumber.substr(0, length)
        return [
            wholeNumber.substr(0, length - precision),
            wholeNumber.substr(-precision)
        ].join('.')
    }

    g.originBuyUrl = $('#InitCartUrl').attr('href')

    g.formatPrice = function(price) {
        var priceNum = parseFloat(price)
        if (isNaN(priceNum) || priceNum <= 0) {
            return '暂无报价'
        } else {
            return priceNum.toFixed(2)
        }
    }

    g.discount = function(jp, mp, tpl) {
        tpl = tpl || '[{num}折]'
        if (typeof jp === 'undefined' || typeof mp === 'undefined') {
            return
        }
        jp = parseFloat(jp)
        mp = parseFloat(mp)

        if (!jp || !mp) {
            return ''
        }
        //float精度16
        var discount = Math.ceil((jp / mp * 100).toFixed(1))
        return discount ? tpl.replace('{num}', parseFloat(discount / 10)) : ''
    }

    g.triggerLazyImg = function($el, prefix) {
        $el.find('img').each(function() {
            var $this = $(this)
            var originSrc = $this.attr(prefix)

            if (!$this.attr('src')) {
                $this.attr('src', originSrc)
                $this.removeAttr(prefix)
                $this.removeClass('err-product loading-style2')
            }
        })
    }

    // 获取用户等级,原名getCustomerLevel
    g.getNewUserLevel = function(level) {
        switch (level) {
        case 50:
            return '注册用户'
        case 56:
            return '铜牌用户'
        case 59:
            return '注册用户'
        case 60:
            return '银牌用户'
        case 61:
            return '银牌用户'
        case 62:
            return '金牌用户'
        case 63:
            return '钻石用户'
        case 64:
            return '经销商'
        case 110:
            return 'VIP'
        case 66:
            return '京东员工'
        case -1:
            return '未注册'
        case 88:
            return '钻石用户'
        case 90:
            return '企业用户'
        case 103:
            return '钻石用户'
        case 104:
            return '钻石用户'
        case 105:
            return '钻石用户'
        }
        return '未知'
    }

    // 模拟发送请求
    g.sendRequest = function(src) {
        var img = new Image()
        img.setAttribute('src', src)

        img = null
    }
    // 手动添加新埋点
    g.clsLog = function(id, action, sku, csku, index) {
        var img = new Image()
        var src =
            '//mercury.jd.com/log.gif?t=rec.' +
            id +
            '&v=' +
            encodeURIComponent(
                'src=rec$action=' +
                    action +
                    '$enb=1$sku=' +
                    sku +
                    '$csku=' +
                    csku +
                    '$index=' +
                    (index || 0) +
                    '$expid=0'
            ) +
            '&m=UA-J2011-1&ref=' +
            encodeURIComponent(document.referrer) +
            '&random=' +
            Math.random()
        img.setAttribute('src', src)
    }

    // 触发某个特殊属性
    g.onAttr = function onAttr(attr, exist, notExist) {
        var attrs = cfg.specialAttrs
        var isStr = typeof attr === 'string'
        var len = attrs.length

        exist = exist || function() {}
        notExist = notExist || function() {}

        if (attrs && attrs.length) {
            for (var i = 0; i < len; i++) {
                // 字符串精准匹配，正则匹配
                if (isStr) {
                    if (attrs[i] === attr) {
                        exist(attrs[i])
                        return true
                    }
                } else {
                    if (attr.test(attrs[i])) {
                        exist(attrs[i])
                        return true
                    }
                }
            }
            notExist()
            return false
        }
        notExist()
        return false
    }

    g.specialAttrs = (function() {
        var cfg = window.pageConfig || {};
        var attrs = (cfg.product &&
            cfg.product.specialAttrs) || [];
        var dict = {};
        for (var i = 0, length = attrs.length;
            i < length && typeof attrs[i] === "string"; i++) {
            var arr = attrs[i].split('-');
            dict[arr[0]] = arr[1] != void(0) ? arr[1] : '1';
        }
        return dict;
    })();

    /**
     * 计算出购买组合价格
     */
    g.calPrice = function(opts) {
        var sku = opts.sku
        var input = opts.input
        var inputs = opts.$el.find('input[type="checkbox"]')
        var len = inputs.length
        var $el = opts.$el

        var targetJP = opts.targetJP
        var targetMP = opts.targetMP

        var jp = 0, mp = 0, count = 0, skus = []

        var callback = opts.callback || function() {}

        for (var i = 0; i < len; i++) {
            var currentInput = inputs.eq(i)
            var currSku = currentInput.attr('data-sku')
            var currJP = currentInput.attr('data-jp')
            var currMP = currentInput.attr('data-mp')

            currJP = parseFloat(currJP)
            currMP = parseFloat(currMP)

            if (currentInput.attr('checked')) {
                if (!isNaN(currJP)) {
                    jp += currJP
                } else {
                    return
                }
                if (!isNaN(currMP)) {
                    mp += currMP
                } else {
                    return
                }
                skus.push(currSku)
                count++
            }
        }

        targetJP.html('￥' + (jp > 0 ? jp.toFixed(2) : '暂无报价'))
        targetMP.html('￥' + (mp > 0 ? mp.toFixed(2) : '暂无报价'))

        callback(count, skus)
    }

    // 初始化小图滚动
    g.setScroll = function(selector) {
        var parent = typeof selector == 'string' ? $(selector) : $('body')

        parent.find('.p-scroll').each(function() {
            var scroll = $(this).find('.p-scroll-wrap'),
                next = $(this).find('.p-scroll-next'),
                prev = $(this).find('.p-scroll-prev')

            if (scroll.find('li').length > 4 && $.fn.imgScroll) {
                scroll.imgScroll({
                    showControl: true,
                    width: 30,
                    height: 30,
                    visible: 4,
                    step: 1,
                    prev: prev,
                    next: next
                })
                next.add(prev).show()
            }
        })
    }

    // 缩略图切换变大图
    g.thumbnailSwitch = function(
        thumbnails,
        desImg,
        resSize,
        currClass,
        evtType
    ) {
        var thumbnailList = thumbnails.find('img')
        var eType = evtType || 'mouseover'

        thumbnailList.bind(eType, function() {
            var $this = $(this)
            var $thisSrc = $this.attr('src')
            var resSrc = $thisSrc.replace(/\/n\d\//, resSize)

            desImg.attr('src', resSrc)
            thumbnails.removeClass('curr')
            $this.parent().addClass('curr')
        })
    }

    // 序列化url参数成JSON
    g.serializeUrl = function(url) {
        var sep = url.indexOf('?'),
            link = url.substr(0, sep),
            params = url.substr(sep + 1),
            paramArr = params.split('&'),
            len = paramArr.length,
            i,
            res = {},
            curr,
            key,
            val

        for (i = 0; i < len; i++) {
            curr = paramArr[i].split('=')
            key = curr[0]
            val = curr[1]

            res[key] = val && val.split('#')[0]
        }
        return {
            url: link,
            param: res
        }
    }

    // 收集 skus
    g.collectSkus = function($el, targetSelector, prefix) {
        var skus = []

        targetSelector = targetSelector || '.p-price strong'
        prefix = prefix || 'J-p-'

        $el.find(targetSelector).each(function() {
            var cName = $(this).attr('class')

            if (cName) {
                skus.push(cName.replace(prefix, ''))
            }
        })
        return skus
    }

    g.getNum = function() {
        return Number($('#buy-num').val())
    }

    g.Countdown = {
        init: function(seconds, callback) {
            this.seconds = seconds
            this.timer = null

            this.callback = callback || function() {}

            this.loopCount()
        },
        loopCount: function() {
            var _this = this
            var first = _this.formatSeconds(_this.seconds)

            _this.callback(first)
            this.timer = setInterval(function() {
                var res = _this.formatSeconds(_this.seconds)

                if (res.d === 0 && res.h === 0 && res.m === 0 && res.s === 0) {
                    clearInterval(_this.timer)
                } else {
                    _this.seconds--
                }

                _this.callback(res)
            }, 1000)
        },
        formatSeconds: function(seconds) {
            var days = Math.floor(seconds / 86400)
            var hours = Math.floor(seconds % 86400 / 3600)
            var minutes = Math.floor(seconds % 86400 % 3600 / 60)
            var seconds = seconds % 86400 % 3600 % 60

            return {
                d: days,
                h: hours,
                m: minutes,
                s: seconds
            }
        }
    }

    /**
     * is
     * @param {any} type 期望的数据类型的值
     * @param {any} val  需要校验的值
     * @returns {Boolean}
     */
    function is(type, val) {
        var toString = ({}).toString;
        return toString.call(type) === toString.call(val);
    }

    /**
     * serializeURL
     * scheme:[//[user[:password]@]host[:port]][/path][?query][#fragment]
     * @param {String} input
     * @return {Object}
     */
    function serializeURL(input) {
        if (typeof input === 'string') {
            var url = input;
            var URL_REGEXP = /(\w+):(?:\/\/)?(?:([^:]+)(?::(.+))?@)?([^/:?#]+)(?::(\d*))?([^?#]*)([^#]*)(.*)/;
            var KEY_REGEXP = /(.+)\[(.+)?\]/;
            if (URL_REGEXP.test(url)) {
                var dict = {};
                var arr = URL_REGEXP.exec(url);
                dict.protocol = arr[1] || '';
                dict.username = arr[2] || '';
                dict.password = arr[3] || '';
                dict.host = arr[4] || '';
                dict.port = arr[5] || '';
                dict.path = arr[6] || '';
                dict.filename = dict.path.split('/').slice(-1).join('');
                dict.fragment = arr[8] || '';
                var params = arr[7] ? arr[7].slice(1).split('&'): [];
                var length = params.length;
                if (length > 0) {
                    dict.query = {};
                    for (var i = 0; i < length; i+=1) {
                        var l = params[i].split('=')
                        var key = l[0];
                        var value = l[1];
                        if (KEY_REGEXP.test(key)) {
                            var r = KEY_REGEXP.exec(key);
                            var k = r[1];
                            var p = r[2];
                            if (!!p) {
                                if (!dict.query[k]) {
                                    dict.query[k] = {}
                                }
                                if (({}).toString() === ({}).toString.call(dict.query[k])) {
                                    dict.query[k][p] = value;
                                } else {
                                    throw 'URL\'s params part has something wrong.';
                                }
                            } else {
                                if (!dict.query[k]) {
                                    dict.query[k] = [];
                                }
                                if (({}).toString.call([]) === ({}).toString.call(dict.query[k])) {
                                    dict.query[k].push(value);
                                } else {
                                    throw 'URL\'s params part has something wrong.';
                                }
                            }
                        } else {
                            dict.query[key] = value;
                        }
                    }
                } else {
                    dict.query = {};
                }
                return dict;
            } else {
                throw 'The URL is illegal.';
            }
        } else {
            throw 'Param\'s data type is String.';
        }
    }

    /**
     * buildURLQueryString
     * @param {Object} params
     * @returns {String}
     */
    function buildURLQueryString(params, flag) {
        if (is({}, params)) {
            var arr = [];
            for (var key in params) {
                var value = params[key];
                if (is([], value)) {
                    for (var i = 0, length = value.length; i < length; i+=1) {
                        if (is([], value[i]) || is({}, value[i])) {
                            var tmp = {};
                            tmp[key +'['+ i +']'] = value[i];
                            arr.push(buildURLQueryString(tmp, true));
                        } else {
                            arr.push(key + '[]=' + value[i]);
                        }
                    }
                } else if (is({}, value)) {
                    for (var k in value) {
                        if (is([], value[k]) || is({}, value[k])) {
                            var tmp = {};
                            tmp[key + '['+ k +']'] = value[k];
                            arr.push(buildURLQueryString(tmp, true));
                        } else {
                            arr.push(key + '[' + k + ']=' + value[k]);
                        }
                    }
                } else {
                    if (flag) {
                        arr.push('[' + key +']=' + value);
                    } else {
                        arr.push(key + '=' + value);
                    }
                }
            }
            return arr.join('&');
        } else {
            return '';
        }
    }
    // This code copy from jQuery, I did a little modification.
    function extend() {
        var options;
        var name;
        var src;
        var copy;
        var copyIsArray;
        var clone;
        var target = arguments[ 0 ] || {};
        var i = 1;
        var length = arguments.length;
        var deep = false;

        // Handle a deep copy situation
        if ( typeof target === "boolean" ) {
            deep = target;
            // Skip the boolean and the target
            target = arguments[ i ] || {};
            i++;
        }

        // Handle case when target is a string or something (possible in deep copy)
        if ( typeof target !== "object" && typeof target !== 'function' ) {
            target = {};
        }

        for ( ; i < length; i++ ) {
            // Only deal with non-null/undefined values
            if ( ( options = arguments[ i ] ) != null ) {
                // Extend the base object
                for ( name in options ) {
                    src = target[ name ];
                    copy = options[ name ];

                    // Prevent never-ending loop
                    if ( target === copy ) {
                    continue;
                    }

                    // Recurse if we're merging plain objects or arrays
                    if ( deep && copy && ( is({}, copy) || (copyIsArray = is([], copy))) ) {

                        if ( copyIsArray ) {
                            copyIsArray = false;
                            clone = src && is([], src) ? src : [];

                        } else {
                            clone = src && is({}, src) ? src : {};
                        }
                        // Never move original objects, clone them
                        target[ name ] = extend(deep, clone, copy);

                    // Don't bring in undefined values
                    } else if ( copy !== undefined ) {
                        target[ name ] = copy;
                    }
                }
            }
        }
        // Return the modified object
        return target;
    }

    /**
     * modifyURL
     * @param {String} url 
     * @param {Object} dict 
        {
            protocol: "https"
            username:""
            password: ""
            host: "github.com"
            port: ""
            path: "/u9648u6653u52c7/extend/blob/master/extend.js"
            filename: "extend.js"
            query: {}
            fragment: ""
        }
    * @param {Boolean} flag
    * @return {String}
    */
    function modifyURL(url, dict, flag) {
        var urlDict = serializeURL(url);
        if (is({}, urlDict) && is({}, dict)) {
            url = '';
            urlDict = extend(true, urlDict, dict);
            
            if (/mailto|data/.test(urlDict.protocol)) {
                url += urlDict.protocol + ':';
            } else {
                url += urlDict.protocol + '://';
            }

            if (urlDict.username.length > 0) {
                url += urlDict.username;
            }
            
            if (urlDict.password.length > 0) {
                url += ':' + urlDict.password;
            }
            
            if (urlDict.username.length > 0) {
                url += '@';
            }
            
            url += urlDict.host;

            if (urlDict.port.length > 0) {
                url += ':' + urlDict.port;
            }

            url += urlDict.path;

            // filter query's keys
            if (flag) {
                for (var k in urlDict.query) {
                    if (urlDict.query.hasOwnProperty(k)) {
                        if (!urlDict.query[k]) {
                            delete urlDict.query[k];
                        }
                    }
                }
            }

            var query = buildURLQueryString(urlDict.query);

            if (query.length > 0 ) {
                url += '?' + query;
            }

            if (urlDict.fragment) {
                url += urlDict.fragment;
            }
            
        } 
        return url;
    }

    /**
     * Klass
     * @param {Function} Parent
     * @param {Object} props
     * @returns {Function}
     */
    function Klass(Parent, props) {
        var Child, F, i;
    
        // 新构造函数
        Child = function(){
            if (Child.uber && Child.uber.hasOwnProperty('__construct')) {
                Child.uber.__construct.apply(this, arguments);
            }
    
            if (Child.prototype.hasOwnProperty('__construct')) {
                Child.prototype.__construct.apply(this, arguments);
            }
        };
    
        // 继承
        Parent = Parent || Object;
        F = function(){};
        F.prototype = Parent.prototype;
        Child.prototype = new F();
        Child.uber = Parent.Prototype;
        Child.prototype.constructor = Child;
    
        // 添加实现方法
    
        for (i in props) {
            if (props.hasOwnProperty(i)){
                Child.prototype[i] = props[i];
            }
        }
    
        return Child;
    }

    // 添加赠品池的url拼装逻辑
    g.getHrefWithGift = function(href, skus) {
        var result = ''

        if (!skus) {
            return href
        }

        // 如果已经选择过
        if (/gids=/.test(href)) {
            var gids = g.serializeUrl(href).param.gids
            result = href.replace('gids=' + gids, 'gids=' + skus.join(','))
        } else {
            if (href.indexOf('?') !== -1) {
                result = href + '&gids=' + skus.join(',')
            } else {
                result = href + '?gids=' + skus.join(',')
            }
        }

        return result
    }

    // 添加延保url拼装逻辑
    g.getHrefWithYB = function(href, skus) {
        var result = ''

        if (!skus) {
            return href
        }

        // 如果已经选择过
        if (/ybId=/.test(href)) {
            var ybId = g.serializeUrl(href).param.ybId
            result = href.replace('ybId=' + ybId, 'ybId=' + skus.join(','))
        } else {
            if (href.indexOf('?') !== -1) {
                result = href + '&ybId=' + skus.join(',')
            } else {
                result = href + '?ybId=' + skus.join(',')
            }
        }
        return result
    }

    // 添加京东服务+url拼装逻辑
    g.getHrefWithJDS = function(href, skus) {
        var result = ''

        if (!skus) {
            return href
        }

        // 如果已经选择过
        if (/jdhsid=/.test(href)) {
            var jdhsid = g.serializeUrl(href).param.jdhsid
            result = href.replace('jdhsid=' + jdhsid, 'jdhsid=' + skus.join(','))
        } else {
            if (href.indexOf('?') !== -1) {
                result = href + '&jdhsid=' + skus.join(',')
            } else {
                result = href + '?jdhsid=' + skus.join(',')
            }
        }
        return result
    }

    // 添加家具增值服务url拼装逻辑
    g.getHrefWithJDSF = function(href, skus) {
        var result = ''

        if (!skus) {
            return href
        }

        // 如果已经选择过
        if (/fsIds=/.test(href)) {
            var fsIds = g.serializeUrl(href).param.fsIds
            result = href.replace('fsIds=' + fsIds, 'fsIds=' + skus.join(','))
        } else {
            if (href.indexOf('?') !== -1) {
                result = href + '&fsIds=' + skus.join(',')
            } else {
                result = href + '?fsIds=' + skus.join(',')
            }
        }
        return result
    }

    // 日志上报
    g.log = function(type, filename, msg) {
        type = type || 'log'

        if (typeof errortracker !== 'undefined') {
            errortracker[type]({
                filename: filename,
                message: msg
            })
        }
    }

    g.is = is;
    g.serializeURL = serializeURL;
    g.buildURLQueryString = buildURLQueryString;
    g.extend = extend;
    g.modifyURL = modifyURL;
    g.Klass = Klass;
    
    module.exports = g;
})
