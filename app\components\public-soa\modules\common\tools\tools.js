define('PUBLIC_ROOT/modules/common/tools/tools', function(require, exports, module) {
    //var CellPhone = require('PUBLIC_ROOT/modules/buytype/buytype').CellPhone;

    var getAreaId = function() {
        var ipLoc = readCookie('ipLoc-djd')
        var result = {
            areaIds: [1, 72, 55653, 0],
            commonAreaId: null
        }

        // 27-2442-2444-31910.138262226
        if (ipLoc) {
            var idx = ipLoc.indexOf('.')
            if (idx > -1) {
                result.commonAreaId = Number(ipLoc.substr(idx + 1))
                ipLoc = ipLoc.substring(0, idx)
            }

            result.areaIds = ipLoc.split('-')
            for (var i = 0; i < result.areaIds.length; i++) {
                result.areaIds[i] = Number(result.areaIds[i])
            }
        }

        return result
    }

    /**
    * 获得数字价格
     JS:
     tools.priceNum({
          skus: [skuid1,skuid2,skuid3,skuid4], // sku数组
          $el: $('body')
      });
     HTML:
     <strong class="J-p-skuid1"></strong>
     <strong class="J-p-skuid2"></strong>
     <strong class="J-p-skuid3"></strong>
     <strong class="J-p-skuid4"></strong>
    */
    var priceNum = function(opts) {
        var url = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            url = pageConfig.product && pageConfig.product.colorApiDomain
        }
        var ipLoc = readCookie('ipLoc-djd')

        var skus = opts.skus || []
        var loc = opts.loc || ipLoc
        var $el = opts.$el || $('body')
        var type = opts.type || 1
        var selector = opts.selector || '.J-p-'
        var text = opts.text || '￥{NUM}'
        var pdbp = opts.pdbp || 0
        var debug = opts.debug || false
        var callback = opts.callback || function() {}
        var onReady = opts.onReady || function() {}

        var isArray = function(obj) {
            return Object.prototype.toString.call(obj) === '[object Array]'
        }

        if (!isArray(skus)) {
            throw new Error('Please give skus param with Array type.')
        }
        if (skus.length < 1) {
            return false
        }

        if (!ipLoc) {
            loc = 1
        } else {
            loc = loc.replace(/-/g, '_')
        }

        if (debug) {
            console.info(
                url +
                    $.param({
                        type: type,
                        area: loc,
                        skuIds: 'J_' + skus.join(',J_')
                    })
            )
        }

        function getUid() {
            var __jda = readCookie('__jda')
            var uid = ''

            if (__jda && __jda.indexOf('.') > -1) {
                uid = __jda.split('.')[1]
            }

            return uid
        }
        var paramJson = {
            area: loc,
            pin: readCookie('pin') || '',
            fields: '11100000',
            skuIds: skus.join(','),//原来有个J_的拼接，最好去掉
            source: 'pc-item'
        };
        var body = JSON.stringify(paramJson);
        var time = new Date().getTime()
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pctradesoa_getprice',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm)) 
            colorParmSign['body'] = SHA256(body).toString()//签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = getCookieNew("__jda") || '';
                            getPriceData(colorParm);
                        }else{
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = getCookieNew("__jda") || '';
                            getPriceData(colorParm);
                        }
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getPriceData(colorParm);
                }
            })
        }catch(e){
        }            
        // 加固end
        function getPriceData(colorParm){
            $.ajax({
                url: url,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                },  
                success: function(r) {
                    if(r){
                        if(parseInt(r.code) < 10 && r.echo){
                            try {
                                var functionId = "pctradesoa_getprice";
                                var echoCode = r.echo.length>1000 ? r.echo.substring(0,999) : r.echo;
                                // jmfe.jsagentReport(
                                //     jmfe.JSAGENT_EXCEPTION_TYPE.business, //固定值不变
                                //     751,  //固定值: 异常码
                                //     '网关调用异常' + functionId ,  // 异常信息
                                //     {
                                //         fid: functionId , // 网关对应的functionid
                                //         resp: echoCode, // 只上报code<10(目前有-1,1,2)的数据，上报echo字段
                                //         body: body // body序列化后的字符；由于浏览器对url有长度限制，body约定限定在1000字符内是绝对满足上报条件的，超过部分前端自行截断。
                                //     }
                                // )
                                window.customPointEvent(
                                    'item_exceptin',
                                    { code: echoCode, error_type_txt: '网关调用异常pctradesoa_getprice' },
                                    {
                                      functionId: functionId,
                                      request: r,
                                      error_msg:  JSON.stringify(echoCode), 
                                    }
                                )
                            } catch(e) {
                                console.log('上报pctradesoa_getprice错误',e)
                            }
                        }else{
                            response(r);
                        }
                    }
                },
                error: function (e) {
                    try {
                        var functionId = "pctradesoa_getprice";
                        // jmfe.jsagentReport(
                        //     jmfe.JSAGENT_EXCEPTION_TYPE.business, //固定值不变
                        //     751,  //固定值: 异常码
                        //     '网关调用异常' + functionId ,  // 异常信息
                        //     {
                        //         fid: functionId , // 网关对应的functionid
                        //         resp: JSON.stringify(e), // 只上报code<10(目前有-1,1,2)的数据，上报echo字段
                        //         // xid: "190835604-10476-1676439710095", //网关Response Headers中获取x-api-request-id字段，方便后续从网关日志平台查询数据; 由于跨域请求，好像拿不到这个值。
                        //         body: body // body序列化后的字符；由于浏览器对url有长度限制，body约定限定在1000字符内是绝对满足上报条件的，超过部分前端自行截断。
                        //     }
                        // )
                        window.customPointEvent(
                            'item_exceptin',
                            { code: '751', error_type_txt: '网关调用异常pctradesoa_getprice' },
                            {
                              functionId: functionId,
                              request: "",
                              error_msg:  JSON.stringify(e), 
                            }
                        )
                    } catch(er) {
                        console.log('上报pctradesoa_getprice错误',er)
                    } 
                }
            })
        }
    }

    /**
    * 广告词
    * @param {Array}        skuid 数组
    * @param {$object}      包裹jQuery元素
    */
    var adWords = function(opts) {
        var loc = opts.loc || readCookie('ipLoc-djd') || '1_0_0'
        var skus = opts.skus || []
        var $el = opts.$el || $('body')
        var $target = opts.$target
        var selector = opts.selector || '.J-ad-'
        var debug = opts.debug || false
        var callback = opts.callback || function() {}

        $.ajax({
            url: '//ad.3.cn/ads/mgets',
            data: {
                skuids: 'AD_' + skus.join(',AD_'),
                areaCode: loc
            },
            dataType: 'jsonp',
            scriptCharset: 'utf-8',
            success: function(r) {
                var i = 0, len, ad, sku

                if (debug) {
                    console.log(r)
                }

                if (r && r.length > 0) {
                    len = r.length

                    for (i; i < len; i++) {
                        ad = r[i].ad
                        sku = r[i].id.replace('AD_', '')
                        $el.find(selector + sku).html(ad)
                        callback(sku, r[i])
                    }
                }
            }
        })
    }

    /**
    * 评价数据
    */
    var commentMeta = function(opts) {
    //     var skus = opts.skus || []
    //     var $el = opts.$el || $('body')
    //     var selector = opts.selector || '.J-comm-'
    //     var text = opts.text || '(已有{NUM}人评价)'
    //     var debug = opts.debug || false
    //     var callback = opts.callback || function() {}
    //     var onlyData = opts.onlyData || false

    //     if (!$.isArray(skus)) {
    //         throw new Error('Please give skus param with Array type.')
    //     }
    //      // start
    //      var time = new Date().getTime()
    //      var bbtfVal = G.serializeUrl(location.href).param.bbtf
    //      var paramJson = {
    //          // appid: 'item-v3',
    //          // functionId: 'pc_club_productCommentSummaries',
    //          // client: 'pc',
    //          // clientVersion: '1.0.0',
    //          // t: time,//生成当前时间毫秒数
    //          referenceIds: skus.join(","),
    //          categoryIds: cIds.join(","),
    //          // loginType: '3',
    //          bbtf: bbtfVal && bbtfVal.length > 0 ? "1" : "",
    //          shield: window.pageConfig.product.shield || "",
    //          // uuid: getCookieNew("__jda") || '',
    //      }
 
    //      var body = JSON.stringify(paramJson);
    //      // 加固start
    //      var colorParm = {
    //          appid: 'item-v3',
    //          functionId: 'pc_club_productCommentSummaries',
    //          client: 'pc',
    //          clientVersion: '1.0.0',
    //          t: time,//生成当前时间毫秒数
    //          body: body,
    //      }
    //      try{
    //          var colorParmSign =JSON.parse(JSON.stringify(colorParm))
    //          colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
    //          window.PSign.sign(colorParmSign).then(function(signedParams){
    //              colorParm['h5st']  = encodeURI(signedParams.h5st)
    //              try{
    //                  getJsToken(function (res) {
    //                      if(res && res.jsToken){
    //                          colorParm['x-api-eid-token'] = res.jsToken;
    //                      }
    //                      colorParm['loginType'] = '3';
    //                      colorParm['uuid'] = getCookieNew("__jda") || '';
    //                      getCommentData(colorParm);
    //                  }, 600);
    //              }catch(e){
    //                  colorParm['loginType'] = '3';
    //                  colorParm['uuid'] = '';
    //                  getCommentData(colorParm);
    //                  //烛龙上报
    //                  getJmfe(colorParm, e, "tools评价数据设备指纹异常",751)
    //              }
    //          })
    //      }catch(e){
    //          colorParm['loginType'] = '3';
    //          colorParm['uuid'] = '';
    //          getCommentData(colorParm);
    //          //烛龙上报
    //          getJmfe(colorParm, e, "tools评价数据加固异常",751)
    //      }            
    //      // 加固end
 
    //      var host = '//api.m.jd.com'
    //      if(pageConfig.product && pageConfig.product.colorApiDomain){
    //          host = pageConfig.product && pageConfig.product.colorApiDomain
    //      }
    //      // end
    //      function getCommentData(colorParm) {
    //          $.ajax({
    //              url: host,
    //              data: colorParm,
    //              dataType: 'json',
    //              contentType: "application/json;charset=gbk",
    //              xhrFields: {
    //                  withCredentials: true,
    //              }, 
    //              headers: getUrlSdx(),
    //              success: function(data) {
    //                  var len, currItem
    //                  if (data && data.CommentsCount.length) {
    //                      len = data.CommentsCount.length
     
    //                      for (var i = 0; i < len; i++) {
    //                          currItem = $el.find(
    //                              selector + data.CommentsCount[i].SkuId
    //                          )
    //                          if (!onlyData) {
    //                              currItem
    //                                  .find('.star')
    //                                  .removeClass('sa5')
    //                                  .addClass(
    //                                      'sa' + data.CommentsCount[i].AverageScore
    //                                  )
    //                              currItem.html(
    //                                  text.replace(
    //                                      '{NUM}',
    //                                      data.CommentsCount[i].CommentCountStr
    //                                  )
    //                              )
    //                          }
     
    //                          if (debug) {
    //                              console.log(currItem)
    //                          }
     
    //                          if (callback) {
    //                              callback(
    //                                  data.CommentsCount[i].SkuId,
    //                                  data.CommentsCount[i]
    //                              )
    //                          }
    //                      }
    //                  }
    //              }
    //          })  
    //      }
    }

    // 手动触发后加载
    var triggerLazyImg = function(ele, attr) {
        var trigger = attr || 'data-src'
        var img = ele.find('img[' + trigger + ']')

        if (img.length) {
            img.each(function(index) {
                $(this).attr('src', $(this).attr(trigger)).removeAttr(trigger)
            })
        }
    }

    // 重新分组json数据
    var reBuildJSON = function(data, itemCountPerPage) {
        var totalPage = data.length / itemCountPerPage
        var resData = []

        for (var i = 0; i < totalPage; i++) {
            resData.push({
                tabs: [],
                increment: null,
                count: itemCountPerPage,
                skuids: []
            })
        }

        var m = 0
        for (var k = 0; k < data.length; k++) {
            if (k % itemCountPerPage == 0) {
                m++
            }

            resData[m - 1]['tabs'].push(data[k])
            resData[m - 1]['increment'] = m
            if (data[k].wid) {
                resData[m - 1]['skuids'].push(data[k].wid)
            }
        }

        return resData
    }

    var getAreaIds = function() {
        var ipLoc = readCookie('ipLoc-djd')
        return ipLoc ? ipLoc.split('-') : [1, 72, 55653, 0]
    }

    var getUUID = function() {
        var __jda = readCookie('__jda')
        var uid = ''

        if (__jda) {
            if (__jda.split('.')[1] == '-') {
                uid = -1
            } else {
                uid = __jda.split('.')[1]
            }
        } else {
            uid = -1
        }

        return uid
    }

    var checkLogin = function(cb) {
        cb = cb || function() {}
        var locname = window.location.hostname
        var locnameNum =  locname.split("item")[1] || ".jd.com"
        $.ajax({
            url: '//passport'+locnameNum+'/loginservice.aspx?method=Login',
            dataType: 'jsonp',
            success: function(r) {
                if (r.Identity) {
                    cb(r.Identity)
                }
            }
        })
    }

    /**
     * 倒计时类库
     * @param {Number} milliseconds
     * @param {Function} callback
     * @param {[Number]} interval
     * @class Countdown
     */
    function Countdown(milliseconds, callback, interval) {
        if (typeof milliseconds === 'number') {
            this.milliseconds = milliseconds;
        } else {
            this.milliseconds = 0;
        }

        if (typeof callback === 'function') {
            this.callback = callback;
        } else {
            this.callback = function(){};
        }

        if (typeof inverval === 'number') {
            this.interval = interval;
        } else {
            this.interval = 1000;
        }
        this.start();
    }

    Countdown.prototype = {
        constructor: Countdown,
        /**
         * start
         * @memberof Countdown
         */
        start: function() {
            var that = this;
            var count = 0;
            var walk = function() {
                count++;
                var now = +new Date();
                var offset = now - (that.stime + count * that.interval);
                if (offset > 10000) {
                    var count2 = Math.floor(offset / that.interval);
                    count += count2;
                    that.milliseconds -= (count2 * that.interval);
                }
    
                var nextTime = that.interval - offset;
                if (nextTime < 0) {
                    nextTime = 0
                }
                that.callback.call(that, that.format(that.milliseconds / 1000));
                that.milliseconds -= that.interval;
    
                if (that.milliseconds < 0) {
                    clearTimeout(that.timer);
                } else {
                    that.timer = setTimeout(walk, nextTime);
                }
            };
    
            if (this.milliseconds >= 0) {
                this.stime = +new Date();
                this.timer = setTimeout(walk, this.interval);
            }
            return this;
        },

        /**
         * stop
         * @memberof Countdown
         */
        stop:function () {
            this.timer && clearTimeout(this.timer);
            return this;
        },

        /**
         * format 时间格式化
         * @param {Number} 秒
         * @returns {Object}
         * @memberof Counter
         */
        format: function(seconds) {
            seconds = (typeof seconds === 'number' && seconds >=0) ? seconds : 0;
            var days = Math.floor(seconds / 86400);
            var hours = Math.floor(seconds % 86400 / 3600);
            var minutes = Math.floor(seconds % 86400 % 3600 / 60);
            var sec = Math.floor(seconds % 86400 % 3600 % 60);
            return {
                d: days,
                h: hours,
                m: minutes,
                s: sec
            };
        }
    };
    
    /**
     * 函数节流方法
     * @param Function fn 延时调用函数
     * @param Number delay 延迟多长时间
     * @param Number atleast 至少多长时间触发一次
     * @return Function 延迟执行的方法
     */
    var throttle = function(fn, delay, atleast) {
        var timer = null
        var previous = null

        return function() {
            var now = +new Date()

            if (!previous) previous = now

            if (now - previous > atleast) {
                fn()
                // 重置上一次开始时间为本次结束时间
                previous = now
            } else {
                clearTimeout(timer)
                timer = setTimeout(function() {
                    fn()
                }, delay)
            }
        }
    }

    // 补零
    // prefix(3, 15)  => 015
    // prefix(3, 5)  => 005
    var prefix = function(num, val) {
        return (new Array(num).join('0') + val).slice(-num)
    }

    // 给 url 添加参数
    var addUrlParam = function(url, key, value, valRE) {
        valRE = valRE || '\\d*'
        var re = new RegExp(key + '=' + valRE, 'gi')
        var result = ''

        if (!value) return url

        if (url.indexOf(key + '=') > -1) {
            result = url.replace(re, key + '=' + value)
        } else {
            var spliter = url.indexOf('?') < 0 ? '?' : '&'
            result = url + spliter + key + '=' + value
        }
        return result
    }

    var modifier = {
        // 获取页面 URL
        itemUrl: function(skuid) {
            return skuid ? '//item.jd.com/' + skuid + '.html' : ''
        },
        // 获取图片分流地址 > http://cf.jd.com/pages/viewpage.action?pageId=61506220
        imgDomain: function(skuid) {
            var imgDomains = [10, 11, 12, 13, 14]
            var num = imgDomains[skuid % 5]
            if (skuid && num) {
                return 'http://img'+ num +'.360buyimg.com/'
            } else {
                return ''
            }
        }
    }

    // 获取公共Cookie方法
    var getCookieNew = function(name) {
        var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
        if (arr != null) return arr[2];
        return null;
    }


    exports.getCookieNew = getCookieNew
    exports.commentMeta = commentMeta
    exports.priceNum = priceNum
    exports.adWords = adWords
    exports.triggerLazyImg = triggerLazyImg
    exports.reBuildJSON = reBuildJSON
    exports.getAreaIds = getAreaIds
    exports.getAreaId = getAreaId
    exports.checkLogin = checkLogin
    exports.getUUID = getUUID
    exports.Countdown = Countdown
    exports.throttle = throttle
    exports.prefix = prefix
    exports.addUrlParam = addUrlParam
    exports.modifier = modifier
})
