define('PUBLIC_ROOT/modules/shopSimilar/shopSimilar', function (require, exports, module) {
    /**
     * 本店好评商品瀑布流
     */
    var follow = require('JDF_UNIT/follow/1.0.0/follow');
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');

    require('PUBLIC_ROOT/modules/ELazyload/ELazyload');
    require('PUBLIC_ROOT/modules/common/plugins/jQuery.masonry');

    var template = '\
        <div class="mt">\
            <h3 class="fl">本店好评商品</h3>\
        </div>\
        <div class="mc">\
            <ul class="item-plist-2 shop-similar-promo-list clearfix" clstag="shangpin|keycount|product|haopingshangpin_2">\
                {for item in data}\
                <li class="item" data-push="${pageConfig[skuHooks].push(item.sku)}">\
                    <div class="pro-wrap">\
                        <div class="p-img">\
                            <a href="//item.jd.com/${item.sku}.html" target="_blank" title="${item.t}" data-clk="${item.clk}"><img src="${pageConfig.FN_GetImageDomain(item.sku)}{if ext.imgSize}${ext.imgSize}{else}n4{/if}/${item.img}" alt="${item.t}" height="{if ext.imgHeight}${ext.imgHeight}{else}282{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}220{/if}"></a>\
                        </div>\
                        <div class="p-name">\
                            <a href="//item.jd.com/${item.sku}.html" target="_blank" data-clk="${item.clk}" title="${item.t}">${item.t}</a>\
                        </div>\
                        <div class="p-price">\
                            <a href="#none" class="p-focus" data-id="${item.sku}"><i class="i-focus"></i><em class="text">关注</em></a>\
                            <strong class="price"><span class="J-p2-${item.sku}">￥</span></strong>\
                        </div>\
                        <div class="p-comment">\
                            <div class="inner" title="${item.comments}">${item.comments}</div>\
                        </div>\
                    </div>\
                </li>\
                {/for}\
            </ul>\
        </div>';

    //var url = "//cd.jd.com/recommend/clothes?sku=1559647278&cat=1315,1343,9719&uuid=400959088&pin=&lid=1&cuuid=11112&csid=2333&callback=tt";
    var ShopSimilar = {
        inited: false,
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $("#shop-similar-promotion");
            this.sku = cfg.skuid;
            this.index = 0;
            this.inited = true;
            return this;
        },
        getData: function () {
            var _this = this;

            var __jda = readCookie('__jda');
            var uid = -1;

            if (__jda) {
                if (__jda.split('.')[1] == '-') {
                    uid = -1;
                } else {
                    uid = __jda.split('.')[1];
                }
            } else {
                uid = -1;
            }

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }

            var body = JSON.stringify({
                sku: _this.sku,
                cat: this.cfg.cat.join(","),
                lid: Tools.getAreaId().areaIds[0],
            });
    
            $.ajax({
                url: host,
                data: {
                    body:body,
                    appid: 'item-v3',
                    functionId: "pctradesoa_recommend_clothes"   
                },
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                // url: host,
                // data: {
                //     body:body,
                //     appid: 'item-v3',
                //     functionId: "pctradesoa_recommend_clothes"   
                // },
                // jsonpCallback: 'fetchJSON_shopSimilar',
                // cache: true,
                // dataType: 'jsonp',
                // scriptCharset: 'gbk',
                // timeout: 5000,
                success: function (r) {
                    _this.setData(r);
                },
                error: function () {
                    _this.$el.html('「本店好评商品 暂无数据」');
                }
            });
        },
        setData: function (r) {
            var _this = this;
            //接口异常
            if (!r) {
                _this.$el.html('「本店好评商品 暂无数据」');
                return;
            }

            if (!r.success || !r.data || !r.data.length) {
                _this.$el.html('「本店好评商品 暂无数据」');
                return;
            }

            r.ext = {
                imgSize: 'n8',
                imgHeight: 282,
                imgWidth: 220
            };
            
            if (this.$el.hasClass('img-square')) {
                r.ext.imgSize = 'n7';
                r.ext.imgHeight = 220;
            }

            if (/debug=shop/.test(location.href)) {
                alert(template.process(r));
            }

            var html = '';
            try {
                var skuHooks = 'shopSimilar';
                r.skuHooks = skuHooks;
                pageConfig[skuHooks] = [];

                html = template.process(r);
                html += '<div class="clear"></div>';
                _this.$el.html(html);
                _this.$el.find(".shop-similar-promo-list").masonry({
                    itemSelector : '.item',
                    columnWidth: 220,
                    gutterWidth: 27
                });
                _this.focusMe();
                _this.bindEvent();

                // Tools.priceNum({
                //     skus: pageConfig[skuHooks],
                //     $el: _this.$el
                // });
                Tools.priceNumRecommend({
                    priceList: r.priceList,
                    $el: _this.$el
                });
            } catch (err) {
                var errMsg = html.match(/\[ERROR.+\]/);
                if (errMsg && errMsg.length) {
                    console.error('Template Render Error @ [shopSimilar.js]. >>>>> \n   %s', errMsg[0]);
                }
            }
        },
        focusMe: function () {
            var _this = this;
            var $followBtn = _this.$el.find('li .p-focus');
            follow.init($followBtn);
        },
        bindEvent: function () {
            var _this = this;
            _this.$el.delegate('a[data-clk]', 'click', function() {
                var dataClkStr = $(this).attr("data-clk");
                _this.setTrackCode(dataClkStr);
            });
        },
        setTrackCode: function (src) {
            var img = new Image();
            src = src + "&m=UA-J2011-1&ref=" + encodeURIComponent(document.referrer) + "&random=" + Math.random();
            img.setAttribute('src', src);
        }
    }

    function init(cfg) {
        ShopSimilar.init(cfg).getData();
    }

    module.exports.__id = 'shopSimilar';
    module.exports.init = init;
    module.exports.ShopSimilar = ShopSimilar;
});
