@import '../common/lib';
.choose-btns {
    margin-top: 10px;
    margin-bottom: 20px;
    // padding: 0px 10px 0 10px;

    .choose-amount {
        width: 58px; //49
        height: 44px;
        overflow: hidden;
        border: 1px solid #ccc;
        position: relative;
        margin-right: 10px;
        float: left;
        input {
            display: block;
            width: 43px; //36
            height: 42px;
            line-height: 42px;
            position: absolute;
            top: 1px;
            left: 0;
            border: none;
            border: 0;
            text-align: center;
        }
        a.disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        a {
            display: block;
            width: 15px;
            text-align: center;
            height: 22px;
            line-height: 22px;
            overflow: hidden;
            background: #f1f1f1;
            color: #666;
            position: absolute;
            right: -1px;
            border: 1px solid #ccc;

            &.btn-add {
                top: -1px;
            }

            &.btn-reduce {
                bottom: -1px;
            }
        }
    }

    .btn-lg {
        margin-right: 10px;
        float: left;
    }
    .pingou-tips{
        line-height: 50px;
        color:#999;
    }

    .yuyue-text {
        line-height: 46px;
        margin-right:10px;
        a {
            color: $colorLinkBlue;
        }
    }
    .question{
        background-image: url(//static.360buyimg.com/item/default/1.0.37/components/baitiao/i/question.png);
        background-repeat: no-repeat;
    }
    .icon{
      width: 16px;
      height: 16px;
      overflow: hidden;
      margin-top: 16px;
    }
}

.noborder {
    border: none;
}

// 必购码
.bgm-text {
    line-height: 48px;
    a {
        color: #666;
    }
}
.bgm-text {
    line-height: 50px;
    color:#999;
}
.bgm-text-active {
    color:#666;
}

// 预约
.reservation-cd {
    .text {
        margin-left: 10px;
        em {
            color: #999;
            margin-right: 10px;
        }
    }
}
/*book*/
.ebook{
    .btn-buy{
        color: #5e69ad;
        float: left;
        margin-top: 13px;
        &:hover{
            color: #e4393c;
        }
    }
    .btn-special3{
        height: 44px;
        line-height: 44px;
    }
}
.btn-extra-text {
    line-height: 50px;
    a {
        color: $colorLinkBlue;
    }
}
.easy-buy-tips-ab {
    p.hl_gray {
        line-height: 18px;
        padding: 3px 0;
    }
    img {
        margin-left:-10px;
        margin-top:-10px;
    }
    .mod-btn {
        margin-top:5px;
        display: block;
        text-align: center;
        padding: 4px 0;
        border: 1px solid #ccc;
        background: #F5F5F5
    }
    .mod-btn,.mod-btn:hover {
        color:#666;
    }
    .mod-btn:hover {
        background:#fff;
    }
}
.btn-tips{
    position: relative;
    float: left;
    margin-right: 10px;
}
.sprite-question{
    display: inline-block;
    vertical-align: -16px;
    width: 16px;
    height: 16px;
    background-image: url(//storage.jd.com/retail-mall/item/pc/unite/1.0.184-cgqd/components/default-soa/ycservice/i/__sprite.png);
    background-position: 0 0
}
.btn-tips .tips {
    z-index: 2;
    width: 270px;
    position: absolute;
    left: -215px;
    top: 35px;
    display: none;

    .content {
        padding: 10px;
        background: #fff;
        border: 1px solid #cecbce;
        color: #666;
        -moz-box-shadow: 0 0 2px 2px #eee;
        -webkit-box-shadow: 0 0 2px 2px #eee;
        box-shadow: 0 0 2px 2px #eee;
        dt{
            font-weight: bold;
            margin-bottom: 3px;
        }
        dd{
            line-height: 170%;
        }
        p{
            border-top: 1px dotted #999;
            margin-top: 7px;
            padding-top: 7px;
            a{
                color: #5e69ad;
                margin: 0 5px;
                &:hover{
                    color: #e4393c;
                }
            }
        }
    }
    .sprite-arrow {
        width: 11px;
        height: 6px;
        background-image: url(//storage.jd.com/retail-mall/item/pc/unite/1.0.184-cgqd/components/default-soa/ycservice/i/__sprite.png);
        background-position: -14px -16px;
        position: absolute;
        overflow: hidden;
        left: 218px;
        top: -5px;
        _bottom: -1px;
    }
}
.hover .tips{
    display: block;
}