@import '../common/lib';
.J_contrast_btn{ // 左侧样式
    position: absolute;
    right: 0;
    top: 0;
    height: 50px;
    line-height: 50px;
    margin-right: 16px;
    padding-right: 16px;
    background: url(https://img13.360buyimg.com/imagetools/jfs/t1/259181/16/14667/489/6790bd3aF60de783b/8ed74ff7b6ef4ed6.png) no-repeat;
    background-position: right center;
    background-size: 12px;
    .J_contrast{
        font-size: 16px;
        font-weight: 400;
        color: #505259;
        .sprite-compare{
            display: inline-block;
            width: 23px;
            height: 13px;
            margin-right: 4px;
            vertical-align: middle;
            background: url(https://img13.360buyimg.com/imagetools/jfs/t1/266535/22/14376/964/678fa2bbF8b318a68/3ab11da200cfbb64.png);
            background-size: 100%;
            margin-top: -2px;
        }
        em {
          font-weight: 400;
        }
    }
}
.choose-btns {
    // margin-top: 10px;
    // margin-bottom: 20px;
    flex: 1;
    position: relative;
    bottom: 0;
    height: 95px;
    // padding: 0px 10px 0 10px;
    .J_contrast_btn { // 右侧样式
        position: static; 
        float: left;
        background: #F7F8FC;
        border: 0.5px solid rgba(0, 0, 0, 0.06);
        width: 58px;
        height: 14px;
        margin-right: 8px;
        margin-top: 6px;
        border-radius: 4px;
        padding: 13px 11px;
        line-height: 14px;
        .J_contrast{
            font-size: 14px;
        }
    }
    .J_books_btn{
        float: left;
        background: #f7f8fc;
        width: 85px;
        height: 27px;
        margin-right: 10px;
        border-radius: 4px;
        padding: 13px 16px;
        line-height: 27px;
        .J_book{
            font-size: 14px;
            font-weight: 400;
            color: #1A1A1A;
            .sprite-compare{
                display: inline-block;
                width: 18px;
                height: 19px;
                margin-right: 5px;
                vertical-align: middle;
                background: url(https://img11.360buyimg.com/imagetools/jfs/t1/266035/3/6548/453/67752c08Fcb0d715d/afd77a8e88c199fd.png);
                background-size: 100%;
                margin-top: -2px;
            }
        }
    }
    .choose-amount {
        width: 151px;
        height: 44px;
        // overflow: hidden;
        position: relative;
        margin-right: 10px;
        float: left;
        margin-top: 6px;
        input {
            display: block;
            width: 60px;
            height: 40px;
             line-height: 40px;
            position: absolute;
            top: 0px;
            left: 42px;
            border: none;
            border: 0;
            text-align: center;
            font-size: 20px;
            font-weight: 400;
        }
        input:focus {
            outline: none; /* 确保没有焦点轮廓 */
            border: none;  /* 去掉边框 */
        }
        .disabled {
            color: #C2C4CC;
            cursor: not-allowed;
        }
        a {
            border: 0.5px solid rgba(0, 0, 0, 0.02);
            display: block;
            width: 42px;
            text-align: center;
            height: 40px;
            line-height: 40px;
            overflow: hidden;
            background: #F7F8FC;
            color: #1A1A1A;
            position: absolute;
            right: 7px;
            border-radius: 4px;
            font-size: 24px;
            font-weight: 700;
            &.btn-add {
                top: 0px;
            }

            &.btn-reduce {
                left: 0px;
            }
        }
        .tips{
            background: rgba(0, 0, 0, .6980392157);
            border-radius: 6px;
            padding: 9px;
            position: absolute;
            top: -40px;
            color: #fff;
        }
        &.invisible {
            visibility: visible;

        }
    }
    .J_choose_btn{
        display: flex;
        flex-direction: row;
        overflow: hidden; 
        .btn-more {
            width: 60px;
            // margin-right: 8px;
            flex-shrink: 0;
            flex-grow: 0;
            background:#FFEBF1 url(https://img10.360buyimg.com/imagetools/jfs/t1/231810/20/30193/1703/67ab458aFd04dc3a9/1d732249ce5875f7.png) no-repeat;
            // background:#FFE7CC url(https://img11.360buyimg.com/imagetools/jfs/t1/282918/21/770/1959/67d00927F38d7a191/0066975b465c45a7.png) no-repeat;
            background-size: 28px 28px;
            background-position: center;
            position: relative;
        }
        .more-btns {
            position: absolute;
            top: -180px;
            background-color: #fff;
            width: 246px;
            height: 168px;
            border: 1px solid rgba(0, 0, 0, 0.06);
            left: 50%;
            transform: translateX(-50%);
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .btn-lg {
                width: 198px;
                flex: 0;
                margin: 0 !important;
            }
            .btn-lg + .btn-lg {
                margin-top: 16px !important;
            }
            .triangle {
                // box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
                width: 12px;
                height: 12px;
                border-right: 1px solid rgba(0, 0, 0, 0.06);
                border-bottom: 1px solid rgba(0, 0, 0, 0.06);
                position: absolute;
                bottom: -7px;
                background-color: #fff;
                transform: rotate(45deg);
            }
        }
     
        &.morethan1 {
            .btn-lg + .btn-lg {
                margin-left: 8px;
            }
        }
    }
    .J_choose_app_btn{
        display: flex;
        flex-direction: row;
        // overflow: hidden; 
        position: relative;
    }
    .btn-lg {
        // margin-right: 10px;
        float: right;
        // margin-bottom: 5px;
        padding: 0;
        cursor: pointer;
        flex: 1 1 auto;
        font-family: PingFang SC;
        line-height: 52px;
        outline: 0;
        text-align: center;
        border-radius: 4px;
        font-size: 20px;
        font-weight: 600;
        height: 52px;
        &.btn-disable {
            border: none;
        }
    }
   
    .pingou-tips{
        line-height: 50px;
        color:#999;
    }

    .yuyue-text {
        line-height: 46px;
        margin-right:10px;
        a {
            color: $colorLinkBlue;
        }
    }
    .question{
        background-image: url(//static.360buyimg.com/item/default/1.0.37/components/baitiao/i/question.png);
        background-repeat: no-repeat;
    }
    .icon{
      width: 16px;
      height: 16px;
      overflow: hidden;
      margin-top: 16px;
    }

    .J-inventory-stock{
        // height: 44px;
    }
}

.choose-btns.extra {
    #btn-reservation,
    #J_ShoppingCartButton{
        font-size: 14px;
        line-height: 18px;
        span {
            @include inline-block;
            margin-top: 6px;
        }
    }
}
#btn-reservation { // 立即预约
    background: linear-gradient(90deg, #FF475D 0%, #FF0F23 100%);
    color: #fff;
    &.pay-deposit { // 支付定金
        background: rgba(255, 114, 25, 1);
        span {
            margin-left: 6px;
        }
    }
}
.noborder {
    border: none;
}

// 必购码
.bgm-text {
    line-height: 48px;
    a {
        color: #666;
    }
}
.bgm-text {
    line-height: 50px;
    color:#999;
}
.bgm-text-active {
    color:#666;
}

// 预约
.reservation-cd {
    .text {
        margin-left: 10px;
        em {
            color: #999;
            margin-right: 10px;
        }
    }
}
/*book*/
.ebook{
    .btn-buy{
        color: #5e69ad;
        float: left;
        margin-top: 13px;
        &:hover{
            color: #e4393c;
        }
    }
    .btn-special3{
        height: 44px;
        line-height: 44px;
    }
}
.btn-extra-text {
    line-height: 50px;
    a {
        color: $colorLinkBlue;
    }
}
.easy-buy-tips-ab {
    p.hl_gray {
        line-height: 18px;
        padding: 3px 0;
    }
    img {
        margin-left:-10px;
        margin-top:-10px;
    }
    .mod-btn {
        margin-top:5px;
        display: block;
        text-align: center;
        padding: 4px 0;
        border: 1px solid #ccc;
        background: #F5F5F5
    }
    .mod-btn,.mod-btn:hover {
        color:#666;
    }
    .mod-btn:hover {
        background:#fff;
    }
}

.btn-tips{
    position: relative;
    float: left;
    margin-right: 10px;
}
.sprite-question{
    display: inline-block;
    vertical-align: -16px;
    width: 16px;
    height: 16px;
    background-image: url(//storage.jd.com/retail-mall/item/pc/unite/1.0.184-cgqd/components/default-soa/ycservice/i/__sprite.png);
    background-position: 0 0
}
.btn-tips .tips {
    z-index: 2;
    width: 270px;
    position: absolute;
    left: -215px;
    top: 35px;
    display: none;

    .content {
        padding: 10px;
        background: #fff;
        border: 1px solid #cecbce;
        color: #666;
        -moz-box-shadow: 0 0 2px 2px #eee;
        -webkit-box-shadow: 0 0 2px 2px #eee;
        box-shadow: 0 0 2px 2px #eee;
        dt{
            font-weight: bold;
            margin-bottom: 3px;
        }
        dd{
            line-height: 170%;
        }
        p{
            border-top: 1px dotted #999;
            margin-top: 7px;
            padding-top: 7px;
            a{
                color: #5e69ad;
                margin: 0 5px;
                &:hover{
                    color: #e4393c;
                }
            }
        }
    }
    .sprite-arrow {
        width: 11px;
        height: 6px;
        background-image: url(//storage.jd.com/retail-mall/item/pc/unite/1.0.184-cgqd/components/default-soa/ycservice/i/__sprite.png);
        background-position: -14px -16px;
        position: absolute;
        overflow: hidden;
        left: 218px;
        top: -5px;
        _bottom: -1px;
    }
}
.hover .tips{
    display: block;
}

// 定期购
.dialogDom{
    position: relative;
    .dialogTip{
        position: absolute;
        left: 50%;
        top: 50%;
        background: #000;
        color: #fff;
        padding: 5px 15px;
        border-radius: 5px;
        margin-left: -40px;
    }
    .plan-goods{
        padding: 12px;
        height: 480px;
        // overflow-y: scroll;
        .goods-info{
            padding: 10px;
            background-color: #F7F8FC;
            display: inline-block;
            border-radius: 5px;
            .goods-info-left{
                float: left;
                margin-right: 10px;
                img{
                    border-radius: 5px;  
                }
            }
            .goods-info-right{
                float: left;
                width: 330px;
                .goods-info-title{
                    color: #1A1A1A;
                    font-size: 14px;
                    font-weight: 400;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    margin-bottom: 5px;
                    width: 325px;
                }
                .goods-info-tip{
                    color: #fa2c19;
                    margin-bottom: 14px;
                    width: 318px;
                    overflow: hidden;
                    height: 18px;
                    line-height: 18px;
                    span{
                        border: 1px solid #fa2c19;
                        padding: 1px 2px;
                        border-radius: 3px;
                        height: 12px;
                        display: inline-block;
                        line-height: 12px;
                    }
                }
                .goods-info-price{
                    width: 318px;
                    overflow: hidden;
                    height: 20px;
                    line-height: 20px;
                    .jd-price{
                        color: #FA2C19;
                        font-weight: 400;
                        i{
                            font-size: 16px;
                        }
                        em{
                            display: inline-block;
                            background-image: url(https://img14.360buyimg.com/imagetools/jfs/t1/21914/5/20805/10593/668cecc0F4b14bf5c/f1c5294317bfb251.png);
                            width: 67px;
                            height: 16px;
                            background-size: 100%;
                            margin-left: 3px;
                        }
                    }
                    .ds-price{
                        margin-left: 5px;
                    }
                }
            }
        }
        .goods-discount-title{
            margin: 10px 0;
            .goods-main-title{
                color: #1A1A1A;
                font-size: 14px;
            }
            .goods-sub-title{
                color: #888B94;
            }
        }
        .goods-discount{
            padding: 10px;
            background-color: #F7F8FC;
            display: inline-block;
            border-radius: 5px;
            margin-bottom: 10px;
            width: 415px;
            border: 1px solid rgba(0,0,0,.08);
            .buy-multiple{
                display: inline-block;
                font-size: 12px;
                color: rgba(26, 26, 26, 1);
                margin-top: -3px;
                .top{
                    padding: 10px 0 10px 0;
                    height: 12px;
                    line-height: 12px;
                    div{
                        float: left;
                        span{
                            width: 98px;
                            display: inline-block;
                            text-align: center;
                            font-family: 'iconfont';
                            margin-left: 21px;
                            overflow: hidden;
                        }
                        .discountDetails{
                            width: 140px;
                        }
                    }
                    .num{
                        width: 50px;
                        display: inline-block;  
                    }
                }
                .line{
                    background:rgba(0,0,0,.08);
                    height: 1px;
                    width: 415px;
                }
                .bottom{
                    padding-top: 10px;
                    height: 15px;
                    line-height: 12px;
                    div{
                        float: left;
                        span{
                            width: 98px;
                            display: inline-block;
                            text-align: center;
                            margin-left: 21px;
                            overflow: hidden;
                            i{
                                font-weight: 400;
                                font-size: 16px;
                                color: rgba(26, 26, 26, 1);
                                width: 22px;
                                overflow: hidden;
                                display: inline-block;
                            }
                        }
                        .discountDetails{
                            width: 140px;
                        }
                    }
                    .num{
                        width: 50px;
                        display: inline-block;  
                    }
                }
            }
        }
        .goods-rules {
            .goods-rules-plan{
                display: block;
                margin-bottom: 10px;
                height: 55px;
                line-height: 50px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                .goods-rules-topic{
                    float: left;
                    font-size: 14px;
                    color: #1A1A1A;
                }
                .goods-rules-btn{
                    float: right; 
                    // width: 363px;
                    overflow: visible;
                    white-space: nowrap;
                    span{
                        color: #1a1a1a;
                        text-align: center;
                        background: #f7f8fc;
                        padding: 4px 6px;
                        font-size: 12px;
                        font-weight: 400;
                        margin-right: 5px;
                        cursor: pointer;
                    }
                    span:last-child{
                        margin-right: 0px;
                    }
                    .check{
                        color: #FA2C19;
                        background: #FEE9E8;
                        border: 0.5px solid #FA2C19;
                    }
                }
            }
            
            .goods-rules-num{
                display: block;
                margin-bottom: 10px;
                height: 55px;
                line-height: 50px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                .goods-rules-topic{
                    float: left;
                    font-size: 14px;
                    color: #1A1A1A;
                }
                .goods-rules-choose{
                    float: right;
                    .center-column {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        float: left;
                    }
                    .tip-color {
                        color: red;
                        text-align: right;
                        display: inline-block;
                        float: right;
                        position: relative;
                        right: 10px;
                        top: -4px;
                    }
                    .last {
                        width: 90px;
                        height: 28px;
                        text-align: center;
                        position: relative;
                        float: right;
                        top: 6px;
                        .count-set {
                            > * {
                                float:left;
                                width: 20px;
                                height: 28px;
                                line-height: 28px;
                                box-sizing: border-box;
                                border:1px solid #ddd;
                                vertical-align: middle;
                                text-align: center;
                            }
                            > input {
                                width: 44px;
                                font: 13px 'Arial';
                                color:#000;
                                background-color: #fff;
                                font-weight: 600;
                                border: {
                                    left-width:0;
                                    right-width:0;
                                }
                                outline: 0;
                                padding: {
                                    top: 6px;
                                    bottom: 6px;
                                }
                                &.disabled {
                                    color: #ccc;
                                    cursor: not-allowed;
                                }
                            }
                        }
                        i {
                            width: 30px;
                            font: normal 20px/30px 'Arial';
                            color:#666;
                            cursor: pointer;
                            -webkit-user-select:none;
                            -moz-user-select:none;
                            -o-user-select:none;
                            user-select: none;
                            &.disabled {
                                cursor: not-allowed;
                                color: #ccc;
                            }
                        }
                        .i-reduce-down1 {
                            line-height: 24px;
                        }
                        .i-reduce-down2 {
                            line-height: 24px;
                        }
                    }
                }
            }
    
            .goods-rules-stage{
                display: block;
                margin-bottom: 10px;
                height: 55px;
                line-height: 50px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                .goods-rules-topic{
                    float: left;
                    font-size: 14px;
                    color: #1A1A1A;
                }
                .goods-rules-choose{
                    float: right;
                    .center-column {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        float: left;
                    }
                    .tip-color {
                        color: red;
                        text-align: right;
                        display: inline-block;
                        float: right;
                        position: relative;
                        right: 10px;
                        top: -4px;
                    }
                    .last {
                        width: 90px;
                        height: 28px;
                        text-align: center;
                        position: relative;
                        float: right;
                        top: 6px;
                        .count-set {
                            > * {
                                float:left;
                                width: 20px;
                                height: 28px;
                                line-height: 28px;
                                box-sizing: border-box;
                                border:1px solid #ddd;
                                vertical-align: middle;
                                text-align: center;
                            }
                            > input {
                                width: 44px;
                                font: 13px 'Arial';
                                color:#000;
                                background-color: #fff;
                                font-weight: 600;
                                border: {
                                    left-width:0;
                                    right-width:0;
                                }
                                outline: 0;
                                padding: {
                                    top: 6px;
                                    bottom: 6px;
                                }
                                &.disabled {
                                    color: #ccc;
                                    cursor: not-allowed;
                                }
                            }
                        }
                        i {
                            width: 30px;
                            font: normal 20px/30px 'Arial';
                            color:#666;
                            cursor: pointer;
                            -webkit-user-select:none;
                            -moz-user-select:none;
                            -o-user-select:none;
                            user-select: none;
                            &.disabled {
                                cursor: not-allowed;
                                color: #ccc;
                            }
                        }
                        .i-reduce-down1 {
                            line-height: 24px;
                        }
                        .i-reduce-down2 {
                            line-height: 24px;
                        }
                    }
                }
            }
        }
        .goods-price-btn{
            margin-top: 10px;
            color: #1a1a1a;
            display: inline-block;
            .goods-price-num{
                width: 335px;
                display: inline-block;
                float: left;
                .goods-price-top{
                    font-size: 14px;
                    font-weight: 400;
                    margin-bottom: 10px;
                    span{
                        color: #FA2C19;
                    }
                    .discount-num{
                        width: 20px;
                        overflow: hidden;
                        display: inline-block;
                        height: 15px;
                        line-height: 15px;
                        vertical-align: middle;
                        margin-top: -4px;
                    }
                }
                .goods-price-bottom{
                    font-size: 14px;
                    font-weight: 400;
                    .price-total{
                        .total{
                            color: #FA2C19;
                            i{
                                font-size: 18px;
                                font-weight: 500;
                            }
                        }
                    }
                    .price-save{
                        color: #FA2C19;
                        padding: 3px 10px;
                        background: linear-gradient(90deg, rgba(242, 48, 178, 0.2) 0%, rgba(255, 136, 77, 0.2) 100%);
                        border-radius: 10px;
                    }
                }
            }
            .goods-btn-trade{
                float: left;
                margin-left: 10px;
                margin-top: 23px;
                a{
                    color: #fff;
                    text-align: center;
                    background: #f23030;
                    padding: 7px 27px;
                    font-size: 12px;
                    font-weight: 600;
                }
            }
        }
    }
    .plan-goods-short{
        height: 355px;
    }
}
// 定期购配送页面楼层
.choose-floor{
    // width: 100%;
    height: 72px;
    background: rgba(255, 241, 240, 1);
    margin-bottom: 20px;
    padding: 8px;
    .step{
        height: 20px;
        margin-bottom: 5px;
        .left{
            float: left;
            font-size: 12px;
            color: rgba(26, 26, 26, 1);
            font-weight: 500;
            em{
                background-image: url(https://img12.360buyimg.com/imagetools/jfs/t1/196748/32/38876/504/66962492F696098b6/dfdab3084ca6364e.png);
                background-repeat: no-repeat;
                width: 7px;
                height: 13px;
                overflow: hidden;
                margin-top: 16px;
                display: inline-block;
                background-size: 100%;
                margin: 0 15px;
                vertical-align: middle;
            }
        }
        .right{
            float: right;
        }
    }
    .part{
        height: 24px;
        background-color: #fff;
        padding: 10px;
        line-height: 24px;
        padding-left: 25px;
        span{
            margin-right: 16px;
            float: left;
            width: 60px;
            display: inline-block;
            text-align: center;
            overflow: hidden;
            height: 25px;
            line-height: 25px;
            em{
                margin-left: 3px;
                color: rgba(26, 26, 26, 1);
                font-weight: 400;
            }
            i{
                border: 1px solid #fa2c19;
                padding: 1px 2px;
                border-radius: 3px;
                color: #fa2c19;
                margin-left: 5px;
            }
        }
        .p1{
            width: 65px;
        }
        .p2{
            width: 185px;
        }
        .p3{
            width: 122px;
        }
        .p4{
            width: 110px;
        }
    }
}
// 定期购配送页面楼层（新版）
.process-floor{
    // width: 100%;
    // height: 72px;
    // background: rgba(255, 241, 240, 1);
    margin-bottom: 16px;
    .step{
        .left{
            // float: left;
            font-size: 14px;
            color: rgba(26, 26, 26, 1);
            em{
                background-image: url(https://img12.360buyimg.com/imagetools/jfs/t1/316760/2/5895/526/683ea655F6c1b50b9/1a3ba228f4a835bb.png);
                background-repeat: no-repeat;
                width: 20px;
                height: 8px;
                overflow: hidden;
                margin-top: 16px;
                display: inline-block;
                background-size: 100%;
                margin: 0 15px;
                vertical-align: middle;
            }
        }
        .right{
            margin-top: 4px;
            // float: right;
            a {
                color: #888B94;
                font-size: 14px;
            }
        }
    }
    .part{
        display: none;
    }
}
// 优惠阶梯楼层
.discount-floor {
    color: #1A1A1A;
    font-size: 14px;
    font-family: JDZhengHeiVRegular2-1;
    height: 34px;
    line-height: 34px;
    margin-bottom: 16px;
    .dt {
        height: 34px;
        line-height: 34px;
    }
    .dd {
        background-image: url(https://img10.360buyimg.com/imagetools/jfs/t1/303190/2/12005/2457/683fb7a9Fc140e6c1/40acff7364cdd1c5.png);
        background-size: 116px 34px;
        background-position: 3px 0;
        width: max-content;
    }
    span {
        display: inline-block;
        text-align: center;
        padding-right: 3px;
        width: 113px;
        height: 34px;
        line-height: 34px;
        background-image: url(https://img10.360buyimg.com/imagetools/jfs/t1/292863/1/11199/1210/683fb79dF186b0eac/f0ac427a1f2aa635.png);
        background-size: 113px 34px;
        background-repeat: no-repeat;
        background-position: 0 0;
        &:first-child {
            background-image: url(https://img10.360buyimg.com/imagetools/jfs/t1/304182/2/7700/881/683fb795Fabfbbcab/31e0562d364a879e.png);
        }
        &:last-child {
            background-image: url(https://img12.360buyimg.com/imagetools/jfs/t1/296590/35/13032/891/683fb7a4F342db034/7a18383b798335db.png);
            padding-right: 0;
        }
    }
}
// 定期计划
.choose-plan {
    margin-bottom: 8px;
    .dt {
        height: 34px;
        line-height: 34px;
    }
    .dd {
        span {
            height: 34px;
            line-height: 34px;
            margin-bottom: 8px;
            padding: 0 12px;
            font-size: 14px;
            color: #1A1A1A;
            background-color: #F7F8FC;
            border-radius: 5px;
            display: inline-block;
            margin-right: 8px;
            cursor: pointer;
            border: 1px solid rgba(0, 0, 0, 0.02);
            &:hover, &.check {
                background-color: #FFEBF1;
                color: #FF0F23;
                border: 1px solid #FF0F23;
            }
        }
    }
}
// 每次件数/配送期数
.choose-num, .choose-term {
    margin-bottom: 16px;
    .dt {
        height: 34px;
        line-height: 34px;
    }
    .dd {
        .center-column {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .tip-color {
            color: red;
            line-height: 34px;
            padding-left: 8px;
            font-size: 14px;
        }
        .last {
            height: 34px;
            text-align: center;
            float: left;
            .count-set {
                > * {
                    float:left;
                    width: 36px;
                    height: 34px;
                    line-height: 34px;
                    box-sizing: border-box;
                    border: 0.5px solid rgba(0, 0, 0, 0.02);
                    border-radius: 4px;
                    vertical-align: middle;
                    text-align: center;
                    background-color: #F7F8FC;
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: 16px;
                    &:first-child {
                        background-image: url(https://img10.360buyimg.com/imagetools/jfs/t1/305643/9/7486/189/683ec2abF870106ec/7eda75352f2aacd1.png);
                        font-size: 0; // 隐藏旧版加减号
                        &.disabled {
                            background-image: url(https://img13.360buyimg.com/imagetools/jfs/t1/270202/29/11544/184/683ec2c8F0c41e641/d2a20e90de466ceb.png);
                        }
                    }
                    &:last-child {
                        background-image: url(https://img12.360buyimg.com/imagetools/jfs/t1/292102/28/10429/296/683ec2ebFb526987e/420dfc831ef6456b.png);
                        font-size: 0; // 隐藏旧版加减号
                        &.disabled {
                            background-image: url(https://img13.360buyimg.com/imagetools/jfs/t1/317595/21/5650/305/683ec302F262bd138/d4b0e1f8add4625c.png);
                        }
                    }
                }
                > input {
                    width: 60px;
                    font: 18px 'JDZhengHeiVRegular2-1';
                    color:#1A1A1A;
                    background-color: #fff;
                    border: none;
                    outline: 0;
                    padding: {
                        top: 6px;
                        bottom: 6px;
                    }
                    &.disabled {
                        color: #ccc;
                        cursor: not-allowed;
                    }
                }
            }
            i {
                width: 30px;
                font: normal 20px/30px 'Arial';
                color:#666;
                cursor: pointer;
                -webkit-user-select:none;
                -moz-user-select:none;
                -o-user-select:none;
                user-select: none;
                &.disabled {
                    cursor: not-allowed;
                    color: #ccc;
                }
            }
        }
    }
}
// 按钮左侧价格和配送期数等
.price-and-num {
    margin-right: 68px;
    .goods-price-top {
        font-size: 14px;
        margin-bottom: 12px;
        span {
            color: #FF0F23;
            font-family: JDZhengHeiVRegular2-1;
        }
    }
    .goods-price-bottom {
        display: flex;
        align-items: center;
        font-size: 20px;
        .price-total {
            font-weight: bold;
            margin-right: 4px;
        }
        .total {
            color: #FF0F23;
            font-family: JDZhengHeiVRegular2-1;
        }
        .price-save{
            color: #fff;
            font-size: 14px;
            font-family: JDZhengHeiVRegular2-1;
            padding: 2px;
            border-radius: 2px;
            background-color: #FF0F23;
        }
    }
}
// 定期购顶部切换按钮
.batch-plan{
    background: #f3f3f3;
    margin-bottom: 10px;
    border-bottom: 1px solid #df3033;
    .plan-tab{
        padding: 7px 15px;
        display: inline-block;
        text-align: center;
        margin: 0;
        cursor: pointer;
        span{
            border: 1px solid #fa2c19;
            padding: 1px 2px;
            border-radius: 3px;
            color: #fa2c19;
            margin-left: 5px;
        }
    }
    .curr{
        background: #df3033;
        color: #fff; 
        span{
            border: 1px solid #fff;
            padding: 1px 2px;
            border-radius: 3px;
            color: #fff;
            margin-left: 5px;
        }
    }
}
.yjhxTip{
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -300px;
    width: 182px;
    height: 244px;
    background: #fff;
    border:#0000001A;
    border-radius: 8px;
    padding: 24px 32px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    display: none;
    .close{
        position: absolute;
        display: inline-block;
        width: 16px;
        height: 16px;
        right: 15px;
        top: 26px;
        background: url(https://img11.360buyimg.com/imagetools/jfs/t1/268448/37/5719/285/6772b2c4F00fdd140/e0434b79ecd2460c.png);
        background-size: 100%;
        cursor: pointer;
    }
    .title{
        text-align: center;
        font-size: 18px;
        color: #1A1A1A;
        font-weight: 600;
        margin-bottom: 10px;
        height: 20px;
        line-height: 20px;
    }
    .bottom{
        font-size: 14px;
        color: #888B94;
        top: 204px;
        position: relative;
        text-align: center;
        font-weight: 400;
        left: 2px;
        top: 200px;
        height: 20px;
        line-height: 20px;
        span{
            color:#FF0F23;
        }
    }
    #summary-mbuy{
        left: 34px;
        width: 146px;
        height: 146px;
        padding: 16px;
        margin-top: -50px;
        box-shadow: unset;
        -webkit-box-shadow: unset;
        border-top: 1px solid #eee;
        .qrcode{
            margin: 0;
            padding: 0;
            width: 147px;
            height: 147px;
        }
    }
}


// 国补新增样式
// https://joyspace.jd.com/pages/lyQyplK7JsCpUZSXVJtg
.right-drawer-dialog{
    top: 0 !important;
    left: unset !important;
    right: 0 !important;
    position: fixed !important;
    border-radius: unset !important;
    transform: translateX(100%);
    transition: transform .2s ease-in-out !important;
    padding: 0 !important;

    &.show-dialog{
        transform: translateX(0%);
    }

    .ui-dialog-content {
		padding: 0;
        position: relative;

        &::before{
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate3d(-50%, -50%, 0);
            z-index: 0;
            width: 56px;
            height: 56px;
            background: hsl(0, 0%, 90%) url(https://img13.360buyimg.com/imagetools/jfs/t1/302979/22/10544/58635/004f1c01F80cb71c4/19a4702b0af5a16d.gif) center center no-repeat;
            background-size: contain;
            border-radius: 8px;
        }

        iframe{
            position: relative;
            z-index: 100;
        }
    }
}