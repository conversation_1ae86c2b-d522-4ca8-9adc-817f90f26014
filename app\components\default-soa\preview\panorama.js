define('MOD_ROOT/preview/panorama', function(require, exports, module) {
    var Viewer = require('MOD_ROOT/preview/panorama.3d');

    function Panorama(config) {
        if (!config || !config.pics || !config.element) return
        this.pics = config.pics
        this.element = config.element
        this.panorama = this.element.find('.panorama-img')
        this.lastIdex = null
        this.img = this.element.find('img')
        this.tips = this.element.find('.panorama-tips')
        this.gap = Math.floor(this.element.width() / this.pics.length)
        this.imgpre = '//img14.360buyimg.com/n0/s534x534_'
        this.imgpreFor3D = '//img14.360buyimg.com/da/'
        this.init()
    }
    Panorama.prototype.beforeLoadImg = function() {
        this.img.attr(
            'src',
            '//img12.360buyimg.com/devfe/jfs/t6079/288/4724556079/25029/8a4987d6/5967412eNb883a2a9.jpg'
        )
        this.tips.text('正在为您开启全景世界')
    }
    Panorama.prototype.LoadImgError = function() {
        this.img.attr(
            'src',
            '//img30.360buyimg.com/devfe/jfs/t6088/341/4782575589/3802/f0639f4/59674139Nbba5386e.jpg'
        )
        this.tips.text('服务器太忙，暂时无法显示，请您稍后再试')
    }
    Panorama.prototype.init = function() {
        this.loadImg(
            $.proxy(this.beforeLoadImg, this),
            $.proxy(this.bindEvent, this),
            $.proxy(this.LoadImgError, this)
        )
    }
    Panorama.prototype.loadImg = function(before, complete, error) {
        var img,
            i = 0,
            total = 0,
            _this = this

        if (!this.pics.length) {
            error && error()
            return
        }

        before && before()
        
        // 如果是3D家 则不展示全景展示
        if(pageConfig.product.is3DHome){
            if(pageConfig.notSupportWebGL)return ;
            var oImg = document.createElement('img'),
            imgFullPath =  _this.imgpreFor3D + _this.pics[0]+'!q50';
            oImg.src =imgFullPath;
            oImg.onload = function(){
               
                _this.panorama.find('img,.panorama-tips').css('display', 'none')
                Viewer.build3D('.panorama-img',imgFullPath);
                
                _this.panorama.css({
                    width:'100%',
                    height: '100%',
                    margin: 0,
                    position: 'relative',
                    top: 0,
                    left: 0});
            };
            oImg.onerror=function(){
                error && error()
            };
            return;
        }
        

        for (; i < this.pics.length; i++) {
            ;(function() {
                img = document.createElement('img')
                img.onload = function() {
                    total++
                    if (total == _this.pics.length) {
                        _this.img.remove()
                        _this.panorama
                            .find('img')
                            .eq(0)
                            .css('display', 'inline')
                        // _this.img.attr('src', _this.imgpre + _this.pics[0])
                        // _this.img.attr({'width':534, 'height':534})
                        complete && complete()
                    }
                }
                img.onerror = function() {
                    total--
                    error && error()
                }
                img.src = _this.imgpre + _this.pics[i]
                $(img)
                    .insertBefore(_this.tips)
                    .attr({width: 534, height: 534})
                    .css('display', 'none')
            })()
        }
        /* setTimeout(function(){
            if(total < _this.pics.length) {
                total--
                error && error()
            }				
        },5000) */
    }
    Panorama.prototype.caclIndex = function(clientX) {
        return Math.floor(clientX / this.gap)
    }
    Panorama.prototype.move = function(evt) {
        var clientX = evt.clientX - this.element.offset().left,
            idx = this.caclIndex(clientX)
        if (idx != this.lastIdex && idx < this.pics.length && idx >= 0) {
            this.lastIdex = idx
            this.panorama.find('img').css('display', 'none')
            this.panorama.find('img').eq(idx).css('display', 'inline')
            // this.img.attr('src', this.imgpre + this.pics[idx])
        }
    }
    Panorama.prototype.bindEvent = function() {
        this.element.removeClass('p-tips')
        this.element.bind('mousemove', $.proxy(this.move, this))
    }

    module.exports.Panorama = Panorama
})
