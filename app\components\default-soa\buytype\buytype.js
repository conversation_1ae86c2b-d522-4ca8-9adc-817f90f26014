define('MOD_ROOT/buytype/buytype', function(require, exports, module) {
    var Promotions = require('MOD_ROOT/prom/prom').Promotions
    var Stock = require('MOD_ROOT/address/stock')
    var Event = require('MOD_ROOT/common/tools/event').Event
    var tools = require('MOD_ROOT/common/tools/tools');
    var __5G = require("MOD_ROOT/buytype/buytype.5g");

    require('MOD_ROOT/ETooltips/ETooltips')
    require('JDF_UNIT/trimPath/1.0.0/trimPath')

    var CellPhone = {
        /**
         * 初始化合约机购买逻辑
         * @param Number skuid
         * @param String provinceId
         * @param String cityId
         */
        init: function(cfg, pid, cid, mainSkuId, onReady, onError) {
            this.$el = $('#choose-type')
            this.$hy = $('#choose-type-hy')
            this.$suit = $('#choose-type-suit')

            this.$btn = $('#btn-heyue')
            this.$qiang = $('#btn-qiang')
            this.$onkeybuy = $('#btn-onkeybuy')

            this.$tipsTxt = this.$suit.find('#J-suit-tips')

            // 加价购
            this.$jjg = $('#prom-phone-jjg')
            // 手机类专用促销
            this.$promPhone = $('#prom-phone')
            // 赠品促销
            this.$promGift = $('#prom-gift')
            // 手机类专用广告词
            this.$phoneAd = $('#p-ad-phone')

            // 购买数量
            this.$amount = $('.choose-amount')
            // 不支持合约机提示
            this.$disabled = $('#btype-tip')

            // 占位元素
            this.$yysPlanTips = $()
            this.$yysArrow = $()

            // ---
            this.$promiseIcon = $('.J-promise-icon')

            this.sku = cfg.skuid
            this.pid = pid
            this.cid = cid
            this.mainSkuId = mainSkuId || cfg.mainSkuId
            this.sp = ''

            this.cfg = cfg

            this.onReady = onReady || function() {}
            this.onError = onError || function() {}

            // 当前选中的购买方式
            this.$currBuyType = null
            // 当前选中的合约类型
            this.$currHeYue = null
            // 当前选中的套餐类型
            this.$currSuit = null

            // this.url = '//feetype.jd.com/services/queryFeeTypes.action'

            // if (/debug=yys/.test(location.href)) {
            //     this.url = '//yys.worker.jd.com/services/queryFeeTypes.action'
            // }

            // 选中的购买方式
            this.buyTypeIndex = null
            this.disableClass = 'btn-disable'

            this.dtype = null
            this.isHySuit = false
            this.noBuyType = false

            // 切换地区重新加载前清除一次dom
            this.destory()
            this.bindEvent()
            // this.getBuyType(this.sku, this.pid, this.cid, this.mainSkuId)

            // url上feetype值是否和当前购买方式下的合约类型里面的任何一个匹配
            pageConfig.matchFeetype = function(ft) {
                var urlFt = CellPhone.getUrlFeeType()
                var isMatched = false
                var i = 0

                if (urlFt && ft && ft.length) {
                    for (i = 0; i < ft.length; i++) {
                        if (ft[i].ft == urlFt) {
                            isMatched = true
                            break
                        }
                    }
                }

                return isMatched
            }
        },

        /**
         * 绑定事件
         */
        bindEvent: function() {
            var _this = this

            if (this.binded) {
                return
            }
            this.binded = true

            // 购买方式点击
            this.$el.undelegate('click').delegate('.item', 'click', function() {
                var $this = $(this)
                var isDisabled = $this.hasClass('disabled')
                var isSelected = $this.hasClass('selected')

                if (isDisabled || isSelected) return

                _this.$currBuyType = $this
                _this.handleBuyTypeClick($this)
            })

            // 合约类型点击
            this.$hy.undelegate('click').delegate('.item', 'click', function() {
                var $this = $(this)
                var isDisabled = $this.hasClass('disabled')
                var isSelected = $this.hasClass('selected')

                if (isDisabled || isSelected) return

                _this.$currHeYue = $this
                _this.handleHyTypeClick($this)
            })

            // 套餐弹出层
            this.$suit
                .undelegate('click')
                .delegate('.J-suit-trigger', 'click', function() {
                    var $this = $(this)
                    // 如果有data-url说明已经选择过，需要传iframe给的url
                    var url =
                        $this.attr('data-url') ||
                        _this.$currHeYue.attr('data-aurl')
                    var auth = _this.$currHeYue.attr('data-auth')

                    _this.$currSuit = $this

                    _this.showSuitIframe(url, auth)
                })

            // 购买按钮反显
            this.$btn.unbind('click').bind('click', function() {
                // 如果当前是套装且没选择过
                if (_this.hasSuitIframe && !_this.suitSelected) {
                    _this.$suit.addClass('item-hl-bg')
                    _this.$tipsTxt.show()
                    return false
                }
            })

            // function reInit(data) {
            //     Promotions.setJdPrice(data.stock.stock);
            //     var aids = tools.getAreaId().areaIds;
            //     _this.init(_this.cfg, aids[0], aids[1]);
            // }
            // function checkStatus() {
            //    _this.setBuyBtn();
            //    _this.switchBtn(_this.isHeYue);
            // }
            // Event.removeListener('onAreaChange', reInit)
            // Event.addListener('onAreaChange', reInit)

            // Event.removeListener('onDefaultStockReady', checkStatus);
            // Event.addListener('onDefaultStockReady', checkStatus);
        },

        /**
         * 购买方式模板
         */
        getTPL: function(type) {
            var btypeTPL =
                '\
            <div class="dt">购买方式</div>\
            <div class="dd">\
                {for list in datas}\
                {var btype = list.feetypes[0]}\
                <div class="J-btype-item item{if list.che} selected{/if}" \
                    clstag="shangpin|keycount|product|gmfs-${list.name}" \
                    data-ind="${list_index}" \
                    data-matched="${pageConfig.matchFeetype(list.feetypes)}"\
                    {if !(list.sp===-1&&btype.ft===100)}\
                    data-ishy="true" \
                    {/if}\
                    data-ft="${btype.ft}"\
                    data-sp="${list.sp}" \
                    data-count="${list.feetypes.length}"\
                    clstag="shangpin|keycount|product|goumaifangshi_${list.name}">\
                    <b></b>\
                    <a href="#none" title="">${list.name}</a>\
                </div>\
                {/for}\
            </div>'

            var suitTPL =
                '\
            <div class="dt">优惠类型</div>\
            <div class="dd">\
                {for item in datas}\
                <div class="J-hy-btype {if !item.che} hide{/if}">\
                    {for list in item.feetypes}\
                    <div class="item{if list.che||(item.sp===-1&&list.ft===100)} selected{/if}{if !list.stat} disabled{/if}" \
                        clstag="shangpin|keycount|product|hylx-${list.name}" \
                        data-aurl="${list.aurl}"\
                        data-rurl="${list.rurl}"\
                        data-type="${list.type}"\
                        data-dtype="${list.dtype}"\
                        data-id="${list.ft}" \
                        data-skus="${list.sids}" \
                        data-ad="${list.ad}" \
                        data-ind="${list_index}" \
                        data-sku="${list.sku}"\
                        data-sp="${item.sp}">\
                        <b></b>\
                        <a href="#none" title="${list.tips}">${list.name}</a>\
                    </div>\
                    {/for}\
                </div>\
                {/for}\
                {if list.ft!==100}\
                    <a class="J-hy-btype-tips hy-btype-tips icon question fl" \
                        href="#none"><i class="sprite-question"></i></a>\
                {/if}\
            </div>'

            var jjgPromTPL =
                '\
            <div class="J-prom-phone-jjg">\
                <em class="hl_red_bg">${title||"加价购"}</em>\
                <em class="hl_red">${text}</em>\
                <a title="参加活动入口" clstag="eve|keycount|treaty|JJGDetails" class="J-jjg-btn" href="#none">\
                    &nbsp;&nbsp;点击抢购 <s class="s-arrow">&gt;</s>\
                </a>\
            </div>'

            var jjgIframeTPL =
                '\
            <div id="shf-feetype">\
                  <dl class="dl-1">\
                      <dt class="fl">您需消费：</dt>\
                      <dd class="lh hl_red">${info.cons}</dd>\
                  </dl>\
                  <dl class="dl-2">\
                      <dt class="fl">您将得到：</dt>\
                      <dd class="lh">\
                          <ul class="hl_red">\
                              <li>手机 <em>&times; 1</em></li>\
                              <li>${stockArea}（手机卡） <em>&times; 1</em></li>\
                              <li>赠送话费 ${info.pres}</li>\
                          </ul><div class="clr"></div>\
                          <p>${info.pack} </p>\
                          <table>\
                              <tr>\
                                  <td><div><strong>语音通话(分钟)</strong></div>{if info.cal!=="null"}${info.cal}{/if}</td>\
                                  <td><div><strong>国内短信(条)</strong></div>{if info.msg!=="null"}${info.msg}{/if}</td>\
                              </tr>\
                              <tr>\
                                  <td><div><strong>国内流量</strong></div>{if info.traf!=="null"}${info.traf}{/if}</td>\
                                  <td><div><strong>接听免费</strong></div>{if info.area!=="null"}${info.area}{/if}</td>\
                              </tr>\
                          </table>\
                          <div class="shf-buy-now">\
                              <a class="btn-primary J-btn" href="${resLink}" target="_blank" clstag="eve|keycount|treaty|JJGRedirect" class="css3-btn">选择号码</a>\
                          </div>\
                      </dd>\
                  </dl><span clstag="eve|keycount|treaty|JJGDetails" id="JJGDetails">\u3000</span>\
            </div>'

            if (type === 'btype') {
                return btypeTPL
            }
            if (type === 'suit') {
                return suitTPL
            }
            if (type === 'jjg-prom') {
                return jjgPromTPL
            }
            if (type === 'jjg-iframe') {
                return jjgIframeTPL
            }
        },

        /**
         * 处理选择购买方式点击事件逻辑
         * @param $ele Object 当前点击jQuery对像
         */
        handleBuyTypeClick: function($ele) {
            var ind = $ele.attr('data-ind')
            var sp = $ele.attr('data-sp')
            var isHeYue = $ele.attr('data-ishy') === 'true'
            var ft =  $ele.attr('data-ft')
            this.switchTo($ele)

            if(ft != '100'){
                this.$amount.css({'visibility':'hidden','marginRight':0})
            }else{
                this.$amount.css({'visibility':'visible','marginRight':'10px'})

            }

            // 当前购买方式下下标
            this.buyTypeIndex = ind
            // 当前购买方式是否是合约机
            this.isHeYue = isHeYue
            pageConfig.product.isHeYue = isHeYue

            this.$currHeYue = this.$hy
                .find('.J-hy-btype')
                .eq(this.buyTypeIndex)
                .find('.selected')

            this.switchBtn(isHeYue)

            this.setResult(this.getParams(this.$currHeYue))
        },

        /**
         * 购买方式是否是合约机
         */
        switchBtn: function(isHeYue) {
            var _this = this
            // 当前选中的是裸机还是合约
            if (isHeYue) {
                // 合约机相关事件处理
                this.onHeYue()
            } else {
                // 非合约机相关事件处理
                this.onNonHeYue()
            }

            // 合约机切换重新调用库存接口
            var param = { extraParam: '{"originid":"1"}' }
            if (this.isHeYue) {
                param.extraParam = '{"originid":"1","heYueJi":"1"}'
            }
            new Stock(param, function onSuccess(r) {
                _this.setBuyBtn()
            })

            Event.fire({
                type: 'onHeYueReady',
                bType: this.bType,
                noBuyType: this.noBuyType
            })
        },

        /**
         * 处理选择合约类型点击事件逻辑
         * @param Object 当前点击jQuery对像
         */
        handleHyTypeClick: function($ele) {
            var ind = $ele.attr('data-ind')
            var $triggers = $ele.parent().find('.item')
            var $currItem = $triggers.removeClass('selected').eq(ind)

            $currItem.addClass('selected')

            this.$currHeYue = $currItem

            this.setResult(this.getParams(this.$currHeYue))
        },

        /**
         * 切换显示购买方式
         */
        switchTo: function($ele) {
            var ind = $ele.attr('data-ind')
            var $triggers = $ele.parent().find('.J-btype-item')
            var $contents = this.$hy.find('.J-hy-btype')

            $triggers.removeClass('selected').eq(ind).addClass('selected')

            $contents.hide().eq(ind).show()
        },

        /**
         * 获取购买方式
         * @param Number skuid
         * @param String provinceId
         * @param String cityId
         */
        // getBuyType: function(sku, pid, cid, mainSkuId) {
        //     var _this = this
        //     window.fetch_feetype_data = function() {}
        //     $.ajax({
        //         url: this.url,
        //         dataType: 'jsonp',
        //         scriptCharset: 'utf-8',
        //         timeout: 5000,
        //         cache: false,
        //         jsonpCallback: 'fetch_feetype_data',
        //         data: {
        //             skuId: sku,
        //             cityId: cid,
        //             provinceId: pid,
        //             productId: mainSkuId
        //         },
        //         error: function(err) {
        //             _this.onReady.call(this, 100)
        //             _this.onError.call(this, 100)
        //         },
        //         success: function(r) {
        //             _this.handleBuyTypeData(r)
        //         }
        //     })
        // },

        /**
         * 处理合约机子流程
         * 1. 加价购
         * 2. 套餐iframe
         * @param r 返回json数据
         */
        handleBuyTypeData: function(r) {
            var hasBuyType = (pageConfig.chooseType = !!r && r.datas.length > 0)
            var toggle = hasBuyType ? 'show' : 'hide';
            //当前ft != 100 hide
            var che = 1;
            if(hasBuyType){
                for(var i = 0;i<r.datas.length;i++){
                    if(r.datas[i] && r.datas[i]['che'] && r.datas[i]['feetypes'] && r.datas[i]['feetypes'][0] && r.datas[i]['feetypes'][0]['ft'] != '100'){
                        che = 0;
                    }
                }

            }

            // 1. 加价购
            var hasJJG = hasBuyType && r.promo

            // 2. 套餐 iframe 挂载一个标识模板渲染的时候直接出html
            // 是否是「套餐变更」，会弹出层提示「53%的用户正在使用138元档移动4G套餐或1G流量月包 去看看」
            //this.hasActivity = hasBuyType && r.activity && r.activity.ad;

            // 切换显示数量
            if(che){
                this.$amount.css({'visibility':'visible','marginRight':'10px'})
            }else{
                this.$amount.css({'visibility':'hidden','marginRight':0})
            }

            if (hasBuyType) {
                //r.hasActivity =  this.hasActivity;
                this.r = r
                this.setBuyType(r)

                // 1. 加价购
                if (hasJJG) {
                    this.setJiaJiaGou(r.promo)
                }
            } else {
                // 重置预售标识
                //pageConfig.product.isHeYue = false;
                // 如果没有合约机，则走普通裸机流程
                Promotions.init(pageConfig.product.skuid)
                // 分区合约机
                //pageConfig.M_addToCart.show();
                this.$el.html('<!--oops, 木有数据了^!^-->')
                // 接口数据为空

                this.disabled()
                this.onReady.call(this, '100')
            }
        },

        /**
         * 渲染购买方式
         * @param Ojbect 合约机接口json数据
         */
        setBuyType: function(r) {
            var tpl = this.getTPL('btype')
            var result = tpl.process(r)
            var errMsg = result.match(/\[ERROR:.+\]/g)

            if (r && r.sp) {
                this.sp = r.sp
            }

            // 错误提示
            if (errMsg && errMsg[0]) {
                $('#summary-promotion').hide()
                console.error('[BuyType]Template Rendering error.\n')
                console.error(errMsg[0])
                return this.onError.call(this, 100, errMsg)
            }
            // 合约机是否可用
            if (r.dis) {
                this.$disabled.show()
            } else {
                this.$disabled.hide()
            }

            this.$el.html(result)

            this.noBuyType =
                r.datas.length == 1 &&
                r.datas[0].feetypes.length == 1 &&
                r.datas[0].feetypes[0].name === '-1'

            if (this.noBuyType) {
                this.$el.hide()
            } else {
                this.$el.show()
            }

            this.setHyType(r)
        },

        /**
         * 渲染合约类型
         * @param Ojbect 合约机接口json数据
         */
        setHyType: function(r) {
            var _this = this
            var tpl = this.getTPL('suit')
            var result = tpl.process(r)
            var errMsg = result.match(/\[ERROR:.+\]/g)

            // 错误提示
            if (errMsg && errMsg[0]) {
                $('#summary-promotion').hide()
                console.error('[BuyType]Template Rendering error.\n')
                console.error(errMsg[0])
            }

            this.$hy.html(result)

            if (this.noBuyType) {
                this.$hy.hide()
            } else {
                this.$hy.show()
            }

            var feetype = this.getUrlFeeType()
            if (feetype) {
                var $tar = this.$el.find('[data-matched="true"]')
                this.switchTo($tar)
            }

            this.$currBuyType = this.$el.find('.selected')
            // 当前购买方式下下标
            this.buyTypeIndex = this.$currBuyType.attr('data-ind')
            // 当前选中的购买方式、合约类型
            this.$currBuyTypes = this.$hy
                .find('.J-hy-btype')
                .eq(this.buyTypeIndex)
                .find('.item')

            var $urlHeYue = this.$currBuyTypes.filter(
                '[data-id="' + feetype + '"]'
            )
            if (feetype && $urlHeYue) {
                this.$currHeYue = $urlHeYue
                this.$currBuyTypes.removeClass('selected')
                this.$currHeYue.addClass('selected')
            } else {
                this.$currHeYue = this.$currBuyTypes.filter('.selected')
            }

            this.bType = this.$currHeYue.attr('data-id')
            this.isHeYue = this.$currBuyType.attr('data-ishy') === 'true'

            pageConfig.product.isHeYue = this.isHeYue

            // 如果当前没有可以选择的购买合约类型
            if (!this.$currHeYue.length) {
                this.disabledBuy = true
            }

            // 合约机套装iframe
            //if ( this.hasActivity ) {
            //    this.setPlanIframe(this.bType);
            //}

            this.switchBtn(this.isHeYue)

            var param = this.getParams(this.$currHeYue)

            this.setResult(param)

            // 合约机重新请求库存接口，需要传不同的 extraParam 给设库接口用
            //var param = {extraParam:'{"originid":"1"}'};
            //if (this.isHeYue) {
            //    param.extraParam = '{"originid":"1","heYueJi":"1"}';
            //}
            //new Stock(param, function onSuccess(r) {
            //    _this.setBuyBtn();
            //});
        },

        getTips: function() {
            var text0 = '移动老用户，无需换号，只需承诺业务内容，即可每月获得高额的话费返还'
            var text1 = '同时购买手机和新号码，即可根据所选的套餐档位，按月获得一定额度的话费赠送'

            var union_telecom = {
                name: '联通/电信',
                '8': '同时购买手机和新号码，并预存一定金额的话费（后续将分月返还），即可抵扣等值的购机款，购机款最高可减至0元',
                '28': '同时购买手机和新号码，即可根据所选的套餐档位，按月获得一定额度的话费赠送',
                '27': '购买新手机时办理一张新电话卡，可以自由选择套餐中语音、流量、短信的搭配和用量',
                '33': '电信老用户，不换号，买手机，办理新套餐，即可按照优惠规则按月获得额外的话费赠送',
                '2': text1,
                '5': text1,
                '12': text1
            }

            var map = {
                '-1': '',
                '1': {
                    name: '移动',
                    '5': text0,
                    '12': text0,
                    '18': '全国移动老用户均可办理，承诺在网时长和套餐内容，即可每月获赠一定额度的话费（移动购机专享无话费返还）',
                    '32': '上海移动老用户，承诺月最低消费，即可每月享受一定额度的话费赠送',
                    '34': '北京移动老用户，不换号，买手机，即可每月享受一定额度的话费赠送',
                    '35': '山西移动老用户，不换号，买手机，即可每月享受一定额度的话费赠送',
                    '36': '福建移动老用户，不换号，办合约，即可每月享受一定额度的话费赠送'
                },
                '2': union_telecom,
                '3': union_telecom,
                '4':{
                    name:'京东联通',
                    0:''
                },
                '5':{
                    name:'京东通信',
                    0:''
                },
            }

            return map
        },

        setTips: function($el) {
            var $tips = this.$hy.find('.J-hy-btype-tips')

            var sp = $el.attr('data-sp') || -1
            var id = $el.attr('data-id') || -1
            var tips = this.getTips()[sp];
            //console.log('SP: %s | ID: %s', sp, id);
            var content = ''
            if(tips && tips[id]){
                content = tips[id]
            }

            if (content) {
                $tips.show().ETooltips({
                    close: false,
                    content: content,
                    width: 200,
                    position: 'bottom',
                    zIndex: 10
                })
            } else {
                $tips.hide()
            }
        },

        /**
         * 获取节点购买方式数据
         * @param Object 节点jquery dom元素
         */
        getParams: function($el) {
            return $el && $el.length
                ? {
                    sku: $el.attr('data-sku'),
                    skus: $el.attr('data-skus'),
                    id: $el.attr('data-id'),
                    ad: $el.attr('data-ad'),
                    ind: $el.attr('data-ind'),
                    type: $el.attr('data-type'),
                    dtype: $el.attr('data-dtype')
                }
                : null
        },

        /**
         * iframe调用此方法传递参数
         */
        setSuitContent: function(param) {
            //param = {
            //    auth: 'encypt',
            //    message: '158元/月 * 24月 13691111111',
            //    aurl: '//yys.eve.jd.com?auth={auth}',
            //    rurl: '//order.eve.jd.com?auth={auth}'
            //};
            // aurl 弹出层地址
            // rurl 购买地址
            this.suitSelected = true
            if (param) {
                this.disabledBuy = false

                this.$suit
                    .find('.J-suit-trigger')
                    .attr(
                        'data-url',
                        this.formatBtypeUrl(param.aurl, param.auth)
                    )
                    .addClass('selected')
                    .find('a')
                    .html(param.message)

                this.$suit.find('.J-suit-resel').show()

                this.$suit.removeClass('item-hl-bg').find('.J-suit-tips').hide()

                //this.$btn.attr('href', this.formatBtypeUrl(param.rurl, param.auth));
                this.setBuyBtn(this.formatBtypeUrl(param.rurl, param.auth))
            }
        },
        getSuitName: function(dtype) {
            var nameMap = {
                '0': '选择套餐与号码',
                '1': '选择套餐与资费',
                "2": "选择套餐"
            }
            return nameMap[dtype]
        },
        setSuitName: function(dtype) {
            this.$suit.find('.J-suit-trigger a').html(this.getSuitName(dtype))
        },
        clearSuit: function() {
            var name = this.getSuitName(this.dtype)

            this.suitSelected = false
            this.$suit.find('.J-suit-resel').hide()
            this.$suit
                .find('.J-suit-trigger')
                .removeClass('selected')
                .removeAttr('data-url')
                .find('a')
                .html(name)
                .attr('title', name)
        },
        /**
         * 设置默认高亮选中的合约类型
         * @param Object 当前选中合约类型参数
         */
        setResult: function(param) {
            //var feetype = this.getUrlFeeType() || (param && param.id);
            var feetype = param && param.id

            // 当前选中的合约类型是否有套餐 aurl
            this.hasSuitIframe = !!this.$currHeYue.attr('data-aurl')

            // 抢购用
            this.bType = feetype
            this.dtype = param ? param.dtype : null

            // 当前选中的有且只有一个合约类型而且是祼机 -> 不显示合约类型
            this.isOnlyNonHeYue =
                this.$currBuyTypes.length === 1 && this.bType === '100'

            // this.$hy[this.isOnlyNonHeYue ? 'hide': 'show']();

            this.setTips(this.$currHeYue)

            if (this.hasSuitIframe && this.noBuyType) {
                this.clearSuit()
                this.setSuitName(param.dtype)
                this.$suit.show()
            } else {
                this.$suit.hide()
            }

            if (this.isOnlyNonHeYue) {
                this.$hy.hide()
            } else {
                if (!this.noBuyType && this.noBuyType) {
                    this.$hy.show()
                }
            }

            if (typeof this.onReady === 'function') {
                this.onReady.call(this, feetype)
            }

            // 如果当前没有选中的参数 || (this.hasSuitIframe && !this.$currSuit)
            this.disabledBuy = !param
            if (this.disabledBuy) {
                return this.setBuyBtn()
            }

            // 如果购买方式sku和当前商品sku不一样
            if (param.sku && this.sku !== Number(param.sku)) {
                //console.error('Warning. sku not matched. page will relocation.');
                //@test
                //alert('当前商品sku与选择的合约机sku不一致，页面会跳转到合约机的页面。—— for test');
                //location.href = '//item.jd.com/' + param.sku + '.html?feetype=' + param.id;
                //alert('//' + location.host + '/' + param.sku + '.html?feetype=' + param.id);
                //location.href = '//' + location.host + '/' + param.sku + '.html?feetype=' + param.id;
                location.href = '//' + location.host + '/' + param.sku + '.html'
            }

            // 如果有套装流程而且还没选中过
            if (this.hasSuitIframe && this.suitSelected) {
                return this.setBuyBtn()
            }

            // 合约机广告词
            if (!!param.ad) {
                this.$phoneAd.html(param.ad).show()
                var title = param.ad.replace(/<.*?>/g, '')
                this.$phoneAd.attr('title', title)
            } else {
                this.$phoneAd.html('').hide()
                this.$phoneAd.attr('title', '')
            }

            // 当前合约是套装类型
            // Yes > 走单独的手机合约套装促销接口逻辑
            // No > 走普通商品的促销接口逻辑
            if (!!param.skus) {
                // 清除原来裸机套装内容
                Promotions.clear()
                this.getPhoneProm(param.skus)
            } else {
                this.clearPhoneProm()

                // 如果上次选择的和当前选择的合约类型不一样就重新调用
                if (param.skus !== this.isHySuit) {
                    Promotions.init(this.sku)
                }
            }
            // 裸机绑合约类型
            this.isHySuit = !!param.skus

            // 设置合约机按钮文案、购买链接
            this.setBuyBtn()

            //pageConfig.product.isHeyue = param.id !== '100';
            //if ( this.isHeYue ) {
            //    // 合约机相关事件处理
            //    this.onHeYue(param);
            //} else {
            //    // 非合约机相关事件处理
            //    this.onNonHeYue(param);
            //}

            //if (typeof this.onReady === 'function') {
            //    this.onReady.call(this, feetype);
            //}

            // 选择购买方式后要重新刷新promise
            //if ( typeof GetCurrentStock !== 'undefined' ) {
            //    GetCurrentStock();
            //}
        },

        /**
         * 当选中的是合约机时
         */
        onHeYue: function() {
            this.cfg.addToCartBtn.hide()
            if (this.noBuyType) {
                this.$btn.show()
            } else {
                // 添加二维码
                this.addQrcode();
            }

            /* --- 合约机屏蔽逻辑 --- */
            if (this.cfg.isKO) {
                this.cfg.koBtn.hide()
            }

            // 2. promiseIcon屏蔽
            this.$promiseIcon.hide()

            // 3. 屏蔽抢购、轻松购商品
            this.$qiang.hide()
            this.cfg.notifyBtn.show()

            this.$yysPlanTips.hide()
            this.$yysArrow.hide();
        },

        /**
         * 当选中的是祼机时(非合约)
         */
        onNonHeYue: function() {
            this.cfg.addToCartBtn.show()
            this.$btn.hide()

            if (this.cfg.isKO) {
                this.cfg.koBtn.show()
            }

            this.cfg.notifyBtn.show(this.cfg)
            // 2. promiseIcon屏蔽
            this.$promiseIcon.show()

            // 3. 屏蔽抢购、轻松购商品
            //pageConfig.M_ysBtn.show();
            //pageConfig.M_btfq.show();

            // 纯套餐和商品状态无关
            this.$yysPlanTips.show()
            this.$yysArrow.show()

            // 移除二维码
            this.removeQrcode();
        },

        /**
         * 格式化输出购买、iframe链接
         */
        formatBtypeUrl: function(url, auth) {
            //console.log(url);
            var result = url
                .replace(/{sku}/g, this.sku)
                .replace(/{btype}/g, this.bType)
                .replace(/{pid}/g, this.pid)
                .replace(/{cid}/g, this.cid)
                .replace(/{productId}/g, this.mainSkuId)
                .replace(/{sp}/g, this.sp)
                .replace(/{venderId}/g, pageConfig.product.venderId)

            if (auth) {
                result = result.replace(/{auth}/g, auth)
            }
            return result
        },

        /**
         * 选择套餐与资费弹出层
         */
        showSuitIframe: function(url) {
            var aUrl = this.formatBtypeUrl(url)
            var name = this.getSuitName(this.dtype)

            seajs.use('JDF_UI/dialog/1.0.0/dialog', function() {
                $('body').dialog({
                    type: 'iframe',
                    autoIframe: false,
                    width: 820,
                    height: 600,
                    title: name,
                    source: aUrl
                })
            })
        },

        setBuyBtnText: function(type) {
            if (!this.$currHeYue) {
                return false
            }
            var type = this.$currHeYue.attr('data-type')
            var text = '不换号办套餐'
            if (type === '0') {
                text = '选择号码和套餐'
            }
            if (type === '2') {
                text = '立即购买'
            }
            this.$btn
                .html(text)
                .attr(
                    'clstag',
                    'shangpin|keycount|product|' +
                        text +
                        '_' +
                        pageConfig.product.pType
                )
        },

        disabled: function() {
            this.$btn.addClass(this.disableClass).attr('href', '#none')
        },
        enabled: function(href) {
            var cfg = this.cfg

            if (cfg.isClosePCShow || !cfg.havestock) {
                return false
            }

            this.$btn.removeClass(this.disableClass)
            if (href) {
                this.$btn.attr('href', href)
            }
        },

        /**
         * 设置合约机购买按钮
         */
        setBuyBtn: function(href) {
            var url = '//eve.jd.com/redirect.action?'

            if (
                pageConfig.product.havestock === false ||
                pageConfig.product.isClosePCShow
            ) {
                this.disabledBuy = true
            }

            this.setBuyBtnText()

            // 如果商品已经无货或者是手机专享商品给合约机添加disable样式
            if (this.disabledBuy) {
                this.disabled()
            } else {
                var result =
                    href ||
                    url +
                        $.param({
                            wid: this.sku,
                            btype: this.bType,
                            pid: this.pid,
                            cid: this.cid,
                            r: Math.random()
                        })

                this.enabled(result)
            }
        },

        /**
         * 判断获取三级分类对应 默认**高亮**的的购买方式id
         * 1. 从目标分类列表页进入详情页自动选择相关购买方式
         */
        getUrlFeeType: function() {
            var loc = location.href.toLowerCase()

            var matchedFT = null
            var feeType = null

            // URL 传参数方式添加默认「购买方式」选项
            if (loc.indexOf('feetype') > -1) {
                matchedFT = loc.match(/feetype=\w+/g)

                if (matchedFT !== null) {
                    feeType = matchedFT[0].replace('feetype=', '')
                }
            }

            return feeType
        },

        /**
         * 获取合约套装专用促销信息
         * @param String 合约套装sku串, 如：1301288,1301289
         */
        getPhoneProm: function(skus) {
            var _this = this
            var url =
                '//cd.jd.com/contractsuit/{sku}_{skus}_{pid},0,0_1_1_CellPhone.setPhoneProm_html'
            var res = url
                .replace('{sku}', this.sku)
                .replace('{skus}', skus)
                .replace('{pid}', this.pid)

            $.ajax({
                url: res,
                dataType: 'jsonp',
                success: function(r) {
                    _this.setPhoneProm(r)
                }
            })
        },

        /**
         * 设置合约套装专用促销信息
         */
        setPhoneProm: function(data) {
            var promotionsItems = []
            var giftsItems = []

            var gift_TPL =
                '\
            <div class="prom-gifts clearfix">\
                <span class="prom-gift-label"><em>赠品</em></span>\
                <div class="prom-gift-list">\
                    {for item in gs}\
                    {if item.t==2}\
                    <div class="prom-gift-item">\
                        <a target="_blank" href="//item.jd.com/${item.id}.html" title="${item.name}">\
                            {if item.img}\
                            <img src="${pageConfig.FN_GetImageDomain(item.id)}n5/${item.img}" width="25" height="25" class="gift-img">\
                            {else}\
                            <img src="//misc.360buyimg.com/product/skin/2012/i/gift.png" width="25" height="25" class="gift-img">\
                            {/if}\
                        </a>\
                        <em class="gift-number">× ${item.num}</em>\
                    </div>\
                    {/if}\
                    {/for}\
                </div>\
            </div>'

            // 套装价格
            if (data.csl && data.csl.length > 0) {
                for (var k = 0; k < data.csl.length; k++) {
                    if (data.csl[k].skuId === data.sid) {
                        $('#summary-price strong').html(
                            '￥' + data.csl[k].p.toFixed(2)
                        )
                        break
                    }
                }
            }

            // 直降
            if (data.d) {
                promotionsItems.push(
                    '<em class="hl_red_bg">直降</em><em class="hl_red">已优惠￥' +
                        data.d +
                        '元</em>'
                )
            }

            // 赠京豆
            if (data.s) {
                promotionsItems.push(
                    '<em class="hl_red_bg">赠京豆</em><em class="hl_red">赠' +
                        data.s +
                        '京豆</em>'
                )
            }

            // 赠品信息
            if (data.gs && data.gs.length > 0) {
                giftsItems.push(gift_TPL.process(data))
            }

            // 送券信息
            if (data.cl && data.cl.length > 0) {
                var strQuan = '', quanPerfix = '', quanPerfix = '', quan = ''

                strQuan += '<em class="hl_red_bg">赠券</em>'

                for (var i = 0; i < data.cl.length; i++) {
                    if (data.cl[i].k) {
                        quanPerfix = '限品类'
                    }
                    if (data.cl[i].t === 1) {
                        quan = '京'
                    }
                    if (data.cl[i].t === 2) {
                        quan = '东'
                    }

                    perfix += data.cl[i].q + '元' + quan + quanPerfix + '券 '
                }
                strQuan += '<em class="hl_red">赠送' + perfix + '</em>'

                promotionsItems.push(strQuan)
            }

            if (promotionsItems.length) {
                this.$promPhone.html(
                    '<div class="J-prom-phone">' +
                        promotionsItems.join('<br/>') +
                        '</div>'
                )
            } else {
                this.$promPhone.html('')
            }

            if (giftsItems.length) {
                this.$promGift.html(
                    '<div class="J-prom-phone-gift">' +
                        giftsItems.join('<br/>') +
                        '</div>'
                )
            } else {
                this.$promGift.html('')
            }

            // 原来手机的促销条数，设置折叠限制
            this.originPromCount =
                ($('#prom-bvalue').length > 0 ? 1 : 0) +
                ($('#prom-quan em').length > 0 ? 1 : 0)
            //Promotions.init(false, true);
            //Promotions.promCount = this.originPromCount + giftsItems.length + promotionsItems.length;
            //Promotions.setFoldLayer();
        },

        /**
         * 合约机加价购子逻辑
         * @param Object 加价购相关字段数据
         */
        setJiaJiaGou: function(data) {
            var resLink =
                '//eve.jd.com/redirect.action?' +
                $.param({
                    wid: this.sku,
                    btype: data.info.ft,
                    pid: this.pid,
                    cid: this.cid,
                    r: Math.random()
                })

            var areaText = $('#store-selector .text div').text()

            if (areaText.indexOf('市') > 0) {
                data.stockArea = areaText.substr(0, areaText.indexOf('市') + 1)
            } else {
                data.stockArea = areaText.substr(0, 2)
            }

            data.resLink = resLink

            var html = this.getTPL('jjg-prom')
            var htmlJJGIframe = this.getTPL('jjg-iframe')

            this.$jjg.html(html.process(data))

            this.$jjg
                .find('.J-jjg-btn')
                .unbind('click')
                .bind('click', function() {
                    seajs.use('JDF_UI/dialog/1.0.0/dialog', function() {
                        $('body').dialog({
                            width: 550,
                            height: 320,
                            title: '加价购',
                            source: htmlJJGIframe.process(data),
                            onReady: function() {
                                var el = $('#shf-feetype a')
                                if (!pageConfig.product.havestock) {
                                    el
                                        .removeClass('css3-btn')
                                        .addClass('css3-btn-gray')
                                        .attr('href', '#none')
                                        .removeAttr('target')
                                        .after(
                                            '<strong>\u3000所选地区该商品暂时无货，非常抱歉！</strong>'
                                        )
                                        .removeAttr('clstag')
                                } else {
                                    el.attr(
                                        'clstag',
                                        'eve|keycount|treaty|JJGRedirect'
                                    )
                                }
                            }
                        })
                    })
                })

            Promotions.init(false, true)
            Promotions.$el.show()
            Promotions.promCount++
            Promotions.setFoldLayer()
        },

        /**
         * 合约机套餐iframe
         */
        setPlanIframe: function(bType) {
            var _this = this
            var cName = 'z-have-phone-service'

            this.$yysPlanTips = this.$el.find('#yys-plan-tips')
            this.$yysArrow = $('#J-yys-arrow')

            if (bType === '100') {
                _this.$yysPlanTips.show()
                _this.$yysArrow.show()
                _this.$el.addClass(cName)
            } else {
                _this.$yysPlanTips.hide()
                _this.$yysArrow.hide()
                _this.$el.removeClass(cName)
            }

            _this.$yysPlanTips
                .undelegate()
                .delegate('.J-popup-iframe', 'click', function() {
                    var defUrl = '//dj.eve.jd.com/queryCtcPackageInfo.action?'
                    var dataUrl = $(this).attr('data-url')
                    var param = $.param({
                        skuId: _this.sku,
                        provinceId: _this.pid,
                        cityId: _this.cid,
                        r: Math.random()
                    })
                    var resultUrl = ''

                    if (dataUrl) {
                        if (dataUrl.indexOf('?') > -1) {
                            resultUrl = dataUrl + param
                        } else {
                            resultUrl = dataUrl + '?' + param
                        }
                    } else {
                        resultUrl = defUrl + param
                    }

                    seajs.use('JDF_UI/dialog/1.0.0/dialog', function() {
                        pageConfig.bTypeIframe = $('body').dialog({
                            type: 'iframe',
                            width: 690,
                            height: 610,
                            title: '套餐变更',
                            autoIframe: false,
                            iframeTimestamp: false,
                            source: resultUrl,
                            onReady: function() {
                                var _w = $(this.el).width()
                                $(this.el).addClass('popup-phone-service')
                                $(this.content).width(_w)
                            }
                        })
                    })
                })
        },

        /**
         * 清除手机类促销信息
         */
        clearPhoneProm: function() {
            this.$promPhone.html('')
            this.$promGift.html('')
        },

        /**
         * 清除合约机渲染的html相关内容
         * 1. 清除dom结构
         * 2. 清除相关促销文案
         */
        destory: function() {
            this.$el.html('').hide()
            this.$hy.html('').hide()
            this.$phoneAd.html('').hide()
            this.$jjg.html('')
            this.$suit.hide().removeClass('item-hl-bg')
            //pageConfig.M_hyBtn.hide();
            //pageConfig.M_addToCart.show();
        },

        addQrcode: function () {
            var img = '<img src="//qrimg.jd.com/'+
                encodeURIComponent('https://wqs.jd.com/pingou/item.shtml?sku=' +
                this.sku +'&ptag=17009.11.1') +'-150-0-4-0.png" alt="二维码加载失败，请尝试刷新页面"/>';
            var __html = "<div id='J_ContractPhoneQrcodeLink'>" + img + "</div>"
            var $qrcodeElem = $("#J_ContractPhoneQrcodeLink");
            
            if ($qrcodeElem.length === 0) {
                $("#choose-btns").find(".J_choose_btn").append(__html);
            }

        },
        removeQrcode: function () {
            $("#J_ContractPhoneQrcodeLink").remove();
        }
    }

    function init(cfg) {

        // 合约机初始化
        var sku = cfg.skuid
        var areaIds = tools.getAreaId().areaIds
        var pid = areaIds[0]
        var cid = areaIds[1]

        if (!cfg.isFeeType) {
            return false
        }

        // 合约弹出层回调 parent.CellPhone.setSuitContent 方法
        window.CellPhone = CellPhone
        CellPhone.init(cfg, pid, cid, cfg.mainSkuId, function(data) {})

        // function wrapCellPhoneInitMethod(data) {
        //     var areaIds = tools.getAreaId().areaIds;
        //     var pid = areaIds[0];
        //     var cid = areaIds[1];

        //     var isPlus = (data && data.stock && data.stock.stock && data.stock.stock.isPlus) || false;
        //     Promotions.isPlus = isPlus;
        //     Promotions.plusTags = data && data.stock && data.stock.stock && data.stock.stock.jdPrice && data.stock.stock.jdPrice.ext;
        //     try {
        //         Promotions.isRealPlus = ("201" == data.stock.stock.PlusFlagInfo);
        //     } catch (err) {
        //         Promotions.isRealPlus = false;
        //     }
            
        //     Promotions.setJdPrice(data.stock.stock);
        //     CellPhone.init(cfg, pid, cid, cfg.mainSkuId, function(data) {});
        // }
        // Event.addListener('onAreaChange', wrapCellPhoneInitMethod);
    }

    if (__5G.isHyj) {
        var init = __5G.init;
    }

    module.exports.__id = 'buytype'
    module.exports.init = init
    module.exports.CellPhone = CellPhone
})
