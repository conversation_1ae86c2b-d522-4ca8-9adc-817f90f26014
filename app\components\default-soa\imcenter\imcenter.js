define('MOD_ROOT/imcenter/imcenter', function(require, exports, module) {

    var Event = require('MOD_ROOT/common/tools/event').Event;

    var template = '\
    {for item in body}\
        <li><a class="link-trigger" href="#none" data-id="${item.id}" data-url="${item.url}" title="${item.name}"><i class="jd-dd ${item.statusClass}"></i><em>${item.name}</em></a></li>\
    {/for}';

    var IMCenter = function (opts) {
        this.shopCserviceJson = opts.shopCserviceJson || [];
        this.groupIdArr = [];
        this.$el = opts.$el || $('<div></div>');
        this.trigger = opts.trigger || '.link-trigger';
        this.$customerServiceList = this.$el.find(".customer-service-list");
        this.skuid = opts.skuid;
        this.commentNum = opts.commentNum;
        this.jprice = opts.jprice;
        this.venderId = opts.venderId;
        this.src = opts.src;
        this.name = opts.name;
        this.init();
    }

    IMCenter.prototype = {
        init: function () {
            this.bindEvent();
            this.get();
        },
        bindEvent: function () {
            var _this = this;

            this.$el.delegate(this.trigger, 'click', function () {
                var url = $(this).attr("data-url");
                _this.open(url);
            });
        },
        get: function () {
            var _this = this;

            // shopCserviceJson:[{"serviceIds":"59271,59272","groupId":"13884"}],
            for (var i = 0; i < _this.shopCserviceJson.length; i++) {
                if (_this.shopCserviceJson[i].groupId) {
                    _this.groupIdArr.push(_this.shopCserviceJson[i].groupId);
                }
            }

            var groupIdStr = _this.groupIdArr.join(",");
            $.ajax({
                url: '//chat.jd.com/venderApi/queryGroupByIdList.action',
                data: {
                    groupIdList: groupIdStr,
                    responseCharset: "gbk"
                },
                scriptCharset: 'gbk',
                dataType: 'jsonp',
                success: function (r) {
                    if (r && r.code && (r.code == 1 || r.code == 2 || r.code == 3 || r.code == 9)) {
                        _this.set(r);
                    }
                }
            });
        },
        set: function (r) {
            var result = '';
            if (r.body && r.body.length > 0) {
                try {
                    this.setStatusClass(r);
                    var result = template.process(r);
                    this.$customerServiceList.html(result);
                } catch (err) {
                    var errMsg = result.match(/\[ERROR.+\]/);
                    if (errMsg && errMsg.length) {
                        console.error('Template Render Error @ [imCenter.js]. >>>>> \n   %s', errMsg[0]);
                    }
                }
            }
        },
        setStatusClass: function (r) {
            for (var i in r.body) {
                if (r.body[i].hasOnlineWaiter) {
                    r.body[i].statusClass = "";
                } else {
                    r.body[i].statusClass = "jd-dd-offline";
                }
            }
        },
        open: function (url) {
            var param = {
                pid: this.skuid,
                advertiseWord: encodeURIComponent($('#p-ad').text()),
                commentNum: this.commentNum,
                evaluationRate: 'x',
                imgUrl: this.src,
                wname: encodeURIComponent(this.name),
                jprice: this.jprice,
                stock: encodeURIComponent($('#store-selector .text').text() + '(' + $("#store-prompt strong").text()) + ')'
            };

            if (this.venderId) {
                param.venderId = this.venderId;
            }

            var connect = url.indexOf("?") != -1 ? "&" : "?";
            open(url + connect + $.param(param), this.skuid, "status=no,toolbar=no,menubar=no,location=no,titlebar=no,resizable=yes,width=1018px,height=590");
        }
    }

    function init(cfg) {
        Event.addListener('onCommentMeta', onCommentMeta);
        Event.addListener('onPriceReady', onPriceReady);

        var priceReady = false, commentReady = false;

        function onPriceReady(r) {
            priceReady = true;
            if (priceReady && commentReady) {
                initImCenter(cfg);
            }
        }

        function onCommentMeta(r) {
            commentReady = true;
            if (priceReady && commentReady) {
                initImCenter(cfg);
            }
        }

        function initImCenter(cfg) {
            if(!cfg.shopCserviceJson || !cfg.shopCserviceJson.length) {
                return false;
            }
            var imCenter = new IMCenter({
                $el: $("#imcenter"),
                shopCserviceJson: cfg.shopCserviceJson,
                skuid: cfg.skuid,
                commentNum: cfg.commentMeta.CommentCount,
                jprice: cfg.jp,
                venderId: cfg.venderId,
                src: cfg.src,
                name: cfg.name
            });
        }
    }

    module.exports.__id = 'imcenter';
    module.exports.init = init;
});
