@charset "uft-8";
@import "ui-lib.scss";

.tab li{float:left;text-align:center;}

/*p-fitting*/
#fitting-suit { overflow:visible; }
#fitting-suit .tab { float:left; }
#fitting-suit .detail-list{padding:8px;border:solid #DEDFDE;border-width:0 1px 1px;overflow:hidden;zoom:1;}
#fitting-suit .detail-list li{float:left;width:33%; overflow:hidden; text-overflow:ellipsis; white-space: nowrap; padding:2px 0; }
#fitting-suit .detail-correction{padding:8px 0 8px 0px;zoom:1;}
#fitting-suit .detail-correction b{ display:inline-block;width:18px;height:15px;background-position:-260px -270px; margin-right:5px; vertical-align: middle; *zoom:1 }
/*#promises{padding:10px;overflow:hidden;zoom:1;border-top:1px dotted #DEDEDE; clear:both;}
#promises a { color:#005AA0; }
#state{padding:10px;overflow:hidden;zoom:1;border-top:1px dotted #DEDEDE;}
#state strong{color:#e4393c;}*/

#fitting-suit .p-tab {
    position:relative;
}
#fitting-suit .p-tab s {
    display:inline-block;
    width:9px;
    height:6px;
    overflow:hidden;
    margin-left:5px;
    background-position: -26px -324px;
    *zoom:1;
    vertical-align:middle;
}

#fitting-suit li.hover a s {
    background-position:-40px -324px
}
#fitting-suit .hover .sub-item {
    display:block;
    position:absolute;
    z-index:1;
    top:32px;
    left:0;
    border:1px solid #ddd;
    border-top:none;
    width:116px;
    background-color:#fff;

    -webkit-box-shadow: 0px 5px 5px 0px rgba(0, 0, 0, 0.12);
    -moz-box-shadow:    0px 5px 5px 0px rgba(0, 0, 0, 0.12);
    box-shadow:         0px 5px 5px 0px rgba(0, 0, 0, 0.12);
}
#fitting-suit .sub-item li {
    float:none;
}
#fitting-suit .sub-item li a {
    border:none;
    color:#666;
    width:90px; text-overflow:ellipsis; overflow:hidden; white-space:nowrap;
    text-align:left;

}
#fitting-suit .sub-item li a:hover,
#fitting-suit .sub-item li.hl_red a{
    color:#e4393c;
}
#fitting-suit .mt .tab { margin-left:-1px; overflow:visible; *display:inline; }
#fitting-suit .mt .tab li a{
    font-size: 14px;
    font-weight: normal;
    height: 38px;
    line-height: 38px;
    display: block;
    padding: 0 25px;
}
#fitting-suit .tab .curr {
    _position: relative;
    color:#fff;
    background-color:#e4393c;
}

#fitting-suit .tab .curr a { height:38px; line-height:38px; padding:0 12px; border-left:1px solid #ddd; border-right:1px solid #ddd; color:#fff; }

.left .mt,
#fitting-suit .mt { background-color:#f7f7f7; }
#fitting-suit .mt { overflow:visible; background:none; border-left:1px solid #ddd; border-right:1px solid #ddd; }
.tab li a{font:bold 15px/30px 'microsoft yahei';}

/*recommend*/
.stabcon .infos .btns a:hover,#i-comment .btns a { text-decoration: none; }
.i-item .btns a:hover,.i-item .btns a:visited { color:#333; }
.master .p-price { display:none; }
#fitting-suit { overflow:visible; }
#fitting-suit .p-name{height:3em;}
#fitting-suit .master .p-name{width:100px; padding:0 13px;}
#fitting-suit .stab{
    //删除浮动，负责pop商品的tab无法切换
    //float:left;
    overflow:hidden;
    padding-bottom:15px;
}
#fitting-suit .stab li{padding:0 10px;height:16px;cursor:pointer;border-left:1px solid #D4D1C8; margin-left:-1px;line-height:16px;text-align:center;color:#005aa0; white-space:nowrap; }
#fitting-suit .stab .fore1{border:none;}
#fitting-suit .master{float:left;width:150px;padding:0 0 0 10px;text-align:center; overflow:hidden; }
#fitting-suit .master s, #fitting-suit .suits li s{ float:right; display:inline; width:24px; height:22px; background-position:0 -260px;margin-top:40px; margin-right:3px; }
#fitting-suit .suits .p-img,
#fitting-suit .suits .p-name,
#fitting-suit .suits .choose,
#fitting-suit .suits .p-more { *float:left;}
#fitting-suit .suits .p-more { *white-space:nowrap;clear:both; }
#fitting-suit .suits .choose { *width:100px; }
/*#fitting-suit .fitting-content .suits div{text-align: center;}
#fitting-suit .fitting-content .choose .p-price{height: 19px;}*/
#fitting-suit .suits li.last-item s,
#fitting-suit .suits li.last-item s,
#fitting-suit .suits li.last_item s,
#fitting-suit .suits li.last_item s{ display:none; }
#fitting-suit .suits li .p-name {width:140px;}
.last_item .p-more { display:block; }

#newFittign-tab .iloading { margin: 60px auto; }
#newFittign-tab .choose { *width:145px; }
#fitting-suit #newFittign-tab .master s, #fitting-suit #newFittign-tab .suits li s { margin-top:60px; }
#fitting-suit .infos{float:right;width:190px;line-height:20px;}
#fitting-suit .infos s { float:left; width:24px; height:22px; background-position:-30px -260px;margin-top:40px;  }
#fitting-suit .infos .selected,
#fitting-suit .infos .p-price,
#fitting-suit .infos .p-suit,
#fitting-suit .infos .p-saving,
#fitting-suit .infos .btns,
#fitting-suit .infos .p-name,
#more-fitting-link { margin-left:35px; color: #999; }
#fitting-suit .infos .p-suit strong { color:#E4393C }
.infos .p-saving { color:#999; }
#more-fitting-link b { position:absolute; top:-16px; left:10px; display:block; width:26px; height:15px; background-position:-247px -352px; }
#more-fitting-link { font-weight:bold; position:relative; top:-20px; }
#more-fitting-link span { font:bold 12px/1em simsun; padding-left:3px; position:relative; top:1px; }
.tab-cat { width:590px; }
#fitting-suit .btn-buy{display:block;width:77px;height:25px;margin-top:10px;background-position:-166px -112px;text-align:center;line-height:25px;color:#fff;font-weight:bold;}
#tab-hot .infos .p-name a,#tab-hot .infos .p-name a:visited { font-weight:bold; color:#005AA0; }
#fitting-suit #tab-hot .suits { position:relative; }

#tab-reco .suits { overflow-x:scroll; padding-bottom:10px; }
#tab-reco #newFittign-tab .suits { height:205px; overflow:hidden; }
#tab-reco .suits ul { width:1958px;/*  height:170px; */ }
#tab-hot .suits { overflow-x:hidden; }
#tab-hot { overflow:visible; }
#tab-hot .suits ul { width:1858px; }

#tab-services { display:none; }
#tab-services .i-mc { padding:0 10px; }
#tab-services table {  margin-left:20px; }
#tab-services table td { padding:2px 5px; font-size:12px; }
#tab-services table td .btn-buy { width:70px; height:21px; line-height: 21px; margin:0; background-position:-69px -84px; }
#tab-services table td .btn-buy:hover { text-decoration:none; }


/* suits recommend */

#fitting-suit #stabcon_suits .master .p-name { width:140px; padding:0 10px; }
#stabcon_suits .pop-wrap { position:relative; float:left; }
#stabcon_suits #pop-info { position:absolute; z-index:5; left:0; top:208px; }
#stabcon_suits .master { width:200px; }
#stabcon_suits .suits { overflow:visible; }
#stabcon_suits .lh { overflow:hidden; }
#stabcon_suits .infos s,#stabcon_suits .infos { margin-top:50px; }

/*scroll*/
#fitting-suit #stabcon_suits li { position:relative; }
.POP-3 #fitting-suit #stabcon_suits li .p-img{width: 118px;overflow: hidden;}
.POP-3 #fitting-suit #stabcon_suits li .p-img img{margin-left: -20px;}
#fitting-suit #stabcon_suits li .p-name,#fitting-suit #stabcon_suits li .choose { width:156px; }
#stabcon_suits .master s, #stabcon_suits .suits li s { margin-top:100px;}
#stabcon_suits .p-scroll-btn {background-image:url(../../css/i/scroll-btns.png);background-repeat:no-repeat;}
#stabcon_suits .selected { border-color:#e4393c; }
#stabcon_suits .p-scroll{ width:155px; _width:155px; height:29px; background:#fff; padding-bottom:10px;overflow:hidden;*zoom:1; padding:5px 0; border:1px solid #fff; border-bottom:0; }
#stabcon_suits .actived { border-color:#c4c4c4; border-bottom:0; -webkit-box-shadow:0px 2px 4px #ccc;box-shadow:0px 2px 4px #ccc;}
#fitting-suit #stabcon_suits .p-scroll li { width:30px; height:30px; padding:0; }
#stabcon_suits .p-scroll-btn-w {float:left; width:16px; height:29px; }
#stabcon_suits .p-scroll-btn{float:left; display:none; width:16px;height:29px;text-indent:-9999px;}
#stabcon_suits .p-scroll-prev{margin-right:2px;background-position:0 0;}
#stabcon_suits .p-scroll-next{background-position:-18px 0;}
#stabcon_suits * html .psearch .disableIE6{background-position:0 -31px;}
#stabcon_suits .p-scroll-prev.disabled{background-position:0 -31px;}
#stabcon_suits .p-scroll-next.disabled{background-position:-18px -31px;}
#stabcon_suits .p-scroll-wrap{float:left;width:186px;height:29px;overflow:hidden;}
#stabcon_suits .p-scroll-wrap li,.p-scroll-wrap li a{float:left;}
#stabcon_suits .p-scroll-wrap li a{padding:1px;border:1px solid #ddd;}
#stabcon_suits .p-scroll-wrap li a img{width:25px;height:25px;}
#stabcon_suits .p-scroll-wrap li a.curr{border:1px solid #e4393c;}
#stabcon_suits .suits li { padding-left:5px; width:195px; }

.root61 #stabcon_suits .suits { width:570px; }
.root61 #stabcon_suits .suits li { padding-left:5px; width:195px; }


/* pop recommend */
#fitting-suit #stabcon_pop .master .p-name { width:140px; padding:0 10px; }
#stabcon_pop .pop-wrap { position:relative; float:left; }
#stabcon_pop #pop-info { position:absolute; z-index:5; left:0; top:208px; }
#stabcon_pop .master { width:200px; }
#stabcon_pop .suits { overflow:visible; }
#stabcon_pop .lh { overflow:hidden; }
.match-pop-tips { padding:5px 0 0 0; color:#e4393c; }
#stabcon_pop .infos s,#stabcon_pop .infos { margin-top:50px; }

#pop-box, #pop-box-suit { visibility:hidden; position:absolute; z-index:5; left:0px; top:169px; width:175px; overflow:hidden;  background:#fff; border: 1px solid #C4C4C4; -moz-box-shadow: 0 0 5px #ddd; -webkit-box-shadow: 0 0 5px #DDD; box-shadow: 0 0 5px #DDD;}
#p-scroll, #p-scroll-suit { padding:10px 0  0 10px; }
.p-scroll { padding:0 0 10px 0; }
#p-tips, #p-tips-suit { color:#e4393c; }
#p-size-btn,
#p-size,
#p-tips,
#p-size-btn-suit,
#p-size-suit,
#p-tips-suit { padding:0 10px; }

#p-size-btn, #p-size-btn-suit { padding-bottom:10px; }
#p-tips p,
#p-tips-suit p { padding:5px 0;}
#p-size-btn a:hover,
#p-size-btn-suit a:hover { text-decoration:none;}
#p-size-btn a,
#p-size-btn-suit a {display:inline-block; *zoom:1; width:50px; margin-right:5px; height:21px; line-height:21px; background:url(../../css/i/scroll-btns.png) -36px 0 no-repeat; text-align:center; }
#p-size a,
#p-size-suit a { display:inline-block; *zoom:1; padding:2px 5px; border:1px solid #ccc; margin:0 5px 10px 0; }
.p-selected { background:#fffdee; width:138px; border:1px solid #edd28b; margin:7px 0; padding:4px 10px; }
.p-selected a { color:#005ea7; white-space:nowrap; }
.p-selected a,.p-selected a:hover,.p-selected a:visited { color:#005ea7; }


/* stabcon_suits */
#stabcon_suits { position:relative; }
#tab-suits { overflow:visible; }
.stabcon_suits .choose { height:16px; }
.p-choose .hl_blue { cursor:pointer; }


/*scroll*/
#fitting-suit #stabcon_pop li { position:relative; }
#fitting-suit #stabcon_pop li .p-name,#fitting-suit #stabcon_pop li .choose { width:156px; }
#stabcon_pop .master s, #stabcon_pop .suits li s { margin-top:100px;}
#stabcon_pop .p-size { border:1px solid #c4c4c4; padding:0 10px 10px 10px; width:155px; left:5px; background:#fff; border-top:0; -webkit-box-shadow:0px 2px 4px #ccc;box-shadow:0px 2px 4px #ccc; }
#stabcon_pop .p-scroll-btn {background-image:url(../../css/i/scroll-btns.png);background-repeat:no-repeat;}
#stabcon_pop .selected { border-color:#e4393c; }
.p-size-list,.p-size-btn { padding:5px 0 0 0; }
.p-size-btn a:hover { text-decoration:none;}
.p-size-btn a {display:inline-block; *zoom:1; width:50px; margin-right:5px; height:21px; line-height:21px; background:url(../../css/i/scroll-btns.png) -36px 0 no-repeat; text-align:center; }
.p-size-list a { display:inline-block; *zoom:1; padding:2px 5px; border:1px solid #ccc; margin:0 5px 5px 0; }
#stabcon_pop .p-scroll{ width:155px; _width:155px; height:29px; background:#fff; padding-bottom:10px;overflow:hidden;*zoom:1; padding:5px 0; border:1px solid #fff; border-bottom:0; }
#stabcon_pop .actived { border-color:#c4c4c4; border-bottom:0; -webkit-box-shadow:0px 2px 4px #ccc;box-shadow:0px 2px 4px #ccc;}
#fitting-suit #stabcon_pop .p-scroll li { width:30px; height:30px; padding:0; }
#stabcon_pop .p-scroll-btn-w {float:left; width:16px; height:29px; }
#stabcon_pop .p-scroll-btn{float:left;display:none;width:16px;height:29px;text-indent:-9999px;}
#stabcon_pop .p-scroll-prev{margin-right:2px;background-position:0 0;}
#stabcon_pop .p-scroll-next{background-position:-18px 0;}
#stabcon_pop * html .psearch .disableIE6{background-position:0 -31px;}
#stabcon_pop .p-scroll-prev.disabled{background-position:0 -31px;}
#stabcon_pop .p-scroll-next.disabled{background-position:-18px -31px;}
#stabcon_pop .p-scroll-wrap{float:left;width:186px;height:29px;overflow:hidden;}
#stabcon_pop .p-scroll-wrap li,.p-scroll-wrap li a{float:left;}
#stabcon_pop .p-scroll-wrap li a{padding:1px;border:1px solid #ddd;}
#stabcon_pop .p-scroll-wrap li a img{width:25px;height:25px;}
#stabcon_pop .p-scroll-wrap li a.curr{border:1px solid #e4393c;}
#stabcon_pop .suits li { padding-left:5px; width:195px; }

.root61 #stabcon_pop .suits { width:570px; }
.root61 #stabcon_pop .suits li { padding-left:5px; width:195px; }
/*fitting-suit*/
#fitting-suit .mt {background-position:0 -287px; }
#fitting-suit .mc{
    padding:10px 0; 
    //解决内衣套装尺码选择被overflow截断
    overflow: visible;
}
#fitting-suit .suits{
    float: left;
    //调整ie7下只显示横向滚动条
    //overflow: auto;
    overflow-x: auto;
    overflow-y: hidden;
    width:580px;
}
#fitting-suit .p-name{height:3em;}
#fitting-suit .master .p-name{width:100px; padding:0 13px;}
#fitting-suit .stab li{padding:0 15px;height:16px;cursor:pointer;border-left:1px solid #D4D1C8;line-height:16px;text-align:center;color:#005aa0;margin-bottom:10px;}
#fitting-suit .stab .fore1{border:none;}
#fitting-suit .stab .scurr{color:#e4393c;}
/*#fitting-suit .fitting-content .stabcon{padding:0 25px;}
#fitting-suit .fitting-content .suits{float: none;}*/
#fitting-suit .suits li{width:145px; /*padding:0 8px;*/ padding-left:5px;background-position:0 -260px;}

#fitting-suit .infos s { float:left; width:24px; height:22px; background-position:-30px -260px;margin-top:40px;  }
#fitting-suit .infos .selected,#fitting-suit .infos .p-price,#fitting-suit .infos .p-saving,#fitting-suit .infos .btns,#fitting-suit .infos .p-name { margin-left:35px; }
#fitting-suit .infos .p-name { height:3em; overflow:hidden; line-height:1.5em; }
#fitting-suit .btn-buy{display:block;width:77px;height:25px;margin-top:10px;background-position:-166px -112px;text-align:center;line-height:25px;color:#fff;font-weight:bold;}
#fitting-suit .infos .p-name a,#fitting-suit .infos .p-name a:visited { font-weight:bold; color:#005AA0; }

#fitting-suit .suits ul {
    float:left;
    height: 100%;
    *padding-bottom:20px;
    overflow: hidden;
}
#fitting-suit .all-fitting,#fitting-suit .all-suit{float: left;padding-left: 20px;}
#fitting-suit .stabcon{clear: both;}
#fitting-suit .float-nav-wrap{height: 38px;position: relative;background-color: #f7f7f7;border: 1px solid #eee;border-bottom: 1px solid #e4393c;}
.nostock .p-stock {
    display:block;
}
#fitting-suit .btn-gray {
    color:#999;
    padding: 0 5px;
    background: #f7f7f7;
}
.suit-content .suits li {
    .p-img {
        position: relative;
    }
    .p-stock {
        width: 80px;
        height: 25px;
        line-height: 25px;
        position: absolute;
        bottom: 1px;
        left: 1px;
        padding: 0 10px;
        color: #fff;
        filter: progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#B2000000', endColorstr='#B2000000');
        background: rgba(0,0,0,.7);
    }
}
/*.root61 #fitting-suit .suit-content .suits .lh{width: 990px;}*/
/*root61*/
.root61 #fitting-suit .suits{width:788px;}
.root61 .tab-cat { width:790px; }
/*.root61 #fitting-suit .fitting-content .suits li{width:145px;padding:0 15px 0 25px;}*/
.root61 #fitting-suit .suits li{width:197px;padding-left:0;}
/*cir-trigger*/
#fitting-suit .cir-trigger{position: relative;right:12px;float: right;text-align: right;width: 100px;}
#fitting-suit .cir-trigger a{margin-right: 8px;}
.cir-trigger a {background: #ccc;border-radius: 12px;display: inline-block;height: 12px;width: 12px;}
 .cir-trigger a.curr {background: #e43a3d;border: 0 none;}



/* 最佳组合list */
.combine-content li,
.popbook-content li {
    width: 130px;
    margin:10px 33px;
    *display: inline;
}
.root61{
    .combine-content li,
    .popbook-content li {
        margin:10px 35px;
    }
}

/* 3c商品最佳组合添加反馈 */
.combine-content{

    .p-match{
        position: absolute;
        display: none;
        height: 18px;
        width: 18px;
        right: 0;
        top: 0;

        .match-tip{
            position: absolute;
            display: none;
            padding: 8px;
            border: 1px solid #ddd;
            width: 170px;
            left: 20px;
            top: 5px;
            background: #fff;
            @include box-shadow(0px 0px 5px rgba(0,0,0,0.15));
        }

        .not-match{
            position: absolute;
            display: none;
            width: 42px;
            height: 16px;
            line-height: 16px;
            border: 1px solid #ddd;
            background: #fff;
            text-align: center;
        }

    }
    // 行末的lihover时的提示窗显示在左侧
    li.fore4,li.fore5{
        .p-match{
            .match-tip{
                left: -192px;
            }
        }
    }
    /**
     * @hook: .combine-content li .p-match
     * @state: hover
     */
    .p-match-hover{
        .i-trash{
            background-position: -150px 0;
        }
        .match-tip{
            display: block;
        }
    }

    /**
     * @hook: .combine-content li
     * @state: hover
     */
    .li-hover{
        position: relative;
        z-index:3;
        .p-match{
            display: block;
        }
    }

    /**
     * @hook: .combine-content li
     * @state: have feedback
     */
    .z-have-feedback{
        position: relative;
        z-index:2;

        .p-match{
            display: block;
            width: 44px;
        }
        .i-trash,
        .match-tip,
        .p-match-hover .match-tip{
            display: none;
        }
        .not-match{
            display: block;
        }

    }


}
