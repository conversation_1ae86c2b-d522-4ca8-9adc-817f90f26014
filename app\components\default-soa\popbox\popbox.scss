@import '../common/lib';
@import '__sprite.scss';

.pop-score-summary {
    .score-sum {
        float: left;
        width: 78px;
        display: inline;
        margin-top: 15px;
        color: #999;
        .number {
            text-align: center;
            font-size: 24px;
            line-height: 45px;
            color: #E94634;
            display: block;
        }
        .up {
            color: #E94634;
        }
        .eq {
            color: #5191D3;
        }
        .down {
            color: #089c08;
        }
    }
    .score-parts {
        float: left;
        width: 110px;
    }
    .score-part {
        overflow: hidden;
        display: inline-block;
        margin-bottom: 5px;
    }
    .score-desc {
        display: inline-block;
        width: 52px;
        color: #999;
    }
    .score-detail {
        color: #999;
    }
    .customer-service {
        padding: 10px 0;
        border-top: 1px solid #eee;
        .seller-phone {
            float: none;
            margin-bottom: 8px;
        }
        .contacts {
        }
    }

    .score-trend {
        .sprite-down {
            @include sprite-down;
        }
        .sprite-up {
            @include sprite-up;
        }
        .sprite-middle {
            @include sprite-middle;
        }

        i {
            @include inline-block;
        }
    }

    .score-infor {
        padding-top: 10px;
        display: block;
        border-bottom: 1px solid #eee;
    }

    .btns {
        padding: 10px 0;
        font-size: 0;
        text-align: center;

        .btn-def {
            width: 88px;
            height: 34px;
            line-height: 34px;
            padding: 0;
            margin-right: 8px;
            font-size: 12px;
            _padding-top: 10px;
            _height: 24px;
        }

        i {
            @include inline-block;
            vertical-align: -2px;
        }

        .sprite-enter {
            @include sprite-enter;
        }

        .sprite-follow {
            @include sprite-follow;
        }

        .follow-shop {
            margin-right: 0;
        }
    }
}

.popbox {
    h3 {
        float: left;
        max-width: 150px;
        height: 18px;
        overflow: hidden;
    }

    .im-wrap {
        margin-left: 5px;
        float: left;

        .im {
            float: left;
            cursor: pointer;
            .sprite-im {
                width: 16px;
                height: 20px;
                @include inline-block;
                vertical-align: middle;
            }

            &.gys-im {
                .sprite-im {
                    background: url(i/sprite-gys-im.gif) no-repeat 0 0;
                }
            }

            &.pop-im {
                .sprite-im {
                    background: url(i/sprite-pop-im.gif) no-repeat 0 0;
                }
            }

            &.newjd-im {
                .sprite-im {
                    background: url(i/sprite-jd-im.gif) no-repeat 0 0;
                }
            }

            &.jd-im-offline {
                .sprite-im {
                    @include sprite-im-offline;
                }
            }
            &.customer-service {
                .sprite-im {
                    @include sprite-communication;
                    vertical-align: text-top;
                }
            }
        }
    }

    .mc {
        padding-top: 0px;
        background-color: #FFF;
    }

    .popbox-inner {
        &.pro-detail-hd-fixed{
            position: fixed;
            top: 0;
            z-index: 30;
            width: 210px;
            .mt{
                padding-top:8px;
                padding-bottom:8px;
            }
            .mt .arrow {
                @include inline-block;
                @include sprite-arr-open;
                float: right;
                margin-top: 8px;
                margin-right: 3px;
                transition: transform .2s ease;
                -ms-transition: transform .2s ease;
                -moz-transition: transform .2s ease;
                -webkit-transition: transform .2s ease;
                -o-transition: transform .2s ease;
            }

            .mc {
                display: none;
            }

            &:hover {
                .mt .arrow {
                    transform: rotate(180deg);
                    -ms-transform: rotate(180deg);
                    -moz-transform: rotate(180deg);
                    -webkit-transform: rotate(180deg);
                    -o-transform: rotate(180deg);
                }
                .mc {
                    display: block;
                }
            }
        }
    }
}
























