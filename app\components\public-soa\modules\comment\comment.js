define('PUBLIC_ROOT/modules/comment/comment', function(require, exports, module) {
    // var login = require('JDF_UNIT/login/1.0.0/login')
    // var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event
    // var G = require('PUBLIC_ROOT/modules/common/core')
    // var ShopImgSwitch = require('PUBLIC_ROOT/modules/comment/showImgSwitch')
    // var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');

    // require('JDF_UI/pager/1.0.0/pager')
    // require('../common/plugins/jQuery.imgScroll')
    // require('JDF_UNIT/trimPath/1.0.0/trimPath')
    // require('JDF_UI/dialog/1.0.0/dialog')

    // var videoBox = require('PUBLIC_ROOT/modules/videoBox/videoBox')
    // var videoArr = [];
    // var template = {}
    // var isIE8 = (videoBox.getBrowserType() == 'IE8');

    // template.commentRate =
    //     '\
    // <div class="comment-percent">\
    //     <strong class="percent-tit">好评度</strong>\
    //     <div class="percent-con">${productCommentSummary.goodRateShow}<span>%</span>\
    //     </div>\
    // </div>\
    // <div class="percent-info">\
    //     {if !(hotCommentTagStatistics && typeof hotCommentTagStatistics!="undefined" && hotCommentTagStatistics!=null && hotCommentTagStatistics.length>0) && !(vTagStatisticsResult && typeof vTagStatisticsResult!="undefined" && vTagStatisticsResult.length>0)}\
    //         <div class="notag">此商品暂时还没有买家印象哦~</div>\
    //     {/if}\
    //     {if typeof hotCommentTagStatistics!="undefined" && hotCommentTagStatistics!=null && hotCommentTagStatistics.length>0}\
    //     <div class="tag-list {if vTagStatisticsResult.length} empty-rate{/if}" clstag="shangpin|keycount|product|comment_icon">\
    //         {for tag in hotCommentTagStatistics}\
    //             <span class="{if rid==tag.rid}selected{/if} tag-${tag.stand}" \
    //                 data-id="${tag.rid}" \
    //                 data-rid="${tag.rid}">${tag.name}(${tag.count})</span>\
    //         {/for}\
    //     </div>\
    //     {/if}\
    //     {if typeof vTagStatisticsResult!="undefined" && vTagStatisticsResult.length>0}\
    //     <div class="percent-rate">\
    //         {for vTagStatistic in vTagStatisticsResult}\
    //             <div class="rate-item">\
    //                 <strong>${vTagStatistic.name}</strong>\
    //                 <div class="rate-wrap">\
    //                     <div class="inner-rate" style="width:${vTagStatistic.maxRate}%;"></div>\
    //                 </div>\
    //                 <div class="rate-info">\
    //                     ${vTagStatistic.maxName} ${vTagStatistic.maxRate}%\
    //                     <span>${vTagStatistic.otherName}</span>\
    //                 </div>\
    //             </div>\
    //         {/for}\
    //     </div>\
    //     {/if}\
    // </div>'

    // template.commentItem =
    //     '\
    // {for list in comments}\
    // <div class="comment-item" data-guid="${list.guid}" data-top="${list.top}">\
    //     <div class="user-column">\
    //         <div class="user-info">\
    //             {if typeof list.userImageUrl!=="undefined"}\
    //                 <img src="//${list.userImageUrl}" width="25" height="25" alt="${list.nickname}" class="avatar"/>\
    //             {/if}\
    //             ${list|getDisplayName}\
    //         </div>\
    //         <div class="user-level">\
    //             {if typeof list.plusAvailable!="undefined"&&(list.plusAvailable==101||list.plusAvailable==201)}\
    //                 <a class="comment-plus-icon" href="//plus.jd.com/index" target="_blank">${list.plusAvailable == 101 ? "PLUS会员[试用]" : "PLUS会员"}</a>{if list.userProvince}${list.userProvince}{/if}\
    //             {else}\
    //                 <span style="color: rgb(136, 136, 136);">{if list.userExpValue!="undefined" && list.userExpValue!=null && list.userExpValue}京享值${list.userExpValue}{/if}</span>{if list.userProvince}${list.userProvince}{/if}\
    //             {/if}\
    //         </div>\
    //     </div>\
    //     <div class="comment-column J-comment-column">\
    //         <div class="comment-star star${list.score}"></div>\
    //         <p class="comment-con">${list.content.replace(/\\n+/g, "<br />")}</p>\
    //         {if list.mergeOrderStatus>0&&list.images}\
    //         <div class="pic-list J-pic-list">\
    //             {if (!list.isIE8)&&list.videos&&list.videos.length>0}\
    //                 <a class="J-thumb-img current" href="#none" data-ind="-1"><img src="${list.videos[0].mainUrl}" width="48" height="48" alt="${list.nickname}的晒单视频"/><span class="video-icon"></span></a>\
    //             {/if}\
    //             {for image in list.images}\
    //                 {if Number(image_index)<10}\
    //                 <a class="J-thumb-img" href="#none" data-ind="${image_index}"><img src="${image.imgUrl.replace("128x96", "48x48")}" width="48" height="48" alt="${list.nickname}的晒单图片"/></a>\
    //                 {/if}\
    //             {/for}\
    //         </div>\
    //         {/if}\
    //         <div class="J-pic-view-wrap clearfix" data-rotation="0">\
    //         </div>\
    //         {if (!list.isIE8)&&list.videos&&list.videos.length>0}\
    //             <div class="J-video-view-wrap clearfix">\
    //                 <video class="video-js" id="player${list.guid}" src="${list.videos[0].remark}" preload="metadata" poster="${list.videos[0].mainUrl}" muted></video>\
    //             </div>\
    //         {/if}\
    //         {if list.commentTags&&list.commentTags.length}\
    //         <div class="tag-list">\
    //             {for tag in list.commentTags}\
    //                 {if Number(tag_index)<5}\
    //                 <span data-tid="${tag.id}">${tag.name}</span>\
    //                 {/if}\
    //             {/for}\
    //         </div>\
    //         {/if}\
    //         {if list.verticalTags&&list.verticalTags.length}\
    //         <div class="tag-list">\
    //             {for verticalTag in list.verticalTags}\
    //                 {if Number(verticalTag_index)<5}\
    //                 <span data-tid="${verticalTag[0].commentId}">${verticalTag[0].name}：${verticalTag[1].name}</span>\
    //                 {/if}\
    //             {/for}\
    //         </div>\
    //         {/if}\
    //         <div class="comment-message">\
    //             <div class="order-info">\
    //                 {if list.productColor&&list.productColor!=""}<span>${list.productColor}</span>{/if}{if list.productSize&&list.productSize!=""}<span>${list.productSize}</span>{/if}\
    //                 {for product in list.productSales}\
    //                     <span>${product.saleValue}</span>\
    //                 {/for}\
    //                 <span>${list.creationTime|formatCommentTime}</span>\
    //             </div>\
    //             <div class="comment-op">\
    //                 <a class="J-report" data-login="1" href="#none" data-guid="${list.guid}"\
    //                     clstag="shangpin|keycount|product|pingjiaubao">举报</a>\
    //                 <a class="J-nice"  data-login="1" href="#none" data-guid="${list.guid}" title="${list.usefulVoteCount}"><i class="sprite-praise"></i>${list.usefulVoteCount}</a>\
    //                 <a href="//club.jd.com/repay/${productCommentSummary.productId}_${list.guid}_1.html" target="_blank"><i class="sprite-comment"></i>${list.replyCount}</a>\
    //             </div>\
    //         </div>\
    //         {if list.afterUserComment && list.afterUserComment.hAfterUserComment}\
    //             <div class="append-comment J-append-comment" data-id="${list.afterUserComment.id}">\
    //                 <div class="append-time">[购买${list.afterDays|formatDay}追评]</div>\
    //                 <p class="comment-con">${list.afterUserComment.hAfterUserComment.content}</p>\
    //                 {if (list.afterImages && list.afterImages.length) || ((!list.isIE8)&&list.afterVideos&&list.afterVideos.length)}\
    //                 <div class="pic-list J-pic-list">\
    //                     {if list.afterVideos&&list.afterVideos.length>0}\
    //                         <a class="J-thumb-img" href="#none" target="_self" data-ind="-1"><img src="${list.afterVideos[0].mainUrl}" width="48" height="48"><span class="video-icon"></span></a>\
    //                     {/if}\
    //                     {for img in list.afterImages}\
    //                         <a class="J-thumb-img" href="#none" target="_self" data-ind="${img_index}"><img src="${img.imgUrl.replace("128x96", "48x48")}" width="48" height="48"></a>\
    //                     {/for}\
    //                 </div>\
    //                 {/if}\
    //                 <div class="J-pic-view-wrap clearfix" data-rotation="0"></div>\
    //                 {if (!list.isIE8)&&list.afterVideos&&list.afterVideos.length>0}\
    //                     <div class="J-video-view-wrap clearfix hide">\
    //                         <video class="video-js" id="playerafter${list.guid}" src="${list.afterVideos[0].remark}" preload="metadata" poster="${list.afterVideos[0].mainUrl}" muted></video>\
    //                     </div>\
    //                 {/if}\
    //             </div>\
    //         {/if}\
    //         {for reply in list.replies}\
    //         <div class="recomment-con">\
    //             <dl class="recomment">\
    //                 <dt>${reply|getReplyName} 回复：</dt>\
    //                 <dd>${reply.content}</dd>\
    //             </dl>\
    //             <div class="comment-time">\
    //                 ${reply.creationTime|timeToDate}\
    //             </div>\
    //         </div>\
    //         {/for}\
    //     </div>\
    // </div>\
    // {forelse}\
    //     <div class="ac comments-item">「暂无评价」</div>\
    // {/for}\
    // {if productCommentSummary.defaultGoodCount && productCommentSummary.defaultGoodCountStr && productCommentSummary.defaultGoodCount!=0 && productCommentSummary.defaultGoodCountStr!="0" && (score==0 || score==3)}\
    //     <div class="comment-default-good-reputation J-default-reputaion hide">\
    //         <span>还有${productCommentSummary.defaultGoodCountStr}位用户给了默认评价</span>\
    //     </div>\
    // {/if}\
    // <div class="comment-more J-fold-comment hide" clstag="shangpin|keycount|product|pingjiazhedie">\
    //     <span>已忽略对购买帮助不大的评价</span>\
    //     <a href="#none">查看</a>\
    //     {if productCommentSummary.defaultGoodCount && productCommentSummary.defaultGoodCountStr && productCommentSummary.defaultGoodCount!=0 && productCommentSummary.defaultGoodCountStr!="0" && (score==0 || score==3)}\
    //         <em class="comment-up-triangle"></em>\
    //     {/if}\
    // </div>\
    // <div class="com-table-footer">\
    //     <div class="ui-page-wrap clearfix">\
    //         <div class="ui-page"></div>\
    //     </div>\
    // </div>'

    // template.foldCommentItem =
    //     '\
    // {for list in comments}\
    // <div class="comment-item" data-guid="${list.guid}" data-top="${list.top}">\
    //     <div class="user-column">\
    //         <div class="user-info">\
    //             {if typeof list.userImageUrl!=="undefined"}\
    //                 <img src="//${list.userImageUrl}" width="25" height="25" alt="${list.nickname}" class="avatar"/>\
    //             {/if}\
    //             ${list|getDisplayName}\
    //         </div>\
    //         <div class="user-level">\
    //             {if typeof list.plusAvailable!="undefined"&&(list.plusAvailable==101||list.plusAvailable==201)}\
    //                 <a class="comment-plus-icon" href="//plus.jd.com/index" target="_blank">${list.plusAvailable == 101 ? "PLUS会员[试用]" : "PLUS会员"}</a>{if list.userProvince}${list.userProvince}{/if}\
    //             {else}\
    //                 <span style="color: rgb(136, 136, 136);">{if list.userExpValue!="undefined" && list.userExpValue!=null && list.userExpValue}京享值${list.userExpValue}{/if}</span>{if list.userProvince}${list.userProvince}{/if}\
    //             {/if}\
    //         </div>\
    //     </div>\
    //     <div class="comment-column J-comment-column">\
    //         <div class="comment-star star${list.score}"></div>\
    //         <p class="comment-con" title="${list.content}">${list.content.replace(/\\n+/g, "<br />")}</p>\
    //         <div class="comment-message">\
    //             <div class="order-info">\
    //                 {for product in list.productSales}\
    //                     <span>${product.saleValue}</span>\
    //                 {/for}\
    //                 <span>${list.creationTime|formatCommentTime}</span>\
    //             </div>\
    //             <div class="comment-op">\
    //                 {if $.inArray(window.pageConfig.product.cat[2], [1505,14697,14698,14699,12609,12610,14700,14701,1506,13172]) == -1}\
    //                 <a href="//club.jd.com/repay/${productCommentSummary.productId}_${list.guid}_1.html" target="_blank"><i class="sprite-comment"></i>${list.replyCount}</a>\
    //                 {/if}\
    //             </div>\
    //         </div>\
    //         {for reply in list.replies}\
    //         <div class="recomment-con">\
    //             <dl class="recomment">\
    //                 <dt>${reply|getReplyName} 回复：</dt>\
    //                 <dd>${reply.content}</dd>\
    //             </dl>\
    //             <div class="comment-time">\
    //                 ${reply.creationTime|timeToDate}\
    //             </div>\
    //         </div>\
    //         {/for}\
    //     </div>\
    // </div>\
    // {forelse}\
    //     <div class="ac comments-item">「暂无折叠评价」</div>\
    // {/for}\
    // <div class="com-table-footer">\
    //     <div class="ui-page-wrap clearfix">\
    //         <div class="ui-page"></div>\
    //     </div>\
    // </div>'

    // template.picView =
    //     '\
    // <div class="pic-view J-pic-view">\
    //     {if isSupportTransform}\
    //     <div class="pic-op">\
    //         <a class="turn-left J-turn-left" href="#none"><i class="sprite-turn-left"></i>左转</a>\
    //         <a class="turn-left J-turn-right" href="#none"><i class="sprite-turn-right"></i>右转</a>\
    //     </div>\
    //     {/if}\
    //     <img src="${imgSrc}"/>\
    //     <div class="cursor-prev J-sprite-prev"></div>\
    //     <div class="cursor-small J-hide-big-show"></div>\
    //     <div class="cursor-next J-sprite-next"></div>\
    // </div>'

    // template.showImgSwitch =
    //     '\
    // <div class="com-table-main">\
    //     <div class="J-comments-showImgSwitch-wrap comments-showImgSwitch-wrap">\
    //         <div class="thumbnails">\
    //             <div class="thumb-list">\
    //                 <ul class="clearfix"></ul>\
    //                 <span class="J-thumb-prev i-prev-btn i-prev-disable"></span>\
    //                 <span class="J-thumb-next i-next-btn i-next-disable"></span>\
    //             </div>\
    //         </div>\
    //         <div class="showContent-viewer clearfix">\
    //             <div class="photo-viewer">\
    //                 <div class="photo-wrap">\
    //                     <i></i>\
    //                     <img class="J-photo-img">\
    //                     <div class="J-cursor-left cursor-left"></div>\
    //                     <div class="J-cursor-small cursor-small"></div>\
    //                     <div class="J-cursor-right cursor-right"></div>\
    //                 </div>\
    //             </div>\
    //             <div class="J-info-viewer info-viewer">\
    //                 <div class="p-comment"></div>\
    //                 <div class="features-wrap">\
    //                     <div class="p-features">\
    //                         <ul></ul>\
    //                     </div>\
    //                     <div class="user-item-wrap">\
    //                         <div class="user-item clearfix">\
    //                             <div class="user-name"></div>\
    //                         </div>\
    //                         <div class="comment-time type-item"></div>\
    //                     </div>\
    //                 </div>\
    //             </div>\
    //         </div>\
    //     </div>\
    // </div>'
    // template.commentFeatures =
    //     '\
    // <div class="p-comment">${commentVo.content}</div>\
    // <div class="features-wrap">\
    //     <div class="p-features">\
    //         <ul>\
    //             {if commentVo.productColor}\
    //                 <li title="${commentVo.productColor}">${commentVo.productColor}</li>\
    //             {/if}\
    //             {if commentVo.productSize}\
    //                 <li title="${commentVo.productSize}">${commentVo.productSize}</li>\
    //             {/if}\
    //         </ul>\
    //     </div>\
    //     <div class="user-item-wrap">\
    //         <div class="user-item clearfix">\
    //             {if typeof commentVo.userImageUrl!=="undefined"}\
    //                 <img src="//${commentVo.userImageUrl}" width="25" height="25" alt="${commentVo.nickname}" class="user-ico"/>\
    //             {else}\
    //                 <img src="//misc.360buyimg.com/lib/img/u/${commentVo.userLevelId}.gif" width="25" height="25" alt="${commentVo.nickname}" class="user-ico"/>\
    //             {/if}\
    //             <div class="user-name" alt="${commentVo|getDisplayName}" title="${commentVo|getDisplayName}">${commentVo|getDisplayName}</div>\
    //         </div>\
    //         <div class="comment-time type-item" alt="${commentVo.creationTime|timeToDate}" title="${commentVo.creationTime|timeToDate}">${commentVo.creationTime|timeToDate}</div>\
    //         {if commentVo.location}\
    //             <div class="comment-loction type-item">${commentVo.location}</div>\
    //         {/if}\
    //     </div>\
    // </div>'

    // template.commentTemp =
    //     '\
    // <div class="mt">\
    //     <h3>商品评价</h3>\
    // </div>\
    // <div class="mc">\
    //     <div class="comment-info J-comment-info"></div>\
    //     <div class="J-comments-list comments-list ETab">\
    //         <div class="tab-main small">\
    //             <ul class="filter-list">\
    //             <li data-tab="trigger" clstag="shangpin|keycount|product|allpingjia" class="current"><a href="#none" clstag="shangpin|keycount|product|allpingjia_tuijianpaixu_{0}">全部评价<em>()</em></a></li>\
    //             <li data-tab="trigger" clstag="shangpin|keycount|product|shaidantab"><a href="#none" clstag="shangpin|keycount|product|shaidantab_tuijianpaixu_{0}">晒图<em>()</em></a></li>\
    //             <li data-tab="trigger" clstag="shangpin|keycount|product|pingjiashipin"><a href="#none" clstag="shangpin|keycount|product|pingjiashipin_tuijianpaixu_{0}">视频晒单<em>()</em></a></li>\
    //             <li class="J-addComment" data-tab="trigger" clstag="shangpin|keycount|product|zhuiping"><a href="#none" clstag="shangpin|keycount|product|zhuiping_tuijianpaixu_{0}">追评<em>()</em></a></li>\
    //             <li data-tab="trigger" clstag="shangpin|keycount|product|haoping"><a href="#none" clstag="shangpin|keycount|product|haoping_tuijianpaixu_{0}">好评<em>()</em></a></li>\
    //             <li data-tab="trigger" clstag="shangpin|keycount|product|zhongping"><a href="#none" clstag="shangpin|keycount|product|zhongping_tuijianpaixu_{0}">中评<em>()</em></a></li>\
    //             <li data-tab="trigger" clstag="shangpin|keycount|product|chaping"><a href="#none" clstag="shangpin|keycount|product|chaping_tuijianpaixu_{0}">差评<em>()</em></a></li>\
    //             <li class="J-try-report-btn" clstag="shangpin|keycount|product|sybg-bq" style="display: none;"><a href="#try-report" clstag="shangpin|keycount|product|sybg-bq_tuijianpaixu_{0}">试用报告<em>()</em></a></li>\
    //             {if !isXnzt}\
    //             <li class="comm-curr-sku" clstag="shangpin|keycount|product|dqshangpinpingjia"><span><input type="checkbox" id="comm-curr-sku" clstag="shangpin|keycount|product|dqshangpinpingjia_tuijianpaixu_{0}"></span><label for= "comm-curr-sku">只看当前商品评价</label></li>\
    //             {/if}\
    //             </ul>\
    //             <div class="extra">\
    //                 <div class="sort-select J-sort-select">\
    //                     <div class="current"><span class="J-current-sortType">推荐排序</span><i></i></div>\
    //                     <div class="others">\
    //                         <div class="curr"><span class="J-current-sortType">推荐排序</span><i></i></div>\
    //                         <ul>\
    //                             <li class="J-sortType-item" data-sorttype="5" clstag="shangpin|keycount|product|morenpaixu">推荐排序</li>\
    //                             <li class="J-sortType-item" data-sorttype="6" clstag="shangpin|keycount|product|shijianpaixu">时间排序</li>\
    //                         </ul>\
    //                     </div>\
    //                 </div>\
    //             </div>\
    //         </div>\
    //         <div class="tab-con">\
    //             <div id="comment-0" data-tab="item">全部</div>\
    //             <div id="comment-1" data-tab="item" class="hide"><div class="iloading">正在加载中，请稍候...</div></div>\
    //             <div id="comment-2" data-tab="item" class="hide"><div class="iloading">正在加载中，请稍候...</div></div>\
    //             <div id="comment-3" data-tab="item" class="hide"><div class="iloading">正在加载中，请稍候...</div></div>\
    //             <div id="comment-4" data-tab="item" class="hide"><div class="iloading">正在加载中，请稍候...</div></div>\
    //             <div id="comment-5" data-tab="item" class="hide"><div class="iloading">正在加载中，请稍候...</div></div>\
    //             <div id="comment-6" data-tab="item" class="hide"><div class="iloading">正在加载中，请稍候...</div></div>\
    //         </div>\
    //     </div>\
    // </div>'

    // //  <!--<dt>${reply.venderShopInfo.title}回复：</dt>-->

    // var commTypeMap = {
    //     '0': 0, // 全部
    //     '1': 4, // 晒图
    //     '2': 7, // 视频评价
    //     '3': 5, // 追评
    //     '4': 3, // 好评
    //     '5': 2, // 中评
    //     '6': 1 // 差评
    // }

    // var _modifiers = {
    //     formatDay: function(day) {
    //         return day === 0 ? '当天' : day + '天后'
    //     },
    //     formatCommentTime: function(time) {
    //         return !!time ? time.substring(0, time.lastIndexOf(' ')) : ''
    //     },
    //     timeToDate: function(time) {
    //         return !!time
    //             ? time.substring(0, time.indexOf(' ')).replace(/-/g, '.')
    //             : ''
    //     },
    //     getDisplayName: function(data) {
    //         if (data.nickname) {
    //             if (data.anonymousFlag && data.anonymousFlag == 1) {
    //                 return data.nickname.replace(
    //                     data.nickname.substring(1, data.nickname.length - 1),
    //                     '***'
    //                 )
    //             } else {
    //                 return data.nickname
    //             }
    //         } else {
    //             return data.pin
    //         }
    //     },
    //     getReplyName: function(reply) {
    //         return reply.venderShopInfo && reply.venderShopInfo.title
    //             ? reply.venderShopInfo.title
    //             : reply.pin
    //     }
    // }

    // var CommentNew = {
    //     inited: false,
    //     init: function(cfg) {
    //         var _this = this
    //         _this.changeTemplate() //替换模板

    //         _this.$commRate = $('.J-comment-info')
    //         _this.$commList = $('.J-comments-list')
    //         _this.$commCurrSku = $('#comm-curr-sku')
    //         _this.$wrap = $('#comment-0')
    //         _this.sku = cfg.skuid
    //         _this.cfg = cfg
    //         _this.isShadowSku = typeof _this.cfg.isShadowSku != 'undefined'
    //             ? _this.cfg.isShadowSku
    //             : 0

    //         _this.isSupportTransform =
    //             'transform' in document.documentElement.style

    //         _this.sortType = 5
    //         _this.isFirstLoad = true
    //         _this.disabledSortType = false
    //         _this.pageNav = _this.$wrap.find('.ui-page')

    //         _this.sortTypeObj = {
    //             5: '推荐排序',
    //             6: '时间排序'
    //         }

    //         _this.pager = null
    //         _this.currCommentType = 0
    //         // tag 是否可以点击
    //         _this.isTagAvailable = false
    //         // 默认标签id
    //         _this.rid = 0

    //         // if (!_this.inited) {
    //         //     $('#comment-0').attr('data-loaded', '1')
    //         // }

    //         _this.inited = true

    //         if (cfg.addComments == false) {
    //             $('.J-addComment').hide()
    //         }

    //         _this.bindEvent()

    //         return _this
    //     },
    //     changeTemplate: function() {
    //         var isXnzt=G.onAttr('isXnzt')
    //         $('#comment').html(template.commentTemp.process({
    //             isXnzt:isXnzt
    //         }))
    //         Event.fire({
    //             type: 'onCommentTemplateReady'
    //         })
    //     },
    //     setImageList: function($panel) {
    //         var _this = this
    //         // var url =
    //         //     '//club.jd.com/discussion/getProductPageImageCommentList.action?productId=' +
    //         //     _this.sku +
    //         //     '&isShadowSku=' +
    //         //     _this.isShadowSku
    //         var bbtfVal = G.serializeUrl(location.href).param.bbtf // 获取URL后bbtf参数
    //         var url = {
    //             productId: _this.sku,
    //             isShadowSku: _this.isShadowSku,
    //             bbtf: bbtfVal && bbtfVal.length > 0 ? "1" : "",
    //             shield: window.pageConfig.product.shield || "",
    //             functionId: "pc_club_getProductPageImageCommentList"
    //         }

    //         if (_this.currSku) {
    //             var url = {
    //                 productId: _this.sku,
    //                 isShadowSku: _this.isShadowSku,
    //                 bbtf: bbtfVal && bbtfVal.length > 0 ? "1" : "",
    //                 shield: window.pageConfig.product.shield || "",
    //                 functionId: "pc_club_getSkuPageImageCommentList"
    //              }
    //         }

    //         if (_this.$commList.find('.current').attr('data-num') == 0) {
    //             $panel.html('<div class="ac comments-item">「暂无晒图」</div>')
    //             return false
    //         }

    //         var imgNum = G.wideVersion ? 10 : 7
    //         $panel.html(template.showImgSwitch)

    //         _this.shopImgSwitch = window.shopImgSwitch = new ShopImgSwitch({
    //             imgNum: imgNum,
    //             wideVersion: G.wideVersion,
    //             showImgSwitch: '.comments-showImgSwitch-wrap',
    //             url: url,
    //             onReady: function(count) {
    //                 _this.$commList
    //                     .find('[data-tab="trigger"] a em')
    //                     .eq(1)
    //                     .html('(' + count + ')')
    //                 if (!count) {
    //                     $panel.html(
    //                         '<div class="ac comments-item">「暂无晒图」</div>'
    //                     )
    //                 }
    //             },
    //             onUpdate: function(imgObj, res) {
    //                 var $infoViewer = _this.$commList.find('.J-info-viewer')
    //                 imgObj._MODIFIERS = _modifiers

    //                 if (imgObj.commentVo.id == 0) {
    //                     $infoViewer.hide()
    //                 } else {
    //                     $infoViewer.show()
    //                     $infoViewer.html(
    //                         template.commentFeatures.process(imgObj)
    //                     )
    //                 }
    //                 if (res && res.imgComments) {
    //                     _this.$commList
    //                         .find('[data-tab="trigger"] a em')
    //                         .eq(1)
    //                         .html('(' + res.imgComments.imgCommentCount + ')')
    //                 }
    //             }
    //         })
    //     },
    //     bindEvent: function() {
    //         var _this = this
    //         _this.$commList.ETab({
    //             onSwitch: function(i) {
    //                 var panel = this.items.eq(i)

    //                 var currCommentType = (_this.currCommentType =
    //                     commTypeMap[i])
    //                 _this.$wrap = panel

    //                 _this.updateSortType(currCommentType) //根据标签类型更新排序规则

    //                 _this.rid = 0

    //                 if (i == 1 && panel.attr('data-loaded') !== '1') {
    //                     _this.setImageList(panel)

    //                     panel.attr('data-loaded', '1');
    //                     return
    //                 }

    //                 // if (panel.attr('data-loaded') !== '1') {
    //                 _this.getData(panel, currCommentType, 0)
    //                 //     panel.attr('data-loaded', '1')
    //                 // }
    //                 // else {
    //                 //     // 折叠评价切换tab时需要手动重新初始化
    //                 //     _this.type = commTypeMap[i]
    //                 //     _this.setFoldComment()
    //                 // }
    //             }
    //         })
    //         pageConfig.commentsList_TAB = _this.$commList.data('ETab')

    //         _this.$commList.delegate(
    //             '.J-nice',
    //             'click',
    //             $.proxy(this.handleOperation, this)
    //         )

    //         _this.$commList.delegate(
    //             '.J-report',
    //             'click',
    //             $.proxy(this.handleOperation, this)
    //         )

    //         _this.$commList.delegate(
    //             '.J-fold-comment',
    //             'click',
    //             $.proxy(this.handleFoldComment, this)
    //         );

    //         var $doc = $(document);

    //         $doc.delegate('.comment-item .J-thumb-img', 'click', function() {
    //             var $this = $(this)
    //             var src = $this.find('img').attr('src')
    //             var ind = $this.data('ind')
    //             var $picList = $this.parents('.J-pic-list'),
    //                 $picViewWrap = $picList.siblings('.J-pic-view-wrap'),
    //                 $videoViewWrap = $picList.siblings('.J-video-view-wrap');
    //             //处理视频
    //             if(ind == -1){
    //                 _this.handleVideo($videoViewWrap)
    //                 return;
    //             }
    //             _this.hadnlePhotoSwitch($picViewWrap, ind, src)
    //         })
    //         // 评论视频跟随页面滚动播放
    //         $(document).bind('scroll',function(){
    //             if(isIE8) return;
    //             //setTimeout防抖
    //             clearTimeout(_this.timer)
    //             _this.timer = setTimeout(function () {
    //                 //滚动条高度+视窗高度 = 可见区域底部高度
    //                 var scrollY = (window.scrollY || window.pageYOffset);
    //                 var visibleBottom = scrollY  + document.documentElement.clientHeight;
    //                 //可见区域顶部高度
    //                 var visibleTop = scrollY;
    //                 for (var i = 0; i < videoArr.length; i++) {
    //                     if(!videoArr[i] || !videoArr[i].el_) continue;
    //                     var centerY = videoArr[i].el_.offsetTop+(videoArr[i].el_.offsetHeight);
    //                     if(centerY>(visibleTop + document.documentElement.clientHeight/2)&&centerY<visibleBottom){
    //                         videoArr[i].play();
    //                     }
    //                 }
    //             },200)
    //         })

    //         $doc.delegate('.comment-item .J-sprite-prev', 'click', function() {
    //             var $this = $(this);
    //             var $picViewWrap = $this.parents('.J-pic-view-wrap');
    //             var index = $picViewWrap.attr('data-ind');
    //             index--
    //             if(index == -1){
    //                 var $videoViewWrap = $picViewWrap.siblings('.J-video-view-wrap');
    //                 _this.handleVideo($videoViewWrap)
    //             }else{
    //                 _this.hadnlePhotoSwitch($picViewWrap, index)
    //             }
    //         })

    //         $doc.delegate('.comment-item .J-sprite-next', 'click', function() {
    //             var $this = $(this)
    //             var $picViewWrap = $this.parents('.J-pic-view-wrap')
    //             var index = $picViewWrap.attr('data-ind')
    //             index++
    //             _this.hadnlePhotoSwitch($picViewWrap, index)
    //         })

    //         $doc.delegate('.comment-item .J-hide-big-show', 'click', function() {
    //             var $this = $(this)
    //             var $picViewWrap = $this.parents('.J-pic-view-wrap')
    //             $picViewWrap.html('')
    //             $picViewWrap
    //                 .siblings('.J-pic-list')
    //                 .find('.J-thumb-img')
    //                 .removeClass('current')
    //         })

    //         if (_this.isSupportTransform) {
    //             $doc.delegate('.comment-item .J-turn-left', 'click', function() {
    //                 var $this = $(this)
    //                 var $picViewWrap = $this.parents('.J-pic-view-wrap')
    //                 _this.hadnleRotate($picViewWrap, true)
    //             })

    //             $doc.delegate('.comment-item .J-turn-right', 'click', function() {
    //                 var $this = $(this)
    //                 var $picViewWrap = $this.parents('.J-pic-view-wrap')
    //                 _this.hadnleRotate($picViewWrap, false)
    //             })
    //         }

    //         _this.$commCurrSku
    //             .unbind('change')
    //             .bind('change', $.proxy(this.handleCheck, this))

    //         //排序
    //         _this.$commList.delegate('.J-sortType-item', 'click', function() {
    //             var $this = $(this)
    //             var sortType = $this.attr('data-sorttype')
    //             _this.userSortType = _this.sortType = parseInt(sortType)

    //             var i = _this.$commList.data('ETab').index
    //             var curr = commTypeMap[i]
    //             _this.getData($('#comment-' + i), curr, 0, sortType)
    //         })

    //         //手机QQ 微信购物弹窗
    //         //            _this.$commList.delegate('.J-p-tooltips', 'mouseenter', function () {
    //         //                $(this).addClass('hover');
    //         //                _this.setClientPopImg();
    //         //            });
    //         //
    //         //            _this.$commList.delegate('.J-p-tooltips', 'mouseleave', function () {
    //         //                $(this).removeClass('hover');
    //         //            });

    //         if ($.browser.isIE6()) {
    //             _this.$commList.delegate(
    //                 '.J-sort-select',
    //                 'mouseenter',
    //                 function() {
    //                     $(this).find('.others').show()
    //                 }
    //             )

    //             _this.$commList.delegate(
    //                 '.J-sort-select',
    //                 'mouseleave',
    //                 function() {
    //                     $(this).find('.others').hide()
    //                 }
    //             )
    //         }
    //     },
    //     handleCheck: function(e) {
    //         var _this = this
    //         _this.currSku = !!$(e.target).attr('checked')
    //         _this.$commList.find('[data-tab="item"]').removeAttr('data-loaded')
    //         _this.getData(_this.wrap, _this.currCommentType)

    //         var $panel = _this.$commList.find('#comment-1')
    //         $panel.removeAttr('data-loaded')
    //         _this.setImageList($panel)
    //     },
    //     hadnleRotate: function($picViewWrap, isLeft) {
    //         var rotate = $picViewWrap.attr('data-rotation')
    //         var $picView = $picViewWrap.find('.J-pic-view')
    //         var $img = $picViewWrap.find('img')

    //         if (isLeft) {
    //             rotate--
    //         } else {
    //             rotate++
    //         }

    //         if (rotate < 0) {
    //             rotate += 4
    //         }

    //         var imgW = $picViewWrap.find('img').width()
    //         var imgH = $picViewWrap.find('img').height()

    //         var resultW = imgW,
    //             resultH = imgH,
    //             ml = 0,
    //             mt = 0,
    //             minH = 0

    //         if (rotate == 1 || rotate == 3) {
    //             resultW = imgH
    //             resultH = imgW
    //             ml = (imgH - imgW) / 2
    //             mt = (imgW - imgH) / 2
    //         }

    //         $picView.css({
    //             paddingTop: Math.max(0, (minH - resultH) / 2),
    //             paddingBottom: Math.max(0, (minH - resultH) / 2),
    //             width: resultW,
    //             height: resultH
    //         })
    //         $picViewWrap.attr('data-rotation', (rotate %= 4))

    //         $img.css({
    //             transform: 'rotate(' + 90 * rotate + 'deg)',
    //             marginLeft: ml,
    //             marginTop: mt
    //         })
    //     },
    //     resetRoate: function($picViewWrap) {
    //         var $picView = $picViewWrap.find('.J-pic-view')
    //         var $img = $picViewWrap.find('img')
    //         $picView.css({
    //             paddingTop: 0,
    //             paddingBottom: 0,
    //             width: 'auto',
    //             height: 'auto'
    //         })
    //         $img.css({
    //             transform: 'rotate(0deg)',
    //             marginLeft: 0,
    //             marginTop: 0
    //         })
    //     },
    //     handleVideo:function($videoViewWrap){
    //         var $picViewWrap = $videoViewWrap
    //             .siblings('.J-pic-view-wrap ');
    //         var videoId = $videoViewWrap.find('video').attr('id');
    //         $picViewWrap.hide();
    //         $videoViewWrap.show();
    //         $picViewWrap.attr('data-rotation', 0)
    //         $picViewWrap.attr('data-ind', -1);
    //         var $thumbImg = $videoViewWrap
    //             .siblings('.J-pic-list')
    //             .find('.J-thumb-img')
    //         $thumbImg.removeClass('current');
    //         var $targetImg = $thumbImg.eq(0)
    //         $targetImg.addClass('current')

    //         videoBox.initPlayer(videoId,{
    //             mute:true,
    //             controls:true,
    //             fuScrnEnabled:true,
    //             callback:function (player) {
    //                 //防止重复绑定
    //                 videoArr.push(player);
    //                 if(!player.pauseFlag){
    //                     player.pauseFlag = 1;
    //                     player.on('play',function () {
    //                         for(var i = 0;i<videoArr.length;i++){
    //                             if(player.id_ != videoArr[i].id_){
    //                                 videoArr[i] && videoArr[i].pause();
    //                             }
    //                         }
    //                         typeof videojs == 'function' && videojs.players['video-player'] && videojs.players['video-player'].pause();
    //                         typeof videojs == 'function' && videojs.players['detail-video-player'] && videojs.players['detail-video-player'].pause();
    //                     })
    //                 }
    //                 player.play();
    //             }
    //         })

    //     },
    //     hadnlePhotoSwitch: function($picViewWrap, index) {
    //         var _this = this
    //         var $thumbImg = $picViewWrap
    //             .siblings('.J-pic-list')
    //             .find(".J-thumb-img")
    //         var $targetImg = $picViewWrap
    //             .siblings('.J-pic-list')
    //             .find(".J-thumb-img[data-ind='"+index+"']")
    //         var imgSrc = $targetImg.find('img').attr('src')
    //         imgSrc = imgSrc.replace('48x48', '616x405')
    //         imgSrc = imgSrc.replace('n0', 'shaidan')
    //         $picViewWrap.show();
    //         var $videoViewWrap = $picViewWrap
    //             .siblings('.J-video-view-wrap ');
    //         var videoId = $picViewWrap.parents('.comment-item').data('guid');
    //         (typeof videojs == "function") && videojs.players['player'+videoId] && (videojs.players['player'+videoId].pause());
    //         (typeof videojs == "function") && videojs.players['playerafter'+videoId] && (videojs.players['playerafter'+videoId].pause());
    //         $videoViewWrap.hide();

    //         if ($picViewWrap.find('.J-pic-view').length) {
    //             $picViewWrap.find('img').attr('src', imgSrc)
    //         } else {
    //             $picViewWrap.html(
    //                 template.picView.process({
    //                     imgSrc: imgSrc,
    //                     isSupportTransform: _this.isSupportTransform
    //                 })
    //             )
    //         }

    //         $picViewWrap.attr('data-rotation', 0)
    //         $picViewWrap.attr('data-ind', index)
    //         $thumbImg.removeClass('current')
    //         $targetImg.addClass('current')

    //         var $prev = $picViewWrap.find('.J-sprite-prev')
    //         var $next = $picViewWrap.find('.J-sprite-next')

    //         if (index == 0 && $thumbImg.length == 1) {
    //             $next.hide()
    //             $prev.hide()
    //         } else {
    //             if (index <= 0) {
    //                 $prev.hide()
    //                 $next.show()
    //             } else if ((index + 1 >= $thumbImg.length && !$videoViewWrap.length) || (index + 1 >= ($thumbImg.length - 1 ) && $videoViewWrap.length)) {
    //                 $next.hide()
    //                 $prev.show()
    //             } else {
    //                 $next.show()
    //                 $prev.show()
    //             }
    //         }
    //         _this.resetRoate($picViewWrap)
    //     },
    //     getData: function($obj, type, page, sortType, rid) {
    //         var _this = this

    //         _this.type = typeof type === 'undefined' ? _this.type : type || 0
    //         _this.page = typeof page === 'undefined' ? _this.page : page || 0
    //         _this.sortType = sortType || _this.sortType

    //         if (!rid) {
    //             this.clearTag()
    //         } else {
    //             rid = this.rid || 0
    //         }

    //         _this.commRateLoaded = false

    //         //为能顺利的度过双十一， 请将pc单品页调用的评价相关的接口域名： 由club.jd.com(不支持cdn)修改为 sclub.jd.com(支持cdn) 2017.11.02
    //          // start
    //          var time = new Date().getTime()
    //          var bbtfVal = G.serializeUrl(location.href).param.bbtf
    //          var paramJson = {
    //              // appid: 'item-v3',
    //              // functionId: 'pc_club_productPageComments',//商品评价列表接口
    //              // client: 'pc',
    //              // clientVersion: '1.0.0',
    //              // t: time,//生成当前时间毫秒数
    //              // loginType: '3',
    //              // uuid: Tools.getCookieNew("__jda") || '',
    //              productId: _this.sku,
    //              score: _this.type,
    //              sortType: _this.sortType,
    //              page: _this.page,
    //              pageSize: 10,
    //              isShadowSku: _this.isShadowSku,
    //              rid: rid,
    //              fold: 1,
    //              bbtf: bbtfVal && bbtfVal.length > 0 ? "1" : "",
    //              shield: window.pageConfig.product.shield || "",
    //          }
 
    //          var body = JSON.stringify(paramJson);
    //          // 加固start
    //          var colorParm = {
    //              appid: 'item-v3',
    //              functionId: 'pc_club_productPageComments',
    //              client: 'pc',
    //              clientVersion: '1.0.0',
    //              t: time,//生成当前时间毫秒数
    //              body: body,
    //          }
    //          if (_this.currSku) {//sku维度获取评价列表
    //              //待定
    //              colorParm.functionId = 'pc_club_skuProductPageComments'
    //          }
    //          try{
    //              var colorParmSign =JSON.parse(JSON.stringify(colorParm))
    //              colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
    //              window.PSign.sign(colorParmSign).then(function(signedParams){
    //                  colorParm['h5st']  = encodeURI(signedParams.h5st)
    //                  try{
    //                      getJsToken(function (res) {
    //                          if(res && res.jsToken){
    //                              colorParm['x-api-eid-token'] = res.jsToken;
    //                          }
    //                          colorParm['loginType'] = '3';
    //                          colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
    //                          getCommentData(colorParm);
    //                      }, 600);
    //                  }catch(e){
    //                      colorParm['loginType'] = '3';
    //                      colorParm['uuid'] = '';
    //                      getCommentData(colorParm);
    //                      //烛龙上报
    //                      Tools.getJmfe(colorParm, e, "comment评价列表设备指纹异常",751)
    //                  }
    //              })
    //          }catch(e){
    //              colorParm['loginType'] = '3';
    //              colorParm['uuid'] = '';
    //              getCommentData(colorParm);
    //              //烛龙上报
    //              Tools.getJmfe(colorParm, e, "comment评价列表加固异常",751)
    //          }            
    //          // 加固end
 
    //          var host = '//api.m.jd.com'
    //          if(pageConfig.product && pageConfig.product.colorApiDomain){
    //              host = pageConfig.product && pageConfig.product.colorApiDomain
    //          }
    //          // end
    //          function getCommentData(colorParm) {
    //              $.ajax({
    //                  url: host,
    //                  data: colorParm,
    //                  dataType: 'json',
    //                  contentType: "application/json;charset=gbk",
    //                  xhrFields: {
    //                      withCredentials: true,
    //                  },
    //                  headers: Tools.getUrlSdx(), 
    //                  success: function(r) {
    //                      // 领导人书籍屏蔽评价tab和楼层
    //                      if (
    //                          r &&
    //                          r.productCommentSummary &&
    //                          (r.productCommentSummary.sensitiveBook == 1)
    //                      ) {
    //                          $('#comment').html('');
    //                      } else {
    //                          _this.setData(r);
    //                      }
    //                  },
    //                  error: function () {
    //                      $('#comment .mc').html('暂无评价');
    //                  }
    //              })
    //          }
    //     },
    //     getCallbackName: function() {
    //         // fetchJSON_comment98vv112211
    //         var cbName = 'fetchJSON_comment'
    //         var jwotest = readCookie('jwotest_product')
    //         var version = pageConfig.product.commentVersion

    //         cbName += jwotest ? jwotest : '98'
    //         cbName += version ? 'vv' + version : ''

    //         return cbName
    //     },
    //     setData: function(r) {
    //         var _this = this

    //         r._MODIFIERS = _modifiers
    //         r.rid = this.rid

    //         this.isTagAvailable = this.checkTag(r)

    //         // 如果是晒单类型，只更新评价数量，不做其它操作
    //         if (this.type === 4) {
    //             return this.setCommentCount(r)
    //         }
    //         //alert(template.list.process(r));
    //         if (!r) {
    //             this.$wrap.html('　暂无评价')
    //             this.commRate.find('.mc').html('　暂无评价')
    //         }
    //         // 接口异常
    //         if (typeof r.comments == 'undefined') {
    //             this.$wrap.html('<div class="norecode"> 暂无商品评价！</div>')
    //             return
    //         }

    //         if (this.commRateLoaded === false) {
    //             this.setCommRate(r)
    //         }

    //         this.setCommentCount(r)
    //         //用于分页时及时销毁播放器
    //         //如此频繁的销毁和重建，可能会有性能问题啊，也没有办法，后期再说吧~
    //         for(var i = 0;i<videoArr.length;i++){
    //             videoArr[i] && videoArr[i].el_ && videoArr[i].dispose();
    //         }
    //         videoArr = [];
    //         //将主图视频和商详视频播放器加进来，评论视频播放时，这俩也要暂停
    //         if(isIE8){
    //             for(var i = 0;i<r.comments.length;i++){
    //                 r.comments[i].isIE8 = true
    //             }
    //         }else{
    //             for(var i = 0;i<r.comments.length;i++){
    //                 r.comments[i].isIE8 = false
    //             }
    //         }
    //         this.$wrap.html(template.commentItem.process(r))
    //         if(!isIE8){
    //             for(var i = 0;i<r.comments.length;i++){
    //                 if(r.comments[i].videos && r.comments[i].videos.length > 0){
    //                     var videoId = 'player'+r.comments[i].guid;
    //                     videoBox.initPlayer(videoId,{
    //                         mute:true,
    //                         controls:true,
    //                         preload:'none',
    //                         fuScrnEnabled:true,
    //                         callback:function (player) {
    //                             videoArr.push(player);
    //                             //防止重复绑定
    //                             if(!player.pauseFlag){
    //                                 player.pauseFlag = 1;
    //                                 player.on('play',function () {
    //                                     for(var i = 0;i<videoArr.length;i++){
    //                                         if(player.id_ != videoArr[i].id_){
    //                                             videoArr[i] && videoArr[i].pause();
    //                                         }
    //                                     }
    //                                     typeof videojs == 'function' && videojs.players['video-player'] && videojs.players['video-player'].pause();
    //                                     typeof videojs == 'function' && videojs.players['detail-video-player'] && videojs.players['detail-video-player'].pause();
    //                                 })
    //                             }
    //                         }

    //                     })
    //                 }
    //             }
    //         }

    //         this.$pageNav = this.$wrap.find('.ui-page')

    //         this.bindTagClick()

    //         this.setPageNav(r)

    //         //晒图评价和视频评价都不要有个排序
    //         if(this.type != 4 && this.type != 7){
    //             this.setSortSelect(r)

    //         }

    //         setTimeout(function() {
    //             _this.setFoldComment(r)
    //         }, 100);

    //         // AI算法推荐埋点
    //         var csv = r && r.csv;

    //         $('.filter-list li', this.$commList).each(function() {
    //             var $this = $(this);
    //             var $target = $this.find('[clstag]');
    //             if ($target.length) {
    //                 var clstag = $target.attr('clstag');
    //                 $target.attr('clstag', clstag.format(csv));
    //             }
    //         });
    //     },
    //     bindTagClick: function () {
    //         if (this.isTagAvailable) {
    //             // 标签
    //             this.$commRate.undelegate('click')
    //                 .delegate('.tag-list span', 'click', $.proxy(this.handleTagClick, this))
    //             this.$commRate.find('.tag-list').addClass('tag-available')
    //         } else {
    //             this.$commRate.find('.tag-list').removeClass('tag-available')
    //         }
    //     },
    //     handleTagClick: function (e) {
    //         var $this = $(e.currentTarget)
    //         this.rid = $this.attr('data-rid')

    //         var tab = this.$commList.data('ETab')
    //         var i = tab.index

    //         tab.go(0, true)

    //         this.$wrap = $('#comment-0')
    //         this.$commCurrSku.attr('checked', false)
    //         this.currSku = false

    //         this.getData(
    //             $('#comment-' + i),
    //             this.type,
    //             0,
    //             this.sortType,
    //             this.rid
    //         )
    //         this.resetTagHL()
    //         $this.addClass('selected')
    //     },
    //     resetTagHL: function () {
    //         this.$commRate.find('span').removeClass('selected')
    //     },
    //     clearTag: function () {
    //         this.rid = 0
    //         this.resetTagHL()
    //     },
    //     checkTag: function(data) {
    //         return (
    //             data &&
    //             data.hotCommentTagStatistics &&
    //             data.hotCommentTagStatistics.length &&
    //             data.hotCommentTagStatistics[0].canBeFiltered
    //         )
    //     },
    //     setCommentCount: function(data) {
    //         var _this = this
    //         var commentsCountWrap = _this.$commList.find('[data-tab="trigger"]')
    //         var r = data.productCommentSummary

    //         data.imageListCount = data.imageListCount || 0

    //         commentsCountWrap
    //             .eq(0)
    //             .attr('data-num', r.commentCount)
    //             .find('em')
    //             .html('(' + r.commentCountStr + ')')
    //         var $current = pageConfig.commentsList_TAB.items.eq(
    //             pageConfig.commentsList_TAB.index
    //         )

    //         if (!$current.is('#comment-1') && !_this.imgListCountLoaded) {
    //             commentsCountWrap
    //                 .eq(1)
    //                 .attr('data-num', data.imageListCount)
    //                 .find('em')
    //                 .html('(' + data.imageListCount + ')')
    //             _this.imgListCountLoaded = true
    //         }
    //         commentsCountWrap
    //             .eq(2)
    //             .attr('data-num', r.videoCountStr)
    //         commentsCountWrap
    //             .eq(2)
    //             .find('em')
    //             .html('(' + r.videoCountStr + ')')
    //         commentsCountWrap
    //             .eq(3)
    //             .attr('data-num', r.afterCount)
    //             .find('em')
    //             .html('(' + r.afterCountStr + ')')
    //         commentsCountWrap
    //             .eq(4)
    //             .attr('data-num', r.goodCount)
    //             .find('em')
    //             .html('(' + r.goodCountStr + ')')
    //         commentsCountWrap
    //             .eq(5)
    //             .attr('data-num', r.generalCount)
    //             .find('em')
    //             .html('(' + r.generalCountStr + ')')
    //         commentsCountWrap
    //             .eq(6)
    //             .attr('data-num', r.poorCount)
    //             .find('em')
    //             .html('(' + r.poorCountStr + ')')
    //     },
    //     setCommRate: function(data) {
    //         var _this = this

    //         data.vTagStatisticsResult = []
    //         for (var i in data.vTagStatistics) {
    //             var arr = data.vTagStatistics[i]
    //             arr.sort(function(a, b) {
    //                 if (a.isTopic) {
    //                     return -1
    //                 }
    //                 return b.tagRate - a.tagRate
    //             })

    //             var namesObjArr = arr.slice(2)
    //             var namesArr = []
    //             for (var i in namesObjArr) {
    //                 namesArr.push(namesObjArr[i].name)
    //             }
    //             data.vTagStatisticsResult.push({
    //                 name: arr[0].name,
    //                 maxName: arr[1].name,
    //                 // 修复浮点数据运算问题
    //                 maxRate: arr[1].tagRate * 10000 / 100,
    //                 otherName: namesArr.join('或')
    //             })
    //         }
    //         _this.$commRate.html(template.commentRate.process(data))
    //         _this.commRateLoaded = true
    //     },
    //     handleFoldComment: function() {
    //         FoldComment.getTheFirstPage(true);
    //     },
    //     isLastPage: function() {
    //         if (this.pager) {
    //             // 当前评价数为0，但是有折叠评价
    //             var opt = this.pager.options
    //             return opt.currentPage === opt.totalPage
    //         } else {
    //             return true
    //         }
    //     },
    //     setFoldComment: function(r) {
    //         if ((this.isLastPage() || r.maxPage == 0) && !this.currSku) {
    //             FoldComment.init(this.$wrap)

    //             var $defaultReputation = $('.J-default-reputaion');
    //             if($defaultReputation.length){
    //                 $defaultReputation.show()
    //             }
    //         } else {
    //             this.$wrap.find('.J-fold-comment').hide()
    //         }
    //     },
    //     setPageNav: function(data) {
    //         var _this = this

    //         //注释此段代码原因：当评论数（不含折叠评论）为0时，走下面判断逻辑不会初始化分页组件，导致totalPage的值取上一次值，从而不请求折叠评价接口 2017.10.21
    //         // if (!data || !data.comments || !data.comments.length) {
    //         //     return _this.$pageNav.html('')
    //         // }

    //         var i = _this.$commList.data('ETab').index
    //         _this.pager = _this.$pageNav.pager({
    //             // total: data['productCommentSummary'][commNumValue],
    //             total: data['maxPage'] * 10,
    //             pageSize: 10,
    //             currentPageClass: 'ui-page-curr',
    //             currentPage: _this.page + 1,
    //             pageHref: '#comment',
    //             prevClass: 'ui-pager-prev',
    //             nextClass: 'ui-pager-next',
    //             prevText: '上一页',
    //             nextText: '下一页',
    //             callback: function(pageId) {
    //                 var page = pageId - 1
    //                 removeLastPage(_this.pager)
    //                 _this.getData(
    //                     $('#comment-' + i),
    //                     data.score,
    //                     page,
    //                     _this.sortType,
    //                     _this.rid || 0
    //                 )
    //             }
    //         })

    //         setTimeout(function() {
    //             removeLastPage(_this.pager)
    //         }, 100)

    //         function removeLastPage(pager) {
    //             if (!pager) {
    //                 return false
    //             }
    //             var $span = pager.el.find('span:last')

    //             if ($span.index() > 5) {
    //                 $span.next().remove()
    //             }
    //         }

    //         setTimeout(function() {
    //             setClsTag(_this.$pageNav)
    //         }, 500)

    //         function setClsTag($el) {
    //             // 添加埋点
    //             $el.find('[rel]').each(function() {
    //                 var page = $(this).attr('rel')

    //                 $(this).attr(
    //                     'clstag',
    //                     'shangpin|keycount|product|pinglunfanye-' + page
    //                 )
    //             })

    //             var $prev = $el.find('.ui-pager-prev')
    //             $prev.attr(
    //                 'clstag',
    //                 'shangpin|keycount|product|pinglunfanye-frontpage'
    //             )
    //             var $next = $el.find('.ui-pager-next')
    //             $next.attr(
    //                 'clstag',
    //                 'shangpin|keycount|product|pinglunfanye-nextpage'
    //             )
    //         }
    //     },
    //     handleOperation: function(e) {
    //         var _this = this
    //         var $this = $(e.currentTarget)
    //         var needLogin = $this.attr('data-login') === '1'

    //         if (needLogin) {
    //             login({
    //                 modal: true,
    //                 complete: function() {
    //                     if ($this.hasClass('J-nice')) {
    //                         _this.agree($this)
    //                     }
    //                     if ($this.hasClass('J-report')) {
    //                         _this.report($this)
    //                     }
    //                 }
    //             })
    //         }
    //     },
    //     report: function($el) {
    //         var guid = $el.parents('.comment-item').attr('data-guid')
    //         Report.init(guid)
    //     },
    //     agree: function($el) {
    //         var commentId = $el.attr('data-guid')
    //         $.ajax({
    //             url: '//club.jd.com/index.php',
    //             data: {
    //                 mod: 'ProductComment',
    //                 action: 'saveCommentUserfulVote',
    //                 commentId: commentId,
    //                 isUseful: true
    //             },
    //             dataType: 'jsonp',
    //             success: function(data) {
    //                 if (1 == data.status) {
    //                     var count = parseInt($el.attr('title')) + 1
    //                     $el.attr('title', count)
    //                     $el.html('点赞（' + count + '）')
    //                     $el.addClass('praised')
    //                 } else {
    //                     alert('只能点赞一次呦')
    //                 }
    //             }
    //         })
    //     },
    //     setSortSelect: function(data) {
    //         var _this = this,
    //             $sortSelect = $('.J-sort-select')
    //         $('.J-current-sortType').html(_this.sortTypeObj[data.soType])
    //         //            if(_this.isFirstLoad) {
    //         $('.J-sort-select').show()
    //         _this.disabledSortType = data.soType != _this.sortType
    //         _this.isFirstLoad = false
    //         //            }
    //         _this.sortType = data.soType
    //         $sortSelect.toggleClass('disable', _this.disabledSortType)
    //     },
    //     updateSortType: function(currPage) {
    //         var _this = this,
    //             $sortSelect = $('.J-sort-select')
    //         if (currPage == 4 || currPage == 7) {
    //             //晒单评价和视频评价只有时间排序，并且不可选择
    //             $sortSelect.addClass('disable')
    //             $('.J-current-sortType').html(_this.sortTypeObj[6])
    //         }
    //     }
    // }

    // var FoldComment = {
    //     dialog: function(opts) {
    //         function noop(){}
    //         opts = $.extend({
    //             type: "text",
    //             title: "已忽略的评价",
    //             fixed: true,
    //             width: 850,
    //             height: 520,
    //             source: "",
    //             onCancel: noop,
    //             onReady: noop
    //         }, opts);
    //         $('body').dialog(opts);
    //     },

    //     init: function($wrap) {
    //         this.$wrap = $wrap;
    //         this.getTheFirstPage();
    //     },

    //     getData: function(params, onSuccess, onError) {
    //         var time = new Date().getTime()
    //         var bbtfVal = G.serializeUrl(location.href).param.bbtf // 获取URL后bbtf参数
    //         var colorParm = $.extend({
    //             appid: 'item-v3',
    //             functionId: 'pc_club_getProductPageFoldComments',
    //             client: 'pc',
    //             clientVersion: '1.0.0',
    //             t: time,//生成当前时间毫秒数
    //             // referenceIds: skus.join(","),
    //             loginType: '3',
    //             uuid: Tools.getCookieNew("__jda") || '',
    //             productId: CommentNew.sku || "",
    //             score: CommentNew.type || "0",
    //             sortType: CommentNew.sortType || "",
    //             page: 0,
    //             bbtf: bbtfVal && bbtfVal.length > 0 ? "1" : "",
    //             shield: window.pageConfig.product.shield || "",
    //             pageSize: 5
    //         }, params)

    //         var host = '//api.m.jd.com'
    //         if(pageConfig.product && pageConfig.product.colorApiDomain){
    //         host = pageConfig.product && pageConfig.product.colorApiDomain
    //     }
    //         return $.ajax({
    //             url: host,
    //             data: colorParm,
    //             dataType: 'json',
    //             contentType: "application/json;charset=gbk",
    //             xhrFields: {
    //                 withCredentials: true,
    //             }, 
    //             success: function() {
    //                 typeof onSuccess === "function" &&
    //                 onSuccess.apply(null, arguments);
    //             },
    //             error: function() {
    //                 typeof onError === "function" &&
    //                 onError.apply(null, arguments);
    //             }
    //         });
    //     },

    //     hasData: function (res) {
    //         if (res) {
    //             return (res.comments &&
    //             res.comments.length > 0) ? true : false;
    //         } else {
    //             return false
    //         }
    //     },

    //     toggleEntry$Elem: function(flag) {
    //         $(".J-fold-comment", this.$wrap).toggle(flag);
    //     },

    //     build$Content: function(res) {
    //         res = res || {};
    //         res._MODIFIERS = _modifiers;

    //         try {
    //             var html = template.commentItem.process(res);
    //         } catch (err) {
    //             var html = "";
    //         }

    //         return $(html);
    //     },

    //     set$ContentPager: function($pager, opts) {

    //         if ($pager.length === 0) {
    //             return;
    //         }

    //         opts = $.extend({
    //             page: 0,
    //             maxPage: 1,
    //             func: function(){}
    //         }, opts);

    //         $pager.pager({
    //             total: opts['maxPage'] * 5,
    //             pageSize: 5,
    //             currentPageClass: 'ui-page-curr',
    //             currentPage: opts.page + 1,
    //             pageHref: '#none',
    //             prevClass: 'ui-pager-prev',
    //             nextClass: 'ui-pager-next',
    //             prevText: '上一页',
    //             nextText: '下一页',
    //             callback: opts.func
    //         });
    //     },

    //     render: function ($mount, data) {
    //         $mount = $mount || $('<div id="fold-comment" class="comment comment-layer">');
    //         data = data || {};

    //         var that = FoldComment;
    //         var $content = that.build$Content(data);
    //         var $pager = $(".ui-page", $content);

    //         that.set$ContentPager($pager, {
    //             page: data.page,
    //             maxPage: data.maxPage,
    //             func: onPageTurning
    //         });

    //         function onPageTurning(p) {
    //             p = p - 1;
    //             that.getData({
    //                 page: p
    //             }).done(function(res){
    //                 if (!that.hasData(res)) {
    //                     return;
    //                 }
    //                 res.page = p;
    //                 that.render($mount, res);
    //             });
    //         }

    //         return $mount.empty().append($content).prop("scrollTop", 0);
    //     },

    //     getTheFirstPage: function(OpenDialog) {
    //         var that = this;
    //         that.getData().done(function(res) {
    //             var hasData = that.hasData(res);
    //             that.toggleEntry$Elem(hasData);
    //             if (!hasData) {return;}
    //             if (OpenDialog) {
    //                 res.page = 0;
    //                 var $r = that.render(null, res);
    //                 that.dialog({source: $r});
    //             }
    //         }).fail(function() {
    //             that.toggleEntry$Elem(false);
    //         });
    //     }
    // };

    // var Report = {
    //     init: function(guid) {
    //         this.guid = guid

    //         this.get()
    //         this.bindEvent()
    //     },
    //     bindEvent: function() {
    //         this.$el = $('#jubao')

    //         this.$el
    //             .find('.J-ok')
    //             .unbind()
    //             .bind('click', $.proxy(this.handleClick, this))
    //         this.$el
    //             .find('.J-wrap-tags span')
    //             .unbind()
    //             .bind('click', $.proxy(this.handleTagClick, this))
    //         this.$el
    //             .find('textarea')
    //             .unbind()
    //             .bind('change keyup', $.proxy(this.handleChange, this))
    //     },
    //     handleChange: function(e) {
    //         var $curr = this.$el.find('.J-curr')
    //         var $this = $(e.currentTarget)
    //         var value = $this.val()
    //         $curr.text(value.length)
    //         if (value.length > 200) {
    //             $this.val(value.substr(0, 200))
    //         }
    //     },
    //     handleTagClick: function(e) {
    //         this.$el.find('.J-wrap-tags span').removeClass('selected')
    //         $(e.currentTarget).addClass('selected')
    //     },
    //     handleClick: function() {
    //         var isValidated = this.validateField()

    //         if (isValidated) {
    //             this.submit()
    //         }
    //     },
    //     submit: function() {
    //         var rid = this.$el.find('.selected').attr('data-id')
    //         var content = this.$el
    //             .find('textarea')
    //             .val()
    //             .replace(/<script>|<\/script>/gi, '')

    //         $.ajax({
    //             url:
    //                 '//like-web.jd.com/business/report/submitReportDetailForPc',
    //             data: {
    //                 businessId: this.guid,
    //                 systemId: 2,
    //                 reasonId: rid,
    //                 content: content
    //             },
    //             dataType: 'jsonp',
    //             success: $.proxy(this.handleSubmit, this)
    //         })
    //     },
    //     handleSubmit: function(r) {
    //         $.closeDialog()

    //         if (r && r.success) {
    //             $('body').dialog({
    //                 type: 'html',
    //                 title: '举报成功',
    //                 source:
    //                     '\
    //                 <div class="dialog_jubao_suc">\
    //                     <div class="icon"></div>\
    //                     <div class="tt">举报成功</div>\
    //                     <div class="def">我们会尽快处理您的反馈</div>\
    //                     <span class="btn_close" onclick="$.closeDialog()">关闭</span>\
    //                 </div>'
    //             })
    //         }
    //         if (!r.success && r.errorCode === 11) {
    //             $('body').dialog({
    //                 type: 'html',
    //                 title: '已举报过',
    //                 source:
    //                     '\
    //                 <div class="dialog_jubao_fail">\
    //                     <div class="icon"></div>\
    //                     <div class="tt">抱歉，您已举报过此评价</div>\
    //                     <div class="def">不能重复举报哦~</div>\
    //                     <span class="btn_close" onclick="$.closeDialog()">关闭</span>\
    //                 </div>'
    //             })
    //         }
    //         if (!r.success && r.errorCode === 10) {
    //             $('body').dialog({
    //                 type: 'html',
    //                 title: '举报过于频繁',
    //                 source:
    //                     '\
    //                 <div class="dialog_jubao_fail">\
    //                     <div class="icon"></div>\
    //                     <div class="tt">抱歉，您举报过于频繁</div>\
    //                     <div class="def">24 小时内最多举报3次~</div>\
    //                     <span class="btn_close" onclick="$.closeDialog()">关闭</span>\
    //                 </div>'
    //             })
    //         }
    //     },
    //     validateField: function() {
    //         if (this.$el.find('.J-wrap-tags .selected').length < 1) {
    //             this.showMessage('请在标签中选择举报原因')
    //             return false
    //         }
    //         return true
    //     },
    //     showMessage: function(txt) {
    //         $('.J-fop-tip').show().find('.J-tip-text').text(txt)
    //     },
    //     hideMessage: function() {
    //         $('.J-fop-tip').hide()
    //     },
    //     get: function() {
    //         $.ajax({
    //             url: '//like-web.jd.com/business/report/getReportReasonList',
    //             data: { systemId: 2 },
    //             dataType: 'jsonp',
    //             success: $.proxy(this.popUp, this)
    //         })
    //     },
    //     popUp: function(r) {
    //         if (!r || !r.result || !r.success) return

    //         var _this = this
    //         var tpl =
    //             '\
    //         <div id="jubao" class="jubao">\
    //             <div class="wrap-tags J-wrap-tags">\
    //                 {for item in result.reasonList}\
    //                 <span class="tag" data-id="${item.id}">${item.reason}</span>\
    //                 {/for}\
    //             </div>\
    //             <div class="f-textarea">\
    //                 <textarea name="" id="" placeholder="说明详细原因，帮助我们更快地处理举报内容（选填）"></textarea>\
    //                 <div class="textarea-ext">\
    //                     <span class="curr J-curr">0</span> / 200\
    //                 </div>\
    //             </div>\
    //             <div class="J-fop-tip fop-tip hide"><i class="tip-icon"></i><em class="J-tip-text tip-text">请在标签中选择举报原因</em></div>\
    //             <div class="btns">\
    //                 <span class="J-cancel btn_cancle" onclick="$.closeDialog()">取消</span>\
    //                 <span class="J-ok btn_sure">确定</span>\
    //                 <br class="clr">\
    //             </div>\
    //         </div>'

    //         $('body').dialog({
    //             type: 'html',
    //             mainId: 'gift-pool-popup',
    //             width: 530,
    //             title: '选择举报原因',
    //             source: tpl.process(r),
    //             onReady: function() {
    //                 _this.bindEvent()
    //             }
    //         })
    //     }
    // }

    // window.CommentNew = CommentNew

    function init(cfg) {
        // var commentList = CommentNew.init(cfg)
        // commentList.getData($('#comment-0'), 0, 0)
    }

    module.exports.__id = 'comment'
    module.exports.init = init
    // module.exports.CommentNew = CommentNew
})
