<div class="w">
    <div id="logo-2014">
        <a href="//www.jd.com/" clstag="shangpin|keycount|topitemnormal|b01" class="logo">京东</a>
        <a href="//e.jd.com/ebook.html/" target="_blank" class="ebookLogo" clstag="shangpin|keycount|product|logodianzishu">电子书</a>
    </div>
    <div id="search-2014">
        <ul id="shelper" class="hide"></ul>
        <div class="form">
            <input type="text" onkeydown="javascript:if(event.keyCode==13) search('key');" autocomplete="off" id="key" accesskey="s" class="text" clstag="shangpin|keycount|topitemnormal|b02" />
            <button onclick="search('key');return false;" class="button cw-icon" clstag="shangpin|keycount|topitemnormal|b03"><i></i>搜索</button>
        </div>
    </div>
    <div id="ebook-cart" class="dorpdown">
        <div class="cw-icon">
            <i class="ci-left"></i>
            <i class="ci-right">&gt;</i>
            <a target="_blank" clstag="shangpin|keycount|product|szspgouwuche" href="//trade.e.jd.com/cart/showCart">数字商品购物车</a>
        </div>
    </div>
    <div id="myd-2014">
        <div class="cw-icon">
            <i class="ci-left"></i>
            <i class="ci-right">&gt;</i>
            <a target="_blank" clstag="shangpin|keycount|product|wodedianzishu" href="//order.jd.com/center/list.action?t=38&d=1&s=4096">我的电子书</a>
        </div>
    </div>
    <div id="hotwords"></div>
    <span class="clr"></span>
</div>

<div id="nav-2014">
    <div class="w">
        <div class="w-spacer"></div>
        <div id="categorys-2014" class="dorpdown" data-type="default">
            <div class="dt" clstag="shangpin|keycount|topitemnormal|c01">
                <a target="_blank" href="//www.jd.com/allSort.aspx">全部商品分类</a>
            </div>
        </div>
        <div id="navitems-2014">
            <ul id="navitems-group1">
                <li class="fore1" clstag="shangpin|keycount|product|shuzishangpin" id="nav-emall">
                    <a target="_blank" href="//e.jd.com/">数字商品</a>
                </li>
                <li class="fore2" clstag="shangpin|keycount|product|dhdianzishu" id="nav-home">
                    <a target="_blank" href="//e.jd.com/ebook.html">电子书</a>
                </li>
                <li class="fore3" clstag="shangpin|keycount|product|changxiaobang" id="nav-magazine">
                    <a target="_blank" href="//e.jd.com/rank/5272-0-2-1.html">热销榜</a>
                </li>
                <li class="fore4" clstag="shangpin|keycount|product|changdu" id="nav-readcard">
                    <a target="_blank" href="//sale.jd.com/act/MypqiIJPYx.html">畅读</a>
                </li>
                <li class="fore5" clstag="shangpin|keycount|product|JDReadyueduqi" id="nav-jdread">
                    <a target="_blank" href="//jdread.jd.com">JDRead阅读器</a>
                </li>
            </ul>
            <div class="spacer"></div>
            <ul id="navitems-group2">
                <li class="fore1" clstag="shangpin|keycount|product|kehuduanxiazai" id="nav-red">
                    <a target="_blank" href="//sale.jd.com/act/W5hugLDc1R.html">客户端下载</a>
                </li>
                <li class="fore2" clstag="shangpin|keycount|product|wodechangdu" id="nav-tuan">
                    <a target="_blank" href="//cread.jd.com/readbook/readbook_myReadBookList.action">我的畅读</a>
                </li>
                <li class="fore3" clstag="shangpin|keycount|product|wodeyuedushebei" id="nav-auction">
                    <a target="_blank" href="//cread.jd.com/binddevice/binddevice_myBindDeviceList.action">我的阅读设备</a>
                </li>
                <li class="fore4" clstag="shangpin|keycount|product|goumaibangzhu" id="nav-jr">
                    <a target="_blank" href="//sale.jd.com/act/QAvop2OVgFKu4fG3.html">购买帮助</a>
                </li>
            </ul>
        </div>
        <div id="treasure"></div>
        <span class="clr"></span>
    </div>
</div><!-- #nav-2014 -->
<script>
    setTimeout(function () {
        seajs.use('//misc.360buyimg.com/jdf/1.0.0/unit/globalInit/2.0.0/globalInit', function(globalInit){
            globalInit();
        });
    }, 500);
</script>
