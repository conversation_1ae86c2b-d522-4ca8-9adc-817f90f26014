define('MOD_ROOT/gift/gift', function(require, exports, module) {
    require('MOD_ROOT/gift/gift.css');
    var G = require('MOD_ROOT/common/core');
    var Klass = G.Klass;
    var Tools = require('MOD_ROOT/common/tools/tools');

    /**
     * @class Counter
     */
    var Counter = Klass(Object, {
        /**
         * Creates an instance of Counter.
         * @param {Number|Function} min
         * @param {Number|Function} max
         * @memberof Counter
         */
        __construct: function(min, max) {
            if (min > 0 || typeof min === 'function') {
                this.min = min;
            } else {
                this.min = 1;
            }
            if (max > 0 || typeof max === 'function') {
                this.max = max;
            } else {
                this.max = 100;
            }
            this.setCount(this.getMin());
        },

        /**
         * getMax
         * @returns {Number}
         * @memberof Counter
         */
        getMax: function() {
            if (typeof this.max == 'function') {
                var max = +this.max();
                if (typeof max === 'number') {
                    return max;
                } else {
                    throw 'Max method return value is invalid.';
                }
            } else {
                return this.max;
            }
        },

        /**
         * getMin
         * @returns {Number}
         * @memberof Counter
         */
        getMin: function() {
            if (typeof this.min == 'function') {
                var min = +this.min();
                if (typeof min === 'number') {
                    return min;
                } else {
                    throw 'Min method return value is invalid.';
                }
            } else {
                return this.min;
            }
        },

        /**
         * setCount
         * @param {Number} num
         * @memberof Counter
         */
        setCount: function(num) {
            var value = +num;
            if (isNaN(value)) {
                value = this.getCount();
            }
            var min = this.getMin();
            var max = this.getMax();
            if (min > max) {
                this.count = min;
            } else {
                if (value < min) {
                    this.count = min;
                } else if (value > max) {
                    this.count = max;
                } else {
                    this.count = value;
                }
            }
        },

        /**
         * getCount
         * @returns {Number}
         * @memberof Counter
         */
        getCount: function() {
            return this.count;
        },

        /**
         * increase
         * @returns {Boolean}
         * @memberof Counter
         */
        increase: function() {
            if (this.getCount() >= this.getMax()) {
                return false;
            } else {
                this.setCount(this.count + 1);
                return true;
            }
        },

        /**
         * reduce
         * @returns {Boolean}
         * @memberof Counter
         */
        reduce: function() {
            if (this.getCount() <= this.getMin()) {
                return false;
            } else {
                this.setCount(this.count - 1);
                return true;
            }
        }
    });

    /**
     * @class Goods
     */
    var Goods = Klass(Object, {
        /**
         *Creates an instance of Goods.
        * @param {String|Object} name
        * @param {Number} count
        * @param {String} image
        * @param {Number} skuId
        * @param {Number} status
        * @param {Function} calculateGoodsPurchaseMax 计算当前商品可购买的最大数量
        * @memberof Goods
        */
        __construct: function(name, count, image, skuId, status, calculateGoodsPurchaseMax) {
            var args = arguments;
            if (typeof args['0'] == 'object') {
                this.name = args['0']['name'];
                this.count = args['0']['count'];
                this.image = args['0']['image'];
                this.skuId = args['0']['skuId'];
                this.status = args['0']['status'];
                this.calculateGoodsPurchaseMax = args['0']['calculateGoodsPurchaseMax'];
            } else {
                this.name = name;
                this.count = count;
                this.image = image;
                this.skuId = skuId;
                this.status = status;
                this.calculateGoodsPurchaseMax = calculateGoodsPurchaseMax;
            }
            if (typeof this.calculateGoodsPurchaseMax === 'function') {
                this.counter = new Counter(1, $.proxy(function(){
                    return this.calculateGoodsPurchaseMax(this);
                }, this));
            } else {
                this.counter = null;
            }
            this.isSelected = false;
        },

        select: function () {
            this.isSelected = true;
        },

        unselect: function () { 
            this.isSelected = false;
        },

        /**
         * 切换选中状态
         * @returns {Boolean}
         * @memberof Goods
         */
        toggleSelect: function() {
            if (this.isSelected) {
                return (this.isSelected = false);
            } else {
                return (this.isSelected = true);
            }
        },

        /**
         * 获取商品数量
         * @returns {Number}
         * @memberof Goods
         */
        getCount: function() {
            if (this.counter) {
                return this.counter.getCount();
            } else if (typeof this.count == 'function') {
                 return this.count();
            } else {
                return this.count;
            }
        }
    });

    /**
     * @class GoodsView
     */
    var GoodsView = Klass(Object, {

        /**
         * Creates an instance of GoodsView.
         * @param {jQuery} $el
         * @param {Goods} model
         * @memberof GoodsView
         */
        __construct: function($el, model) {
            if ($el instanceof jQuery) {
                this.$el = $el;
            } else {
                this.$el = $('<div>');
            }
            if (model instanceof Goods) {
                this.model = model;
            } else {
                throw 'Model\'s type error.';
            }
            this.template = '\
                <div class="goods J_goods {if isSelected} selected{/if}"> \
                    <div class="goods-inner-wrap"> \
                        <img class="goods-img" src="${pageConfig.FN_GetImageDomain(skuId)}n1/s60x60_${image}" alt="${name}" /> \
                        <div class="icon-selected"></div> \
                    </div> \
                    <a href="//item.jd.com/${skuId}.html" target="_blank" class="goods-name" title="${name}">${name}</a> \
                </div> \
                {if counter} \
                <div class="counter-wrap"> \
                    <div class="counter"> \
                        <div class="reduce J_reduce">-</div> \
                        <input class="J_input" type="text" value="${getCount()}"/> \
                        <div class="increase J_increase">+</div> \
                    </div> \
                </div> \
                {/if} \
            ';
            this.init();
        },

        init: function() {
            this.render();
            this.bindEvents();
        },

        render: function() {
            return this.$el.html(this.template.process(this.model));
        },

        alert: function(msg) {
            var $warn = $('#choose-gift .J_warn');
            var $text = $warn.find('.text');
            $text.html(msg);
            $warn.show();
            setTimeout(function(){
                $warn.hide();
                $text.html(msg);
            }, 1000);
        },

        bindEvents: function() {
            var that = this;
            if (that.model.counter) {
                var $goods = this.$el.find('.J_goods');
                var $input = this.$el.find('.J_input');
                var $reduce = this.$el.find('.J_reduce');
                var $increase = this.$el.find('.J_increase');

                $goods.bind('click', function () {
                    var $this = $(this);
                    if (that.model.toggleSelect()) {
                        if (
                            that.model.counter.getMax() >= 
                            that.model.counter.getCount()
                        ) {
                            $this.addClass('selected');
                        } else {
                            that.model.unselect();
                        }
                    } else {
                        $this.removeClass('selected');
                    }
                });

                that.setCount = function(value) {
                    that.model.counter.setCount(value);
                    $input.val(that.model.counter.getCount());
                    if (
                        !that.model.isSelected &&
                        (that.model.counter.getCount() <= that.model.counter.getMax())
                    ) {
                        $goods.triggerHandler('click');
                    }
                };

                $input.bind('change keydown keyup', Tools.throttle(function(){
                    that.setCount($input.val());
                }, 500));

                $reduce.bind('click', function () {
                    var $this = $(this);
                    if (that.model.counter.reduce()) {
                        $input.val(that.model.counter.getCount());
                        $this.removeClass('disabled');
                        $increase.removeClass('disabled');
                        if (!that.model.isSelected) {
                            $goods.triggerHandler('click');
                        }
                    } else {
                        $this.addClass('disabled');
                        if (
                            !that.model.isSelected &&
                            (that.model.counter.getCount() == that.model.counter.getMax())
                        ) {
                            $goods.triggerHandler('click');
                        }
                    }
                });
                
                $increase.bind('click', function () {
                    var $this = $(this);
                    if (that.model.counter.increase()) {
                        $input.val(that.model.counter.getCount());
                        $this.removeClass('disabled');
                        $reduce.removeClass('disabled');
                        if (!that.model.isSelected) {
                            $goods.triggerHandler('click');
                        }
                    } else {
                        $this.addClass('disabled');
                        if (
                            !that.model.isSelected &&
                            (that.model.counter.getCount() == that.model.counter.getMax())
                        ) {
                            $goods.triggerHandler('click');
                        }
                        if (that.model.counter.getMax() > 0) {
                            that.alert('最多可选' + that.model.counter.getMax() + '件赠品');
                        }
                    }
                });
            } else {
                var $goods = this.$el.find('.J_goods');
                $goods.bind('click', function () {
                    var $this = $(this);
                    if (that.model.toggleSelect()) {
                        $this.addClass('selected');
                    } else {
                        $this.removeClass('selected');
                    }
                });
            }
        }
    });

    /**
     * @class Pool
     */
    var Pool = Klass(Object, {
        /**
         * Creates an instance of Pool.
         * @param {String}   name
         * @param {Boolean}  type
         * @param {Object}   data 接口返回的赠品池数据
         * @param {Function} getMainProdctCount
         * @memberof Pool
         */
        __construct: function(name, type, data, getMainProdctCount) {
            this.list = [];
            this.name = name;
            this.isMultiple = type;
            this.data = data;
            this.getMainProdctCount = getMainProdctCount;
            this.add(this.buildGoodsModels(this.data));
        },

        has: function(model) {
            var list = this.list;
            for (var i = 0, size = list.length; i < size; i++) {
                if (list[i] === model) {
                    return true;
                }
            }
            return false;
        },

        add: function(model) {
            if ($.isArray(model)) {
                for (var i = 0, size = model.length; i < size; i++) {
                    this.add(model[i]);
                }
            }
            if (model instanceof Goods) {
                if (this.has(model)) {
                    return true;
                } else {
                    this.list.push(model);
                    return true;
                }
            } else {
                return false;
            }
        },

        getSelectedModels: function() {
            var arr = [];
            var list = this.list;
            for (var i = 0, size = list.length ; i < size; i++) {
                var model = list[i];
                if (model.isSelected) {
                    arr.push(model);
                }
            }
            return arr;
        },

        buildGoodsModels: function(data) {
            var arr =[];
            if ($.isArray(data)) {
                if (this.isMultiple) {
                    for (var i = 0, size = data.length; i < size; i++) {
                        var item = data[i];
                        arr.push(
                            new Goods(
                                item.Name,
                                item.Num,
                                item.ImgUrl,
                                item.Id,
                                1,
                                $.proxy(this.calculateGoodsPurchaseMax, this)
                            )
                        );
                    }
                } else {
                    for (var i = 0, size = data.length; i < size; i++) {
                        var item = data[i];
                        arr.push(
                            new Goods(
                                item.Name,
                                // item.num, 
                                this.getMainProdctCount,
                                item.ImgUrl,
                                item.Id,
                                1
                            )
                        );
                    }
                }
            }
            return arr;
        },

        calculateGoodsPurchaseMax: function(currentOperationModel) {
            if (typeof this.getMainProdctCount === 'function') {
                var max = this.getMainProdctCount();
                if (isNaN(+max)) {
                    throw 'getMainProdctCount method return value is invalid.'
                }
            } else {
                throw 'getMainProdctCount method should be a function.'
            }
            var selectedModels = this.getSelectedModels();
            var count = 0;
            for (var i = 0, size = selectedModels.length; i < size; i++) {
                if (selectedModels[i] !== currentOperationModel) {
                    count += selectedModels[i].getCount();
                }
            }
            return max - count;
        },

        getSelectedGoodsCount: function() {
            var selectedModels = this.getSelectedModels();
            var count = 0;
            for (var i = 0, size = selectedModels.length; i < size; i++) {
                count += selectedModels[i].getCount();
            }
            return count;
        }
    });

    var PoolView = Klass(Object, {
        __construct: function($el, model) {
            if ($el instanceof jQuery) {
                this.$el = $el;
            } else {
                this.$el = $('<div>');
            }
            if (model instanceof Pool) {
                this.model = model;
            } else {
                throw 'Model\'s type error.';
            }
            this.init();
        },

        init: function() {
            this.render();
            this.bindEvents();
        },
    
        render: function() {
            this.views = [];
            // 赠品池名称
            if (this.model.isMultiple) {
                this.$el.append('<div class="pool-name">'+ this.model.name + '</div>');
            } else {
                this.$el.append('<div class="pool-name">'+ this.model.name + '，可获取' + this.model.getMainProdctCount() + '件赠品' + '</div>');
            }
            var list = this.model.list;
            for (var i = 0, size = list.length; i < size; i++) {
                var view = new GoodsView($('<div class="item">'), list[i]);
                this.views.push(view);
                this.$el.append(view.$el);
            }
            return this.$el;
        },
    
        bindEvents: function() {
            var that = this;
            if (that.model.isMultiple) {
                that.$el.bind('click', function () {
                    if (
                        that.model.getSelectedGoodsCount() <
                        that.model.getMainProdctCount()
                    ) {
                        var views  = that.views;
                        for (var i = 0, size = views.length; i < size; i++) {
                            var view  = views[i];
                            view.$el.find('.J_increase').removeClass('disabled');
                            if (!view.model.isSelected) {
                                view.$el.find('.J_reduce').removeClass('disabled');
                            }
                        }
                    } else if(
                        that.model.getSelectedGoodsCount() ==
                        that.model.getMainProdctCount()
                    ){
                        var views  = that.views;
                        for (var i = 0, size = views.length; i < size; i++) {
                            var view  = views[i];
                            if (view.model.isSelected) {
                                view.$el.find('.J_increase').addClass('disabled');
                            }
                        }
                    }
                });
            } else {
                that.$el.bind('click', function (event) {
                    var target = event.target;
                    var views  = that.views;
                    for (var i = 0, size = views.length; i < size; i++) {
                        var view  = views[i];
                        if (
                            view.$el.find(target).length === 0 && 
                            view.model.isSelected
                        ) {
                            view.$el.find('.J_goods').triggerHandler('click');
                        }
                    }
                });
            }
        }
    });

    var Pools = Klass(Object, {
        /**
         * Creates an instance of Pools.
         * @param {Object} data 接口返回的数据
         * @memberof Pools
         */
        __construct: function(data, getMainProdctCount) {
            this.type = false;
            this.pools = [];
            this.allModels = [];
            this.getMainProdctCount = getMainProdctCount;
    
            var customtag = data.customtag;
            if (customtag) {
                if (customtag == 0) {
                    this.type = false;
                } 
                try {
                    customtag = $.parseJSON(customtag);
                    if (customtag.ifg == 1) {
                        this.type = true;
                    } else {
                        this.type = false;
                    }
                } catch (error) {
                    console && console.log(error);
                }
            } else {
                this.type = false;
            }
    
            var pools = data.giftPools;
            if ($.isArray(pools)) {
                for (var i = 0, size = pools.length; i < size; i++) {
                    var pool = new Pool(
                            pools[i]['name'],
                            this.type, 
                            pools[i]['gifts'],
                            getMainProdctCount
                    );
                    this.pools.push(pool);
                    this.allModels = this.allModels.concat(pool.list);
                }
            }
            this.selectedResult = null;
        },
        
        hasGift: function() {
            if (this.pools.length > 0) {
                return true;
            } else {
                return false;
            }
        },

        getSelectedResult: function() {
            return this.selectedResult;
        },

        setSelectedResult: function(data) {
            this.selectedResult = data || this.calcSelectedResult(
                this.getSelectedModels(), 
                this.getSelectedGoodsCount()
            );
        },

        getSelectedModels: function () {
            var arr = [];
            var pools = this.pools;
            for (var i = 0, size = pools.length; i < size; i++) {
                arr = arr.concat(pools[i].getSelectedModels());
            }
            return arr;
        },

        getSelectedGoodsCount: function () {
            var count = 0;
            var pools = this.pools;
            for (var i = 0, size = pools.length; i < size; i++) {
                count += pools[i].getSelectedGoodsCount();
            }
            return count;
        },
    
        getGoodsTotalCount: function() {
            // if (this.type) {
                return (this.getMainProdctCount()) * (this.pools.length);
            // } else {
                // return this.pools.length;
            // }
        },

        isFulfil: function() {
            if (
                this.getSelectedGoodsCount() ==
                this.getGoodsTotalCount()
            ) {
                return true;
            } else {
                return false;
            }
        },

        calcSelectedResult: function(selectedModels, count) {
            var dict = {
                selectedModels: selectedModels,
                count: count
            };
            var selectedModels = dict['selectedModels'];
            if (this.type) {
                var arr = [];
                for (var i = 0, size = selectedModels.length; i < size; i++) {
                    var model = selectedModels[i];
                    arr.push(model.skuId + '_' + model.getCount());
                }
                dict['giftPoolType'] = 1;
                dict['gids'] = arr.join();
            } else {
                var arr = [];
                for (var i = 0, size = selectedModels.length; i < size; i++) {
                    var model = selectedModels[i];
                    arr.push(model.skuId);
                }
                dict['giftPoolType'] = 0;
                dict['gids'] = arr.join();
            }
            return dict;
        },

        resetSelectedResultModelSatus: function() {
            var allModels = this.allModels;
            var selectedResultModels = this.selectedResult ? 
                    this.selectedResult.selectedModels : null;
            if (selectedResultModels) {
                for (var i = 0, size = allModels.length; i < size; i++) {
                    var model = allModels[i];
                    model.unselect();
                }
                if (this.selectedResult.giftPoolType) {
                    var obj = {};
                    var arr = this.selectedResult.gids.split(",");
                    for (var i = 0; i < arr.length; i++) {
                        var tmp = arr[i].split("_");
                        obj[tmp[0]] = tmp[1];
                    }
                    for (var i = 0, size = selectedResultModels.length; i < size; i++) {
                        var model = selectedResultModels[i];
                        model.select();
                        if (model.counter) {
                            model.counter.setCount(obj[model.skuId]);
                        }
                    }
                } else {
                    for (var i = 0, size = selectedResultModels.length; i < size; i++) {
                        var model = selectedResultModels[i];
                        model.select();
                    }
                }
            }
        },

        rebuildSelectedResult: function() {
            if (this.type && this.selectedResult) {
                var total = this.getGoodsTotalCount();
                var count = this.selectedResult.count;
                var models = this.selectedResult.selectedModels;
                if ( total > count) {
                    var model = models[0];
                    model.counter.setCount(model.counter.getCount() + (total - count));
                    this.setSelectedResult(this.calcSelectedResult(models, total));
                } else {
                    var arr = [];
                    var dvalue = count - total;
                    for (var i = 0, size = models.length; i < size; i++) {
                        var model = models[i];
                        var num = model.counter.getCount();
                        if (num > dvalue) {
                            model.counter.setCount(num - dvalue);
                            arr = models.slice(i);
                            break;
                        } else {
                            dvalue -= num;
                            model.unselect();
                        }
                    }
                    this.setSelectedResult(this.calcSelectedResult(arr, total));
                }
                return true;
            } else {
                return false;
            }
        }
    });
    
    var PoolsView = Klass(Object, {
    
        __construct: function($el, model, options) {
            if ($el instanceof jQuery) {
                this.$el = $el;
            } else {
                this.$el = $('<div>');
            }
            if (model instanceof Pools) {
                this.model = model;
            } else {
                throw 'Model\'s type error.';
            }
            
            if ($.isPlainObject(options)) {
                this.options = options;
            } else {
                this.options = {};
            }

            this.template = '\
            <div class="giftpool-head"> \
                <span class="icon-giftbox"></span><span class="text">搭配赠品（可选${goodsTotalCount}件）</span> \
                <div class="border-bottom"></div> \
            </div> \
            <a class="btn-modify J_modify" href="javascript:;" style="display:none;">更改</a>\
            <div class="giftpool-body" style="display:none"> \
                <div class="container"></div> \
                <div class="actionbar"> \
                    {if type}\
                    <div class="tips"> \
                        <span class="total">可选${goodsTotalCount}件</span> \
                        <span class="had">已选${selectedGoodsCount}件</span> \
                        <i class="icon-info" title="每件商品可自由选择一个赠品"></i> \
                    </div> \
                    {/if} \
                    <div class="btns"> \
                        <div class="btn-confirm J_confirm">确定</div> \
                        <div class="btn-cancel  J_cancel">取消</div> \
                    </div> \
                </div> \
                <span class="icon-close J_close"></span> \
                <div class="absoluteCenter J_warn" style="display:none;"> \
                    <div class="column"></div> \
                    <div class="warn-tips content"><span class="icon-warn"></span><span class="text"></span></div> \
                </div> \
            </div> \
            ';
            this.init();
        },
        
        init: function() {
            if (
                this.$el.length > 0 && 
                this.model.hasGift()
            ) {
                this.render();
                this.bindEvents();
            } else {
                this.$el.hide();
                this.$el.html('');
            }
        },

        render: function() {
            var $root = this.$el.find('.dd');
            $root.html(this.template.process({
                type: this.model.type,
                goodsTotalCount: this.model.getGoodsTotalCount(),
                selectedGoodsCount: this.model.getSelectedGoodsCount()
            }));
            this.$head = $('.giftpool-head', $root);
            this.$body = $('.giftpool-body', $root);
            this.$modify = $('.J_modify', $root);
            this.$container = $('.container', $root);
            // this.renderPoolView();
            this.$el.show();
        },

        renderPoolView: function() {
            this.views = [];
            this.$container.html('');
            var pools = this.model.pools;
            for (var i = 0, size = pools.length; i < size; i++) {
                var view = new PoolView($('<div class="pool">'), pools[i]);
                this.views.push(view);
                this.$container.append(view.$el);
            }
        },

        open: function() {
            this.model.resetSelectedResultModelSatus();
            this.renderPoolView();
            if (this.model.getSelectedResult()) {
                this.$body.find('.had').html('已选' + this.model.getGoodsTotalCount() + '件');
            }
            this.$head.addClass('active');
            this.$body.show();
        },

        close: function() {
            this.$body.hide();
            this.$head.removeClass('active');
        },

        toggle: function () {
            var isShow = this.$body.is(':visible');
            if (isShow) {
                this.close();
            } else {
                this.open();
            }
        },

        setHeadText: function(msg) {
            if (typeof msg === 'string') {
                this.$head.find('.text')
                .html(msg);
            }
        },
        
        setTotalText: function(msg) {
            if (typeof msg === 'string') {
                this.$body.find('.total')
                .html(msg);
            }
        },
        
        setCartButtonLink: function(params) {
            if (!$.isPlainObject(params)) {
                return;
            }
            params = {
                giftPoolType: params.giftPoolType,
                gids: params.gids
            };
            this.options.$cartButtons.each(function() {
                var $btn = $(this);
                var href = $btn.attr('href');
                if (href && href !== '#none') {
                    if (!/^https?:/.test(href)) {
                        href = location.protocol + href;
                    }
                    $btn.attr('href', G.modifyURL(href, {
                        query: params
                    }, true));
                }
            });
        },

        bindEvents: function() {
            var that = this;
            var __html = '\
                <div class="notice"> \
                    <div class="icon-question"></div> \
                    <div class="notice-text">还差{0}件赠品未选</div> \
                    <div class="notice-ps">系统将自动帮您挑选</div> \
                </div> \
            ';

            that.$head.bind('click', function() {
                that.toggle();
            });

            that.$modify.bind('click', function() {
                that.toggle();
            });

            $('.J_close', that.$body).bind('click', function() {
                that.close();
            });

            $('.J_confirm', that.$body).bind('click', function () {
                that.close();
                if (that.model.isFulfil()) {
                    that.model.setSelectedResult();
                    that.setCartButtonLink(that.model.getSelectedResult());
                    that.$el.removeClass('item-hl-bg');
                    that.setHeadText('已选' + that.model.getGoodsTotalCount() + '件赠品');
                    that.$body.find('.had').html('已选' + that.model.getGoodsTotalCount() + '件');
                    that.$modify.show();
                } else {
                    var count = that.model.getGoodsTotalCount() - that.model.getSelectedGoodsCount();
                    $('body').dialog({
                        width: 420,
                        type: "html",
                        extendMainClass: 'gift-pool',
                        source: __html.format(count),
                        hasButton: true,
                        submitButton: '好的',
                        cancelButton: '自己选',
                        onReady: function() {
                            var that = this;
                            setTimeout(function(){
                                that.el.find(".ui-dialog-close")
                                .unbind('click').bind("click", function () {
                                    that.el.remove();
                                    that.mask.remove()
                                });
                            }, 0);
                        },
                        onSubmit: function() {
                            var views = that.views;
                            if (that.model.type) {
                                var goodsView = views[0]['views'][0];
                                goodsView.setCount(count + goodsView.model.getCount());
                            } else {
                                for (var i = 0, size = views.length; i < size; i++) {
                                    var view = views[i];
                                    if (
                                        (view.model.getSelectedModels()).length == 0
                                    ) {
                                        view.views[0].$el.find('.J_goods').trigger('click');
                                    }
                                }
                            }
                            if (that.model.isFulfil()) {
                                that.model.setSelectedResult();
                                that.setCartButtonLink(that.model.getSelectedResult());
                                that.$el.removeClass('item-hl-bg');
                                that.setHeadText('已选' + that.model.getGoodsTotalCount() + '件赠品');
                                that.$body.find('.had').html('已选' + that.model.getGoodsTotalCount() + '件');
                                that.$modify.show();
                            }

                            var $mask = $('.ui-mask');
                            var $dialog = $(this).closest('.ui-dialog');
                            $dialog.remove(); $mask.remove();
                        },
                        onCancel: function() {
                            that.open();
                        }
                    });
                }
            });

            $('.J_cancel', that.$body).bind('click', function() {
                that.close();
            });

            if (that.model.type) {
                var $had  = $('.had', that.$body);
                that.$container.bind('click', function () {
                    var count = that.model.getSelectedGoodsCount();
                    $had.html('已选' + count + '件');
                });
                $('.icon-info', that.$body).ETooltips({pos: 'top', zIndex: 10});

                // 可多选的赠品池屏蔽“白条”、“一键购”
                // that.options.$block.remove();
            } 
        },
        // 数量变化后重新绘制部分UI
        repaint: function () {
            var count = this.model.getGoodsTotalCount();
            this.renderPoolView();
            if (this.model.getSelectedResult()) {
                this.setHeadText('已选' + count + '件赠品');
                this.$body.find('.had').html('已选' + this.model.getGoodsTotalCount() + '件');
                this.setCartButtonLink(this.model.getSelectedResult());
            } else {
                this.setHeadText('搭配赠品（可选' + count + '件）');
            }
            this.setTotalText('可选' + count +  '件');
        }
    });


    var GiftPool = {
        $block: $('#choose-baitiao, #btn-onkeybuy'),
        $cartButtons: $('#btn-reservation, #btn-reservation-mini, #InitCartUrl, #InitCartUrl-mini'),
        mainProductCount: $('.choose-amount .buy-num').val(), // 用来判定是如何触发的促销接口的
        init: function(data) {
            var giftPool = data && data.giftPool3C;

            if (this.mainProductCount == this.getMainProdctCount()) {
                if ($.isPlainObject(giftPool)) {
                    this.model = new Pools(giftPool, this.getMainProdctCount);
                    this.view  = new PoolsView($('#choose-gift'), this.model, {
                        $block: this.$block,
                        $cartButtons: this.$cartButtons
                    });
                } else {
                    this.model = null;
                    this.view  = null;
                    $('#choose-gift').hide();
                }
            } else {
                this.mainProductCount = this.getMainProdctCount();
                if (this.model && this.view) {
                    this.model.rebuildSelectedResult();
                    this.view.repaint();
                }
            }
        },
        getMainProdctCount: function() {
            return Number($('.choose-amount .buy-num').val());
        },
        bindEvents: function() {
            var that = this;
            that.$cartButtons.bind('click', function(){ // 未选择赠品，点击加入购物车会红背景提示
                if (that.model &&
                    !that.model.getSelectedResult() ) {
                    that.view.$el.addClass('item-hl-bg');
                    return false;
                }
            }); 
            $("#InitTradeUrl").bind('click', function(){ // 未选择赠品，点击立即购买会红背景提示
                if (that.model &&
                    !that.model.getSelectedResult() ) {
                    that.view.$el.addClass('item-hl-bg');
                    return false;
                }
            }); 
        }
    };

    GiftPool.bindEvents();

    module.exports = GiftPool;
});
