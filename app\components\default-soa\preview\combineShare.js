define('MOD_ROOT/preview/combineShare', function(require, exports, module) {
    var dialog = require('JDF_UI/dialog/1.0.0/dialog');
    var login  = require('JDF_UNIT/login/1.0.0/login');

    require('MOD_ROOT/preview/combineShare.css');

    /**
     * 联合分享组件
     * 登录jd.com情况下可用
     */
    function CombineShare(opts) {
        this.$el          = opts.$el;
        this.imgs         = typeof opts.imgs === 'string' ? [opts.imgs] : opts.imgs;
        this.onbeforeOpen = opts.onbeforeOpen || function() {};

        // 限制title字数为30个字符
        if ( opts.title.length > 27 ) {
            opts.title = opts.title.substr(0, 27) + '...';
        }

        this.shareParam = {
            // 分享所在页面系统的id，如评价系统为1, 晒单为2，单品页可以为3,范围在0-127
            sid: opts.sid,
            // 参照id: 如商品的id、活动的id等,必须为整数
            rid: opts.rid,
            title: opts.title,
            content: opts.content,
            imgUrl: this.imgs[0],
            url: opts.url
        };

        //1:qq空间 2:新浪微博 3.腾讯微博 4.人人网 5.开心网 6.豆瓣网;
        this.shareMap = {
            //T1: {
                //name: 'qqZone',
                //text: 'QQ空间',
                //url: '//share.jd.com/qzone/login.action'
            //},
            T2: {
                name: 'sina',
                text: '新浪微博',
                url: '//share.jd.com/sina/login.action'
            },
            T3: {
                name: 'qqWeibo',
                text: '腾讯微博',
                url: '//share.jd.com/qqweibo/login.action'
            },
            T4: {
                name: 'renren',
                text: '人人网',
                url: '//share.jd.com/renren/login.action'
            },
            T5: {
                name: 'kaixin',
                text: '开心网',
                url: '//share.jd.com/kaixin001/login.action'
            },
            T6: {
                name: 'douban',
                text: '豆瓣网',
                url: '//share.jd.com/douban/login.action'
            }
        };

        this.init();

        return this;
    }
    CombineShare.prototype = {
        init: function() {
            this.bindEvent();
        },
        popUp: function () {
            var _this = this;
            login({
                modal: true,//false跳转,true显示登录注册弹层
                complete: function() {
                    _this.getThickBox(1);
                }
            });
        },
        getThickBox: function(n, ext) {
            var _this = this;
            var TPL = {};

            TPL['T1'] = ''
                +'<div id="cb-share1" class="combine-share">'
                +'    <div class="mt">'
                +'        <dl>'
                +'            <dt class="fl">分享到：</dt>'
                +'            <dd class="lh">{SHARELIST}</dd>'
                +'        </dl>'
                +'    </div>'
                +'    <div class="mc">'
                +'        <textarea id="share-text" name="" cols="30" rows="10" placeholder="说点什么吧...">{TEXT}</textarea>'
                +'        <div class="share-meta clearfix">'
                +'            <div class="share-link fl"><a class="s-btn s-btn-gray" href="{LINK}" target="_blank"><b class="s-btn"></b>分享链接</a></div>'
                +'            <div class="share-count lh ar">还可以输入<em>100</em>字</div>'
                +'        </div>'
                +'        {IMGWRAP}'
                +'    </div>'
                +'    <div class="mb">'
                +'        <div class="share-btn">'
                +'            <a href="#none" class="s-btn s-btn-ok share-btn-ok">分享</a>'
                +'        </div>'
                +'    </div>'
                +'</div>';

            TPL['T2'] = ''
                +'<div id="cb-share2" class="combine-share share-msg">'
                +'    <dl>'
                +'        <dd class="ld"><h5 class="ac"><strong><s class="share-ico"></s>成功分享到 {EXT}</strong></h5></dd>'
                +'    </dl>'
                +'</div>';

            TPL['T3'] = ''
                +'<div id="cb-share3" class="combine-share share-msg share-warning">'
                +'    <dl>'
                +'        <dt class="fl share-ico"></dt>'
                +'        <dd class="ld"><h5>分享失败，请重新操作。</h5></dd>'
                +'    </dl>'
                +'    <div class="share-btn"><a href="#none" class="s-btn s-btn-gray share-btn-fail">确定</a></div>'
                +'</div>';

            TPL['T4'] = ''
                +'<div id="cb-share4" class="combine-share share-msg share-warning">'
                +'    <dl>'
                +'        <dt class="fl share-ico"></dt>'
                +'        <dd class="ld"><h5>字数超出限制请重新输入</h5></dd>'
                +'    </dl>'
                +'</div>';

            //TPL['T5'] = ''
                //+'<div id="cb-share5" class="combine-share share-warning">'
                //+'    <dl>'
                //+'        <dt class="fl share-ico"></dt>'
                //+'        <dd class="ld"><h5>请先绑定分享平台账号再进行分享</h5></dd>'
                //+'    </dl>'
                //+'    <div class="share-btn"><a href="#none" class="s-btn s-btn-gray share-btn-rebind">确定</a></div>'
                //+'</div>';

            TPL['IMG'] = ''
                +'<p>选择配图：</p>'
                +'<div class="share-imgScroll">'
                +'    <a id="share-img-prev" class="share-controls s-btn s-btn-gray" href="#none"><s></s></a>'
                +'    <a id="share-img-next" class="share-controls s-btn s-btn-gray" href="#none"><s></s></a>'
                +'    <div class="share-img-wrap">'
                +'        <ul class="lh">{IMGS}</ul>'
                +'    </div>'
                +'</div>'

            function generateImgListHtml() {
                var imgs = _this.imgs;
                var result = '';

                if ( imgs.length < 1 ) {
                    return false;
                }

                for (var i = 0; i < imgs.length; i++) {
                    result += ('<li class="'+ (i===0?'selected':'') +'"><a href="#none"><img src="'+ imgs[i].replace(/\/n\d/, '/n4') +'" alt="" /><s></s></a></li>');
                }

                return result;
            }
            function generateShareListHTML() {
                var list = _this.shareMap;
                var result = '';

                for ( var k in list ) {
                    result += '<label title="'+ list[k].text +'" for="share-'+ list[k].name +'"><input value="'+ k.replace('T', '') +'" id="share-'+ list[k].name +'" type="checkbox"><em id="share-ico-'+ list[k].name +'">'+ list[k].text +'</em></label>';
                }
                return result;
            }

            // 打开窗口之前调用，用于异步获取价格接口
            if ( this.onbeforeOpen ) {
                this.onbeforeOpen(this);
            }

            var resImgList= generateImgListHtml();
            var resShareList= generateShareListHTML();
            var resTPL = '';

            if ( resImgList ) {
                resTPL = TPL['T'+n].replace('{IMGWRAP}', TPL['IMG']).replace('{IMGS}', generateImgListHtml());
            } else {
                resTPL = TPL['T'+n].replace('{IMGWRAP}', '');
            }

            resTPL = resTPL.replace('{LINK}', this.shareParam.url);
            resTPL = resTPL.replace('{SHARELIST}', resShareList);

            if ( this.textNumOverflow ) {
                this.shareParam.content = this.cacheText;
            }
            if ( this.jp ) {
                this.shareParam.content = this.shareParam.content.replace('{PRICE}', this.jp);
            }
            resTPL = resTPL.replace('{TEXT}', this.shareParam.content);

            if ( typeof ext !== 'undefined' ) {
                resTPL = resTPL.replace('{EXT}', ext);
            }

            if ( n === 1 ) {
                this.dialog1 = $('body').dialog({
                    width: 540,
                    height: resImgList ? 340 : 200,
                    title: '分享给好友',
                    type:'html',
                    source: resTPL,
                    onReady: function() {
                        // 取到当前用户分享绑定状态
                        _this.wrap = $('#cb-share'+n);
                        _this.textarea = _this.wrap.find('textarea');
                        _this.getBindStatus();
                        _this.updateCount( _this.textarea );
                        if ( resImgList ) {
                            _this.initScroll();
                        }
                    }
                });
            }
            if ( n === 2 ) {
                $('body').dialog({
                    title: '分享给好友',
                    width: 520,
                    height: 100,
                    source: resTPL,
                    hasButton: true
                });
            }
            if ( n === 3 ) {
                $('body').dialog({
                    title: '分享给好友',
                    width: 400,
                    height: 100,
                    source: resTPL
                });
            }
            if ( n === 4 ) {
                $('body').dialog({
                    title: '提示',
                    width: 400,
                    height: 70,
                    source: resTPL,
                    hasButton: true
                });
            }
            //if ( n === 5 ) {
                //$('body').dialog({
                    //title: '账号绑定',
                    //width: 400,
                    //height: 100,
                    //source: resTPL
                //});
            //}


        },
        bindEvent: function() {
            var _this = this;
            var $body = $('body');

            $body.delegate('#cb-share1 .share-img-wrap li', 'click', function() {
                $('#cb-share1 .share-img-wrap li').removeClass('selected');
                $(this).addClass('selected');
            });

            $body.delegate('#cb-share1 .mt input', 'click', function(e) {
                var val = $(this).val();

                if ( !$(this).next().hasClass('actived') ) {
                    $(this).next().addClass('actived');
                    window.open(_this.shareMap['T' + val].url);
                }
            });
            $body.delegate('#cb-share1 .mc textarea', 'keyup change', function(e) {
                _this.updateCount( $(this) );
                _this.cacheText = $(this).val();
            });

            $body.delegate('#cb-share1 .share-btn-ok', 'click', function() {
                var textarea =  $('#share-text');
                var isOverflow = $('#cb-share1 .share-count').hasClass('overflow');

                if ( isOverflow ) {
                    _this.getThickBox(4);
                    _this.textNumOverflow = true;
                } else {
                    _this.dialog1.close();
                    _this.goShare();
                    _this.textNumOverflow = false;
                }
            });
        },
        initScroll: function() {
            seajs.use('product/js/2013/jQuery.imgScroll-lite-min', function() {
                var scroll = $('#cb-share1 .share-img-wrap');
                var prev = $('#share-img-prev');
                var next = $('#share-img-next');
                var imgItems = scroll.find('li');

                scroll.imgScroll({
                    visible: 4,
                    step: 4,
                    prev: prev,
                    next: next,
                    showControl: true
                });
            });
        },
        updateCount: function($el) {
            $el          = $el || this.textarea;
            var val      = $el.val();
            var len      = val.replace(/[\u4e00-\u9fa5]/g, 'jd').length;
            var num      = this.wrap.find('.share-count');
            var target   = num.find('em');
            var maxCount = 100;

            if ( maxCount - Math.ceil(len/2) < 0 ) {
                target.html('0');
            } else {
                target.html( ( maxCount - Math.ceil(len/2) ) + '' );
            }
            if ( Math.ceil(len/2) > maxCount ) {
                num.addClass('overflow');
            } else {
                num.removeClass('overflow');
            }
        },
        getBindStatus: function() {
            var _this = this;
            $.ajax({
                url: '//share.jd.com/share/getBindStatus.action',
                dataType: 'jsonp',
                success: function(r) {
                    if ( r && r.length ) {
                        _this.setBindStatus(r);
                    }
                }
            });
        },
        setBindStatus: function(r) {
            var name, status, type;

            for (var i = 0; i < r.length; i++) {
                name   = r[i].name;
                status = r[i].status;
                type   = r[i].type;

                if ( status === 1 ) {
                    this.wrap.find('#share-ico-' + name).addClass('actived');
                    this.wrap.find('#share-' + name).attr('checked', true);
                }
            }
        },
        goShare: function() {
            var _this  = this;
            var inputs = this.wrap.find('dd input:checked');
            var imgUrl = this.wrap.find('.share-img-wrap .selected img').attr('src');
            var text   = this.wrap.find('#share-text').val();
            var type   = [];
            var param  = this.shareParam;

            param.content = text;
            param.imgUrl = imgUrl;

            inputs.each(function() {
                var val =  $(this).val();

                if ( val ) {
                    type.push( val );
                }
            });

            if ( type.length ) {
                param.type = type.join(',');

                $.ajax({
                    url: '//share.jd.com/share/shareItem.action',
                    dataType: 'jsonp',
                    data: param,
                    success: function(r) {
                        var types = [];
                        var resName = [];

                        if ( r && typeof r.code !== 'undefined' ) {
                            if ( r.code === -1 ) {
                                _this.getThickBox(3);
                            }
                            if ( r.code === 0 ) {
                                types = r.status.split(',');

                                for (var i = 0; i < types.length; i++) {
                                    resName.push( _this.shareMap['T' + types[i]].text );
                                }

                                _this.getThickBox(2, resName.join(','));
                            }
                        }
                    }
                });
            }
        }
    };

    return CombineShare;
});
