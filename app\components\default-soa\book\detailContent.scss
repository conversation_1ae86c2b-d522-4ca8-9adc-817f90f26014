@import "ui-lib.scss";

#product-detail{
    // .mc{
    //  // 因自营图书的内页插图hover时的大图被截取，故overflow改为visible
    //  overflow: visible;
    // }
}

#J-detail-banner{
    width: 100%;
    // margin-top: 10px;
    img{
        width: 100%;
        height: auto;
    }
}

/**
 * 商品详情顶部fixed导航
 */
#pro-detail-hd{
    width: 770px;
    position: relative;
    overflow: visible;
    background-color: #fff;
    // 加z-index解决ie67下二维码下拉框被遮挡问题
    z-index:2;
    &.pro-detail-hd-fixed{
        position: fixed;
        top: 0;
        z-index: 30;
    }
}

/*nav mini cart*/
/* #product-detail .mt { overflow:visible; }
#product-detail .mc { clear:both; } */
#nav-minicart {
    width:110px;
    position:absolute;
    z-index:2;
    right:1px;
    top:1px;
    .err-price {
        height:12px;
        width:auto;
    }
    .nav-minicart-inner{
        font-size:12px;
        width:110px;
        margin:-1px -1px 0 0;
        padding:1px;
    }
    .nav-minicart-btn {
        overflow:hidden;
        a {
            float:right;
            display:inline;
            width:87px;
            height: 24px;
            line-height: 24px;
            background-position:0 -46px;
            overflow:hidden;
            margin:7px 10px 0 0;
            cursor:pointer;
            text-align: center;
            color:#fff;
            background-image: none;
            background-color: #e4393c;
        }
    }

    .nav-minicart-buynow a,
    .nav-minicart-buynow a:hover{
        width:77px;
        height:25px;
        margin-top:2px;
        line-height:25px;
        text-align:center;
        background-position:-166px -112px;
        font-weight:normal;
    }

    .nav-minicart-con {
        padding:10px 0;
        clear:both;
        width:227px;
        overflow: hidden;
        display:none;
    }
    .p-img {
        float:left;
        margin:0 10px;
        padding:0;
    }
    .p-name {
        overflow:hidden;
        line-height:1.5em;
        height:4.5em;
        margin-bottom:10px;
        color:#333;
        font-weight:normal;
    }
    .p-price{
        overflow:hidden;
        line-height:1.2em;
        font-weight:bold;
        color:#e4393c;
        em{
            color:#999;
            font-weight:normal;
        }
    }

    &.hover,&:hover{
        width:229px;
        .nav-minicart-inner{
            width: 229px;
            border:1px solid #ddd;
            padding:0;
            background:#fff;
            @include box-shadow(0 0 5px #ddd);
        }
        .nav-minicart-con{
            display: block;
        }
    }

}



.detail-content{
    position: relative;
    // margin-top:10px;
    // margin-bottom:10px;
    // border:1px solid #ddd;
    background-color:#f7f7f7;
}

/* 限制pop图书内容区边距 */
.POP-3 #J-detail-content{
    // padding: 5px;
}
.detail-content-wrap{
    width: 100%;
    float:left;
    background-color:#fff;

    .detail-content-item{
        width: 100%;
    }

    .content_tpl{
        width: 750px;
        margin: 0 auto;
    }
}
.detail-content-nav{
    display: none;
    position: absolute;
    right:0;
    top: 0;
    width:157px;

    .detail-update{
        display: block;
        position: relative;
        padding-left: 36px;
        width: 62px;
        height: 32px;
        line-height: 32px;
        color: #fff;
        background: #6e9cd0;
        margin: 10px auto 0;

        i{
            position: absolute;
            width: 20px;
            height: 20px;
            background: url(i/item.sprite.png) no-repeat -30px -48px;
            display: block;
            left: 8px;
            top: 5px;
        }

        &:hover{
            text-decoration: none;
            background: #5289c7;

            i{
                background-position: 0 -48px;
            }
        }
    }

    .book-qrcode{
        padding-top:10px;
        .imgbox{
            border: 1px solid #ddd;
            width: 98px;
            height: 98px;
            margin: 0 auto;
            img{
                width: 98px;
                height: 98px;
            }
        }
        .text{
            padding-top: 5px;
            text-align: center;
        }
    }

    &.fixed-top {
        position: fixed;
        top: 36px;
        right:50%;
        margin-right: -605px;
    }

    .detail-content-tab{
        float:left;font-size:14px;
        li{
            position:relative;
            margin-top: 16px;
            padding:0 0 0 33px;
            i{display:none;}
        }
        .current{
            background-color: #f7f7f7;
            a{color: #e4393c;}
            i{display:block;}
            .arrow-l{
                width:16px;
                height:16px;
                position:absolute;
                left:-13px;
                _left:-49px;
                top:2px;
                background: none;
                b{
                    position: absolute;
                    width: 0;
                    height: 0;
                    border-width: 8px;
                    border-style: dashed solid dashed dashed;
                    overflow: hidden;
                    &.layer1{
                        left: -1px;
                        border-color: transparent #ccc transparent transparent;
                    }
                    &.layer2{
                        border-color: transparent #f7f7f7 transparent transparent;
                    }
                }
            }
            .arrow-r{
                background:url(i/arrowTab.png) no-repeat;
                width:13px;
                height:13px;
                position:absolute;
                left:15px;
                _left:-18px;
                top:2px;
            }
        }
    }
}

// 售后保障延保
.yb-tab-img a {
  color: #005aa0;
  display: inline-block;
  *zoom: 1;
  padding: 10px 0;
}

/**
 *
 */
.root61{
    .z-have-detail-nav{
        padding-right:157px;
        .detail-content-nav{
            display: block;
        }
        .detail-content-wrap{
            width: 830px;
            border-right: 1px solid #ddd;
            .content_tpl{
                width: 753px;
            }
        }
    }
}


#state{
    padding-top:8px; margin-top:8px; overflow:hidden;zoom:1;
    strong{color:#e4393c;}
}
.serve-agree-bd{
    padding:20px 20px 20px 62px;
    //border-top:1px solid #ddd;
    dt{
        display:block;
        height:32px;
        line-height:32px;
        margin-left: -42px;
        font-size:16px;
        color: #e4393c;
        font-family: $font-yahei;
        i{ @include inline-block(); width:32px;height:32px;line-height:32px;padding-right:10px;vertical-align:bottom;}
        .goods{background:url(i/iconZP.png) no-repeat;}
        .unprofor{background:url(i/iconLB.png) no-repeat;}
        .no-worries{background:url(i/iconWY.png) no-repeat;}
    }
    dd{
        display:block;
        padding:10px 0 30px;
        line-height: 18px;
        a{
            color: $color03;
        }
    }
    .no-worries-text{padding-bottom:0px;}
}
/*p-parameter*/
.right .p-parameter{border:solid 1px #dedfde;border-top:none;}
.p-parameter{padding:0 10px 10px;}
.p-parameter .more-par{padding-right:20px;margin-top:-5px;text-align: right;}
.p-parameter .more-par a{color: #015eab;}

.p-parameter-list{
    padding: 20px 0 15px;
    border-bottom: 1px dotted #ddd;
    margin-top: -1px;
    overflow:hidden;
    _zoom:1;
    li{
        width: 145px;
        padding-left: 42px;
        float:left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        i{
            float: left;
            width: 32px;
            display: inline;
            margin-top: 2px;
            margin-left: -37px;
            background:url(i/item.icons.png) no-repeat;
            &.i-phone{
                height:31px;
                background-position: -50px 0;
            }
            &.i-camera{
                height:27px;background-position: -100px 0;
            }
            &.i-cpu{
                height:25px;background-position: -150px 0;
            }
            &.i-network{
                height:22px;background-position: -200px 0;
            }
        }
        a{
            color: $color03;
        }
        .detail{
            width: 142px;
            p{
                width: 100%;
                line-height: 18px;
                margin-bottom: 4px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}

#parameter2{
    border-bottom: none;
    li{
        line-height: 22px;
    }
}

#parameter3{
    li{
        line-height: 22px;
        padding-left: 10px;
    }
}

#nav-jdapp{
    position: absolute;
    right:110px;
    top:0;
    width:140px;
    height: 30px;
    line-height: 30px;
    z-index: 1;

    i{
        background-image: url("//misc.360buyimg.com/lib/skin/2013/i/20130330A_2.png");
        background-position: -128px -360px;
        background-repeat: no-repeat;
        height: 22px;
        right: 20px;
        position: absolute;
        top: 5px;
        @include transition(transform 0.2s ease 0s);
        width: 13px;
        z-index: 2;
    }
    .dt{
        padding-left: 10px;
        padding-right:25px;
        height: 30px;

        a{
            display: block;
            width: 95px;
            height: 30px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        b {
            position: absolute;
            display: inline-block;
            background-image: url("//misc.360buyimg.com/lib/skin/2013/i/20130330A_2.png");
            background-repeat: no-repeat;
            background-position: -95px -55px;
            height: 4px;
            right: 5px;
            top: 13px;
            width: 7px;
            @include transition(transform 0.2s ease-in 0s);
        }
    }
    .dd{
        display: none;
        top: 30px;
        left:0;
        border:solid 1px #ddd;
        border-top:none;
        width: 145px;
        height: 145px;
        padding: 13px 14px 13px 15px;
        background:#fff;
    }

    &:hover,&.hover{
        width: 176px;
        margin-left: 0%;
        .dt{
            //width: 144px;
            border: 1px solid #ddd;
            border-bottom: medium none;
            background-color: #fff;
            @include box-shadow(1px -1px 1px rgba(0,0,0,0.1),-1px -1px 1px rgba(0,0,0,0.1));
            a{
                width: 125px;
            }
        }
        .dd{
            display: block;
            @include box-shadow(1px 1px 1px rgba(0,0,0,0.1),-1px 1px 1px rgba(0,0,0,0.1));
        }
        i{
            background-position: -128px -399px;
        }

        b{
            transform: rotate(180deg);
        }
    }
}

.root61{
    #pro-detail-hd{
        width: 990px;
    }
    #nav-jdapp{
        right:140px;
        width: 176px;
        .dt{
            a{
                width: 120px;
            }
        }
    }
    .p-parameter{padding:0 10px 10px;}
    .p-parameter-list{
        padding: 20px 0 15px;
        li{
            width: 200px;
            .detail{
                width: 197px;
            }
        }
    }

}

// 图书商品详情样式

.book-detail-item:nth-child(1) {padding-top: 0;}
.book-detail-item{overflow:hidden;padding-top: 20px;}
.book-detail-item .more { padding: 10px 0 0 10px; }
.book-detail-item .more a { color:#005aa0;}
#detail-root-9{overflow:visible}
.book-detail-item .item-mt{height:24px;border-bottom:3px solid $color01;}
.book-detail-item .item-mt h3{line-height:28px;width:99px;height:25px;overflow:hidden;color:#FFF;padding-left:20px;background:url(i/item.sprite.png) no-repeat 0 -13px}
.book-detail-item .item-mc{padding:18px 0 0;}
.book-detail-item .item-mc .more{padding:5px 25px 0;line-height:30px;font-family:simsun}
.book-detail-item .item-mc .more a{color:#005eaa}
.detail-item-sub .sub-mt{height:23px;background:url(i/item.sprite.png) repeat-x 0 20px}
.detail-item-sub .sub-mt h4{border-bottom:3px solid #b5915f;display:inline-block;*display:inline;*zoom:1;height:20px;line-height:20px;padding:0 10px}
.detail-item-sub .sub-mc{padding:20px 0}
.book-detail-item .book-detail-content{line-height:24px;font-size: 14px;}
.book-detail-content a{color:#005aa0}

.book-detail-item .illustrated li {
  border:1px solid #fff;
  float: left;
  width: 150px;
  height: 160px;
  overflow: hidden;
  margin: 0px 18px 18px 0;
  padding: 0 11px 20px;
}
.book-detail-item .illustrated li img {
  cursor:url(../../css/i/big.cur), auto;
}
.book-detail-item .illustrated li:hover {
  border: 1px solid #ccc;
}
.book-detail-item .illustrated .img-con{position:relative;width:132px;height:160px;zoom:1}
.book-detail-item .illustrated .img-con a{cursor:default}
.book-detail-item .illustrated .img-con .img-box{
    position:absolute;
    z-index:2;
    left:130px;
    top:0;
    overflow:hidden;
    border:1px solid #ccc;
    box-shadow:0 0 5px #999;
    background:#fff;
    width:480px;
    height:580px;
    display: none;
}
.book-detail-item .illustrated .hover{z-index:1}
.book-detail-item .illustrated .hover a{z-index:0}
.book-detail-item .illustrated .hover .img-box{display: block;}
.book-detail-item .illustrated-old{padding:20px 0 0 16px;line-height:18px}
.book-detail-item .illustrated-old img{display:block;margin-bottom:10px}
.book-detail-item .illustrated .illustrated-right .img-box{left:-388px}

// 图书-在线试读
.book-detail-item{

    .list-works{
        width: 100%;
        overflow: hidden;
        padding-top: 20px;

        li{
            float:left;
            margin-right: 10px;
            margin-bottom:10px;

            .chapter-item{
                padding: 0 15px;
                font-family: $font-st;
            }
            .btn-onlineread{
                width:80px;
            }
        }
    }
}

/* 品质生活 */
.quality-life {
  margin-top: 10px;
  border: 1px solid #e3dddd;
  @include clearfix;
  .q-logo {
    float: left;
    width: 250px;
  }
  ul {
    float: left;
  }
  li {
    float: left;
    display: inline;
    width: 100px;
    text-align: center;
    padding-top: 5px;
    margin-right: 20px;
    position: relative;
    span {
      display:block;
    }
    .fresh-wd {
        position: absolute;
        top: 20px;
        left: 0;
        width: 100%;
        text-align: center;
        color: #656EAE;
    }
      .fresh-ico-1 {
          cursor: default;
      }
  }
  i {
    @include inline-block;
    width: 45px;
    height: 45px;
    background: url(i/quality-life2.png) 0 0 no-repeat;
  }
  .ql-ico-3 i { background-position: 0 0; }
  .ql-ico-2 i { background-position: 0 -150px; }
  .ql-ico-5 i { background-position: 0 -50px; }
  .ql-ico-1 i { background-position: 0 -100px; }
  .ql-ico-4 i { background-position: 0 -200px; }
  .ql-ico-10 i { background-position: 0 -300px; }
  .ql-ico-yuan i { background-position: 0 -350px; }
  .fresh-ico-1 i { background-position: 0 -401px; }
  .fresh-ico-2 i { background-position: 0 -200px; }
}
.root61 .quality-life li {
  margin-right: 80px;
}

// 关注品牌
#parameter-brand {
    padding-bottom: 0;
    border-bottom: none;
    li {
        width: 50%;
    }
}
#parameter-brand {
    li {
        width: 50%;
    }
}
.follow-brand {
    margin-left: 5px;
    .p-parameter-list & {
        color: #666;
    }
    b {
        color: #e4393c;
        font-size: 22px;
        font-family: simsun;
        vertical-align: middle;
        padding-right: 3px;
    }
}

.service-rule{
    padding: 10px 20px;
    line-height: 160%;
}
.service-rule h3{
    font-size: 14px;
    color: #e4393c;
    padding-bottom: 10px;
}
.service-rule p{
    text-indent: 2em;
}
.service-rule .rule-con{
    padding-top: 8px;
}
.service-rule .rule-con h4{
    color: #e4393c;
}
.service-rule .rule-con ol{
    padding-top: 4px;
}
.service-rule .rule-con li{
    list-style-type: decimal;
    list-style-position: inside;
    padding-left: 10px;
}
.service-rule .rule-con strong{
    display: block;
    font-weight: normal;
    margin-bottom: 8px;
    padding-left: 10px;
}
.service-rule .rule-con strong span{
    font-weight: bold;
}

.detail-video-con{
    padding: 20px 0;
    .video-wrap{
        width: 750px;
        height: 422px;
        margin: 0 auto;
        position: relative;
    }
    .video-player{
        width: 750px;
        height: 422px;
    }
    .play-btn {
        width: 42px;
        height: 42px;
        background: url(i/__sprite.png) -42px -0px;
        position: absolute;
        left: 20px;
        bottom: 20px;
    }
    .video-list{
        position: relative;
        width: 750px;
        height: 144px;
        overflow: hidden;
        zoom: 1;
        margin: 7px auto 0 auto;
        .ui-switchable-panel-main {
            position: relative;
            width: 756px;
            height: 144px;
            overflow: hidden;
        }
        .list-prev,.list-next{
            position: absolute;
            z-index: 5;
            top: 0;
            bottom: 32px;
            width: 24px;
            padding-top: 47px;
            background: rgba(0,0,0,.5);
            i{
                display: block;
                margin: 0 auto;
            }
            .sprite-prev{
                width: 9px;
                height: 17px;
                background-image: url(i/__sprite.png);
                background-position: -9px -42px;
            }
            .sprite-next{
                width: 9px;
                height: 17px;
                background-image: url(i/__sprite.png);
                background-position: -0px -42px;
            }
            &.disabled {
                display: none;
            }
        }
        .list-prev{
            left: 0;
        }
        .list-next{
            right: 7px;
        }
        //ul{
            //width: 2000px;
            //.play,.pause{
            //    .mask,.video-play,.video-pause{
            //        display: block;
            //    }
            //}
        //}
        li{
            width: 189px;
            //margin-right: 7px;
            float: left;
            position: relative;
            &:hover{
                .mask,.video-play,.video-pause{
                    display: block;
                }
            }
            img {
                margin-right: 7px;
            }
        }
        .tips{
            height: 18px;
            line-height: 18px;
            padding: 0 5px;
            color: #fff;
            border-radius: 3px;
            position: absolute;
            z-index: 4;
            left: 4px;
            top: 4px;
            &.live {
                background: #13ab41;
            }
            &.playback {
                background: #ed444c;
            }
        }
        .mask{
            display: none;
            position: absolute;
            left: 0;
            top: 0;
            width: 182px;
            height: 108px;
            background: rgba(0,0,0,.5);
        }
        .title {
            line-height: 32px;
            text-align: center;
            background: #ededed;
            color: #242424;
            display: block;
            margin-right: 7px;
            font-size: 14px;
        }
        .video-play{
            display: none;
            width: 42px;
            height: 42px;
            background: url(i/__sprite.png) -42px -0px;
            position: absolute;
            z-index: 2;
            top: 50%;
            left: 50%;
            margin: -37px 0 0 -21px;
        }
        .video-pause{
            display: none;
            width: 42px;
            height: 42px;
            background-image: url(i/__sprite.png);
            background-position: -0px -0px;
            position: absolute;
            z-index: 2;
            top: 50%;
            left: 50%;
            margin: -37px 0 0 -21px;
        }
    }
}

.book-timeline {
    background: #ccc;
    font-size: 14px;
    height: 300px;
    overflow: hidden;
}

.book-timeline {
    margin-top:10px;
}
.timeline-content {
    margin: 10px;
    dl {
        overflow: hidden;
        *zoom: 1;
        margin-bottom:10px;
        dt {
            float: left;
        }
        dd {
            overflow: hidden;
            padding-left: 10px;
        }
        strong {
            display: block;
        }
        .img {
            border-radius: 32px;
            border: 1px solid #ccc;
            padding: 1px;
            background:#fff;
            box-shadow: 0 0 5px #ddd;
        }
    }
    blockquote {
        padding: 10px;
        //background: #fff;
        border-radius: 10px;
        margin-top: 5px;
        display: inline-block;
    }
    // generated from http://www.cssarrowplease.com/
    .arrow-box {
        position: relative;
        background: #ffffff;
        border: 1px solid #cccccc;
    }
    .arrow-box:after, .arrow-box:before {
        right: 100%;
        top: 50%;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
    }

    .arrow-box:after {
        border-color: rgba(255, 255, 255, 0);
        border-right-color: #ffffff;
        border-width: 6px;
        margin-top: -6px;
    }
    .arrow-box:before {
        border-color: rgba(204, 204, 204, 0);
        border-right-color: #cccccc;
        border-width: 7px;
        margin-top: -7px;
    }
}


.book-timelineV2{
    margin: 10px 0 0;
    overflow: auto;
    overflow-x: hidden;
    position: relative;

    .timeline-content{
        margin: 0;
    }

    dl{
        padding: 0 36px;
        height: 150px;
        overflow: hidden;
        transform-origin: 0 0;
        -webkit-transform-origin: 0 0;
        -moz-transform-origin: 0 0;
        -ms-transform-origin: 0 0;
        transform: translateY(20px) scale(.8);
        -webkit-transform: translateY(20px) scale(.8);
        -moz-transform: translateY(20px) scale(.8);
        -ms-transform: translateY(20px) scale(.8);
        opacity: .6;
        transition: all .5s;
        -webkit-transition: all .5s;
        -moz-transition: all .5s;
        -ms-transition: all .5s;
        margin-bottom: 0;

        dt{
            width: 120px;
            float: left;
            .img{
                border: none;
                padding: 0;
                background: none;
                box-shadow: none;
                border-radius: 50%;
                display: block;
                transform: scale(.8);
                -webkit-transform: scale(.8);
                -moz-transform: scale(.8);
                -ms-transform: scale(.8);
                transform-origin: 0 0;
                -webkit-transform-origin: 0 0;
                -moz-transform-origin: 0 0;
                -ms-transform-origin: 0 0;
                transition: transform .5s;
                -webkit-transition: transform .5s;
                -moz-transition: transform .5s;
                -ms-transition: transform .5s;
            }
        }
        dd{
            overflow: hidden;
            zoom: 1;
            padding-left: 0;
            font-size: 16px;
            color: #333;
            transition: padding .5s;
            -webkit-transition: padding .5s;
            -moz-transition: padding .5s;
            -ms-transition: padding .5s;
            .user_info{
                margin-bottom: 8px;
                strong{
                    font-weight: bold;
                }
            }
            .arrow-box{
                display: inline-block;
                padding: 11px 15px 13px;
                background: #fff;
                background: rgba(255,255,255,.8);
                line-height: 24px;
                color: #666;
                border: 1px solid #ccc;
                border-radius: 25px;
                overflow: hidden;
                max-height: 67px;
            }
        }

        // 激活样式
        &.current{
            transform: translateY(-10px) scale(1);
            -webkit-transform: translateY(-10px) scale(1);
            -moz-transform: translateY(-10px) scale(1);
            -ms-transform: translateY(-10px) scale(1);
            opacity: 1;
            padding: 0 30px;
            dt{
                .img{
                     transform: scale(1);
                    -webkit-transform: scale(1);
                    -moz-transform: scale(1);
                    -ms-transform: scale(1);
                }
            }
            dd{
                padding-left: 20px;
            }
        }

        // IE8及以下兼容样式
        &.lteIE8{
            zoom: .8;
            padding: 30px 36px 0;
            height: 120px;
            margin: 0 0 30px;
            dt{
                zoom: .8;
            }
            dd{
                padding-left: 25px;
                *padding-left: 0;
                position: relative;
                *display: inline;
            }

            &.current{
                zoom: 1;
                padding: 0 30px;
                height: 150px;
                margin: -10px 0 10px;
                dt{
                    zoom: 1;
                    margin-left: 0;
                }
                dd{
                    padding-left: 20px;
                }
            }
        }

    }

}