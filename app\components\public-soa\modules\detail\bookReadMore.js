define('PUBLIC_ROOT/modules/detail/bookReadMore', function(require, exports, module) {

    var bookReadMore = {
        bookInit: function() {
            var $bookQuan = $('#summary-quan');
    
            this.setBookMore();
            this.setBookIllustrate();
    
            if ( $bookQuan.length > 0 ) {
                $bookQuan.show();
                $('#p-ad-book').hide();
            }
        },
        // 设置图书详情内容过长显示查看全部
        setBookMore: function() {
            var $detail = $('#J-detail-content');
            var $detailItem = $detail.find('.book-detail-item');

            var maxHeight = 314;

            //var moreHTML = '<div class="more"><a data-open="0"  href="#none">查看全部↓</a></div>';

            $detail.delegate('.more a', 'click', function () {
                var href = $(this).attr('href');
                var toOpen = $(this).attr('data-open') === '0';
                var text = toOpen ? '收起全部↑' : '查看全部↓';
                var $content = $(this).parents('.book-detail-item').eq(0).find('.book-detail-content');

                if ( toOpen ) {
                    $content.removeAttr('style');
                    $(this).attr('data-open', '1');
                } else {
                    $content.css({
                        'height': maxHeight,
                        'overflow': 'hidden'
                    });
                    $(this).attr('data-open', '0');
                }

                $(this).text(text);

                var sTop = $(href).offset().top;
                $('body,html').animate({
                    scrollTop: sTop - 20
                });
                return false;

            });

            $detailItem.each(function () {
                var $content = $(this).find('.book-detail-content');
                var contentHeight = $content.outerHeight();
                var $more = $(this).find('.more [data-open]');

                if ( $more.length > 0 ) {
                    if ( contentHeight > maxHeight ) {
                        $content.css({
                            'height': maxHeight,
                            'overflow': 'hidden'
                        });
                    } else {
                        $more.hide();
                    }
                }

            });
        },
        // 图书内页插图
        setBookIllustrate: function () {
            seajs.use(['JDF_UI/imgpreview/1.0.0/imgpreview.js', 'JDF_UI/dialog/1.0.0/dialog.js'], function(imgpreview){
                $('.img-preview').imgpreview({
                    title: '内页插图'
                });
            });
        }

        
    }

    module.exports = bookReadMore;
});
