@import "__sprite";
@import '../common/lib';

.choose-shop {
    .dt {
        height: 32px;
        line-height: 32px;
    }
    .service-provider {
        color:#999;
        clear: both;
    }
    .operate {
        a {
            display: inline;
            line-height: 30px;
            margin-right: 5px;
            margin-left: 9px;
            color: #505259;
            font-size: 15px;
            padding-right: 12px;
            background: url(https://img13.360buyimg.com/imagetools/jfs/t1/259181/16/14667/489/6790bd3aF60de783b/8ed74ff7b6ef4ed6.png) no-repeat;
            background-size: 8px;
            background-position: right center;
        }
        a:hover {
            color: #e4393c;
        }
    }
    .icon-o2o{
        display: inline-block;
        margin-right: 5px;
        vertical-align: -3px;
        @include sprite-o2o;
    }
}

#choose-shop-show {
    .dt {
        height: 33px;
        line-height: 33px;
    }
    .item {
        margin-bottom: 16px;
    }
}
.deli-to-shop {
    a {
        margin-right:20px;
    }
    input {
        vertical-align: middle;
    }
}

