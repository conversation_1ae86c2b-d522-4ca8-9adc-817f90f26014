@import "../common/lib";
@import "./__sprite";
#choose .commitments .dd {
    overflow: visible;
}
.commitments {
    margin: 16px 0; // 楼层上下间距
    .commitments-item{
        font-size: 15px;
        padding-bottom: 7px;
        display: inline-block;
        *display: inline;
        *zoom: 1;
        position: relative;
        cursor: default;
        span{
            color: #1a1a1a;
            vertical-align: 3px;
            *vertical-align: -3px;
            margin-right: 10px;
        }
    }
    .sprite-7day{
        @include sprite-7day;
        display: inline-block;
        margin-right: 10px;
        vertical-align: -1px;
        _background-image:url(i/__sprite-8.png);
    }
    .sprite-heart{
        @include sprite-heart;
        display: inline-block;
        margin-right: 8px;
        vertical-align: -1px;
        _background-image:url(i/__sprite-8.png);
    }
    .commitments-tips{
        display: none;
        width:265px;
        position: absolute;
        left: -6px;
        top: 28px;
        *top:25px;
        padding: 12px;
        border: 1px solid #ccc;
        background: #fff;
        z-index: 10; // 底bar 为9
        strong{
            color: #e4393c;
            font-weight: normal;
        }
        a{
            color: #5e69ad;
            float: right;
            *display: block;
            *width: 47px;
            &:hover{
                color: #e4393c;
            }
        }
        p{
            float: left;
            margin-left: 10px;
            width: 190px;
            _display: inline;
        }
        .sprite-arrow{
            @include sprite-arrow;
            position: absolute;
            left: 5px;
            top: -8px;
            *width:250px;
            _background-image:url(i/__sprite-8.png);
            *background-repeat:no-repeat;
        }
        .sprite-heartPic{
            // @include sprite-heartPic;
            width: 50px;
            height: 50px;
            background-image: url(i/heartPic.png);
            // background-position: -0px -0px;
            float: left;
            background-size: 100%;
            // _background-image:url(i/__sprite-8.png);
        }
    }
    .heart:hover .commitments-tips,.heart-hover .commitments-tips{
        display: block;
    }
}
