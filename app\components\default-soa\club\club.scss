@import "../common/lib";
/* consult */

@mixin cli-style{
    border-bottom: #eee 1px solid;
    overflow:hidden;
    padding: 20px 30px;
}
$clubGray: #989898;

.club{
    .list-content .ac {
        padding: 20px 0 100px;
    }
    .tab-con{
        padding-top: 0;
    }
    .list-content {
        padding: 0 20px 20px;
    }

    .hot-post{
        li{
            @include cli-style;
            
            h6 {
                margin-bottom: 10px;
            }
            .c-operate {
                float: right;
            }
            .c-details {
                padding-top: 5px;
                clear: both;
                @include clearfix;
            }
            div.c-pic{
                position: relative;
                z-index: 2;
                float: left;
                width: 0;
                height: 0;

                img{
                    display: block;
                }
            }

            div.p-cont{
                min-height: 76px;
                h6{
                    font-size: 14px;
                }
                p{
                    font:14px/26px 'simsun';
                    color: #999;
                }
            }

            .c-issue {
                float: left;
                color: #999;
                *display: inline;
            }

        }

        li.consult0{
            div.c-pic{
                width: 120px;
                height: 120px;
                float: left;
                *display: inline;
                margin-right: 10px;
                display:none;
            }
            div.p-cont{
                overflow: hidden;
                *zoom: 1;
            }
        }
    }

    .show-post{
        table {
            margin-top: 20px;
        }
        thead {
            background: #fafafa;
            outline: 1px solid #f5f5f5;
            height: 28px;
            line-height: 28px;
            color: $clubGray;
        }
        tbody td {
            border-bottom: 1px solid #eee;
            height: 45px;
            line-height: 45px;
        }
        .col2,
        .col3,.col3 a,
        .col4 {
            color: $clubGray;
            text-align: center;
        }
        .col4 {
            width: 150px;
        }
    }

    .c-total{
        color: $clubGray;
        text-align: right;
        padding-top: 15px;
        padding-right: 15px;
    }
}

.statement{
    color: #999;
    font: 12px/20px 'simsun';
    padding-left: 20px;
}

