{% import '../../views/maco/user.html' as wow %}

<div id="choose-countdown" class="li" style="display:none">
    <div class="dt">倒 计 时</div>
    <div class="dd">
        <span id="bgm-countdown"></span>
    </div>
</div>

<div id="choose-reservation-cd" class="li reservation-cd" style="display:none">
    <span class="text"><em>预约人数</em><strong class="J-count"></strong></span>
    <span class="text"><em>倒计时</em><strong class="J-time"></strong></span>
</div>

<div id="choose-btns" class="choose-btns clearfix">
    <div class="choose-amount">
        <div class="wrap-input">
            <input class="text buy-num" onkeyup="setAmount.modify('#buy-num');" id="buy-num" value="1">
            <a class="btn-reduce" onclick="setAmount.reduce('#buy-num')" href="javascript:;">-</a>
            <a class="btn-add" onclick="setAmount.add('#buy-num')" href="javascript:;">+</a>
        </div>
    </div>

    <!--{{ wow.button(name="必购码", id="choose-btn-bgm", class="btn-special1 btn-lg btn-disable J-s-login", style="display:none;") }}-->
    <span class="bgm-text J-bgm-text"></span>
    <!--<span class="yuyue-share J-yuyue-share"><i class="sprite-share"></i>分享</span>-->
    {{ wow.button(name="立即预约", id="btn-reservation", class="btn-special1 btn-lg", style="display:none;") }}
    {{ wow.button(name="邀请好友", id="btn-pg-share", class="btn-special3 btn-lg", style="display:none;") }}
    {{ wow.button(name="加入购物车", id="InitCartUrl", class="btn-special1 btn-lg") }}
    {{ wow.button(name="购机并办理合约", id="btn-heyue", class="btn-special1 btn-lg", style="display:none;") }}
    {{ wow.button(name="一键购", id="btn-onkeybuy", class="btn-special2 btn-lg", style="display:none;") }}
    {{ wow.button(name="打白条", id="btn-baitiao", class="btn-special2 btn-lg", style="display:none;") }}
    <!--<span class="pingou-tips J-pingou-tips">成功邀请10人入拼，可获10京豆！</span>-->
    <a href="#none" class="btn-buy">纸书购买</a>
    <a href="#none" class="J-notify-stock btn-def btn-lg" style="display:none;" data-type="2" data-sku="1217499">到货通知</a>
</div>
