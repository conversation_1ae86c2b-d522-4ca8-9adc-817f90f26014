define('MOD_ROOT/o2o/o2o', function(require, exports, module) {
    "use strict";
    var G = require('MOD_ROOT/common/core');
    var tools = require('MOD_ROOT/common/tools/tools');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Vehicle = require('MOD_ROOT/vehicle/vehicle');

    var cat = (pageConfig &&
        pageConfig.product &&
        pageConfig.product.cat) || [];
    var c1 = cat[0];

    if (c1 == "9987") {
        var TEXT = ["选择门店购买服务", "到店服务", "无需服务"];
    } else if (c1 == "1319" || c1 == "6233") {
        var TEXT = ["选择门店购买服务", "到店体验", "无需服务"];
    } else {
        var TEXT = ["选择门店购买安装服务", "到店安装", "无需安装"];
    }

    var ChooseShop = {
        init: function($el, cfg) {
            var _this=this;
            _this.$el = $el;
            _this.cfg = cfg;
            _this.isInstallNow = false;
            _this.isStock = false;
            _this.hasHave = false;
            _this.install = "";
            _this.ysStatus = "";
            _this.enTimeOrderInfo = false;
            Event.addListener('onStockReady', function(data){
                var data = data && data.stock && data.stock.data
                _this.isInstallNow = data.isInstallNow;//是否是马上装有货商品
                _this.isStock = data.stockInfo.isStock;//是否有库存
                _this.hasHave = data && data.carShopInfo && data.carShopInfo.hasHave //有无门店服务
                _this.install = data && data.carShopInfo && data.carShopInfo.install //install==“1” 强制安装 屏蔽无需安装按钮
                _this.yushouStepType = data && data.YuShouInfo && data.YuShouInfo.yushouStepType;//是否预售并且yushouStepType==5
                _this.ysStatus = data && data.YuShouInfo && data.YuShouInfo.status;// 预售状态 0 不展示支付定金按钮
                _this.enTimeOrderInfo = data && data.enTimeOrderInfo && data.enTimeOrderInfo.enTimeOrder // 定期购是否展示企业购楼层和企业购加车按钮标识
                if (!_this.$el.length 
                    || !_this.isStock 
                    || _this.cfg.isYuYue 
                    || _this.cfg.isPinGou 
                    || (_this.cfg.isYuShou && _this.yushouStepType != "5")
                    ) { return false; }
                if ($.inArray('isFreeload-2', cfg.specialAttrs) != -1) {
                    _this.hasAddedService();  // 当商品有特殊标识（isFreeload-2）时，才去检查有无增值服务
                } else {
                    _this.hasBindSale();  // 若无增值服务，再去检查捆绑销售
                }
            })
            
            

            return this;
        },
        bindEvent: function() {
            var _this = this;
            this.$el.undelegate('click').delegate('.item,.J-mod', 'click', $.proxy(this.openIframe, this));
            Event.addListener('onNumChange', $.proxy(this.setBtnLink, this,'numberchange'))
            /// 地区和数量变化会触发此事件
            Event.addListener('onStockReady', function () {  
                if (_this.cfg.havestock) {
                    if (!_this.isO2oCanGoCart) {
                        if(_this.ysStatus !== "0"){
                            _this.cfg.addToCartBtn.setEnable('#none');
                        }
                    }
                }
            });
        },
        openIframe: function() {

            var url = '',
                param = {},
                areaIds = tools.getAreaId().areaIds;

            /* 车型下传需求 产品：路平 开发：lqf  20181219
             * 文档地址：https://cf.jd.com/pages/viewpage.action?pageId=137886451
             * 简述：当用户选择 到店安装 时 必须选择服务门店 需要往 选择门店购买安装服务 的弹层里下传增加 modelid 参数下传
             * 拼接字段为 cm
             */
            // 获取 modelid
            var modelId = '';
            var chooseCarBox = $('#choose-car');
            var vehicleSelectedBox = chooseCarBox.find('.vehicle.selected');
            if (chooseCarBox.length > 0 && vehicleSelectedBox.length > 0) {
                modelId = vehicleSelectedBox.attr('data-modelid');
            }

            if (this.btnType == 'carService') {
                url = '//fcar.jd.com/carService/pc/index.html?';
                param = {
                    skuId: this.cfg.skuid,
                    provinceId: areaIds[0],
                    cityId: areaIds[1],
                    countyId: areaIds[2],
                    num: $('#buy-num').val(),
                    cm: modelId
                }
            } else if (this.btnType == 'bindSale') {
                url = '//fcar.jd.com/locshop/index.html?';
                param = {
                    skuId: this.cfg.skuid,
                    cat: this.cfg.cat.join(','),
                    pName: escape(this.cfg._area.provinceName),
                    addrCodeLv1: areaIds[0],
                    addrCodeLv2: areaIds[1],
                    addrCodeLv3: areaIds[2],
                    venderId: this.cfg.venderId,
                    sku: this.sku || '',
                    sid: this.sid || '',
                    num: this.num || '',
                    name: this.name || '',
                    price: this.price || '',
                    provider: this.provider || '',
                    coor: this.coor || '',
                    currentPage: 1 || '',
                    pageSize: 10 || '',
                    cm: modelId
                }
                }

            var gifts = Vehicle.getGiftSkus();
            if (gifts) {
                param.giftId = gifts;
            }

            $('body').dialog({
                width: 900,
                height: 520,
                title: '选择门店',
                type: 'iframe',
                autoIframe: false,
                source: url + $.param(param),
                onReady: function() {
                    $('#dialogIframe').css('height', '100%')
                }
            });
        },
        setBtnLink: function(type) {
            /*
             http://cart.jd.com/cart/dynamic/gateForSubFlow.action?wids=773132,10132882551&nums=1,1&sid=1985425&subType=47&随机数
             wids:实物,服务
             nums:实物数量,服务数量
             sid:服务门店
             subType:类型，写死47
             最后随机数

             cart.jd.com/gate.action?pid=主商品id&pcount=主商品数量&lid=服务id&lcount=服务数量&ptype=4&lsid=门店id&ybId=延保id1,延保id2

             */
            //到店安装不能直接加车的情况下，数量编辑进入此方法时 不生成链接
            if(type && 'numberchange'==type && !this.isO2oCanGoCart) return;
            var url = '//cart.jd.com/gate.action?'
            // var param = $.param({
            //     pid: this.cfg.skuid,
            //     pcount: $('#buy-num').val(),
            //     lid: this.sku,
            //     lcount: this.num,
            //     ptype: 4,
            //     lsid: this.sid,
            //     r: Math.random()
            // })
            // var url = '//cart.jd.com/cart/dynamic/gateForSubFlow.action?'
            // var param = $.param({
            //     wids: this.cfg.skuid + ',' + this.sku,
            //     nums: $('#buy-num').val() + ',' + this.num,
            //     sid: this.sid,
            //     subType: 47,
            //     r: Math.random()
            // })
            //this.buyUrl = url + param
            //如果是改变数量进入此方法
            if(type && 'numberchange'==type){
                var currentCartLink = this.cfg.addToCartBtn.$el.attr('href');
                if(currentCartLink && currentCartLink.indexOf('none')==-1){
                    this.originalCartLink = currentCartLink;
                }
            }
            
            if(this.originalCartLink){
                this.originalCartLink = this.originalCartLink.replace(/(.*pcount=)(\d*)(.*)/,'$1'+$('#buy-num').val()+'$3');
            }

            if (!this.cfg.addToCartBtn.$el.hasClass('btn-disable')) {
                this.cfg.addToCartBtn.setEnable(this.originalCartLink);
            }
        },
        /**
         * 【新增代码】 需求描述：POP汽车服务市场项目 产品：孙庆美  2018-02-09  added by wutengteng  begin
         *  逻辑： 先通过“增值服务接口”判断有无门店，若有，则显示“选择门店购买安装服务”，若无门店，再读“捆绑销售”接口，判断是否有门店
         */
        hasAddedService: function(a) {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/addedService', // 汽车增值服务接口
                data: {
                    skuId: this.cfg.skuid,
                    appid: 'item-v3',
                    functionId: "pc_addedService"
                },
                dataType: 'jsonp',
                timeout: 1000,
                error: $.proxy(this.hasService, this),
                success: $.proxy(this.hasService, this)
            })
        },
        hasService: function(r) {
            if (!r || !r.isHave) { // 无增值服务
                this.hasBindSale();
            } else { // 有增值服务，显示：“选择门店购买安装服务”
                this.serviceClass = "addedService" || "";
                //this.showChooseShop();
                this.showChooseShop(r.setupStatus);
                this.btnType = 'carService';
                this.bindEvent();
                if (this.isInstallNow && !this.hasHave)
                {
                    this.clearShop();
                }
            }
        },
        hasBindSale: function() {
            if  ($.inArray('isActualServ-1',pageConfig.product.specialAttrs) != -1) {
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }
                $.ajax({
                    url: host + '/locshop/has', // 捆绑销售接口
                    data: {
                        skuId: this.cfg.skuid,
                        cat: this.cfg.cat.join(','),
                        appid: 'item-v3',
                        functionId: "pc_locshop_has"
                    },
                    dataType: 'jsonp',
                    timeout: 1000,
                    error: $.proxy(this.clearShop, this),
                    success: $.proxy(this.setBindSale, this)
                })
            }
        },
        setBindSale: function(r) {
            if (!r || r.hasLocShop !== 'true') {
                this.clearShop();
            } else {
                
                this.showChooseShop();
                this.btnType = 'bindSale';
                this.bindEvent();
                if (this.isInstallNow && !this.hasHave)
                {
                    this.clearShop();
                }
            }
        },
        clearShop: function() {
            this.$el.hide();
            this.clear();
        },
        showChooseShop: function(c) { // 显示：“选择门店购买安装服务”
            this.render();
            this.$el.find('.dt').html('服务门店');
            this.renderShop(c);
            this.$el.show();
        },
        // 【新增代码】 需求描述：POP汽车服务市场项目 产品：孙庆美  2018-02-09  added by wutengteng  end
        render: function() {
            var template ='\
                <div class="item ' + this.serviceClass + '"\
                    clstag="shangpin|keycount|product|xuanzemendian_'+ this.cfg.cat[2] +'">\
                    <b></b>\
                    <a href="#none"><i class="icon-o2o"></i>' + TEXT[0] + '</a>\
                </div>';

            this.$el.find('.dd').html(template)
        },
        renderShop:function (c) {
            // 判断是否必须到店安装sku，特殊属性的 psdd: 2 为必须到店安装，此时屏蔽无需安装按钮（原来是pssd等于2修改tssp=32）2024 04.17
            // 此需求产品文档：https://cf.jd.com/pages/viewpage.action?pageId=143921145
            // 商品特殊属性文档：https://cf.jd.com/pages/viewpage.action?pageId=59314641
            // 产品负责人：范冬  开发：lqf  2018 11.26 
            var isMustTssp = this.cfg.specialAttrs.join(',').indexOf('tssp-32') > -1 ? true : false;
            if($("#choose-shop-show").length > 0){
                $("#choose-shop-show").remove()
            }
            var template =
                '\<div id="choose-shop-show" class="li">\
               <div class="dt">可选服务</div>\
               <div class="dd">\
               <div class="item selected" data-index="0">\
                    <b></b>\
                    <a href="#none">' + TEXT[1] + '</a>\
                </div>'
            // 如果是为必须到店安装 sku 时，不显示 ‘无需安装’ 按钮
            if (isMustTssp || c === 1) {
                template += '\
                </div>\
                </div>' 
            }else if (this.isInstallNow && this.hasHave && this.install=="1") {
                template += '\
                </div>\
                </div>' 
            }else {
                template += '\
                    <div class="item" data-index="1">\
                        <b></b>\
                        <a href="#none">' + TEXT[2] + '</a>\
                    </div>\
                </div>\
                </div>'
            }

            this.$el.before(template)
            this.bindShopEvent()
        },
        bindShopEvent:function () {
            var _this = this;
            _this.originalCartLink = _this.cfg.addToCartBtn.originHref;
                _this.disabeAddToCartBtn();

            $('#choose-shop-show').delegate('.item', 'click', function () {
                var $this = $(this);
                var index = $this.attr('data-index');
                $('#choose-shop-show .item').removeClass('selected');
                $this.addClass('selected');
                if (index == 0) {
                    _this.$el.show();
                    $('#btn-baitiao').hide();
                    //如果是从“无需安装”切换到“到店安装” 保存切换前链接值
                    var currentCartLink = _this.cfg.addToCartBtn.$el.attr('href');
                    if(currentCartLink && currentCartLink.indexOf('none')==-1){
                        _this.originalCartLink = currentCartLink;
                    }
                    _this.disabeAddToCartBtn();
                } else if (index == 1){
                    _this.isO2oCanGoCart = true;
                    _this.$el.hide();
                    _this.setBtnLink('noNeedInstall');
                    if($('#choose-baitiao .selected').length > 0){
                        $('#btn-baitiao').show();
                    }
                    if(_this.ysStatus !== "0"){
                        _this.cfg.addToCartBtn.enabled(_this.originalCartLink);
                    }
                    Vehicle.setCartBtn();
                    // $("#InitTradeUrl").show()// 隐藏立即购买按钮
                }
            });

            try{
                // 屏蔽服务门店和可选服务统一方法（命中定期购）
                if(_this.enTimeOrderInfo){ 
                    $("#choose-shop-show,#choose-shop").remove()
                }
            }catch(e){
                console.log(e)
            }
        },
        disabeAddToCartBtn:function () {
            var _this = this;
            _this.cfg.addToCartBtn.$el.attr('href', '#none');
            _this.isO2oCanGoCart = false;
            if(_this.ysStatus !== "0"){
                _this.cfg.addToCartBtn.enabled('#none');
            }
            $("#InitTradeUrl").hide()// 隐藏立即购买按钮
            // _this.cfg.addToCartBtn.$el.find('#InitCartUrl').attr('href', '#');
            // _this.cfg.addToCartBtn.$el.find('#InitCartUrl-mini').attr('href', '#choose-shop-show');
            _this.cfg.addToCartBtn.$el.unbind('click',$.proxy(this.showShopTips, this)).bind('click',$.proxy(this.showShopTips, this))
        },
        showShopTips:function(){
            var _this = this;
            _this.$el.addClass('item-hl-bg');
            if(!$('.display-tips').length){
                var html = '<div class="fl display-tips">请选择门店</div>'
                _this.$el.append(html);
            }
        },
        clear: function() {
            this.$el.find('.dd').html('')
        }
    }

    function init(cfg) {
        var $chooseShop = $('#choose-shop')
        cfg.__chooseShop = ChooseShop.init($chooseShop, cfg)
    }

    module.exports.__id = 'o2o'
    module.exports.init = init
})
