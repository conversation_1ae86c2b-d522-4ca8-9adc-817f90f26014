define('PUBLIC_ROOT/modules/videoBox/videoTrailer', function(require, exports, module) {
    //视频片尾
    var videoBox = require('PUBLIC_ROOT/modules/videoBox/videoBox')
    var videoTrack = require('PUBLIC_ROOT/modules/videoBox/videoTrack')
    var videoAnchorPoint = require('PUBLIC_ROOT/modules/videoBox/videoAnchorPoint')

    var tools = {
        // 类选择器
        getElementsByClassName: function (className,context,tagName) {

            if(typeof context == 'string'){
                tagName = context;
                context = document;
            }else{
                context = context || document;
                tagName = tagName || '*';
            }
            if(context.getElementsByClassName){
                return context.getElementsByClassName(className);
            }
            var nodes = context.getElementsByTagName(tagName);
            var results= [];
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                var classNames = node.className.split(' ');
                for (var j = 0; j < classNames.length; j++) {
                    if (classNames[j] == className) {
                        results.push(node);
                        break;
                    }
                }
            }
            return results;
        },

        //videojs版本兼容
        getVideoElement:function (player,elClass) {
            // || videojs.players["video-player"].controlBar.progressControl.seekBar.b
            //    找属性，不具有公用性，废弃
            if(typeof player.$ == 'function'){
                return player.$('.' + elClass);
            }else{
                return this.getElementsByClassName(elClass)[0];
            }
        },
        getVideoDom:function (player) {
            return player.el_ || player.b || null;
        },
        // 添加class
        addClass: function (eleList, cls){
            eleList = Array.prototype.slice.apply(eleList);
            var add = function (obj) {
                var obj_class = obj.className; //获取 class 内容.
                var blank = (obj_class != '') ? ' ' : '';//判断获取到的 class 是否为空, 如果不为空在前面加个'空格'.
                var added = obj_class + blank + cls;//组合原来的 class 和需要添加的 class.
                obj.className = added;//替换原来的 class.
            }
            if (eleList instanceof Array) {
                for (var i=0; i<eleList.length; i++) {
                    add(eleList[i]);
                }
            } else {
                add(eleList);
            }
        },
        // 移除class
        removeClass: function (eleList, cls) {
            eleList = Array.prototype.slice.apply(eleList);
            var remove = function (obj) {
                var obj_class = ' '+obj.className+' ';
                obj_class = obj_class.replace(/(\s+)/gi, ' ');
                var removed = obj_class.replace(' '+cls+' ', ' ');
                removed = removed.replace(/(^\s+)|(\s+$)/g, '');
                obj.className = removed;//替换原来的 class.
            }
            if (eleList instanceof Array) {
                for (var i=0; i<eleList.length; i++) {
                    remove(eleList[i]);
                }
            } else {
                remove(eleList)
            }

        },
    };

    var videoTrailer = {
        init:function (player,extInfo) {
            if(!extInfo || !extInfo.trailerPlayUrl) return false;

            this.player =  player;
            this.extInfo = extInfo;
            this.ifTrailer = true;//是否播放片尾
            this.trailerFlag = 0;//当前视频内容，0 正片，1是片尾 ,其他无效
            this.bingEvent();
        },
        ifTrailer:false,//默认不播放片尾
        trailerFlag:0,//当前视频内容，0 正片，1是片尾 ,其他无效
        extInfo:{
            trailerPlayUrl:'',//（片尾播放地址）
            trailerDuration:'',//（片尾时长）
            trailerImgUrl:'',//（片尾封面图地址）
        },
        //片尾全屏时添加关闭按钮
        addCloseBtn:function (player,callback) {
            var playerDom = tools.getVideoDom(player);
            if(!playerDom) return false;
            var _this = this;
            var closeBtn = document.createElement('div');
            closeBtn.setAttribute('class', 'vjs-close-btn vjs-hidden');
            closeBtn.innerText = '关闭';
            closeBtn.onclick = function (ev) {
                callback()
            }
            playerDom.appendChild(closeBtn);
        },
        hideCloseBtn:function () {
            var _this = this;
            var closeBtn = tools.getElementsByClassName('vjs-close-btn','','div');
            tools.addClass(closeBtn,'vjs-hidden')
        },
        showCloseBtn:function () {
            console.log('showCloseBtn')
            var _this = this;
            var closeBtn = tools.getElementsByClassName('vjs-close-btn','','div');
            tools.removeClass(closeBtn,'vjs-hidden')
        },
        bingEvent:function () {
            var _this = this;
            var _player = _this.player;
            var playerDom = tools.getVideoDom(_player)
            // _player.on('ready',function () {
            // });

            _player.on('loadeddata',function () {
                if(_this.ifTrailer && _this.trailerFlag == 1){
                    _player.poster(_this.extInfo.trailerImgUrl);
                }
            });

            //主视频播放完毕，加载片尾
            _player.on('ended',function () {
                if(_this.ifTrailer && !_this.trailerFlag){
                    _player.src(videoBox.setVideoType(_this.extInfo.trailerPlayUrl));
                    _this.trailerFlag = 1;
                    //片尾视频没必要传递数据
                    videoTrack.disabledSend();
                    //片尾无打点
                    videoAnchorPoint.disabledPoint();
                    //片尾移除控制条
                    var elControlBar = tools.getVideoElement(_player,'vjs-control-bar');
                    if(elControlBar && playerDom){
                        playerDom.removeChild(elControlBar);
                    }
                    //全屏展示关闭按钮
                    if(_player.isFullscreen()){
                        _this.showCloseBtn()
                    }
                    setTimeout(function () {
                        _player.play();
                    }, 0)
                }
            });
            _player.on('fullscreenchange',function () {
                if(_this.ifTrailer && _this.trailerFlag == 1){
                    if(_player.isFullscreen()){
                        _this.showCloseBtn()
                    }else {
                        _this.hideCloseBtn()
                    }
                }
            });

        }
    }
    module.exports = videoTrailer
})
