@import '../common/lib';
@import '_sprite';

.choose-gift {
    line-height: 30px;
    padding-bottom: 4px;
    
    .dd {position: relative;}

    .giftpool-head {
        @include inline-block;
        position: relative;
        height: 30px;
        line-height: 30px;
        padding: 0 9px;
        border: 1px solid #d1d1d1;
        border-bottom: none;
        background-color: #fff;
        cursor: pointer;
        border-radius: 4px;
        .border-bottom {
            position: absolute;
            left: 0;
            bottom: 0;
            // z-index: 2;
            overflow: hidden;
            width: 100%;
            height: 1px;
            background-color: #d1d1d1;
        }
        .icon-giftbox {
            @include inline-block;
            @include sprite-gift;
            vertical-align: sub;
            margin-right: 5px;
        }
        .text {
            color: #666;
            font-size: 12px;
        }
    }

    .giftpool-head.active {
        // z-index: 3; // 遮挡腰带取消或降低层级
        border-color: #e4393c;
        .border-bottom {
            background-color: #fff;
        }
    }

    .giftpool-body {
        // position: absolute;
        top: 30px;
        left: 0;
        z-index: 2;
        width: 570px;
        border: 1px solid #e4393c;
        background-color: #fff;
        .container {
            overflow-y: auto;
            max-height: 350px;
            padding: 20px 20px 0 20px;
            .pool {
                overflow: hidden;
                margin-right: -20px;
            }
            .pool-name {
                line-height: 30px;
                color: #333;
            }
            .item {
                float: left;
                background-color: #fff;
                width: 90px;
                margin-right: 20px;
                margin-bottom: 20px;
            }
            .goods {
                 .goods-inner-wrap {
                    overflow: hidden;
                    position: relative;
                    width: 88px;
                    height: 88px;
                    line-height: 88px;
                    border: 1px solid #eee;
                    &:hover {
                        border-color: #e4393c;
                    }
                }
                .goods-img {
                    width: 100%;
                    vertical-align: middle;
                }
                .icon-selected {
                    @include sprite-selected;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    display: none;
                }
                .goods-name {
                    max-height: 36px;
                    line-height: 18px;
                    padding: 0;
                    margin: 8px 0;
                    color: #666;
                    font-size: 12px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    border: none;
                    &:hover {
                        color: #e4393c;
                        border: none;
                    }
                }
                cursor: pointer;
                display: block;
                height: auto;
                margin: 0;
            }
            .goods.selected {
                .goods-inner-wrap {
                    border-color: #e4393c;
                }
                .icon-selected {
                    display: block;
                }
            }
            .counter-wrap {
                text-align: center;
            }
            .counter {
                @include inline-block;
                line-height: initial;
                border: 1px solid #eee;
                vertical-align: top;
                font-size: 0;
                .reduce, .increase {
                    @include inline-block;
                    width: 16px;
                    height: 20px;
                    line-height: 20px;
                    color: #666;
                    font-size: 12px;
                    text-align: center;
                    cursor: pointer;
                }
                
                input {
                    border: none;
                    padding: 0;
                    width: 44px;
                    height: 20px;
                    border-left: 1px solid #eee;
                    border-right: 1px solid #eee;
                    text-align: center;
                }
                .disabled {
                    color: #ccc;
                    cursor: not-allowed;
                }
            }
        }
        .actionbar {
            overflow: hidden;
            height: 30px;
            line-height: 30px;
            padding: 10px 20px;
            background-color: #f8f8f8;
            .tips {
                float: left;
                color: #666;
                font-size: 12px;
            }
            .had {color: #333;}
            .icon-info {
                @include inline-block;
                @include sprite-info;
                vertical-align: text-bottom;
            }
            .btns {
                float: right;
                font-size: 0;
            }
            .btn-confirm {
                @include inline-block;
                padding: 6px 20px;
                line-height: normal;
                background-color: #df3033;
                color: #fff;
                font-size: 12px;
                cursor: pointer;

                margin-right: 10px;
            }
            .btn-cancel {
                @include inline-block;
                display: inline-block;
                line-height: normal;
                padding: 5px 20px;
                border: 1px solid #df3033;
                color: #df3033;
                font-size: 12px;
                cursor: pointer;
            }
        }
        .icon-close{
            @include inline-block;
            @include sprite-close;
            position: absolute;
            right: 18px;
            top: 14px;
            cursor: pointer;
        }
        .icon-warn {
            @include inline-block;
            @include sprite-warn;
            vertical-align: middle;
        }
        .absoluteCenter {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            text-align: center;
            -webkit-user-select:none;
            -moz-user-select:none;
            -ms-user-select:none;
            user-select:none;
            .column {
                @include inline-block;
                vertical-align: middle;
                height: 100%;
            }
            .content {
                @include inline-block;
                vertical-align: middle;
            }
        }
        .warn-tips {
            line-height: 20px;
            padding: 17px 20px;
            background-color: #fff; 
            @include box-shadow(1px 3px 15px -1px rgba(124,124,124,0.50));
            color: #333;
            font-size: 13px;
            .icon-warn {margin-right: 5px;}
        }
    }
}

// 定制弹框
.ui-dialog.gift-pool {
    .ui-dialog-title {
        display: none;
    }
    .ui-dialog-close {
        @include sprite-close;
    }
    .ui-dialog-btn-submit {
        padding: 8px 20px;
        background: #e4393c;
        border-color: #e4393c;
        color: #fff;
        &:hover {
            color: #fff;
        }
    }
    .ui-dialog-btn-cancel {
        padding: 8px 20px;
        background: #fff;
    }
    
    .notice {
        padding-top: 10px;
        background: #fff;
        text-align: center;
        .icon-question {
            @include inline-block;
            @include sprite-question;
            vertical-align: top;
        }
        .notice-text {
            margin: 10px 0;
            color: #333;
            font-size: 14px; 
        }
        .notice-ps {
            color: #666;
            font-size: 12px;
        }
    }

}