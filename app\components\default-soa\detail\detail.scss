@import "../common/lib";
@import "__sprite";
.detail-con {
    li {
        background: #ccc;
    }
}

#img-text-warp {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

#img-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}


.module-title {
 font-size: 20px;
 font-weight: 600;
 padding: 24px 0;
 color: rgba(26, 26, 26, 1);
}
#state {// 权利声明，价格说明
    font-size: 14px;
    line-height: 18px;
    strong {
        font-size: 16px;
        margin-bottom: 10px;
    }
}
/* 京喜商家资质 */
.jx-merchant-qualification {
    margin-bottom: 24px;
    height: 44px;
    background: rgba(247, 248, 252, 1);
    border-radius: 6px;
    
    a {
      display: flex;
      align-items: center;
      height: 44px;
      margin-left: 16px;
    }

    span {
        color: rgba(26, 26, 26, 1);
        font-size: 14px;
        margin-left: 8px;
        display: block;
    }

    .jxIcon {
        width: 20px;
        height: 20px;
        display: block;
        background-image: url(https://img14.360buyimg.com/imagetools/jfs/t1/256217/21/11772/764/67820d15F6f744ce2/1c142ba9d43c7b7b.png);
        background-size: 100% 100%;
      
    }
    .jxJt {
        width: 5px;
        height: 9px;
        display: inline-block;
        background-image: url(https://img14.360buyimg.com/imagetools/jfs/t1/89221/9/33267/215/65815dedFc83097a3/fd9113b2cde2a758.png);
        background-size: 100%;
        display: block;
    }
}

/* 商品介绍 */
.p-parameter {
    padding: 0 10px 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
    .more-par {
        padding-right: 20px;
        margin-top: -5px;
        text-align: right;
        a {
            color: $colorLinkBlue;
        }
    }
    ul {
        padding: 20px 0 15px;
        overflow:hidden;
        overflow:hidden;
        _zoom:1;
        li{
            width: 145px;
            padding-left: 42px;
            float:left;
            margin-bottom: 5px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            i{
                float: left;
                @include icons(33px, 33px);
                margin-top: 2px;
                margin-left: -37px;
                background-repeat: no-repeat;
                background-position: 0 0;
            }
            .i-phone{
                @include sprite-param1;
            }
            .i-camera{
                @include sprite-param2;
            }
            .i-cpu{
                @include sprite-param3;
            }
            .i-network{
                @include sprite-param4;
            }
            .detail{
                width: 142px;
                min-height: 0px;
                p{
                    width: 100%;
                    line-height: 18px;
                    margin-bottom: 4px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }

    .p-parameter-list {
        li a {
            color: #5e69ad;
        }
    }

}
/* 规格参数 */
.detail-correction{
    padding:8px 0 8px 10px;
    zoom:1;
    background-color:#fff;

    b {
        display:inline-block;
        @include sprite-warring; 
        margin-right:5px;
        vertical-align: middle;
        *zoom:1;
    }
    a {
        color:#005ea7;
    }
}

/* 规格参数表格 */
.Ptable {margin:10px 0; }
.Ptable-item{border-bottom:1px solid #eee;padding:12px 0;line-height:220%;color:#999;font-size:12px;}
.Ptable-item:after{
    content: "";
    height: 0;
    visibility: hidden;
    display: block;
    clear: both;
}
.Ptable-item h3{font-weight: normal;width:110px;text-align:right;float:left;font-size:12px;}
.Ptable-item dl{margin-left:110px;}
.Ptable-item dt{width:160px;float:left;text-align:right;padding-right:5px;}
.Ptable-item dd{margin-left:210px;}
.Ptable-item .Ptable-tips{position: relative;float:left;width:auto; margin-left: 0;}
.Ptable-item .Ptable-tips:hover{z-index:2;}
.Ptable-item .Ptable-sprite-question{
    display: inline-block;
    margin-left: 4px;
    width: 16px;
    height: 16px;
    vertical-align: -3px;
    background: url(i/sprite.png) no-repeat;
}
.Ptable-tips .tips{
    display: none;
    position: absolute;
    left: -10px;
    top: 27px;
    width: 300px;
}
.Ptable-tips:hover .tips{
    display: block;
}
.Ptable-tips .content{
    padding: 8px 10px;
    background: #fff;
    border: 1px solid #cecbce;
    box-shadow: 0 0 2px 2px #eee;
}
.Ptable-tips p{
    font-family: "microsoft yahei";
    color: #999;
    line-height: 160%;
    text-align: left;
}
.Ptable-tips .Ptable-sprite-arrow{
    position:absolute;
    overflow: hidden;
    left: 15px;
    top: -5px;
    width: 11px;
    height: 6px;
    background: url(i/sprite.png) no-repeat -19px 0;
}




.package-list{padding:12px 0;line-height:220%;color:#999;font-size:12px;margin-top:-1px;}
.package-list p{margin-left:155px;padding-right:50px;}
.package-list h3{font-weight: normal;width:130px;text-align:right;float:left;font-size:12px;}

.item-detail-copyright strong{ display: inline-block; }
#tuan-shouhou p { margin:5px 0; line-height:150%; }
.item-warnning { display:inline-block; padding:1px 5px; color:#e4393c; background:#ffe8e8; border:1px solid #db9a9a; margin-left:10px; *zoom:1; }
.item-warnning s { float:left; width:16px; height:16px; margin:1px 4px 0 0; background-position:-100px -267px; }

/* 详情模板样式 */
.formwork{overflow:hidden;width:100%;padding:10px 0;border-bottom:1px dashed #e6e6e6;line-height:23px;text-align:left;font-family:Arial,Helvetica,sans-serif;font-size:14px;}
.formwork p{margin:0;padding:0;}
.formwork_img{width:750px;margin:0 auto;text-align:center;}
.formwork_titleleft{line-height:25px;font-size:14px;font-weight:bold;}
.formwork_titleleft2{line-height:25px;font-size:14px;}
.formwork_titlecenter{line-height:25px;text-align:center;font-size:14px;font-weight:bold;}
.formwork_text{width:100%;text-indent:2em;}
.formwork_imgleft,.formwork_imgleft1,.formwork_tpl_left,.formwork_tpl_imgleft,.formwork_tpl_imgright,.formwork_tpl_left1{float:left;}
.formwork_imgleft{width:200px;padding:10px 20px 10px 0;}
.formwork_imgright{float:right;width:200px;padding:10px 0 10px 20px;}
.formwork_imgleft1{width:360px;padding:10px 20px 10px 0;}
.formwork_imgright1{float:right;width:360px;padding:10px 0 10px 20px;}
.formwork_tpl_left{width:375px;}
.formwork_tpl_imgleft{width:150px;padding:10px 20px 10px 0;}
.formwork_tpl_imgright{width:150px;padding:10px 20px 10px 10px;}
.formwork_tpl_left1{width:375px;text-align:center;}
.formwork_tpl_img{width:100%;padding:20px;}
.formwork_title1{line-height:25px;text-align:center;font-size:14px;font-weight:bold;}

/*formwork bt*/
// .formwork_bt_dz,.formwork_bt_rb,.formwork_bt_it,.formwork_bt_top{width:750px;height:43px;background-image:url("//misc.360buyimg.com/product/skin/2013/i/20130604A.png");background-repeat:no-repeat;}
// .formwork_bt_dz span,.formwork_bt_rb span,.formwork_bt_it span,.formwork_bt_top span{float:left;padding-left:10px;line-height:25px;font-family:"microsoft yahei";font-size:14px;}
// .formwork_bt_dz span.s2,.formwork_bt_rb span.s2,.formwork_bt_it span.s2,.formwork_bt_top span.s2{padding-left:4px;line-height:20px;font-size:12px;}
// .formwork_bt{overflow:hidden;width:753px;padding:10px 0;line-height:23px;text-align:left;font-size:14px;font-family:Arial,Helvetica,sans-serif;}
// .formwork_bt_dz{background-position:0 0;}
// .formwork_bt_dz span{color:#FFF;padding-top:14px; }
// .formwork_bt_dz span.s2{padding-top:18px; }
// .formwork_bt_rb{background-position:0 -90px;}
// .formwork_bt_rb span{font-size:18px;color:#C90014;padding-left:2px;padding-top:12px;}
// .formwork_bt_rb span.s2{color:#666666;padding-left:10px;line-height:25px;padding-top:16px;}
// .formwork_bt_it{background-position:0 -45px;}
// .formwork_bt_it span{color:#000;padding-top:8px;}
// .formwork_bt_it span.s2{line-height:23px;padding-top:10px;}
// .formwork_bt_top{background-position:0 -135px;}
// .formwork_bt_top span{color:#000;padding-top:10px;}
// .formwork_bt_top span.s2{padding-top:14px;}

/* 商品详情 */

// 商品详情顶部fixed导航
#detail {
    .tab-main{
        position: relative;
        overflow: visible;
        &.pro-detail-hd-fixed{
            position: fixed;
            top: 0;
            z-index: 30;
            width: 850px;

            .root61 & {
                width: 850px;
                background: #fff;
            }
        }
    }
}
/**
 * .nav-qrcode
 */
$qrcodeDropdownHeight: 30px;
.nav-qrcode {
    // @override
    .EDropdown {
        .head {
            height: $qrcodeDropdownHeight;
        }
        .content {
            top: $qrcodeDropdownHeight;
            right: 0;
            left: auto;
        }
    }

    .icon-qr {
        margin-left: 10px;
        @include inline-block;
        @include icons(16px, 16px);
        background: url(i/qr-thumb.png) 0 0 no-repeat;
        vertical-align: middle;
    }
}
/**
 * .addcart-mini
 */
.addcart-mini {
    display: none;
    .hover .content {
        z-index: 1
    }
    .EDropdown {
        .content {
            top: 26px;
            right: 1px;
            left: auto;
        }
    }
    .mini-product-info {
        background-color: #FFF;
        width: 224px;
        height: 101px;
        padding: 10px;
        border: 1px solid #ccc;
        @include clearfix;
        .p-img {
            margin-right: 10px;
        }
    }
}

#J-detail-pop-tpl-top-new {
  .zndz-module-pc-wrap.zndz-format-wrap {
    width: auto;
  }
}

.detail-content{
    position: relative;
    // margin-top:24px;
    // margin-bottom:10px;
    //border:1px solid #ddd;
    background: #f7f7f7;
}
.detail-content-wrap{
    width: 100%;
    float:left;
    background-color:#fff;
    border-top: 1px soild #eee;

    .detail-content-item{
        width: 100%;
        contain: content; 
    }

    .content_tpl{
        width: 750px;
        margin: 0 auto;
    }
}
.detail-video-con{
    padding: 20px 0;
    .video-wrap{
        width: 750px;
        height: 422px;
        margin: 0 auto;
        position: relative;
    }
    .video-player,.video-introduce{
        width: 750px;
        height: 422px;
    }
    .video-introduce{
        position:absolute;
        left:0;
        top:0
    }

    .play-btn {
        width: 42px;
        height: 42px;
        background: url(i/__sprite.png) -42px -0px;
        position: absolute;
        left: 20px;
        bottom: 20px;
    }
    .video-list{
        position: relative;
        width: 750px;
        height: 144px;
        overflow: hidden;
        zoom: 1;
        margin: 7px auto 0 auto;
        .ui-switchable-panel-main {
            position: relative;
            width: 756px;
            height: 144px;
            overflow: hidden;
        }
        .playing .mask,.playing .video-play{
            display: block;
        }
        .list-prev,.list-next{
            position: absolute;
            z-index: 5;
            top: 0;
            bottom: 32px;
            width: 24px;
            padding-top: 47px;
            background: rgba(0,0,0,.5);
            i{
                display: block;
                margin: 0 auto;
            }
            .sprite-prev{
                width: 9px;
                height: 17px;
                background-image: url(i/__sprite.png);
                background-position: -9px -42px;
            }
            .sprite-next{
                width: 9px;
                height: 17px;
                background-image: url(i/__sprite.png);
                background-position: -0px -42px;
            }
            &.disabled {
                display: none;
            }
        }
        .list-prev{
            left: 0;
        }
        .list-next{
            right: 7px;
        }
        //ul{
        //width: 2000px;
        //.play,.pause{
        //    .mask,.video-play,.video-pause{
        //        display: block;
        //    }
        //}
        //}
        li{
            width: 189px;
            //margin-right: 7px;
            float: left;
            position: relative;
            &:hover{
                .mask,.video-play,.video-pause{
                    display: block;
                }
            }
            img {
                margin-right: 7px;
            }
        }
        .tips{
            height: 18px;
            line-height: 18px;
            padding: 0 5px;
            color: #fff;
            border-radius: 3px;
            position: absolute;
            z-index: 4;
            left: 4px;
            top: 4px;
            &.live {
                background: #13ab41;
            }
            &.playback {
                background: #ed444c;
            }
        }
        .mask{
            display: none;
            position: absolute;
            left: 0;
            top: 0;
            width: 182px;
            height: 108px;
            background: rgba(0,0,0,.5);
        }
        .title {
            line-height: 32px;
            text-align: center;
            background: #ededed;
            color: #242424;
            display: block;
            margin-right: 7px;
            font-size: 14px;
        }
        .video-play{
            display: none;
            width: 42px;
            height: 42px;
            background: url(i/__sprite.png) -42px -0px;
            position: absolute;
            z-index: 2;
            top: 50%;
            left: 50%;
            margin: -37px 0 0 -21px;
        }
        .video-pause{
            display: none;
            width: 42px;
            height: 42px;
            background-image: url(i/__sprite.png);
            background-position: -0px -0px;
            position: absolute;
            z-index: 2;
            top: 50%;
            left: 50%;
            margin: -37px 0 0 -21px;
        }
    }
}
.detail-content-nav{
    display: none;
    position: absolute;
    right:0;
    top: 0;
    width:72px;

    .detail-update{
        display: block;
        position: relative;
        padding-left: 36px;
        width: 62px;
        height: 32px;
        line-height: 32px;
        color: #fff;
        background: #6e9cd0;
        margin: 10px auto 0;

        i{
            position: absolute;
            width: 20px;
            height: 20px;
            background: url(/css/i/item.sprite.png) no-repeat -30px -48px;
            display: block;
            left: 8px;
            top: 5px;
        }

        &:hover{
            text-decoration: none;
            background: #5289c7;

            i{
                background-position: 0 -48px;
            }
        }
    }

    .book-qrcode{
        padding-top:10px;
        .imgbox{
            width: 98px;
            height: 98px;
            margin: 0 auto;
            img{
                width: 98px;
                height: 98px;
            }
        }
        .text{
            padding-top: 5px;
            text-align: center;
        }
    }

    &.fixed-top {
        position: fixed;
        top: 36px;
        right:50%;
        margin-right: -605px;
    }
}
$tabBgRed: #e4393c;

.detail-content-tab{
    //float:left;
    font-size:12px;
    li{
        position:relative;
        padding:15px 0 15px 15px;
        i{display:none;}

    }
    .current{
        background-color: $tabBgRed;
        a {
            color:#fff;
        }
        i {
            display: block;
        }
        .arrow-l{
            width:7px;
            height:13px;
            position:absolute;
            left:-7px;
            top:17px;
            background: url(i/arrow.png) no-repeat;
        }
    }
}
.service-pic{
    text-align: center;
    padding-top: 10px;

    img {
      width: 100%;
    }
}


.root61{
    .z-have-detail-nav{
        padding-right:157px;
        .detail-content-nav{
            display: block;
            background: #f7f7f7;
        }
        .detail-content-wrap{
            width: 990px;
            //border-right: 1px solid #ddd;
            .content_tpl{
                width: 753px;
            }
        }
    }
}

.root61{
    .detail {
        //.tab-main{
        //    width: 990px;
        //}

        .p-parameter{padding:0 10px 10px;}
        .p-parameter-list{
            padding: 20px 0 15px;
            li{
                width: 200px;
                .detail{
                    width: 197px;
                }
            }
        }
    }
    .addcart-mini {
        display: block;
    }
}

/* 售后保障 */
#state strong {
    color: $colorPriceRed;
}
.serve-agree-bd{
    margin-bottom: 24px;
    dt{
        display: flex;
        align-items: center;
        font-size:16px;
        font-weight: 600;
        color: rgba(26, 26, 26, 1);
        i{
            @include inline-block();
            width:20px;
            height:20px;
            margin-right:8px;
            background: url(https://img14.360buyimg.com/imagetools/jfs/t1/256217/21/11772/764/67820d15F6f744ce2/1c142ba9d43c7b7b.png) left top / 100% 100% no-repeat;
        }
        .goods{
            // @include sprite-iconZP;
            margin-bottom: 0;
        }
        // .unprofor{
        //     @include sprite-iconLB;
        // }
        // .no-worries{
        //     @include sprite-iconWY;
        // }
    }
    dd{
        display:block;
        padding:10px 0;
        line-height: 18px;
        margin-left: 28px;
        font-size: 14px;
        color: rgba(136, 139, 148, 1);
        line-height: 18px;
        a{
            color: $colorLinkBlue;
        }
    }
    .no-worries-text{padding-bottom:0px;}
}

#J-detail-banner {
    text-align: center;
    img {
        width: 750px;
    }
}
#detail .tab-con:after {
    content: "";
    height: 0;
    visibility: hidden;
    display: block;
    clear: both;
}
#detail .tab-con{
    zoom: 1;
}

.root61 {
    #J-detail-banner {
        img {
            width: 990px;
        }
    }
}

// 关注品牌
#parameter-brand {
    padding-bottom: 0;
    li {
        width: 50%;
    }
}
.follow-brand {
    margin-left: 5px;
    .p-parameter .p-parameter-list & {
        color: #666;
    }
    b {
        color: #e4393c;
        font-size: 22px;
        font-family: simsun;
        vertical-align: middle;
        padding-right: 3px;
    }
}
/*book*/
.ebook{
    .detail-content-wrap{
        // .content_tpl{
        //     width: auto;
        //     padding: 0 30px;
        // }
        // .formwork{
        //     border: none;
        // }
        .formwork_bt{
            // width: auto;
            // padding-top: 0;
            // _width: 770px;
            padding: 24px 0;
            border-bottom: 1px solid #dfdfdf;

            img {
              max-width: 100%;
            }
        }

        .formwork_bt:last-child{
          border-bottom: none;
        }
      
        .formwork_bt .more {
            // display: none;
        }
        .formwork_bt .more a {
            // color: #828ABF;
        }

        .con {
          color: rgba(26, 26, 26, 1);
          font-size: 14px;
          line-height: 22px;
        }

        .formwork_bt_it{
          font-size: 18px;
          font-weight: 600;
          color: rgba(26, 26, 26, 1);
          margin-bottom: 24px;
            // width: auto;
            // height: auto;
            // background: none;
            // border-bottom: 1px dotted #666;
            // padding: 0 0 5px;
            // margin-bottom: 10px;
            // span{
            //     display: block;
            //     line-height: 35px;
            //     float: none;
            //     color: #e4393c;
            //     font-weight: bold;
            //     border-bottom: 1px dotted #666;
            // }
            // .s2{
            //     display: none;
            // }
        }
    }
}
// .root61{
//     .ebook{
//         .detail-content-wrap {
//             .detail-content-item {
//                 width: auto;
//                 padding: 0 70px 0 20px;
//             }
//         }
//     }
// }

/* 品质生活 */
.quality-life {
    // margin-top: 10px;
    margin-bottom: 10px;
    // border: 1px solid #e3dddd;

    @include clearfix;
    .q-logo {
        float: left;
        width: 250px;
    }
    ul {
        float: left;
    }

    .fresh-ico-3 {
        .fresh-wd {
            font-size: 18px;
        }
    }
    li {
        float: left;
        display: inline;
        width: 100px;
        text-align: center;
        padding-top: 5px;
        margin-right: 20px;
        position: relative;
        span {
            display: block;
        }
        .fresh-wd {
            position: absolute;
            top: 20px;
            left: 0;
            width: 100%;
            text-align: center;
            color: #df7368;
        }
        .fresh-ico-1 {
            cursor: default;
        }
    }
    i {
        @include inline-block;
        width: 45px;
        height: 45px;
        margin-bottom: 5px;
        vertical-align: middle;
        background: url(i/quality-life.png) 0 0 no-repeat;
    }
    .ql-ico-3 i {
        background-position: 0 0;
    }
    .ql-ico-2 i {
        background-position: 0 -150px;
    }
    .ql-ico-5 i {
        background-position: 0 -50px;
    }
    .ql-ico-1 i {
        background-position: 0 -100px;
    }
    .ql-ico-4 i {
        background-position: 0 -200px;
    }
    .ql-ico-10 i {
        background-position: 0 -300px;
    }
    .ql-ico-yuan i {
        background-position: 0 -350px;
    }
    // 只要边框，没有 icon
    .fresh-ico-1 i {
        background-position: 0 -401px;
    }

    .fresh-ico-3 {
        i { background-position: 0 -451px;}
        .fresh-wd {display: none;}
    }

    .fresh-ico-2 i {
        background-position: 0 -200px;
    }
}

.root61 .quality-life li {
    // margin-right: 80px;
}

.detail-video-con{
    padding: 20px 0;
    .video-wrap{
        width: 750px;
        height: 422px;
        margin: 0 auto;
        position: relative;
    }
    .video-player{
        width: 750px;
        height: 422px;
    }
    .play-btn {
        position: absolute;
        left: 20px;
        bottom: 20px;
        @include sprite-play;
    }
    .video-list{
        position: relative;
        width: 750px;
        height: 144px;
        overflow: hidden;
        zoom: 1;
        margin: 7px auto 0 auto;
        .ui-switchable-panel-main {
            position: relative;
            width: 756px;
            height: 144px;
            overflow: hidden;
        }
        .list-prev,.list-next{
            position: absolute;
            z-index: 5;
            top: 0;
            bottom: 32px;
            width: 24px;
            padding-top: 47px;
            background: rgba(0,0,0,.5);
            i{
                display: block;
                margin: 0 auto;
            }
            .sprite-prev{
                @include sprite-prev;
            }
            .sprite-next{
                @include sprite-next;
            }
            &.disabled {
                display: none;
            }
        }
        .list-prev{
            left: 0;
        }
        .list-next{
            right: 7px;
        }
        //ul{
        //width: 2000px;
        //.play,.pause{
        //    .mask,.video-play,.video-pause{
        //        display: block;
        //    }
        //}
        //}
        li{
            width: 189px;
            //margin-right: 7px;
            float: left;
            position: relative;
            &:hover{
                .mask,.video-play,.video-pause{
                    display: block;
                }
            }
            img {
                margin-right: 7px;
            }
        }
        .tips{
            height: 18px;
            line-height: 18px;
            padding: 0 5px;
            color: #fff;
            border-radius: 3px;
            position: absolute;
            z-index: 4;
            left: 4px;
            top: 4px;
            &.live {
                background: #13ab41;
            }
            &.playback {
                background: #ed444c;
            }
        }
        .mask{
            display: none;
            position: absolute;
            left: 0;
            top: 0;
            width: 182px;
            height: 108px;
            background: rgba(0,0,0,.5);
        }
        .title {
            line-height: 32px;
            text-align: center;
            background: #ededed;
            color: #242424;
            display: block;
            margin-right: 7px;
            font-size: 14px;
        }
        .video-play{
            display: none;
            @include sprite-play;
            position: absolute;
            z-index: 2;
            top: 50%;
            left: 50%;
            margin: -37px 0 0 -21px;
        }
        .video-pause{
            display: none;
            @include sprite-pause;
            position: absolute;
            z-index: 2;
            top: 50%;
            left: 50%;
            margin: -37px 0 0 -21px;
        }
    }
}

/*building*/
.building{
    #detail{
        .pro-detail-hd-fixed{
            width: 850px;
        }
    }
    #J-detail-content{
        img{
            margin: 0 auto;
            display: block;
        }
    }
}
.root61{
    .building{
        #detail{
            .tab-main {
                &.pro-detail-hd-fixed {
                    width: 1210px;
                }
            }
        }
    }
}
/*书评*/
.book-review{
    padding: 20px 0 10px;
    .has-more {
        .more {
            display: block;
        }
    }
    .open {
        height: auto;
        overflow:auto;
    }
    .close {
        height: 120px;
        overflow:hidden;
    }
    .review-item{
        padding: 0 0 20px 10px;
        .user-line{
            padding-left: 25px;
            .avatar{
                display: inline-block;
                width: 40px;
                height: 40px;
                border-radius: 20px;
                overflow: hidden;
                vertical-align: middle;
                img{
                    width: 40px;
                    height: 40px;
                }
            }
            .name{
                color: #000;
                vertical-align: middle;
                margin: 0 20px 0 10px;
            }
            .star {
                display: inline-block;
                vertical-align: middle;
                width: 78px;
                height: 14px;
                background: url(../comment/i/star.png) no-repeat;
            }
            .star0 {
                background-position: -80px 0;
            }
            .star1 {
                background-position: -64px 0;
            }
            .star2 {
                background-position: -48px 0;
            }
            .star3 {
                background-position: -32px 0;
            }
            .star4 {
                background-position: -16px 0;
            }
            .star5 {
                background-position: 0 0;
            }
            .time{
                float: right;
                color: #999;
                padding-top: 10px;
            }
        }
    }
    .review-content {
        line-height: 2em;
    }
    .review-con {
        background: #f9f9f9;
        padding: 8px 18px;
        line-height: 220%;
        border-radius: 8px;
        margin-top: 10px;
        position: relative;
        .arrowUp{
            position: absolute;
            top: -8px;
            left: 40px;
            width: 0;
            height: 0;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 10px solid #f9f9f9;
        }

        .more {
            text-align: right;
            a{
                color: #5e69ad;
            }
        }
    }
    .ui-page-wrap{
        text-align: right;
    }
}

/*yiyao*/
.item-detail {

}
.service-rule {
    padding: 10px 20px;
    line-height: 160%;
    h3 {
        font-size: 14px;
        color: #e4393c;
        padding-bottom: 10px;
    }
    p {
        text-indent: 2em;
    }
    .rule-con {
        padding-top: 8px;
        h4 {
            color: #e4393c;
        }
         ol {
            padding-top: 4px;
        }
        li{
            list-style-type: decimal;
            list-style-position: inside;
            padding-left: 10px;
        }
        strong {
            display: block;
            font-weight: 400;
            margin-bottom: 8px;
            padding-left: 10px;
            span{
                font-weight: 700;
            }
        }
    }
}

//二手提示
.er-same-tip {
    padding: 4px 10px;
    border: 1px solid #ccc;
    display: inline-block;
    margin-left: 10px;
    margin-top: 10px;
}
.pro-detail-hd-fixed-div{
    // position: fixed;
    // width: 100%;
    // border-bottom: solid 1px #dbdbdb;
    // left: 0;
    // top: 0;
    // height: 36.5px;
    // height: 38px\0;
    // background: #fff;
    // display:none;
    // z-index:3;
}

//处理横向滚动条bug
#J-detail-pop-tpl-top-new{
  overflow:hidden;
}

.goods-base {
  display: flex;
  flex-wrap: wrap;
  line-height: 1;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
}

.goods-base .item {
  display: flex;
  height: 48px;
  width: 50%;
  box-sizing: border-box;
}

.goods-base .exclusive-row {
  width: 100%;
  border-bottom: none;

  .adaptive {
    border-bottom: none;
  }
}

.goods-base .item:nth-last-child(1) .text {
  border-bottom: none;
}

.goods-base .item:nth-last-child(2) .flex-center {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.goods-base .flex-center {
  display: flex;
  align-items: center;
  width: 142px;
  background: rgba(249, 250, 252, 1);
  color: rgba(26, 26, 26, 1);
  box-sizing: border-box;
}

.goods-base .name {
  margin: 0 4px 0 16px;
  font-weight: 600;
  font-size: 14px;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 18px;
  position: relative;
}

.goods-base .adaptive {
  display: flex;
  align-items: center;
  font-size: 14px;
  flex: 1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  border-left: 1px solid rgba(0, 0, 0, 0.06);
  line-height: 18px;
  word-break: break-all;
  padding: 0 24px;
  box-sizing: border-box;
}

.goods-base .text {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  position: relative;
  color: rgba(26, 26, 26, 1);

  a{ 
    color: rgba(26, 26, 26, 1);
  }
}

.goods-base .tips {
  width: 14px;
  height: 14px;
  display: block;
  background: url(https://img14.360buyimg.com/imagetools/jfs/t1/266050/1/9639/934/677e1eedFe7b35b35/8c6b919766c364c0.png) left top / 100% 100% no-repeat;
  cursor: pointer;
  position: relative;
  line-height: 18px;
}


.industrial-introduction {
  width: 100%;
  display: block;
}

.popover {
  position: fixed;
  z-index: 1000;
  width: 282px;
  text-align: center;
}

.popover-content {
  padding: 12px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  font-size: 16px;
  color: rgba(80, 82, 89, 1);
  display: inline-block;
  background: #fff;
  font-size: 14px;
  font-weight: 400;
}

.popover.active {
  display: block;
}


// 别删 重置 loading 图用的
#comment > img {
  width: 100%;
}

#answers > img {
  width: 100%;
}

#expert-selection > img {
  width: 100%;
}

#tying-sale-recommend > img {
  width: 100%;
}

#mayalsolike > img {
  width: 100%;
}
