/* combine share */
.combine-share {
    width: 500px;
    padding: 10px 0 0 10px;
}
.combine-share dl {
    padding-bottom:5px;
}
.combine-share p {
    margin-bottom:5px;
}
.combine-share .s-btn-ok {
    border: 1px solid #bfd6af;
    background:#F6FBF3;
    background-image: -ms-linear-gradient(top, #F6FBF3 0%, #EEF7E7 100%);
    background-image: -moz-linear-gradient(top, #F6FBF3 0%, #EEF7E7 100%);
    background-image: -o-linear-gradient(top, #F6FBF3 0%, #EEF7E7 100%);
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #F6FBF3), color-stop(1, #EEF7E7));
    background-image: -webkit-linear-gradient(top, #F6FBF3 0%, #EEF7E7 100%);
    background-image: linear-gradient(to bottom, #F6FBF3 0%, #EEF7E7 100%);
}
.combine-share .s-btn-gray {
    border: 1px solid #ddd;
    background:#F7F7F7;
    background-image: -ms-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    background-image: -moz-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    background-image: -o-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #F7F7F7), color-stop(1, #F3F2F2));
    background-image: -webkit-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    background-image: linear-gradient(to bottom, #F7F7F7 0%, #F3F2F2 100%);
}
.combine-share .s-btn {
    padding:2px 10px;
    border-radius: 2px;
    display:inline-block;
    color:#333;
    *zoom:1;
}
.combine-share .s-btn:hover {
    text-decoration:none;
    color:#333;
}

.combine-share .mt label {
    float:left;
    display:inline;
}
.combine-share .mt label input {
    float:left;
    display:inline;
    position:relative;
    top:-2px;
}
.combine-share .mc textarea {
    width:494px;
    height: 78px;
    margin-bottom:5px;
    font-family:Arial,Verdana;
}
.combine-share .share-meta {
    margin-bottom:5px;
}
.combine-share .mt dd em {
    float:left;
    display:inline;
    width: 15px;
    height: 15px;
    margin-right:10px;
    line-height: 100px;
    overflow: hidden;
    background-image:url(i/combine-share.png);
    background-repeat:no-repeat;
}
.combine-share #share-ico-sina  { background-position:0px -30px; }
.combine-share #share-ico-qqZone{ background-position:-25px -30px; }
.combine-share #share-ico-qqWeibo { background-position:-50px -30px; }
.combine-share #share-ico-renren  { background-position:-75px -30px; }
.combine-share #share-ico-kaixin  { background-position:-100px -30px; }
.combine-share #share-ico-douban  { background-position:-125px -30px; }
.combine-share #share-ico-sina.actived  { background-position:0px 0px; }
.combine-share #share-ico-qqZone.actived    { background-position:-25px 0px; }
.combine-share #share-ico-qqWeibo.actived { background-position:-50px 0px; }
.combine-share #share-ico-renren.actived  { background-position:-75px 0px; }
.combine-share #share-ico-kaixin.actived  { background-position:-100px 0px; }
.combine-share #share-ico-douban.actived  { background-position:-125px 0px; }

.combine-share .mt dd .actived {
    cursor:pointer;
}
.combine-share .share-link a {
}
.combine-share .share-link .s-btn b {
    padding:0;
    margin-right:2px;
    width: 16px;
    height: 7px;
    overflow: hidden;
    background: url(i/combine-share.png) -158px -87px no-repeat;
    *position:relative;
    *top: -2px;
}
.combine-share .overflow {
    color:#E4393C;
}
.combine-share .share-imgScroll {
    position:relative;
    margin-bottom:20px;
    width:500px;
}
.combine-share .share-img-wrap {
    margin-left:20px;
}
.combine-share .share-imgScroll ul li {
}
.combine-share .share-imgScroll ul li a {
    position:relative;
    width:115px;
    float:left; display:inline;
}
.combine-share .share-imgScroll ul li a s{
    display:none;
}
.combine-share .share-imgScroll ul li.selected a s {
    position:absolute;
    bottom:10px;
    right: 20px;
    display: inline-block;
    width: 16px;
    height: 16px;
    background:url(i/combine-share.png) -25px -48px no-repeat;

    *zoom: 1;
}
.combine-share .share-imgScroll ul li img {
    width: 87px;
    height: 87px;
    padding:5px;
    border: 1px solid #ccc;
    margin: 0 8px;
}
.combine-share .share-imgScroll ul li a:hover img,
.combine-share .share-imgScroll ul li.selected a img {
    border: 2px solid #e3393c;
    padding:4px;
}

.combine-share .share-imgScroll .s-btn s {
    display: inline-block;
    width: 5px;
    height: 9px;
    overflow: hidden;
    background: url(i/combine-share.png) 0px -52px no-repeat;
    margin-top:45px;
    cursor: pointer;
    *zoom: 1;
}
.combine-share .share-imgScroll .disabled s {
    cursor: default;
}
.combine-share .share-imgScroll .s-btn {
    position:relative;
    z-index:2;
    display: block;
    padding: 0;
    width: 15px;
    height: 98px;
    position:absolute;
    top:0;
    left: 0;
    text-align:center;
}
.combine-share #share-img-next {
    right:0;
    left: auto;
}
.combine-share #share-img-next s { background-position:-8px -52px }

.combine-share .share-btn {
    text-align:center;
    padding-bottom:10px;
}
.combine-share .share-btn-ok {
    font-size: 14px;
    font-weight:bold;
    padding: 4px 15px;
}
.combine-share dd h5 strong {
    position:relative;
}
.combine-share s.share-ico {
    display:block;
    position:absolute;
    left:-50px;
    top:0px;
}
.combine-share .share-ico {
    width: 50px;
    height: 42px;
    background-image:url(i/combine-share.png);
    background-repeat:no-repeat;
}
.share-binding dl,
.share-warning dl {
    padding:0px 20px 10px 20px;
}
.combine-share h5 {
    height:42px;
    font: bold 18px/42px 'microsoft yahei';
}
div.share-warning {
    width: auto;
}
div.share-binding {
    width: auto;
}
div.share-binding .share-btn {
    text-align:left;
}
div.share-binding p {
    margin-bottom:10px;
}
.share-msg h5 { color:#7abd54; }
.share-warning h5 { color:#ff6600; }
.share-msg .share-ico {
    background-position:0px -67px
}
.share-warning .share-ico {
    margin-right:10px;
    background-position:-74px -68px
}
