define('MOD_ROOT/buybtn/reservation', function(require, exports, module) {
    var Countdown = require('MOD_ROOT/common/tools/tools').Countdown;
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Tools = require('MOD_ROOT/common/tools/tools');
    var Login = require("JDF_UNIT/login/1.0.0/login");
    var G = require('MOD_ROOT/common/core');
    var Conf = require('PUBLIC_ROOT/conf');
    require('JDF_UI/dialog/1.0.0/dialog');
    require('MOD_ROOT/ETooltips/ETooltips');

    /**
     * polling
     * @param {Number} delay frequency
     * @param {Function} callback
     */
    function polling(delay, callback) {
        if (typeof delay === 'number' &&
            typeof callback === 'function') {
            setTimeout(function () {
                var r = callback();
                if (!r) {
                    polling(delay, callback); 
                }
            }, delay);
        } else {
            throw 'Invalid params.';
        }
    }

    function showInvite(cfg) {
        var flag = cfg.isPinGou ? '1' : '3';
        var title = cfg.isPinGou ? '邀请好友' : '分享';
        var url = '//fenxiang.jd.com/shareFront/initShareIcons.action?sku='+ cfg.skuid +'&flag=' + flag;

        Login({
            modal: true,
            complete: function(r) {
                if (r && r.Identity && r.Identity.IsAuthenticated) {
                    showShareDialog();
                }
            }
        });
        function showShareDialog() {
            $('body').dialog({
                title: title,
                type: 'iframe',
                source: url,
                width: 600,
                height: 200
            });
        }
    }

    // 预约商品按钮
    var Reservation = {
        init: function (cfg) {
            $('.J-yuyue-share').before('<span class="yuyue-text J-yuyue-text"></span>')
            var _mthis=this;
            this.$el = $('#btn-reservation,#btn-reservation-mini');
            this.$banner = $('#yuyue-banner');
            this.$process = $('#yuyue-process');
            this.$textTip = $('.J-yuyue-text')
            this.jnbtBtn = cfg.jnbtBtn;
            this.cfg = cfg;
            this.isPlusProduct = false;
            Reservation.isNoBuy=true;
            Reservation.isRealPlus= false;
            // 如果是预约
            if (this.cfg.isYuYue) {
                this.$count = this.$banner.find('.J-count');
                this.$time = this.$banner.find('.J-time');
                this.$price = $('.J-summary-price .p-price').hide();  // 为了用户体验，预约商品先隐藏价格内容，然后根据接口标识条件进行渲染

                this.bindEvent();
                this.get();
            }
        },
        invite: function () {
            return showInvite(this.cfg);
        },
        bindEvent: function () {
            var _this = this;

            // $(document).delegate('.J-yuyue-share', 'click.pingou_invite', function () {
            //     _this.invite();
            // });
            $('.J-yuyue-tips .J-tips').unbind('click').bind('click', function () {
                if ($('#detail').length) {
                    $('html,body').scrollTop($('#detail').offset().top);
                }
                $('#pingou-rules-tab').trigger('click');
            });

            Event.addListener('onStockReady', function (data) {
                var jnbtBtn = _this.cfg.jnbtBtn;
                var data = data && data.stock && data.stock.data
                var pid =data.stockInfo && data.stockInfo.area && data.stockInfo.area.provinceId;
                _this.cfg.havestock = data.stockInfo && data.stockInfo.isStock
                if (_this.cfg.havestock) {
                    jnbtBtn.enabled(data);
                } else {
                    jnbtBtn.disabled();
                }

                // 北京地区支持节能补贴
                if (pid == "1" && _this.ysSupportJnbt) {
                    jnbtBtn.show();
                } else {
                    jnbtBtn.hide();
                }

                if ( _this.state === 4 && _this.type != "5") {//口罩抽签type==5
                    if (_this.cfg.havestock) {
                        _this.enabled(data);
                    } else {
                        _this.disabled();
                    }
                }

                // //通过返回数据isPlus判断是否是会员，来展示促销plus文案
                // var isPlus = (data && data.isPlus) || false;
                // Reservation.isPlus = isPlus;
                // //Promotions.plusTags = data && data.stock && data.stock.stock && data.stock.stock.jdPrice && data.stock.stock.jdPrice.ext;
                // try {
                //     Reservation.isRealPlus = ("1" ==  data.data.isPlusMember);// 1-PLUS正式会员,2-PLUS试用会员,3-试用过期,4-正式过期,5-未试用状态,-1=其他状态
                // } catch (err) {
                //     Reservation.isRealPlus = false;
                // }
            });

            // Event.addListener('onLDPSelected', function (data) {
            //     _this.LDP = data.did;
            //     _this.enabled()
            // });

            Event.addListener("onStockReady", function (data) {
                onPromR(data);
            })

            function onPromR(data) {
                var data = data && data.stock && data.stock.data
                var category = data && data.yuyueInfo && data.yuyueInfo.type
                this.type = category
                try {
                    var isPlusLimit = (data.promotion.limitBuyInfo.resultExt.isPlusLimit == 1);// isPlusLimit=1，则为专享购，反之，不是专享购。
                } catch (err) {
                    var isPlusLimit = false;
                }
                //var isPlus = Reservation.isRealPlus;
                //Promotions.plusTags = data && data.stock && data.stock.stock && data.stock.stock.jdPrice && data.stock.stock.jdPrice.ext;
                try {
                    var isPlus = Reservation.isRealPlus = ("1" ==  data.isPlusMember);// 1-PLUS正式会员,2-PLUS试用会员,3-试用过期,4-正式过期,5-未试用状态,-1=其他状态
                } catch (err) {
                    var isPlus = Reservation.isRealPlus = false;
                }
                var isMsZxg=true;
                // console.log("isPlusLimit",isPlusLimit)
                // console.log("unSupportedArea",!_this.cfg.unSupportedArea)
                if(!Reservation.isNoBuy && !_this.cfg.havestock) // 预约状态4 独立秒杀且无库存
                {
                    isMsZxg=false;
                }
                if (isPlusLimit && !_this.cfg.unSupportedArea && isMsZxg) { // 专享购、地区可售、独秒杀有货
                    var text = category == "5" ? "加入PLUS会员即可获得参与活动资格，中签后才能购买本商品哦~":"加入PLUS会员即可获得本商品抢购资格，库存有限，抢完即止~";
                    $(".J_JoinPlus").remove();
                    $("#yuyue-banner").attr("style","background:url(//img14.360buyimg.com/imagetools/jfs/t1/113418/21/9492/25909/5ed8a23bE59c3eec6/6cd2c9282bf901c5.png) 0 0 no-repeat").find(".activity-type").hide();
                    // handleBanner("//img10.360buyimg.com/imagetools/jfs/t1/100106/16/5507/28230/5dedc52aE9b858e9c/4e787a6565e817ac.png");
                    _this.hideTheSecondPriceFloor();
                    _this.addJoinPlusButton();
                    Tools.checkLogin().done(function (res) {
                        res = res || {};
                        var data = res.Identity || {};
                        var isLogin = data.IsAuthenticated;
                        isLogin && isPlus ?
                            _this.removeTipBar() :
                            _this.addTipBar(text);
                            _this.toggleButton(isLogin ? isPlus : false);
                            // _this.checkState(r);//对预约的5个状态的处理 因为抢购阶段 无库存要对专享购失效
                    }).fail(function () {
                            _this.addTipBar(text);
                            _this.toggleButton(false);
                            // _this.checkState(r);

                    });
                    category == "5" ? $('#btn-reservation,#btn-reservation-mini').hide() : $('#btn-reservation,#btn-reservation-mini').show();
                } else if (isPlusLimit){
                    // 清理 + 还原
                    _this.removeTipBar()
                    $("#yuyue-banner").attr("style","").find(".activity-type").show();
                    $(".J_JoinPlus").remove();
                    category == "5" ? $('#btn-reservation,#btn-reservation-mini').hide() : "";
                    // _this.checkState(r);
                }
                if(isPlusLimit && category == "5" && $(".J-mobile-only").length){
                    $(".J-mobile-only").show();
                    $(".choose-amount").hide();
                }

            }

            /// 预约抢购双价格处理

            function addReservedPrice(price) {
                var $elem = $('#J_YuYuePrice');
                if ($elem.length) {
                    $('.price', $elem).html(price);
                } else {
                    var __html = '\
                        <div class="summary-price" id="J_YuYuePrice"> \
                            <div class="dt">预约价</div> \
                            <div class="dd"> \
                                <span class="p-price"><span>￥</span><span class="price">' + price + '</span></span>\
                            </div>\
                        </div>';
                    $('.J-summary-price').before(__html);
                }
            }

            function removeReservedPrice() {
                $('#J_YuYuePrice').remove();
            }

            function addDelPrice(price) {
                var $elem = $('#J_DelPrice');
                if ($elem.length) {
                    $('del', $elem).html('￥' + price);
                } else {
                    $('.J-summary-price .p-price')
                    .after('<span id="J_DelPrice" style="vertical-align:3px;">[<del>￥' + price + '</del>]</span>');

                }
            }

            function removeDelPrice() {
                $('#J_DelPrice').remove();
            }

            function modifyJDPrice(price) {
                var selector = '.J-p-' + pageConfig.product.skuid;
                $(selector).html(price);
                pageConfig.product.jp = price;  // 主图打开后的价格数据
            }

            function addShoppingCartButton(text) {
                var $elem = $('#J_ShoppingCartButton');
                if ($elem.length) {
                    $elem.html(text);
                    if (!$elem.is(':visible')) {
                        $elem.css('display', '');
                    }
                } else {
                    // var href = '//cart.jd.com/gate.action?pid=' + pageConfig.product.skuid + '&pcount=1&ptype=1';
                    var __html = '<a href="javascript:void(0);" id="J_ShoppingCartButton" class="btn-special2 btn-lg">' + text + '</a>';
                    $('#btn-reservation').after(__html);
                    // var addToCartBtn = pageConfig.product.addToCartBtn;
                    // if (addToCartBtn) {
                    //     addToCartBtn.$el = addToCartBtn.$el.add('#J_ShoppingCartButton');
                    //     addToCartBtn.originHref = href;
                    //     addToCartBtn.href = href;
                    // }
                    $('#J_ShoppingCartButton').ETooltips({
                        close: false,
                        content: 'PC端暂不支持购买，手机下载京东APP，享受商品提前抢！',
                        width: 270,
                        pos: 'bottom',
                        zIndex: 10,
                    })
                }
            }

            function hideShoppingCartButton() {
                $('#J_ShoppingCartButton').hide();
            }

            function setReservationButton(text) {
                var $elem = $('#btn-reservation');
                if ($elem.length) {
                    $elem.html(text);
                } else {
                    console && console.log('操作的节点不存在');
                }
            }

            function handleReservedPrice(reservationData, priceData) {
                var reservationData = reservationData || {};
                var state = reservationData.state;
                var hidePrice = reservationData.hidePrice;
                var showPromoPrice = reservationData.showPromoPrice;
                var sellWhilePresell = reservationData.sellWhilePresell;
                var priceData = priceData || {};
                var userPrice = priceData.p;
                var originalPrice = priceData.op;
                var previewPriceDict = priceData.np || {};
                var previewPriceType = previewPriceDict.type;
                var previewPrice = previewPriceDict.price;
                var text = ({
                    "1": "等待预约",
                    "2": "立即预约",
                    "3": "等待抢购",
                    "4": "抢购",
                    "5": "抢购已结束"
                })[state] || '立即预约';
                if (
                    hidePrice == 0 &&
                    showPromoPrice == 1
                ) {
                    removeReservedPrice();
                    removeDelPrice();
                    hideShoppingCartButton();
                    if (text !== $('#btn-reservation').html()) {
                        setReservationButton(text);
                    }
                    $('#choose-btns').removeClass('extra');
                    // if (
                    //   state <= 3 &&
                    //   previewPriceType == 3 &&
                    //   +previewPrice > 0 &&
                    //   +previewPrice < +userPrice
                    // ) {
                    //     if (sellWhilePresell == 1) {
                    //         $('#choose-btns').addClass('extra');
                    //         addReservedPrice(previewPrice);
                    //         setReservationButton('<span>￥' + previewPrice + '</span><br><stong>' + text + '</stong>');
                    //         addShoppingCartButton('<span>￥' + userPrice + '</span><br><stong>直接购买</stong>');
                    //         $('.addcart-mini .p-price strong').html(previewPrice);
                    //     } else {
                    //         modifyJDPrice(previewPrice);
                    //         addDelPrice(userPrice);
                    //     }
                    // } else if (
                    //     +originalPrice > 0 &&
                    //     +originalPrice > +userPrice
                    // ) {
                    //     addDelPrice(originalPrice);
                    // } else {
                    //     // pass
                    // }
                }
            }



            var flag = true;

            if (Conf.get('BASE.RESERVATION.reservedPriceSignal')) {
                Event.addListener('onPriceReady', function (data) {
                    var priceData = data.price || {};
                    if (_this.__res__) {
                        handleReservedPrice(_this.__res__, priceData);
                    } else {
                        if (flag) {  // 保证一次调用
                            polling(0, function(){
                                if (_this.__res__) {
                                    handleReservedPrice(_this.__res__, priceData);
                                    return true;
                                } else {
                                    return false;
                                }
                            });
                            flag = false;
                        }
                    }
                });
            }
        // 预约抢购双价格展示
        // Event.addListener('onPriceReady', function (data) {
        //     var price = data.price || {};
        //     var p = +price.p;
        //     var op = +price.op;
        //     if (op > p) {
        //         polling(0, function() {
        //             if (_this.__res__) {
        //                 if (
        //                     _this.__res__.showPromoPrice == 1 &&
        //                     _this.__res__.hidePrice == 0
        //                 ) {
        //                     var $mount = $('#J_bookingDoublePrice');
        //                     if ($mount.length) {
        //                         $mount.html('￥' + op);
        //                     } else {
        //                         $('.J-summary-price .p-price')
        //                         .after('<span style="vertical-align:3px;"><del id="J_bookingDoublePrice">￥' + op + '</del></span>');

        //                     }
        //                 }
        //                 return true;
        //             } else {
        //                 return false;
        //             }
        //         });
        //     } else {
        //         var $mount = $('#J_bookingDoublePrice');
        //         if ($mount.length) {
        //             $mount.parent().remove();
        //         }
        //     }
        // });

        },
        get: function () {
            var _this = this;
            if (_this.cfg.isYuYue) {
                Event.addListener('onStockReady', function(obj) {
                    var data = obj && obj.stock && obj.stock.data
                    _this.cfg.havestock =  data.stockInfo && data.stockInfo.isStock
                    var r = data.yuyueInfo
                    // 预约兜底，主要解决预约已经结束，但是主数据依然打着预约标导致sku走预约模版
                    if (_this.cfg.isYuYue) {
                        if (
                            !r ||
                            $.isEmptyObject(r) ||
                            r.error ||
                            r.state == 5
                        ) {
                            location.href = G.modifyURL(location.href, {
                                query: {
                                    yuyue: '0'
                                }
                            })
                        }

                    }
                    if (r && r.yuyue) {
                        _this.set(data);
                    } else {
                        _this.onError();
                    }
                    // 预约隐藏价格
                    _this.setPrice(r && r.hidePrice);
                    _this.__res__ = r;
                });
            }
        },
        // 获取秒杀链接
        getIsKOurl: function () {
            var _this = this;
            var url = '//itemko.jd.com/itemShowBtn'
            $.ajax({
                url: url,
                data: {
                    skuId: this.cfg.skuid,
                    from: 'pc'
                },
                dataType: 'jsonp',
                success: function (r) {
                    _this.url = r.url;
                    if (_this.cfg.havestock) {
                        if (!r || r.type !== '3') {
                             _this.disabled(r.url);
                        } else {
                            _this.enabled(r);                       
                        }
                    } else {
                        _this.disabled(r.url);
                    }
                   

                    // _this.onError();

                }
            })
        },
        log: function (msg) {
            G.log(null, 'reservation.js', msg);
        },
        onError: function () {
            // 接口出错降级展示加入购物车按钮
            this.cfg.addToCartBtn.reInit(this.$el.eq(0));
            this.log('YuShou service return a error, Maybe the YuShou special attr has expired or service unavailable.');
        },
        set: function (d) {
            var r = d.yuyueInfo
            if(r.type != "5")
            {
                var _this=this;
                this.$el.show();
                // this.setPlus(r, $.proxy(function() {
                //     _this.setCountdown(r);
                //     _this.checkState(r);
                //     _this.setProcess(r);

                // }, this));

                // this.setCategory(r.category);
                this.setTextTips(r.plusText, r)
                this.setCountdown(r);
                this.setProcess(r);
                this.checkState(d);
                this.setCategory(r);

                this.supportJNBT(d);

                // 美妆加赠+预约/预售， 展示美妆加赠腰带，原有预约/预售腰带整体屏蔽
                // this.$banner.hide()
            }else{
                var _this=this;
                this.$banner.find("strong").text("预约抽签")
                this.$el.show();
                // this.setPlus(r, $.proxy(function() {
                //     _this.setCountdown(r);
                //     _this.checkState(r);
                //     _this.setProcess(r);

                // }, this));

                // this.setCategory(r.category);
                this.setTextTips(r.plusText, r)
                this.setCountdown(r);
                this.setProcess(r);
                this.checkStateKz(d);
                this.setCategory(r);

                this.supportJNBT(d);
                $("#yuyue-process h3").text("预约抽签流程");
                $("#yuyue-process>.item3").find("dt").text("3.抽签中");
                $("#yuyue-process>.item3").find("dd").text("抽取抢购资格");
                var ruleHTML = '<li>1、部分商品预约成功后才有抽签资格，中签后，请关注抢购时间及时抢购，货源有限，先抢先得！</li> \
                <li>2、部分商品在预约期间抢购时间未定，我们会在商品开抢前通过Push通知提醒您，请在设置中选择允许通知，以免错过抢购时间。</li> \
                <li>3、对于预约成功享优惠的商品，预约用户可获得优惠券或专属优惠。优惠券在抢购开始后使用，使用时间以优惠券有效期为准；专属优惠在抢购开始后，点击“立即抢购”将商品加入购物车，可在购物车查看优惠，若抢购时间结束，优惠自动失效。</li> \
                <li>4、查看预约商品请至“我的京东”-“我的预售”-“我的预约”进行查看。</li> \
                <li>5、如果提供赠品，赠品赠送顺序按照预约商品购买成功时间来计算，而不是预约成功时间。</li> \
                <li>6、如您对活动有任何疑问，请联系客服咨询。</li> \ '

                $(".pingou-rules ol").html(ruleHTML);
            }
        },
        /**
         * 添加提示条
         * @param {String} text
         */
        addTipBar: function(text) {
            var $elem = $("#J_TipBar");
            var innerHTML = '{0} \
                <i onclick="$(\'#J_TipBar\').remove()" class="sprite-close"></i> \
        '.format(text);

            if ($elem.length == 0) {
                var __html = "<div class='DJD-tips' id='J_TipBar'>" + innerHTML + "</div>";
                $(".choose-btns-wrapper").prepend(__html);
            } else {
                $elem.html(innerHTML);
            }
        },
        /**
         * 移除提示条
         */
        removeTipBar: function() {
            $("#J_TipBar").remove();
        },

        /**
         * 增加“开通PLUS立即购买”按钮
         */
        addJoinPlusButton: function() {
            var _this=this;
            // 增加“开通PLUS立即购买”按钮
            $('#btn-reservation,#btn-reservation-mini').each(function () {
                var $this = $(this);
                var openBtn="开通PLUS立即参与";
                var id = $this.attr("id");
                if (_this.cfg.isKO) { // 抢购阶段
                    openBtn="开通PLUS立即抢购";
                }
                var $button = $("<a class='J_JoinPlus' href='//plus.jd.com/index'>"+openBtn+"</a>").
                    addClass(id.indexOf("mini") == -1 ? "btn-buyplus btn-lg" : "btn-primary btn-buyplus");
                $this.after($button).hide();
            });

            // 根据地区不可售状态修改“开通PLUS立即购买”按钮的状态
            function toggleButtonState() {
                var unSupportedArea = !_this.cfg.unSupportedArea;
                $(".J_JoinPlus").toggleClass("btn-disable", !unSupportedArea).
                    attr("href", !!unSupportedArea ? "//plus.jd.com/index" : "#none");
            }
            toggleButtonState();
            Event.addListener('onStockReady', toggleButtonState);
            // require.async("JDF_UNIT/event/1.0.0/event.js", function (event) {
            //     event.on("loginSuccessByIframe", callStock);
            // });
            // addJoinPlusButton = function() {}
            _this.type == "5" ? $('.J_JoinPlus').hide() : "";

        },

        /**
         * 切换“加入购物车”按钮和“开通PLUS立即购买”按钮
         */
        toggleButton: function(flag) {
            var Cart = this.$el;
            var $joinPlusButton = $(".J_JoinPlus");
            var $baitiao = $("#choose-baitiao");
            $joinPlusButton.toggle(!flag);
            flag ? Cart.show() : Cart.hide();
            setTimeout(function () {
                !flag && $baitiao.hide();
            }, 100);
            this.type == 5 ? $('.J_JoinPlus').hide() :"";
            this.type == 5 ? Cart.hide() : "";

        },
        /**
         * 隐藏双价格楼层
         */
        hideTheSecondPriceFloor: function() {
            var arr = [
                ".J-sam-price",
                ".J-plus-price",
                ".J-fans-price",
                "#J_NewcomerPrice",
                "#J_DelPrice", // 增加不展示划线价
                "#J_StudentPrice"
            ];
            $(arr.join()).hide();
        },

        // getPlusText: function (r) {
        //     if (r.state === 2 || r.state === 3) {
        //         return {
        //             'P_1-U_2' : '您是PLUS会员，将提前{0}分钟开抢',
        //             'P_1-U_1' : '开通PLUS正式会员提前开抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
        //             'P_1-U_N' : '开通PLUS正式会员提前开抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
        //             'P_2-U_2' : '您是PLUS会员，将提前{0}分钟开抢',
        //             'P_2-U_1' : '您是PLUS会员，将提前{0}分钟开抢',
        //             'P_2-U_N' : '开通PLUS会员提前开抢，<a target="_blank" href="//plus.jd.com/index">去开通 &gt;&gt;</a>',
        //             'NO_LOGIN': 'PLUS会员提前开抢，请 <a href="{0}" class="J-plus-login">登录</a> 确认购买资格'
        //         }
        //     } else if (r.state === 4) {
        //         return {
        //             'P_1-U_2' : '您是PLUS会员，免预约可抢',
        //             'P_1-U_1' : '开通PLUS正式会员免预约可抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
        //             'P_1-U_N' : '开通PLUS正式会员免预约可抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
        //             'P_2-U_2' : '您是PLUS会员，免预约可抢',
        //             'P_2-U_1' : '您是PLUS会员，免预约可抢',
        //             'P_2-U_N' : '开通PLUS会员免预约可抢，<a target="_blank" href="//plus.jd.com/index">去开通 &gt;&gt;</a>',
        //             'NO_LOGIN': 'PLUS会员免预约可抢，请 <a href="{0}" class="J-plus-login">登录</a> 确认购买资格'
        //         }
        //     } else {
        //         return {}
        //     }
        // },
        setPlus: function (r, callback) {
        //     var plusType = r.plusType
        //     this.isPlusProduct = plusType === 1 || plusType === 2
        //     // 等待抢购阶段 || 预约阶段
        //     if (!this.isPlusProduct) {
        //         callback();
        //         return false;
        //     }

        //     /**
        //      * 商品(Product) & 用户(User)类型
        //      * P_1 商品：仅正式
        //      * P_2 商品：正式加试用
        //      * U_2 用户：正式Plus
        //      * U_1 用户：试用Plus
        //      * U_N 用户：普通（非PLUS）
        //      */
        //     var plusText = this.getPlusText(r)

        //     Login.isLogin($.proxy(function(isLogin) {
        //         var userType = this.getUserType(r)
        //         var textTip = 'P_{0}-U_{1}'.format(plusType, userType)
        //         var loginUrl = 'https://passport.jd.com/new/login.aspx?ReturnUrl=' + encodeURIComponent(location.href)

        //         if (isLogin) {
        //             // 有资格用户 showPlusTime
        //             // plusType 1 正式
        //             // plusType 2 正式 + 试用
        //             // userType 2 正式期
        //             // userType 1 试用期
        //             this.showPlusTime = plusType === 1 && userType === 2
        //                 || plusType === 2 && userType === 2
        //                 || plusType === 2 && userType === 1;

        //             // console.log('plusType: ' + plusType);
        //             // console.log('userType: ' + userType);
        //             // console.log('符合资格：' + this.showPlusTime);

        //             if (plusText[textTip]) {
        //                 this.setTextTips(plusText[textTip].format(this.getPlusPriorityTime(r)), r)
        //             }
        //         } else {
        //             if (plusText['NO_LOGIN']) {
        //                 this.setTextTips(plusText['NO_LOGIN'].format(loginUrl), r)
        //             }
        //         }
        //         callback()
        //     }, this))
        // },
        // // 展示 plus 优先几分钟开始抢购 plusEtime - qiangStime
        // getPlusPriorityTime: function (r) {
        //     var time = this.convertDate(r.plusEtime) - this.convertDate(r.qiangStime)
        //     return parseInt(time / 1000 / 60)
        // },
        // /**
        //  * 转换时间字符串为时间戳
        //  * 2017-03-28 17:00:00
        //  * 1490691600000
        //  * @param dateString
        //  */
        // convertDate: function (dateString) {
        //     var times = dateString.split(/-|\s|:/)
        //     for (var i = 0; i < times.length; i++) {
        //         times[i] = Number(times[i])
        //     }
        //     return new Date(times[0], times[1] - 1, times[2],
        //         times[3], times[4], times[5]).getTime()
        // },
        // // 展示 plus 倒计时时间
        // getPlusTime: function(r) {
        //     if (r.state === 3) {
        //         return this.showPlusTime ? r.plusD : r.d
        //     }
        //     if (r.state === 4) {
        //         // r.isBefore === 1 && !this.showPlusTime
        //         // plusEtime - now
        //         var plusTime;
        //         if (r.isBefore === 1) {
        //             if (this.showPlusTime) {
        //                 plusTime = (this.convertDate(r.qiangEtime) - new Date().getTime()) / 1000;
        //             } else {
        //                 plusTime = (this.convertDate(r.plusEtime) - new Date().getTime()) / 1000;
        //             }
        //             return parseInt(plusTime)
        //         } else {
        //             return r.d
        //         }
        //     }

        //     return r.d
        this.setTextTips(r.plusText, r)
        callback()
        },
        setTextTips: function (text, r) {
            // if (!(r.state === 4 && r.isBefore !== 1)) {
            //     this.$textTip.html(text)
            // }
            this.$textTip.html(text)
        },
        getUserType: function () {
            // http://cf.jd.com/pages/viewpage.action?pageId=57215567
            var ceshi3 = readCookie('ceshi3.com')
            if (ceshi3) {
                var firstChar = ceshi3.substr(0, 1)
                // 1 试用期
                // 2 正式期
                if (firstChar === '1' || firstChar === '2') {
                    return parseInt(firstChar)
                } else {
                    return 'N'
                }
            }
            return 'NO_LOGIN'
        },
        setCategory: function (r) {
            var $el = $('.J-yy-category');
            var text = r.yuyueText;

            // if (r === '1') { text = '预约享资格'; }
            // if (r === '3') { text = '预约享优惠'; }
            // if (r === '4') { text = '预约享提醒'; }

            if (text) {
                $el.html(text).show();
            } else {
                $el.hide();
            }
        },

        setProcess: function (r) {
            var $step2 = this.$process.find('.J-step2');
            var $step4 = this.$process.find('.J-step4');
            $step2.html(r.yuyueTime);
            $step4.html(r.buyTime);
        },
        setYuyueRule: function (r) {
            var $yuyueRuleList = $(".pingou-rules ol")
            var ruleList = r.yuyueRuleText
            var html="";
            if(ruleList!=null && ruleList.length>0) {
                for (var i = 0; i < ruleList.length; i++) {
                    html += "<li>" + ruleList[i] + "</li>";
                }
                $yuyueRuleList.html(html)

            }
        },

        /**
         * 设置预约商品价格
         */
        setPrice: function (flag) {
            flag = Number(flag);
            if (flag === 1) {
                this.$price.find('span:last').removeClass().addClass('price').html('待发布');
                pageConfig.product.isHidePrice = true;
            }
            this.$price.show();
        },

        disabled: function () {
            this.$el.addClass('btn-disable');
            this.$el.attr('href', '#none');
            // this.isEnabled = true;
            this.isEnabled = false;
        },
        enabled: function (data) {
            if (this.cfg.isClosePCShow) { return false; }
            var url = this.$el.attr('href');
            var _this=this;
            
            if (url !== '#none') {
                this.url = url;
            } 
            if (this.url) {
                // this.url = Tools.addUrlParam(this.url, 'did', this.LDP);
               
                // this.$el.attr('href', this.url);
                if(this.state == 2){//立即预约
                    var isPlusLimit = 0;
                    try {
                        isPlusLimit = (data.promotion.limitBuyInfo.resultExt.isPlusLimit == 1) ? 1 : 0;// isPlusLimit=1，则为专享购，反之，不是专享购。
                    } catch (err) {
                        isPlusLimit = 0;
                    }
                    this.$el.attr('href', _this.url+"&shopId="+_this.cfg.shopId+"&isPlusLimit="+isPlusLimit);
                }else{
                    this.$el.attr('href', this.url);
                }

            }
            this.$el.removeClass('btn-disable');
            this.isEnabled = true;
        },
        setCountdown: function (r) {
            var $time = this.$time;
            var $text = this.$banner.find('.J-text');

            this.$count.html(r.num).closest('.J-item-1').show();
            if(r.hideMemberNumber && r.hideMemberNumber=="1"){//屏蔽预约人数
                this.$banner.find('.J-item-1').hide(); 
            }
            $text.html(r.cdPrefix);

            var d = r.countdown
            if (d > 0) {
                this.$banner.find('.J-item-2').show();

                new Countdown(d*1000, function(res) {
                    if ( res.d < 1 ) {
                        $time.html( res.h + '小时' + res.m + '分' + res.s + '秒');
                    } else {
                        $time.html( res.d + '天' + res.h + '小时' + res.m + '分' + res.s + '秒');
                    }
                });
            } else {
                this.$banner.find('.J-item-2').hide();
            }
        },
        checkState: function (d) {
            /**
             * state 是1 表示 距离预约开始的倒计时
             * state 是2 表示 距离预约结束的倒计时
             * state 是3 表示 距离抢购开始的倒计时
             * state 是4 表示距离抢购结束的倒计时
             */
            var r = d.yuyueInfo;
            this.state = r.state;
            this.url = r.url;
            // console.log("预约状态",r.state)
            this.type = r.type
            if(r.sellWhilePresell!="1")// 非全程付
            {
                this.setBtnText(r.btnText);
            }
            this.cfg.yuyueState=r.state
            this.cfg.yuyueCategory=r.category

            //if (r.state === 1) {
                // this.$count.hide();
                // $('#choose-service,#choose-service\\+').hide();
            //}

            if ( r.state === 2 ) {
                this.enabled(d);
                if(r.sellWhilePresell!="1")// 非全程付
                {
                    this.setBtnText('立即预约');//立即                  
                }
                // $('#choose-service,#choose-service\\+').hide();
            }else if (r.state === 1 || r.state === 3 || r.state === 5 ) {
                this.disabled();
                // $('#choose-service,#choose-service\\+').hide();
            }else if ( r.state === 4 ) {
                Reservation.isNoBuy=false;
                this.setBtnText('抢购'); //立即
                this.url = location.protocol + '//cart.jd.com/gate.action?pcount=1&ptype=1&pid=' + this.cfg.skuid;
                try {
                    window.setAmount.$buyBtn = window.setAmount.$buyBtn.add(this.$el);
                } catch (error) {
                    console && console.log(error);
                }

                // pageConfig.isCfy 代表处方药
                //处方药预约预售抢购链接 预约后的抢购阶段 设置新链接
                if(pageConfig.product.isCfy){
                    this.url = '//rx.yiyaojd.com/cart_addItem.action?pid='+pageConfig.product.skuid+'&ptype=1&pcount=1';
                } else if (this.cfg.isKO){// 如果是秒杀，将链接换成秒杀链接
                    this.url = '#none'
                    this.getIsKOurl()
                }
                // 抢购 阶段需要判断库存状态
                if (this.cfg.havestock) {
                    if (r.plusType == 1 && !r.showPlusTime) {
                        this.disabled(r.url);
                    } else {
                        this.enabled(d);
                    }
                } else {
                    this.disabled(r.url);
                }
            }
            

            // if ( r.state === 5 ) {
            //     this.setBtnText('抢购已结束');
            //     this.disabled();
            // }
            
        },
        checkStateKz: function (d) {
            /**
             * state 是1 表示 距离预约开始的倒计时
             * state 是2 表示 距离预约结束的倒计时
             * state 是3 表示 距离抢购开始的倒计时
             * state 是4 表示距离抢购结束的倒计时
             */
            var r = d.yuyueInfo;
            this.state = r.state;
            this.url = r.url;
            this.type = r.type
            if (r.state === 1) {
                this.setBtnText('等待预约');
                this.disabled();
            }

            if ( r.state === 2 ) {
                this.url = '#none'
                this.setBtnText('立即预约');
                this.enabled(d);
                this.openResult("使用京东APP最新版本扫码参与，成功预约即可获得抽签资格哦~");
            }

            if ( r.state === 3 ) {
                this.setBtnText('活动抽签中');
                this.disabled();
            }

            if ( r.state === 4 ) {
                this.url = '#none'
                if(r.drawStatus === "1"){
                    this.setBtnText('查看抽签结果');
                    this.enabled(d);
                    this.openResult("使用京东APP最新版本扫码参与，查看抽签结果，中签后才能获得购买资格哦～");
                }else{
                    this.setBtnText('活动抽签中');
                    this.disabled();
                }
            }

            if ( r.state === 5 ) {
                this.setBtnText('抽签已结束');
                this.disabled();
            }
        },
        openResult: function (t) {
            var $this=this
            var url = this.$el.attr('data-url')
            var html =
                '\
                <p style="padding: 15px 30px;top: 60px;text-align: center;">\
                <span style="background: url(//img11.360buyimg.com/imagetools/jfs/t1/99478/39/16273/6095/5e798880Eb75106c9/db383dcd3bcb72a8.png) no-repeat 0 0;width: 95px;height: 32px;display: inline-block;background-size: 100%;"></span>\
                </p>\
                <p style="padding: 5px 30px;font-size: 14px;font-weight: 600;color: #999;text-indent: 24px;">'+t+'</p>\
                <div id="summary-mbuy" style="left:112px;width: 173px;margin-top: 35px;">\
                    <i></i>\
                    <div class="qrcode" style="margin-left: 15px;margin-top: 8px;">\
                        <div class="loading-style1"><b></b>加载中，请稍候...</div>\
                    </div>\
                </div>\
                <p style="padding: 0 30px;position: absolute;bottom: 68px;color: #999;">\
                <i style="display: inline-block;margin-right: 6px;vertical-align: -3px;width: 16px;height: 16px;background-image: url(//img12.360buyimg.com/imagetools/jfs/t1/88278/27/16409/599/5e798862Ebf6899ed/de23ce453bfe64c5.png);"></i>\
                打开“京东app-首页-右上角扫一扫[-], 扫描屏幕上的二维码”</p>'
            this.$el.click(function(){
                $('body').dialog({
                    width: 392,
                    title: '',
                    height: 429,
                    type: 'text',
                    maskClose: true,
                    source: html,
                    onReady: function() {
                        $this.loadQrcode(url)
                    }
                })
            })
        },
        loadQrcode: function(url) {
            var skuid=this.cfg.skuid;
            require.async('PLG_ROOT/jQuery.qrcode', function() {
                var qrcodeUrl =
                    url || '//m.jd.com/product/' + skuid + '.html?from=qrcode'

                $('#summary-mbuy .qrcode').html('').jdQrcode({
                    render: 'image',
                    ecLevel: 'L',
                    size: 145,
                    text: qrcodeUrl
                })
            })
        },
        setBtnText: function (text) {
            this.$el.html(text);
        },
        supportJNBT: function (d) {
            // 支持节能补贴
            var r = d.yuyueInfo;
            this.ysSupportJnbt = pageConfig.product.ysSupportJnbt = r.supportOther === 1;
            if (this.ysSupportJnbt) {
                this.jnbtBtn.show();
                this.jnbtBtn.enabled(d);
            } else {
                this.jnbtBtn.hide();
            }
        }
    };

    module.exports = Reservation;
    module.exports.showInvite = showInvite;
    module.exports.__id = 'Reservation';
});
