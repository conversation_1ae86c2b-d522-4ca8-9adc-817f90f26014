/* dropdown */
@import '../common/lib';
@import './__sprite';

#area-refer.item {
    z-index: 30;
}
#stock-address.hover {
    z-index: 3;
}
.stock-address {
    float: left;
    margin-top: -3px;
    // margin-left: -5px;
    #area-2024 {
        position: relative;
    }
    .head {
        white-space: nowrap;
        .text {
            float: left;
        }
    }
    dl {
        margin:0 10px;
        padding-bottom: 15px;
    }
    dd {
        padding-top: 10px;
        a {
            margin-right: 10px;
        }
    }
    .line {
        margin: 0 10px 10px;
        height: 0;
        overflow:hidden;
        border-bottom: 1px dotted #eee;
    }
    .content {
        background-color:#fff;
        width: 380px;
        padding-top: 20px;
    }
    .address-used {
        cursor: pointer;
        .arrow {
            float: right;
            margin-top: 5px;
            background: url(../common/i/arr-open.png) no-repeat 0 0;
            background: url(../common/i/arr-close.png) no-repeat 0 0\9;
        }

        &.more {
            .arrow {
                background: url(../common/i/arr-close.png) no-repeat 0 0;
                background: url(../common/i/arr-open.png) no-repeat 0 0\9;
            }
        }
        li a {
            width: auto;
        }
    }
    .address-select {
        cursor: pointer;
        .arrow {
            float: right;
            margin-top: 5px;
            background: url(../common/i/arr-open.png) no-repeat 0 0;
            background: url(../common/i/arr-close.png) no-repeat 0 0\9;
        }

        &.clicked {
            .arrow {
                background: url(../common/i/arr-close.png) no-repeat 0 0;
                background: url(../common/i/arr-open.png) no-repeat 0 0\9;
            }
        }
    }
    .hw-tab-con {
        position: relative;
        .hw-letters {
            li {
                padding: 0 2px 0 0;
                margin-right:0;
                display: inline;
                a:hover, a.clicked {
                    background: none;
                    color:$colorPriceRed
                }
            }
        }
        div.area-letter {
            padding-top:10px;
            padding-bottom:10px;
            color: #999;
        }
        div.area-letter-curr {
            color: $colorPriceRed;
        }
        ul {
            padding-bottom:10px;
            border-bottom: 1px solid #eee;
        }
    }
    .hw-area-wrap {
        height: 300px;
        overflow-y: scroll;
    }
    .ui-area-wrap .ui-area-text-wrap{
        top: 2px;
    }
}
.stock-address-list {
    li {
        @include inline-block;
    }
    a {
        @include inline-block;
        padding: 0 2px;
    }
    .clicked & {
        display: block;
    }
    a:hover,a.clicked {
        background: $colorPriceRed;
        color: #fff;
    }
}

.address-used {
    &.auto .stock-address-list ul {
        height: auto;
    }
    &.clicked .stock-address-list ul {
        height: 9em;
        overflow: auto;
        .address {
            width:238px;
        }
    }
    //div.more ul.less {
    //    height: 1.5em;
    //    overflow:hidden;
    //}
    .stock-address-list {
        ul {
            height: 1.5em;
            overflow:hidden;
        }
        li {
            display: block;
        }
        a {
            display: block;
        }
        a:hover, li.selected a {
            background-color: transparent;
            color: $colorPriceRed;
        }
        .name {
            display: inline-block;
            width: 4em;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 15px;
        }
        .address {
            display: inline-block;
            width: 248px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

.address-tab {
    .tab li {
        @include inline-block;
        margin-right: 5px;
        border: 1px solid #ddd;
        padding: 2px 10px;
        background:#fff;
        color: #005aa0;
        font: bold 14px 'microsoft yahei';
        border-bottom: none;
    }
    .tab .current {
        border: 2px solid #e4393c;
        border-bottom: 2px solid #fff;
    }
    .tab-con {
        border-top: 2px solid #e4393c;
        margin-top: -2px;
        padding-top: 10px;
        li {
            width: 6.4em;
            margin-right: 5px;
            margin-bottom: 5px;
            a {
                margin-right: 0;
            }
            &.long-area {
                width: 13em;
            }
            &.longer-area {
                width: 26em;
            }
        }
    }
}

.summary-service { // 配送时效 和 包邮
    color: #1A1A1A;
    font-size: $baseFontSize;
    position: relative;
    display: flex;
    &.old {
        display: block;
        .free-shipping {
            padding-left: 0;
            border: none;
            a {
                margin-left: 0;
                position: relative;
            }
            a:hover::after {
                position: absolute;
                z-index: 2; // 盖过地址组件
                padding: 0 12px;
                content: '查看运费规则';
                display: block;
                background: white; // 与地址栏重叠时，遮挡地址栏
                width: max-content;
                height: 38px;
                line-height: 38px;
                text-align: center;
                border: 1px solid rgba(0, 0, 0, 0.06);
                border-radius: 8px;
                color: rgba(80, 82, 89, 1);
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }
    i {
        @include inline-block;
        vertical-align: top;
        // margin-left: 2px;
    }
    a {
        color:#999;
        margin: 0 5px;

        .clothing & {
            color: $colorPriceRed;
        }
    }
    span.delivery {
        margin: 8px 8px 8px 0;
        height: 14px;
        line-height: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative;
        &.more:hover {
            color: rgba(255, 15, 35, 1);
            cursor: pointer;
        }
        // &:hover + .delivery-tips {
        //     display: block;
        // }
    }
    span.delivery-tips {
        display: none;
        position: absolute;
        z-index: 2; // 盖过地址组件
        padding: 0 12px;
        background: white; // 与地址栏重叠时，遮挡地址栏
        width: max-content;
        height: 38px;
        line-height: 38px;
        text-align: center;
        border: 1px solid rgba(0, 0, 0, 0.06);
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        color: rgba(80, 82, 89, 1);
        top: 32px;
        left: -10px;
    }
    b {
        margin-right: 8px;
    }
    span.free-shipping { // 包邮
        // margin-left: 8px;
        // padding: 0 8px;
        height: 14px;
        line-height: 14px;
        margin: 8px 0;
        flex-shrink: 0;
        display: inline-block;
        position: relative;
        cursor: pointer;
        a {
            margin: 0 0 0 8px;
            display: inline-block;
            z-index: 2; // 需要大于地址组件
            span {
                box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
                position: absolute;
                z-index: 2; // 盖过地址组件
                padding: 0 12px;
                // content: '查看运费规则';
                display: none;
                background: white; // 与地址栏重叠时，遮挡地址栏
                width: max-content;
                height: 38px;
                line-height: 38px;
                text-align: center;
                border: 1px solid rgba(0, 0, 0, 0.06);
                border-radius: 8px;
                color: rgba(80, 82, 89, 1);
                left: 50%;
                transform: translateX(-50%);
            }
            
        }
        &:hover {
            color: rgba(255, 15, 35, 1);
            span {
                display: block;
            }
            i.sprite-question{
                background-image: url(https://img10.360buyimg.com/imagetools/jfs/t1/255898/37/13478/762/678a4853F37d10aa5/3467e3bf704b3340.png);
            }
        }
        i.sprite-question {
            vertical-align: middle;
            background: url(https://img11.360buyimg.com/imagetools/jfs/t1/265713/6/12921/978/678a4853Fb877b897/479338c16704e1f8.png) no-repeat;
            background-size: 14px;
        }
    }
}
#summary-supply {
    // clear: both;
    padding-top: 10px;
    .dt{
        line-height: 32px;
        height: 32px;
    }
    .summary-service{
        .hl_red_bg{
            color: #df3033;
            background: 0 0;
            border: 1px solid #df3033;
            padding: 2px 3px;
            margin-right: 5px;
            display: inline-block;
            line-height: 16px; 
        }
    }

}
#summary-supply-time {
    .dt{
        height: 32px;
        line-height: 32px;
    }
}
#summary-supply.stand-alone {
    margin-top: 0;
}
// .summary-line {
    // height: 0;
    // overflow:hidden;
    // border-bottom: 1px dotted #dfdfdf;
    // margin-bottom:15px;
// }
.summary-stock {
    // margin-top: 16px;
    margin-bottom: 15px; // 行高32 字体15 余 17
    // height: 16px; // 15px 导致门店样式错乱 10036461116852
    .store {
        // margin-bottom: 5px;
        .store-selector {
            margin-right: 10px;
            float: left;
            div {
                float: left;
                z-index: 1;
            }
            .head {
                z-index: 2;
            }
            .sprite-arr-close {
                margin-left: 5px;
                display: inline-block;
                vertical-align: middle;
            }
        }

        .store-prompt {
            margin-right: 10px;
            color: #999;
            height: 24px;
            float: left;
            strong {
                margin-right: 5px;
                float: left;
                font-size: 12px;
            }
            .store-prompt-num {
                float: left;
                margin-right: 10px;
            }
            .support {
                float: left;
                .support-name {
                    color: #666;
                }
                img {
                    margin: 0 3px;
                }
            }
            .tip {
                float: left;
            }
        }
    }

    .dcashDesc i {
        @include inline-block;
        @include icons(16px, 16px);
        vertical-align: middle;
        background-image: url(i/question.png);
    }
}

.choose-support {
    li {
        margin-right: 10px;
        float: left;
        *position: relative;
    }

    a {
        display: inline-block;
        line-height: 18px;
        span {
            position: relative;
            *left: 5px;
            // z-index: -1;
            display: inline-block;
            border: 1px solid #ced2e6;
            line-height: 16px;
            line-height: 14px \9;
            border-radius:8px;
            padding: 0 5px 0 18px;
            *padding-left: 15px;
            color: #5e69ad;
            margin-left: -18px;
            margin-left: -15px \9;
            *margin-left: 5px;
        }

        .sprite-jnbt {
            @include sprite-jnbt;
        }
        .sprite-old2new {
            @include sprite-old2new;
        }
        .sprite-question {
            @include sprite-question;
        }
        .sprite-sjwx {
            @include sprite-sjwx;
        }
        .sprite-tcbg {
            @include sprite-tcbg;
        }
        .sprite-giftbuy {
            @include sprite-giftbuy;
        }
        .sprite-baina {
            @include sprite-baina;
        }
        .sprite-zengzhi {
            @include sprite-zengzhi;
        }

        i {
            @include inline-block;
            //margin-right: 5px;
            vertical-align: -4px;
            *position: absolute;
            *z-index: 1;
        }
    }
}

/*book*/
.ebook{
    .summary-stock{
        display: none;
    }
}

.DJD-tips {
    position: absolute;
    height: 36px;
    width: 100%;
    top: -36px;
    left: 0px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 16px;
    background: #FFF5CC;
    color: #C47600;
    font-size: 14px;
    &.state-subsidy {
        background: #EFFCF3;
        // justify-content: flex-start;
        color: #191919;
        background-image: url(https://img13.360buyimg.com/imagetools/jfs/t1/275384/1/27625/4392/680e2ecfF257713af/27563fe318a1f8d5.png);
        background-size: 49px 16px;
        background-position: 16px 10px;
        background-repeat: no-repeat;
        padding-left: 71px;
        font-family: JDZhengHeiVRegular2-1;
        a, .receive {
            font-family: PingFang SC, microsoft yahei;
            display: flex;
            align-items: center;
            height: 24px;
            padding-left: 8px;
            padding-right: 6px;
            line-height: 24px;
            border-radius: 3px;
            border: 0.5px solid #0AAD48;
            color: #0AAD48;
            font-size: 12px;
            img {
                width: 8px;
                height: 8px;
                margin-left: 2px;
            }
        }
    }
    i {
        @include inline-block;
        width: 16px;
        height: 16px;
        vertical-align: middle;
    }
    .sprite-close {
        width: 12px;
        height: 12px;
        cursor: pointer;
        background: url(https://img13.360buyimg.com/imagetools/jfs/t1/267374/32/19324/339/67adbcfaF4f49767c/f54cb4c7c6590b71.png) no-repeat;
        background-size: 12px;
    }
    .sprite-info {
        margin-right: 5px;
        @include sprite-info;
    }
}

/*增值浮层*/
.zengzhi-layer{
    padding: 20px;
    h3{
        font-family: "Microsoft YaHei";
        font-size: 14px;
        color: #5e69ad;
        padding-bottom: 8px;
        .icon-zengzhi{
            display: inline-block;
            width: 22px;
            height: 22px;
            background: url(i/icon-zengzhi.png) no-repeat;
            margin-right: 5px;
            vertical-align: -4px;
        }
    }
    .zengzhi-intr{
        font-family: $font-st;
        font-size: 12px;
        color: #000;
        line-height: 180%;
    }
    .zengzhi-info{
        font-family: $font-st;
        background: #f7f7f7;
        border: 1px solid #ddd;
        margin-top: 15px;
        padding: 14px 20px 20px;
        font-size: 12px;
        color: #666;
        line-height: 180%;
        li{
            padding: 4px 0;
            list-style-position: inside;
        }
    }
    .process{
        margin: 0 16px;
        .arrow{
            display: block;
            width: 16px;
            height: 15px;
            background: url(i/icon-arrow.png) no-repeat;
            margin: 0 auto;
        }
    }
    .process-item{
        background: #fff;
        border: 1px solid #ddd;
        margin: 8px 0;
        text-align: center;
        line-height: 26px;
        span{
            color: #e33c3f;
        }
    }
    .btn-confirm{
        margin:0 auto;
        display: block;
        width: 122px;
        line-height: 36px;
        background: #e33c3f;
        color: #fff;
        border-radius: 2px;
        font-size: 14px;
        text-align: center;
        font-weight: bold;
        &:hover{
            background: #ee484b;
        }
    }
}




//预售金融券接口优惠券样式
#summary-presale-wellfare {
    padding-bottom: 2px;

    .dt{
        line-height: 32px;
    }

    .dd{
        padding-top: 8px;
    }
    .quan-item {
        position: relative;
        float: left;
        height: 16px;
        padding-left: 2px;
        line-height: 15px;
        text-align: center;
        border-top: 1px solid #df3033;
        border-bottom: 1px solid #df3033;
        background: #ffdedf;
        font-size: 14px;
        white-space: nowrap;
        margin-right: 13px;
        cursor: pointer;
    }
    .text {
        padding: 0 10px;
        color: #df3033;
        font-size: 12px;
    }
    .quan-item s, .quan-item b {
        position: absolute;
        top: -1px;
        display: block;
        height: 18px;
        width:2px;
        overflow:hidden;
        background: url(i/quan-arr.gif) 0 0 no-repeat;
    }
    .quan-item s {
        left: 0;
        background-position: -2px 0;
    }
    .quan-item b {
        right: -2px;
    }
    .quan-item:hover{
        background: #fff4f4;
        s,b{
            background: url(i/quan-arr-hover.gif) no-repeat;
        }
        s{
            background-position: -2px 0;
        }
    }
    .more-btn {
        // font-family: SimSun;
        color: #999;
        position: relative;
        float: left;
        height: 16px;
        line-height: 18px;
        cursor: pointer;
    }
}

/// 服务支持列表

.ns_services_info.services {
    // overflow: hidden;
    position: relative;
    @include inline-block;
    height: 27px;
    line-height: 27px;
    background-color: #fff;
    font-size: 15px;

    &.services--more {
        padding-right: 25px;
    }

    a,span {
        color: #FF0F23;
        font-size: 15px;
        word-break: keep-all;
    }

    a:hover {
        color: #E4393C;
    }

    i {
        // padding: 0 8px;
        color: #FF0F23;
    }

    .arrow {
        position: absolute;
        right: 7px;
        top: 8.5px;
        width: 10px;
        height: 10px;
        background: url(https://img12.360buyimg.com/imagetools/jfs/t1/266539/18/14151/476/678f5c60F3358f69f/ce4c94df51709093.png) no-repeat 0 0;
        background-size: 100%;
    }

    &.services--more.expand {
        overflow: visible;
        height: auto;
        // padding-top: 5px;
        padding-bottom: 5px;
        // border: 1px solid #ccc;
        .arrow {
            transform: rotate(180deg);
        }
    }
}

#service-support{ // 服务楼层
    a,span {
        color: #1A1A1A;
        font-size: $baseFontSize;
        word-break: keep-all;
    }

    a:hover {
        color: rgba(255, 15, 35, 1);
    }

    i {
        // padding: 0 8px;
        color: #1A1A1A;
    }
    .ns_services.services{
        padding-left: 0;
    }
}

/// 地址模块区域服务支持列表样式修正
.J-promise-icon.promise-icon {
    position: relative;
    // height: 27px; 
    .services {
        top: -6px;
    }
    
}

/// 放心购
.SelfAssuredPurchase { // 送至楼层和省心装
    position: relative;
    // z-index: 3;
    font-family: "Microsoft YaHei";
    .icon-SelfAssuredPurchase {
        @include inline-block;
        @include sprite-SelfAssuredPurchase;
        cursor: pointer;
    }

    .icon-freeBuy {
        @include inline-block;
        width: 81px;
        height: 19px;
        background-image: url(//img13.360buyimg.com/imagetools/jfs/t1/102818/19/38689/9459/65f1797dFe145828a/e55fd6c55c28edc8.png);
        background-size: 100%;
        cursor: pointer;
    }

    .icon-qyService {
        @include inline-block;
        @include sprite-SelfAssuredPurchase3;
        cursor: pointer;
    }


    .icon-SelfAssuredPurchase.extra3 {
        @include inline-block;
        @include sprite-SelfAssuredPurchase3;
        cursor: pointer;
    }

    .icon-SelfAssuredPurchase,
    .services {
        position: relative;
        top: -4px;
        vertical-align: middle;
    }

    .services.services--more:hover {
        position: absolute !important;
        top: -1px;
    }

    .promises {
        line-height: 18px;
        margin-top: -3px;
        margin-bottom: 3px;
        a {
            margin-right: 5px;
            color: #999;
            font-size: 12px;
            word-break: keep-all;
        }
    }
    a:hover {color: #e4393c;}
}
#J_LogisticsService{
    z-index: 1;
    margin-bottom: 14px; // 行高23 - 字号15-pading-bottom 10px + 14=32 
    line-height: 15px;
    .dd {
        display: flex;
        align-items: center; // 对齐地址和物流标签
    }
    .store{
        display: inline-block;
        position: relative;
    }
    .ns_services{
        position: relative;
        .logistic-basic {
            display: inline-block;
            position: relative;
            // padding: 0 8px;
            // padding-bottom: 8px;
            // border-right: 1px solid #0000000F;
            margin: 0 8px;
            font-size: $baseFontSize;
            font-family: 'PingFang SC';
            color: #FF0F23;
            height: 15px;
            padding-right: 8px;
            a {
                color: #FF0F23;
            }
            &:hover {
                .logistic-more {
                    display: block;
                }
            }
            // &::before {
            //     position: absolute;
            //     left: 0px;
            //     top: 1.5px;
            //     display: block;
            //     content: "";
            //     width: 1px;
            //     height: 12px;
            //     background:  rgba(0, 0, 0, 0.12);
            // }
            &::after {
                position: absolute;
                right: 0px;
                top: 1.5px;
                display: block;
                content: "";
                width: 1px;
                height: 12px;
                background:  rgba(0, 0, 0, 0.12);
            }
        }
        .logistic-more {
            display: none;
            font-size: 14px;
            word-break: keep-all;
            position: absolute;
            top: 18px;
            left: 50%;
            transform: translateX(-50%);
            // max-width: 258px;
            background: #fff;
            border: 0.5px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
            padding: 6px 12px;
            line-height: 24px;
            border-radius: 8px;
            a {
                color: #505259;
                &:hover {
                    color: rgba(255, 15, 35, 1);
                }
            }
            a + a{
                border-left: 1px solid #0000000F;
                padding-left: 8px;
                margin-left: 8px;
            }
    
        }
        
    }
    
}
.icon-wl,.icon-kd {
    position: relative;
    font-size: $baseFontSize;
    font-weight: 600;
    color: #FF0F23;
    display: inline-block;
}
.icon-sf {
    position: relative;
    font-size: $baseFontSize;
    font-weight: 400;
    color: #1a1a1a;
    display: inline-block;
}
.icon-cs {
    position: relative;
    font-size: $baseFontSize;
    font-weight: 400;
    color: #1a1a1a;
    display: inline-block;
    margin-left: 8px;
}