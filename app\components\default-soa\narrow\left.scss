@media (max-width: 1679px) {
  .root61 {
    .product-intro {
      .preview-wrap {
        width: 656px;
      }

      .detail {
        width: 656px;
      }
    }

    .preview {
      .spec-items {
        ul {
          li {
            width: 88px;
            height: 88px;
          }
        }
      }

      .bigimg {
        width: 1104px;
      }

      .video {
        width: 552px;
        height: 552px;
      }
  
      .video-player-dimensions {
        width: 552px;
        height: 552px;
      }
  
      .spec-list {
        height: 552px;

        .arrow-prev, .arrow-next {
          width: 88px;
        }
  
        .spec-items {
          width: 88px !important;
          height: 552px !important;
        }
      }
  
      .main-img {
        width: 552px;
        height: 552px;
  
        #spec-img {
          height: 552px;
        }
      }

      .zoomdiv {
        left: 568px !important;
      }
    }

    #detail .tab-main.pro-detail-hd-fixed {
      width: 656px;
    }

    .detail-shop {
      padding-bottom: 52px;
      position: relative;

      .advantage {
        justify-content: flex-end;
      }

      .btns {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 36px;

        & > div {
          flex: 1;
        }
      }
    }
  }
}