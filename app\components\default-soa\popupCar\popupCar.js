define('MOD_ROOT/popupCar/popupCar', function(require, exports, module) {
    var dialog = require('JDF_UI/dialog/1.0.0/dialog');
    var login  = require('JDF_UNIT/login/1.0.0/login');
    var G = require('MOD_ROOT/common/core');
    var Tools=require('MOD_ROOT/common/tools/tools');
    var newCarButler=null;

    var carDialog = '\
        <div class="J-car-layer-con car-layer-con">\
            <div class="love-car {if cars.length} show{/if}">\
                <h5>我的爱车：</h5>\
                <ul class="car-list">\
                    {for car in cars}\
                    <li class="J-car-item {if userDefaultCarId == car.id} current{/if}" data-id="${car.id}" data-modelId="${car.model_id}">\
                        <div class="J-item-layer item-layer" style="display: none;">\
                            <div class="item-bt"><a class="J-qr-del-btn" href="javascript:;">删除车型</a><a class="J-qr-cancel-btn" href="javascript:;">取消</a></div>\
                        </div>\
                        <a href="javascript:;" class="J-del-btn item-op"></a>\
                        <img src="//img30.360buyimg.com/car/${car.brand_logo}" title="${car.brand_name}">\
                        <div class="car-info">\
                            <h4 title="${car.brand_name} ${car.series_name}">${car.brand_name} ${car.series_name}</h4>\
                            <p title="${car.series_year_name} 款${car.model_name}">${car.series_year_name} 款${car.model_name}</p>\
                        </div>\
                    </li>\
                    {/for}\
                    {if cars.length < 4}\
                    <li class="null">\
                        <a class="J-addCar item-add" href="javascript:;" target="_self"><i></i><em>添加新车型</em></a>\
                    </li>\
                    {/if}\
                </ul>\
                {if cars.length >= 4}\
                <p class="tips">最多添加4辆爱车，如需新增，请先删除</p>\
                {/if}\
            </div>\
            <div id="iscgj2" class="car-filter {if cars.length==0} show{/if}">\
                <h5>添加新车型</h5>\
                <div class="car-filter-bd">\
                    <div class="menu-drop car-filter-item1">\
                        <div class="trigger">\
                            <span class="curr">品牌</span><i class="menu-drop-arrow"></i>\
                        </div>\
                        <div class="menu-drop-main">\
                            <ul class="menu-drop-letter-list">\
                                <li class="fore0 curr"><a href="">热门</a></li>\
                                <li class="fore1"><a href="">A</a></li>\
                                <li class="fore2"><a href="">B</a></li>\
                                <li class="fore3"><a href="">C</a></li>\
                                <li class="fore4"><a href="">D</a></li>\
                                <li class="fore5"><a href="">E</a></li>\
                                <li class="fore6"><a href="">F</a></li>\
                                <li class="fore7"><a href="">G</a></li>\
                                <li class="fore8"><a href="">H</a></li>\
                                <li class="fore9"><a href="">I</a></li>\
                                <li class="fore10"><a href="">J</a></li>\
                                <li class="fore11"><a href="">K</a></li>\
                                <li class="fore12"><a href="">L</a></li>\
                                <li class="fore13"><a href="">M</a></li>\
                                <li class="fore14"><a href="">N</a></li>\
                                <li class="fore15"><a href="">O</a></li>\
                                <li class="fore16"><a href="">P</a></li>\
                                <li class="fore17"><a href="">Q</a></li>\
                                <li class="fore18"><a href="">R</a></li>\
                                <li class="fore19"><a href="">S</a></li>\
                                <li class="fore20"><a href="">T</a></li>\
                                <li class="fore21"><a href="">U</a></li>\
                                <li class="fore22"><a href="">V</a></li>\
                                <li class="fore23"><a href="">W</a></li>\
                                <li class="fore24"><a href="">X</a></li>\
                                <li class="fore25"><a href="">Y</a></li>\
                                <li class="fore26"><a href="">Z</a></li>\
                            </ul>\
                            <ul class="menu-drop-list">\
                            </ul>\
                        </div>\
                    </div>\
                    <div class="menu-drop car-filter-item2">\
                        <div class="trigger">\
                            <span class="curr">车系</span><i class="menu-drop-arrow"></i>\
                        </div>\
                        <div class="menu-drop-main">\
                            <div class="menu-drop-hint">请先选择品牌</div>\
                            <div class="menu-drop-list-container">\
                            </div>\
                        </div>\
                    </div>\
                    <div class="menu-drop car-filter-item3">\
                        <div class="trigger">\
                            <span class="curr">年款</span><i class="menu-drop-arrow"></i>\
                        </div>\
                        <div class="menu-drop-main">\
                            <div class="menu-drop-hint">请先选择品牌和车系</div>\
                            <ul class="menu-drop-list">\
                            </ul>\
                        </div>\
                    </div>\
                    <div class="menu-drop car-filter-item4">\
                        <div class="trigger">\
                            <span class="curr">车型</span><i class="menu-drop-arrow"></i>\
                        </div>\
                        <div class="menu-drop-main">\
                            <div class="menu-drop-hint">请先选择品牌，车系和年款</div>\
                            <ul class="menu-drop-list">\
                            </ul>\
                        </div>\
                    </div>\
                    <a href="javascript:;" target="_self" class="btn btn-default car-filter-btn">添加爱车</a>\
                </div>\
            </div>\
            <div class="match-goods">\
                <h5>匹配的商品</h5>\
                <div class="match-goods-con">\
                    <div class="match-goods-choose {if cars.length==0} show{/if}"><i></i><em>请先选择您的车型...</em></div>\
                    <div class="match-goods-loading {if cars.length} show{/if}"><i></i><em>正在加载匹配的商品...</em></div>\
                    <div class="no-match-goods"></div>\
                    <div class="addError"><i></i><em>添加车型失败，请稍后再试...</em></div>\
                    <div class="deleteError"><i></i><em>删除车型失败，请稍后再试...</em></div>\
                    <ul class="match-goods-list">\
                    </ul>\
                </div>\
            </div>\
        </div>';

    var noMatchGoods = '<i></i><em>此页暂无该车型匹配商品，</em><em><a href="//iche.jd.com/market.html?fore=0&cid3=${cid}&referer=1&modelId=${modelid}" target="_blank">去<strong>京东车管家</strong>查看全部匹配商品</a></em>';

    var carDialogLoveCarList = '\
                <ul class="car-list">\
                    {for car in cars}\
                    <li class="J-car-item {if choosedCarmodelid == car.model_id} current{/if}" data-id="${car.id}" data-modelId="${car.model_id}">\
                        <div class="J-item-layer item-layer" style="display: none;">\
                            <div class="item-bt"><a class="J-qr-del-btn" href="javascript:;">删除车型</a><a class="J-qr-cancel-btn" href="javascript:;">取消</a></div>\
                        </div>\
                        <a href="javascript:;" class="J-del-btn item-op"></a>\
                        <img src="//img30.360buyimg.com/car/${car.brand_logo}" title="${car.brand_name}">\
                        <div class="car-info">\
                            <h4 title="${car.brand_name} ${car.series_name}">${car.brand_name} ${car.series_name}</h4>\
                            <p title="${car.series_year_name} 款${car.model_name}">${car.series_year_name} 款${car.model_name}</p>\
                        </div>\
                    </li>\
                    {/for}\
                    {if cars.length < 4}\
                    <li class="null">\
                        <a class="J-addCar item-add" href="javascript:;" target="_self"><i></i><em>添加新车型</em></a>\
                    </li>\
                    {/if}\
                </ul>\
                {if cars.length >= 4}\
                    <p class="tips">最多添加4辆爱车，如需新增，请先删除</p>\
                {/if}';

    // 车管家赠品只有一个
    var matchGoodsList='\
        {for match in matchs}\
        <li>\
            <dl class="clearfix" class="J-match-goods" giftId="${match.skuId}">\
                <dt><img src="//img10.360buyimg.com/n1/s100x100_${match.imagePath}" title="" alt="" width="58"  height="58" /></dt>\
                <dd class="goods-detail">\
                    {if match.mark == 1}\
                    <p>当前浏览商品</p>\
                    {/if}\
                    <p><a class="J-match-list" href="//item.jd.com/${match.skuId}.html" target="_blank">${match.name}</a></p>\
                    <p class="color999">${match.color} ${match.size}</p>\
                    {if match.hasGift}\
                    <p class="J-gift" data-sku="${match.gifts.sku}">\
                        <span class="color999">赠品：</span>\
                        <img src="//img10.360buyimg.com/n1/s100x100_${match.gifts.mainImage}"/>\
                        <a class="gift-title" title="${match.gifts.skuName}" href="//item.jd.com/${match.gifts.sku}.html" target="_blank">${match.gifts.skuName}</a>\
                        <span class="J-gift-num">× ${match.gifts.giftCount}</span>\
                        {if !match.gifts.hashStore}\
                        <span class="no-goods">无货</span>\
                        {/if}\
                        <a class="car-gift-price" href="//item.jd.com/${match.gifts.sku}.html" target="_blank">查看价格</a>\
                    </p>\
                    {/if}\
                </dd>\
                <dd class="goods-price J-p-${match.skuId}">\
                    <p>￥${match.p}</p>\
                    {if match.mark == 1}\
                    <a class="go-buy J-match-list" href="//item.jd.com/${match.skuId}.html">继续购买</a>\
                    {/if}\
                </dd>\
            </dl>\
        </li>\
        {/for}';


    var PopupCar = {
        init:function(cfg) {
            var _this = this;
            _this.cfg = cfg;

            var $chooseCar = $("#choose-car");                          //车型选择
            _this.$wraningTips = $chooseCar.find(".warning-tips");      //匹配提示
            _this.$chooseCarTips = $chooseCar.find(".tips");            //车型文本
            _this.$chooseOP = $chooseCar.find(".choose-op");            //选择按钮
            _this.$selcancelOP = $chooseCar.find(".J-selcancel-op");    //取消车型查看全部商品型号按钮
            _this.carDialog = null;                                     //汽车列表弹窗
            _this.userDefaultCarId = null;
            _this.choosedCarmodelid = null;
            _this.skulist = [];                                         //根据车型推荐商品sku列表
            _this.userCarArr = null;

            //_this.queryUserCarModel();
            _this.isShowCarType();
            _this.checkFromCheGuanJia();
            _this.addEvent();
        },

        checkFromCheGuanJia: function () {
            var _this = this,
                hrefParams = G.serializeUrl(location.href).param,
                hrefCarModelId = hrefParams.carModelId;

            if(hrefCarModelId) {
                _this.queryUserCarModel(function () {
                    //console.log('从车管家过来的，需要请求接口，查询用户车型');
                });
            }
        },
        queryUserCarModel: function(callback) {
            var _this = this;
            var pin = readCookie('pin');

            if(!pin) {
                _this.updateCarModelTxt(null, null);
                return;
            }

            //console.log(_this.cfg);
            $.ajax({
                url: '//cd.jd.com/auto/skubypin',
                data: {
                    skuId: _this.cfg.skuid
                },
                dataType: 'jsonp',
                jsonpCallback: 'popupCarAutoskubypin',
                success: function (r) {
                    if(r.hasModelOrSku) {
                        if(r.userModels) {
                            _this.userCarArr = r.userModels;
                            _this.userDefaultCarId = r.userDefaultCar.id;
                        }
                        callback.call(_this);
                    } else {
                        callback.call(_this);
                    }
                }
            });
        },
        openDialog: function() {
            if( $('.car-layer-con').length ){
                return false;
            }
            var _this = this,
                $body = $('body'),
                dialogHtml = '',
                dialogW = 0;

          if(_this.userCarArr && _this.userCarArr.length) {
                dialogHtml = carDialog.process({cars:_this.userCarArr, userDefaultCarId: _this.userDefaultCarId, skuid: _this.cfg.skuid});

            } else {
                _this.userCarArr=[];
                _this.userDefaultCarId=null;
                dialogHtml = carDialog.process({cars:_this.userCarArr, userDefaultCarId: _this.userDefaultCarId, skuid: null});
            }
            dialogW = 773;
            //require.async('WDG_ROOT/popupCar/popupCar.css', function () { });
            _this.carDialog = $body.dialog({
                type: 'html',
                width: dialogW,
                modal: true,
                title: '选择车型精准匹配商品',
                source: dialogHtml,
                onReady: function() {
                    _this.removeLayerEvent();
                    _this.addLayerEvent();
                    _this.loadMatchGoods();

                    //车管家初始化
                    if (!$("#iscgj2").length) return false;
                    require.async([
                        'MOD_ROOT/carButler/carButler'
                    ], function (CarButler) {
                        newCarButler=new CarButler({
                            el:"#iscgj2",
                            isGoToCarPage:false
                        });
                        newCarButler.addNewCar(function($thisEl,response){
                            _this.choosedCarmodelid=$thisEl.find('.car-filter-btn').attr('choosedCarmodelid');
                            if(_this.choosedCarmodelid){
                                _this.userCarArr=response.userModels;

                                var htmlStr = carDialogLoveCarList.process({cars:_this.userCarArr, choosedCarmodelid:_this.choosedCarmodelid , skuid: _this.cfg.skuid});
                                $('.love-car p.tips,.love-car .car-list').remove();
                                $('.love-car h5').after(htmlStr);
                                $thisEl.removeClass('show');
                                $('.love-car').addClass('show');

                                //加载匹配的商品
                                _this.loadMatchGoods();

                            }else{
                                $('.match-goods-con > div').removeClass('show');
                                $('.match-goods-con ul').removeClass('show');
                                newCarButler.initOptionSate();
                                $('.match-goods-con > div.addError').addClass('show');
                            }

                        },function(){
                            $('.match-goods-con > div').removeClass('show');
                            $('.match-goods-con ul').removeClass('show');
                            newCarButler.initOptionSate();
                            $('.match-goods-con > div.addError').addClass('show');
                        });
                    });
                }
            });
        },
        addEvent: function () {
            var _this = this;

            _this.$chooseOP.click(function () {
                login({
                    modal: true,
                    complete: function(result) {
                        if (result != null &&result.Identity && result.Identity.IsAuthenticated) {
                            _this.queryUserCarModel(_this.openDialog);
                        }
                    }
                });
            });

            _this.$selcancelOP.click(function () {
                //setCancel(true);
            });
            _this.addLayerEvent();
        },

        addLayerEvent:function () {
            var _this = this,
                $body = $("body");

            $body.delegate('.car-list li', 'mouseenter.popupCar', function () {
                $(this).addClass('hover');
            });
            $body.delegate('.car-list li', 'mouseleave.popupCar', function () {
                $(this).removeClass('hover');
            });

            $body.delegate('.match-goods-list .J-match-list', 'click.popupCar', function () { // 点击匹配商品列表，将车型写入cookie
                var jumpCarType = _this.choosedCarmodelid;
                createCookie('jumpCarType',jumpCarType);
                // 跳转时，将车型带入另一个页面
                var $currentCar = $(this).parents(".J-car-layer-con").find(".J-car-item.current");
                var jumpCarTxt = $currentCar.find(".car-info h4").text() + $currentCar.find(".car-info p").text();
                createCookie('jumpCarTxt',jumpCarTxt);
            });
            
            $body.delegate('.car-list li', 'click.popupCar', function () {
                var $this = $(this),
                    $carItem = $(".car-list li");

                $carItem.removeClass("current");
                $(this).addClass('current');
                // 点击添加爱车
                if( $(this).hasClass('null') && $(this).hasClass('current') ){
                    $('.match-goods-choose,.match-goods-list').removeClass('show');
                    $('#iscgj2').addClass('show');
                    newCarButler.initOptionSate();
                    $('.no-match-goods').removeClass('show');
                    $('.match-goods-choose').addClass('show');

                }
                // 点击车型
                else{
                    _this.currentModuleId = $this.data('modelid')
                    $('#iscgj2').removeClass('show');
                    _this.loadMatchGoods();
                }

                /*if( $('.J-car-item').length>0 ){
                    $(".J-car-layer-con .J-ok-btn").toggleClass("disabled", !$carItem.hasClass("current"));
                }*/
            });
            $body.delegate('.J-car-item .J-del-btn', 'click.popupCar', function (e) {
                $(this).siblings('.J-item-layer').show();
                e.stopPropagation();
            });
            $body.delegate('.match-goods-list li', 'mouseenter.popupCar', function (e) {
                $(this).siblings().removeClass('active').end().addClass('active');
                return false;
            });
            $body.delegate('.match-goods-list', 'mouseleave.popupCar', function (e) {
               $(this).find('li').removeClass('active');
            });

            $body.delegate('.J-car-item .J-qr-del-btn', 'click.popupCar', function (e) {
                var id = $(this).parents("li").attr("data-id");
                _this.deleteCar($(this),_this);
                e.stopPropagation();
            });
            $body.delegate('.J-car-item .J-qr-cancel-btn', 'click.popupCar', function (e) {
                $(this).parents('li').find('.J-item-layer').hide();
                e.stopPropagation();
            });
            $body.delegate('.J-car-layer-con .J-ok-btn', 'click.popupCar', function () {
                var $currentCarItem = $('.J-car-item.current');
                if(!$currentCarItem.length) {
                    return;
                }
                var modelId = $currentCarItem.attr("data-modelid");
                _this.querySkuListByCarModelId(modelId);
                _this.carDialog.close();
            });
            $body.delegate('.J-car-layer-con .J-cancel-btn', 'click.popupCar', function () {
                _this.carDialog.close();
            });
        },
        removeLayerEvent: function() {
            $("body").undelegate(".popupCar");
        },
        querySkuListByCarModelId: function (modelId) {
            var _this = this,
                ipLocDjd=Tools.getAreaId().areaIds,
                provinceId=ipLocDjd[0],
                cityId=ipLocDjd[1],
                areaId=ipLocDjd[2],
                townId=ipLocDjd[3];

            $.ajax({
                url: '//cd.jd.com/auto/skubymodelidarea',
                data: {
                    modelId: modelId,
                    skuId: _this.cfg.skuid,
                    provinceId:provinceId,
                    cityId:cityId,
                    areaId:areaId,
                    townId:townId
                },
                dataType: 'jsonp',
                jsonpCallback: 'skubymodelidCallback',
                success: function (r) {
                    if(r.hasModelOrSku) {
                        if(r.model && r.model.length && r.model[0].skulist) {
                            //console.log('到我了2');
                            _this.skulist = r.model[0].skulist;

                            if(_this.skulist.length) {
                                var skuIndex = $.inArray(_this.cfg.skuid, _this.skulist);
                                if(skuIndex != - 1) {
//                                    console.log('skuIndex != - 1');
//                                    //当前sku存在于skulist
//                                    var $chooseVersionItems = $("#choose-version .dd");
//                                    $chooseVersionItems.find(".item").hide();
//                                    var colorsArr = [], sizeArr = [];
//
//                                    var colorSizeObj = _this.getColorSize();
//
//                                    for(var i = 0; i < _this.skulist.length; i++) {
//                                        var sku = _this.skulist[i];
//
//                                        var colorSizeSkuIds = colorSizeObj.colorSizeSkuIds;
//                                        for(var j in colorSizeSkuIds) {
//                                            var sizes = colorSizeSkuIds[j];
//                                            for(var k in sizes) {
//                                                if(sku == sizes[k]['*']) {
//                                                    colorsArr.push(j);
//                                                    if($.inArray(k, sizeArr) == -1) {
//                                                        sizeArr.push(k);
//                                                    }
//                                                }
//                                            }
//                                        }
//                                    }
//
//                                    $("#choose-color").find('.item').hide();
//                                    var $item;
//                                    for(var m in colorsArr) {
//                                        $item = $("#choose-color").find(".item a[title='" + colorsArr[m] + "']").parents(".item");
//                                        if(!$item.hasClass("disabled")) {
//                                            $item.show();
//                                        }
//                                    }
//                                    for(var n in sizeArr) {
//                                        $item = $("#choose-version").find(".item a[title='" + sizeArr[n] + "']").parents(".item");
//                                        if(!$item.hasClass("disabled")) {
//                                            $item.show();
//                                        }
//                                    }
//
//                                    _this.updateCarModelTxt(r.userDefaultCar, 'pipei');
                                    //console.log('我了12222');
                                    callback.call(_this);
//                                } else {
//                                    //当前sku不存在于skulist
//                                    if(_this.skulist.length) {
//                                        //跳转到第一个
//                                        window.location.href = '//item.jd.com/' + _this.skulist[0] + '.html';
//                                    }
                                } else {
                                    //console.log('当前sku不在list数组里，跳转推荐');
                                    //console.log(r);
                                    location.href = '//item.jd.com/' + _this.skulist[0] + '.html';
                                }
                            } else {
                                //console.log('list数组长度为0，提示没有匹配信息');
                                //console.log(r);
                                _this.updateCarModelTxt(null, null);
                            }
                        } else {
                            //console.log('model为空，数组长度为0，提示没有匹配信息');
                            //console.log(r);
                            _this.updateCarModelTxt(null, null);
                        }
                    } else {
                        //console.log('提示没有匹配信息');
                        //console.log(r);
                        _this.updateCarModelTxt(null, null);
                    }
                }
            });
        },
        getColorSize: function () {
            var _this = this;
        },

        updateCarModelTxt: function (carInfo, type) {
            var _this = this;
            var pipeiTxt = '';
            var btnStr = '';
            var chooseCarTxt = '';

            if (carInfo) {
                var carStr = carInfo.brand_name + " " + carInfo.series_name + " " + carInfo.series_year_name + "款 " + carInfo.model_name;
                chooseCarTxt = '<span class="tips" title="' + carStr + '" data-carmodelid="' + carInfo.model_id + '" data-carid="' + carInfo.id + '">' + carStr + '</span>';
            }

            if (type == 'pipei') {
                pipeiTxt += '与商品匹配';
                btnStr = '修改';
                _this.$selcancelOP.show();
            } else if (type == 'bupipei') {
                pipeiTxt += '无匹配商品，谨慎购买';
                btnStr = '修改';
                _this.$selcancelOP.show();
            } else {
                chooseCarTxt = '请选择车型，为您精确匹配商品';
                btnStr = '选择';
            }
            _this.$wraningTips.html(pipeiTxt);
            _this.$chooseCarTips.html(chooseCarTxt);
            _this.$chooseOP.html(btnStr);
        },

        //是否显示车型选项
        isShowCarType:function(){
            var arrShowCarTypeCatId=[6766,6767,9248,9971,11849,11852,11859];
            if($.inArray(arrShowCarTypeCatId,pageConfig.product.cat[2])){
                $('#choose-car').addClass('show');
            }
        },
        //加载匹配的商品
        loadMatchGoods:function(){
            $('.match-goods-con > div').removeClass('show');
            $('.match-goods-con ul').removeClass('show');

            if( $('.J-car-item.current').length==0 ){
                $('.match-goods-con > div.match-goods-choose').addClass('show');
                return;
            }

            var _this=this,
                matchSkuId=[],
                ipLocDjd=Tools.getAreaId().areaIds,
                provinceId=ipLocDjd[0],
                cityId=ipLocDjd[1],
                areaId=ipLocDjd[2],
                townId=ipLocDjd[3];

            var currentModel = $('.J-car-item.current').attr('data-modelid') || "";

            var postData={
                modelId:currentModel,
                skuId:this.cfg.skuid,
                provinceId:provinceId,
                cityId:cityId,
                areaId:areaId,
                townId:townId,
                appid: 'item-v3',
                functionId: "pc_auto_skubymodelidarea_v2"
            };

            $('.match-goods-loading').addClass('show');
            //cd.jd.com/auto/skubymodelid?modelId=24075&skuId=1218423
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/auto/skubymodelidarea_v2',
                data: postData,
                scriptCharset: 'gbk',
                timeout:3000,
                dataType: "jsonp",
                success: function (res) {
                    if(res){
                        _this.choosedCarmodelid=$('.J-car-item.current').attr('data-modelid');
                        var matchsArr = [];
                        for(var i = 0; i < res.length; i++){
                            var list = res[i];
                            if(list.gifts && list.gifts.length && list.gifts[0].mainImage && list.gifts[0].sku){
                                list.hasGift = true;
                                list.gifts = list.gifts[0];
                            }else{
                                list.hasGift = false;
                            }
                            matchsArr.push(list);
                        }
                        var htmlStr = matchGoodsList.process({matchs:matchsArr, skuid: _this.cfg.skuid});
                        $('.match-goods-list li').remove();
                        $('.match-goods-loading').removeClass('show');
                        $('.match-goods-list').html(htmlStr);
                        $('.match-goods-list,.love-car').addClass('show');
                    } else {
                        _this.noFilterGoods();
                    }
                },
                error:function(){
                    _this.noFilterGoods();
                }
            });   
        },
        
        // 无匹配商品时操作
        noFilterGoods: function(){
            var _this = this;
            $('.match-goods-loading').removeClass('show');
            $('.love-car').addClass('show');
            $('.match-goods-con > div.no-match-goods').addClass('show');
            _this.setCurrentModelId()
        },
        // 更改无适配车型的ID
        setCurrentModelId: function () {
            var _this = this;
            var $noMatchGoods = $('.match-goods-con > div.no-match-goods'),
                _cid = '';
            if(_this.cfg && _this.cfg.cat){
                _cid = _this.cfg.cat[2]||''
            }
            var htmlStr = noMatchGoods.process({cid:_cid, modelid: _this.choosedCarmodelid});
            $noMatchGoods.html(htmlStr)
        },
        //删除车型
        deleteCar: function($me,_this){
            //cd.jd.com/auto/delmodel?userPin=davidwei_001&id=0000
            var postData={
                id:$me.parents('.J-car-item').attr('data-id')
            };
            $.ajax({
                url: '//cd.jd.com/auto/delmodel',
                data: postData,
                scriptCharset: 'gbk',
                timeout:3000,
                dataType: "jsonp",
                success: function (response) {
                    if(response.isSuccess){
                        if(response.userModels){
                            _this.choosedCarmodelid=response.userModels[0].model_id;
                            _this.userCarArr=response.userModels;

                        }else{
                            response.userModels=[];
                            _this.userCarArr=response.userModels;
                        }
                        var htmlStr = carDialogLoveCarList.process({cars:_this.userCarArr, choosedCarmodelid:_this.choosedCarmodelid , skuid: _this.cfg.skuid});
                        $('.love-car .car-list').remove();
                        $('.love-car h5').after(htmlStr);

                        if( $('.love-car li.J-car-item').length<4 ){
                            $('.love-car p.tips').remove();
                        }
                        _this.loadMatchGoods();

                    }else{
                        $('.match-goods-con > div').removeClass('show');
                        $('.match-goods-con ul').removeClass('show');

                        $('.match-goods-con > div.deleteError').addClass('show');
                    }
                },
                error:function(){
                    $('.match-goods-con > div').removeClass('show');
                    $('.match-goods-con ul').removeClass('show');

                    $('.match-goods-con > div.deleteError').addClass('show');
                }
            });
        }
//        setDefaultCar: function (carId) {
//            var pin = readCookie('pin');
//            if(!pin) {
//                return;
//            }
//            $.ajax({
//                url: '//cd.jd.com/auto/setdefaultcar',
//                data: {
//                    userPin: pin,
//                    id: carId
//                },
//                dataType: 'jsonp',
//                jsonpCallback: 'setdefaultcar',
//                success: function (r) {
//                    if(r.setDefaultCar) {
//                        window.location.reload();
//                    }
//                }
//            });
//        },
    };
//    function setCancel(isCancel) {
//        var pin = readCookie('pin');
//        if (!pin) {
//            return;
//        }
//        var status = isCancel ? 1 : 0;
//        $.ajax({
//            url: '//cd.jd.com/auto/setcancel',
//            data: {
//                isCancel: status,
//                userPin: pin
//            },
//            dataType: 'jsonp',
//            jsonpCallback: 'setcancel',
//            success: function (r) {
//                if(r.status == status) {
//                    window.location.reload();
//                }
//            }
//        });
//    }

    function init(cfg) {
        PopupCar.init(cfg);
    }

    module.exports.__id = 'popupCar';
    module.exports.init = init;
});
