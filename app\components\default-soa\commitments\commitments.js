define('MOD_ROOT/commitments/commitments', function (require, exports, module) {

    var G = require('MOD_ROOT/common/core');
    var loveDdHoverInfo =  pageConfig && pageConfig.product && pageConfig.product.loveDdHoverInfo
    var loveDdText = loveDdHoverInfo && loveDdHoverInfo.loveDdText
    var loveDdImgUrl = loveDdHoverInfo && loveDdHoverInfo.loveDdImgUrl
    var careTemplate = loveDdText ? '\
        <i class="sprite-arrow"></i>\
        <div class="sprite-heartPic" style="background-image: url('+loveDdImgUrl+');"></div>\
        <p>'+loveDdText+ '</p>\
        <a style="display:none;" href="#" target="_blank">详细 &gt;&gt;</a>'
        :'\
        <i class="sprite-arrow"></i>\
        <div class="sprite-heartPic"></div>\
        <p>"公益好物：商家承诺每完成销售一笔订单，将捐赠一定金额用于公益项目执行。"</p>\
        <a style="display:none;" href="#" target="_blank">详细 &gt;&gt;</a>';

    var Commitments = function (sku) {
        this.init(sku);
    }

    Commitments.prototype = {
        $el: $('#commitments'),
        init: function (sku) {
            this.sku = sku;
            if(G.onAttr('isCare')) {
                this.addEvents();
                this.loadCare();
            }
        },
        addEvents: function() {
            var _this = this;
            var $heart = _this.$el.find(".heart");
            $heart.hover(function() {
                $(this).addClass("heart-hover");
            }, function () {
                $(this).removeClass("heart-hover");
            });
        },
        loadCare: function () {
            var _this = this;
            
            require.async(['MOD_ROOT/commitments/commitments.css'], function () {
                _this.$el.find(".heart .commitments-tips").html(careTemplate.process({}));
                _this.$el.show();
            });
            //屏蔽于 2018-06-06 产品 wangxi21 展示内容为固定文案，无须请求接口
            // $.ajax({
            //     url: '//cd.jd.com/loveProject/' + _this.sku,
            //     dataType: 'jsonp',
            //     scriptCharset: 'gbk',
            //     success: function (r) {
            //         if (r.name && r.webUrl) {
            //             require.async(['MOD_ROOT/commitments/commitments.css'], function () {
            //                 _this.$el.find(".heart .commitments-tips").html(careTemplate.process(r));
            //                 _this.$el.show();
            //             });
            //         }
            //     }
            // });
        }
    }

    function init(cfg) {
        new Commitments(cfg.skuid);
    }

    module.exports.__id = 'commitments';
    module.exports.init = init;
});
