#ie6tip {
    zoom: 1;
    .left-con {
        margin-top: 70px;
        margin-left: 44px;
        margin-right: 40px;
        width: 186px;
        height: 182px;
        background: url(i/left-main.png) no-repeat 0 0;
        float: left;
        display: inline;
    }

    .right-con {
        float: left;
        margin-top: 42px;
        color: #000;

        .tip1 {
            line-height: 20px;
            font-size: 18px;
            margin-bottom: 20px;
        }
        .tip2 {
            font-size: 16px;
            margin-bottom: 30px;
        }

        ul li {
            float: left;
            display: inline;
            width: 73px;
            margin: 0 22px;
            text-align: center;

            div {
                margin-bottom: 12px;
            }

            .img {
                width: 73px;
                height: 73px;
                display: block;
                margin-bottom: 12px;
            }

            .btn a {
                padding: 2px 5px;
                color: #6A77B6;
                border: 1px solid #b1b5d6;
                &:hover {
                    color: #e64346;
                    border: 1px solid #f2a1a2;
                }
            }
        }

        .chrome .img {
            background: url(i/chrome.png) no-repeat 0 0;
        }

        .qq .img {
            background: url(i/qq.png) no-repeat 0 0;
        }

        .ie .img {
            background: url(i/ie.png) no-repeat 0 0;
        }
    }
}