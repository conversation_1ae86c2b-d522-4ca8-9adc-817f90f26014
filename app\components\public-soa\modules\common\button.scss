@import "lib.scss";

/**
 * 按钮
 */
@mixin btn-size($size...){
    @if length($size) > 1{
        width : nth($size,2);
        height : nth($size,1);
        line-height : nth($size,1);
    }@else if length($size) == 1{
        height: $size;
        line-height: $size;
    }
}

%btn {
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;

    &:hover{
        text-decoration: none;
    }
}

// Button skins, all skins will @extend %btn
%btn-gray{
    @extend %btn;
    border: 1px solid #ddd;
    background-color: #f8f8f8;
    color: #666;

    &:hover{
        color:#333;
    }
}

%btn-red{
    @extend %btn;
    background-color: #df3033;
    color: #fff;

    &:hover{
        color: #fff;
    }
}

%btn-green{
    @extend %btn;
    background-color: #FF475D;
    color: #fff;

    &:hover{
        color: #fff;
    }
}


/**
 * Button types
 */

// default button
.btn-def{
    @extend %btn-gray;
    @include btn-size(23px);
    padding: 0 10px;
}

// primary button
.btn-primary{
    @extend %btn-red;
    @include btn-size(28px);
    padding: 0 16px;
}

// special button type1
.btn-special1{
    @extend %btn-red;
    @include btn-size(23px);
    font-size: 18px;
    font-weight:bold;
}

// special button type2
.btn-special2{
    @extend %btn-green;
    @include btn-size(23px);
    font-size: 18px;
    font-weight:bold;
}
// special button type3
.btn-special3{
    @extend %btn-green;
    @include btn-size(23px);
    font-size: 18px;
    background: #fff;
    border: 1px solid $colorPriceRed;
    color: #e4393c;
    &:hover{
        color:$colorPriceRed;
    }
}
// 开通PLUS按钮
.btn-buyplus,.btn-buyplus:hover{
    background-color: #414141;
    color: #fff38d;
}

/**
 * Button size
 */

// small button
.btn-sm{
    @include btn-size(19px);
}

// large button
.btn-lg{
    @include btn-size(46px);
    padding: 0 26px;
    font-size: 18px;
    font-family: $font-yahei;
}

.notify-stock{
    background: #fff;
    border: 1px solid #e4393c;
    color: #e4393c;
    &:hover {
        color: #e4393c;
    }
}

/**
 * Button state
 */
.btn-disable{
    border:1px solid #ebebeb;
    color:#ccc;
    background-color: #f7f7f7;
    cursor: not-allowed;

    &:hover{
        border-color:#ebebeb;
        color:#ccc;
    }
}

//到货通知
//.notify-stock {
//    background: #fff;
//    border: 1px solid #e4393c;
//    color: #e4393c;
//    &:hover{
//        color:#e4393c;
//    }
//}

/*二手*/
.ershou{
    .btn-primary{
        background: $baseColorErshou;
        &:hover{
            color: #fff;
        }
    }
    .btn-primary{
        background: $baseColorErshou;
    }

    .btn-special1{
        background: $baseColorErshou;
    }
    .btn-disable{
        border:1px solid #ebebeb;
        color:#ccc;
        background-color: #f7f7f7;
        cursor: not-allowed;

        &:hover{
            border-color:#ebebeb;
            color:#ccc;
        }
    }
}


