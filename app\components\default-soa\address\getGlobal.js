define('MOD_ROOT/address/getGlobal', function(require, exports, module) {
    var Tools = require('MOD_ROOT/common/tools/tools');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    function init() {
        $.ajax({
            url: '//item-soa.jd.com/getWareBusiness',
            cache: true,
            dataType: 'jsonp',
            data: {
                skuId: pageConfig.product.skuid + '',
                cat: pageConfig.product.cat.join(','),
                area: Tools.getAreaId().areaIds.join('_'),
                shopId: pageConfig.product.shopId,
                venderId: pageConfig.product.venderId+'',
                paramJson: pageConfig.product.paramJson,
                num: 1
            },
            success: function (data) {
                // Promotions.isPlus = false;
                // Promotions.isRealPlus = false;
                // if(data && data.isPlusMember){
                //     var plusMember = data.isPlusMember;
                //     Promotions.isRealPlus = plusMember == "1"||plusMember=="2"?true:false;
                //     Promotions.isPlus = plusMember == "1"||plusMember=="2"?true:false;
                //     Promotions.isRealzxPlus = plusMember == "1"?true:false;
                // }
                // Promotions.isSam = (data && data.isSamMember)?true:false;
                // Promotions.isLogin = (data && data.isLogin )?true:false;

                //if(data && data.price){
                if(data){
                    // Promotions.plusTags = data.price && data.price.plusTag;
                    //data.type = 'onWareBusinessReady'
                    // data.isRealPlus = Promotions.isRealPlus
                    // data.isRealzxPlus = Promotions.isRealzxPlus
                    // data.isPlus = Promotions.isPlus
                    // data.isSam = Promotions.isSam
                    // data.discount = data.discountPriceInfo
                    // 触发价格加载完成事件
                    Event.fire({
                        type: 'onWareBusinessReady',
                        // isRealPlus: Promotions.isRealPlus,
                        // isRealzxPlus: Promotions.isRealzxPlus,
                        // isPlus: Promotions.isPlus,
                        // isSam: Promotions.isSam,
                        discount: 111,
                        data: data
                    })
                    
                }

            }
        });
    }
    init()
    module.exports = init
})
