define('MOD_ROOT/buybtn/buybtn', function(require, exports, module) {
    require('JDF_UI/dialog/1.0.0/dialog');
    require('PLG_ROOT/jQuery.scroller');
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var tools = require('MOD_ROOT/common/tools/tools');
    var colorSize   = require('MOD_ROOT/colorsize/colorsize');
    var reservation = require('MOD_ROOT/buybtn/reservation');
    var ko          = require('MOD_ROOT/buybtn/ko');
    var bgm         = require('MOD_ROOT/buybtn/bigouma');
    var notif       = require('JDF_UNIT/notif/1.0.0/notif');
    var login       = require('JDF_UNIT/login/1.0.0/login');
    var GiftPool = require('MOD_ROOT/gift/gift');
    var Guobu = require('MOD_ROOT/buybtn/guobu');

    var plantabid          = decodeURI(G.serializeUrl(location.href).param.purchasetab)// url上区分单品和企业计划购
    var plantabidNum       = plantabid && plantabid.indexOf("qyjhg") != -1  // url参数plantab只有等于qyjhg的时候调用
    var plantabidYjhx       = plantabid && plantabid.indexOf("yjhx") != -1  // url参数plantab只有等于以旧换新的时候调用
    var plantabidGfgm       = plantabid && plantabid.indexOf("gfgm") != -1  // url参数plantab只有等于官方购买的时候调用
    var btnBag              = [] // 存放合并的按钮
    var shipInstallService = [] // 存放送装服务
    var dqgLoaded = false // 确保定期购只执行一次

    // 购买数量加减
    window.setAmount = {
        init: function () {
            this.min = 1;
            this.max = 999;
            this.count = 1;

            this.disableAdd = false;
            this.disableReduce = true;

            this.$buyNum = $('#buy-num');
            this.$buyBtn = $("#InitCartUrl,#InitCartUrl-mini");

            this.$add = $('#choose-btns .btn-add');
            this.$reduce = $('#choose-btns .btn-reduce');

            this.matchCountKey = ['pcount', 'pCount', 'num', 'buyNum','number'];

            if (this.$buyNum.length < 1) return false;

            this.$buyNum.attr('autocomplete', 'off')

            this.checkLimit();
            this.handleChange();
            // invisible 时由占位不可见改为可见不可点
            if ($('.choose-amount').hasClass('invisible') || $('.choose-amount').css('visibility') == 'hidden') {
                $('.choose-amount').css('visibility', 'visible')
                this.disableAll()
            }
            this.bindEvent();
        },
        bindEvent: function () {
            var _this = this;

            this.$buyNum.unbind('keydown keyup')
                .bind('keydown keyup', tools.throttle($.proxy(this.handleChange, this), 500));

            // setTips(this.$reduce, '{0} 最少购买 {1} 件');
            // setTips(this.$add, '{0} 最多购买 {1} 件');

            // function setTips($el, template) {
            //     $el.ETooltips({
            //         close: false,
            //         content: '<div class="min-buy-tips"></div>',
            //         width: 150,
            //         pos: 'bottom',
            //         zIndex: 10,
            //         onOpen: function () {
            //             var img = '<img src="//img20.360buyimg.com/da/jfs/t2734/145/4239060100/1006/b6d0f0d8/57b4240fN9cc48b02.png" />';
            //             this.$tooltips.find('.min-buy-tips').html(template.format(img, _this.$buyNum.val()));
            //         }
            //     });
            // }
            this.$buyNum.click(function() {
                tools.landmine({
                    functionName: 'PC_Productdetail_ProductNumEditBox_Click',
                    exposureData: ['mainskuid'],
                    extraData: {
                        buttonName: '数字区'
                    },
                    errorTips: '商品数量编辑框埋点报错'
                })
            })
            this.$add.click(function(){
                var hasDisable = $(this).hasClass("disabled")
                
                if(hasDisable){
                    $(".choose-amount").find(".tips").css("right","-20px").html("最多购买"+_this.max+"件").show()
                    setTimeout(function() {
                        $(".choose-amount").find(".tips").removeAttr("style").hide()
                    }, 1000);
                }
                tools.landmine({
                    functionName: 'PC_Productdetail_ProductNumEditBox_Click',
                    exposureData: ['mainskuid'],
                    extraData: {
                        buttonName: '加号'
                    },
                    errorTips: '商品加号埋点报错'
                })

            })

            this.$reduce.click(function(){
                var hasDisable = $(this).hasClass("disabled")
                
                if(hasDisable){
                    $(".choose-amount").find(".tips").css("left","-10px").html("最少购买"+_this.min+"件").show()
                    setTimeout(function() {
                        $(".choose-amount").find(".tips").removeAttr("style").hide()
                    }, 1000);
                }
                tools.landmine({
                    functionName: 'PC_Productdetail_ProductNumEditBox_Click',
                    exposureData: ['mainskuid'],
                    extraData: {
                        buttonName: '减号'
                    },
                    errorTips: '商品减号埋点报错'
                })
            })

            function updateDataMax(r) {
              if (r && r.stock && r.stock.data && r.stock.data.commonLimitInfo) {

                // 限购数量
                // 需求文档：https://joyspace.jd.com/pages/jOJOXf8w9OxKTnUnprD3
                var max = r.stock.data.commonLimitInfo.mergeMaxBuyNum * 1
                var min = r.stock.data.commonLimitInfo.limitMinNum * 1

                // 最大限购，兼容接口不下发或者无值的情况
                if(max) {
                    _this.max = max
                    // _this.$buyNum.attr('data-max', max)
                }

                // 最小限购，兼容接口不下发或者无值的情况
                if(min) {
                    _this.min = min
                    // _this.$buyNum.attr('data-min', min)

                    // 最小限购大于默认值，重置为最小限购
                    if(_this.count < min){
                        _this.count = min
                        _this.setVal(min);
                    }
                }
              }
            }
            Event.addListener('onStockReady', updateDataMax);
        },
        disabledReduce: function (showTips) {
            this.disableReduce = true;
            this.disableAdd = false;
            this.$reduce.addClass('disabled');
            this.$add.removeClass('disabled');
        },
        disabledAdd: function (showTips) {
            this.disableAdd = true;
            this.disableReduce = false;
            this.$add.addClass('disabled');
            this.$reduce.removeClass('disabled');
        },
        enabledAll: function () {
            this.disableAdd = false;
            this.disableReduce = false;
            this.$reduce.removeClass('disabled') // .attr('data-disabled', '1');
            this.$add.removeClass('disabled') // .attr('data-disabled', '1');
        },
        disableAll: function() {
            this.disableAdd = true;
            this.disableReduce = true;
            this.$buyNum.addClass('disabled')
            this.$reduce.addClass('disabled')
            this.$add.addClass('disabled')
        },
        getVal: function () { return this.$buyNum.val(); },
        setVal: function (val) {
            this.$buyNum.val(val).attr('title', val);
        },
        checkLimit: function () {
            var value = Number(this.getVal());

            // 初始化时不展示tips，其他情况展示tips
            // 当value小于等于或等于min时，禁用减号
            if (value <= this.min) this.disabledReduce();

            // 当value大于等于max时，禁用加号
            if (value >= this.max) this.disabledAdd(true);
            
            // 当value大于min且小于max时，启用所有按钮
            if (value > this.min && value < this.max) this.enabledAll();
        },
        isEmpty: function (val) { return $.trim(val) == ''; },
        isFloat: function (n) { return Number(n) === n && n % 1 !== 0; },
        add: function() {
            var value = Number(this.getVal());
            if ( this.disableAdd || this.isEmpty(value) ) return false;

            if (value > this.min) {
                this.disableReduce = false;
            }

            if ( value >= this.max ) {
                // this.setDisabled(this.$add);
                this.disableAdd = true;
                return false;
            } else {
                this.disableAdd = false;
                // this.setEnabled(this.$add);
                this.count++;
            }

            this.setVal(this.count);
            this.checkLimit();
            this.setBuyLink();
        },
        reduce: function() {
            var value = Number(this.getVal());
            if ( this.disableReduce || this.isEmpty(value) ) return false;

            if (value < this.max) {
                this.disableAdd = false;
            }

            if ( value <= this.min ) {
                // this.setDisabled(this.$reduce);
                this.disableReduce = true;
                return false;
            } else {
                // this.setEnabled(this.$reduce);
                this.disableReduce = false;
                this.count--;
            }

            this.setVal(this.count);
            this.checkLimit();
            this.setBuyLink();
        },
        handleChange: function () {
            var value = this.getVal();
            var result = null;

            // 非法字符
            if (isNaN(Number(value)) || this.isEmpty(value) || this.isFloat(Number(value))) {
                result = this.count;
            } else {
                // 小于最小值
                if (value < this.min) {
                    result = this.min;
                    this.disabledReduce(result!==1);
                }
                // 大于最大值
                if (value > this.max) {
                    result = this.max;
                    this.disabledAdd(true);
                }
            }

            if (result) {
                this.count = result;
                this.setVal(result);
            } else {
                this.count = Number(value);
                this.$buyNum.attr('title', value);
            }

            this.checkLimit();
            this.setBuyLink();
        },
        modify: function() {},
        setBuyLink: function() {
            var _this = this;

            _this.$buyBtn.each(function() {
                var $this = $(this),
                    orginHref = $this.attr("href"),
                    param = orginHref.split("?")[1],
                    res, re;

                (function() {
                    for (var h = 0; h < _this.matchCountKey.length; h++) {
                        re = new RegExp(_this.matchCountKey[h] + "=\\d+");
                        if (re.test(param)) {
                            res = orginHref.replace(re, _this.matchCountKey[h] + "=" + _this.count);
                            $this.attr("href", res);
                            pageConfig.product.cartBuyUrlParam = res // 全局购物车链接参数
                            return false
                        }
                    }
                })();
            });
            pageConfig.product.countNum=this.count;
            Event.fire({
                type: 'onNumChange',
                count: this.count
            });
        }
    };
    setAmount.init();

    var addToCartBtn = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#InitCartUrl,#InitCartUrl-mini');
            this.cName = 'btn-disable';
            this.originHref = this.$el.attr('href');
            this.href = this.$el.attr('href');

            /// 网上冲印按钮
            this.$filmPrintBtn = $('#flimPrintUrl');
            if (this.$filmPrintBtn.length) {
                this.flimPrintUrl = this.$filmPrintBtn.attr('href');
            }

            this.bindEvent(cfg);
            return this;
        },
        reInit: function ($el) {
            var btnHTML = '<a href="'+ this.cfg.addToCartUrl +'" id="InitCartUrl" class="btn-special1 btn-lg" clstag="shangpin|keycount|product|加入购物车_1">加入购物车</a>';

            if ($('#InitCartUrl').length < 1) {
                $el.before(btnHTML);
            }
            // 用来做按钮降级用
            this.init(this.cfg);
            this.disabled();
            this.enabled();
        },
        bindEvent: function (cfg) {
            var _this = this;

            function handleFilmPrintButtonState(flag) {
                if (flag) {
                    _this.$filmPrintBtn.removeClass(_this.cName);
                    _this.$filmPrintBtn.attr('href', _this.flimPrintUrl);
                } else {
                    _this.$filmPrintBtn.addClass(_this.cName);
                    _this.$filmPrintBtn.attr('href', '#none');
                }
            }

            function detectBuyBtn() {
                if ( cfg.havestock ) {
                    _this.enabled();
                } else {
                    _this.disabled();
                }
                if (_this.$filmPrintBtn.length) {
                    handleFilmPrintButtonState(cfg.havestock);
                }
            }
            Event.addListener('onStockReady', detectBuyBtn);
        },
        show: function () {
            // 合约机、预售、必购码 不显示加入购物车按钮
            var disabled = this.cfg.isHeYue || this.cfg.isYuShou || this.cfg.isBiGouMa || this.cfg.isKO;

            if (disabled) { return false; }

            this.$el.show();
        },
        hide: function () {
            this.$el.hide();
        },
        updateNum: function (href) {
            var num = $('#buy-num').val();
            if (href) {
                return href.replace(/(nums|num|pcount|buyNum|number)=\d+/g, '$1' + '=' + num)
            }
        },
        disabled: function () {
            var href = this.$el.attr('href');

            this.$el.addClass(this.cName);
            this.$el.attr('href', '#none');

            return href;
        },
        enabled: function (href) {
            href = this.updateNum(href || this.href);

            var isDisabled = this.cfg.isClosePCShow || !this.cfg.havestock;

            if ( isDisabled ) {
                return false;
            }
            this.setEnable(href)
        },
        setEnable: function (href) {
            this.$el.removeClass(this.cName);
            this.href = href;
            this.$el.attr('href', href);
        },
        setDisable: function () {
            return this.disabled()
        }
    };

    var notify = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#btn-notify');// 到货通知

            // 降价通知
            notif({
                el:$('.J-notify-sale')
            });

            // 到货通知
            notif({
                el:$('.J-notify-stock')
            });
            
            this.bindEvent();
            
            $('.J-notify-sale').click(function() {
                tools.landmine({
                    functionName: 'PC_Productdetail_ReducedPriceNoticeBtn_Click',
                    exposureData: ['mainskuid'],
                    errorTips: '降价通知埋点报错'
                })
            })
            
            $(window).resize(this.changeNoticeStyle)

            return this;
        },
        show: function () {
            if (!this.cfg.havestock && !this.cfg.unSupportedArea) {
                this.$el.show();
            } else {
                this.hide();
            }
        },
        hide: function () {
            this.$el.hide();
        },
        changeNoticeStyle: function() { // 适配宽窄版，显示不开时隐藏降价通知
            // 此padding 有两个作用，1.给价格区和累计评价之间增加间距，防止极端情况下间距为 0
            // 2. 此方法执行时，京东字体还未加载完成，字体加载完成后价格区会变宽，给字体变宽预留 10px，此数字可以适当增大 
            $('.commentNotice').css({paddingLeft: '10px'}) 
            var height = $('.summary-price-wrap').height()
            
            if (window.innerWidth < 1680) {
                height > 23 && $('.J-notify-sale').hide()
            } else {
                $('.J-notify-sale').show()
            }
            // 取消 padding，把预留的空间给字体
            $('.commentNotice').css({paddingLeft: '0px'})
        },
        bindEvent: function () {
            var _this = this;

            Event.addListener('onStockReady', function () {
                _this.show();
                _this.changeNoticeStyle()
            });
            Event.addListener('commentChange', function () {
                _this.changeNoticeStyle()
            })
            // window.addEventListener('load', function () {
            //     _this.changeNoticeStyle()
            // });
        }
    };

    var inventory = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#btn-inventory');
            this.$buyBtn = $("#InitCartUrl");
            var _this = this;
            var sku = _this.cfg.skuid
            if (this.$buyBtn.length > 0) { // 购物车按钮曝光埋点
                try {
                    expLogJSON('smb_pc', 'AddCart_Expo', '{"SKUID": ' + sku + ',"Pin":' + readCookie('pin') + '}');
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('购物车按钮曝光埋点错误');
                    }
                }
            }
            
            var bjdInsert = true
            Event.addListener('onStockReady', function (data) {
                var serviceskuid = tools.getServiceSkuid(data.stock && data.stock.data && data.stock.data.warrantyInfo)
                var isCanAddCart = data.stock && data.stock.data && data.stock.data.canAddCart
                if(isCanAddCart && pageConfig.product){ // 融合接口下发展示以及开关true，2025/04/15 去掉 businessBuySwitch 开关
                    _this.$el.show()
                    tools.otherBtnExpo(_this.$el.text(), serviceskuid)
                    // var prefix = 'https://storage.360buyimg.com/bjd-utils-sdk/bjdcommon/purchaselist/1.0.4/bjd.'
                    try {
                      // 默认兜底
                      var bjdjs = 'https://storage.360buyimg.com/bjd-utils-sdk/bjdcommon/purchaselist/1.0.4/bjd.js'
                      var bjdcss = 'https://storage.360buyimg.com/bjd-utils-sdk/bjdcommon/purchaselist/1.0.4/bjd.css'

                      if (window.pageConfig && window.pageConfig.assets && window.pageConfig.assets.bjdjs) {
                        bjdjs = window.pageConfig.assets.bjdjs
                      }

                      if (window.pageConfig && window.pageConfig.assets && window.pageConfig.assets.bjdcss) {
                        bjdcss = window.pageConfig.assets.bjdcss
                      }
                      if(bjdInsert){// 加载一次采购清单组件
                        _this.loadResource('script', bjdjs)
                        _this.loadResource('link', bjdcss)
                        bjdInsert = false
                      }
                     
                    } catch (error) {
                      console.error('bjd配置加载异常', JSON.stringify(error || {}))
                    }
                    try { // 加入采购清单曝光埋点
                        expLogJSON('smb_pc', 'EnterpiseCart_Expo', '{"SKUID": ' + sku + '}');
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.log('加入采购清单曝光埋点错误');
                        }
                    }
                }else{
                    _this.$el.hide()
                }
            });

            this.bindEvent();

            return this;
        },
        loadResource: function (tagName, url, callback) { // 加载采购清单 js & css
            var source = document.createElement(tagName);
            if (tagName == 'script') {
                source.src = url
            } else { // link
                source.rel = 'stylesheet';
                source.href = url
            }
            source.onload = function() {
                callback && callback()
            }
            source.onerror = function() {
                console.error(url + ' 资源加载出错');
            }
            document.body.appendChild(source);
        },
        bindEvent: function () {
            var _this = this;
            var sku = _this.cfg.skuid
            $("#btn-inventory").click(function() {
                try {
                    var getPlugin = window.bproCommonContext.getPlugin;
                    var addToPurchaseListPlugin = getPlugin('addToPurchaseList');
                    // 展示加入采购清单弹窗
                    // addToPurchaseListPlugin.showModal({ skuId: sku})
                    addToPurchaseListPlugin.showModal({ skuList:[{"skuId":sku,"skuNum":$('#buy-num').val()||"1"}]}) 
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('加入采购清单插件报错');
                    }
                }
                try {// 加入采购清单点击埋点
                    var selectedSkuid = tools.getSelectedSkuid()
                    tools.otherBtnClick($(this).text(), selectedSkuid)
                    log('smb_pc', 'EnterpiseCart_click', '{"SKUID ":' + sku + '}')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('加入采购清单点击埋点错误');
                    }
                }
            })
            // 后面问号提示
            var timeoutId = 0;
            $("#choose-btns").delegate('.btn-tips', 'mouseenter', function() {
                clearTimeout(timeoutId);
                $(this).addClass('hover');
            });

             $("#choose-btns").delegate('.btn-tips', 'mouseleave', function() {
                var $this = $(this);
                timeoutId = setTimeout(function() {
                    $this.removeClass('hover');
                }, 300);
            });
        }
    };

    var jnbt = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#choose-btn-jnbt');

            if (this.$el.length) {
                this.bindEvent();
            }
            return this;
        },
        loadQrcode: function(url) {
            var skuid=this.cfg.skuid;
            require.async('PLG_ROOT/jQuery.qrcode', function() {
                var qrcodeUrl =
                    url || '//m.jd.com/product/' + skuid + '.html?from=qrcode'

                $('#summary-mbuy .qrcode').html('').jdQrcode({
                    render: 'image',
                    ecLevel: 'L',
                    size: 145,
                    text: qrcodeUrl
                })
            })
        },
        // 北京才有节能补贴
        isTargetArea: function() {
            var pid = tools.getAreaId().areaIds[0];
            return pid === 1;
        },
        changeArea: function() {
            var $jnbt = this.$el;
            if ($jnbt.length < 1) return;

            this.show();

            if (this.cfg.havestock) {
                this.enabled();
            } else {
                this.disabled();
            }
        },
        bindEvent: function() {
            Event.addListener('onStockReady', $.proxy(this.changeArea, this));
            var $this=this
            var $jnbt = this.$el;
            var url = $jnbt.attr('data-url')
            var text = '<p>1. 点击参加节能补贴，最高可减免800元！</p> \
                <p>2. 节能补贴面向北京地区的用户，需您收货地址为北京且提供相应证件证明，\
                最高可享商品金额13%的减免（上限800元），并同时支持使用京东优惠券（暂不支持京东支付的营销活动）';
            var html =
                '\
                <p style="padding: 15px 30px;top: 60px;text-align: center;">\
                <span style="background: url(//img11.360buyimg.com/imagetools/jfs/t1/99478/39/16273/6095/5e798880Eb75106c9/db383dcd3bcb72a8.png) no-repeat 0 0;width: 95px;height: 29px;display: inline-block;background-size: 100%;"></span>\
                </p>\
                <p style="padding: 5px 30px;font-size: 14px;font-weight: 600;color: #999;text-indent: 24px;">使用京东APP V8.3.4以上版本扫码，可在商品页-活动楼层处，点击参加节能补贴，本单即可使用</p>\
                <div id="summary-mbuy" style="left:112px;width: 173px;margin-top: 35px;">\
                    <i></i>\
                    <div class="qrcode" style="margin-left: 15px;margin-top: 8px;">\
                        <div class="loading-style1"><b></b>加载中，请稍候...</div>\
                    </div>\
                </div>\
                <p style="padding: 0 30px;position: absolute;bottom: 68px;color: #999;">\
                <i style="display: inline-block;margin-right: 6px;vertical-align: -3px;width: 16px;height: 16px;background-image: url(//img12.360buyimg.com/imagetools/jfs/t1/88278/27/16409/599/5e798862Ebf6899ed/de23ce453bfe64c5.png);"></i>\
                仅北京地区用户可参加，记得注意收货地址嗷~</p>'    

            seajs.use('MOD_ROOT/ETooltips/ETooltips', function () {
                $jnbt
                    .ETooltips({
                        close: false,
                        content: text,
                        width: 265,
                        position: 'bottom',
                        zIndex: 10
                    });
            });

            $jnbt.click(function() {
                $('body').dialog({
                    width: 392,
                    title: '',
                    height: 429,
                    type: 'text',
                    maskClose: true,
                    source: html,
                    onReady: function() {
                        $this.loadQrcode(url)
                    }
                })
            })
            
            
        },
        enabled: function () {
            if (this.cfg.havestock) {
                this.$el.removeClass('btn-disable');
                // this.$el.attr('href', this.$el.attr("dataurl"))
                this.$el.attr('href', "#none")
            } else {
                this.disabled();
            }
        },
        disabled: function () {
            this.$el.addClass('btn-disable').attr('href', '#none');
        },
        show: function () {
            if (this.isTargetArea()) {
                this.$el.show();
            } else {
                this.hide();
            }
        },
        hide: function () {
            this.$el.hide();
        }
    };

    var yjhx = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#btn-goapp');
            var _this = this;
            if (this.$el.length) {
                Event.addListener('onStockReady', function (data) {
                    var hitOld4New = data.stock && data.stock.data && data.stock.data.hitOld4New // 静态模板依旧下发容器，融合层调依旧换新接口判断是否展示，hitOld4New： true展示   false
                    if(hitOld4New || (pageConfig.product && pageConfig.product.tradeInSwitch)){// tradeInSwitch  true老逻辑  false新逻辑，默认为false
                        _this.show();
                        // $(".common-plan").find(".yjhx").show()
                    }else{
                        _this.hide();
                        // $(".common-plan").find(".yjhx").hide()
                    }
                    if(plantabidYjhx){
                        $(".common-plan").find(".yjhx").addClass("curr").siblings().removeClass("curr")
                        $("#btn-goapp").show()
                        $(".J_choose_btn").remove()
                    }
                });
                this.bindEvent();
            }
            return this;
        },
        loadQrcode: function(url) {
            var skuid=this.cfg.skuid;
            var jumpChannel=this.cfg.jumpChannel;
            require.async('PLG_ROOT/jQuery.qrcode', function() {
                var qrcodeUrl =
                    url || 'https://api.m.jd.com/qrcode?appid=item-v3&functionId=pc_qrcode&skuId='+ skuid +'&position=2&isWeChatStock='+ jumpChannel

                $('#choose-btns .qrcode').html('').jdQrcode({
                    render: 'image',
                    ecLevel: 'L',
                    size: 147,
                    text: qrcodeUrl
                })
            })
        },
        bindEvent: function() {
            // Event.addListener('onStockReady', $.proxy(this.changeArea, this));
            var $this=this
            var $jnbt = this.$el;
            var url = $jnbt.attr('data-url')
            // var text = '<p>点击后使用京东APPV12.2.0及以上版本上扫码当前商品，可参加以旧换新</p>';
            // var html =
            //     '\
            //     <span class="close"></span>\
            //     <p class="title">\
            //     扫码二维码查看商品\
            //     </p>\
            //     <div id="summary-mbuy">\
            //         <div class="qrcode">\
            //             <div class="loading-style1">加载中，请稍候...</div>\
            //         </div>\
            //     </div>\
            //     <p class="bottom">\
            //     打开<span>手机京东</span> 扫码二维码\
            //     </p>'    

            // seajs.use('MOD_ROOT/ETooltips/ETooltips', function () {
            //     $jnbt
            //         .ETooltips({
            //             close: false,
            //             content: text,
            //             width: 265,
            //             position: 'bottom',
            //             zIndex: 10
            //         });
            // });

            // $jnbt.click(function() {
            //     $('body').dialog({
            //         width: 392,
            //         title: '',
            //         height: 429,
            //         type: 'text',
            //         maskClose: true,
            //         source: html,
            //         onReady: function() {
            //             $this.loadQrcode(url)
            //         }
            //     })
            // })

            var zIndex = $('.choose-btns-wrapper').css('z-index')
            $jnbt.hover(function() {
                $('.choose-btns-wrapper').css('z-index', 3) // 大于等于地址楼层 z-index
                $(".yjhxTip").show()
                $this.loadQrcode(url)
                
            }, function() {
                $(".yjhxTip").hide()
                $('.choose-btns-wrapper').css('z-index', zIndex)
            })

            $(".yjhxTip").find(".close").click(function(){
                $(".yjhxTip").hide()
            })

            
            
            
            
        },
        enabled: function () {
            if (this.cfg.havestock) {
                this.$el.removeClass('btn-disable');
                // this.$el.attr('href', this.$el.attr("dataurl"))
                this.$el.attr('href', "#none")
            } else {
                this.disabled();
            }
        },
        disabled: function () {
            this.$el.addClass('btn-disable').attr('href', '#none');
        },
        show: function () {
            var yjhx = G.specialAttrs["yjhx"];
            if (yjhx == 2 || yjhx == 1) {
                // this.$el.show();
                $(".common-plan").show()
                $(".common-plan").find(".gfgm").show()
                $(".common-plan").find(".yjhx").show()
            } else {
                this.hide();
            }
            Event.fire({
                type: 'tabShow',
            })
            tools.TabSwitch()// tab样式切换逻辑
        },
        hide: function () {
            $(".common-plan").find(".gfgm").hide()
            $(".common-plan").find(".yjhx").hide()
            if(tools.areAllChildrenHidden("#common-plan")){ // 判断tab都隐藏
                $(".common-plan").hide()// 隐藏tab切换
                Event.fire({
                    type: 'tabShow',
                })
            }
            tools.TabSwitch()// tab样式切换逻辑
        }
    };


    // 定期送
    var dqsBtn = function(cfg) {
        var $button = $('#btn-dqs');
        if ($button.length == 0) { return; }
        var shopId = cfg.shopId;
        var attribute = cfg.specialAttrs.join('|').match(/isTimeOrder\-([^\|]*)/);
        var isTimeOrder = attribute ? +attribute[1] : 0;
        // 构造服务链接，`613998`为光明牛奶店铺
        if ((isTimeOrder == 3 && shopId == 613998) || isTimeOrder == 5) {  // 特殊判断优先
            var url = '//ding-server.jd.com/period-buy-gm.html?skuId=' + cfg.skuid + '&frequency=5'
        } else if (isTimeOrder != 0) {  // 普通判断次之
            var url = '//ding.jd.com/orderPlan/toCreateOrderPlan.action?skuId=' + cfg.skuid + '&buyNum=' + $('#buy-num').val() + '&r=' + Math.random();
            var hasSkuNumber = true;
        } else {
            $button.addClass('btn-disable');
            return;
        }

        // 注册事件
        Event.addListener('onStockReady', function() {
            if (cfg.havestock) {
                $button.removeClass('btn-disable')
            } else {
                $button.addClass('btn-disable')
            }
        });

        $button.click(function() {
            var $this = $(this);
            var status = $this.hasClass('btn-disable');
            var params = {
                skuId: pageConfig.product.skuid + '',
                channel: '3',
                area: tools.getAreaId().areaIds.join('_'),
                source: 'PC'
            };
            var planInfoUrl = '//ding-server.jd.com/plan/toCreatePlanInfo'
            if (status) { return; }
            if (hasSkuNumber) {
                url = '//ding.jd.com/orderPlan/toCreateOrderPlan.action?skuId=' + cfg.skuid + '&buyNum=' + $('#buy-num').val() + '&r=' + Math.random();
            }
            login.isLogin(function(isLogin){
                if (isLogin) {
                    dqsType(planInfoUrl, params, url) 
                } else {
                    login({
                        modal: true,
                        complete: function(data) {
                            if (
                                data !== null 
                                && data.Identity 
                                && data.Identity.IsAuthenticated
                            ) {
                                dqsType(planInfoUrl, params, url)
                            }
                        }
                    });
                }
            });
        });
        
    };


    var dqsType = function(planInfoUrl, params, url) {
        $.ajax({
            url: planInfoUrl,
            cache: true,
            dataType: 'jsonp',
            data: params,
            success: function (res) {
                var useUrl = res.result && res.result.data && res.result.data.useUrl 
                var urlFromProductDetailPage = res.result && res.result.data && res.result.data.urlFromProductDetailPage 
                if(!useUrl)
                {   
                    $('body').dialog({
                        type: 'iframe',
                        width: 720,
                        height: 360,
                        title: '定期送',
                        autoIframe: false,
                        iframeTimestamp: false,
                        source: url
                    });
                }else{
                    location.href = urlFromProductDetailPage
                }
            }
        });   
    }

    // 【定期购】企业耗材金融化 PRD: https://joyspace.jd.com/pages/VMff58hqFbwDJtYzl59Q
    var dqgBtn = function(cfg) {
        // "activityId": 89025, "skuId": 100048670988, 命中activityType==2 有期数赠送
        //  skuId：100049272345  活动ID=88976，命中阶梯折扣|仅定期购
        // pageConfig.product.activityIdData = 88976 // 活动id
        // getdqgDialog(cfg, '', '', '', cfg.activityIdData, "1");

        var attribute = cfg.specialAttrs.join('|').match(/isTimeOrder\-([^\|]*)/);
        var isTimeOrder = attribute ? +attribute[1] : 0;
        Event.addListener('onStockReady', function(data) {
            
            var res = data && data.stock && data.stock.data
            var enTimeOrderInfo = res && res.enTimeOrderInfo // 企业购楼层数据
            var enTimeOrder = enTimeOrderInfo && enTimeOrderInfo.enTimeOrder // 是否展示企业购楼层和企业购加车按钮标识
            var periodNumMin = enTimeOrderInfo && enTimeOrderInfo.periodNumMin // 企业购期数
            var sizePeriodMin = enTimeOrderInfo && enTimeOrderInfo.sizePeriodMin // 企业购最小购买数量
            var content = enTimeOrderInfo && enTimeOrderInfo.content // 企业购期数后面折扣
            var activityId = enTimeOrderInfo && enTimeOrderInfo.activityId // 活动id
            var promotionText = enTimeOrderInfo && enTimeOrderInfo.promotionText // tab促销气泡
            var frequencyNum = enTimeOrderInfo && enTimeOrderInfo.frequency // 次数
            var isStock = res && res.stockInfo && res.stockInfo.isStock // 是否有货

            if(enTimeOrder && isTimeOrder == 5){ // 不展示tab切换
                // 企业购楼层公共数据和企业计划购按钮展示以及按钮点击
                floorAndBtn(periodNumMin, content, sizePeriodMin, frequencyNum, activityId, cfg, isStock)
                if(tools.areAllChildrenHidden("#common-plan")){
                    $(".common-plan").hide()// 隐藏tab切换
                    Event.fire({
                        type: 'tabShow',
                    })
                }
                
            }else if(enTimeOrder && isTimeOrder == 3){ // 展示tab切换
                //购买方式优先级：以旧换新>省心租赁>定期购>买一试三>批量订购>合约机>3C信用试
                var $batchPlan = $(".common-plan")
                if($batchPlan.length > 0){
                    // $batchPlan.show() // 定期购tab展示
                    // console.log("展示企业计划购按钮Tab")
                    $batchPlan.show()
                    $batchPlan.find(".gfgm").show() // 官方购买展示
                    $batchPlan.find(".qyjhg").show() // 企业计划购展示
                     // $(".batch-purchase").remove() // 删除工服定制容器，企业计划购优先级高
                    $batchPlan.find(".dzgm").hide() // 隐藏单品定制
                    $batchPlan.find(".plgm").hide() // 隐藏批量定制
                    try {// 企业计划购tab曝光
                        expLogJSON('smb_pc', 'epptab_expo', '{"skuid": ' + cfg.skuid + '}');
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.log('企业计划购tab曝光埋点错误');
                        }
                    }
                    if(promotionText && promotionText.length > 0){
                        $batchPlan.find(".jhgtip").html(promotionText).show()// 企业计划tab后折扣信息
                    }else{
                        $batchPlan.find(".jhgtip").hide()
                    }

                    // 点击切换顶部tab按钮
                    // $batchPlan.find(".plan-tab").click(function(){
                    //     var $this = $(this)
                    //     $this.addClass("curr").siblings().removeClass("curr")
                    //     if ($this.index() == 1) {// 企业计划购tab命中
                    //         location.href = G.modifyURL(location.href, {// 跳转企业计划购tab下
                    //             query: {
                    //             plantab: '2'
                    //             }
                    //         })
                            
                    //     } else {// 批量tab命中单品
                    //         // $("#choose-results").hide()
                    //         location.href = G.modifyURL(location.href, {
                    //         query: {
                    //             plantab: '1'
                    //         }
                    //         })
                    //     }
                        
                    // })

                    if(plantabidNum){// url上切换plantab=2的时候
                        $batchPlan.find(".qyjhg").addClass("curr").siblings().removeClass("curr")
                        $(".choose-amount").hide()// 命中企业按钮屏蔽数量
                        // 企业购楼层公共数据和企业计划购按钮展示以及按钮点击
                        floorAndBtn(periodNumMin, content, sizePeriodMin, frequencyNum, activityId, cfg, isStock)

                        // 删除企业计划购价格容器
                        $('#J_FinalPrice').remove() 
                    }
                    tools.TabSwitch()// tab样式切换逻辑
                }
            }else { // 接口异常或者无定期购无数下发
               if($("#InitCartUrlEnPlanBuy").length > 0) // 判断有无定期购按钮
               {
                $("body").find(".J_choose_btn").html('<a href="#none" class="btn-special1 btn-lg  btn-disable">企业计划购</a>')
               } 
            }  
        });

        // 企业购楼层公共数据和企业计划购按钮展示以及按钮点击
        function floorAndBtn(periodNumMin, content, sizePeriodMin, frequencyNum, activityId, cfg, isStock) {
            if (dqgLoaded) return
            dqgLoaded = true 

            floorList(periodNumMin, content, sizePeriodMin, frequencyNum, isStock) // 企业购楼层公共数据展示
            $(".choose-floor").show() // 展示流程楼层
            $(".process-floor").show() // 展示流程楼层(新版)
            tools.exposure({
                functionName: 'Moregoodies_Expo',
                exposureData: ['mainskuid'],
                extraData: {
                    clerk: $('.process-floor a').text()
                },
                errorTips: '更多好物去逛逛埋点报错'
            })
            $('.process-floor a').click(function() { // 点击更多好物去逛逛
                tools.landmine({
                    functionName: 'Moregoodies_Click',
                    exposureData: ['mainskuid'],
                    extraData: {
                        clerk: $('.process-floor a').text()
                    },
                    errorTips: '更多好物去逛逛埋点报错'
                })
            })
            // 点击企业计划购按钮
            pageConfig.product.activityIdData = activityId
            if (pageConfig.product.enPlanOptSwitch) { // 新版企业计划购开关
                getdqgDialog(cfg, '', '', '', activityId, "1");// 调用定期购弹层
            }
            $("body").find("#InitCartUrlEnPlanBuy").click(function(){
                getdqgDialog(cfg, '', '', '', activityId, "1");// 调用定期购弹层
                try {// 企业计划购按钮点击埋点
                    log('smb_pc', 'eppbutton', '{"skuid ":' + cfg.skuid + '}')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('企业计划购按钮点击埋点错误');
                    }
                }
            })
        }

        function floorList(periodNumMin, content, sizePeriodMin, frequencyNum, isStock){
            // 企业计划购楼层逻辑
            $(".choose-floor .p2").find("em").html(periodNumMin + "期起订") // 企业购期数
            if(content && content.length > 0){ // 企业购期数后面折扣没有值删除容器
                $(".choose-floor .p2").find("i").html(content) // 企业购期数后面折扣
            }else{
                $(".choose-floor .p2").find("i").remove()
            }
            $(".choose-floor .p3").find("em").html(frequencyNum) // 频次代产品确认
            $(".choose-floor .p4").find("em").html(sizePeriodMin + "件起订") // 企业购最小购买数量
            try {// 企业计划购楼层曝光
                expLogJSON('smb_pc', 'eppfloor_expo', '{"skuid": ' + cfg.skuid + '}');
            } catch (e) {
                if (typeof console !== 'undefined') {
                    console.log('企业计划购楼层曝光埋点错误');
                }
            }

            // 底部企业计划购按钮展示逻辑
            var $chooseBtns = $("body").find(".J_choose_btn") // 通用按钮容器
            var $jMobileOnly = $(".J-mobile-only") //二维码容器
            var $mItemover = $(".m-itemover") // 不可购买楼层提示
            var $btnGoapp = $("#btn-goapp") //二维码按钮
            if($chooseBtns.length == 0 && $jMobileOnly.length > 0){
                if(isStock && $mItemover.length == 0){ // 有库存并且没有不可购买楼层
                    $jMobileOnly.after('<div id="choose-btns" class="choose-btns clearfix"><a href="#none" id="InitCartUrlEnPlanBuy" class="btn-special1 btn-lg">企业计划购</a></div>').hide()
                }else{
                    $jMobileOnly.after('<div id="choose-btns" class="choose-btns clearfix"><a href="#none" class="btn-special1 btn-lg btn-disable">企业计划购</a></div>').hide()
                }
            }else if($chooseBtns.length > 0){
                $btnGoapp.hide()
                if(isStock && $mItemover.length == 0){ // 有库存并且没有不可购买楼层
                    $chooseBtns.html('<a href="#none" id="InitCartUrlEnPlanBuy" class="btn-special1 btn-lg">企业计划购</a>').show()
                }else{
                    $chooseBtns.html('<a href="#none" class="btn-special1 btn-lg  btn-disable">企业计划购</a>').show()
                }
            }
            $("#InitCartUrl-mini").hide() // 迷你购物车按钮隐藏
            try {// 企业计划购底部按钮曝光
                expLogJSON('smb_pc', 'eppbutton_expo', '{"skuid": ' + cfg.skuid + '}');
            } catch (e) {
                if (typeof console !== 'undefined') {
                    console.log('企业计划购底部按钮曝光埋点错误');
                }
            }
        }
        
    }

    var getdqgDialog = function(cfg, frequency, periodNum, sizePeriod, activityId, type) { // cfg、配送频率、期数、件数、活动id、初始化类型
        var url = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            url = pageConfig.product && pageConfig.product.colorApiDomain
        }
        var areaInfo = tools.getAreaId().areaIds // 四级地址id数组，类似： [1, 2810, 51081, 0]
        // 入参 接口文档：https://joyspace.jd.com/pages/uPkLUZrzDuLWip5f8cya 
        // PRD：https://joyspace.jd.com/pages/uOFKMZ9yIvaM10YwBqbG
        var paramJson = {
            "activityId": activityId,// 87992、88139、78708、89025 活动id
            "areaInfo": { // 四级地址id
                "cityCode": areaInfo[1],
                "countyCode": areaInfo[2],
                "provinceCode": areaInfo[0],
                "townCode": areaInfo[3]
            },
            "frequency": frequency, // 配送频率
            "periodNum": periodNum, // 期数
            "serverIP": "127.0.0.1",
            "sizePeriod": sizePeriod, // 件数
            "skuId": cfg.skuid,// 当前skuid,cfg.skuId
            "storeId": 0,
            "systemName": "item-v3",
            "token": "2BCE15D5C1A7E2BDFDCD16E2A4352BDD",
            "uniformBizInfo": {
                "buId": 301,
                "data": {
                    "buId": 301,
                    "tenantId": 1024
                },
                "tenantId": 1024
            },
            "userIP": "127.0.0.1"
        }


        var body = JSON.stringify(paramJson);
        var time = new Date().getTime()
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'ding_calculateTotalPrice',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = tools.getCookieNew("__jda") || '';
                        getDataList(colorParm);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getDataList(colorParm);
                    //烛龙上报
                    tools.getJmfe(colorParm, e, "定期购填写配送计划接口设备指纹异常",752)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getDataList(colorParm);
            //烛龙上报
            tools.getJmfe(colorParm, e, "定期购填写配送计划接口加固异常",752)
        } 
        // 加固end
       
        function getDataList(colorParm){
            $.ajax({
                url: url,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function(r) {
                    if(r && r.data){
                        setdqgData(r, type, frequency, periodNum, sizePeriod)
                    }else{
                        //烛龙上报
                        tools.getJmfe(colorParm, r, "定期购填写配送计划接口返回空异常",753)
                    }
                },
                error: function (e) {
                    //烛龙上报
                    tools.getJmfe(colorParm, e, "定期购填写配送计划接口错误异常",752)
                }
            })
        }

        
    }
    
    var setdqgData = function(r, type, frequency, periodNum, sizePeriod){
        var data = r.data
        // 标题下折扣信息
        var contentData= ""
        if(data.content && data.content != "undefined"){
            contentData= ' <span>'+ data.content +'</span>'
        }
        var enPlanSwitch = pageConfig.product && pageConfig.product.enPlanOptSwitch // 企业计划购开关
        if (enPlanSwitch) {
            $('.J-firmbuy-price').remove() // 删除企业阶梯价（与定期购优惠冲突）
        }

        // 到手价
        var purchase= ""
        if (data.purchasePrice && data.purchasePrice != "undefined") {
            if (enPlanSwitch) {
                purchase = '<span class="finalPrice finalPriceIcon" ><span class="symbol">￥</span><span class="price">' + data.purchasePrice + '</span><span class="priceContent" style="background: #FF0F23; padding: 0 4px; height: 22px; line-height: 22px; color: #fff; border-radius: 2px; margin-left: 8px;">单件到手价</span></span>'
            } else {
                purchase = '<span class="jd-price">￥<i>' + data.purchasePrice + '</i><em></em></span>'
            }
        }

        // 优惠阶梯楼层
        var discount = ""
        if (data.activityType == "6") {
            var num = ""
            var disc = ""
            if (enPlanSwitch) {
                if(data.tieredDiscountDetails && data.tieredDiscountDetails.length > 0){
                    for (var i = 0; i < data.tieredDiscountDetails.length; i++) {
                        num += '<span>满' + data.tieredDiscountDetails[i].item +'件打' + (data.tieredDiscountDetails[i].discount * 10).toFixed(1) + '折</span>'
                    }
                }
                discount += '<div class="dt">优惠</div>\
                    <div class="dd">\
                        ' + num + '\
                    </div>'
                // 优惠阶梯
                $('.discount-floor').html(discount) // 添加优惠阶梯
                $('.discount-floor').show()
            } else {
                if(data.tieredDiscountDetails && data.tieredDiscountDetails.length > 0){
                    if(data.tieredDiscountDetails.length > 2){ // 折扣数量只有3和2条数据 样式不一样
                        for (var i = 0; i < data.tieredDiscountDetails.length; i++) {
                            num += '<span>≥'+ data.tieredDiscountDetails[i].item+'件</span>'
                            disc += '<span><i>'+ (data.tieredDiscountDetails[i].discount * 10).toFixed(1) +'</i>折</span>'
                        }
                    }else{
                        for (var i = 0; i < data.tieredDiscountDetails.length; i++) {
                            num += '<span class="discountDetails">≥'+ data.tieredDiscountDetails[i].item+'件</span>'
                            disc += '<span class="discountDetails"><i>'+ (data.tieredDiscountDetails[i].discount * 10).toFixed(1) +'</i>折</span>'
                        }
                    }
                    
                }
                discount += '<div class="goods-discount-title">\
                    <span class="goods-main-title">企业套餐折扣</span>\
                    <span class="goods-sub-title">多买多优惠</span>\
                </div>\
                <div class="goods-discount">\
                    <div class="buy-multiple">\
                        <div class="top">\
                            <div class="num">商品数量</div>\
                            <div class="discount">\
                                '+num+'\
                            </div>\
                        </div>\
                        <div class="line"></div>\
                        <div class="bottom">\
                            <div class="num">折扣</div>\
                            <div class="discount">\
                                '+disc+'\
                            </div>\
                        </div>\
                    </div>\
                </div>'
            }
        }

        // 定期计划楼层
        var frequencyListBtn = ""
        if(data.frequencyList && data.frequencyList.length > 0){
            var frequencyBtn = ""
            if(type == "1") {// 初始化渲染定期计划
                if (data.frequencyList.includes(1)) {
                    frequencyBtn += data.frequencyList[0] == 1 
                    ? '<span class="check" data-type="1">每天送1次</span>' 
                    : '<span data-type="1">每天送1次</span>'
                }
                if (data.frequencyList.includes(2)) {
                    frequencyBtn += data.frequencyList[0] == 2 
                    ? '<span class="check" data-type="2" data-interval="'+ data.frequencyWeek +'">每'+ data.frequencyWeek +'周送1次</span>' 
                    : '<span data-type="2" data-interval="'+ data.frequencyWeek +'">每'+ data.frequencyWeek +'周送1次</span>'
                }
                if (data.frequencyList.includes(3)) {
                    frequencyBtn += data.frequencyList[0] == 3 
                    ? '<span class="check" data-type="3" data-interval="'+ data.frequencyMonth +'">每'+ data.frequencyMonth +'月送1次</span>' 
                    : '<span data-type="3" data-interval="'+ data.frequencyMonth +'">每'+ data.frequencyMonth +'月送1次</span>'
                }
                if (data.frequencyList.includes(5)) {
                    frequencyBtn += data.frequencyList[0] == 5 
                    ? '<span class="check" data-type="5">每天送1次(非周末)</span>' 
                    : '<span data-type="5">每天送1次(非周末)</span>'
                }
                pageConfig.product.frequencyListData = data.frequencyList[0]
                if(data.frequencyList[0] == 2)// 根据频率判断间隔
                {
                    pageConfig.product.interval = data.frequencyWeek
                }else if(data.frequencyList[0] == 3){
                    pageConfig.product.interval = data.frequencyMonth
                }
            } else {// 切换数量渲染定期计划
                if (data.frequencyList.includes(1)) {
                    frequencyBtn += frequency == 1 
                    ? '<span class="check" data-type="1">每天送1次</span>' 
                    : '<span data-type="1">每天送1次</span>'
                }
                if (data.frequencyList.includes(2)) {
                    frequencyBtn += frequency == 2 
                    ? '<span class="check" data-type="2"  data-interval="'+ data.frequencyWeek +'">每'+ data.frequencyWeek +'周送1次</span>' 
                    : '<span data-type="2"  data-interval="'+ data.frequencyWeek +'">每'+ data.frequencyWeek +'周送1次</span>'
                }
                if (data.frequencyList.includes(3)) {
                    frequencyBtn += frequency == 3 
                    ? '<span class="check" data-type="3"  data-interval="'+ data.frequencyMonth +'">每'+ data.frequencyMonth +'月送1次</span>' 
                    : '<span data-type="3"  data-interval="'+ data.frequencyMonth +'">每'+ data.frequencyMonth +'月送1次</span>'
                }
                if (data.frequencyList.includes(5)) {
                    frequencyBtn += frequency == 5 
                    ? '<span class="check" data-type="5">每天送1次(非周末)</span>' 
                    : '<span data-type="5">每天送1次(非周末)</span>'
                }
            }
            if (enPlanSwitch) {
                frequencyListBtn = '<div class="dt">定期计划</div>\
                    <div class="dd">\
                        '+ frequencyBtn +'\
                    </div>'
            } else {
                frequencyListBtn = '<div class="goods-rules-plan">\
                    <span class="goods-rules-topic">定期计划</span>\
                    <div class="goods-rules-btn">\
                        '+ frequencyBtn +'\
                    </div>\
                </div>'
            }
        }

        // 最多件数提示
        var sizePeriodMaxDom = ""
        if (data.sizePeriodMax) {
            sizePeriodMaxDom = '<div class="center-column tip-color">最多'+ data.sizePeriodMax +'件</div>'
        }else{
            sizePeriodMaxDom = '<div class="center-column tip-color">最多99件</div>' // 默认最多99件
        }

        // 最多期数提示
        var periodNumMinDom = ""
        if(data.periodNumMax){
            periodNumMinDom = '<div class="center-column tip-color">最多'+ data.periodNumMax +'期</div>'
        }else{
            periodNumMinDom = '<div class="center-column tip-color">最多200期</div>'// 默认最多200期
        }

        if(type == "1"){// 初始化拿
            pageConfig.product.sizePeriodData = data.sizePeriodMin || 1 // 默认件数，没下发默认1
            pageConfig.product.periodNumData = data.periodNumMin || 2 // 默认期数，没下发默认2
        }else{
            pageConfig.product.sizePeriodData = sizePeriod// 默认件数，没下发默认1
            pageConfig.product.periodNumData = periodNum // 默认期数，没下发默认2
        }

        // 件数加减
        var reduceDown1 = pageConfig.product.sizePeriodData == data.sizePeriodMin ? '<i class="i-reduce-down1 disabled" data-type="1">-</i>' : '<i class="i-reduce-down1" data-type="1">-</i>'
        var addUp1 = pageConfig.product.sizePeriodData == data.sizePeriodMax ? '<i class="i-add-up1 disabled" data-type="1">+</i>' : '<i class="i-add-up1" data-type="1">+</i>'

        // 期数加减
        var reduceDown2 = pageConfig.product.periodNumData == data.periodNumMin ? '<i class="i-reduce-down2 disabled" data-type="2">-</i>' : '<i class="i-reduce-down2" data-type="2">-</i>'
        var addUp2 = pageConfig.product.periodNumData == data.periodNumMax ? '<i class="i-add-up2 disabled" data-type="2">+</i>' : '<i class="i-add-up2" data-type="2">+</i>'

        // 配送文案
        var  goodsPriceTop  = ''
        var  totalGift  = ''
        if(data.activityType == "1"){ // 定期购折扣
            goodsPriceTop = '<div class="goods-price-top">共配送<span>'+ data.totalPlanNum +'</span>期，共<span>'+ data.skuNum +'</span>件商品</div>'
        }else if(data.activityType == "2" || data.activityType == "3"){ // 2（满M期赠N期）或者3（每满M期赠N期）时
            totalGift = data.totalGift ? '(其中'+ data.totalGift +'期赠送)' : ''
            goodsPriceTop  = '<div class="goods-price-top">共配送<span>'+ data.totalPlanNum +'</span>期'+totalGift+'，共<span>'+ data.skuNum +'</span>件商品</div>'
        }else if(data.activityType == "6"){ // 满M件N折
            goodsPriceTop  = '<div class="goods-price-top">共配送<span>'+ data.totalPlanNum +'</span>期，共<span>'+ data.skuNum +'</span>件商品，可享<span class="discount-num">'+ (data.regularDiscount * 10).toFixed(1) +'</span>折</div>'
        }

        // 不同内容高度样式处理
        var planGood = data.activityType != "6" ? "plan-goods plan-goods-short": "plan-goods" 

        // 预计节省价格判断
        var saveText = enPlanSwitch ? '省'+ data.discountPrice +'元' : '预计节省￥'+ data.discountPrice
        var discountPrice = data.discountPrice && data.discountPrice > 0 ? '<span class="price-save">'+ saveText +'</span>' : ''
        // 显示企业计划购各楼层
        if (enPlanSwitch) {
            $('.finalPrice').remove() // 删除原有的企业到手价
            $('#buynow-container').remove() // 删除按钮区域
            // 价格楼层
            var $priceArea = $('.summary-price .dd')
            $priceArea.prepend(purchase)
            $priceArea.find('.p-price').addClass('jdPrice') // 添加jd-price样式
            $priceArea.find('.p-price .price').css('text-decoration', 'line-through') // 删除原价线
            $priceArea.find('.p-price').removeClass('p-price') // 删除p-price样式，确保融合接口不会更改此价格
            // 定期计划
            $('.choose-plan').html(frequencyListBtn) // 添加定期计划
            $('.choose-plan').show()
            tools.exposure({
                functionName: 'DQJH_Expo',
                exposureData: ['mainskuid'],
                extraData: {
                    dqg: $(".choose-plan span").map(function() { return $(this).text()}).get() || []
                },
                errorTips: '定期计划埋点报错'
            })
            // 每次件数
            var dom = '<span class="dt">每次件数</span>\
                    <div class="dd">\
                        <div class="last">\
                            <div class="count-set" fbybt="true">\
                                '+ reduceDown1 +'\
                                <input type="text" class="ipt-count-n1" value="'+ pageConfig.product.sizePeriodData +'" data-type="1" data-min-num="'+data.sizePeriodMin+'" data-minmr-num="1" data-max-num="'+data.sizePeriodMax+'" data-maxmr-num="99">\
                                '+ addUp1 +'\
                            </div>\
                        </div>\
                        '+ sizePeriodMaxDom +'\
                    </div>'
            $('.choose-num').html(dom)
            $('.choose-num').show() // 显示每次件数
            tools.exposure({
                functionName: 'MCJS_Expo',
                exposureData: ['mainskuid'],
                extraData: {
                    num: String(pageConfig.product.sizePeriodData), // 件数
                    clerk: $(sizePeriodMaxDom).text()
                },
                errorTips: '每次件数埋点报错'
            })
            // 配送期数
            dom = '<span class="dt">配送期数</span>\
                <div class="dd">\
                    <div class="last">\
                        <div class="count-set" fbybt="true">\
                            '+ reduceDown2 +'\
                            <input type="text" class="ipt-count-n2" value="'+ pageConfig.product.periodNumData +'" data-type="2" data-min-num="'+data.periodNumMin+'" data-minmr-num="2" data-max-num="'+data.periodNumMax+'" data-maxmr-num="200">\
                            '+ addUp2 +'\
                        </div>\
                    </div>\
                    '+ periodNumMinDom +'\
                </div>'
            $('.choose-term').html(dom)
            $('.choose-term').show() // 显示配送期数
            tools.exposure({
                functionName: 'PSZQ_Expo',
                exposureData: ['mainskuid'],
                extraData: {
                    // num: String(pageConfig.product.periodNumData), // 期数
                    clerk: $(periodNumMinDom).text()
                },
                errorTips: '配送期数埋点报错'
            })
            // 按钮区域
            dom = '<div id="buynow-container" class="J_choose_btn">\
                <div class="price-and-num">\
                    '+ goodsPriceTop +'\
                    <div class="goods-price-bottom">\
                        <span class="price-total">总价预计<span class="total">￥<i>'+ data.needPayPrice +'</i></span></span>\
                        '+discountPrice+'\
                    </div>\
                </div>\
                <a href="#none" id="buynow" class="btn-special1 btn-lg" style="font-size: 20px;">立即购买</a>\
            </div>'
            $('.J_choose_btn').hide() // 隐藏原有的按钮
            $('.J_choose_btn').after(dom)
            // 定期计划切换
            $("body").find(".choose-plan span").click(function(){
                var planId = $(this).data("type")
                var interval = $(this).data("interval")
                $(this).addClass("check").siblings().removeClass()
                pageConfig.product.frequencyListData = planId // 定制计划类型id
                pageConfig.product.interval = interval // 定制计划间隔 只有type=2、3的时候
                tools.landmine({
                    functionName: 'DQJH_Click',
                    exposureData: ['mainskuid'],
                    extraData: {
                        dqg: $(this).text()
                    },
                    errorTips: '定期计划埋点报错'
                })
            })
            // 去结算
            $("#buynow").unbind('click').click(function(){
                var extFlag = {
                    "timeOrderId" : data.planUuid,// 计划id
                    "to_activityId" : data.activityId,// 活动id
                    "timeOrderNum" : pageConfig.product.periodNumData,// 期数
                    "perTimeNum" : pageConfig.product.sizePeriodData, // 件数
                    "to_frequency" :  pageConfig.product.frequencyListData, // 定期频率
                    "frequencyCount" : pageConfig.product.interval ? pageConfig.product.interval : "1",// 定期间隔
                }
                getCarInfo(extFlag)
            })
            bindEventChange();// 绑定件数和期数数量切换
        } else {
            var template = '\
            <div class="'+ planGood +'">\
                <div class="goods-info">\
                    <div class="goods-info-left">\
                        <img src="//img10.360buyimg.com/N2/'+ data.imageUrl +'" width="72">\
                    </div>\
                    <div class="goods-info-right">\
                        <div class="goods-info-title">'+ data.skuName +'</div>\
                        <div class="goods-info-tip">\
                            '+ contentData +'\
                        </div>\
                        <div class="goods-info-price">\
                            '+ purchase +'\
                            <span class="ds-price">￥'+ data.skuPrice +'</span>\
                        </div>\
                    </div>\
                </div>\
                '+ discount +'\
                <div class="goods-rules">\
                '+ frequencyListBtn +'\
                    <div class="goods-rules-num">\
                        <span class="goods-rules-topic">每次件数</span>\
                        <div class="goods-rules-choose">\
                            <div class="last">\
                                <div class="count-set" fbybt="true">\
                                    '+ reduceDown1 +'\
                                    <input type="text" class="ipt-count-n1" value="'+ pageConfig.product.sizePeriodData +'" data-type="1" data-min-num="'+data.sizePeriodMin+'" data-minmr-num="1" data-max-num="'+data.sizePeriodMax+'" data-maxmr-num="99">\
                                    '+ addUp1 +'\
                                </div>\
                            </div>\
                            '+ sizePeriodMaxDom +'\
                        </div>\
                    </div>\
                    <div class="goods-rules-stage">\
                        <span class="goods-rules-topic">配送期数</span>\
                        <div class="goods-rules-choose">\
                            <div class="last">\
                                <div class="count-set" fbybt="true">\
                                    '+ reduceDown2 +'\
                                    <input type="text" class="ipt-count-n2" value="'+ pageConfig.product.periodNumData +'" data-type="2" data-min-num="'+data.periodNumMin+'" data-minmr-num="2" data-max-num="'+data.periodNumMax+'" data-maxmr-num="200">\
                                    '+ addUp2 +'\
                                </div>\
                            </div>\
                            '+ periodNumMinDom +'\
                        </div>\
                    </div>\
                </div>\
                <div class="goods-price-btn">\
                    <div class="goods-price-num">\
                        '+ goodsPriceTop +'\
                        <div class="goods-price-bottom">\
                            <span class="price-total">总价预计 <span class="total">￥<i>'+ data.needPayPrice +'</i></span></span>\
                            '+discountPrice+'\
                        </div>\
                    </div>\
                    <div class="goods-btn-trade">\
                        <a href="#none">去结算</a>\
                    </div>\
                </div>\
            </div>';


            if(type=="1"){// 初始化
                $("body").dialog({
                    title: "填写配送计划", 
                    width: 480, 
                    height: data.activityType != "6" ? 350: 480,
                    source: "<div class='dialogDom'><div class='dialogTip hide'></div>"+template+"</div>",
                    onReady: function () {
                        // 定期计划切换
                        $("body").find(".goods-rules-btn span").click(function(){                            
                            var planId = $(this).data("type")
                            var interval = $(this).data("interval")
                            $(this).addClass("check").siblings().removeClass()
                            pageConfig.product.frequencyListData = planId // 定制计划类型id
                            pageConfig.product.interval = interval // 定制计划间隔 只有type=2、3的时候
                        })
                        // 去结算
                        $("body").find(".goods-btn-trade").click(function(){
                            var extFlag = {
                                "timeOrderId" : data.planUuid,// 计划id
                                "to_activityId" : data.activityId,// 活动id
                                "timeOrderNum" : pageConfig.product.periodNumData,// 期数
                                "perTimeNum" : pageConfig.product.sizePeriodData, // 件数
                                "to_frequency" :  pageConfig.product.frequencyListData, // 定期频率
                                "frequencyCount" : pageConfig.product.interval ? pageConfig.product.interval : "1",// 定期间隔
                            }
                            getCarInfo(extFlag)
                        })
                        bindEventChange();// 绑定件数和期数数量切换
                    }
                });
            }else{
                // 切换数量渲染弹框全部信息
                $("body").find(".dialogDom").html("<div class='dialogTip hide'></div>"+template)
                // 定期计划切换
                $("body").find(".goods-rules-btn span").click(function(){
                    var planId = $(this).data("type")
                    var interval = $(this).data("interval")
                    $(this).addClass("check").siblings().removeClass()
                    pageConfig.product.frequencyListData = planId // 定制计划类型id
                    pageConfig.product.interval = interval // 定制计划间隔 只有type=2、3的时候
                })
                // 去结算
                $("body").find(".goods-btn-trade").click(function(){
                    var extFlag = {
                        "timeOrderId" : data.planUuid,// 计划id
                        "to_activityId" : data.activityId,// 活动id
                        "timeOrderNum" : pageConfig.product.periodNumData,// 期数
                        "perTimeNum" : pageConfig.product.sizePeriodData, // 件数
                        "to_frequency" :  pageConfig.product.frequencyListData, // 定期频率
                        "frequencyCount" : pageConfig.product.interval ? pageConfig.product.interval : "1",// 定期间隔
                    }
                    getCarInfo(extFlag)
                })
                bindEventChange();// 绑定件数和期数数量切换
            }
        }
    } 

    var getCarInfo = function (extFlag) { // 去结算
        var url = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            url = pageConfig.product && pageConfig.product.colorApiDomain
        }
        // 入参 文档：https://joyspace.jd.com/pages/mMbpeCcSM3PwimL2gknv
        var paramJson = {
            "serInfo": {
                "area": tools.getAreaId().areaIds.join('_'),
                "user-key": tools.getCookieNew("user-key") || '',
            },
            "directOperation": {
                "source": "dqg",
                "theSkus": [{
                    "skuId": pageConfig.product.skuid,
                    "num":  parseInt(pageConfig.product.periodNumData * pageConfig.product.sizePeriodData), // 商品数量 虚拟组套传1，其他传期数*件数，本期pc不支持虚拟组套类型
                    "itemType": 1,// 商品类型， 等待确认
                    "extFlag": extFlag
                    }
                ]
            }
            
        }
        var time = new Date().getTime()
        var body = JSON.stringify(paramJson);
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pcCart_jc_buyNow',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = tools.getCookieNew("__jda") || '';
                        getCarDataList(colorParm);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getCarDataList(colorParm);
                    //烛龙上报
                    tools.getJmfe(colorParm, e, "定期购填写配送计划接口设备指纹异常",752)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getCarDataList(colorParm);
            //烛龙上报
            tools.getJmfe(colorParm, e, "定期购填写配送计划接口加固异常",752)
        } 
        // 加固end
       
        function getCarDataList(colorParm){
            $.ajax({
                url: url,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function(r) {
                    var msg = "操作失败，请重试" // 如网关接口返回加车失败，则在弹窗上toast提示“操作失败，请重试”，弹窗不关
                    var url = r && r.url
                    if(r && r.success){
                        window.location.href = url;
                    }else{
                        $("body").find(".dialogTip").html(msg).show()
                        setTimeout(function(){
                            $("body").find(".dialogTip").hide() 
                        },1000)
                    }
                },
                error: function (e) {
                    //烛龙上报
                    tools.getJmfe(colorParm, e, "去结算加入购物车接口错误异常",752)
                }
            })
        }
    }
    
    var bindEventChange = function () { // 绑定加减和输入事件
        var isJClick = true
        // 件数加减事件
        $('.i-add-up1').unbind('click').bind('click', function (e) {
            tools.landmine({
                functionName: 'MCJS_Click',
                exposureData: ['mainskuid'],
                extraData: {
                    num: $('.ipt-count-n1').val(), // 件数
                },
                errorTips: '每次件数埋点报错'
            })
            if(isJClick){
                isJClick = false
                var type = $(this).data("type")
                setTimeout(function(){
                    isJClick = true
                    addCount(e, type)
                },300)
            }
        })

        $('.i-reduce-down1').unbind('click').bind('click', function (e) { // 每次件数点击减少数量
            tools.landmine({
                functionName: 'MCJS_Click',
                exposureData: ['mainskuid'],
                extraData: {
                    num: $('.ipt-count-n1').val(), // 件数
                },
                errorTips: '每次件数埋点报错'
            })
            if(isJClick){
                isJClick = false
                var type = $(this).data("type")
                setTimeout(function(){
                    isJClick = true
                    reduceCount(e, type)
                },300)
            }
        })
  
        $('.ipt-count-n1').unbind('keyup').bind('keyup', tools.debounce(inputKeyupHandler, 500));

        var isQClick = true
        // 期数加减事件
        $('.i-add-up2').unbind('click').bind('click', function (e) { // 期数点击增加数量、
            tools.landmine({
                functionName: 'PSZQ_Click',
                exposureData: ['mainskuid'],
                extraData: {
                    num: $('.ipt-count-n2').val(), // 件数
                },
                errorTips: '配送期数埋点报错'
            })
            if(isQClick){
                isQClick = false
                var type = $(this).data("type")
                setTimeout(function(){
                    isQClick = true
                    addCount(e, type)
                },300)
            }
        })
        
        $('.i-reduce-down2').unbind('click').bind('click', function (e) { // 期数点击减少数量
            tools.landmine({
                functionName: 'PSZQ_Click',
                exposureData: ['mainskuid'],
                extraData: {
                    num: $('.ipt-count-n2').val(), // 件数
                },
                errorTips: '配送期数埋点报错'
            })
            if(isQClick){
                isQClick = false
                var type = $(this).data("type")
                setTimeout(function(){
                    isQClick = true
                    reduceCount(e, type)
                },300)
            }
            
        }) 
    
        $('.ipt-count-n2').unbind('keyup').bind('keyup', tools.debounce(inputKeyupHandler, 500)); // 期数输入数量
    }
    var addCount = function (e, type) { // 加+数量
        var _this = $(e.target)
        var $this = _this.parent().find(".ipt-count-n"+type)
        var minBuyNum = $this.data("min-num") || $this.data("minmr-num") // 最小限购数量,期数最小默认2期,件数最小默认1件
        var maxBuyNum = $this.data("max-num") || $this.data("maxmr-num") // 最大限购数量,期数最大默认200期,件数最大默认99件 
        var reduceDisabled =  _this.parent().find(".i-reduce-down"+type) // 减按钮
        var inputVal = parseInt($this.val());// 具体值
       
        if (inputVal == maxBuyNum) {// 增加按钮禁用状态
            var content = type == "1" ? "最多" + maxBuyNum + "件" : "最多" + maxBuyNum + "期"
            $("body").find(".dialogTip").html(content).show()
            setTimeout(function(){
                $("body").find(".dialogTip").hide() 
            },1000)
            $this.val(maxBuyNum);// 输入框置最小数量
            _this.addClass("disabled"); // 加号禁止灰色
            reduceDisabled.removeClass("disabled"); // 减号恢复点击
        }else if (inputVal < maxBuyNum) {
            inputVal++;
            // 改变数量通用逻辑判断
            changeNum($this, inputVal, minBuyNum, maxBuyNum, reduceDisabled, _this, type)
        }
    }
    var reduceCount = function (e, type) {// 减-数量
        var _this = $(e.target)
        var $this = _this.parent().find(".ipt-count-n"+type) // 输入框
        var minBuyNum = $this.data("min-num") || $this.data("minmr-num") // 最小限购数量,期数最小默认2期,件数最小默认1件
        var maxBuyNum = $this.data("max-num") || $this.data("maxmr-num") // 最大限购数量,期数最大默认200期,件数最大默认99件
        var inputVal = parseInt($this.val());// 输入框值
        var addDisabled = _this.parent().find(".i-add-up"+type) // 加按钮
        if (inputVal == minBuyNum) {
            var content = type == "1" ? "最少" + minBuyNum + "件" : "最少" + minBuyNum + "期"
            // alert(content)
            $("body").find(".dialogTip").html(content).show()
            setTimeout(function(){
                $("body").find(".dialogTip").hide() 
            },1000)
            $this.val(minBuyNum);// 输入框置最小数量
            _this.addClass("disabled");// 减号禁止灰色
            addDisabled.removeClass("disabled");// 加号恢复点击
            return false
        }else if (inputVal > minBuyNum){
            inputVal--;
            // 改变数量通用逻辑判断
            changeNum($this, inputVal, minBuyNum, maxBuyNum, _this ,addDisabled, type)
        }
        
    }
    var inputKeyupHandler = function (e) {// 输入数量
        var $this =  $(e.target)
        var type = $this.data("type") // 1件数，2期数
        var minBuyNum = $this.data("min-num") || $this.data("minmr-num") // 最小限购数量,期数最小默认2期,件数最小默认1件
        var maxBuyNum = $this.data("max-num") || $this.data("maxmr-num") // 最大限购数量,期数最大默认200期,件数最大默认99件
        var reduceDisabled =  $this.parents(".count-set").find(".i-reduce-down"+type) // 减按钮
        var addDisabled =  $this.parents(".count-set").find(".i-add-up"+type) // 加按钮
        var val = parseInt($this.val()) // 输入的值
        var content1 = type == "1" ? "最少" + minBuyNum + "件" : "最少" + minBuyNum + "期"
        var content2 = type == "1" ? "最多" + maxBuyNum + "件" : "最多" + maxBuyNum + "期"
        if(minBuyNum > val){ // 输入数量小于最小限购数量或者输入空格
            $this.val(minBuyNum);// 输入框置最小数量
            $("body").find(".dialogTip").html(content1).show()
            setTimeout(function(){
                $("body").find(".dialogTip").hide() 
                changeNum($this, val, minBuyNum, maxBuyNum, reduceDisabled, addDisabled, type)
            },1000)
            addDisabled.removeClass("disabled");
            reduceDisabled.addClass("disabled");
            
            
        }else if(val >= maxBuyNum){// 输入的值大于等于最大限购数量
            $("body").find(".dialogTip").html(content2).show()
            setTimeout(function(){
                $("body").find(".dialogTip").hide()
                changeNum($this, val, minBuyNum, maxBuyNum, reduceDisabled, addDisabled, type)
            },1000)
            reduceDisabled.removeClass("disabled");
            addDisabled.addClass("disabled");
           
        }else{
            changeNum($this, val, minBuyNum, maxBuyNum, reduceDisabled, addDisabled, type)
        }
       
    }
    var changeNum = function($this, val, minBuyNum, maxBuyNum, reduceDisabled, addDisabled, type){// 数量通用方法
        if (!$this.hasClass("disabled")) {
            $this.val(
            $this
                .val()
                .replace(/[^0-9]/g, "") &&
            parseInt(
                $this
                .val()
                .replace(/[^0-9]/g, "")
            ) &&  $this
                    .val()
                        .replace(/^0+/gi, "")
            );

            if (val > 0 && val < minBuyNum) { // 输入的值大于0并且小于最小限购数量
                $this.val(minBuyNum);// 展示最小数量
                reduceDisabled.removeClass("disabled");
                addDisabled.removeClass("disabled");
            } else if (val > minBuyNum && val < maxBuyNum) { // 区间内展示用户输入数量
                $this.val(val);
                reduceDisabled.removeClass("disabled");
                addDisabled.removeClass("disabled");
            } else if (val >= maxBuyNum) { // 展示最大数量
                $this.val(maxBuyNum);
                reduceDisabled.removeClass("disabled");
                addDisabled.addClass("disabled");
            }else if(isNaN($.trim(val)) || val == "0") {
                $this.val(minBuyNum);// 输入框置最小数量
                reduceDisabled.addClass("disabled");
                addDisabled.removeClass("disabled");
            }else{
                $this.val(val);
                reduceDisabled.removeClass("disabled");
                addDisabled.removeClass("disabled");
            }

            if(type == "1"){
                pageConfig.product.sizePeriodData =  $this.val() // 件数
            }else{
                pageConfig.product.periodNumData =  $this.val() // 期数
            }
            // 调用中台定期购接口
            var cfg = pageConfig.product
            // cfg、配送频率、期数、件数、活动id、非初始化类型
            getdqgDialog(cfg, cfg.frequencyListData, cfg.periodNumData, cfg.sizePeriodData, cfg.activityIdData, "2")
        }
    }
    // 定期购end


    // 租赁服务
    var leaseBtn = function (cfg) {
        var $leaseBtn = $('.choose-btn-fqy')
        var href = $leaseBtn.attr('href')
        if (!$leaseBtn.length) return;

        var $btns = $leaseBtn.add('#InitCartUrl-mini')

        function getLease() {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            $.ajax({
                url: host + '/credit/verify?',
                data: { 
                    pin: readCookie('pin'),
                    appid: 'item-v3',
                    functionId: "pc_credit_verify" 
                 },
                dataType: 'jsonp',
                success: function (r) {
                    if (r && r.accountStatus === '1') {
                        location.href = href
                    } else {
                        $('body').dialog({
                            width: 360,
                            title: '分期用服务提示',
                            type: 'text',
                            maskClose: true,
                            source: '<div class="ac"><h2>抱歉，您无法使用分期用服务</h2></div><br>\
                            <p style="padding:10px 20px;">分期用服务目前仅对开通京东白条的用户提供，请前往 <a target="_blank" class="hl_blue" href="https://bt.jd.com/v3/activity/open">https://bt.jd.com/v3/activity/open</a> 开通京东白条服务</p>\
                            <br>\
                            <div class="ac"><a href="#none" onclick="$.closeDialog()" class="btn-def">好的</a></div>\
                            <br>'
                        })
                    }
                }
            })
        }

        $btns.click(function () {
            if (!$(this).hasClass('btn-disable')) {
                login({
                    modal: true,
                    complete: function() {
                        getLease()
                    }
                });
            }
            return false;
        })
    }

    // 山姆会员专享购
    // ref: http://cf.jd.com/pages/viewpage.action?pageId=88143870
    function samText(cfg) {
        var $text = $('.J-sam-login-text')
        var loginText = '山姆会员专享商品，请<a target="_blank" href="//passport.jd.com/new/login.aspx?ReturnUrl='+ location.href +'">登录</a>确认购买资格'
        var notSamText = '山姆会员专享商品，请<a target="_blank" href="//cartv.jd.com/item/200100420635.html">成为山姆会员</a>后购买'

        if (!$text.length) return

        var isLogin = null
        var isSAM = null

        function isSam(cb) {
            $.ajax({
                url: '//cd.jd.com/sam/info?',
                dataType: 'jsonp',
                success: function(r) {
                    cb(r && r.issam && r.issam === '1')
                }
            })
        }
        function handleLoginData(d) {
            isLogin = d.login
            if (!d.login) {
                $text.html(loginText)
            } else {
                isSam(function(is) {
                    isSAM = is
                    if (!is) {
                        cfg.addToCartBtn.disabled()
                        $text.html(notSamText)
                    }
                })
            }
        }
        function handleStockData() {
            if (isLogin === false) {
                $text.html(loginText)
            } else {
                if (isSAM === false) {
                    cfg.addToCartBtn.disabled()
                    $text.html(notSamText)
                }
            }

        }

        Event.addListener('onLogin', handleLoginData)
        Event.addListener('onStockReady', handleStockData)
    }

    //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  begin
    function goCustomize2(cfg){
        var $button = $('#btn-goCustomize2');
        if ($button.length) {
            $button.unbind('click').bind('click',function(){
                var selectedSkuid = tools.getSelectedSkuid()
                tools.otherBtnClick($(this).text(), selectedSkuid)
                //如果无货则不跳转 去定制页面
                if($(this).hasClass('btn-disable')) return false;
                if (G.onAttr('Customize-2')) {
                    window.location.href = '//dz.jd.com/customMade/toCustomizePC.action?pid='+cfg.skuid+'&pcount='+$('#buy-num').val();
                } else if (G.onAttr('Customize-3')) {
                    $.ajax({
                        type: "get",
                        url: "//bespoke.jd.com/bespokeAddress/queryBespokeAddress?skuId=" + cfg.skuid,
                        dataType: "jsonp",
                        jsonpCallback: 'jsonpCall'
                    })
                    .done(function (res) {
                        if (res && res.bespokeUrl) {
                            window.location.href = res.bespokeUrl+'&pcount='+$('#buy-num').val();;
                        }
                    })
                    .fail(function (err) {
                        console && console.log(err);
                    });
                }
            });

            Event.addListener('onStockReady', function checkDisplay(data) {
                var serviceskuid = tools.getServiceSkuid(data.stock && data.stock.data && data.stock.data.warrantyInfo)
                tools.otherBtnExpo($button.text(), serviceskuid)
                if (pageConfig.product.havestock) {
                    $button.removeClass('btn-disable');
                } else {
                    $button.addClass('btn-disable');
                }
            });
        }
    }
    //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  end

    // 百亿补贴底部二维码换成立即购买和加入购物车逻辑
    giftParam = '';  // 赠品池数据
    giftType  = '';  // 赠品池类型
    var bybtbtn = function (cfg) {
        var _this = this;
        Event.addListener('onStockReady', function(data) {
            var res = data && data.stock && data.stock.data
            var isStock = res && res.stockInfo && res.stockInfo.isStock // 是否有货
            var yuyueInfo = res && res.yuyueInfo; // 预约对象
            var yuyueInfoState = yuyueInfo && yuyueInfo.state; // 预约状态码
            var bybtInfo = res && res.bybtInfo // 百亿补贴对象
            // var nowBuyAb = res && res.nowBuyAb || true// 全流程支持立即购买开关和实验标识
            var govSupportInfo = res && res.govSupportInfo // 国补对象
            var bbpbjc =  bybtInfo && bybtInfo.bbpbjc // 屏蔽加车，true屏蔽加车
            var bybtInfoBybt = bybtInfo && bybtInfo.bybt;//百亿补贴促销标标识
            var productBybt = bybtInfo && bybtInfo.productBybt//百亿补贴商品标标识
            var bybtInfoLimit = bybtInfo && bybtInfo.limit;// 百亿补贴限购信息
            var bbfc = bybtInfo && bybtInfo.bbfc;// 是否跳转首页,出自PRD：https://joyspace.jd.com/pages/T8xWBAKPcU6tgDEgUibp
            var ab = bybtInfo && bybtInfo.ab;// true表示命中百补，露出加车立即购买，false表示未命中百补，前端展示二维码
            var flag = bybtInfoLimit && bybtInfoLimit.flag//  限购信息是否有效，命中限购信息加购、立即购买按钮置灰
            var bestPromotion = res.bestPromotion // 立即购买和加入购物车促销入参
            // var sourceInit = "common" // 接口文档：https://joyspace.jd.com/pages/LlqSuThwCeYndznpLaMU

            var govSupport = govSupportInfo && govSupportInfo.govSupport|| false // 国补促销标识，true 国补，false：非国补
            var gbAb = govSupportInfo && govSupportInfo.ab // 实验标识，true，走国补逻辑，false，反之
            var cloudPay = govSupportInfo && govSupportInfo.cloudPay || false // 云闪付标识，是否在标题前展示【国家补助】，true：展示，false：不展示
            var yellowBar = govSupportInfo && govSupportInfo.yellowBar //是否展示去绑定国补楼层，true：展示，false，不展示
            var bindUrl = govSupportInfo && govSupportInfo.bindUrl  || false // 黄条去绑定链接
            cfg.bestPromotion = bestPromotion // 全局控制参数
            if(bbfc){// PC端无兜底页面，接口返回1或者2时，则直接跳转首页
                window.location.href = '//www.jd.com?from=pc_item&bbfc=1';
            }
            var $InitTradeUrl =  $("#InitTradeUrl") //立即购买容器
            var $InitCartUrl =  $("#InitCartUrl,#InitCartUrl-mini") //加入购物车容器

            $InitTradeUrl.hide()// 默认隐藏立即购买按钮
            $InitCartUrl.removeClass("btn-disable-new")// 默认放开加入购物车
            $InitTradeUrl.removeClass("btn-disable-new")// 默认放开立即购买
            // cfg.nowBuyAb = nowBuyAb //暴露全局立即购买实验逻辑
            cfg.govSupport = govSupport //暴露全局国补标识
            // if(plantabidGfgm || (plantabid=="undefined")){ //只有官方购买或者没有tab选中的时候才支持立即购买等逻辑
                if(!productBybt && !govSupport){ // 全流程支持立即购买开关和实验标识、并有立即购买按钮
                    // 全流程放开立即购买
                    var sourceInit = "common"
                    var carHref = $("#InitCartUrl").attr("href")// 获取加入购物车原链接地址
                    var regex = /cart\.jd\.com\/gate\.action/;// 匹配原主流程加车链接
                    var carName = $("#InitCartUrl").html()// 获取加入购物车名称
                    if(regex.test(carHref) && carName == "加入购物车" && !$("#InitCartUrl").hasClass("btn-disable")){ // URL 包含 cart.jd.com/gate.action和名称是加入购物车
                        if(isStock){ // 有库存
                            if($InitTradeUrl.length > 0){
                                $InitTradeUrl.attr("data-source",sourceInit).show()// 展示立即购买按钮
                            }
                            $InitCartUrl.attr("data-source",sourceInit)
                        }else{
                            $InitCartUrl.addClass("btn-disable-new")// 置灰加入购物车
                            $InitTradeUrl.addClass("btn-disable-new")// 置灰立即购买
                        }
                    }
                }
    
                // 底部百亿补贴按钮展示逻辑
                var $chooseBtns = $("body").find("#choose-btns") // 通用按钮容器
                if($chooseBtns.length > 0 && productBybt && !govSupport){ // 屏蔽百亿补贴二维码展示按钮并且有百亿补贴商品标标识,并且没有国补的时候
                    if(ab){// 后端ab=true里面包含了百亿补贴、商品标、实验、Bpin 都满足
                        sourceInit = "bybt" // 更新source
                        var carHref = $("#InitCartUrl").attr("href")// 获取加入购物车原链接地址
                        var regex = /cart\.jd\.com\/gate\.action/;// 匹配原主流程加车链接
                        var carName = $("#InitCartUrl").html()// 获取加入购物车名称
                        if(regex.test(carHref) && carName == "加入购物车"){ // URL 包含 cart.jd.com/gate.action和名称是加入购物车
                            $InitTradeUrl.attr("data-source",sourceInit).show()// 展示立即购买按钮
                            $InitCartUrl.attr("data-source",sourceInit)
                            // $("#btn-baitiao").attr("data-source",sourceInit)// 命中百补白条购source制定
                        }
                        if(isStock){ // 有库存
                            $InitCartUrl.removeClass("btn-disable-new")// 放开加入购物车
                            $InitTradeUrl.removeClass("btn-disable-new")// 放开立即购买
                            if(bybtInfoBybt){// 命中百亿补贴促销标加购、立即购买按钮都展示
                                if(bbpbjc){// 只屏蔽加车按钮
                                    $InitCartUrl.addClass('btn-disable-new')// 置灰加入购物车
                                }else if(flag){// 限购信息是否有效，命中限购信息加购、立即购买按钮置灰
                                    $InitCartUrl.addClass("btn-disable-new")// 置灰加入购物车
                                    $InitTradeUrl.addClass("btn-disable-new")// 置灰立即购买
                                }else{
                                    $InitCartUrl.removeClass("btn-disable-new")// 放开加入购物车
                                    $InitTradeUrl.removeClass("btn-disable-new")// 放开立即购买
                                }
                            }else{// 没有百亿补贴促销标加购展示、立即购买按钮屏蔽
                                if(bbpbjc || flag){// 屏蔽加车按钮+没有百亿补贴促销标屏蔽立即购买按钮、限购信息是否有效，命中限购信息加购、立即购买按钮置灰
                                    $InitCartUrl.addClass("btn-disable-new")// 置灰加入购物车
                                    $InitTradeUrl.addClass("btn-disable-new")// 置灰立即购买
                                }else{// 只没有百亿补贴促销标屏蔽立即购买按钮
                                    $InitTradeUrl.addClass("btn-disable-new")// 置灰立即购买
                                }
                            }
                        }else{
                            $InitCartUrl.addClass("btn-disable-new")// 置灰加入购物车
                            $InitTradeUrl.addClass("btn-disable-new")// 置灰立即购买
                        }
                    }else{ // 命中百亿补贴商品标、BPin、ab=false 展示二维码
                        $("#btn-goapp").show() //展示去手机购买按钮
                        $(".J_choose_btn").hide() // 隐藏其他按钮
                        // 展示二维码
                        // $chooseBtns.html('<div class="mobile-only clearfix J-mobile-only" style="margin-bottom: 20px;"><div class="qrcode fl"></div><div class="text lh" data-url='+'https://api.m.jd.com/qrcode?appid=item-v3&functionId=pc_qrcode&skuId='+ cfg.skuid +'&position=2&isWeChatStock='+ cfg.jumpChannel+'>仅支持手机扫码购买<br/>扫一扫，立即下单</div>')
                        // require.async('PLG_ROOT/jQuery.qrcode', function() {
                        //     $('#choose-btns .qrcode').html('').jdQrcode({
                        //         render: 'image',
                        //         ecLevel: 'L',
                        //         size: 80,
                        //         text: 'https://api.m.jd.com/qrcode?appid=item-v3&functionId=pc_qrcode&skuId='+ cfg.skuid +'&position=2&isWeChatStock='+ cfg.jumpChannel
                        //     })
                        // })
                    }
                }
    
                // 仅百补source传bybt，仅国补source传zfbt，两个同时命中，优先国补source传zfbt
                if($chooseBtns.length > 0 && govSupport){ // 屏蔽二维码展示按钮并且有国补促销标标识
                    // $(".ui-area-common-wrap").find(".ui-area-module-title").html("收货地址<b></b>")
                    // $(".ui-area-common-wrap").find(".more").addClass("test")
                    // $(".ui-area-select-wrap").remove() //隐藏四级地址选择
                    if(isStock){ // 有库存
                        sourceInit = "zfbt"
                        var carHref = $("#InitCartUrl").attr("href")// 获取加入购物车原链接地址
                        var carName = $("#InitCartUrl").html()// 获取加入购物车名称

                        var reservationHref = $("#btn-reservation").attr("href")// 获取预约抢购原链接地址
                        var reservationName = $("#btn-reservation").html()// 获取预约抢购名称

                        var regex = /cart\.jd\.com\/gate\.action/;// 匹配原主流程加车链接
                        $InitCartUrl.removeClass("btn-disable-new")// 放开加入购物车
                        $InitTradeUrl.removeClass("btn-disable-new")// 放开立即购买
                        if((regex.test(carHref) && carName == "加入购物车") || (regex.test(reservationHref) && reservationName == "抢购" && yuyueInfoState == 4)){ // URL 包含 cart.jd.com/gate.action和名称是加入购物车以及预约期预约状态为4的时候
                            $InitTradeUrl.attr("data-source",sourceInit).show()// 展示立即购买按钮
                            $InitCartUrl.attr("data-source",sourceInit)
                            $("#btn-baitiao").attr("data-source",sourceInit)// 命中国补白条购source制定,白条购是否走主车还是立购参数还不一样
                        }
                    }else{
                        $InitCartUrl.addClass("btn-disable-new")// 置灰加入购物车
                        $InitTradeUrl.addClass("btn-disable-new")// 置灰立即购买
                    }
                }
                Event.fire({
                    type: 'onBtnChange',
                })
            // }
            // if(yellowBar && bindUrl && bindUrl.length > 0)// 展示国补楼层并且有去绑定链接，不需要判断国补促销标和实验
            // {
            //     $("#gb-support").show();
            //     $("#gb-support").find(".gb-link").attr("href",bindUrl)
            //     // 国补黄条楼层曝光埋点
            //     try {
            //         // 业务标识、功能标识、主商品skuid
            //         expLogJSON('pcsx', 'YellowtipsExpo', '{"biz_name": "gbzg"}')
            //     } catch (e) {
            //         if (typeof console !== 'undefined') {
            //             console.log('国补黄条曝光埋点错误');
            //         }
            //     }
            // }else{
            //     $("#gb-support").hide();
            // }

            if(cloudPay){// 云闪付标识，是否在标题前展示【国家补助】，不需要判断国补促销标和实验
                $("#bgIcon").show();
            }else{
                $("#bgIcon").hide();
            }
            
        });

        

        // 立即购买按钮
        $("body").find("#InitTradeUrl").click(function(){
            if (!verifyGiftPoolInfo()) { // 核查赠品池信息
                return;
            }
            if(!$(this).hasClass("btn-disable-new")){
                var carUrl = $("#InitCartUrl").attr("href")
                var source = $(this).attr("data-source") || ""
                if(source  ==  "zfbt" || source  ==  "bybt" || source  ==  "common"){ // 目前就百补和国补走新立即购买流程
                    var did = ""
                    var jd3csid =  ""
                    var ybId = ""
                    var jdhsid = ""
                    var customGiftInfoId = ""
                    var lsid = ""

                    if (/did=/.test(carUrl)) { // 送装服务
                        did =  G.serializeUrl(carUrl).param.did
                    }
                    if (/jd3csid=/.test(carUrl)) { // 平生各种服务
                        jd3csid =  G.serializeUrl(carUrl).param.jd3csid
                    }
                    shipInstallService.forEach(function(item) {
                            if(item == did) {
                                if (jd3csid) {
                                    jd3csid += ',' + did 
                                } else {
                                    jd3csid = did
                                }
                            }
                        })
                    if (/ybId=/.test(carUrl)) { // 延保服务
                        ybId =  G.serializeUrl(carUrl).param.ybId
                    }

                    if (/jdhsid=/.test(carUrl)) { // 京东服务
                        jdhsid =  G.serializeUrl(carUrl).param.jdhsid
                    }

                    if (/customGiftInfoId=/.test(carUrl)) { // 工服定制id
                        customGiftInfoId =  G.serializeUrl(carUrl).param.customGiftInfoId
                    }

                    if (/lsid=/.test(carUrl)) { // loc门店商品
                        lsid =  G.serializeUrl(carUrl).param.lsid
                    }

                    var extFlag = {
                        "did" : did,
                        "lsid" : lsid,
                        "customGiftInfoId" : customGiftInfoId
                    }
                    
                    var relationSkus = {
                        "jd3csid" : jd3csid,// 京选服务（平生）
                        "ybId" : ybId,// 延保
                        "jdhsid": jdhsid,// 京东服务
                        "giftPoolType":  giftType,// 赠品池类型
                        "gids":  giftParam,// 赠品
                    }
                    // 立即购买
                    getBybtTradeInfo(filterObject(extFlag), filterObject(relationSkus), cfg, filterObject(cfg.bestPromotion), source)
                    
                }
            }
        })
        // 通用加入购物车按钮
        $("body").find("#InitCartUrl,#InitCartUrl-mini").click(function(event){
            addToCart.call(this, event, cfg)
        })

        // 国补楼层黄条点击埋点
        $("#gb-support").find(".gb-link").click(function(){
            try {
                log('pcsx', 'Yellowtips', '{"biz_name": "gbzg"}')
            } catch (e) {
                if (typeof console !== 'undefined') {
                    console.log('国补黄条点击埋点错误');
                }
            }
        })
    }
    // 核查赠品池信息
    function verifyGiftPoolInfo() {
        if ( GiftPool.model && GiftPool.model.hasGift() ) {
            var result = GiftPool.model.getSelectedResult();
            if (result) {
                if (result.giftPoolType == 0) {
                    giftParam = result.gids;
                } else if (result.giftPoolType == 1) {
                    giftParam = result.gids;
                    giftType = 1;
                } else {
                    giftParam = '';
                }
                } else {
                GiftPool.view.$el.addClass('item-hl-bg');
                return false;
            }
        }
        return true;
    }
    // 判断是否为空对象
    var isEmptyObject = function(obj){    
        return Object.keys(obj).length === 0; 
    } 

    var filterObject = function(originalObj){  // 过滤一个对象里面没有值的字段并返回过滤后的对象
        try {
            var filteredObj = {}; 
            $.each(originalObj, function(key, value) 
            { 
                if (value) { 
                    filteredObj[key] = value; 
                } 
            });
            return filteredObj; 
        } catch(e) {
            return originalObj; 
        }
    } 
    var addToCart = function (event, cfg) { // 加入购物车
        var carUrl = $(this).attr("href")// 获取加入购物车原链接地址
        var reg = /cart\.jd\.com\/gate\.action/;// 匹配原主流程加车链接
        var source = $(this).attr("data-source") || ""
        if (!verifyGiftPoolInfo()) {
            return;
        }
        if(reg.test(carUrl) && !$(this).hasClass("btn-disable-new")){ // URL 包含 cart.jd.com/gate.action
            if(source  ==  "zfbt" || source  ==  "bybt"  || source  ==  "common"){ // 目前就百亿补贴和国补走新加车流程
                event.preventDefault();  // 阻止默认的跳转行为
                event.stopPropagation();
                var did = ""
                var jd3csid =  ""
                var ybId = ""
                var jdhsid = ""
                var customGiftInfoId = ""
                var lsid = ""

                if (/did=/.test(carUrl)) { // 送装服务
                    did =  G.serializeUrl(carUrl).param.did
                }
                if (/jd3csid=/.test(carUrl)) { // 平生各种服务
                    jd3csid =  G.serializeUrl(carUrl).param.jd3csid
                }
                shipInstallService.forEach(function(item) {
                        if(item == did) {
                            if (jd3csid) {
                                jd3csid += ',' + did 
                            } else {
                                jd3csid = did
                            }
                        }
                    })
                if (/ybId=/.test(carUrl)) { // 延保服务
                    ybId =  G.serializeUrl(carUrl).param.ybId
                }

                if (/jdhsid=/.test(carUrl)) { // 京东服务
                    jdhsid =  G.serializeUrl(carUrl).param.jdhsid
                }

                if (/customGiftInfoId=/.test(carUrl)) { // 工服定制id
                    customGiftInfoId =  G.serializeUrl(carUrl).param.customGiftInfoId
                }

                if (/lsid=/.test(carUrl)) { // loc门店商品
                    lsid =  G.serializeUrl(carUrl).param.lsid
                }

                var extFlag = {
                    "did" : did,
                    "lsid" : lsid,
                    "customGiftInfoId" : customGiftInfoId
                }
                var relationSkus = {
                    "jd3csid" : jd3csid,// 京选服务（平生）
                    "ybId" : ybId,// 延保
                    "jdhsid": jdhsid,// 京东服务
                    "giftPoolType":  giftType,// 赠品池类型
                    "gids":  giftParam,// 赠品
                }
                // 加入购物车
                getBybtCarInfo(filterObject(extFlag), filterObject(relationSkus), cfg, filterObject(cfg.bestPromotion), source)
            } else {
                tools.checkLogin(function (r) {
                    if(!(r && r.IsAuthenticated)){// 未登录
                        window.login && window.login()
                    }
                })
            }
        }else if($(this).hasClass("btn-disable-new")){
            event.preventDefault();  // 阻止默认的跳转行为
            event.stopPropagation();
        }
    }
    var getBybtCarInfo = function (extFlag, relationSkus, cfg, ext, source) { // 加入购物车
        if(cfg.item_gb_fastTest_label == "test_show_2"){// test_show_1: 新版商详base \ test_show_2: toast \ test_show_3:进购物车
            try{// 加入购物车成功埋点
                pageConfig && pageConfig.flyToCart({
                    img:$("#spec-img").attr('src'),
                    id:'InitCartUrl',
                    apiBody:{
                      appid: 'item-v3',
                      directOperation: {
                            source: source,
                            theSkus: [
                                {
                                skuId: cfg.skuid,
                                "num":  $("#buy-num").val() || "1", // 商品数量 虚拟组套传1，其他传期数*件数，本期pc不支持虚拟组套类型
                                itemType: 1, // 商品类型， 等待确认
                                extFlag: extFlag,
                                relationSkus: relationSkus
                                },
                            ],
                            ext: ext
                        },
                    },
                    callback:{
                        success: function(r) {
                            try {// 加入购物车点击
                                // log('pcsx', 'Productdetail_addcart', '{"mainskuid": "' + cfg.skuid + '", "touchstone_expids": [' + cfg.bbtouchstoneExpids + '], "type": [' + cfg.bbtype +'], "from_page": "' + cfg.bbfPage + '", "SgroupId": [' + cfg.bbsgroupId + '], "addcart_num": "' + num + '", "price": [' + cfg.bbpriceArr + ']}')
                                // 新版埋点
                                tools.landmine({
                                    functionName: 'Productdetail_addcart', 
                                    exposureData: ['mainskuid', 'touchstone_expids', 'type', 'from_page', 'SgroupId', 'price'],
                                    extraData: {
                                        addcart_num: $('#buy-num').val() || "1",
                                        serviceskuid: Object.keys(relationSkus)
                                    },
                                    errorTips: '点击购物车埋点错误'
                                })
                            } catch (e) {
                                if (typeof console !== 'undefined') {
                                    console.log('点击购物车埋点错误');
                                }
                            }
                            var url = r && r.url
                            if(r && r.success && url){
                                // window.location.href = url;
                                try {// 加入购物车成功埋点
                                    log('pcsx', 'Productdetail_BuyStatus', '{"mainskuid": "' + cfg.skuid + '", "addcart_num": "' + $('#buy-num').val() || "1", + '", "act": "addcar", "resultCode": "success"}')
                                } catch (e) {
                                    if (typeof console !== 'undefined') {
                                        console.log('点击购物车埋点错误');
                                    }
                                }
                            }else if(r && !r.success && r.code == 1){ // 未登录
                                window.login && window.login()
                                // var locname = window.location.hostname
                                // window.location.href = '//passport'+locname.split("item")[1]+'/new/login.aspx?ReturnUrl=' + encodeURIComponent(location.href);
                                tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "点击购物车按钮未登录",752)
    
                            }else if(r && !r.success && r.code == -881 && url){ // 其他跳转
                                window.location.href = url;
                            }else if(r && !r.success && r.code == -100){
                                tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "code=-100错误",752)
                            }else if(r && !r.success && r.code == -12){
                                // 加车失败
                                $("body").dialog({
                                    title: "提示", 
                                    source: "<div style='color:#e4393c'>购物车已经“超载”啦，清理部分商品后可继续加车哦～</div>",
                                    width: 350, 
                                    autoCloseTime:3
                                });
                                tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "点击购物车已经“超载”啦，清理部分商品后可继续加车哦",752)
                            }else{
                                // 加车失败
                                tools.showAddCartFailDialog(cfg.skuid)
                                
                                try {// 加入购物车失败埋点
                                    log('pcsx', 'Productdetail_BuyStatus', '{"mainskuid": "' + cfg.skuid + '", "addcart_num": "' + $('#buy-num').val() || "1", + '", "act": "addcar", "resultCode": "'+r.code+'"}')
                                } catch (e) {
                                    if (typeof console !== 'undefined') {
                                        console.log('点击购物车埋点错误');
                                    }
                                }
                                tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "当前商品可扫页面右下角二维码，前往京东App购买",752)
                            }
                        },
                        complete: function(){},
                        error: function(e) {
                            tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, e, "加入购物车接口错误异常",752)
                        }
                    }
                })                        
            } catch (e) {
                if (typeof console !== 'undefined') {
                    console.error('点击购物车方法动画报错:',e);
                }
                // 拖底自己调用下加车接口
                oldGetBybtCarInfo(extFlag, relationSkus, cfg, ext, source)// 旧的加入购物车
            }
        }else{
           // 拖底自己调用下加车接口
           oldGetBybtCarInfo(extFlag, relationSkus, cfg, ext, source)// 旧的加入购物车 
        }
       
        
    }
    var oldGetBybtCarInfo = function (extFlag, relationSkus, cfg, ext, source) { // 旧的加入购物车
        var url = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            url = pageConfig.product && pageConfig.product.colorApiDomain
        }
        // 入参 文档：https://joyspace.jd.com/pages/LlqSuThwCeYndznpLaMU
        var paramJson = {
            "serInfo": {
                "area": tools.getAreaId().areaIds.join('_'),
                "user-key": tools.getCookieNew("user-key") || '',
            },
            "directOperation": {
                "source": source,
                "theSkus": [{
                    "skuId": cfg.skuid,
                    "num":  $("#buy-num").val() || "1", // 商品数量 虚拟组套传1，其他传期数*件数，本期pc不支持虚拟组套类型
                    "itemType": 1,// 商品类型， 等待确认
                    "extFlag": extFlag,
                    "relationSkus": relationSkus
                }],
                "ext": ext
            }
            
        }
        var time = new Date().getTime()
        var body = JSON.stringify(paramJson);
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pcCart_jc_gate',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = tools.getCookieNew("__jda") || '';
                        getCarDataList(colorParm);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getCarDataList(colorParm);
                    //烛龙上报
                    console.error("加入购物车接口设备指纹异常:",e)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getCarDataList(colorParm);
            //烛龙上报
            console.error("加入购物车接口加固异常:",e)
        } 
        // 加固end
       
        function getCarDataList(colorParm){
            var num = $('#buy-num').val() || "1"
            $.ajax({
                url: url,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function(r) {
                    try {// 加入购物车点击
                        // log('pcsx', 'Productdetail_addcart', '{"mainskuid": "' + cfg.skuid + '", "touchstone_expids": [' + cfg.bbtouchstoneExpids + '], "type": [' + cfg.bbtype +'], "from_page": "' + cfg.bbfPage + '", "SgroupId": [' + cfg.bbsgroupId + '], "addcart_num": "' + num + '", "price": [' + cfg.bbpriceArr + ']}')
                        // 新版埋点
                        tools.landmine({
                            functionName: 'Productdetail_addcart', 
                            exposureData: ['mainskuid', 'touchstone_expids', 'type', 'from_page', 'SgroupId', 'price'],
                            extraData: {
                                addcart_num: $('#buy-num').val() || "1",
                                serviceskuid: Object.keys(relationSkus)
                            },
                            errorTips: '点击购物车埋点错误'
                        })
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.error('点击购物车埋点错误');
                        }
                    }
                    var url = r && r.url
                    if(r && r.success && url){
                        window.location.href = url;
                        try {// 加入购物车成功埋点
                            log('pcsx', 'Productdetail_BuyStatus', '{"mainskuid": "' + cfg.skuid + '", "addcart_num": "' + num + '", "act": "addcar", "resultCode": "success"}')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.error("点击购物车埋点错误:", e)
                            }
                        }
                    }else if(r && !r.success && r.code == 1){ // 未登录
                        window.login && window.login()
                        // var locname = window.location.hostname
                        // window.location.href = '//passport'+locname.split("item")[1]+'/new/login.aspx?ReturnUrl=' + encodeURIComponent(location.href);
                        tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "点击购物车按钮未登录",752)

                    }else if(r && !r.success && r.code == -881 && url){ // 其他跳转
                        window.location.href = url;
                    }else if(r && !r.success && r.code == -100){
                        tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "code=-100错误",752)
                      }else if(r && !r.success && r.code == -12){
                        // 加车失败
                        $("body").dialog({
                            title: "提示", 
                            source: "<div style='color:#e4393c'>购物车已经“超载”啦，清理部分商品后可继续加车哦～</div>",
                            width: 350, 
                            autoCloseTime:3
                        });
                        tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "点击购物车已经“超载”啦，清理部分商品后可继续加车哦",752)
                    }else{
                        // 加车失败
                        tools.showAddCartFailDialog(cfg.skuid)
                        
                        try {// 加入购物车失败埋点
                            log('pcsx', 'Productdetail_BuyStatus', '{"mainskuid": "' + cfg.skuid + '", "addcart_num": "' + num + '", "act": "addcar", "resultCode": "'+r.code+'"}')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                 //烛龙上报
                                console.error("点击购物车埋点错误:",e)
                            }
                        }
                        tools.getJmfe({functionId:"pcCart_jc_gate",body:relationSkus}, "code="+ r.code, "当前商品可扫页面右下角二维码，前往京东App购买",752)
                    }
                },
                error: function (e) {
                    //烛龙上报
                    console.error("加入购物车接口错误异常:",e)
                }
            })
        }
    }
    var getBybtTradeInfo = function (extFlag, relationSkus, cfg, ext, source) { // 去结算
        var url = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            url = pageConfig.product && pageConfig.product.colorApiDomain
        }
        // 入参 文档：https://joyspace.jd.com/pages/LlqSuThwCeYndznpLaMU
        var paramJson = {
            "serInfo": {
                "area": tools.getAreaId().areaIds.join('_'),
                "user-key": tools.getCookieNew("user-key") || '',
            },
            "directOperation": {
                "source": source,
                "theSkus": [{
                    "skuId": cfg.skuid,
                    "num":  $("#buy-num").val() || "1", // 商品数量 虚拟组套传1，其他传期数*件数，本期pc不支持虚拟组套类型
                    "itemType": 1,// 商品类型， 等待确认
                    "extFlag": extFlag,
                    "relationSkus": relationSkus
                }],
                "ext": ext
            }
            
        }
        var time = new Date().getTime()
        var body = JSON.stringify(paramJson);
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pcCart_jc_buyNow',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = tools.getCookieNew("__jda") || '';
                        getTradeDataList(colorParm);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getTradeDataList(colorParm);
                    //烛龙上报
                    tools.getJmfe(colorParm, e, "百亿补贴立即购买接口设备指纹异常",752)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getTradeDataList(colorParm);
            //烛龙上报
            tools.getJmfe(colorParm, e, "百亿补贴立即购买接口加固异常",752)
        } 
        // 加固end
       
        function getTradeDataList(colorParm){
            var num = $('#buy-num').val() || "1"
            $.ajax({
                url: url,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function(r) {
                    try{   
                        tools.landmine({
                            functionName: 'Productdetail_NowBuy',
                            exposureData: ['mainskuid', 'touchstone_expids', 'type', 'from_page', 'SgroupId', 'price'],
                            extraData: {
                                addcart_num: num,
                                serviceskuid: Object.keys(relationSkus)
                            },
                            errorTips: '立即购买曝光埋点错误'
                        })
                    } catch(e) {
                        console.log('上报Productdetail_NowBuy错误',e)
                    }
                    
                    var url = r && r.url
                    if(r && r.success && url){
                        window.location.href = url;
                        try {// 立即购买成功埋点
                            log('pcsx', 'Productdetail_BuyStatus', '{"mainskuid": "' + cfg.skuid + '", "addcart_num": "' + num + '", "act": "nowbuy", "resultCode": "success"}')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log('立即购买成功埋点错误');
                            }
                        }
                    }else if(r && !r.success && r.code == 1){ // 未登录
                        window.login && window.login()
                        // var locname = window.location.hostname
                        // window.location.href = '//passport'+locname.split("item")[1]+'/new/login.aspx?ReturnUrl=' + encodeURIComponent(location.href);
                    }else if(r && !r.success && r.code == -100){
                        console.log("r.code",r.code)
                    }else if(r && !r.success && r.code == -881 && url){ // 其他跳转
                        window.location.href = url;
                    }else{
                        // 加车失败
                        tools.showAddCartFailDialog(cfg.skuid)
                        try {// 立即购买成功埋点
                            log('pcsx', 'Productdetail_BuyStatus', '{"mainskuid": "' + cfg.skuid + '", "addcart_num": "' + num + '", "act": "nowbuy", "resultCode": "'+r.code+'"}')
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log('立即购买成功埋点错误');
                            }
                        }
                    }
                },
                error: function (e) {
                    //烛龙上报
                    tools.getJmfe(colorParm, e, "百亿补贴立即购买接口错误异常",752)
                }
            })
        }
    }
    var stickBeltAndTab = function() {
        var belt = $('#common_banner')
        var beltCopy = $('.belt-copy')
        if(!beltCopy.length) {
            belt.after('<div class="belt-copy"></div>')
            beltCopy = $('.belt-copy')
        }
        var tab = $('.common-plan')
        var tabCopy = $('.tab-copy')
        if(!tabCopy.length) {
            tab.after('<div class="tab-copy"></div>')
            tabCopy = $('.tab-copy')
        } 
        var hasBelt = belt.height() && belt.css('display') != 'none'
        var hasTab = tab.children().length && tab.css('display') != 'none'
        beltCopy.css({
            display: 'none',
            // width: belt.css('width'),
            height: '52px', 
        })
        tabCopy.css({
            display: 'none',
            // width: $('.information-wrap').width() + 'px',
            height: hasBelt ? tab.height() - 10 + 'px' : tab.height() + 'px',
        })
        // 腰带 tab 吸顶时显示 copy, 非吸顶时隐藏 copy
        function changeState() {
            hasBelt = belt.height() && belt.css('display') != 'none'
            hasTab = tab.children().length && tab.css('display') != 'none'
            var priceTop = setPriceTop(hasBelt, hasTab)
            var firmbuyH = $('.J-firmbuy-price').height() || 0;// 企业团购的高度
            $('#summary-quan').css('top', priceTop + 52 + firmbuyH + 'px') // 价格楼层高度 53px
            var tabTop = hasBelt ? 16 + belt.height() : 16 // 如果有腰带 Tab 位置下移
           
            var width = $('.information-wrap').width()
            var beltPadding = parseInt(belt.css('padding-left')) * 2
            if ($('.information-wrap').hasClass('pro-detail-hd-fixed')) {
                if(hasBelt) { // 有些腰带（3.8节腰带）没有子元素，改为判断其高度
                    belt.css({
                        width: width - beltPadding + 'px',
                        position: 'fixed',
                        top: '16px',
                        zIndex: 3
                    })
                    beltCopy.show()
                }
                if(hasTab) {
                    tab.css({
                        width: width + 'px',
                        position: 'fixed',
                        top: tabTop + 'px',
                        marginTop: hasBelt ? '-8px' : 0,
                        zIndex: 3
                    })
                    tabCopy.show()
                } else {
                    tabCopy.hide() // tab隐藏的时候，需要隐藏下面占位tab
                }
            // } else if ($('.information-wrap').hasClass('bottom-fixed')) {
            //     if(belt.height() && belt.css('display') != 'none') {
            //         belt.css({
            //             width: width - beltPadding + 'px',
            //             position: 'sticky',
            //             top: '16px',
            //             zIndex: 1
            //         })
            //         beltCopy.show()
            //     }
            //     if(tab.children().length && tab.css('display') != 'none') {
            //         tab.css({
            //             width: width + 'px',
            //             position: 'sticky',
            //             top: tabTop + 'px',
            //             zIndex: 1
            //         })
            //         tabCopy.show()
            //     }
            } else {
                if(hasBelt) {
                    belt.css({
                        width: width - beltPadding + 'px',
                        position: 'relative',
                        top: '0px',
                        zIndex: 1
                    })
                    beltCopy.hide()
                }
                if(hasTab) {
                    tab.css({
                        width: width + 'px',
                        position: 'relative',
                        top: '0px',
                        marginTop: hasBelt ? '-8px' : 0,
                        zIndex: 1
                    })
                    tabCopy.hide()
                }
            }
        }
        // 设置价格楼层吸顶高度
        function setPriceTop(hasBelt, hasTab) {
            var dom = $('.summary-first').length ? $('.summary-first') : $('#pingou').length ? $('#pingou') : $('.summary-price-wrap') // 预约、预售以及 普通价格
            var priceTop = $('.information-wrap').hasClass('fix-bottom') ? 0 : 16 // 上间距16
            hasBelt && (priceTop += 43) // 腰带高52 - 内容上移8 = 44 少一个像素，防止误差
            hasTab && (priceTop += 48)
            dom.css('top', priceTop + 'px')
            return priceTop
        }
        $(window).scroll(function() {
            changeState()
            setTimeout(function() {
                changeState()
            })
        })
        $(window).resize(function() {
            changeState() // 改变窗口时，改变 belt & tab 长度
        })
        Event.addListener('tabHide', function() { // tab 异步隐藏时直接隐藏 tab-copy
            tabCopy.hide()
        })
        Event.addListener('tabShow', function() { // tab 从隐藏改为显示时，调用 changeState， 因为 tab 可能需要重新计算 top 值
            changeState()
        })
        changeState()
    }
    var stickToBottom = function() {
        var btns = $('#choose-btns') // 底 bar 按钮楼层，包含 PK，步进器
        btns.wrapInner('<div class="choose-btns-wrapper"></div>');
        var wrapper = $('.choose-btns-wrapper')
        var bottom = $('.p-choose-wrap') // 右侧底部区域，包含底部按钮楼层及白条，京采支付楼层等
        var informationHeight, windowHeight
        // if (!bottom.length) return // 老版下架商品无底部元素
        function initStick() {
            var height = $('#J_TipBar').length ? '125px' : '95px'
            btns.length && btns.css('height', height)
            $('.sprite-close').click(function() {
                $('#J_TipBar').remove()
                btns.css('height', '95px')
            })
            wrapper.length && wrapper.css({
                background: '#fff',
                width: bottom.width() + 'px',
                // height: '52px',//'84px'
                bottom: 0,
                padding: '16px',
                marginLeft: '-16px',
                borderTop: '0.5px solid #f0f0f0'
                
            })
            informationHeight = $('.information-wrap').height()
            windowHeight = window.innerHeight - 16; // 上边距16
            if (informationHeight <= windowHeight + 1) {
                var beltHeight = $('#common_banner')[0] ? $('#common_banner')[0].getBoundingClientRect().height : 0
                var tabHeight = $('.common-plan')[0] ? $('.common-plan')[0].getBoundingClientRect().height: 0
                var marginTop =  parseInt($('.infomation').css('marginTop')) || 0 // 有腰带时，内容上移 8 像素
                var height = windowHeight - beltHeight - tabHeight - 10 - marginTop // 10 下边距
                $('.information-wrap').css('min-height', windowHeight) 
                $('.infomation').css('min-height', height + 1) // 把所有情况都按大于屏幕处理，简化逻辑
                bottom.length && bottom.css('min-height') == 'auto' && bottom.css('min-height', height - bottom.position().top) // min-height 只在 auto 的时候设置值，防止用户展开京选服务时重新设置值
            } else { // 当调整分辨率时，恢复为原始样式
                $('.information-wrap').css('min-height', '') 
                $('.infomation').css('min-height', '')
                bottom.length && bottom.css('min-height', '')
            }
        }
        function stick() {
            if(!btns[0]) return
            var btnsRect = btns[0].getBoundingClientRect();
            
            // if (!$('.information-wrap').hasClass('bottom-fixed')) {
            if($('.information-wrap').hasClass('pro-detail-hd-fixed') || document.documentElement.scrollTop < $('.information-wrap').offset().top){
                wrapper.css({
                    position: 'fixed',
                    left: btnsRect.left,
                    zIndex: 9,
                    paddingBottom: '26px'
                })
                btns.css('position', 'relative')
            } else {
                wrapper.css({
                    position: 'absolute',
                    left: 0,
                    zIndex: 0, // 设置为 0 否则会遮挡地址栏
                    paddingBottom: '16px',
                })
                if ($('.infomation').height() <= windowHeight) {
                    btns.css('position', 'absolute')
                } else {
                    btns.css('position', 'relative')
                }
            }
        }
        // $(document).ready(stick)
        $(window).resize(function() {
            initStick()
            stick()
        })
        Event.addListener('onStockReady', function(){ // 内容更新后
            setTimeout(function() {
                initStick()
                stick()
            }, 1000)
        });
        $(window).scroll(function() {
            initStick()
            stick()
            setTimeout(function() {
                stick() // 快速滚动滚动条滚动到页面底部时，可能会吸到最底部，加个延迟解决
            }, 100)
        })
        $(".information-wrap").scroll(stick)
        // var observer = new IntersectionObserver(function(entries) {
        //     entries.forEach(function(entry) {
        //         if (entry.isIntersecting) {
        //             wrapper.css({
        //                 position: 'relative',
        //                 padding: 0,
        //                 margin: 0,
        //                 zIndex: 0
        //             })
        //         } else {
        //             var wrapperRect = wrapper[0].getBoundingClientRect();
        //             var {top} = wrapperRect
        //             if (top < 0) { // 上滑出
        //                 return
        //             }
        //             wrapper.css({
        //                 position: 'fixed',
        //                 padding: '16px',
        //                 zIndex: 9,
        //                 'margin-left': '-16px',
        //                 'padding-bottom': '32px'
        //             })
        //         }
        //     });
        // }, {
        //     root: null,
        //     rootMargin: '0px',
        //     threshold: 0
        // });
        // observer.observe(btns[0]);
    }
    var findBtnByText = function(displayedBtns, text) {
        return displayedBtns.find(function(btn) {
            return $(btn).text() == text
        })
    }
    // 右侧按钮为下列按钮之一时，取消两个按钮中间间距
    var sepcialBtns = ['立即购买', '白条支付', '金采支付', '拼团价', '支付定金', '单独买']
    // 根据按钮数量改变按钮样式
    var changeBtnStyle = function(cfg) {
        var btnWrapper = $('.J_choose_btn')
        
        if($('.btn-more').length && $('.more-btns').children().length) {
            btnBag.reverse()
            btnBag.forEach(function(btn) {
                btnWrapper[0].insertBefore(btn.dom, btn.next)
            })
            btnBag = []
        }
        $('.btn-more').remove()
        var displayedBtns = btnWrapper.children().toArray().filter(function(btn) {
            return $(btn).css('display') != 'none'
        })
        // 重置按钮样式
        displayedBtns.forEach(function(btn) {
            $(btn).css({
                margin: '',
                borderRadius: ''
            })
        })
        var btnNum = displayedBtns.length

        if (btnNum > 1) {
            btnWrapper.addClass('morethan1')
            if (btnNum > 3) {
                $('<a class="btn-special1 btn-lg btn-more"></a>').insertBefore(btnWrapper.children()[0])
                $('.btn-more').append('<div class="more-btns"></div>')
                $('.more-btns').append('<div class="triangle"></div>')
                $('.btn-more').click(function() {
                    $('.btn-more').toggleClass('show-more-btns')
                    if($('.btn-more').hasClass('show-more-btns')) {
                        $('.more-btns').css('display', 'flex')
                        btnWrapper.css('overflow', 'visible')
                    } else {
                        $('.more-btns').css('display', 'none')
                        btnWrapper.css('overflow', 'hidden')
                    }
                })
                var shoppingListBtn = findBtnByText(displayedBtns, '加入采购清单')
                var shoppingCarBtn = findBtnByText(displayedBtns, '加入购物车')
                var jincaiPayBtn = findBtnByText(displayedBtns, '金采支付')
                if (shoppingListBtn && shoppingCarBtn) {
                    btnBag.push({
                        dom: shoppingListBtn,
                        next: $(shoppingListBtn).next().get(0)
                    })
                    btnBag.push({
                        dom: shoppingCarBtn,
                        next: $(shoppingCarBtn).next().get(0)
                    })
                    $('.more-btns').append($(shoppingListBtn))
                    $('.more-btns').append($(shoppingCarBtn))
                } else if (shoppingListBtn && jincaiPayBtn) {
                    btnBag.push({
                        dom: shoppingListBtn,
                        next: $(shoppingListBtn).next().get(0)
                    })
                    btnBag.push({
                        dom: jincaiPayBtn,
                        next: $(jincaiPayBtn).next().get(0)
                    })
                    $('.more-btns').append($(shoppingListBtn))
                    $('.more-btns').append($(jincaiPayBtn))
                }
            } else {
                var leftBtn = $(displayedBtns[0])
                var rightBtn = $(displayedBtns[1])
                leftBtn.css({margin: 0})

                if (btnNum == 2) {
                    var abTest = ['pre1', 'pre2', 'test_show', 'test_show_1']
                    var label = cfg.item_gb_label
                    // label = 'test2'
                    if (label && abTest.includes(label) && leftBtn.text() == '加入购物车') {
                        if (label == abTest[1]) {
                            leftBtn.css({
                                background: 'linear-gradient(90deg, #FFEDDA 0%, #FFDFBC 100%)',
                                color: '#B5691A'
                            })
                        } else {
                            leftBtn.css({
                                background: '#ff475d',
                                color: '#fff'
                            })
                        }
                        return
                    }
                    var text = rightBtn.text().split('￥')[0]
                    if (!sepcialBtns.includes(text)) return
                    // 改变圆角
                    leftBtn.css({
                        'border-top-right-radius': 0,
                        'border-bottom-right-radius': 0,
                    })
                    rightBtn.css({
                        'border-top-left-radius': 0,
                        'border-bottom-left-radius': 0,
                        margin: 0
                    })
                }
            }
        } else {
            btnWrapper.removeClass('morethan1')
        }
        if(cfg.commonButtonSwitch){ // 加车立购开关未登录托底开关
             // 未登录处理统一底部加车和立买按钮
            tools.checkLogin(function (r) {
                if(!(r && r.IsAuthenticated)){// 未登录
                    $(".J_choose_btn").html('<a href="#none" onclick="window.login && window.login()" class="btn-special1 btn-lg" style="font-size: 20px;margin: 0px;background: rgb(255, 71, 93);color: rgb(255, 255, 255);" data-source="common">加入购物车</a><a href="#none" onclick="window.login && window.login()" class="btn-special2 btn-lg" style="font-size: 20px; margin-left:8px">立即购买</a>')
                }
            })
        }
       
    }
    // 窄版并且按钮大于 2 时，字体缩小为 16 px
    var adjustBtnFontSize = function() {
        var displayedBtns = $('.J_choose_btn').children().toArray().filter(function(btn) {
            return $(btn).css('display') != 'none'
        })
        if (window.innerWidth < 1680 && displayedBtns.length > 2) {
            $('.choose-btns .btn-lg').css('font-size', '16px')
        } else {
            $('.choose-btns .btn-lg').css('font-size', '20px')
        }
    }
    function init(cfg) {
        // 加购按钮区域超出可视区域吸到底部
        try {
            stickToBottom()
        } catch(e) {
            console.error(e, 'stickToBottom 吸底功能报错')
        }
        adjustBtnFontSize() 
        $(window).resize(adjustBtnFontSize)
        Event.addListener('onStockReady', function(data){ // 内容更新后
            try {
                stickBeltAndTab()
                var data = data && data.stock && data.stock.data
                shipInstallService = (data.warrantyInfo && data.warrantyInfo.optionInstallInfo) || [] // 送装服务
                shipInstallService = shipInstallService.filter(function(item) { 
                    return item && item.isNewOptionInstallInfo
                }).map(function(item) {
                    return item.platformPid
                })
            } catch (error) {
                console.error(error, 'stickBeltAndTab 腰带Tab吸顶功能报错')
            }
        })
        Event.addListener('onJincaiChange', function(data) {
            changeBtnStyle(cfg)
        })
        Event.addListener('onBaiTiaoSelect', function(data) {
            changeBtnStyle(cfg)
        })
        Event.addListener('onBtnChange', function() {
            adjustBtnFontSize()
            changeBtnStyle(cfg)
        })
        // 加入购物车
        cfg.addToCartBtn = addToCartBtn.init(cfg);

        // 到货通知
        cfg.notifyBtn = notify.init(cfg);

        // 加入采购清单
        cfg.inventoryBtn = inventory.init(cfg);

        // 必购码
        cfg.bgmBtn = bgm.init(cfg);

        // 节能补贴
        cfg.jnbtBtn = jnbt.init(cfg);

        // 以旧换新
        cfg.yjhxBtn = yjhx.init(cfg);

        // 预约
        //拼购，优先级处理,拼购>预约
        if(!cfg.skuMarkJson || !cfg.skuMarkJson.pg){
            cfg.reservationBtn = reservation.init(cfg);
        }else{
            $('#yuyue-banner').remove()
        }

        // 秒杀
        cfg.koBtn = ko.init(cfg);

        // 定期送
        dqsBtn(cfg)

        // 定期购
        dqgBtn(cfg)

        // 分期用
        leaseBtn(cfg)
        

        // 山姆会员专享购
        samText(cfg);
        
        //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  begin
        goCustomize2(cfg);
        //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  end

        // 如果有车管家赠品，点击“购物车”按钮时，拼上参数gids
        $("#InitCartUrl").click(function(){
            if (colorSize.highLighAttr()) { // 如果颜色或尺码未选中,呈现红色高亮状态，禁用“加购物车”功能
                return;
            }
            var $promCarGift = $("#prom-car-gift .J-prom-gift");
            if ($promCarGift.length) {
                var carGiftId = $promCarGift.attr("data-id");
                if (carGiftId) {
                    var carHref = $(this).attr("href") + "&gids=" + carGiftId;
                    $(this).attr("href",carHref);
                }
            }

            /* 车型下传需求 产品：路平 开发：lqf  20181219
             * 文档地址：https://cf.jd.com/pages/viewpage.action?pageId=137886451
             * 简述：当用户选择 无需安装 时 点击 加入购物车 按钮，如果选择了车型 增加 modelid 参数下传
             * 拼接字段为 cm
             */
            var chooseCarBox = $('#choose-car');
            var vehicleSelectedBox = chooseCarBox.find('.vehicle.selected');
            if (chooseCarBox.length > 0 && vehicleSelectedBox.length > 0) {
                var modelId = vehicleSelectedBox.attr('data-modelid');
                if (modelId) {
                    var hasModelIdHref = $(this).attr('href') + '&cm=' + modelId;
                    $(this).attr('href', hasModelIdHref);
                }
            }
        });

        $("#btn-baitiao").unbind('click').bind('click', function() {
            colorSize.highLighAttr();
        });

        $("#InitCartUrl-mini").bind('click', function() {
            if (colorSize.highLighAttr()) {
                $('html,body').scrollTop($(".product-intro").offset().top);
            }
        });

        // 百亿补贴底部按钮处理逻辑
        bybtbtn(cfg)

        // PC国补资格领取
        // 需求文档：https://joyspace.jd.com/pages/lyQyplK7JsCpUZSXVJtg
        Guobu && Guobu.init(cfg)
        
        
        // 底部按钮展示二维码按钮还是常规按钮
        if(cfg && cfg.isClosePCShow){
            $("#btn-goapp").show()// 去手机购买二维码展示
            $(".J_choose_btn").hide()// 常规按钮隐藏
        }

        
        // setTimeout(function() {
        //     var $btn = $(".J_choose_btn")
        //     try{
        //         var LabelListArr = []
        //         $btn.find('a').each(function(index, ele){
        //             var text = $(ele).text().trim()
        //             console.log("text",text)
        //             LabelListArr.push({
        //                 "buttonName": text || "",
        //                 "serviceskuid":  -100
        //             })
        //         })
        //         // 曝光
        //         tools.exposure({
        //             functionName: 'PC_Productdetail_OtherBtn_Expo',
        //             exposureData: ['mainskuid'],
        //             extraData: {
        //                 LabelList: LabelListArr
        //             },
        //             errorTips: '其他底部决策按钮曝光报错'
        //         })
        //     } catch (error) {
        //         console.log("PC_Productdetail_OtherBtn_Expo报错",error)
        //     }
        //     try{
        //         // 埋点
        //         $btn.find('a').unbind("click").click(function(){
        //             console.log($(this).text().trim())
        //             try{
        //                 var text = $(this).text().trim()
        //                 tools.landmine({
        //                     functionName: 'PC_Productdetail_PromotionFloor_Click',
        //                     exposureData: ['mainskuid'],
        //                     extraData: {
        //                         LabelList: {
        //                             "buttonName": text|| "",
        //                             "serviceskuid":  -100,
        //                         }
        //                     },
        //                     errorTips: 'PC_Productdetail_PromotionFloor_Click埋点报错'
        //                 })
        //             } catch (error) {
        //                 console.log("PC_Productdetail_PromotionFloor_Click埋点报错",error)
        //             }
        //         })
        //     } catch (error) {
        //         console.log("PC_Productdetail_PromotionFloor_Click报错",error)
        //     }
        // }, 1000);
        
    }
    // 加入购物车按钮区域滚出可视区域时吸到底部
    
    module.exports.__id = 'buybtn';
    module.exports.init = init;
    module.exports.setAmount = setAmount;
    module.exports.addToCartBtn = addToCartBtn;
    module.exports.addToCart = addToCart;
});
