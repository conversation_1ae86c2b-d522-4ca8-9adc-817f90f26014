/* 媒体查询：1280px ≤ 窗口尺寸 < 1680px */
@media screen and (max-width: 1679px) {
    .root61 .product-intro .itemInfo-wrap, .root61 .product-intro .itemInfo-wrap .information-wrap {
        width: 528px !important;
    }
    .itemInfo-wrap .sku-name .sku-name-title {
        max-height: 56px;
        width: 405px; // 496 - 收藏（58.5+0.5+16 = 75）- 间距16 
        overflow: hidden;
    }
    .itemInfo-wrap .sku-name .sku-name-title:hover {
        max-height: max-content;
    }
    .itemInfo-wrap .sku-name .collect.vertical {
        flex-direction: column;
        justify-content: space-between;
        height: 42px;
        width: 58.5px; // 已收藏宽度
        padding-left: 16px;
        border-left: 0.5px solid rgba(0, 0, 0, 0.12)
    }
    .itemInfo-wrap .sku-name .collect.vertical i {
        margin: 0;
    }
    .choose-car .vehicles {
        padding-right: 48px;
        position: relative;
    }
    .choose-car .other {
        // position: absolute;
    }
    #choose-thmd .item a {
        max-width: 327px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .yjhxTip {
        right: 47px;
    }
    #choose-custom .custom-list ul li {
        width: 183px;
    }
    .other-styles-container .other-styles dd .title .center-column, .other-styles-container .other-styles dd .content .center-column {
        max-width: 192px;
    }
    // 按钮区域
    .choose-btns-wrapper{
        .J_contrast_btn em {
            display: none;
        }
        
        .J_contrast_btn {
            width: 24px;
            padding: 13px 8px;
        }
        .choose-amount {
            width: 128px;
            a {
                right: 0
            }
            .buy-num {
                width: 40px;
                left: 44px;
            }
        }
    }
    // 企业计划购
    .choose-floor .part {
        padding: 10px;
    }
    .choose-floor .part span {
        margin: 0;
    }
    .choose-floor .part .p2 {
        width: 175px;
    }
    .choose-floor .part .p3 {
        width: 110px;
    }
    .choose-floor .part .p4 {
        margin: 0;
    }
    // 搭配赠品
    .choose-gift .giftpool-body {
        width: 412px !important;
    }
    // 定制
    #choose-custom .custom-list ul li span:first-child {
        width: 90px;
    }
    // 门店
    .choose-shop span {
        max-width: 310px;   
    }
    // 腰带
    #common_banner {
        clip-path: path("M 8 0 H 520 A 8 8 0 0 1 528 8 V 52 A 8 8 0 0 0 520 44 H 8 A 8 8 0 0 0 0 52 V 8 A 8 8 0 0 1 8 0 Z");
    }
    .stock-address .ui-area-wrap .ui-area-text-wrap {
        max-width: 200px; // 地址宽度
    }
}