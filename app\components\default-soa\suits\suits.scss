@import '../common/lib';
@import './__sprite.scss';

#choose-suits {
    *position: relative;
    *z-index: 2;
    .dd {
        position: relative;
        z-index: 2;
    }
    .item {
        position: static;
    }




}
.choose-suits {
    .item {
        .title {
            background-color: #FFF;
            position: relative;
        }
    }

    .open {
        .title {
            z-index: 4;
            border: 1px solid $colorPriceRed;
        }
        a.title,a:hover {
            border-bottom: 1px solid #fff;
            color: $colorPriceRed;
        }
        .suits-panel {
            //border-color: $colorPriceRed;
            display: block;
            z-index: 3;
        }
    }

    .suits-panel {
        position: absolute;
        top: 33px;
        left: 0px;
        z-index: 1;
        background-color: #FFF;
        border: 1px solid #ccc;
        width: 540px;
        display: none;
        .p-price strong{
            font-size: 12px;
        }
        .disabled{
            .p-price strong{
                color: #f4adad;
            }
        }
    }

    .suits-box {
        margin: 20px 35px 0;
        //height: 240px;
        border-bottom: 1px dotted #dfdfdf;
        overflow:hidden;

        .J-scroll{
            width: 505px;
            overflow: hidden;
            ul{
                width: 1000px;
            }
            .thumb-scroll-wrap {
                margin-left: 16px;
                margin-right: 16px;
                width: 78px;
                overflow: hidden;
            }
            .yb-item-cat{
                width: 130px;
                height: 30px;
                margin-top: 5px;
            }
        }

        li.sku-item {
            width: 135px;
            //height: 230px;
            float: left;
            padding-right: 35px;
            position: relative;

            .p-img {
                position: relative;
                width: 100px;
                height: 100px;
                position: relative;
                padding-bottom: 0;
                text-align: center;
                margin: 0 auto;
                span{
                    position: absolute;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    line-height: 20px;
                    padding-left: 5px;
                    color: #fff;
                    background: rgba(0, 0, 0, .5);
                    *filter:alpha(opacity=50);
                }

                a {
                    padding: 0;
                    &.disabled {
                        cursor: default;
                    }
                }

                .no-item,
                .no-stock {
                    color: #FFF;
                    background: rgba(0, 0, 0, 0.3);
                    filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#32000000, endcolorstr=#32000000);
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    line-height: 20px;
                    height: 20px;
                    width: 100%;
                    padding: 0 2px;
                    overflow: hidden;
                }
            }

            .p-pic{
                position: relative;
                z-index: 2;
                width: 110px;
                margin: 5px auto 0;
                .sprite-arrowL{
                    @include sprite-arrowL;
                    position: absolute;
                    left: 0;
                    top: 4px;
                    padding: 0;
                }
                .sprite-arrowR{
                    @include sprite-arrowR;
                    position: absolute;
                    right: 0;
                    top: 4px;
                    padding: 0;
                }
                .pic-list{
                    padding: 0 2px;
                    line-height: 24px;
                    overflow: hidden;
                    a{
                        display: inline;
                        padding: 0;
                        vertical-align: top;
                    }
                    img{
                        border: 1px solid #fff;
                    }
                    .current{
                        img{
                            border: 1px solid #e53e41;
                        }
                    }
                }
                .inner-list{
                    width: 26px;
                }
            }

            .p-name {
                width: 130px;
                height: 28px;
                margin-top: 5px;
                overflow: hidden;
                a {
                    height: 34px;
                    padding: 0;
                    white-space: normal;
                    &:hover {
                        color: #E3393C;
                    }
                }
            }

            .p-check-price-num {
                line-height: 14px;
                font-size: 14px;
                .p-checkbox {
                    margin-right: 3px;
                    vertical-align: -3px;
                }
                .p-price {
                    color: #E4393C;
                }
            }

            //.p-price {
            //    width: 100px;
            //    line-height: 12px;
            //    input{
            //        vertical-align: -3px;
            //        margin-right: 3px;
            //    }
            //}
            //
            //.p-checkbox{
            //    line-height: 14px;
            //    input{
            //        margin-left: 0;
            //    }
            //}
            //.p-count{
            //    line-height: 14px;
            //    color: #999;
            //}

            .plus {
                width: 23px;
                height: 23px;
                overflow: hidden;
                line-height: 1000px;
                position: absolute;
                left: 139px;
                top: 46px;
                background: url(i/plus.png) 0 0 no-repeat;
            }

            &.last {
                margin-right: 0;
                padding-right: 0;
                .plus {
                    display: none;
                }
            }
        }
        .disabled{
            .p-name{
                a{
                    color: #bfbfbf;
                    &:hover{
                        color: #bfbfbf;
                    }
                }
            }
        }

        .arrow {
            position: absolute;
            top: 62px;
            width: 22px;
            height: 32px;
            cursor: pointer;
            padding: 0;
            i {
                @include inline-block;
                vertical-align: middle;
                margin-right: 5px;
            }
        }

        .arrow-next {
            .sprite-arrow-next {
                @include sprite-arrow-next;
            }
            @extend .arrow;
            right: 5px;
        }

        .arrow-prev {
            .sprite-arrow-prev {
                @include sprite-arrow-prev;
            }
            @extend .arrow;
            left: 5px;
        }
    }
    .suits-detail {
        padding: 10px;
        height: 28px;

        .btns {
            float: right;
            a {
                _display: inline;
            }
        }

        .text {
            color: #999;
        }

        .suits-price {
            float: left;
            margin-right: 15px;
            .p-price strong {
                font-size: 18px;
            }
        }

        .suits-save-price {
            float: left;
            span {
                @include inline-block;
                line-height:20px;
                padding: 1px 4px;
                vertical-align: middle;
            }
            .text {
                background:#999;
                color:#fff;
            }
            .p-price {
                border: 1px solid #ccc;
                border-left: none;
                padding: 0px 4px;
            }
        }
        .suits-tips {
            color: $colorPriceRed;
        }
        .suits-save-price .p-price strong{
            font-weight: normal;
            color: #999;
        }
    }

    .item:hover .btns a {
        color: #FFF;
    }
    .item:hover .btns a.btn-disable {
        color: #ccc;
    }
}
.p-choose-wrap{
    .item{
        .suits-panel{
            a{
                border: none;
            }
        }
    }
}

.ele-suits {
    .suits-panel {
        width: 500px;
    }

    .suits-box {
        margin: 20px 40px 0;

        .J-scroll {
            width: 415px;

            .thumb-scroll-wrap {
                margin-left: 16px;
                margin-right: auto;
                width: auto;
                overflow: visible;
            }

            .yb-item-cat {
                position: relative;
                left: 0;
                top: 5px;
                width: 130px;
                height: 40px;
                .yb-item {
                    padding: 4px 5px 4px 6px;
                    border: 1px solid #DDD;
                    line-height: 20px;
                    position: relative;
                    z-index: 2;
                    cursor: pointer;
                    a {
                        padding: 0;
                    }
                    .arrow-icon {
                        position: absolute;
                        right: 8px;
                        top: 12px;
                        @include sprite-arrowD;
                    }
                }
                .more-item {
                    display: none;
                    position: absolute;
                    top: 29px;
                    background-color: #FFF;
                    border: 1px solid #DDD;
                    width: 128px;
                    padding-top: 5px;
                    overflow: hidden;
                    z-index: 1;
                    a {
                        display: block;
                        line-height: 20px;
                        padding: 0 12px;
                        color: #999;
                        &:hover {
                            color: #E4393C;
                        }
                    }
                }
            }
            .selected {
                .more-item {
                    display: block;
                }
                .yb-item {
                    .arrow-icon {
                        @include sprite-arrowU;
                    }
                }
            }
        }

        li.sku-item {
            width: 100px;
            height: 163px;
            float: left;
            padding-right: 46px;
            position: relative;
            .p-img {
                width: 100px;
                height: 100px;
                position: relative;
                padding-bottom: 0;
                margin-bottom: 5px;
                a {
                    padding: 0;
                }
                .no-item,
                .no-stock {
                    color: #FFF;
                    background: rgba(0, 0, 0, .3);
                    filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#32000000, endcolorstr=#32000000);
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    line-height: 20px;
                    height: 20px;
                    width: 100%;
                    padding: 0 2px;
                    overflow: hidden;
                }
            }
            .p-pic {
                width: auto;
                margin: auto;
                .pic-list {
                    width: 95px;
                }
            }
            .p-name {
                height: 34px;
                margin: auto;
            }
            .plus {
                left: 110px;
            }
            .arrow {
                padding: auto;
            }
            .arrow-next {
                right: 0;
            }
            .asrrow-prev {
                left: 0;
            }
        }
    }
}



#choose-shop {
    .bt-info-tips{
        height: 32px;
        .icon {
            @include icons(16px, 16px);
            margin-top: 8px;
            margin-right: 5px;

        }
        .prom {
            background-image: url(i/hui.png);
        }
    }
}



