@import "./__sprite";
/*定期购*/
.period-buy{
    position: relative;
    li {
        line-height: 1.5em;
        margin-bottom: 0.5em;
    }
    .dd{
        position: relative;
    }
    .period-wrap{
    }
    .item{
        position: static;
    }
    .period-inner{
        line-height: 32px;
        padding: 0 15px;
        margin-right: 4px;
        border: 1px solid #ccc;
    }

    .period-more{
        display: none;
        position: absolute;
        top: 32px;
        left: 0;
        background: #fff;
        border: 1px solid #ddd;
        padding: 15px 10px 15px;
        width: 580px;
        height: 310px;
        overflow: hidden;
        z-index: 2;
    }
    .selected{
        .period-more{
            display: block;
        }
        .period-inner{
            border-color: #e4393c;
            background: #fff;
            border-bottom: none !important;
            position: relative;
            z-index: 3;
        }
    }

    .service-tips{
        position: relative;
        display: inline-block;
    }
    .sprite-question{
        display: inline-block;
        vertical-align: -2px;
        @include sprite-question;
    }
    .sprite-hui{
        display: inline-block;
        vertical-align: -2px;
        @include sprite-hui;
        margin-right: 2px;
    }
    .service-tips .tips {
        display: none;
        width: 400px;
        position: absolute;
        left: -10px;
        top: 32px;
        display: none;

        .content {
            padding: 10px;
            background: #fff;
            border: 1px solid #cecbce;
            color: #666;
            -moz-box-shadow: 0 0 2px 2px #eee;
            -webkit-box-shadow: 0 0 2px 2px #eee;
            box-shadow: 0 0 2px 2px #eee;
            dt{
                font-weight: bold;
                margin-bottom: 3px;
            }
            dd{
                line-height: 170%;
            }
            p{
                border-top: 1px dotted #999;
                margin-top: 7px;
                padding-top: 7px;
                a{
                    color: #5e69ad;
                    margin: 0 5px;
                    &:hover{
                        color: #e4393c;
                    }
                }
            }
        }
        .sprite-arrow {
            @include sprite-arrow;
            position: absolute;
            overflow: hidden;
            left: 12px;
            top: -5px;
            _bottom: -1px;
        }
    }
    .hover .tips{
        display: block;
        z-index: 2;
    }

}
.choose-shop {
    .icon-o2o{
        flex-shrink: 0;
        display: inline-block;
        margin-right: 5px;
        vertical-align: 0px;
        width: 12px;
        height: 12px;
        background: url(https://img13.360buyimg.com/imagetools/jfs/t1/254016/37/15018/743/678f9ab5F71c1ea4f/fbf96b28d926b9ef.png) no-repeat;
        background-size: 12px;

    }
    span {
        // max-width: 310px;   
        overflow: hidden;    
        text-overflow: ellipsis;
    }
    .item {
        // width: 130px; // 字数不固定，宽度不固定
        // float: none;
        a {
            padding: 0 12px;
            border: 0.5px solid rgba(0, 0, 0, 0.02);
            display: flex;
            align-items: center;
        }
    }
    .tips strong {
        color: #888B94;
        font-size: 14px;
        font-weight: normal;
    }
}
