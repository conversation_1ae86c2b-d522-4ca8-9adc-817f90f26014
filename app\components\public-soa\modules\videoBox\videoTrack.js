define('PUBLIC_ROOT/modules/videoBox/videoTrack', function(require, exports, module) {
    //视频埋点
    //cors 兼容 ie9一下
    require('./jquery.xdomainrequest.min.js')
    jQuery.support.cors = true;

    var tools = {
        // 类选择器
        getElementsByClassName: function (className,context,tagName) {

            if(typeof context == 'string'){
                tagName = context;
                context = document;
            }else{
                context = context || document;
                tagName = tagName || '*';
            }
            if(context.getElementsByClassName){
                return context.getElementsByClassName(className);
            }
            var nodes = context.getElementsByTagName(tagName);
            var results= [];
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                var classNames = node.className.split(' ');
                for (var j = 0; j < classNames.length; j++) {
                    if (classNames[j] == className) {
                        results.push(node);
                        break;
                    }
                }
            }
            return results;
        },

        //videojs版本兼容
        getVideoElement:function (player,elClass) {
            // || videojs.players["video-player"].controlBar.progressControl.seekBar.b
            //    找属性，不具有公用性，废弃
            if(typeof player.$ == 'function'){
                return player.$('.' + elClass);
            }else{
                return this.getElementsByClassName(elClass)[0];
            }
        },

        //获取参数,举例a.mp4?a=1&b=2，获取{a:1,b:2}
        getUrlParams:function (url) {
            var params,paramsFormat,obj = {};
            if(!url) return obj;
            params = url.split('?')[1];
            if(!params) return obj;
            paramsFormat = params.split('&');
            for(var i=0;i<paramsFormat.length;i++){
                var  k = paramsFormat[i].split('=');
                if(!!k[1]){
                    obj[k[0]] = k[1]
                }
            }
            return obj;
        }

    };

    var videoTrack = {
        init:function (player,sku,vid,playUrl) {
            var params = tools.getUrlParams(playUrl);
            this.player =  player;
            this.data = {
                sku:sku,
                vid:vid,
                videoDuration:0,
                playDuration:0,
                status:1,//0：播放完成 1：未播放完结束
                isLoading:0,//是否卡顿 0：否，1：是（卡顿时长 > 3秒，视为卡顿）
                loadingTime:0,//卡顿总时长（如果没卡顿，可以不传）
                isError:0,//0：无异常，1：异常
                errorCode:0,//异常码（发生异常时，原始异常码；如果没有异常，可以不传）
                videoSource:0,//视频来源 0：主图页视频， 1：商祥视频，2：晒单视频
                time:0,
                dockingId:params['dockingId']||'',
                storageSource:params['storageSource']||''
            }
            this.waitingFlag = false;
            this.waitingBegin = 0;
            this.sendFlag = false;
            this.bingEvent();
            this.ifSend = true;//是否发送数据，默认发送
        },
        data:{},
        disabledSend:function () {
            this.ifSend = false;
        },
        enabledSend:function () {
          this.ifSend = true;
        },
        bingEvent:function () {
            var _this = this;
            var _player = _this.player;
            _player.on('waiting',function () {
                if(!_this.ifSend) return false;
                _this.waitingFlag = true;
                _this.waitingBegin = (new Date()).getTime();
                console.log('waiting')
            });
            _player.on('loadeddata',function () {
                if(!_this.ifSend) return false;
                //处理ie9兼容性问题：视频过小时，会不显示放大按钮
                var fullScreenBtn = tools.getVideoElement(_player,'vjs-fullscreen-control');
                fullScreenBtn && (fullScreenBtn.style.display = 'block');
                _this.data.videoDuration = typeof _player.duration() == 'number'?_player.duration().toFixed(2):0;
            });

            _player.on('playing',function () {
                if(!_this.ifSend) return false;
                if(_this.waitingFlag){
                    var time = (new Date()).getTime();
                    var ti = time - _this.waitingBegin;
                    if(ti > 3000){
                        _this.data.isLoading = 1;
                        _this.data.loadingTime += ti;
                    }
                    _this.waitingFlag = false;
                }
                console.log('playing')
            });
            _player.on('ended',function () {
                if(!_this.ifSend) return false;
                console.log('ended')
                _this.data.playDuration = typeof _player.currentTime() == 'number'?_player.currentTime().toFixed(2):0;
                _this.data.status = 0;
                //播放结束，再播放，可以接着传
                _this.sendFlag = false;
                _this.sendData();
            });
            _player.on('timeupdate',function () {
                if(!_this.ifSend) return false;
                _this.data.playDuration = typeof _player.currentTime() == 'number'?_player.currentTime().toFixed(2):0;
            });

            _player.on('dispose',function () {
                if(!_this.ifSend) return false;
                console.log('dispose');
                _this.data.playDuration = typeof _player.currentTime() == 'number'?_player.currentTime().toFixed(2):0;
                _this.sendData();
            });
            _player.on('error',function (erropt) {
                if(!_this.ifSend) return false;
                console.log('error');
                var err_code = _player.error().code
                _this.data.isError = '1',//0：无异常，1：异常
                _this.data.errorCode = err_code,//异常码（发生异常时，原始异常码；如果没有异常，可以不传）
                _this.data.playDuration = typeof _player.currentTime() == 'number'?_player.currentTime().toFixed(2):0;
                _this.sendData();
            });
            var beforeunloadCallback = function(){
                _this.sendData();
            }
            //绑定浏览器关闭事件
            $(window)
                .unbind('beforeunload', beforeunloadCallback)
                .bind('beforeunload', beforeunloadCallback);
        },
        sendData:function () {
            var _this = this;
            var _data = _this.data;
            //防止重复发送
            if(_this.sendFlag ||  !_this.ifSend) {
                return;
            }
            _this.sendFlag = true;
            _data.time = (new Date()).getTime();
            localStorage.sendTime = _data.time;
            //时间转化成毫秒
            _data.videoDuration = _data.videoDuration*1000;
            _data.playDuration = _data.playDuration*1000;
            // var thor = typeof readCookie !='undefined' && ( 'thor='+ readCookie('thor') );
            var track_data = {
                functionId:'videoReport',
                appid:'video_pc',
                client:'video_pc',
                clientVersion:'',
                body:JSON.stringify(_data)
            }
            //预发布 beta-api.m.jd.com/api
            //正式：api.m.jd.com/api
            $.ajax({
                url: '//api.m.jd.com/api',
                data:track_data,
                contentType:"text/plain",
                type:"get",
                dataType:"json",
                // async: false,
                // dataType: "jsonp",
                timeout: 6000,
                xhrFields: {
                    withCredentials: true,
                    crossDomain: true,
                },
                success: function (data) {
                    console.log(data);
                }
            })
        }
    }
    module.exports = videoTrack
})
