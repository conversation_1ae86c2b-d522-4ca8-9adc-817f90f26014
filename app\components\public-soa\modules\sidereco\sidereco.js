define('PUBLIC_ROOT/modules/sidereco/sidereco', function(require, exports, module) {
    var Recommend = require('PUBLIC_ROOT/modules/common/tools/recommend')
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools')
    var G = require('PUBLIC_ROOT/modules/common/core')

    require('PUBLIC_ROOT/modules/ELazyload/ELazyload')
    require('PUBLIC_ROOT/modules/ETab/ETab')
    require('JDF_UNIT/trimPath/1.0.0/trimPath')

    var template =
        '\
    <div class="mt">\
        <h3>${ext.title}</h3>\
    </div>\
    <div class="mc">\
        <ul class="plist {if !pageConfig.product.twoColumn && !pageConfig.product.isEBook} plist-2 {/if}">\
        {for item in data}\
        <li class="fore${Number(item_index)+1}" \
            data-clk="${item.clk}" \
            data-push="${pageConfig[skuHooks].push(item.sku)}">\
            <div class="p-img ac">\
                <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">\
                    <img height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" alt="${item.t}" src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s${ext.imgWidth}x${ext.imgHeight}_${item.img}">\
                </a>\
            </div>\
            <div class="p-name"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
            <div class="p-price"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
        </li>\
        {/for}\
        </ul>\
    </div>'

    var templatePOP =
        '\
    <ul id="" class="plist plist-pop {if !pageConfig.product.twoColumn} plist-2 {/if}">\
        {for item in data}\
        <li class="fore${Number(item_index)+1}" \
            data-clk="${item.clk}" \
            data-push="${pageConfig[skuHooks].push(item.sku)}">\
            <div class="p-img ac">\
                <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">\
                    <img height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" alt="${item.t}" src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s${ext.imgWidth}x${ext.imgHeight}_${item.img}">\
                </a>\
                <div class="p-name">\
                    <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a>\
                </div>\
            </div>\
            <div class="p-info clearfix">\
                <div class="p-price fr"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
            </div>\
            {if !ext.hideHotSaleNum && !ext.isRLShop}\
            <div class="p-num">${Number(item_index)+1}</div>\
            {/if}\
        </li>\
        {/for}\
    </ul>'

    function mLazyload($el, cb) {
        if ($el.length > 0) {
            $el.ELazyload({
                type: 'module',
                onAppear: cb
            })
        }
    }

    function initSearch(cfg) {
        var obj = $('#btnShopSearch')

        window.changeSpPrice = function(id) {
            var val = $('#' + id).val()
            var min = $('#sp-price').val()
            var max = $('#sp-price1').val()
            if (parseInt(val) > 0 && parseInt(val) + '' == val) {
            } else {
                $('#' + id).val('')
            }
        }

        if (obj.length > 0) {
            pageConfig.searchClick = function(type) {
                var searchLink = obj.attr('data-url')
                var keyword = $('#sp-keyword').val()
                if (keyword) {
                    keyword = encodeURIComponent(encodeURIComponent(keyword))
                }
                var minprice = $('#sp-price').val()
                var maxprice = $('#sp-price1').val()
                if (keyword || minprice || maxprice) {
                    window.open(
                        searchLink +
                            '?orderBy=5&keyword=' +
                            keyword +
                            '&minPrice=' +
                            minprice +
                            '&maxPrice=' +
                            maxprice,
                        '_blank'
                    )
                } else {
                    if (type == 1) return
                    window.open(searchLink + '?orderBy=5', '_blank')
                }
            }
            obj.click(pageConfig.searchClick)
        }
    }

    function initCate(cfg) {
        var $sideCategory = $('#sp-category')

        $sideCategory.delegate('dt s', 'click', function() {
            var $this = $(this)
            var $parent = $(this).parents('dl').eq(0)

            $parent.toggleClass('open')
        })
    }

    function initRank(cfg) {
        var $rank = $('#rank')

        if (!$rank.length) {
            return false
        }
        $rank.ETab().ELazyload({
            source: 'data-lazyload'
        })

        var skus = []
        $rank.find('[data-sku]').each(function() {
            skus.push($(this).data('sku'))
        })

        Tools.priceNum({
            skus: skus,
            $el: $rank
        })
    }
    // 火热预约下线处理2024/03
    // function initYuYue(cfg) {
    //     var $yuyue = $('#yuyue-reco')

    //     if (!$yuyue.length) {
    //         return false
    //     }

    //     function render(r) {
    //         var tpl =
    //             '\
    //         <ul class="plist">\
    //         {for item in data}\
    //         {if Number(item_index) < 6}\
    //         <li class="fore${Number(item_index)+1}" \
    //             data-push="${pageConfig[skuHooks].push(item.sku)}">\
    //             <div class="p-img ac">\
    //                 <a target="_blank" title="${item.name}" href="//item.jd.com/${item.sku}.html">\
    //                     <img height="150" width="150" alt="${item.name}" src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s150x150_${item.url}">\
    //                 </a>\
    //             </div>\
    //             <div class="p-name"><a target="_blank" title="${item.name}" href="//item.jd.com/${item.sku}.html">${item.name}</a></div>\
    //             <div class="p-price"><strong class="J-p-${item.sku}"></strong></div>\
    //         </li>\
    //         {/if}\
    //         {/for}\
    //         </ul>'

    //         var skuHooks = 'yuyue_reco'
    //         r.skuHooks = skuHooks
    //         pageConfig[skuHooks] = []

    //         $yuyue.show().find('.mc').html(tpl.process(r))

    //         Tools.priceNum({
    //             skus: pageConfig[skuHooks],
    //             $el: $yuyue
    //         })
    //     }

    //     var time = new Date().getTime()
    //     var colorParm = {
    //         appid: 'item-v3',
    //         functionId: 'hot_getHotPresell',
    //         client: 'pc',
    //         clientVersion: '1.0.0',
    //         t: time,//生成当前时间毫秒数
    //         loginType: '3',
    //         uuid: Tools.getCookieNew("__jda") || '',
    //         body: JSON.stringify({sku: cfg.skuid}),
            
    //     }

    //     var host = '//api.m.jd.com'
    //     if(pageConfig.product && pageConfig.product.colorApiDomain){
    //         host = pageConfig.product && pageConfig.product.colorApiDomain
    //     }


    //     $.ajax({
    //         url: host,
    //         data: colorParm,
    //         dataType: 'json',
    //         // contentType: "application/json;charset=gbk",
    //         xhrFields: {
    //             withCredentials: true,
    //         }, 
    //         success: function(r) {
    //             if (r && r.length) {
    //                 render({
    //                     data: r
    //                 })
    //             }
    //         }
    //     })
    // }

    function init(cfg) {
        var pType = cfg.pType
        var url = null

        // 达人选购
        var $viewBuy = $('#view-buy')
        var viewBuyPid = null
        if (pType === 1) {
            viewBuyPid = 103001
        }
        if (pType === 2) {
            viewBuyPid = 102003
        }
        if (viewBuyPid) {
            mLazyload($viewBuy, function() {
                new Recommend({
                    // url: '//mixer.jd.com/api?',
                    
                    // url: '//api.m.jd.care/api?', //预发
                    // url: '//intra.m.jd.care/api?', //内网预发
                    url: '//api.m.jd.com',  //线上
                    // url: '//intra.m.jd.local/api?',  //内网线上
                    $el: $viewBuy,
                    skuHooks: 'SKUS_viewBuy',
                    template: template,
                    ext: {
                        title: '达人选购',
                        imgWidth: 180,
                        imgHeight: 180
                    },
                    param: {
                        p: viewBuyPid,
                        sku: cfg.skuid,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    isNewMixer: true
                })
            })
        }

        // 买了还买
        var $viewView = $('#view-view')
        var viewViewPid = null
        if (pType === 1) {
            viewViewPid = 105000
        }
        if (pType === 2) {
            viewViewPid = 102004
        }
        if (G.isPop && cfg.isCloseLoop) {
            viewViewPid = 902029
        }
        if (viewViewPid) {
            mLazyload($viewView, function() {
                new Recommend({
                    url: url,
                    $el: $viewView,
                    skuHooks: 'SKUS_viewView',
                    template: template,
                    ext: {
                        title: '看了又看',
                        imgWidth: 180,
                        imgHeight: 180
                    },
                    param: {
                        p: viewViewPid,
                        sku: cfg.skuid,
                        ck: 'pin,ipLocation,atw,aview'
                    }
                })
            })
        }

        //电子书
        if (cfg.isEBook) {
            //重磅好书
            var $zbhsReco = $('#zbhs-reco')
            mLazyload($zbhsReco, function() {
                new Recommend({
                    url: url,
                    $el: $zbhsReco,
                    skuHooks: 'SKUS_zbhsReco',
                    template: template,
                    // loadPrice: false,
                    ext: {
                        title: '重磅好书',
                        imgWidth: 170,
                        imgHeight: 170
                    },
                    param: {
                        p: 619057,
                        sku: cfg.skuid,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    callback: function(hasData, r) {
                        if (r.success) {
                            var arr = []
                            $.each(r.data, function(i, n) {
                                arr.push(n.sku)
                            })
                            // var priceNum = require('PUBLIC_ROOT/modules/ebook/ebook')
                            //     .priceNum
                            // var opts = {
                            //     skus: arr,
                            //     $el: $zbhsReco
                            // }
                            // priceNum(opts)
                        }
                    }
                })
            })
            
        }

        // 店铺热销,店铺人气
        ;(function(cfg) {
            cfg = cfg || {};
            var isOnSale = cfg.warestatus == 1 ? true : false;
            var $popHot = $('#pop-hot')

            if ($popHot.length) {
                $popHot.ETab({
                    onSwitch: function(n) {
                        if (n === 1 && !this.items.eq(n).data('loaded')) {
                            loadHotSale()
                            this.items.eq(n).data('loaded', true)
                        }
                    }
                });
                var popHotTab = $popHot.data('ETab');
                if (!isOnSale) {
                    popHotTab.triggers.eq(1).hide();
                }
            }

            function loadHotSale(firstEmpty) {
                //热门关注
                var $popHotFo = $('#sp-hot-fo')
                new Recommend({
                    $el: $popHotFo,
                    showOnData: false,
                    skuHooks: 'SKUS_popHotFo',
                    template: templatePOP,
                    hasEmptyMsg: true,
                    ext: {
                        title: '热门关注',
                        imgWidth: 160,
                        imgHeight: 160,
                        isFollow: true,
                        isRLShop: cfg.isRLShop,
                        hideHotSaleNum: cfg.isCfy || (cfg.isOtc&&!cfg.isPop)
                    },
                    param: {
                        p: 509002,
                        sku: cfg.shopId,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    callback: function(hasData) {
                        if (firstEmpty && !hasData) {
                            $popHot.hide()
                        }
                        console.log("hasData",hasData)
                        try{
                            if(hasData){
                                // 热门关注主图浮层渲染
                                // var skuArrs = []
                                // for(i = 0;i < r.data.length; i++){
                                //     skuArrs.push(r.data[i].sku)
                                // }
                                // Tools.getMainPic(160, 160,"rmgz-","2",skuArrs)
                                var layerList = r.floatLayerList
                                if(layerList.length > 0){
                                    Tools.getPcSkuLayers(layerList, 160, 160,"rmgz-","2")
                                }
                            }
                        }catch(e){
                            console.log("主图浮层初始化渲染",e)
                        }
                    }
                })
            }
            //店铺热销
            var $popHotSale = $('#sp-hot-sale')
            mLazyload($popHotSale, function() {
                new Recommend({
                    $el: $popHotSale,
                    skuHooks: 'SKUS_popHotSale',
                    template: templatePOP,
                    hasEmptyMsg: true,
                    ext: {
                        title: '店铺热销',
                        imgWidth: 160,
                        imgHeight: 160,
                        isFollow: false,
                        // http://cf.jd.com/pages/viewpage.action?pageId=87980043
                        isRLShop: cfg.isRLShop,
                        hideHotSaleNum: cfg.isCfy || (cfg.isOtc&&!cfg.isPop) || (parseInt(cfg.cat[0]) == 737)
                    },
                    param: {
                        p: 509001,
                        sku: cfg.shopId,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    callback: function(hasData) {
                        if (!hasData) {
                            if (isOnSale) {
                                loadHotSale(true);
                                popHotTab.go(1);
                            } else {
                                $popHot.hide();
                            }
                        }
                    }
                })
            })

            // 店铺新品
            var $shopNew = $('#sp-new')
            mLazyload($shopNew, function() {
                new Recommend({
                    $el: $shopNew,
                    skuHooks: 'SKUS_new',
                    template: template,
                    ext: {
                        title: '店铺新品',
                        imgHeight: '160',
                        imgWidth: '160'
                    },
                    param: {
                        p: 610008,
                        sku: cfg.shopId,
                        ck: 'pin,ipLocation,atw,aview'
                    }
                })
            })
        })(cfg);

        initRank(cfg)
        initCate(cfg)
        initSearch(cfg)
        // initYuYue(cfg) 
    }

    module.exports.__id = 'sidereco'
    module.exports.init = init
})
