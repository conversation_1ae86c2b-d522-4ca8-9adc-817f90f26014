define('MOD_ROOT/jincai/jincai', function(require, exports, module) {
    var G = require('MOD_ROOT/common/core')
    var Event = require('MOD_ROOT/common/tools/event').Event
    var login = require('JDF_UNIT/login/1.0.0/login')

    require('MOD_ROOT/ETooltips/ETooltips')
    require('JDF_UNIT/trimPath/1.0.0/trimPath')

    function log(msg) {
        if (console && typeof console.log === "function") {
            console.log(msg);
        }
    }

    var JinCai = function(opts, onSelected) {
        this.price = opts.price

        this.$jc = $('#choose-jincai')
        this.$jcBtn = $('#btn-jincai')

        this.cfg = opts.cfg

        this.onSelected = onSelected || function() {}

        this.sku = opts.sku
        this.cat = opts.cat
        this.shopId = opts.cfg.shopId

        this.enable = false
        this.payType = null

        this.giftParam = '';  // 赠品池数据
        this.giftType  = '';  // 赠品池类型

        // 落地配 id
        this.did = ''

        window.queryBtPlanInfo = function() {}

        this.hideJCItem()
        this.hideJCBtn()
        this.init(opts.data)
        Event.fire({
            type: 'onJincaiChange',
            isSelected: false
        });
    }

    JinCai.prototype = {
        init: function(r) {
            this.bindEvent()
            this.get(r)
        },
        bindEvent: function() {
            var _this = this;

            /// 加入购物车
            function addToCart(params, onSuccess, onError) {
                return $.ajax({
                    url: "//cart.jd.com/gate.action",
                    data: $.extend({}, params),
                    dataType: 'jsonp',
                    success: function() {
                        if (typeof onSuccess === 'function') {
                            onSuccess.apply(null, arguments);
                        }
                    },
                    error: function() {
                        if (typeof onError === 'function') {
                            onError.apply(null, arguments);
                        }
                    }
            });
            }

            /// 收集加车服务的请求参数
            function collectQueryParams() {
                var params = {
                    // btg: 1,
                    f: 3,
                    payType: _this.payType || '',
                    pid: _this.sku,
                    pcount: _this.getNum(),
                    ptype : 1,
                    did: _this.did
                };

                if (_this.giftType) {
                    params.giftPoolType = _this.giftType;
                }
                
                if (_this.giftParam) {
                    params.gids = _this.giftParam;
                }

                return params;
            }

            /// 处理加车动作
            function handleAddToCart() {
                // if (!verifyGiftPoolInfo()) {
                //     return;
                // }

                addToCart(collectQueryParams()).
                    done(function(res){
                        var res = res || {};
                        if (res.flag) {
                            // 加车成功跳转到结算页
                            window.location.href = "//trade.jd.com/shopping/order/getOrderInfo.action?rid=" + (+new Date());
                        } else {
                            // 加车失败
                            $("body").dialog({
                                title: "提示", 
                                source: "<div style='color:#e4393c'>当前操作用户太多，请稍后再试...</div>",
                                width: 350, 
                                autoCloseTime:3
                            });
                        }
                    }).
                    fail(function(){
                        log("金采支付调用加车服务失败~")
                    });
            }

            this.$jc.undelegate('click').delegate('.J-jincai-list .item', 'click', $.proxy(this.handleJC, this))


            this.$jcBtn.unbind("click").bind('click', function(event){
                event.preventDefault();
                login({
                    modal: true,
                    complete: function() {
                        handleAddToCart();
                    }
                });
            });

        },
        log: function(msg) {
            if (typeof errortracker !== 'undefined') {
                errortracker.log({ filename: 'reservation.js', message: msg })
            }
        },
        getNum: function() {
            var amount = $('#buy-num').val()
            var num = Number(amount)
            return isNaN(num) ? 1 : num
        },
        get: function(r) {
            var _this = this
            if (r && r.jcUserInfo ) {
                _this.set(r.jcUserInfo)
            } else {
                G.log(null, 'JinCai.js', 'JinCai service error.')
            }
        },
        set: function(data) {
            if (data && data.showFlag) {
                this.$jc.show()
                if(data && data.opened){
                    this.payType = data.payType
                    this.$jc.find(".J-jincai-list").html('<div class="item"><a href="#none"><div>不使用</div><div>常规付款</div></a></div>\
                        <div class="item"><a href="#none"><div>使用金采支付</div><div>0元下单 先票后款</div></a>\
                        </div><div class="bt-info-tips"><a class="J-bt-tips question icon fl" href="#none">　</a></div>')
                    $('.J-jincai-list .item').eq(0).addClass('selected')
                    // this.setJinCai()
                }else{
                    this.$jc.find(".J-jincai-list").html('<div class="item1"><a target="_blank" href="'+data.pcSMBAccessUrl+'">'+data.activityName+'</a></div>')
                }
               
            }
        },
        // setJinCai: function () {
        //     this.$jc.find('.J-bt-tips').ETooltips({
        //         close: false,
        //         content: '<strong>什么是企业金釆？</strong>企业金釆是为优质企业客户推出的一款“先采购、后付款”的信用支付产品。企业金釆客户可享受“周结21天免息”或“月结低息分期”的延期付款体验。',
        //         width: 300,
        //         pos: 'bottom',
        //         zIndex: 10
        //     })
        // },
        hideJCItem: function () {
            this.$jc.hide()
            this.$jc.find('.item').removeClass('selected')
        },
        showJCBtn: function () {
            this.$jcBtn.show()
        },
        handleJC: function (e) {
            var $this = $(e.currentTarget)
            var isSelected 
            if ($this.hasClass('selected')) return
            $this.toggleClass('selected')
            $this.siblings().toggleClass('selected')
            if ($this.text().includes('不使用')) {
                isSelected = false
            } else {
                isSelected = true
            }
            
            this.cfg.isJinCaiSelected = isSelected

            if (isSelected) {
                this.showJCBtn()
                $("#InitTradeUrl").hide()
            } else {
                this.hideJCBtn()
                var carHref = $("#InitCartUrl").attr("href")// 获取加入购物车原链接地址
                var regex = /cart\.jd\.com\/gate\.action/;// 匹配原主流程加车链接
                var carName = $("#InitCartUrl").html()// 获取加入购物车名称
                if(regex.test(carHref) && carName == "加入购物车" && !$("#InitCartUrl").hasClass("btn-disable")){// 全流程标识和国补标签
                    $("#InitTradeUrl").show()
                }
            }
            Event.fire({
                type: 'onJincaiChange',
                isSelected: isSelected
            });
        },
        hideJCBtn: function () {
            this.$jcBtn.hide()
        },
    }

    function init(cfg) {
        var $jinCai = $('#choose-jincai')
        if (!$jinCai.length) return false
        Event.addListener('onStockReady', function(data) {
            // if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && cfg.skuMarkJson && cfg.skuMarkJson.pg) return
            var p = data.stock && data.stock.data
            cfg.jinCaiFenQi = new JinCai({
                $el: $jinCai,
                sku: cfg.skuid,
                data: p,
                price: p.price,
                cat: cfg.cat,
                cfg: cfg
            })
        }) 
    }

    module.exports.__id = 'jincai'
    module.exports.init = init
})
