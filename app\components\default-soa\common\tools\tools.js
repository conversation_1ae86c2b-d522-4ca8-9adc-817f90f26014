define('MOD_ROOT/common/tools/tools', function(require, exports, module) {
    //var CellPhone = require('MOD_ROOT/buytype/buytype').CellPhone;
    var Conf = require('PUBLIC_ROOT/conf');
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var exposure = require('MOD_ROOT/common/tools/exposure').exposure
    var landmine = require('MOD_ROOT/common/tools/exposure').landmine
    var initExposureData = require('MOD_ROOT/common/tools/exposure').initExposureData
    //pageConfig.product && pageConfig.product.floatLayerJs
    var pc_sku_layers = require('//storage.jd.com/retail-mall/pc_sku_layers/0.0.2/layer.1bb31224.js') // 新主图渲染组件
    
    var customtabid  = decodeURI(G.serializeUrl(location.href).param.purchasetab)// url上区分单品和批量


    var getAreaId = function() {
        var ipLoc = readCookie('ipLoc-djd')
        if (readCookie('ipLoc-djd') == '0-0-0-0.0'){
            ipLoc = ""
        }// 无地址的时候去结算，结算会种一个0-0-0-0的cookie
        var result = {
            areaIds: [1, 72, 55653, 0],
            commonAreaId: null
        }

        // 27-2442-2444-31910.138262226
        if (ipLoc) {
            var idx = ipLoc.indexOf('.')
            if (idx > -1) {
                result.commonAreaId = Number(ipLoc.substr(idx + 1))
                ipLoc = ipLoc.substring(0, idx)
            }

            result.areaIds = ipLoc.split('-')
            for (var i = 0; i < result.areaIds.length; i++) {
                result.areaIds[i] = Number(result.areaIds[i])
            }
        }

        return result
    }

    /**
    * 获得数字价格
     JS:
     tools.priceNum({
          skus: [skuid1,skuid2,skuid3,skuid4], // sku数组
          $el: $('body')
      });
     HTML:
     <strong class="J-p-skuid1"></strong>
     <strong class="J-p-skuid2"></strong>
     <strong class="J-p-skuid3"></strong>
     <strong class="J-p-skuid4"></strong>
    */
    var priceNum = function(opts) {
        var url = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            url = pageConfig.product && pageConfig.product.colorApiDomain
        }
        var ipLoc = readCookie('ipLoc-djd')

        var skus = opts.skus || []
        var loc = opts.loc || ipLoc
        var $el = opts.$el || $('body')
        var type = opts.type || 1
        var selector = opts.selector || '.J-p-'
        var text = opts.text || '￥{NUM}'
        var pdbp = opts.pdbp || 0
        var debug = opts.debug || false
        var callback = opts.callback || function() {}
        var onReady = opts.onReady || function() {}
        var priceData = opts.priceData || [];
        var isArray = function(obj) {
            return Object.prototype.toString.call(obj) === '[object Array]'
        }

        if (!isArray(skus)) {
            throw new Error('Please give skus param with Array type.')
        }
        if (skus.length < 1) {
            return false
        }

        if (!ipLoc) {
            loc = 1
        } else {
            loc = loc.replace(/-/g, '_')
        }

        if (debug) {
            console.info(
                url +
                    $.param({
                        type: type,
                        area: loc,
                        skuIds: 'J_' + skus.join(',J_')
                    })
            )
        }

        function getUid() {
            var __jda = readCookie('__jda')
            var uid = ''

            if (__jda && __jda.indexOf('.') > -1) {
                uid = __jda.split('.')[1]
            }

            return uid
        }
        if(priceData.length > 0){
            response(priceData)
            return false;
        }
        function response(r){
            var i, len

                if (debug) {
                    console.info(r)
                }

                if (r && r.length) {
                    len = r.length

                    for (i = 0; i < len; i++) {
                        if (!r[i].id) {
                            return false
                        }

                        var sku = r[i].id.replace('J_', '')
                        var jp = parseFloat(r[i].p)
                        var mp = parseFloat(r[i].m)

                        if (jp > 0) {
                            $el
                                .find(selector + sku)
                                .html(text.replace('{NUM}', r[i].p))
                        } else {
                            var str = "暂无报价";
                            if (Conf.get("BASE.PRICE.5G") &&
                                [0, 1, 1][G.specialAttrs["tsop"]]) {
                                str = "免费办理";
                            }
                            $el.find(selector + sku).html(str);
                        }

                        if (typeof callback === 'function') {
                            callback(sku, r[i])
                        }
                    }
                }

            onReady(r)
            
            // 店长推荐全部暂无报价时 屏蔽楼层
            var shopRecLi = $(".J-shopRec-content li");
            var shopRecNum = 0;
            if(shopRecLi.length > 0){
                for(i = 0;i < shopRecLi.length; i++){
                    if(shopRecLi.eq(i).find("strong").html() == "暂无报价")
                    {
                        shopRecNum++;
                    }
                }
                if(shopRecNum == shopRecLi.length){
                    $("#shopRecSuit").hide()
                }
            }
        }
        var paramJson = {
            area: loc,
            pin: readCookie('pin') || '',
            fields: '11100000',
            skuIds: skus.join(','),//原来有个J_的拼接，最好去掉
            source: 'pc-item'
        };
        var body = JSON.stringify(paramJson);
        var time = new Date().getTime()
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pctradesoa_getprice',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = getCookieNew("__jda") || '';
                        getPriceData(colorParm);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getPriceData(colorParm);
                    //烛龙上报
                    getJmfe(colorParm, e, "tools价格接口设备指纹异常",751)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getPriceData(colorParm);
            //烛龙上报
            getJmfe(colorParm, e, "tools价格接口加固异常",751)
        }            
        // 加固end
        function getPriceData(colorParm){
            $.ajax({
                url: url,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                },
                headers: getUrlSdx(),  
                success: function(r) {
                    if(r){
                        if(parseInt(r.code) < 10 && r.echo){
                            try {
                                var echoCode = r.echo.length>1000 ? r.echo.substring(0,999) : r.echo;
                                //烛龙上报
                                getJmfe(colorParm, echoCode, "tools价格接口成功异常",751)
                            } catch(e) {
                                console.log('上报pctradesoa_getprice错误',e)
                            }
                        }else{
                            response(r);
                        }
                    }
                },
                error: function (e) {
                    //烛龙上报
                    getJmfe(colorParm, e, "tools价格接口错误异常",751)
                }
            })
        }
    }

    var priceNumRecommend = function(opts) {
        var priceList = opts.priceList || []
        var $el = opts.$el || $('body')
        var selector = opts.selector || '.J-p2-'
        var text = opts.text || '￥{NUM}'
        var debug = opts.debug || false
        var callback = opts.callback || function() {}
        var onReady = opts.onReady || function() {}

        if(priceList.length > 0){
            response(priceList)
            return false;
        }
        function response(r){
            var i, len

                if (debug) {
                    console.info(r)
                }

                if (r && r.length) {
                    len = r.length

                    for (i = 0; i < len; i++) {
                        if (!r[i].id) {
                            return false
                        }

                        var sku = r[i].id.replace('J_', '')
                        var jp = parseFloat(r[i].p)
                        var mp = parseFloat(r[i].mp)

                        if (jp > 0) {
                            var priceDom = r[i].finalPrice && r[i].finalPrice.estimatedPrice
                            var titleDom = r[i].finalPrice && r[i].finalPrice.title
                            if(priceDom && titleDom){
                                $el
                                .find(selector + sku)
                                .html(text.replace('￥{NUM}', " ￥" + priceDom + " <span class='price-dsj'>到手价</span>"))
                            }else{
                                $el
                                .find(selector + sku)
                                .html(text.replace('￥{NUM}', r[i].p))
                            }
                           
                        } else {
                            var str = "暂无报价";
                            if (Conf.get("BASE.PRICE.5G") &&
                                [0, 1, 1][G.specialAttrs["tsop"]]) {
                                str = "免费办理";
                            }
                            $el.find(selector + sku).html(str);
                        }

                        if (typeof callback === 'function') {
                            callback(sku, r[i])
                        }
                    }
                }

            onReady(r)
        }
    }

    /**
    * 广告词
    * @param {Array}        skuid 数组
    * @param {$object}      包裹jQuery元素
    */
    var adWords = function(opts) {
        var loc = opts.loc || readCookie('ipLoc-djd') || '1_0_0'
        var skus = opts.skus || []
        var $el = opts.$el || $('body')
        var $target = opts.$target
        var selector = opts.selector || '.J-ad-'
        var debug = opts.debug || false
        var callback = opts.callback || function() {}

        $.ajax({
            url: '//ad.3.cn/ads/mgets',
            data: {
                skuids: 'AD_' + skus.join(',AD_'),
                areaCode: loc
            },
            dataType: 'jsonp',
            scriptCharset: 'utf-8',
            success: function(r) {
                var i = 0, len, ad, sku

                if (debug) {
                    console.log(r)
                }

                if (r && r.length > 0) {
                    len = r.length

                    for (i; i < len; i++) {
                        ad = r[i].ad
                        sku = r[i].id.replace('AD_', '')
                        $el.find(selector + sku).html(ad)
                        callback(sku, r[i])
                    }
                }
            }
        })
    }

    /**
    * 评价数据
    */
    var commentMeta = function(opts) {
        var cfg = (window &&
            window.pageConfig &&
            window.pageConfig.product) || {};
        var $el = opts.$el || $('body')
        // var skus = opts.skus || []
        // var selector = opts.selector || '.J-comm-'
        // var text = opts.text || '(已有{NUM}人评价)'
        // var debug = opts.debug || false
        // var callback = opts.callback || function() {}
        // var onlyData = opts.onlyData || false
        // var cIds = cfg.cat || ""

        // if (!$.isArray(skus)) {
        //     throw new Error('Please give skus param with Array type.')
        // }
        // // start
        // var time = new Date().getTime()
        // var bbtfVal = G.serializeUrl(location.href).param.bbtf
        // var paramJson = {
        //     // appid: 'item-v3',
        //     // functionId: 'pc_club_productCommentSummaries',
        //     // client: 'pc',
        //     // clientVersion: '1.0.0',
        //     // t: time,//生成当前时间毫秒数
        //     referenceIds: skus.join(","),
        //     categoryIds: cIds.join(","),
        //     // loginType: '3',
        //     bbtf: bbtfVal && bbtfVal.length > 0 ? "1" : "",
        //     shield: window.pageConfig.product.shield || "",
        //     // uuid: getCookieNew("__jda") || '',
        // }

        // var body = JSON.stringify(paramJson);
        // // 加固start
        // var colorParm = {
        //     appid: 'item-v3',
        //     functionId: 'pc_club_productCommentSummaries',
        //     client: 'pc',
        //     clientVersion: '1.0.0',
        //     t: time,//生成当前时间毫秒数
        //     body: body,
        // }
        // try{
        //     var colorParmSign =JSON.parse(JSON.stringify(colorParm))
        //     colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
        //     window.PSign.sign(colorParmSign).then(function(signedParams){
        //         colorParm['h5st']  = encodeURI(signedParams.h5st)
        //         try{
        //             getJsToken(function (res) {
        //                 if(res && res.jsToken){
        //                     colorParm['x-api-eid-token'] = res.jsToken;
        //                 }
        //                 colorParm['loginType'] = '3';
        //                 colorParm['uuid'] = getCookieNew("__jda") || '';
        //                 getCommentData(colorParm);
        //             }, 600);
        //         }catch(e){
        //             colorParm['loginType'] = '3';
        //             colorParm['uuid'] = '';
        //             getCommentData(colorParm);
        //             //烛龙上报
        //             getJmfe(colorParm, e, "tools评价数据设备指纹异常",751)
        //         }
        //     })
        // }catch(e){
        //     colorParm['loginType'] = '3';
        //     colorParm['uuid'] = '';
        //     getCommentData(colorParm);
        //     //烛龙上报
        //     getJmfe(colorParm, e, "tools评价数据加固异常",751)
        // }            
        // // 加固end

        // var host = '//api.m.jd.com'
        // if(pageConfig.product && pageConfig.product.colorApiDomain){
        //     host = pageConfig.product && pageConfig.product.colorApiDomain
        // }
        // // end
        // function getCommentData(colorParm) {
        //     $.ajax({
        //         url: host,
        //         data: colorParm,
        //         dataType: 'json',
        //         contentType: "application/json;charset=gbk",
        //         xhrFields: {
        //             withCredentials: true,
        //         }, 
        //         headers: getUrlSdx(),
        //         success: function(data) {
        //             var len, currItem
        //             if (data && data.CommentsCount.length) {
        //                 len = data.CommentsCount.length
    
        //                 for (var i = 0; i < len; i++) {
        //                     currItem = $el.find(
        //                         selector + data.CommentsCount[i].SkuId
        //                     )
        //                     if (!onlyData) {
        //                         currItem
        //                             .find('.star')
        //                             .removeClass('sa5')
        //                             .addClass(
        //                                 'sa' + data.CommentsCount[i].AverageScore
        //                             )
        //                             // console.log(" data.CommentsCount[i].CommentCountStr",data.CommentsCount[i].SkuId)
        //                         // currItem.html(
        //                         //     text.replace(
        //                         //         '{NUM}',
        //                         //         data.CommentsCount[i].CommentCountStr
        //                         //     )
        //                         // )
        //                     }
    
        //                     if (debug) {
        //                         console.log(currItem)
        //                     }
    
        //                     if (callback) {
        //                         callback(
        //                             data.CommentsCount[i].SkuId,
        //                             data.CommentsCount[i]
        //                         )
        //                     }
        //                 }
        //             }
        //         }
        //     })  
        // }
        
        try{
            // 监听评价返回数据
            itemEventBus && itemEventBus.once('commentChange', function(res) {
                var currItem  =  $el.find('.J-comm-' + cfg.skuid)
                var allCntStr = res && res.allCntStr
                currItem.html(allCntStr)
                Event.fire({ // 窄版时使用（评论更新之后可能一行展示不开）
                    type: 'commentChange',
                })
            });
            itemEventBus && itemEventBus.dispatchEmit('commentGet') // 防止评论接口在监听事件之前触发
        }catch(e){
            console.error("commentChange异常:",e)
        }
    }


    /**
    * 主图浮层
    */
    var getMainPic = function(width, height, domId, zIndex, csSku) {
       
        var skus = csSku || []

        if (skus.length > 0 && pageConfig.product  && pageConfig.product.floatLayerSwitch) { //有sku数据以及开关打开
            var skuIdArr = chunk(skus, 29);// 一个推荐位sku大于30个分批请求
            for(var i = 0; i < skuIdArr.length; i++){
                var skuArr = []
                for (var m = 0; m < skuIdArr[i].length; m++) {
                    skuArr.push(skuIdArr[i][m])
                }
                var paramJson1 = {
                    // pin: readCookie('pin') || '',
                    // fields: '11100000',
                    area: getAreaIds().join('_'),
                    skuIds: skuArr.join(','),//原来有个J_的拼接，最好去掉
                    // source: 'pc-item',
                    canvasType: width == height ? 1 : 2 //长图和方图 1- 1:1 或者2- 3:4
                };
                getMainPicDraw(width, height, domId, zIndex, paramJson1)
            }
            
        }
        function chunk(arr, size) {
            var newArray = [];
            for(var i=0; i<arr.length; i=i+size){
                newArray.push(arr.slice(i,i+size));
            }
            return newArray;
        };
    }

    var getMainPicDraw = function(width, height, domId, zIndex, paramJson) {
        var url = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            url = pageConfig.product && pageConfig.product.colorApiDomain
        }
        var body = JSON.stringify(paramJson);
        var time = new Date().getTime()
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pctradesoa_getFloatLayer',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = getCookieNew("__jda") || '';
                        getFloatLayer(colorParm);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getFloatLayer(colorParm);
                    //烛龙上报
                    getJmfe(colorParm, e, "tools浮层接口设备指纹异常",752)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getFloatLayer(colorParm);
            //烛龙上报
            getJmfe(colorParm, e, "tools浮层接口加固异常",752)
        } 
        // 加固end
       
        function getFloatLayer(colorParm){
            $.ajax({
                url: url,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                headers: getUrlSdx(), 
                success: function(r) {
                    if(r){
                        if(parseInt(r.code) < 10 && r.echo){
                            try {
                                var echoCode = r.echo.length>1000 ? r.echo.substring(0,999) : r.echo;
                                 //烛龙上报
                                 getJmfe(colorParm, echoCode, "tools浮层接口成功异常",752)
                            } catch(e) {
                                console.log('上报pctradesoa_getFloatLayer错误',e)
                            }
                        }else{
                            response(r);
                        }
                    }
                },
                error: function (e) {
                    //烛龙上报
                    getJmfe(colorParm, e, "tools浮层接口错误异常",752)
                }
            })
        }

        function response(r){
            var layerList = r.layerList
            if(layerList.length > 0){
                getPcSkuLayers(layerList, width, height, domId, zIndex)
            } 
                
        } 
    }


    // 主图浮层共有方法
    var getPcSkuLayers = function(layerList, width, height, domId, zIndex) {
        try{
            pc_sku_layers.loadlayerCDN(layerList, function (data) {
                new pc_sku_layers.MSupernatant(data, {
                width: width,
                height: height,
                domId: domId,
                // isIdxSwitch: true,
                zIndex: zIndex,
                });
            });            
        }catch(e){
            console.log("主图浮层共有方法", e)
        }
        
    }

    // 获取公共Cookie方法
    var getCookieNew = function(name) {
        var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
        if (arr != null) return arr[2];
        return null;
    }

    // 手动触发后加载
    var triggerLazyImg = function(ele, attr) {
        var trigger = attr || 'data-src'
        var img = ele.find('img[' + trigger + ']')

        if (img.length) {
            img.each(function(index) {
                $(this).attr('src', $(this).attr(trigger)).removeAttr(trigger)
            })
        }
    }

    // 获取未被隐藏的元素数量
    var getVisibleElementsCount = function() {
        // 获取所有 DOM 元素
        var allElements = document.getElementsByClassName("btn-lg")
        
        // 过滤出所有可见的元素
        var visibleElements = Array.from(allElements).filter(function(element) {
            var style = window.getComputedStyle(element);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
        });
      
        // 返回可见的元素数量
        return visibleElements.length;
    }

    // 重新分组json数据
    var reBuildJSON = function(data, itemCountPerPage) {
        var totalPage = data.length / itemCountPerPage
        var resData = []

        for (var i = 0; i < totalPage; i++) {
            resData.push({
                tabs: [],
                increment: null,
                count: itemCountPerPage,
                skuids: []
            })
        }

        var m = 0
        for (var k = 0; k < data.length; k++) {
            if (k % itemCountPerPage == 0) {
                m++
            }

            resData[m - 1]['tabs'].push(data[k])
            resData[m - 1]['increment'] = m
            if (data[k].wid) {
                resData[m - 1]['skuids'].push(data[k].wid)
            }
        }

        return resData
    }

    var getAreaIds = function() {
        var ipLoc = readCookie('ipLoc-djd')
        return ipLoc ? ipLoc.split('-') : [1, 72, 55653, 0]
    }

    // 获取url参数sdx(神盾放开拦截PC商家行为)
    var getUrlSdx = function() {
        var hData = {}
        try {
            if (/sdx=/.test(location.href)) {
                var xSdx =  G.serializeUrl(location.href).param.sdx
                hData = xSdx ? {"x-sdx":  xSdx} : {} 
            }
        } catch(e) {
        }
        return hData
    }

    // 获取url参数放在headers里面
    var getUrlHeaders = function() {
        var hData = {}
        var xSdx = ""
        var soaRate = ""
        try {
            if (/sdx=/.test(location.href)) {
                xSdx =  G.serializeUrl(location.href).param.sdx
            }
            if (/soaRate=/.test(location.href)) {
                soaRate =  G.serializeUrl(location.href).param.soaRate
            }
            hData = {"x-sdx": xSdx, "soaRate": soaRate}
        } catch(e) {
        }
        return filterObject(hData)
    }

    var filterObject = function(originalObj){  // 过滤一个对象里面没有值的字段并返回过滤后的对象
        try {
            var filteredObj = {}; 
            $.each(originalObj, function(key, value) 
            { 
                if (value) { 
                    filteredObj[key] = value; 
                } 
            });
            return filteredObj; 
        } catch(e) {
            return originalObj; 
        }
    } 


    var getUUID = function() {
        var __jda = readCookie('__jda')
        var uid = ''

        if (__jda) {
            if (__jda.split('.')[1] == '-') {
                uid = -1
            } else {
                uid = __jda.split('.')[1]
            }
        } else {
            uid = -1
        }

        return uid
    }

    var checkLogin = function(cb) {
        cb = cb || function() {}
        var locname = window.location.hostname
        var locnameNum =  locname.split("item")[1] || ".jd.com"
        return $.ajax({
            url: '//passport'+locnameNum+'/loginservice.aspx?method=Login',
            dataType: 'jsonp',
            success: function(r) {
                if (!r || !r.Identity) {
                  window.loginStatus = false
                  console.warn('获取登录态异常')
                }
                if (r.Identity) {
                    cb(r.Identity)

                    window.loginStatus = !!r.Identity.IsAuthenticated
                }
            },
            error: function(r) {
                window.loginStatus = false
                console.warn('获取登录态异常error')
            }
        })
    }

    // 统一烛龙上报
    var getJmfe = function(colorParm, e, tip, code) {
        try {
            // jmfe.jsagentReport(
            //     jmfe.JSAGENT_EXCEPTION_TYPE.net, //固定值不变
            //     code || 200,  //固定值: 异常码
            //     tip + colorParm.functionId ,  // 异常信息
            //     {
            //         fid: colorParm.functionId , // 网关对应的functionid
            //         resp: JSON.stringify(e), 
            //         body: colorParm.body,
            //         colorParm: colorParm,
            //     }
            // )
            window.customPointEvent(
                'item_exceptin',
                { code: code, error_type_txt: tip },
                {
                  functionId: colorParm.functionId,
                  request: colorParm.body,
                  error_msg:  JSON.stringify(e), 
                }
            )
        } catch(er) {
            console.log('统一烛龙上报错误',er)
        }
    }

    // 统一加固五件套
    var getJsTokenSign = function(opts) {
        var callback = opts.render || function() {}
        var body = opts.body || {}
        var msg1 = opts.message1 || "设备指纹异常"
        var msg2 = opts.message2 || "加固异常"
        var functionId = opts.functionid || ""
        var appid = opts.appid || ""

        var time = new Date().getTime()
        // 加固start
        var colorParm = {
            appid: appid,
            functionId: functionId,
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = getCookieNew("__jda") || '';
                        callback(colorParm)
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    callback(colorParm)
                    //烛龙上报
                    getJmfe(colorParm, e, msg1, 250)
                }
                
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            callback(colorParm)
            //烛龙上报
            getJmfe(colorParm, e, msg2, 250)
        }            
        // 加固end
    }

    // 统一埋点上报
    var MClick = function(eventId, jsonParam, pageParam) {
        try {
          const click = new MPing.inputs.Click(eventId); // 构造click请求
          click.page_id = "https://item.jd.com/";
          click.json_param = jsonParam; // 参数可选
          click.page_param = pageParam;
          click.updateEventSeries(); // 更新事件串
          const mping = new MPing(); // 构造上报实例
          mping.send(click); // 上报click
        } catch (e) {
          console.log("mclick e", e);
        }
      }
    

    /**
     * 倒计时类库
     * @param {Number} milliseconds
     * @param {Function} callback
     * @param {[Number]} interval
     * @class Countdown
     */
    function Countdown(milliseconds, callback, interval) {
        if (typeof milliseconds === 'number') {
            this.milliseconds = milliseconds;
        } else {
            this.milliseconds = 0;
        }

        if (typeof callback === 'function') {
            this.callback = callback;
        } else {
            this.callback = function(){};
        }

        if (typeof inverval === 'number') {
            this.interval = interval;
        } else {
            this.interval = 1000;
        }
        this.start();
    }

    Countdown.prototype = {
        constructor: Countdown,
        /**
         * start
         * @memberof Countdown
         */
        start: function() {
            var that = this;
            var count = 0;
            var walk = function() {
                count++;
                var now = +new Date();
                var offset = now - (that.stime + count * that.interval);
                if (offset > 10000) {
                    var count2 = Math.floor(offset / that.interval);
                    count += count2;
                    that.milliseconds -= (count2 * that.interval);
                }
    
                var nextTime = that.interval - offset;
                if (nextTime < 0) {
                    nextTime = 0
                }
                that.callback.call(that, that.format(that.milliseconds / 1000));
                that.milliseconds -= that.interval;
    
                if (that.milliseconds < 0) {
                    clearTimeout(that.timer);
                } else {
                    that.timer = setTimeout(walk, nextTime);
                }
            };
    
            if (this.milliseconds >= 0) {
                this.stime = +new Date();
                this.timer = setTimeout(walk, this.interval);
            }
            return this;
        },

        /**
         * stop
         * @memberof Countdown
         */
        stop:function () {
            this.timer && clearTimeout(this.timer);
            return this;
        },

        /**
         * format 时间格式化
         * @param {Number} 秒
         * @returns {Object}
         * @memberof Counter
         */
        format: function(seconds) {
            seconds = (typeof seconds === 'number' && seconds >=0) ? seconds : 0;
            var days = Math.floor(seconds / 86400);
            var hours = Math.floor(seconds % 86400 / 3600);
            var minutes = Math.floor(seconds % 86400 % 3600 / 60);
            var sec = Math.floor(seconds % 86400 % 3600 % 60);
            return {
                d: days,
                h: hours,
                m: minutes,
                s: sec
            };
        }
    };

    /**
     * 函数节流方法
     * @param Function fn 延时调用函数
     * @param Number delay 延迟多长时间
     * @param Number atleast 至少多长时间触发一次
     * @return Function 延迟执行的方法
     */
    var throttle = function(fn, delay, atleast) {
        var timer = null
        var previous = null

        return function() {
            var now = +new Date()

            if (!previous) previous = now

            if (now - previous > atleast) {
                fn()
                // 重置上一次开始时间为本次结束时间
                previous = now
            } else {
                clearTimeout(timer)
                timer = setTimeout(function() {
                    fn()
                }, delay)
            }
        }
    }
    // 防抖方法
    var debounce = function(func, wait, immediate) {
    var timeout;
    return function() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}
    // 补零
    // prefix(3, 15)  => 015
    // prefix(3, 5)  => 005
    var prefix = function(num, val) {
        return (new Array(num).join('0') + val).slice(-num)
    }

    // 给 url 添加参数
    var addUrlParam = function(url, key, value, valRE) {
        valRE = valRE || '\\d*'
        var re = new RegExp(key + '=' + valRE, 'gi')
        var result = ''

        if (!value) return url

        if (url.indexOf(key + '=') > -1) {
            result = url.replace(re, key + '=' + value)
        } else {
            var spliter = url.indexOf('?') < 0 ? '?' : '&'
            result = url + spliter + key + '=' + value
        }
        return result
    }

    var modifier = {
        // 获取页面 URL
        itemUrl: function(skuid) {
            return skuid ? '//item.jd.com/' + skuid + '.html' : ''
        },
        // 获取图片分流地址 > http://cf.jd.com/pages/viewpage.action?pageId=61506220
        imgDomain: function(skuid) {
            var imgDomains = [10, 11, 12, 13, 14]
            var num = imgDomains[skuid % 5]
            if (skuid && num) {
                return '//img'+ num +'.360buyimg.com/'
            } else {
                return ''
            }
        }
    }

    ////////////////////条件触发器 BEGIN////////////////////////////

     /**
     * Creates an instance of ConditionTrigger.
     * @param {String} conditionMode 
     * @param {Object} receivers 
     * @param {Object} actions 
     * @memberof ConditionTrigger
     */
    function ConditionTrigger(conditionMode, receivers, actions) {
        this.mode = '';
        this.receivers = {};
        this.actions   = {
            failure: function(){},
            success: function(){}
        };
        this.setConditionMode(conditionMode);
        this.addReceiver(receivers);
        this.addAction(actions);
    }
    ConditionTrigger.prototype = {
        constructor: ConditionTrigger,
        /**
         * 
         * @param {String|Object} receiver
         * @param {Function} onTrue
         * @param {Function} onFalse
         * @param {Boolean} signal 
         * @param {Any} data
         * @returns {Object} an instance of ConditionTrigger 
         * @memberof ConditionTrigger
         */
        addReceiver: function(receiver, onTrue, onFalse, signal, data) {
            if (typeof receiver === 'string') {
                if (!this.receivers.hasOwnProperty(receiver)){
                    this.receivers[receiver] = {
                        onTrue:  onTrue,
                        onFalse: onFalse,
                        signal: !!signal,
                        data: data
                    };
                } else {
                    throw 'I had this receiver.';
                }
            } else if(({}).toString() === ({}).toString.call(receiver)) {
                for (var k in receiver) {
                    if (receiver.hasOwnProperty(k)) {
                        var value = receiver[k];
                        if (({}).toString() === ({}).toString.call(value)) {
                            this.addReceiver(k, value.onTrue, value.onFalse, !!value.signal, value.data);
                        } else {
                            throw 'I need a Map Structure.';
                        }
                    }
                }
            }
            return this;
        },
        /**
         * @param {Function} action
         * @param {Boolean} status
         * @returns {Object} an instance of ConditionTrigger 
         * @memberof ConditionTrigger
         */
        addAction: function(action, status){
            if (typeof action === 'function') {
                if (this.actions.hasOwnProperty(status)) {
                    this.actions[status] = action;
                } else {
                    throw 'I do\'t have this status.';
                }
            }  else if (({}).toString() === ({}).toString.call(action)) {
                for (var k in action) {
                    this.addAction(action[k], k);
                }
            }
            return this;
        },
        
        callReceiverAction: function (receiver) {
            if (this.receivers.hasOwnProperty(receiver)) {
                var r = this.receivers[receiver];
                var data = r.data;
                var signal = r.signal;
                if (signal) {
                    if (typeof r.onTrue === 'function') {
                        r.onTrue(data);
                    }
                } else {
                    if (typeof r.onFalse === 'function') {
                        r.onFalse(data);
                    }
                }
            } else {
                throw 'I do\'t have this receiver.';
            }
        },

        callActions: function() {
            var values = [];
            var condition;
            for (var i in this.receivers) {
                if (this.receivers.hasOwnProperty(i)) {
                    values.push(+this.receivers[i].signal);
                }
            }
    
            if (this.mode === 'AND') {
                condition = (values.join('').indexOf(0) === -1);
            } else if(this.mode === 'OR') {
                condition = (values.join('').indexOf(1) !== -1);
            }
            
            if (condition) {
                this.actions.success();
            } else {
                this.actions.failure();
            }
        },
        /**
         * 
         * @param {String} receiver 
         * @param {Boolean} signal 
         * @param {Any} data
         * @memberof ConditionTrigger
         */
        emit: function(receiver, signal, data) {
            if (this.receivers.hasOwnProperty(receiver)){
                this.receivers[receiver].signal = !!signal;
                this.receivers[receiver].data = data;
                this.callReceiverAction(receiver);
                this.callActions();
            } else {
                throw 'I do\'t have this receiver';
            }
        },
        setConditionMode: function(mode) {
            if (typeof mode === 'string') {
                mode = mode.toUpperCase();
                if (mode === 'AND') {
                    this.mode = 'AND'
                } else if (mode === "OR") {
                    this.mode = 'OR';
                } else {
                    throw 'Invalid string.';
                }
            } else {
                throw 'I need a string, it only can be "AND" or "OR".';
            }
        }
    };
    ConditionTrigger.globalConditionTrigger = new ConditionTrigger('OR');
    ////////////////////条件触发器 END////////////////////////////

    /**
     * getStockV2
     * @param [Object] params 
     * @param [Function] onSuccess 
     * @param [Function] onError 
     */
    function getStockV2(params, onSuccess, onError) {
        var cfg = (window &&
            window.pageConfig &&
            window.pageConfig.product) || {};

        var attrs = cfg.specialAttrs || {};
        var areaIds = getAreaId().areaIds;

        /// 构建请求参数
        var defaults = {
            skuId: cfg.skuid || "",
            area: areaIds.join('_'),
            shopId: cfg.shopId,
            venderId: cfg.venderId || 0,
            buyNum: $("#buy-num").val() || 1,
            choseSuitSkuIds: cfg.suitSkuids || "",

            cat: cfg.cat instanceof Array ?
                 cfg.cat.join(',') : "",
                
            extraParam: cfg.isHeYue ?
                '{"originid":"1","heYueJi":"1"}' :
                '{"originid":"1"}',

            fqsp: ($.inArray("fqsp-1", attrs) != -1 && 1) ||
                  ($.inArray("fqsp-2", attrs) != -1 && 2) ||
                  ($.inArray("fqsp-3", attrs) != -1 && 3) || 0,

            pdpin: readCookie('pin') || "",
            pduid: getUUID(),
            ch: 1,
            ///
            isCanUseDQ: $.inArray("isCanUseDQ-1", attrs) != -1 ?
                1 : ($.inArray("isCanUseDQ-0", attrs) != -1 ? 0 : 1),
            isCanUseJQ: $.inArray("isCanUseJQ-1", attrs) != -1 ?
                1 : ($.inArray("isCanUseJQ-0", attrs) != -1 ? 0 : 1),
            platform: 0,
            orgType: 2,
            jdPrice: '',
            appid:1
        };

        if (cfg.locStoreId) {
            defaults["storeId"] = cfg.locStoreId;
        }
        
        var coord = readCookie('detailedAdd_coord');
        if (coord) {
            defaults["coord"] = coord;
        }

        var detailedAdd = readCookie('detailedAdd');
        if (detailedAdd) {
            defaults["detailedAdd"] = detailedAdd;
        }

        /// `detailedAdd_areaid`cookie在重新选择地址不会清除， `detailedAdd`cookie在重新选择地址会清除;
        /// 故采取双重校验以免出重新选择了地址后还使用常用地址的areaid
        var detailedAddAreaId = readCookie('detailedAdd_areaid');
        if (detailedAddAreaId && detailedAdd) {
            defaults["area"] = detailedAddAreaId.split('-').join('_');
        }
        
        return $.ajax({
            url: "//cd.jd.com/stock/v2",
            data: $.extend(defaults, params),
            dataType: "jsonp",
            scriptCharset: 'gb18030',
            success: function () {
                if (typeof onSuccess === 'function') {
                    onSuccess.apply(null, arguments);
                }
            },
            error:function(){
                if (typeof onError === 'function') {
                    onError.apply(null, arguments);
                }
            }
        });
    }
    //tab切换公共样式
    function TabSwitch() {
        //大于6个的时候切换按钮
        var step = G.wideVersion ? 6 : 3;

        function initScrollTab() {
            // var step = G.wideVersion ? 6 : 3;
            // var step = 6;

            $('#common-plan').imgScroll({
                width: 219,
                height: 48,
                visible: 3,
                direction: 'x',
                showControl: true,
                step: 2,
                loop: false,
                prev: '#pre',
                next: '#next'
            })


        }


        //css为block数组
        var arr = []
        //css为block索引数组
        var arrs = []
        //曝光埋点数组
        var List = []
        //遍历tab 如果 style为undefined 则设置为none 如果 class里面有curr或者为block放到arr数组里面
        var itemsArray = $('.purchase-tab').map(function(index, element) {
            if ($(element).attr("style") === "undefined") {
                $(element).css('display','none')
            }
            if ($(element).attr('class').indexOf('curr') > -1 || $(element).css('display') === 'block') {
                arr.push(index)
            }

            return $(element);
        }).get()


        //如果 遍历出来的css里面包含block贼把索引放在arrs数组里面 List为埋点曝光需要参数
        $('.purchase-tab').map(function(index, element) {
            if ( $(element).css('display') === 'block') {
                arrs.push(index)
                List.push({tabName:$(element)[0].innerText,tabIndex:index})
            }
        }).get()

        // console.log(List,'132913291329')


        //arrs的长度为0 就返回false
        if(arrs && arrs.length == 0){ // 没有tab切换按钮展示不走下面逻辑容易报错
            return false
        }

        //arrs的长度如果大于3 则显示左右箭头
        if (arrs && arrs.length > 3) {
            initScrollTab()
            $('.pre').addClass('preFlex')
            $('.pre').removeClass('hide')
            $('.next').addClass('preFlex')
            $('.next').removeClass('hide')
        }
        //获取url上面是否有上次选择的tab名 如果有则取 没有则默认取数组第一个的data-type
        var test
        if (customtabid === 'undefined') {
            test = itemsArray[arrs && arrs[0]].attr("data-type")
        } else {
            test =  customtabid.slice(0,4)
        }

        $('.purchase-tab').each(function(index,item){
            //设置 num 得到下一个的索引
            var num = index + 1
            if (index + 1 === $('.purchase-tab').length) {
                num = index - 1
            }
            $('.common-plan').attr('id','common-plan')

            var element = document.getElementById("common-plan");

            //如果 block的数组的长度小于2 则把唯一的block的css也设置为none
            if (arrs && arrs.length < 2) {
                element.style.display = 'none'
                itemsArray[0].css('display','none')
            }else {

                if($(item).attr('class').indexOf(test) > -1){
                    //先判断那个class里面包含test的值则哪个先高亮
                    $(item).addClass('curr')
                    //获取到当前的display的值
                    var itemsArrayCss = $('.purchase-tab').map(function(index, element) {
                        return $(element).css('display');
                    }).get()
                    //根据索引+1来判断当前是第几个
                    var count = itemsArrayCss.reduce(function(accumulator, currentValue) {
                        return currentValue === 'block' ? accumulator + 1 : accumulator;
                    }, 0);
                    if ($(item).attr('class').indexOf('hide') > -1 || $(item).attr('class').indexOf('hide') === -1) {
                        $(item).removeClass('hide')
                    }
                    //判断是否是最左侧背景图
                    if ( arrs && arrs[0] === index && arrs &&  arrs.length > 1) {

                        $(item).addClass('curr-left')
                    } else if ( index !== 0) {
                        //只有下一个为none或者是最后一个的时候才走下面样式不然 就是中间左右竖线的背景图
                        if (itemsArray[num].css('display') === 'none' || index === itemsArray.length - 1 ) {
                            //如果 数组里面当前的下一个dom的display为none并且自己刚好是第二个的时候 显示 长条样式 否则显示短条样式
                            if (itemsArray[num].css('display') === 'none' && count === 2) {
                                $(item).addClass('curr-right-one')
                                $(item).attr('id','curr-right')
                            }else {
                                $(item).addClass('curr-right')
                                $(item).attr('id','curr-right')
                            }
                        }else {
                            $(item).addClass('curr-content')
                        }
                    }
                }
            }

        })

        if($('.common-plan').is(':visible'))
        {
            $(".infomation").css({"border-top-left-radius":"0px","border-top-right-radius":"0px"}) // 主图往上移动8px
        }

        if($('#common_banner').is(':visible'))
        {
            $(".common-plan").css("margin-top","-8px")// tab往上移动8px
        }

        try {
            exposure({
                functionName: 'PC_Productdetail_PurchaseMethodTab_Expo',
                exposureData: ['mainskuid'],
                extraData: {
                    TabList:List
                },
                errorTips: '购买方式楼层-tab曝光-异常'
            })
        }catch (e){
            console.log(e)
        }

    }
    //服务展开全部
    function ServiceOpen(itemCat,arr) {

        $('#btn-open-div').click(function() {
            var _this = $('#btn-open')
            if (arr.length > 3) {

                if (_this.hasClass("opens")) {
                    itemCat.css({'height': $(arr[3])[0].offsetTop,'overflow': 'hidden'});
                    _this.text('展开全部');
                    _this.removeClass('opens')
                    $('.open-opens').removeClass('bt-open-span-rotate')
                }else {
                    _this.text('收起全部');
                    // element.classList.remove('bt-dd');
                    // element.classList.add('bt-dd-hidden')
                    itemCat.css('height', 'auto');
                    _this.addClass('opens')
                    $('.open-opens').addClass('bt-open-span-rotate')
                }
            } else {
                _this.css('display', 'none')
                $('.open-opens').css('display','none')
            }

        })
    }

    //隐藏白条按钮需要看看立即展示逻辑
    function showTradeUrl(_this){
        var carHref = $("#InitCartUrl").attr("href")// 获取加入购物车原链接地址
        var regex = /cart\.jd\.com\/gate\.action/;// 匹配原主流程加车链接
        var carName = $("#InitCartUrl").html()// 获取加入购物车名称
        if(regex.test(carHref) && carName == "加入购物车" && !$("#InitCartUrl").hasClass("btn-disable")){
            if(!$('#btn-jincai').is(':visible'))// 金采支付按钮展示的时候不展示立即购买
            {
                $("#InitTradeUrl").show()
            }
           
        }
    }
    // 显示服务副标题（提示）
    function showServiceTips($item, $ybItem) {
        var serviceSubTitle = $item.find('.name-tip').html() // 获取服务副标题
        var tip = $ybItem.find('.name-tip') // 获取服务副标题容器
        var sku = $item.data('sku') // 获取服务的sku
        if (sku) {
            serviceSubTitle = serviceSubTitle || '什么是' + $item.find('.name').html()
            tip.html('<a target="_blank" href="//item.jd.com/' + sku + '.html">' + serviceSubTitle + '</a>')
        } else {
            serviceSubTitle = serviceSubTitle || '自营品质，心意服务'
            tip.html(serviceSubTitle)
        }
        tip.attr('title', serviceSubTitle)
        // $ybItem.find('.price').html($item.find('.price').html())
        $ybItem.attr('data-sku', $item.attr('data-sku'))
    }
    //点击选项的时候通用
    function ClickMoreItem(moreItem, _this) {
        var $this = moreItem
        var $thisLi = $('.choose-btn').parents('li')
        var sku = $thisLi.attr('data-sku')
        var $catEl = $this.parents('.yb-item-cat')

        if ($this.hasClass('selected')) {
            $thisLi.removeClass('selected')
            // $catEl.removeClass('selected hover')
            $catEl.find('.yb-item').removeAttr('data-sku')
            $this.removeClass('selected')
            $catEl.find('.name-tip').css('display', 'none');
            // $('.name-tip').text('');
            $('.a_test').css('background','')
        } else {
            // $catEl.find('li').removeClass('selected')
            // $thisLi.addClass('selected')
            $catEl.addClass('selected').removeClass('hover')
            $this.addClass('selected').siblings().removeClass("selected")
            showServiceTips($this, $catEl.find('.yb-item'))
            $catEl.find('.name-tip').css('display', 'block');
            $('.choose-btn').find('.name-tip').css('display', 'none')
            $('.a_test').css('background','#f7f8fc')
        }

        _this.currSku = sku
        _this.currEl = $this
        _this.calResult()
        return false
    }

    //其他按钮的曝光埋点
    function otherBtnExpo(buttonName, serviceskuid) {
        var serviceskuid = serviceskuid.length ? serviceskuid : [-100]
        try{
            // 曝光
            exposure({
                functionName: 'PC_Productdetail_OtherBtn_Expo',
                exposureData: ['mainskuid'],
                extraData: {
                    "buttonName": buttonName || "",
                    "serviceskuid": serviceskuid
                },
                errorTips: '其他底部决策按钮曝光报错'
            })
        } catch (error) {
            console.log("PC_Productdetail_OtherBtn_Expo报错",error)
        }
    }
    function otherBtnClick(buttonName, serviceskuid) {
        var serviceskuid = serviceskuid.length ? serviceskuid : [-100]
        try{
            // 埋点
            landmine({
                functionName: 'PC_Productdetail_OtherBtn_Click',
                exposureData: ['mainskuid'],
                extraData: {
                    "buttonName": buttonName || "",
                    "serviceskuid": serviceskuid,
                    addcart_num: $('#buy-num').val() || "1",
                },
                errorTips: '其他底部决策按钮埋点报错'
            })
        } catch (error) {
            console.log("PC_Productdetail_OtherBtn_Click报错",error)
        }
    }
    // 获取 ServiceSkuid
    function getServiceSkuid(warrantyInfo) {
        if (!warrantyInfo) return []
        var skuid = []
        var jd3csid = warrantyInfo.originalFactoryServiceVo // 京选服务
        var ybId = warrantyInfo.serviceItems // 增值保障
        if (jd3csid) {
            skuid.push({
                serviceType: 'jd3csid',
                serviceskuid: jd3csid.serviceItems.map(function(item) {
                    return item.products.map(function(prod) {
                        return prod.serviceSku
                    }).join(',')
                }).join(',')
            })
        }
        if (ybId && ybId.length) {
            skuid.push({
                serviceType: 'ybId',
                serviceskuid: ybId.map(function(item) {
                    return item.products.map(function(prod) {
                        return prod.platformPid
                    }).join(',')
                }).join(',')
            })
        }
        return skuid
    }
    function getParamsFromUrl(key) {
        if ($("#InitCartUrl").length) {
            return G.serializeUrl($("#InitCartUrl").attr("href")).param[key]
        }
    }
    // 获取选中的 ServiceSkuid
    function getSelectedSkuid() {
        var skuid = []
        var keyList = ['did', 'jd3csid', 'ybId', 'jdhsid', 'customGiftInfoId', 'lsid']
        keyList.forEach(function(k) {
            var v = getParamsFromUrl(k)
            if (v) {
                skuid.push({
                    serviceType: k,
                    serviceskuid: v
                })
            }
        })
        return skuid
    }
    //鼠标悬浮tip方法
    function mouseTip(name, fun,title) {

        // console.log(name,fun)
        if (fun === 'mouseover') {
            // name.append("<div id='tool-title' class='tool-test'>'+ title +'</div>");
            if (title !== '') {
                name.append($('<div class=\'tool-test\'></div>').text(title));

                //visible
                // mousetip
                $('.tool-test').css({'visibility': 'visible',
                    'background':'#FFF',
                    'color':'#505259',
                    'position':'absolute',
                    'borderRadius':'8px',
                    'padding':'12px',
                    'top':'40px',
                    // 'left':'0',
                    'minHeight':'16px',
                    'boxShadow':'0px 4px 10px 0px rgba(0, 0, 0, 0.1)',
                    'fontSize':'14px','zIndex':'99'});
                // $(this).find('.title').attr('title','')
            }

        }else {
            //hidden
            $('.tool-test').remove()

        }
    }

    // 监听dom曝光并上报埋点
    function exposurePointObserve(domList, preloadHeight, attr, businessTag, featTag) {
      try {
        var observer = new IntersectionObserver(
          function (entries) {
            entries.forEach(function (entry) {
              if (entry.isIntersecting && entry.intersectionRatio >= 0.5) {
                // 目标元素出现在 root 可视区，返回 true
                var $target = entry.target
                if ($target && $target.getAttribute(attr)) {
                  expLogJSON(businessTag, featTag, JSON.parse($target.getAttribute(attr) || '{}'))
                }
                observer.unobserve($target) // 解除观察
              }
            })
          },
          {
            threshold: [0.5],
            // root: document.querySelector('#serviceList'),
            rootMargin: '0px 0px '+ preloadHeight +'px 0px'
          }
        )
        if (domList && domList.length) {
          domList.each(function (index,dom) {
            observer.observe(dom) // 开始观察
          })
        }
      } catch (e) {
        console.error('元素监听曝光异常', e)
      }
    }

    // 监听dom露出
    function observerElExpose(domList, cb) {
      try {
        if (!window.pageConfig.product.lazyLoadSwitch) {
          cb()
          return
        }
        var observer = new IntersectionObserver(
          function (entries) {
            entries.forEach(function (entry) {
              if (entry.isIntersecting) {
                observer.unobserve(entry.target) // 解除观察
                cb()
              }
            })
          }
        )
        if (domList && domList.length) {
          domList.each(function (index,dom) {
            observer.observe(dom) // 开始观察
          })
        }
      } catch (e) {
        console.error('元素监听曝光异常', e)
      }
    }

    // 判断当前元素下所有子元素是否都隐藏
    function areAllChildrenHidden(parentSelector) {
        var $parent = $(parentSelector);
        return $parent.children().length > 0 && $parent.children(':visible').length === 0;
    }
    // 加车失败显示弹窗提示
    function showAddCartFailDialog(skuId) {
        if(!skuId) skuId = typeof pageConfig != 'undefined' && pageConfig.product && pageConfig.product.skuid || ''
        
        var source = "<div style='text-align: center'><div style='color:#e4393c; margin-bottom: 16px'>当前商品可扫下方二维码，前往京东APP购买....</div>\
            <img src='https://qrimg.jd.com/https%3A%2F%2Fitem.m.jd.com%2Fproduct%2F"+skuId+".html%3Fpc_source%3Dpc_productDetail_"+skuId+"-118-1-4-2.png?ltype=0'/>\
        </div>";
        $("body").dialog({
            title: "提示", 
            source: source,
            width: 300, 
            // autoCloseTime:3
        });
    }   
    exports.getStockV2 = getStockV2;
    exports.ConditionTrigger = ConditionTrigger
    exports.commentMeta = commentMeta
    exports.getMainPic = getMainPic
    exports.getMainPicDraw = getMainPicDraw
    exports.getPcSkuLayers = getPcSkuLayers
    exports.getCookieNew = getCookieNew
    exports.priceNum = priceNum
    exports.priceNumRecommend = priceNumRecommend
    exports.adWords = adWords
    exports.triggerLazyImg = triggerLazyImg
    exports.getVisibleElementsCount = getVisibleElementsCount
    exports.reBuildJSON = reBuildJSON
    exports.getJmfe = getJmfe
    exports.getJsTokenSign = getJsTokenSign
    exports.MClick = MClick
    exports.getAreaIds = getAreaIds
    exports.getUrlSdx = getUrlSdx
    exports.getAreaId = getAreaId
    exports.checkLogin = checkLogin
    exports.getUUID = getUUID
    exports.filterObject = filterObject
    exports.getUrlHeaders = getUrlHeaders
    exports.Countdown = Countdown
    exports.throttle = throttle
    exports.debounce = debounce
    exports.prefix = prefix
    exports.addUrlParam = addUrlParam
    exports.modifier = modifier
    exports.exposure = exposure
    exports.landmine = landmine
    exports.initExposureData = initExposureData
    exports.TabSwitch = TabSwitch
    exports.ServiceOpen = ServiceOpen
    exports.showTradeUrl = showTradeUrl
    exports.ClickMoreItem = ClickMoreItem
    exports.otherBtnExpo = otherBtnExpo
    exports.otherBtnClick = otherBtnClick
    exports.getServiceSkuid = getServiceSkuid
    exports.getSelectedSkuid = getSelectedSkuid
    exports.mouseTip = mouseTip
    exports.exposurePointObserve = exposurePointObserve
    exports.observerElExpose = observerElExpose
    exports.areAllChildrenHidden = areAllChildrenHidden
    exports.showAddCartFailDialog = showAddCartFailDialog
})
