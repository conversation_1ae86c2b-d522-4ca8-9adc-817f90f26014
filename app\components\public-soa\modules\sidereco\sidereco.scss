@import '../common/lib';


// 排行
#rank {
    .p-price {
        font-size: 14px;
    }
}

// pop 热门
.pop-hot {
    .ETab .tab-main li {
        padding: 10px 22px;
    }
}

.plist-pop {
    li {
        //width: 150px;
        //height: 150px;
        //overflow: hidden;
        position: relative;
        margin-bottom: 10px;

        //.p-img a {
        //    display: block;
        //}
        //.p-name {
        //    position: absolute;
        //    bottom: 35px;
        //    height: 35px;
        //}
        .p-count {
            color: #999;
        }
        .p-num {
            position: absolute;
            left: 0px;
            top: 0px;
            width: 20px;
            height: 20px;
            line-height: 20px;
            border-radius: 10px;
            background-color: $colorPriceRed;
            color:#fff;
            text-align: center;
        }
    }
    padding: 10px 10px 0;
}

// 店内搜索，分类
.sp-search {
    .mc {
        padding: 8px;
    }
    p {
        overflow: hidden;
        *zoom: 1;
        label {
            display: inline-block;
            width: 5em;
            text-align: right;
        }
        span {
            display: inline-block;
        }
        padding: 2px;
        &.sp-form-item1 input {
            width: 104px;
            *width: 107px;
        }
        &.sp-form-item2 input {
            width: 3em;
        }
    }
}
.sp-form-item1 input,
.sp-form-item2 input,
.sp-form-item3 input { border:1px solid #ccc; padding:2px; }
.sp-form-item3 input {
    margin-left:-1px;
    background:#fff; color:#000; padding:3px 10px; border-radius:3px; cursor:pointer;
    /* IE10 Consumer Preview */
    background-image: -ms-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    /* Mozilla Firefox */
    background-image: -moz-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    /* Opera */
    background-image: -o-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    /* Webkit (Safari/Chrome 10) */
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #F7F7F7), color-stop(1, #F3F2F2));
    /* Webkit (Chrome 11+) */
    background-image: -webkit-linear-gradient(top, #F7F7F7 0%, #F3F2F2 100%);
    /* W3C Markup, IE10 Release Preview */
    background-image: linear-gradient(to bottom, #F7F7F7 0%, #F3F2F2 100%);

    *border:0;
    *padding:0;
    *width:48px;
    *height:24px;
    *line-height:1000px;
    *overflow:hidden;
    *background:#fff url(../css/i/sp-btn-search.png) 0 0 no-repeat;
}

/* .sp-category */
.sp-category {
    .sp-single {
        border-bottom: none;
    }
    dl a {
        display: block;
        height: 28px;
        line-height: 28px;
        text-decoration: none;
    }
    dd {
        display: none;
        a {
            height: 26px;
            line-height: 26px;
            white-space: nowrap;
        }
    }
    dt {
        border-top: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        position: relative;
        s {
            display: block;
            position: absolute;
            left: 10px;
            top: 6px;
            width: 16px;
            height: 16px;
            background-image: url(//misc.360buyimg.com/201007/skin/df/i/20130603A.png);
            background-repeat: no-repeat;
            background-position: -20px 0;
            cursor: pointer;
        }
    }
    .open {
        s {
            background-position: -37px 0;
        }
        dd {
            display: block;
        }
    }
    dt {
        border-bottom: none;
    }
    .open dt {
        border-bottom: 1px solid #ddd;
    }
    dt a {
        background: #f7f7f7;
        padding-left: 36px;
    }
    dd a {
        padding-left: 37px;
        &:hover {
            color: #e4393c;
            font-weight: bold;
        }
    }
    .mt {
        background: #f7f7f7;
    }
    .mc {
        margin-top: -1px;
    }
}
.m-aside{
    .mc{
        .plist li{
            padding: 5px 0;
            _zoom: 1;
        }
    }
}


