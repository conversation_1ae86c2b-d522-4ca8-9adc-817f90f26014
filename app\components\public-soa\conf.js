
define('PUBLIC_ROOT/conf', function(require, exports, module) {
    var conf = {

        data: {},

        /**
         * 获取配置数据
         * @param {String} path
         * @example 'a.b.c.d'
         * @returns {Any}
         */
        get: function(path) {
            if (typeof path === 'string') {
                var arr = path.split('.');
                var data = this.data;
                for (var i = 0, size = arr.length; i < size; i++) {
                    data = data[arr[i]];
                    if (data === void(0) || data ===null) {
                        return data;
                    }
                }
                return data;
            } else {
                return void(0);
            }
        },

        /**
         * 控制台打印方法
         */
        print: function(msg) {
            console && (typeof console.log === 'function') && console.log(msg)
        }
    };

    try {
        var source = document.getElementById('J_JSConfig').innerHTML;
        var data = eval('(' + source +')');
        if (data instanceof Object) {
            conf.data = data;
        } else {
            conf.data = {};
        }
    } catch (error) {
        conf.print(error);
    }

    return conf;
});

