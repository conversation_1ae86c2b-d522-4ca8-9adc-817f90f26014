@import "./__sprite.scss";
@import "lib";
.itemInfo-wrap {
    //全球购国旗icon和国家名称
    .country-sign {

        padding-bottom:12px;

        .country-map-icon {
            width:28px;
            height:28px;
        }

        .country-name {
            font-size:12px;
            color:#000;
        }
    }
    /*d-tip*/

.summary .active .tips-message {
    display: block
}
.summary-tallage {
    *position: relative;
    z-index: 6;
}
.d-tip {
    position: relative;
    display: inline-block;
    margin-left: 7px;
    color: #6286ca;
    z-index: 5;
    .tips {
        position: relative;
        top: 1px;
        z-index: 5;
        display: inline-block;
        margin-left: 5px;
        @include sprite-01-tips-icon;
        cursor: pointer;
    }
    .tips-message {
        display: none;
        position: absolute;
        top: -10px;
        left: 16px;
        width: 280px;
        line-height: 180%;
        padding: 8px 10px;
        border: 1px solid #eee;
        background: #fff;
        color: #595959;
        text-align: left;
        cursor: default;
        a {
            color: $colorLinkBlue;
        }
    }
    .icon-arrowL {
        position: absolute;
        left: -6px;
        top: 10px;
        @include sprite-02-arrowL-icon;
    }
    &.J-deliver-date {
        .tips-message {
            width: 126px;

        }
    }
}
}
.service-info {
    line-height: 20px;
    padding: 10px 0;

    .tips-list {
        float: left;
        width:350px;
        li {
            float: left;
            margin-right: 20px;
            color: #939393;
            position:relative;
            i{
                display: inline-block;
                margin-right: 5px;
                vertical-align: -2px;
            }
            img {
                width: 13px;
                height: 13px;
            }
            .tips {
                display: none;
                width: 270px;
                position: absolute;
                z-index:9;
                left: -110px;
                top: 25px;
                display: none;

                .content {
                    padding: 10px;
                    background: #fff;
                    border: 1px solid #cecbce;
                    color: #666;
                    -moz-box-shadow: 0 0 2px 2px #eee;
                    -webkit-box-shadow: 0 0 2px 2px #eee;
                    box-shadow: 0 0 2px 2px #eee;
                    dt{
                        font-weight: bold;
                        margin-bottom: 3px;
                    }
                    dd{
                        line-height: 170%;
                    }
                    p{
                        border-top: 1px dotted #999;
                        margin-top: 7px;
                        padding-top: 7px;
                        a{
                            color: #5e69ad;
                            margin: 0 5px;
                            &:hover{
                                color: #e4393c;
                            }
                        }
                    }
                }
                .sprite-arrow {
                    @include sprite-arrow;
                    position: absolute;
                    overflow: hidden;
                    left: 135px;
                    top: -5px;
                    _bottom: -1px;
                }
            }

            &.hover {
                .tips{
                    display: block;
                }
            }

        }
    }
    .tips-more{
        float: right;
        color: #939393;
        .view-more{
            color: #939393;
            &:hover {
                color:$red-base;
                .more-arrow {
                    &:after {
                        background-position: -6px -16px;
                    }
                }

            }
        }
        .more-arrow{
            display: inline-block;
            position: relative;
            width:6px;
            height:11px;
            margin-left: 6px;
            &:after {
                content:'';
                display:block;
                position:absolute;
                @include sprite-arrow-right;
                background-position: 0 -16px;
                width: 6px;
                height: 11px;
                vertical-align: 1px;
            }
        }



    }
}
.itemInfo-wrap {
    .summary-tips {
        border-top: 1px solid #eee;
        padding-top: 16px;

        .tips-list {
            float: left;
            li {
                float: left;
                margin-right: 20px;
                color: #939393;
                i{
                    display: inline-block;
                    margin-right: 5px;
                    vertical-align: -2px;
                }
                .sprite-01-nonsupport{
                    @include sprite-01-nonsupport;
                }
                .sprite-02-service{
                    @include sprite-02-service;
                }
            }
        }
        .tips-more{
            position: relative;
            top: -1px;
            float: right;
            color: #939393;
            border: 1px solid #fff;
            .view-more{
                position: relative;
                z-index: 3;
                display: inline-block;
                background: #fff;
                color: #939393;
                padding: 0 10px 10px;
            }
            .more-arrow{
                display: inline-block;
                margin-left: 6px;
                vertical-align: 1px;
                @include sprite-03-arrowD;
            }
            .more-con{
                display: none;
                position: absolute;
                z-index: 3;
                right: -1px;
                top: 36px;
                width: 320px;
                line-height: 190%;
                border: 1px solid #dadada;
                background: #fff;
                padding: 12px 20px;
                li{
                    padding: 2px 0;
                    list-style: decimal inside none;
                }
            }
            &:hover{
                border-color: #dadada;
                border-bottom: none;
                margin-top: -10px;
                .more-arrow{
                    @include sprite-04-arrowU;
                }
                .view-more{
                    padding: 10px;
                }
                .more-con{
                    display: block;
                }
            }
        }
    }
}
.notice-msg {
    padding: 15px;
    margin-top:10px;
    margin-bottom: 20px;
    background: #f8f8f8;
    color: #d91f20;
    strong {
        line-height: 16px;
    }
    s {
        @include inline-block;
        margin-right: 5px;
        vertical-align: middle;
        width: 16px;
        height: 16px;
        background:url(./i/notice.png) 0 0 no-repeat;
    }
}