define('MOD_ROOT/summary/summary', function(require, exports, module) {
    //var Area = require('MOD_ROOT/address/area')
    require('//static.360buyimg.com/item/assets/address/area');
    var Area =  common_getAreaMap();
    var Login = require('JDF_UNIT/login/1.0.0/login')
    var Tools = require('MOD_ROOT/common/tools/tools')
    var Event = require('MOD_ROOT/common/tools/event').Event
    var G = require('MOD_ROOT/common/core');
    var Stock = require('MOD_ROOT/address/stock');

    require('JDF_UI/dialog/1.0.0/dialog')

    // 赛式报名
    function competition(cfg) {
        var $chooseCompetition = $('#choose-competition')
        var $competition = $chooseCompetition.find('.J-dest a')
        var $count = $chooseCompetition.find('.J-count .dd')
        if (!cfg.isLOC3 || !$competition.length) return false

        function openIframe() {
            var url = '//cd.jd.com/acty?'
            var param = $.param({
                addressId: cfg.locMatch.addressId,
                coordinate: cfg.locMatch.coordinate,
                detailAddress: cfg.locMatch.detailAddress
            })

            param.skuId = cfg.skuid

            $('body').dialog({
                width: 900,
                height: 470,
                title: '地图',
                type: 'iframe',
                maskClose: true,
                autoIframe: false,
                source: url + param
            })
        }

        function getNum(cfg) {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host +'/acty/info?skuId=' + cfg.skuid,
                dataType: 'jsonp',
                data: {
                    appid: 'item-v3',
                    functionId: "pc_acty_info"   
                },
                success: function(r) {
                    if (r) {
                        $count.text(r.availableStockNum)
                        if (!r.availableStockNum) {
                            cfg.addToCartBtn.setDisable()
                        }
                    }
                }
            })
        }

        if (cfg.locMatchIsExpire !== 1) {
            setTimeout(function() {
                cfg.addToCartBtn.setDisable()
            }, 100)
        }
        $competition.click($.proxy(openIframe, this))
        getNum(cfg)
    }

    // LOC商品加购物车
    var ChooseLocShop = {
        flag: (function() {
            var cfg = window.pageConfig;
            var attrs = (cfg &&
                cfg.product &&
                cfg.product.specialAttrs) || [];

            var dict = {};
            for (var i = 0, length = attrs.length;
                i < length && typeof attrs[i] === "string"; i++) {
                var arr = attrs[i].split('-');
                dict[arr[0]] = arr[1] != void(0) ? arr[1] : '1';
            }

            // return dict.isLOC == "1" &&
            //     (dict.locfwlx == "1" ||
            //      dict.locfwlx == "1,2");
            return dict.isLOC == "1" &&
                (dict.locfwlx && dict.locfwlx.indexOf("1") !== -1);
        })(),
        init: function(cfg, $el) {
            this.$el = $el || $('#choose-thmd')
            // this.buyBtn = cfg.addToCartBtn
            this.cfg = cfg

            this.sid = 1
            this.cid = ''
            this.ccid = ''
            this.pid = ''
            this.selected = false

            if (!this.$el.length) return false

            this.$el.find('.item a').prepend('<i class="icon-o2o"></i>')
            this.text = this.$el.find('.dd .item a').text()
            this.bindEvent()

            return this
        },
        bindEvent: function() {
            this.$el.undelegate('click')

            this.$el.delegate(
                '.item,.J-mod',
                'click',
                $.proxy(this.openIframe, this)
            )

            if (!this.flag) {
                this.cfg.addToCartBtn.$el.bind(
                    'click',
                    $.proxy(this.handleBuyBtnClick, this)
                );
                $("#InitTradeUrl").bind(
                    'click',
                    $.proxy(this.handleBuyBtnClick, this)
                );
            }

            this.$el.delegate('.J-cancel', 'click', $.proxy(this.reset, this))

            Event.addListener('onNumChange', $.proxy(this.setBtnLink, this))
        },
        handleBuyBtnClick: function() {
            if (!this.selected) {
                // this.highLightSelect()
                this.showTips()
                return false
            }
        },
        highLightSelect: function() {
            this.$el.addClass('item-hl-bg')
        },
        normalSelect: function() {
            this.$el.removeClass('item-hl-bg')
        },
        getMapUrl: function(cfg, mod) {
            var ids = Tools.getAreaId().areaIds

            function getProvinceName() {
                var ipLocation = readCookie('ipLocation')
                return ipLocation ? ipLocation : '%u5317%u4EAC'
            }

            var param = {
                storeGroupId: cfg.storeGroupId || '',
                venderId: cfg.venderId,
                pName: escape(this.cfg._area.provinceName),
                isNarrow: (+(!(pageConfig.wideVersion && pageConfig.compatible))),
                provinceId: ids[0],
                cityId: ids[1],
                countyId: ids[2],
                shopId: this.sid,
                skuId: this.cfg.skuid
            }

            if (this.cfg.isZx && this.cfg.decorationCurrentCityIds) {
                param.pName = escape(Area.provinceMap[this.cfg.decorationCurrentCityIds[0]])
                param.provinceId = this.cfg.decorationCurrentCityIds[0]
                param.cityId = this.cfg.decorationCurrentCityIds[1]
            }

            if (mod) {
                param.modifyFlag = 'true'
                param.pid = this.pid
                param.cid = this.cid
                param.ccid = this.ccid
            }

            param.shouldHide = this.flag ? '1' : '0';

            // var url = '//cd.jd.com/store/locinfo?'
            var url = '//item.jd.com/loco2oshop?'

            // if (G.onAttr('isActualServ-1') || this.cfg.cat[0] === 6728) {
            //     url = '//cd.jd.com/loco2oshop?'
            // }

            return url + $.param(param)
        },
        openIframe: function(e) {
            var isModify = $(e.currentTarget).is('.J-mod')
            var url = this.getMapUrl(this.cfg, isModify)

            $('body').dialog({
                width: 900,
                height: 520,
                title: this.flag ? "所有适用门店" : '选择门店',
                type: 'iframe',
                autoIframe: false,
                iframeTimestamp: false,
                source: url,
                onReady: function() {
                    $('#dialogIframe').css('height', '100%')
                }
            })
        },
        /* iframe 回调函数
         if (window.parent) {
            window.parent.pageConfig.product.chooseLOCShop.setResult();
         }
         */
        setResult: function(sid, name, pid, cid, ccid) {
            sid = sid || ''
            pid = pid || ''
            cid = cid || 1
            ccid = ccid || 1
            name = name || ''

            this.selected = true

            this.sid = sid
            this.cid = cid
            this.ccid = ccid
            this.name = name
            this.pid = pid

            if (name.length > 25) {
                name = name.substr(0, 25) + '...'
            }

            this.$el.find('.item').addClass('selected')
            this.normalSelect()
            this.showOperate()
            this.hideTips()
            this.setContent(name)
            this.setBtnLink(sid);
            /// 设置预约看车的服务地址
            ;(function setBookingCarUrl(shopId) {
                var $btn = $("#bookingCarUrl");
                if (!shopId || $btn.length === 0) {
                    return;
                }
                var href = $btn.attr("href");
                if (href && typeof href === "string") {
                    if (href.indexOf("storeId=") === -1) {
                        href += "&storeId=";
                    }
                    href = href.replace(/(storeId=)[^&?#]*/, "$1" + shopId);
                } else {
                    href = "//carbrand.jd.com/pc/reservationCar.html?skuId=" +
                        pageConfig.product.skuId +
                        "&storeId=" + shopId;
                }
                $btn.attr("href", href);
            })(sid);
            // 重新调stock，更新京东价，白条等信息
            new Stock({storeId:sid}, function(r) {
                var _this = this;
                Event.fire({
                    type: 'onStockReady',
                    area: {
                        id: _this.areas
                    },
                    stock: r
                });
            });

        },
        setContent: function(name) {
            this.$el.find('.item a').html('<i class="icon-o2o"></i><span>' + name + '</span>')
        },
        setBtnLink: function(sid) {
            // cart.jd.com/gate.action?pid=loc商品id&pcount=数量&ptype=1&lsid=门店
            // var url = '//cart.jd.com/gate.action?'
            // var param = $.param({
            //     pid: this.cfg.skuid,
            //     pcount: $('#buy-num').val(),
            //     ptype: 1,
            //     lsid: sid,
            //     r: Math.random()
            // });

            var $cartButton = this.cfg.addToCartBtn.$el;
            if (!$cartButton.length) {
                return
            }
            var url = $cartButton[0].href;
            if (sid && !(typeof sid == 'object')) {
                url = G.modifyURL(url, {
                    query: {
                        pcount: $('#buy-num').val(),
                        lsid: sid
                    }
                });
            } else {
                url = G.modifyURL(url, {
                    query: {
                        pcount: $('#buy-num').val()
                    }
                });
            }
            this.selected = true;
            this.cfg.addToCartBtn.enabled(url);
        },
        showTips: function() {
            this.$el.find('.J-msg').show()
        },
        hideTips: function() {
            this.$el.find('.J-msg').hide()
        },
        showOperate: function() {
            this.$el.find('.J-operate').show()
        },
        hideOperate: function() {
            this.$el.find('.J-operate').hide()
        },
        reset: function() {
            this.selected = false
            this.$el.find('.item').removeClass('selected')
            this.setContent(this.text)
            this.hideOperate()
            this.hideTips()
        }
    }

    // 定期购
    window.PeriodicalBuy = {
        isBinded:false,
        init: function(cfg) {
            this.$el = $('#choose-period-buy')
            this.cfg = cfg





            //定期购,新增显示条件
            if (this.$el.length) {
                this.get()
                this.bindEvent()
            }
        },
        bindEvent: function() {
            if(this.isBinded) return;
            $(document).bind('click', $.proxy(this.handleCloseAll, this))
            this.$el.delegate('.period-more', 'click', function () {
                return false
            })
            this.$el.delegate('.item', 'click', $.proxy(this.handleClick, this))
            this.$el.find('.service-tips').hover(function () {
                $(this).addClass('hover')
            }, function () {
                $(this).removeClass('hover')
            })


            this.isBinded = true;
        },
        handleCloseAll: function (e) {
            if (!$(e.target).is('.period-inner')) {
                this.closeAll()
            }
        },
        closeAll: function () {
            this.$el.find('.item').removeClass('selected')
        },
        handleClick: function(e) {
            var _this = this

            Login({
                modal: true,
                complete: function(r) {
                    if (r && r.Identity && r.Identity.IsAuthenticated) {
                        _this.itemSelect($(e.currentTarget))
                    }
                }
            })
        },
        itemSelect: function($this) {
            var selected = $this.hasClass('selected')
            var pid = $this.attr('data-pid')
            var $items = this.$el.find('.item')
            var $currItem = $items.filter('[data-pid="'+ pid +'"]')

            this.closeAll()
            if (selected) {
                $this.removeClass('selected')
            } else {
                $this.addClass('selected')
                if (!$currItem.data('loaded')) {
                    this.openIframe($currItem)
                    $currItem.data('loaded', true)
                }
            }
        },
        openIframe: function($curr) {
            var $iframe = $curr.find('iframe')
            $iframe.attr('src', $iframe.attr('data-src'))
        },
        get: function() {
            // var url = '//www.mocky.io/v2/59103cab1100005106591949'
            var url = '//ding-server.jd.com/plan/queryFrequencies?skuId=' + this.cfg.skuid
            $.ajax({
                url: url,
                dataType: 'jsonp',
                success: $.proxy(this.set, this)
            })
        },
        set: function(r) {
            if (!r || r.code !== '200' || !r.result.data) return

            this.$el.show()
            this.renderItem(r.result.data)
            this.setDiscount(r.result.data.regularDiscount)
        },
        setDiscount: function(discount) {
            if (discount) {
                var str = (discount * 10) + ''
                this.$el.find('.J-discount').text(str.substr(0,3))
            }
        },
        renderItem: function(data) {
            var tpl = '\
            {for item in frequencyEnumList}\
            <div class="item" data-pid="${item.id}" clstag="shangpin|keycount|product|dingqigou_${item.value}">\
                <a href="#none" class="period-inner">${item.value}</a>\
                <div class="period-more">\
                    <iframe style="width:100%;height:100%" data-src="${item.url}" frameborder="0" scrolling="no"></iframe>\
                </div>\
            </div>\
            {/for}'

            this.$el.find('.J-period-wrap').html(tpl.process(data))
        }
    }

    function init(cfg) {
        competition(cfg)

        setTimeout(function() {
            cfg.chooseLOCShop = ChooseLocShop.init(cfg)
        }, 100)

    }

    module.exports.init = init
})
