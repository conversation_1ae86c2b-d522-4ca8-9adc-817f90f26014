@import '../common/pager';
@import '../common/lib';
@import './__sprite';

.askAnswer{
    @mixin askAnswer-icon {
        float: left;
        width: 18px;
        height: 18px;
        text-align: center;
        border-radius: 9px;
        font-size: 12px;
        color: #fff;
    }
    .icon-ask{
        @include askAnswer-icon;
        background: #ff9911;
    }
    .icon-answer{
        @include askAnswer-icon;
        background: #99bb11;
        margin-top: 15px;
    }
    .ask-wrap{
        height: 75px;
        text-align: center;
        font-size: 14px;
        color: #333;
        font-family: "microsoft yahei";
        background: #f9f9f9 url(i/radius-rect.png) no-repeat top center;
        .icon-dog{
            display: inline-block;
            width: 123px;
            height: 67px;
            margin-top: 8px;
            background: url(i/icon-dog.png) no-repeat;
            vertical-align: middle;
            margin-right: 15px;
        }
        .btn-ask{
            display: inline-block;
            line-height: 26px;
            padding: 0 18px;
            margin-left: 12px;
            border: 1px solid #e4393c;
            border-radius:13px;
            vertical-align: middle;
            color: #e4393c;
            &:hover {
                background: #e4393c;
                color: #FFF;
            }
        }
    }
    .askAnswer-list{
        
    }
    .askAnswer-item{
        padding-top: 18px;
        border-bottom: 1px solid #ddd;
        .ask{
            overflow: hidden;
            zoom: 1;
            p{
                width: 770px;
                //overflow:hidden;
                //text-overflow:ellipsis;
                //white-space:nowrap;
            }
        }
        .item-con{
            margin-left: 25px;
            font-size: 14px;
            font-family: "microsoft yahei";
            overflow: hidden;
            zoom: 1;
            p {
                float: left;
                color: #333;
            }
            span{
                float: right;
                color: #999;
            }
        }
        .ask{
            .item-con{
                p{
                    font-weight: bold;
                }
            }
        }
        .answer{
            p{
                width: 770px;
                padding: 10px 0 8px;
                line-height: 220%;
            }
            .item-con{
                line-height: 50px;
                .more{
                    line-height: 100%;
                    padding-bottom: 18px;
                    .retract {
                        display: none;
                    }
                    a{
                        color: #005ea7;
                        &:hover {
                            color: #e4393c;
                        }
                    }
                    .sprite-arrowUp{
                        display: inline-block;
                        @include sprite-arrowUp;
                        margin-left: 5px;
                        vertical-align: 1px;
                    }
                    .sprite-arrowDown{
                        display: inline-block;
                        @include sprite-arrowDown;
                        margin-left: 5px;
                        vertical-align: 1px;
                    }
                }
            }
        }
        .disabled{
            .icon-answer{
                background: #bababa;
            }
            .item-con{
                p{
                    color: #999;
                }
            } 
        }
        .answer-list {
            li {
                overflow: hidden;
                zoom: 1;
            }
            .more-answer {
                display: none;
            }
        }
        .unfold {
            margin-bottom: 18px;
            li{
                border-bottom: 1px solid #f4f4f4;
            }
            .more-answer {
                display: block;
            }
        }
    }
    .askAnswer-tips{
        padding-top: 13px;
        .sprite-tips{
            @include sprite-tips;
            float: left;
            margin-top: 2px;
        }
        p{
            color: #999;
            margin-left: 17px;
            line-height: 180%;
        }
    }
    .ui-page{
        float: right;
        margin-top: 30px;
    }
}
/*askAnswer layer*/
.askAnswer-layer{
    //width: 379px;
    .textarea-tips{
        color: #999;
    }
    .ask-textarea{
        width: 398px;
        height: 53px;
        margin-top: 5px;
        border: 1px solid #ddd;
        position: relative;
        textarea{
            width: 382px;
            height: 20px;
            padding: 8px;
            line-height: 150%;
            border: none;
            resize: none;
            color: #333;
            overflow: auto;
            outline: none;
        }
        span{
            .error {
                color: #E4393C;
            }
            color: #ccc;
            position: absolute;
            right: 4px;
            bottom: 1px;
            i{
                color: #e4393c;
            }
        }
        .textarea-error{
            color: #e4393c;
            position: absolute;
            left: 0;
            right: -20px;
            bottom: -25px;
            display: none;
        }
        .sprite-error{
            display: inline-block;
            vertical-align: -3px;
            margin-right: 5px;
            @include sprite-error;
        }
    }
    .bt-wrap{
        text-align: right;
        padding-top: 30px;
        padding-bottom: 30px;
        color: #666;
        input{
            vertical-align: -2px;
        }
        a{
            display: inline-block;
            width: 88px;
            height: 34px;
            line-height: 34px;
            text-align: center;
            border: 1px solid #e4393c;
            border-radius: 2px;
            margin-left: 10px;
            font-size: 14px;
        }
        .btn-confirm{
            background: #e4393c;
            color: #fff;
        }
        .btn-cancel{
            border-color: #999999;
            color: #333;
        }
    }

    .layer-warning{
      width: 400px;
      background-color: #fff8f0;
      margin-left: -10px;
      margin-top: -20px;
      margin-bottom: 10px;
      padding: 6px 10px;

      .sprite-failsmall{
          display: inline-block;
          vertical-align: middle;
          @include sprite-failsmall;
      }
      font{
        white-space: nowrap;
        vertical-align: middle;
        padding-left: 5px;
      }
      a{
        color: #0066cc;
        padding-left: 5px;
        cursor: pointer;
      }
    }
}
.asked{
    text-align: center;
    padding: 15px;
    .sprite-success{
        display: inline-block;
        @include sprite-success;
    }
    .sprite-failure{
        display: inline-block;
        @include sprite-failure;
    }
    p{
        text-align: left;
        font-size: 12px;
        color: #999;
        line-height: 200%;
        padding-left: 12px;
        vertical-align: 18px;
        display: inline-block;
        *display: inline;
        *zoom: 1;
    }
    strong{
        font-size: 16px;
        color: #333;
    }
    .bt-wrap{
        text-align: center;
        padding-top: 20px;
    }
}
.warning{
    text-align: center;
    padding: 15px;
    .sprite-success{
        display: block;
        margin: 0 auto;
        @include sprite-success;
    }
    .sprite-failure{
        display: block;
        margin: 0 auto;
        @include sprite-failure;
    }
    p{
        font-size: 12px;
        color: #999;
        display: inline-block;
        padding-top: 15px;
        *display: inline;
        *zoom: 1;
    }
    strong{
        font-size: 16px;
        color: #333;
    }
    .bt-wrap{
        text-align: center;
        padding-top: 20px;
    }
}
