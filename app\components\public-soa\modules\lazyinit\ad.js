define('PUBLIC_ROOT/modules/lazyinit/ad', function(require, exports, module) {
    var G = require('PUBLIC_ROOT/modules/common/core')
    var tools = require('PUBLIC_ROOT/modules/common/tools/tools')

    require('PUBLIC_ROOT/modules/common/tools/json2')
    require('PUBLIC_ROOT/modules/ELazyload/ELazyload')

    // erp fa 广告位
    function setFAads(cfg) {
        // 边栏广告位
        var ads = [
            // '2_163_817', // 广告部 | 产品终端页
            '2_163_818', // 广告部 | 产品终端页
            '2_232_3431', // 广告部 | 新首页
            '0_0_3743', // 广告部 | 产品终端页
            '2_163_6092' // 延保服务图片
        ];

        // 非苹果店铺
        if (cfg.shopId != 1000000127) {
            ads.push('2_163_817');
        }

        // 商品页顶通和logo右侧广告位, 搜索引擎来源不显示
        if (typeof searchEngineSource === 'undefined') {
            ads.push('2_163_5395')
            ads.push('2_163_5394')
        }

        // POP 商品通用广告位
        if (cfg.isPop) {
            ads.push('2_601_3951')
        }

        if (cfg.isEBook) {
            ads.push('0_0_8246')
        }

        // 图书广告位
        if (cfg.pType === 3) {
            ads.push('0_0_3789')
            ads.push('0_0_3790')
            ads.push('0_0_6052')
            ads.push('0_0_4035')
        }
        // 音响广告位
        //if ( pageConfig.product.pType === 4 ) {
        //    ads.push('19_689_5767');
        //}

        /*
         一级分类ID=4053（教育音像），广告位ID为109897
         一级分类ID=4051（音乐），广告位ID为109895
         一级分类ID=4052（影视），广告位ID为109896
         */
        // 教育音像
        if (cfg.cat[0] === 4053) {
            // 商品详情
            ads.push('0_0_8253')
            // 左侧栏
            ads.push('0_0_5790')
        }
        // 音乐
        if (cfg.cat[0] === 4051) {
            // 商品详情
            ads.push('0_0_8251')
            // 左侧栏
            ads.push('0_0_5767')
        }
        // 影视
        if (cfg.cat[0] === 4052) {
            // 商品详情
            ads.push('0_0_8252')
            // 左侧栏
            ads.push('0_0_5789')
        }
        // 教育培训
        if (cfg.cat[0] == 13678) {
            // 顶通
            ads.push('0_0_12161')
        }
        // 品类强制商品详情广告位
        if (cfg.forceAdUpdate) {
            ads.push('0_0_' + cfg.forceAdUpdate)
        }
        $.ajax({
            url: '//nfa.jd.com/loadFa_toJson.js?aid=' +
                ads.join('-') +
                '&ver=20131107',
            dataType: 'script',
            cache: true,
            success: function() {
                var $target = $('#miaozhen7886,#miaozhen10767')
                if ($target.length) {
                    $target.ELazyload({ source: 'data-lazyload' })
                }
            }
        })
    }

    // 市场部广告位 x.jd.com
    function setMarketAds(cfg) {
        var $adMarket = $('#ad_market_1')

        if ($adMarket.length) {
            $.ajax({
                dataType: 'jsonp',
                //url: '//x.jd.com/ShowInterface',
                url: '//rtb-x.jd.com/swc/show_interface',
                data: {
                    sku_id: cfg.skuid,
                    shop_id: cfg.shopId,
                    urlcid3: cfg.cat[2],
                    ad_ids: '57:1',
                    ad_type: 8,
                    spread_type: 1,
                    debug: 0,
                    location_info: 0
                },
                success: function(data) {
                    if (!data) {
                        return false
                    }

                    if (data.errcode == 0) {
                        try {
                            var el = document.getElementById('ad_market_1')
                            var data2 = /<script type=\"text\/javascript\">(.*?)<\/script>/gim.exec(
                                data.data
                            )
                            if (data2) {
                                var data3 = data2[1]
                                var dataHtml = data.data.replace(
                                    /<script type=\"text\/javascript\">.*?<\/script>/gmi,
                                    ''
                                )
                                el.innerHTML = dataHtml
                                eval(data3)
                            } else {
                                el.innerHTML = data.data
                            }
                            if (data.scriptsrc) {
                                jsf.loadScript(data.scriptsrc)
                            }
                        } catch (e) {}
                    }
                }
            })
        }
    }

    // 统一服务活动、气氛
    function setActivity(cfg) {
        // function setSideBanner(r) {
        //     var $el = $('#product-intro,.product-intro')
        //     var tpl =
        //         '\
        //     <style>\
        //         .side__ad { position: absolute; top: 0; width:200px; height:800px; }\
        //         .side__ad-left { left: -200px; background:url(//img13.360buyimg.com/cms/${materials[0].imagePath}) top right no-repeat }\
        //         .side__ad-right { right: -200px; background:url(//img13.360buyimg.com/cms/${materials[1].imagePath}) top left no-repeat }\
        //     </style>\
        //     <div id="ccc-side-ad" data-name="${name}" data-type="${type}">\
        //         {if typeof materials[0].link !== "undefined"}\
        //         <a href="${materials[0].link}" target="_blank" class="side__ad side__ad-left"></a>\
        //         <a href="${materials[1].link}" target="_blank" class="side__ad side__ad-right"></a>\
        //         {else}\
        //         <div class="side__ad side__ad-left"></div>\
        //         <div class="side__ad side__ad-right"></div>\
        //         {/if}\
        //     </div>'

        //     function setRightBannerWidth() {
        //         var width = ($(window).width() - $el.outerWidth()) / 2
        //         if (width > 0) {
        //             $('#ccc-side-ad .side__ad-right').css({
        //                 width: width,
        //                 right: -width
        //             })
        //         }
        //     }

        //     $el.append(tpl.process(r))
        //     setRightBannerWidth()
        //     $(window).resize(tools.throttle(setRightBannerWidth, 100))
        // }
        // function setTopBanner(r) {
        //     var $choose = $('#summary,.summary').eq(0)
        //     var tpl =
        //         '\
        //     <div id="act-top-bottom" data-name="${name}" style="height:32px;overflow:hidden;">\
        //         {if materials[0].link}<a target="_blank" href="${materials[0].link}">{/if}\
        //             <img height="32" src="//img13.360buyimg.com/cms/${materials[0].imagePath}" >\
        //         {if materials[0].link}</a>{/if}\
        //     </div>'

        //     if ($('#act-top-bottom').length) {
        //         $('#act-top-bottom').remove()
        //     }

        //     // banner 头优先级：预约/售 > 秒杀 > CMS(current) > 闪购
        //     if ($('body').hasClass('yyp')) return
        //     if ($('#banner-miaosha').length) return
        //     if ($('#banner-shangou').length) {
        //         $('#banner-shangou').remove()
        //     }

        //     // POP三免一 > 大促预告价 > 市场部618大促
        //     if ($('#prepare-price-banner').length) {
        //         if (r.name === '618大促氛围') {
        //             return false
        //         } else {
        //             $('#prepare-price-banner').hide()
        //         }
        //     }

        //     if (r.materials.length) {
        //         $choose.before(tpl.process(r))
        //     }
        // }
        // function setContentBanner(r) {
        //     var $choose = $('#choose,.itemInfo-wrap')
        //     var tpl =
        //         '\
        //     <div id="act-banner-bottom" data-name="${name}" style="clear:both">\
        //         {if materials[0].link}<a target="_blank" href="${materials[0].link}">{/if}\
        //             <img width="500" height="50" src="//img13.360buyimg.com/cms/${materials[0].imagePath}" >\
        //         {if materials[0].link}</a>{/if}\
        //     </div>'

        //     if (!$choose.length) {
        //         $choose.append(tpl.process(r))
        //     }
        // }

        function setToolbar(r) {

            // var aid = r && (r.materials[0].imagePath || r.materials[0].link)

            var sid = cfg.cat[2] === 832 ? '737542' : '992349'
            var phoneNetwork = cfg.phoneNetwork
                ? cfg.phoneNetwork.join(',')
                : ''

            var hallEnable = cfg.cat[2] === 655
            var hallUrl = {
                url: '//ctc.jd.com/hall/index?',
                param: {
                    sku: cfg.skuid,
                    cat: cfg.cat.join(','),
                    mode: phoneNetwork
                }
            }

            var ad_entry = {
                enabled: false,
                startTime: +new Date(1970, 1, 1, 0, 0, 0) / 1000,
                endTime: +new Date(3017, 1, 1, 0, 0, 0) / 1000
            }

            // if (aid) {
            //     ad_entry.enabled = true
            //     ad_entry.id = '0_0_' + aid
            // }

            var url = 'https://item.m.jd.com/product/{0}.html?pc_source=pc_productDetail_{0}'.format(
                cfg.skuid
            )
            var img = '<img src="//qrimg.jd.com/{0}-118-1-4-2.png?ltype=0" width="118" height="118" />'.format(
                encodeURIComponent(url)
            )
            var qrHTML = '\
                <div class="toolbar-qrcode" id="toolbar-qrcode">\
                    <span class="close" href="#none">×</span>\
                    <p class="ac">使用京东APP<br>随时随地看商品</p>\
                    '+ img +'\
                </div>'

            var option = {
                pType: 'item',
                bars: {
                    hall: {
                        index: 0.5,
                        title: '营业厅',
                        login: true,
                        enabled: hallEnable,
                        iframe: hallUrl.url + $.param(hallUrl.param)
                    },
                    cart: {
                        enabled: true
                    },
                    history: {
                        enabled: false
                    },
                    coupon: {
                        index: 1.5,
                        enabled: true,
                        title: '优惠券',
                        login: true,
                        iframe: '//item.jd.com/coupons?' +
                        $.param({
                            skuId: cfg.skuid,
                            cat: cfg.cat.join(','),
                            venderId: cfg.venderId,
                            isCanUseDQ: 'isCanUseDQ-' + (G.onAttr('isCanUseDQ-1') ? 1 : (G.onAttr('isCanUseDQ-0') ? 0 : 1)),
                            isCanUseJQ: 'isCanUseJQ-' + (G.onAttr('isCanUseJQ-1') ? 1 : (G.onAttr('isCanUseJQ-0') ? 0 : 1))
                        })
                    },
                    jimi: {
                        iframe: '//jimi.jd.com/index.action?productId=' +
                        cfg.skuid +
                        '&source=jdhome'
                    }
                },
                links: {
                    qrcode: {
                        index: 1,
                        anchor: '#none',
                        extraHTML: qrHTML
                    },
                    feedback: {
                        index: 2,
                        href: '//surveys.jd.com/index.php?r=survey/index/sid/323814/newtest/Y/lang/zh-Hans'
                    },
                    top: { index: 3, anchor: '#' }
                },
                ad: ad_entry
            }
            //配置限时特惠
            var xianshitehui = function(cfg){
                this.$el = null;
                this.$ext = null;
                this.category = cfg.cat || [];
                this.specialAttrs = cfg.specialAttrs || [];
                this.skuid = cfg.skuid;
                this.showToolbar = false;
                this.timer = null;
                this.init();
            }
            xianshitehui.prototype = {
                init:function(){
                    if(this.isJiaDian() && this.hasAttr(/gdsp-1/)){
                        this.showToolbar = true;
                    }
                },
                setEl:function(){
                    this.$el = $("#J-global-toolbar .jdm-tbar-tab-xianshitehui");
                    this.$ext = this.$el.find(".toolbar-ext-xianshitehui");
                },
                gdspFun:function(){
                    if(!this.showToolbar){
                        return false;
                    }
                    this.setEl();
                    var _this = this;
                    //注册鼠标滑动事件
                    this.$ext.hover(function(){
                        _this.hoverInCallback();
                    },function(){
                        _this.hoverOutCallback();
                    });
                    //设置高端品的初始状态
                    this.gdspInit();
                },
                gdspInit:function(){
                    if(!this.showToolbar){
                        return false;
                    }
                    var _this = this;
                    this.checkLogin(function(data){
                        _this.setToolExtState(data,false);
                    });
                },
                setToolExtState:function(data,isTipShow){
                    if(data.IsAuthenticated){
                        //请求接口
                        this.doVerifyByLogin(data,isTipShow);
                    }
                    else{
                        this.showItemExt();
                        this.setSwingAnimate(isTipShow);
                        this.setTipsNoLogin();
                        this.setTipsState(isTipShow);
                    }
                },
                setTipsState:function(isTipShow){
                    isTipShow = isTipShow || false;
                    if(isTipShow){
                        this.$ext.find(".toolbar-ext-tips").show();
                    }
                    else{
                        this.$ext.find(".toolbar-ext-tips").hide();
                    }
                },
                doVerifyByLogin:function(data,isTipShow){
                    var _this = this;
                    try{
                        $.ajax({
                            url: '//cd.jd.com/gdpPromotion',
                            data: {
                                pin: data.Name,
                                skuId: _this.skuid
                            },
                            dataType: 'jsonp',
                            error:function(e){
                                _this.hideItemExt();
                                _this.clearSwingAnimate();
                            },
                            success: function(r) {
                                if(r && r.resultCode && r.resultCode == "SUCCESS"){
                                    _this.showItemExt();
                                    _this.setSwingAnimate(isTipShow);
                                    _this.setTipsLogin(r.resultObj);
                                    _this.setTipsState(isTipShow);
                                }
                                else{
                                    _this.hideItemExt();
                                    _this.clearSwingAnimate();
                                }
                            }
                        });
                    }catch(e){
                        _this.hideItemExt();
                        _this.clearSwingAnimate();
                        throw new Error("interface error");
                    }

                },
                setTipsLogin:function(data){
                    data.diffPrice = data.diffPrice || 0;
                    data.detailText = data.detailText || "";
                    var tipsContent =  '\
                              <h4>本商品为您直降<span class="ext-price">'+data.diffPrice+'</span>元</h4>\
                              <span class="ac">'+data.detailText+'</span>';
                    var params = {
                        el:this.$ext.find(".toolbar-ext-tips"),
                        content:tipsContent,
                        width:180,
                        height:80,
                        pos:"bottom",
                        className:"xianshi-ext-lg",
                        zIndex:10
                    };
                    this.resizeTip(params);
                },
                setTipsNoLogin:function(){
                    var params = {
                        el:this.$ext.find(".toolbar-ext-tips"),
                        content:"<span>登录查看具体促销信息</span>",
                        width:160,
                        height:60,
                        pos:"bottom",
                        zIndex:10
                    };
                    this.resizeTip(params);
                },
                hoverInCallback:function(){
                    var _this = this;
                    this.checkLogin(function(data){
                        _this.setToolExtState(data,true);
                    });
                },
                hoverOutCallback:function(){
                    this.setTipsState(false);
                    this.setSwingAnimate(false);
                },
                isJiaDian:function(){
                    return parseInt(this.category[0]) == 737 ? true : false;
                },
                hasAttr:function(attr){
                    var attrs = this.specialAttrs;
                    var isStr = typeof attr === 'string';
                    var len = attrs.length;
                    if (attrs && attrs.length) {
                        for (var i = 0; i < len; i++) {
                            if(isStr){
                                if (attrs[i] === attr) {
                                    return true;
                                }
                            }
                            else{
                                if(attr.test(attrs[i])){
                                    return true;
                                }
                            }

                        }
                        return false;
                    }
                    return false;
                },
                checkLogin:function(cb){
                    cb = cb || function(data){};
                    tools.checkLogin(function(r){
                        cb(r);
                    });
                },
                getTemplate:function(){
                    var img = '<img src="//img30.360buyimg.com/devfe/jfs/t9766/193/475664612/4671/2175a09b/59d0608bN88e8132f.png" width="95" height="74" />'.format(
                        encodeURIComponent(url)
                    );
                    var img_animate1 = '<img src="//img10.360buyimg.com/devfe/jfs/t10174/177/1224509602/4427/bd5db91a/59dddce7Na2880abf.png" width="95" height="74" />'.format(
                        encodeURIComponent(url)
                    );
                    var img_animate2 = '<img src="//img13.360buyimg.com/devfe/jfs/t10675/360/1218150373/4777/1a704dee/59ddddafNba577b3f.png" width="95" height="74" />'.format(
                        encodeURIComponent(url)
                    );
                    var img_animate3 = '<img src="//img20.360buyimg.com/devfe/jfs/t10297/23/1209404015/4771/351820db/59ddddd1Neb144188.png" width="95" height="74" />'.format(
                        encodeURIComponent(url)
                    );
                    var img_animate4 = '<img src="//img12.360buyimg.com/devfe/jfs/t10762/134/1238810609/4777/51612783/59ddddf2N22a37ed1.png" width="95" height="74" />'.format(
                        encodeURIComponent(url)
                    );

                    var img_animate = img_animate1 + img_animate2 + img_animate3 + img_animate4;
                    var extraHTML = '\
                        <div class="toolbar-ext-xianshitehui" id="toolbar-ext-xianshitehui">\
                            <a class="xianshitehui-bg" href="javascript:void(0)">\
                            '+ img +'\
                            </a>\
                            <a class="xianshitehui-bg-animate" href="javascript:void(0)">\
                            '+ img_animate +'\
                            </a>\
                            <div class="toolbar-ext-tips"><i class="ext-tip-arrow"></i><div class="ext-tip-text"></div></div>\
                        </div>';
                    return extraHTML;
                },
                configToolBar:function(opts){
                    if(!G.isObject(opts) || !G.isObject(opts.bars)){
                        return {};
                    }
                    if(this.showToolbar){
                        var extraHTML = this.getTemplate();
                        opts.bars.xianshitehui = {
                            index: 0.2,
                            title: '限时专属特惠',
                            enabled: true,
                            login:true,
                            extraHTML:extraHTML,
                            iframe: "//notice.jd.com/notice/userlist"
                        };
                    }
                    return opts;
                },
                hideItemExt:function(){
                    this.$ext.removeClass("hide").addClass("hide");
                },
                showItemExt:function(){
                    this.$ext.removeClass("hide");
                },
                resizeTip:function(params){
                    params = params || {};
                    params.el = params.el || $(".toolbar-ext-tips");
                    params.content = params.content || "";
                    params.width = params.width || 160;
                    params.height = params.height || 60;
                    params.pos = params.pos || "bottom";
                    params.zIndex = params.zIndex || 10;
                    params.close = params.close || false;
                    params.className = params.className || "";
                    var $tipsObj = params.el;
                    $tipsObj.addClass(params.className).css({"width":params.width + "px","height":params.height + "px","zIndex":params.zIndex});
                    if(params.pos == "top"){
                        $tipsObj.css("top","-10px");
                    }
                    else{
                        $tipsObj.css("bottom","65px");
                    }

                    if(params.pos == "left"){
                        $tipsObj.css("left","-10px");
                    }
                    else{
                        $tipsObj.css("right","40px");
                    }
                    $tipsObj.children(".ext-tip-text").html(params.content);
                },
                setSwingAnimate:function(isHover){
                    var $swingBg = this.$ext.find(".xianshitehui-bg-animate");
                    if($swingBg && $swingBg.length){
                        if(!isHover){
                            if(!this.timer){
                                var i = 0;
                                this.timer = setInterval(function(){
                                    $swingBg.find("img").hide().each(function(index){
                                        if(parseInt(index) == parseInt(i)){
                                            $(this).show();
                                        }
                                    });
                                    i = (i+1)%4;
                                },100);
                            }
                        }
                        else{
                            if(this.timer){
                                this.timer = clearInterval(this.timer);
                            }
                        }
                    }
                },
                clearSwingAnimate:function(){
                    if(this.timer){
                        this.timer = clearInterval(this.timer);
                    }
                    var $swingBg = this.$ext.find(".xianshitehui-bg-animate");
                    $swingBg.find("img").hide();
                }
            };
            var obj_xsth = new xianshitehui(cfg);
            option = obj_xsth.configToolBar(option);

            seajs.use(
                ['//static.360buyimg.com/devfe/toolbar/1.0.0/js/main'],
                function(toolbar) {
                    pageConfig.toolbar = new toolbar($.extend(true, option, pageConfig.toolbarParam))

                    $('.jdm-tbar-tab-qrcode').hover(function () {
                        $('#toolbar-qrcode').show()
                    }, function () {
                        $('#toolbar-qrcode').hide()
                    }).click(function () {
                        $('#toolbar-qrcode').hide()
                        return false
                    });

                    //限时特惠交互
                    obj_xsth.gdspFun();
                }
            )
        }

        // $.ajax({
        //     url: host + '/atmosphere',
        //     data: {
        //         skuId: cfg.skuid,
        //         brandId: cfg.brand,
        //         cat: cfg.cat.join(','),
        //         venderId: cfg.venderId,
        //         pTag: cfg.pTag,
        //         expands: JSON.stringify(cfg.specialAttrs),
        //         appid: 'item-v3',
        //         functionId: "pc_atmosphere"
        //     },
        //     dataType: 'jsonp',
        //     success: function(r) {
        //         r = r || {}

        //         if (r['1']) setSideBanner(r['1'])
        //         if (r['2']) setTopBanner(r['2'])
        //         if (r['3']) setContentBanner(r['3'])

        //         setToolbar(r['4'])
        //     }
        // })
        // setToolbar({}) // 由于接口/atmosphere无返回，需要下线，故把侧边栏代码迁移出来
    }
    function init(cfg) {
        setFAads(cfg)
        setMarketAds(cfg)
        setActivity(cfg)
    }

    exports.init = init
})
