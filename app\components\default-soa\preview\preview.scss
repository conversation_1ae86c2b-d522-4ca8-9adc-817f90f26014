@import "../common/lib";
@import "./__sprite";


.preview {
    .center-layer {
      border-radius: 8px;
      overflow: hidden;
    }
    .video-play-icon {
      width: 32px;
      height: 32px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -16px;
      margin-top: -16px;
      background: url(https://img11.360buyimg.com/imagetools/jfs/t1/257894/4/3508/1579/676cc6adF966af6a5/a24e08cf2c65f8ea.png) left top / 100% 100% no-repeat;
      cursor: pointer;
    }
    .video-player-dimensions {
      width: 720px;
      height: 720px;
      border-radius: 8px;
    }

    position: relative;
    .preview-btn{
        position: absolute;
        z-index: 7;
        left: 0;
        width: 100%;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        justify-content: center;
        text-align: center;
        top: 50%;
        margin-top: -50px;
        li{
            // display: inline-block;
            // margin: 0 5px;
            // vertical-align:text-top;
        }
        // .btn-3d {
        //     display: inline-block;
        //     width: 50px;
        //     height: 50px;
        //     background: url(i/main-circles.png) no-repeat -110px 0;
        //     &:hover {
        //         background: url(i/main-circles.png) no-repeat -165px 0;
        //     }
        //     &.vr-single-pic {
        //         background: url(i/main-circles.png) no-repeat 0 0;
        //         &:hover {
        //             background: url(i/main-circles.png) no-repeat -55px 0;
        //         }
        //     }
        // }
        // .vr-single-pic {
        //     em {
        //         span {
        //             margin-left: 4px;
        //         }
        //     }
        // }
        
        // .video-icon{
        //     cursor: pointer;
        //     display: inline-block;
        //     width: 50px;
        //     height: 50px;
        //     background: url(i/main-circles.png) no-repeat 0 -55px;
        //     &:hover {
        //         background: url(i/main-circles.png) no-repeat -55px -55px;
        //     }
        // }
        .video-icon {
          width: 100px;
          height: 100px;
          display: block;
          background: url(https://img12.360buyimg.com/imagetools/jfs/t1/268427/6/7334/5868/677778bfFdfcd1873/09c35cebfaf51498.png) left top / 100% 100% no-repeat;
          cursor: pointer;
        }
    }
    .main-img {
        width: 720px;
        height: 720px;
        text-align: center;
        background: #fff;
        border-radius: 8px;


        & > div:nth-child(1) {
          display: inline-block;
          position: relative;
        }

        i {
            position: absolute;
            right: 0;
            bottom: 0;
            @include sprite-magnify;
        }
    }

    .jqzoom {
        position: relative;
        padding: 0;
        float: right;
    }

    .zoomdiv {
        z-index: 1000;
        position: absolute;
        top: 0;
        left: 771px;
        width: 770px;
        height: 770px;
        background: url(//static.360buyimg.com/item/main/1.0.12/css/i/loading.gif) #fff no-repeat center center;
        // border: 1px solid #e4e4e4;
        display: none;
        text-align: center;
        overflow: hidden;
        box-sizing: border-box;
        border-radius: 8px;
        .root61 .clothing & {
            left: 771px;
        }
    }

    .bigimg {
        width: 1440px;
    }

    .jqZoomPup {
        z-index: 3;
        visibility: hidden;
        position: absolute;
        top: 0;
        left: 0;
        width: 50px;
        height: 50px;
        // border: 1px solid #aaa;
        border-radius: 5px;
        background: rgba(255, 235, 239, 0.5);
        // opacity: .5;
        // -moz-opacity: .5;
        // -khtml-opacity: .5;
        // filter: alpha(Opacity=50);
        cursor: move;
    }
    .spec-list {
        position: relative;
        float: left;

        .arrow-prev,
        .arrow-next {
          width: 114px;
          height: 24px;
          border-radius: 4px;
          border: 1px solid rgba(0, 0, 0, 0.06);
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          cursor: pointer;
          z-index: 2;
          box-sizing: border-box;
          background: #fff;

          i {
              display: block;
          }
        }
        .arrow-prev {
            top: 0;

            .sprite-arrow-prev {
              width: 12px;
              height: 12px;
              background: url(https://img13.360buyimg.com/imagetools/jfs/t1/261254/24/4000/566/676e4bc1F503ba8a9/6d1a84d14e57f629.png) left top / 100% 100% no-repeat;
            }

            &:hover {
                i {
                    
                }
            }
            &.disabled {
                display: none;
            }
        }
        .arrow-next {
            bottom: 0;
            .sprite-arrow-next {
              width: 12px;
              height: 12px;
              background: url(https://img14.360buyimg.com/imagetools/jfs/t1/260409/1/4004/517/676e4b5aF4164c995/296d825f3f929a6a.png) left top / 100% 100% no-repeat;
            }
            &:hover {
                i {
                    
                }
            }
            &.disabled {
              display: none;
            }
        }
    }
    .spec-items {

        ul {
            height: 3000px;
            li {
                float: left;
                margin-bottom: 7px;
                position: relative;
                width: 114px;
                height: 114px;
                border: 1px solid rgba(0, 0, 0, 0.06);
                box-sizing: border-box;
                border-radius: 6px;
                cursor: pointer;
                overflow: hidden;

                .ctCloth & .thumb_video {
                    padding: 6px 0;
                }
                img {
                    height: 100%;
                    display: block;
                    margin: 0 auto;

                    &.thumb_video {
                        width: 54px;
                        height: 54px;
                        background: url(i/thumb-video-1.png) no-repeat center center;
                        border: 2px solid #fff;
                        .clothing & {
                            width: 50px;
                            height: auto;
                        }
                    }
                }

                &:hover, &.img-hover {
                  border: 1px solid #FF0F23;
                }
            }
        }
    }
    #spec-img{
        height: 720px;
        display: block;
        border-radius: 8px;
    }
    .preview-info {
        display: none;// 隐藏底部对比关注功能
        .left-btns {
            float: left;
            a {
                cursor: pointer;
                margin-right: 2px;
                i {
                    @include inline-block;
                    vertical-align: middle;
                    margin-right: 5px;
                }
                &:hover {
                    color: #E3393C;
                }
            }
            .follow {
                i {
                    @include sprite-follow-sku-hover;
                }
            }

            .share {
                i {
                    @include sprite-share-hover;
                }
            }
            .compare {
                i {
                    @include sprite-compare-hover;
                }
            }
        }
        .right-btns {
            float: right;
        }
    }
    .purchase-op{
        padding-top: 30px;
        text-align: center;
        display: none;
        .volume-purchase{
            display: inline-block;
            line-height: 26px;
            padding: 0 16px;
            border: 1px solid #ccc;
            color: #666;
            &:hover{
                border-color: #e3393c;
            }
        }
    }

    iframe {
        display: block;
    }

    .p-watermark{
        position: absolute;
        left:5px;
        bottom:5px;
        z-index:2;
        width: 56px;
        height: 63px;
    }
    .video{
        position: absolute;
        width: 720px;
        height: 720px;
        top: 0;
        right: 0;
        z-index: 20 !important;
        // height: 450px;
        .has-3d-show {
            left: 90px;
        }
        .close-video{
            position: absolute;
            right: 15px;
            top: 15px;
            @include sprite-close;
        }
    }
}

.root61 {
    .preview {
        .zoomdiv {
            left: 736px !important;
        }
        .preview-info {
            height: 20px;
        }
        .spec-list {
            .spec-items {
                width: 114px;
                height: 720px !important;
                overflow: hidden;
            }
        }
    }

    .clothing{
        .preview-wrap{
            // width:850px;
        }
        .preview{
            .spec-list{
                .spec-items{
                    // width: 290px;
                    ul{
                        li{

                        }
                    }
                }
            }
        }
    }
}

/*book*/
.ebook{
    .preview-wrap{
        // width:850px;
    }
    .preview {
        .spec-list {
            // display: none;
        }
        .main-img {
            // position: relative;
            // margin-bottom: 10px;
        }
        .img-tips {
            // position: absolute;
            // top: -1px;
            // right: 9px;
            // width: 45px;
            // height: 59px;
            // line-height: 50px;
            // color: #fff;
            // text-align: center;
            // background: url(i/img-tips.png) no-repeat;
            display: none;
        }
        .btn-read{ // 电子书试读按钮
            z-index: 7;
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 137.5px;
            height: 105.5px;
            background: url(https://img10.360buyimg.com/imagetools/jfs/t1/262475/9/6040/21051/6773bf59F2529ff03/823d9a08ed2eb127.png) left top / 100% 100% no-repeat;
            margin-left: -68.75px;
          }
        #spec-img{
          margin: 0 auto;
        }
    }
    .itemInfo-wrap{
        .news{
            .item{
                white-space: normal;
            }
        }
    }
}
/*root61*/
.root61 {
    .ebook {
        .preview-wrap {
            // width: 850px;
        }
    }
}


/*二手*/
.ershou{
    .preview{
        .spec-items{
            ul{
                li:hover, li.img-hover {
                    img {
                        
                    }
                }
            }
        }
    }
}
.clothing{
    .preview{
        .video{
            // width: 350px;
            // height: 350px;
        }
    }
}
.ctCloth{
    .preview{
        .video{
            // width: 350px;
            // height: 449px;
        }
    }
}
#v-video.ie9 .vjs-fullscreen-control {
    display: none;
}
/*大图预览*/
.preview-layer{
    width: 940px;
    height: 580px;
    background: #fff;
    .layer-tab{
        border-bottom: 1px solid #e4e4e4;
        padding-bottom: 29px;
        *padding: 0;
        *zoom: 1;
        li{
            float: left;
        }
        a{
            display: block;
            width: 90px;
            line-height: 28px;
            border: 1px solid #e4e4e4;
            border-bottom: none;
            text-align: center;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        .current a{
            height: 29px;
            border: none;
            background: #e4393c;
            color: #fff;
        }
    }
    .layer-con {
        height: 570px;
        .panorama-show{
            position: relative;
            width: 940px;
            height: 570px;
            text-align: center;
            .panorama-img {
                position: absolute;
                top:50%;
                left:50%;
                margin-top:-267px;
                margin-left:-267px;
            }
            .panorama-tips{
                text-align: center;
                display: none;
            }
        }
        .p-tips{
            .panorama-img {
                margin-top:-59px;
                // margin-left:-54px;
                margin-left:-115px;
                width: 230px;
                text-align: center
            }
            .panorama-tips{
                display: block;
            }
        }
    }
    .layer-img{
        width: 750px;
        height: 550px;
        float: left;
        text-align: center;
        img{
            vertical-align: middle;
        }
        span{
            display: inline-block;
            width: 1px;
            height: 550px;
            vertical-align: middle;
        }
    }
    .layer-side{
        width: 175px;
        float: right;
        padding-top: 20px;
        color: #666;
        font-size: 12px;
        .pro-name{
            max-height: 66px;
            font-size: 14px;
            font-weight: normal;
            line-height: 160%;
            overflow: hidden;
        }
        .pro-price{
            padding: 5px 0 8px;
            span{
                font-size: 18px;
                color: #e4393c;
            }
        }
        .side-list{
            border: 1px solid #e4e4e4;
            padding: 14px;
            margin-top: 15px;
            ul{
                overflow: hidden;
                zoom: 1;
            }
            li{
                width: 145px;
                float: left;
                a{
                    float: left;
                    width: 56px;
                    margin: 6px;
                    padding: 1px;
                    border: 1px solid #e4e4e4;
                    &:hover{
                        padding: 0;
                        border: 2px solid #e4393c;
                    }
                }
                .current{
                    padding: 0;
                    border: 2px solid #e4393c;
                }
            }
            img{
                width: 56px;
                height: 56px;
            }
            .list-page{
                text-align: center;
                padding: 30px 0 15px;
                .prev,.next{
                    display: inline-block;
                    height: 11px;
                    border: 1px solid #e4e4e4;
                    padding: 4px 6px;
                    line-height: 0;
                    margin: 0 10px;
                    vertical-align: -2px;
                }
                .prev {
                    i{
                        display: inline-block;
                        width: 0;
                        height: 0;
                        border-top: 6px dashed transparent;
                        border-right: 6px solid #666;
                        border-left: 0px dashed transparent;
                        border-bottom:6px dashed transparent;
                    }
                    &:hover{
                        i{
                            border-right-color: #e4393c;
                        }
                    }
                    &.disabled{
                        cursor: default;
                        i{
                            border-right-color: #999;
                        }
                    }
                }

                .next{
                    i{
                        display: inline-block;
                        width: 0;
                        height: 0;
                        border-top: 6px dashed transparent;
                        border-left: 6px solid #666;
                        border-right: 0px dashed transparent;
                        border-bottom:6px dashed transparent;
                    }
                    &:hover{
                        i{
                            border-left-color: #e4393c;
                        }
                    }
                    &.disabled{
                        cursor: default;
                        i{
                            border-left-color: #999;
                        }
                    }
                }
            }
        }
    }
}
.vjs-close-btn{
    padding:6px 10px;
    background: #333;
    color:#fff;
    border-radius: 10px;
    font-size:14px;
    line-height: 14px;
    position:absolute;
    right:10px;
    top:10px;
    cursor: pointer;
}

/* 3D家 begin*/
.jdpano-container {
    margin: 0;
    padding: 0;
    overflow: hidden;
    position: relative;
    cursor: default;
    width: 100%;
    height: 100%;
    font-family: Helvetica, "Nimbus Sans L", "Liberation Sans", Arial, sans-serif;
    background: #f4f4f4 url(./i/pra.3d1.svg) repeat;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: 0;
    line-height: 1.4;
    contain: content;
    width: 480px;
    height: 390px
}

.jdpano-container * {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box
}

.jdpano-grab {
    cursor: -webkit-grab;
    cursor: -moz-grab;
    cursor: grab;
    cursor: url(./i/pra.3d.hand1.svg) 12 8, default;
}

.jdpano-grabbing {
    cursor: -webkit-grabbing;
    cursor: -moz-grabbing;
    cursor: grabbing;
    cursor: url(./i/pra.3d.hand2.svg) 12 8, default;
}

.jdpano-sprite {
    background-image: url(./i/pra.3d2.svg)
}

.jdpano-container:-moz-full-screen {
    height: 100%!important;
    width: 100%!important
}

.jdpano-container:-webkit-full-screen {
    height: 100%!important;
    width: 100%!important
}

.jdpano-container:-ms-fullscreen {
    height: 100%!important;
    width: 100%!important
}

.jdpano-container:fullscreen {
    height: 100%!important;
    width: 100%!important
}

.jdpano-render-container {
    cursor: inherit;
    position: absolute;
    height: 100%;
    width: 100%
}

.jdpano-controls {
    margin-top: 4px;
    background-color: #fff;
    border: 1px solid #999;
    border-color: rgba(0, 0, 0, .4);
    border-radius: 3px;
    cursor: pointer;
    z-index: 2;
    -webkit-transform: translateZ(9999px);
    transform: translateZ(9999px)
}

.jdpano-control:hover {
    background-color: #f8f8f8
}

.jdpano-controls-container {
    position: absolute;
    top: 0;
    left: 4px;
    z-index: 1
}

.jdpano-zoom-controls {
    width: 26px;
    height: 52px
}

.jdpano-zoom-in {
    width: 100%;
    height: 50%;
    position: absolute;
    top: 0;
    border-radius: 3px 3px 0 0
}

.jdpano-zoom-out {
    width: 100%;
    height: 50%;
    position: absolute;
    bottom: 0;
    background-position: 0 -26px;
    border-top: 1px solid #ddd;
    border-top-color: rgba(0, 0, 0, .1);
    border-radius: 0 0 3px 3px
}

.jdpano-fullscreen-toggle-button,
.jdpano-orientation-button,
.jdpano-hot-spot-debug-indicator {
    width: 26px;
    height: 26px
}

.jdpano-hot-spot-debug-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 26px;
    height: 26px;
    margin: -13px 0 0 -13px;
    background-color: rgba(255, 255, 255, .5);
    border-radius: 13px;
    display: none
}

.jdpano-orientation-button-inactive {
    background-position: 0 -156px
}

.jdpano-orientation-button-active {
    background-position: 0 -182px
}

.jdpano-fullscreen-toggle-button-inactive {
    background-position: 0 -52px
}

.jdpano-fullscreen-toggle-button-active {
    background-position: 0 -78px
}

.jdpano-panorama-info {
    position: absolute;
    bottom: 4px;
    background-color: rgba(0, 0, 0, .7);
    border-radius: 0 3px 3px 0;
    padding-right: 10px;
    color: #fff;
    text-align: left;
    display: none;
    z-index: 2;
    -webkit-transform: translateZ(9999px);
    transform: translateZ(9999px)
}

.jdpano-title-box {
    position: relative;
    font-size: 20px;
    display: table;
    padding-left: 5px;
    margin-bottom: 3px
}

.jdpano-author-box {
    position: relative;
    font-size: 12px;
    display: table;
    padding-left: 5px
}

.jdpano-load-box {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 150px;
    margin: -75px 0 0 -100px;
    background-color: rgba(0, 0, 0, .7);
    border-radius: 3px;
    text-align: center;
    font-size: 20px;
    display: none;
    color: #fff
}

.jdpano-load-box p {
    margin: 20px 0
}

.jdpano-lbox {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    display: none
}

.jdpano-loading {
    -o-animation-duration: 1.5s;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    -o-animation-name: jdpano-mv;
    animation-name: jdpano-mv;
    -webkit-animation-name: jdpano-mv;
    -o-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
    -o-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-timing-function: linear;
    height: 10px;
    width: 10px;
    background-color: #fff;
    position: relative
}

@-o-keyframes jdpano-mv {
    from {
        left: 0;
        top: 0
    }
    25% {
        left: 10px;
        top: 0
    }
    50% {
        left: 10px;
        top: 10px
    }
    75% {
        left: 0;
        top: 10px
    }
    to {
        left: 0;
        top: 0
    }
}

@keyframes jdpano-mv {
    from {
        left: 0;
        top: 0
    }
    25% {
        left: 10px;
        top: 0
    }
    50% {
        left: 10px;
        top: 10px
    }
    75% {
        left: 0;
        top: 10px
    }
    to {
        left: 0;
        top: 0
    }
}

@-webkit-keyframes jdpano-mv {
    from {
        left: 0;
        top: 0
    }
    25% {
        left: 10px;
        top: 0
    }
    50% {
        left: 10px;
        top: 10px
    }
    75% {
        left: 0;
        top: 10px
    }
    to {
        left: 0;
        top: 0
    }
}

.jdpano-load-button {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 100px;
    margin: -50px 0 0 -100px;
    background-color: rgba(0, 0, 0, .7);
    border-radius: 3px;
    text-align: center;
    font-size: 20px;
    display: table;
    color: #fff;
    cursor: pointer
}

.jdpano-load-button:hover {
    background-color: rgba(0, 0, 0, .8)
}

.jdpano-load-button p {
    display: table-cell;
    vertical-align: middle
}

.jdpano-info-box {
    font-size: 15px;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 150px;
    margin: -75px 0 0 -100px;
    background-color: #000;
    border-radius: 3px;
    display: table;
    text-align: center;
    color: #fff;
    table-layout: fixed
}

.jdpano-info-box a {
    color: #fff;
    word-wrap: break-word;
    overflow-wrap: break-word
}

.jdpano-info-box p {
    display: table-cell;
    vertical-align: middle;
    padding: 0 5px 0 5px
}

.jdpano-error-msg {
    display: none
}

.jdpano-about-msg {
    font-size: 11px;
    line-height: 11px;
    color: #fff;
    padding: 5px 8px 5px 8px;
    background: rgba(0, 0, 0, .7);
    border-radius: 3px;
    position: absolute;
    top: 50px;
    left: 50px;
    display: none;
    opacity: 0;
    -webkit-transition: opacity .3s ease-in-out;
    -o-transition: opacity .3s ease-in-out;
    transition: opacity .3s ease-in-out;
    z-index: 1
}

.jdpano-about-msg a:link,
.jdpano-about-msg a:visited {
    color: #fff
}

.jdpano-about-msg a:hover,
.jdpano-about-msg a:active {
    color: #eee
}

.jdpano-hotspot-base {
    position: absolute;
    visibility: hidden;
    cursor: default;
    vertical-align: middle;
    top: 0;
    z-index: 1
}

.jdpano-hotspot {
    height: .52rem;
    width: .52rem;
    border-radius: .26rem
}

.jdpano-hotspot:hover {
    background-color: rgba(255, 255, 255, .2)
}

.jdpano-hotspot.jdpano-info {
    background-position: 0 -104px
}

.jdpano-hotspot.jdpano-scene {
    background-position: 0 -130px
}

div.jdpano-tooltip span {
    visibility: hidden;
    position: absolute;
    border-radius: 3px;
    background-color: rgba(0, 0, 0, .7);
    color: #fff;
    text-align: center;
    max-width: 200px;
    padding: 5px 10px;
    margin-left: -220px;
    cursor: default
}

div.jdpano-tooltip:hover span {
    visibility: visible
}

div.jdpano-tooltip:hover span:after {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border-width: 10px;
    border-style: solid;
    border-color: rgba(0, 0, 0, .7) transparent transparent transparent;
    bottom: -20px;
    left: -10px;
    margin: 0 50%
}

.jdpano-compass {
    position: absolute;
    width: 50px;
    height: 50px;
    right: 4px;
    bottom: 4px;
    border-radius: 25px;
    background-image: url(./i/pra.3d3.svg);
    cursor: default;
    display: none
}

.jdpano-world {
    position: absolute;
    left: 50%;
    top: 50%
}

.jdpano-face {
    position: absolute;
    -webkit-transform-origin: 0 0 0;
    -ms-transform-origin: 0 0 0;
    -o-transform-origin: 0 0 0;
    transform-origin: 0 0 0
}

.jdpano-dragfix,
.jdpano-preview-img {
    position: absolute;
    height: 100%;
    width: 100%
}

.jdpano-preview-img {
    -webkit-background-size: cover;
    background-size: cover;
    background-position: center
}

.jdpano-lbar {
    width: 150px;
    margin: 0 auto;
    border: #fff 1px solid;
    height: 6px
}

.jdpano-lbar-fill {
    background: #fff;
    height: 100%;
    width: 0
}

.jdpano-lmsg {
    font-size: 12px
}

.jdpano-fade-img {
    position: absolute;
    top: 0;
    left: 0
}
/* 3D家 end*/