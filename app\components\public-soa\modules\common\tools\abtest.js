define('PUBLIC_ROOT/modules/common/tools/abtest', function(require, exports, module) {
    /**
     * AB 测试
     * @param {String}        类型，uuid/pin
     * @param {Number}        百分比 0.9
     * @param {Number}        hash基数
     * @return {Object}       ABTest
     *
     * example:
     * var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');
     * var ABTest = require('PUBLIC_ROOT/modules/common/tools/abtest');
     * pageConfig.__yourABTestInstance = new ABTest(Tools.getUUID(), 0.5);
     */
    var ABTest = function(id, percent, base) {
        this.id = id
        this.percent = percent
        this.base = base || 10000

        this.tested = false

        this.version = {
            A: 0,
            B: 0,
            N: 0
        }
        this.result = {
            A: [],
            B: [],
            N: []
        }

        return this
    }

    ABTest.prototype = {
        /**
         * 跑测试用例
         */
        test: function() {
            var _this = this
            window.collectUUUID = function(data) {
                run(data)
            }

            function showResult() {
                var result = _this.version

                console.log(
                    '%c====================结果====================',
                    'color:#f00'
                )
                for (var i in result) {
                    console.info(
                        '| 版本' + i + '命中了' + result[i] / 100 + '\t% 的uuuid'
                    )
                }
                console.log(
                    '%c====================结果====================',
                    'color:#f00'
                )

                _this.tested = true
            }

            function run(r) {
                var uuuids = r
                var len = r.length
                var result = []

                for (var i = 0; i < len; i++) {
                    result.push(
                        'UUUID「' +
                            uuuids[i] +
                            '」\t命中了 版本「' +
                            _this.isHitVersion(uuuids[i]) +
                            '」'
                    )
                }

                console.log(result.join('\n'))

                showResult()
            }

            $.ajax({
                url: '//nfa.jd.com/loadFa_toJson.js?aid=2_163_4827-2_163_4828',
                dataType: 'jsonp',
                cache: true,
                success: function(r) {}
            })
        },
        isHitVersion: function(id) {
            id = id || this.id

            var hashResult, step = this.base * this.percent

            if (typeof id !== 'string') {
                id = id + ''
            }

            // 公共方法 getUUID 可能返回负数表示没有 UUID
            if (id && id > 0) {
                hashResult = this.getHashProbability(id, this.base)

                if (hashResult < step) {
                    this.version['A']++
                    this.result['A'].push(id)
                    return 'A'
                } else if (hashResult >= step && hashResult < step * 2) {
                    this.version['B']++
                    this.result['B'].push(id)
                    return 'B'
                } else {
                    this.version['N']++
                    this.result['N'].push(id)
                    return 'NONE'
                }
            } else {
                this.version['N']++
                this.result['N'].push(id)
                return 'NONE'
            }
        },
        /**
         * 切换测试版本
         * @param {String}        切换成a,b,n的某个版本uuuid
         */
        switchVersion: function(type) {
            if (!type || !/A|B|N/.test(type)) {
                console.warn(
                    'Are you kidding me~ Is there a test version called ' +
                        type +
                        '?'
                )
                return false
            }
            if (!this.tested) {
                console.warn('Please run test method first.')
            }
            var items = this.result[type]
            var randomItem = items[Math.floor(Math.random() * items.length)]

            createCookie(
                '__jda',
                '122270672.' + randomItem + '.043878.1386043878.1386043878.1',
                10,
                '/;domain=jd.com'
            )
            console.log(randomItem)
        },
        /**
         * [getHashProbability hash概率]
         * @param  {[String]} strNum   [源数据]
         * @param  {[Number]} baseNum  [基数 分母]
         */
        getHashProbability: function(strNum, baseNum) {
            // hash算法
            function hashCode(str) {
                for (var result = 0, i = 0; i < str.length; i++) {
                    result = (result << 5) - result + str.charCodeAt(i)
                    result &= result
                }
                return result
            }
            return Math.abs(hashCode(strNum)) % baseNum
        }
    }

    module.exports = ABTest
})
