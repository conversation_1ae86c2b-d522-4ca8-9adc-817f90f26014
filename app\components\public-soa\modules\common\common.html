{% import '../../views/maco/user.html' as wow %}
<div class="clr"></div>

<div class="w">
    <style type="text/css">
        .css-guide dd {
            padding: 10px;
        }
    </style>
    <div class="css-guide">
        <div class="hd">常见按钮预定义样式</div>
        <div class="bd">
            <dl>
                <dt>使用 <span class="cname">.btn-def</span>、<span class="cname">.btn-primary</span>可以获得不同风格的按钮。</dt>
                <dd><a href="#none" class="btn-def">Default</a></dd>
                <dd><a href="#none" class="btn-primary">Primary</a></dd>
                <dd><a href="#none" class="btn-special1 btn-lg">Special1</a></dd>
                <dd><a href="#none" class="btn-special2 btn-lg">Special1</a></dd>
            </dl>
        </div>
    </div>
    <div class="css-guide">
        <div class="hd">按钮的尺寸</div>
        <div class="bd">
            <dl>
                <dt>使用 <span class="cname">.btn-lg</span>、<span class="cname">.btn-sm</span>可以获得不同尺寸的按钮。</dt>
                <dd>
                    <a href="#none" class="btn-def btn-sm">small button</a>
                    <a href="#none" class="btn-primary btn-sm">small button</a>
                </dd>
                <dd>
                    <a href="#none" class="btn-def">default button</a>
                    <a href="#none" class="btn-primary">default button</a>
                </dd>
                <dd>
                    <a href="#none" class="btn-def btn-lg">large button</a>
                    <a href="#none" class="btn-primary btn-lg">large button</a>
                </dd>
            </dl>
        </div>
    </div>
    <div class="css-guide">
        <div class="hd">不可点击状态</div>
        <div class="bd">
            <dl>
                <dt>使用 <span class="cname">.btn-disable</span>控制按钮的不可用状态</dt>
                <dd>
                    <a href="#none" class="btn-def">Default</a>
                    <a href="#none" class="btn-def btn-disable">Default</a>
                </dd>
                <dd>
                    <a href="#none" class="btn-primary">Primary</a>
                    <a href="#none" class="btn-primary btn-disable">Primary</a>
                </dd>
                <dd>
                    <a href="#none" class="btn-special1 btn-lg">Special1</a>
                    <a href="#none" class="btn-special1 btn-lg btn-disable">Special1</a>
                </dd>
                <dd>
                    <a href="#none" class="btn-special2 btn-lg">Special1</a>
                    <a href="#none" class="btn-special2 btn-lg btn-disable">Special1</a>
                </dd>
            </dl>
        </div>
    </div>
</div>


<br>
<br>
<br>
<br>

<div class="m m-aside">
    <div class="mt">
        <h3>标题 </h3>
    </div>
    <div class="mc">
        <ul class="tag-list">
            <li>安卓（Android）</li>
            <li>5.0-4.6英寸</li>
            <li>1080P屏</li>
            <li>1080P屏</li>
            <li>移动2G/联通2G</li>
            <li>双卡双待</li>
        </ul>
    </div>
</div>

<div class="m m-aside">
    <div class="mt">
        <h3>浏览了最终购买</h3>
    </div>
    <div class="mc">
        {{ wow.plist(id='', class='plist', source="src", length=2, width=180, height=180, text_size=30, text='product') }}
    </div>
</div>

<div class="m m-aside">
    <div class="mt">
        <h3>手机热门销榜</h3>
    </div>
    <div class="mc no-padding">
        <div class="ETab">
            <div class="tab-main medium">
                <ul>
                    <li data-tab="trigger" class="current">同价位<sup>new<b>◤</b></sup></li>
                    <li data-tab="trigger">同品牌</li>
                    <li data-tab="trigger">总排行</li>
                </ul>
            </div>
            <div class="tab-con">
                <div data-tab="item">
                    {{ wow.plist(id='', class='plist-1', length=2, source="src", width=85, height=85, text_size=30, text='product') }}
                </div>
                <div data-tab="item" class="hide">请选择1</div>
                <div data-tab="item" class="hide">请选择2</div>
            </div>
        </div>
    </div>
</div>

<br>
<br>
<br>
<br>
<br>

<h2>Tooltips()</h2>
<div>
    <a href="#none" class="tooltips" title="Just hover me">hover me</a>
    <br>
    <a href="#none" class="tooltips" title="Just hover me">hover me with close</a>
    <br>
    <a href="#none" class="tooltips" title="Just hover me">hover me with custome contents</a>

</div>

<br>
<br>

<h2>Tabs(.large)</h2>
<div>
    <div class="ETab">
        <div class="tab-main large">
            <ul>
                <li data-tab="trigger" class="current">请选择0</li>
                <li data-tab="trigger">请选择1</li>
                <li data-tab="trigger">请选择2</li>
                <li data-tab="trigger">请选择3 <sup>new<b>◤</b></sup> </li>
            </ul>
            <div class="extra">
                <div class="item addcart-mini">
                    <div class="J-addcart-mini EDropdown">
                        <div class="inner">
                            <div class="head" data-drop="head">
                                <a href="#none" class="btn-primary">加入购物车</a>
                            </div>
                            <div class="content hide" data-drop="content">
                                <div class="mini-product-info">
                                    <div class="p-img fl">
                                        {{ wow.imgLink(width=100, height=100, text='mini product') }}
                                    </div>
                                    <div class="p-info lh">
                                        <div class="p-name">
                                            <a href="//jd.com/" target="_blank">
                                                商品名称（Apple iPhone 6s(A1700) 16G 玫瑰 金色 移动联通电信4G手机）
                                            </a>
                                        </div>
                                        <div class="p-price">
                                            <strong class="J-p-sku">￥578.00</strong> <span>X <span class="J-buy-num">2</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="item nav-qrcode">
                    <div class="J-nav-qrcode EDropdown">
                        <div class="inner">
                            <div class="head" data-drop="head">
                                <span class="icon-qr"></span>
                                <span class="text">扫一扫下单</span>
                                <span class="arrow arr-close"></span>
                            </div>
                            <div class="content ac hide" data-drop="content">
                                {{ wow.img(width=145, height=145, text='qrcode') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-con">
            <div data-tab="item">请选择0</div>
            <div data-tab="item" class="hide">请选择1</div>
            <div data-tab="item" class="hide">请选择2</div>
            <div data-tab="item" class="hide">请选择3</div>
        </div>
    </div>
</div>

<br>
<br>

<h2>Tabs(.medium)</h2>
<div>
    <div class="ETab">
        <div class="tab-main medium">
            <ul>
                <li data-tab="trigger" class="current">同价位</li>
                <li data-tab="trigger">同品牌</li>
                <li data-tab="trigger">总排行<sup>new<b>◤</b></sup></li>
            </ul>
        </div>
        <div class="tab-con">
            <div data-tab="item"> 请选择0</div>
            <div data-tab="item" class="hide">请选择1</div>
            <div data-tab="item" class="hide">请选择2</div>
        </div>
    </div>
</div>

<div id="fsss"></div>
<script>
    $(function () {
        seajs.use('PUBLIC_ROOT/modules/ETooltips/ETooltips', function () {
            $('.tooltips').eq(0).ETooltips();
            $('.tooltips').eq(2).ETooltips({
                content: 'this can be <strong>HTML</strong> syntax.'
            });
            $('.tooltips').eq(1).ETooltips({
                close: true
            });
        });
        seajs.use('PUBLIC_ROOT/modules/ETab/ETab', function () {
            $('.ETab').ETab();
        });

        seajs.use('PUBLIC_ROOT/modules/EDropdown/EDropdown', function () {
            $('.J-addcart-mini').EDropdown();
            $('.J-nav-qrcode').EDropdown({
                onOpen: function () {
                    var $inner = this.$el.find('.inner');
                    var $arr = this.$el.find('.arrow');

                    $inner.addClass('border');
                    $arr.addClass('arr-open');
                },
                onClose: function () {
                    var $inner = this.$el.find('.inner');
                    var $arr = this.$el.find('.arrow');

                    $inner.removeClass('border');
                    $arr.removeClass('arr-open');
                }
            });
        })
    });
</script>
