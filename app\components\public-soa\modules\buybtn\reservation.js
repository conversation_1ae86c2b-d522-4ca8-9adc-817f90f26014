define('PUBLIC_ROOT/modules/buybtn/reservation', function(require, exports, module) {
    var Countdown = require('PUBLIC_ROOT/modules/common/tools/tools').Countdown;
    var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event;
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');
    var Login = require("JDF_UNIT/login/1.0.0/login");
    var G = require('PUBLIC_ROOT/modules/common/core');

    require('JDF_UI/dialog/1.0.0/dialog');

    function showInvite(cfg) {
        var flag = cfg.isPinGou ? '1' : '3';
        var title = cfg.isPinGou ? '邀请好友' : '分享';
        var url = '//fenxiang.jd.com/shareFront/initShareIcons.action?sku='+ cfg.skuid +'&flag=' + flag;

        Login({
            modal: true,
            complete: function(r) {
                if (r && r.Identity && r.Identity.IsAuthenticated) {
                    showShareDialog();
                }
            }
        });
        function showShareDialog() {
            $('body').dialog({
                title: title,
                type: 'iframe',
                source: url,
                width: 600,
                height: 200
            });
        }
    }

    // 预约商品按钮
    var Reservation = {
        init: function (cfg) {
            $('.J-yuyue-share').before('<span class="yuyue-text J-yuyue-text"></span>')

            this.$el = $('#btn-reservation,#btn-reservation-mini');
            this.$banner = $('#yuyue-banner');
            this.$process = $('#yuyue-process');
            this.$textTip = $('.J-yuyue-text')
            this.jnbtBtn = cfg.jnbtBtn;
            this.cfg = cfg;
            this.isPlusProduct = false;
            // 如果是预约
            if (this.cfg.isYuYue) {
                this.$count = this.$banner.find('.J-count');
                this.$time = this.$banner.find('.J-time');


                this.bindEvent();
                this.get();
            }
        },
        invite: function () {
            return showInvite(this.cfg);
        },
        bindEvent: function () {
            var _this = this;

            $(document).delegate('.J-yuyue-share', 'click.pingou_invite', function () {
                _this.invite();
            });
            $('.J-yuyue-tips .J-tips').unbind('click').bind('click', function () {
                if ($('#detail').length) {
                    $('html,body').scrollTop($('#detail').offset().top);
                }
                $('#pingou-rules-tab').trigger('click');
            });

            Event.addListener('onStockReady', function (data) {
                var jnbtBtn = _this.cfg.jnbtBtn;
                var pid = data.area.id[0];

                if (_this.cfg.havestock) {
                    jnbtBtn.enabled();
                } else {
                    jnbtBtn.disabled();
                }

                // 北京地区支持节能补贴
                if (pid == "1" && _this.ysSupportJnbt) {
                    jnbtBtn.show();
                } else {
                    jnbtBtn.hide();
                }

                if ( _this.state === 4 ) {
                    if (_this.cfg.havestock) {
                        _this.enabled();
                    } else {
                        _this.disabled();
                    }
                }
            });

            Event.addListener('onLDPSelected', function (data) {
                _this.LDP = data.did;
                _this.enabled()
            })
        },
        get: function () {
            var _this = this;
            
            if (_this.cfg.isYuYue) {
                Event.addListener('onStockReady', function(obj) {
                    var data = obj && obj.stock && obj.stock.data
                    _this.cfg.havestock =  data.stockInfo && data.stockInfo.isStock
                    var r = data.yuyueInfo
                    // 预约兜底，主要解决预约已经结束，但是主数据依然打着预约标导致sku走预约模版
                    if (_this.cfg.isYuYue) {
                        if (
                            !r ||
                            $.isEmptyObject(r) ||
                            r.error ||
                            r.state == 5
                        ) {
                            location.href = G.modifyURL(location.href, {
                                query: {
                                    yuyue: '0'
                                }
                            })
                        }

                    }
                    if (r && r.yuyue) {
                        _this.set(data);
                    } else {
                        _this.onError();
                    }
                    // 预约隐藏价格
                    _this.setPrice(r && r.hidePrice);
                    _this.__res__ = r;
                });
            }
        },
        // 获取秒杀链接
        getIsKOurl: function () {
            var _this = this;
            var url = '//itemko.jd.com/itemShowBtn'
            $.ajax({
                url: url,
                data: {
                    skuId: this.cfg.skuid,
                    from: 'pc'
                },
                dataType: 'jsonp',
                success: function (r) {
                    _this.url = r.url;
                    if (_this.cfg.havestock) {
                        if (!r || r.type !== '3') {
                             _this.disabled(r.url);
                        } else {
                            _this.enabled();                       
                        }
                    } else {
                        _this.disabled(r.url);
                    }
                   

                    // _this.onError();

                }
            })
        },
        log: function (msg) {
            G.log(null, 'reservation.js', msg);
        },
        onError: function () {
            // 接口出错降级展示加入购物车按钮
            this.cfg.addToCartBtn.reInit(this.$el.eq(0));
            this.log('YuShou service return a error, Maybe the YuShou special attr has expired or service unavailable.');
        },
        set: function (r) {
            this.$el.show();
            this.setPlus(r, $.proxy(function() {
                this.setCountdown(r);
                this.setProcess(r);
                this.checkState(r);
            }, this));

            this.setCategory(r.category);
            this.supportJNBT(r);
        },
        getPlusText: function (r) {
            if (r.state === 2 || r.state === 3) {
                return {
                    'P_1-U_2' : '您是PLUS会员，将提前{0}分钟开抢',
                    'P_1-U_1' : '开通PLUS正式会员提前开抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
                    'P_1-U_N' : '开通PLUS正式会员提前开抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
                    'P_2-U_2' : '您是PLUS会员，将提前{0}分钟开抢',
                    'P_2-U_1' : '您是PLUS会员，将提前{0}分钟开抢',
                    'P_2-U_N' : '开通PLUS会员提前开抢，<a target="_blank" href="//plus.jd.com/index">去开通 &gt;&gt;</a>',
                    'NO_LOGIN': 'PLUS会员提前开抢，请 <a href="{0}" class="J-plus-login">登录</a> 确认购买资格'
                }
            } else if (r.state === 4) {
                return {
                    'P_1-U_2' : '您是PLUS会员，免预约可抢',
                    'P_1-U_1' : '开通PLUS正式会员免预约可抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
                    'P_1-U_N' : '开通PLUS正式会员免预约可抢，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>',
                    'P_2-U_2' : '您是PLUS会员，免预约可抢',
                    'P_2-U_1' : '您是PLUS会员，免预约可抢',
                    'P_2-U_N' : '开通PLUS会员免预约可抢，<a target="_blank" href="//plus.jd.com/index">去开通 &gt;&gt;</a>',
                    'NO_LOGIN': 'PLUS会员免预约可抢，请 <a href="{0}" class="J-plus-login">登录</a> 确认购买资格'
                }
            } else {
                return {}
            }
        },
        setPlus: function (r, callback) {
            var plusType = r.plusType
            this.isPlusProduct = plusType === 1 || plusType === 2
            // 等待抢购阶段 || 预约阶段
            if (!this.isPlusProduct) {
                callback();
                return false;
            }

            /**
             * 商品(Product) & 用户(User)类型
             * P_1 商品：仅正式
             * P_2 商品：正式加试用
             * U_2 用户：正式Plus
             * U_1 用户：试用Plus
             * U_N 用户：普通（非PLUS）
             */
            var plusText = this.getPlusText(r)

            Login.isLogin($.proxy(function(isLogin) {
                var userType = this.getUserType(r)
                var textTip = 'P_{0}-U_{1}'.format(plusType, userType)
                var loginUrl = 'https://passport.jd.com/new/login.aspx?ReturnUrl=' + encodeURIComponent(location.href)

                if (isLogin) {
                    // 有资格用户 showPlusTime
                    // plusType 1 正式
                    // plusType 2 正式 + 试用
                    // userType 2 正式期
                    // userType 1 试用期
                    this.showPlusTime = plusType === 1 && userType === 2
                        || plusType === 2 && userType === 2
                        || plusType === 2 && userType === 1;

                    // console.log('plusType: ' + plusType);
                    // console.log('userType: ' + userType);
                    // console.log('符合资格：' + this.showPlusTime);

                    if (plusText[textTip]) {
                        this.setTextTips(plusText[textTip].format(this.getPlusPriorityTime(r)), r)
                    }
                } else {
                    if (plusText['NO_LOGIN']) {
                        this.setTextTips(plusText['NO_LOGIN'].format(loginUrl), r)
                    }
                }
                callback()
            }, this))
        },
        // 展示 plus 优先几分钟开始抢购 plusEtime - qiangStime
        getPlusPriorityTime: function (r) {
            var time = this.convertDate(r.plusEtime) - this.convertDate(r.qiangStime)
            return parseInt(time / 1000 / 60)
        },
        /**
         * 转换时间字符串为时间戳
         * 2017-03-28 17:00:00
         * 1490691600000
         * @param dateString
         */
        convertDate: function (dateString) {
            var times = dateString.split(/-|\s|:/)
            for (var i = 0; i < times.length; i++) {
                times[i] = Number(times[i])
            }
            return new Date(times[0], times[1] - 1, times[2],
                times[3], times[4], times[5]).getTime()
        },
        // 展示 plus 倒计时时间
        getPlusTime: function(r) {
            if (r.state === 3) {
                return this.showPlusTime ? r.plusD : r.d
            }
            if (r.state === 4) {
                // r.isBefore === 1 && !this.showPlusTime
                // plusEtime - now
                var plusTime;
                if (r.isBefore === 1) {
                    if (this.showPlusTime) {
                        plusTime = (this.convertDate(r.qiangEtime) - new Date().getTime()) / 1000;
                    } else {
                        plusTime = (this.convertDate(r.plusEtime) - new Date().getTime()) / 1000;
                    }
                    return parseInt(plusTime)
                } else {
                    return r.d
                }
            }

            return r.d
        },
        setTextTips: function (text, r) {
            if (!(r.state === 4 && r.isBefore !== 1)) {
                this.$textTip.html(text)
            }
        },
        getUserType: function () {
            // http://cf.jd.com/pages/viewpage.action?pageId=57215567
            var ceshi3 = readCookie('ceshi3.com')
            if (ceshi3) {
                var firstChar = ceshi3.substr(0, 1)
                // 1 试用期
                // 2 正式期
                if (firstChar === '1' || firstChar === '2') {
                    return parseInt(firstChar)
                } else {
                    return 'N'
                }
            }
            return 'NO_LOGIN'
        },
        setCategory: function (r) {
            var $el = $('.J-yy-category');
            var text = null;

            if (r === '1') { text = '预约享资格'; }
            if (r === '3') { text = '预约享优惠'; }
            // if (r === '4') { text = '预约享提醒'; }

            if (text) {
                $el.html(text).show();
            } else {
                $el.hide();
            }
        },
        setProcess: function (r) {
            var $step2 = this.$process.find('.J-step2');
            var $step4 = this.$process.find('.J-step4');

            function removeSeconds(str) {
                return str ? str.replace(/:\d\d$/, '') : '';
            }

            $step2.html(removeSeconds(r.yueStime)+ '-' + removeSeconds(r.yueEtime));

            if (this.showPlusTime) {
                // 流程抢购开始/结束时间
                $step4.html(removeSeconds(r.plusStime) + '-' + removeSeconds(r.qiangEtime));
            } else {
                var sTime = this.isPlusProduct ? r.plusEtime : r.qiangStime
                $step4.html(removeSeconds(sTime) + '-' + removeSeconds(r.qiangEtime));
            }
        },
        disabled: function () {
            this.$el.addClass('btn-disable');
            this.$el.attr('href', '#none');
            this.isEnabled = true;
        },
        enabled: function () {
            if (this.cfg.isClosePCShow) { return false; }

            if (this.url) {
                this.url = Tools.addUrlParam(this.url, 'did', this.LDP);
                this.$el.attr('href', this.url);
            }
            this.$el.removeClass('btn-disable');
            this.isEnabled = true;
        },
        setCountdown: function (r) {
            var $time = this.$time;
            var $text = this.$banner.find('.J-text');

            var cdPrefix = '剩余';

            this.$count.html(r.num);

            if (r.state === 1) {
                cdPrefix = '距预约还需';
            }
            if (r.state === 2) {
                cdPrefix = '预约剩余';
            }
            if (r.state === 3) {
                cdPrefix = '距抢购还需';
            }
            if (r.state === 4) {
                cdPrefix = this.isPlusProduct
                    ? (this.showPlusTime ? '抢购剩余' : '距抢购还需')
                    : '抢购剩余'
            }

            $text.html(cdPrefix);

            var d = this.isPlusProduct ? this.getPlusTime(r) : r.d
            if (d > 0) {
                this.$banner.find('.J-item-2').show();

                new Countdown(d*1000, function(res) {
                    if ( res.d < 1 ) {
                        $time.html( res.h + '小时' + res.m + '分' + res.s + '秒');
                    } else {
                        $time.html( res.d + '天' + res.h + '小时' + res.m + '分' + res.s + '秒');
                    }
                });
            } else {
                this.$banner.find('.J-item-2').hide();
            }
        },
        checkState: function (r) {
            /**
             * state 是1 表示 距离预约开始的倒计时
             * state 是2 表示 距离预约结束的倒计时
             * state 是3 表示 距离抢购开始的倒计时
             * state 是4 表示距离抢购结束的倒计时
             */
            this.state = r.state;
            this.url = r.url;

            if (r.state === 1) {
                this.$count.hide();
                this.setBtnText('等待预约');
                this.disabled();
            }

            if ( r.state === 2 ) {
                this.setBtnText('立即预约');
                this.enabled();
            }

            if ( r.state === 3 ) {
                this.setBtnText('等待抢购');
                this.disabled();
            }

            if ( r.state === 4 ) {
                this.setBtnText('立即抢购');

                // pageConfig.isCfy 代表处方药
                //处方药预约预售抢购链接 预约后的抢购阶段 设置新链接
                if(pageConfig.product.isCfy){
                    this.url = '//rx.yiyaojd.com/cart_addItem.action?pid='+pageConfig.product.skuid+'&ptype=1&pcount=1';
                } else {
                    // 如果是秒杀，将链接换成秒杀链接
                    if (this.cfg.isKO) {
                        this.url = '#none'
                        this.getIsKOurl()
                    } 
                }
                // 抢购 阶段需要判断库存状态
                if (this.cfg.havestock) {
                    if (r.isBefore === 1 && !this.showPlusTime) {
                        this.disabled(r.url);
                    } else {
                        this.enabled();
                    }
                } else {
                    this.disabled(r.url);
                }
            }

            if ( r.state === 5 ) {
                this.setBtnText('抢购已结束');
                this.disabled();
            }
        },
        setBtnText: function (text) {

            this.$el.html(text);

            // 动态修改埋点
            this.$el.each(function () {
                var clstag = $(this).attr('clstag');
                $(this).attr('clstag', clstag.replace(/-[\u4e00-\u9fa5]*-/, '-' + text + '-'));
            });
        },
        supportJNBT: function (r) {
            // 支持节能补贴
            this.ysSupportJnbt = pageConfig.product.ysSupportJnbt = r.supportOther === 1;
            if (this.ysSupportJnbt) {
                this.jnbtBtn.show();
                this.jnbtBtn.enabled();
            } else {
                this.jnbtBtn.hide();
            }
        }
    };

    module.exports = Reservation;
    module.exports.showInvite = showInvite;
    module.exports.__id = 'Reservation';
});
