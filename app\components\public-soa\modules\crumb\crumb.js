define('PUBLIC_ROOT/modules/crumb/crumb', function(require, exports, module) {
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');

    require('PUBLIC_ROOT/modules/EDropdown/EDropdown');

    var template = '\
        {for item in items}\
        <li class="fore${pageConfig.crumbBR.push(item.id)}">\
            <div class="p-img">\
                <a href="//item.jd.com/${item.id}.html" target="_blank">\
                    <img width="65" height="65" alt="${item.base_info.name}" src="${pageConfig.FN_GetImageDomain(item.id)}n1/s65x65_${item.base_info.imageUrl}">\
                </a>\
            </div>\
            <div class="p-name">\
                <a href="//item.jd.com/${item.id}.html" target="_blank">${item.base_info.name}</a>\
            </div>\
            <div class="p-price">\
                <strong class="J-p-${item.id}">￥</strong>\
            </div>\
        </li>\
        {/for}';

    var hasData = false;

    function getBrandReco(cfg, $el) {
        if(hasData) {
            return;
        }
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        $.ajax({
            url: host + '/brand/recommend',
            data: {
                skuId: cfg.skuid,
                cat: cfg.cat.join(','),
                brandId: cfg.brand,
                appid: 'item-v3',
                functionId: "pc_brand_recommend"
            },
            scriptCharset: 'gbk',
            dataType: 'jsonp',
            success: function (r) {
                if (r && r.items.length) {
                    hasData = true;
                    pageConfig.crumbBR = [];
                    $el.html(template.process(r)).show();
                    Tools.priceNum({
                        skus: pageConfig.crumbBR,
                        $el: $el
                    });
                }
            }
        });
    }

    function init(cfg) {
        var $crumbBrand = $('.J-crumb-br');

        if ($crumbBrand.length < 1) {
            return false;
        }
        
        $crumbBrand.EDropdown({
            lazyload: 'data-src',
            onOpen: function () {
                getBrandReco(cfg, $crumbBrand.find('.content .br-reco'))
            }
        });
    }

    module.exports.__id = 'crumb';
    module.exports.init = init;
});
