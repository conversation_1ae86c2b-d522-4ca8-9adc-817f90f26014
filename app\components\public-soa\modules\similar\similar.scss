@import '../common/lib';
@import './__sprite.scss';

#similar {
    margin-bottom: 0;
    padding-bottom: 15px;
    .mt {
        .extra {
            .page-num {
                padding: 10px;
                .b {
                    color: #e4393c;
                }
            }
        }
    }

    .mc {
        height: 210px;
        margin-left: 35px;
        margin-right: 55px;
        position: relative;

        .list {
            overflow: hidden;
            .plist {
                width: 2000px;
                height: 160px;
                margin-top: 20px;
                li {
                    float: left;
                    width: 100px;
                    margin: 0 28px;
                    _margin: 0 25px;
                }
            }
        }
    }

    .arrow {
        position: absolute;
        top: 56px;
        width: 22px;
        height: 32px;
        cursor: pointer;
        i {
            @include inline-block;
            vertical-align: middle;
            margin-right: 5px;
        }
        .sprite-arrow-prev {
            @include sprite-arrow-prev;
        }
        .sprite-arrow-next {
            @include sprite-arrow-next;
        }
    }

    .arrow-next {
        @extend .arrow;
        right: -45px;
    }

    .arrow-prev {
        @extend .arrow;
        left: -25px;
    }
}

.root61 {
    #similar {
        margin-bottom: 0;
        padding-bottom: 15px;
        .mc {
            margin-left: 55px;
        }
        .arrow-prev {
            left: -45px;
        }
    }
}