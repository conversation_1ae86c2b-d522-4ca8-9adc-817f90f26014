'use strict'
define('MOD_ROOT/detail/qualitylife', function(require, exports, module) {
    var Event = require('MOD_ROOT/common/tools/event').Event
    // var G = require('MOD_ROOT/common/core')

    require('JDF_UI/dialog/1.0.0/dialog')

    var PREFIX_REG = /^(?:https?:)?(?:\/\/)?/;  // 用于替换链接前缀为`//`

    /**
     * 品质生活模块
     */
    function QualityLife(cfg, $el) {
        this.cfg = cfg
        this.$el = $el || $('#quality-life')
        this.$icon = this.$el.find('li')

        // icon 弹出层图片
        this.image = {
            v2: []
        }
        // 更新模块左侧的图片素材
        this.$el.find('.q-logo img').attr('src', '//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg');
        this.init()
    }

    QualityLife.prototype = {
        init: function() {
            this.getData()
            this.showRegularIcon()
            this.bindEvent()
        },
        // “京东溯源”图标，模板根据特殊属性`specialAttrs`判定是否输出HTML；只要有该DOM节点，前端就显示该图标；
        showRegularIcon: function () {
            if (this.$el.find('.fresh-ico-3').length) {
                this.$el.show()
            }
        },
        bindEvent: function() {
            this.$el.delegate('ul li', 'click', $.proxy(this.handleClick, this))
        },
        handleClick: function(e) {
            var $this = $(e.currentTarget)
            var type = $this.attr('data-type')
            var text = $this.attr('data-text')
            var url = $this.attr('data-url')

            // iframe
            if (type === '1') {
                this.setQualityPromise(url, text)
            } else {
                this.showDialog(type, text)
            }
            // 生鲜品类的图标点击交互
            if ($this.hasClass('J_fresh')) {
                this.showIconDialog($this);
            }
            // POP商品资质标
            if ($this.hasClass('J_qua')) {
                this.showIconDialog($this, {
                    filePath: 'quaFileVoList',
                    iconPath: 'refImg',
                    tagName: 'markName',
                    tagDesc: 'remark'
                });
            }

        },
        getData: function() {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            var url = host + '/qualification/life_v2';

            $.ajax({
                url: url,
                dataType: 'jsonp',
                data: {
                    skuId: this.cfg.skuid,
                    pid: this.cfg.mainSkuId || this.cfg.skuid,
                    catId1: this.cfg.cat[0],
                    catId2: this.cfg.cat[1],
                    catId3: this.cfg.cat[2],
                    venderId: this.cfg.venderId,
                    videoPath: this.cfg.videoPath,
                    appid: 'item-v3',
                    functionId: "pc_qualification_life_v2"
                },
                success: $.proxy(this.handleData, this)
            })
        },
        handleData: function(r) {
            if (!r) return r

            if (r['quality']) {
                this.handleIconMap(r['quality'])
            }
            // 生鲜溯源
            if (r['trace']) {
                this.setTempIcon(r['trace'])
            }

            // 自营生鲜资质标
            if ((r.fresh || r.trace) && !this.cfg.isPop) {
                this.setFreshCategoryIcons(r);
            }

            // POP商品资质亮标
            // if (r.qua && this.cfg.isPop) {
            if (r.qua && this.cfg.isQualityLifeShow) {    
                this.setQuaIcons(r);
            }

            // type 10 写死图片数据
            // this.image['10'] = this.getVendorQualificationImages()
        },
        setTempIcon: function(r) {
            // 给生鲜溯源 详情视频传数据
            if (r.data && r.data.videos &&  r.data.videos.length > 0) {
                Event.fire({
                    type: 'onVideoData',
                    data: r.data
                })
            }
        },
        handleIconMap: function(r) {
            if ( !(r && r.objs && r.objs.length > 0) ) {
                return;
            }

            var arr = r.objs;
            var hasType10 = $('[data-type="10"]', this.$el).length > 0;
            var canShow = false;
            for (var i = 0; i < arr.length; i++) {
                var obj = arr[i] || {};
                var url = obj.url;
                var type = obj.type;
                var images = obj.images || [];

                if (type == 6 && images.length > 0) {
                    this.setErShouTab(images[0]);
                } else if (type == 10 && !hasType10) {
                    // pass
                } else if(type) {
                    if (images.length > 0) {
                        this.image[type] = images;
                    }

                    if (url) {
                        this.setIconUrl(type, url);
                    }

                    this.showIcon(type);
                    
                    if ($("[data-type='" + type + "']", this.$el).length > 0) {
                        canShow = true;
                    }
                }
            }
            
            if (canShow) {
                this.show();
            }

        },

        /**
         * 生鲜品类的商品打标
         * @param {Object} resp接口返回数据
         */
        setFreshCategoryIcons: function (resp) {
            var tmp = [];
            var fresh = resp.fresh || [];
            var temperature = (resp.trace && resp.trace.temperature);
            var hasTraceIcon = (this.$el.find('.fresh-ico-3').length > 0);

            // 依据优先级（溯源＞品质生活＞温度）计算显示的icon图标个数; 品质生活图标个数限制为5个

            if (fresh.length < 4) {
                tmp = fresh;
            } else {
                if (hasTraceIcon) {
                    tmp = fresh.slice(0, 4);
                } else {
                    tmp = fresh.slice(0, 5);
                }
            }

            // 渲染icon图标

            var length = tmp.length;
            var $temperature = this.$el.find('[data-type="v1"]');

            // if (typeof temperature === 'string' && !!$.trim(temperature)) {  // 温度图标的显示判断条件
            //     if (length < 4 || (!hasTraceIcon && length === 4)) {
            //         $temperature.show().find('.J-fresh-wd').text(temperature + '℃');
            //         this.show();
            //     }
            // }

            if (!!length) {
                for (var index = 0; index < length; index++) {  // 动态追加DOM
                    var __html = '\
                    <li class="J_fresh" title="'+ tmp[index]['tagName'] +'"> \
                        <a href="#none"> \
                        <i style="background:none"> \
                            <img src="'+ tmp[index]['iconPath'].replace(PREFIX_REG, '//') +'" style="width:100%"/> \
                        </i> \
                        <span>'+ tmp[index]['tagName'] +'</span> \
                        </a> \
                    </li>';
    
                    $temperature.before($(__html).data('data', tmp[index]));
                }
                this.show();
            } 
        },

        /**
         * POP商品资质亮标
         * @param {Object} resp接口返回数据
         */
        setQuaIcons: function (resp) {
            var data = resp.qua;
            var length = data.length;
            var $iconContainer = this.$icon.parent();

            if (!!length) {
                for (var index = 0; index < length && index < 5; index++) {  // 动态追加DOM
                    if (data[index].enableStatus === 1) {
                        var __html = '\
                        <li class="J_qua" title="'+ data[index]['markName'] +'" clstag="shangpin|keycount|product|fengchao_' + data[index].quaMarkId + '"> \
                            <a href="#none"> \
                            <i style="background:none"> \
                                <img src="'+ data[index]['refImg'].replace(PREFIX_REG, '//') +'" style="width:100%"/> \
                            </i> \
                            <span>'+ data[index]['markName'] +'</span> \
                            </a> \
                        </li>';
        
                        $iconContainer.append($(__html).data('data', data[index]));
                    }
                }
                this.show();
            } 
        },

        /**
         * 图标点击弹框
         * @param {Object} $dom
         * @param {Object} dict  字段映射字典
         * {
         *   filePath: 'filePath',
         *   iconPath: 'iconPath',
         *   tagName: 'tagName',
         *   tagDesc: 'tagDesc'
         * }
         */
        showIconDialog: function ($dom, dict) {
            dict = dict || {
                filePath: 'filePath',
                iconPath: 'iconPath',
                tagName: 'tagName',
                tagDesc: 'tagDesc'
            };
            var data = $dom.data('data');
            var images = data[dict.filePath];
            
            var __html = '\
            <div class="fresh-dialog-head" style="overflow:hidden;line-height:20px;border-bottom: 1px solid #f3f3f3;padding: 20px 0 20px 10px;"> \
                <img src="'+ data[dict.iconPath].replace(PREFIX_REG, '//') +'" style="float:left;margin-right:10px;" /> \
                <div class="fresh-dialog-head__title" style="overflow:hidden;margin-top: 2px;color: #454545;font-weight: bold;">' + data[dict.tagName] + '</div> \
                <div class="fresh-dialog-head__desc" style="overflow:hidden;color:#8d8d8d;">'+ data[dict.tagDesc] + '</div> \
            </div>';

            for (var i = 0, length = images.length; i < length; i++) {
                if (typeof images[i] === 'string') {
                    __html += '<img src="'+ images[i].replace(PREFIX_REG, '//') + '" style="width:100%" />'
                } else if(typeof images[i] === 'object') {
                    __html += '<img src="'+ images[i].key.replace(PREFIX_REG, '//') + '" style="width:100%" />'
                }
            }

            __html = '<div style="width: 520px;height:720px;margin: 0 auto;overflow:hidden;overflow-y:auto;">' + __html +'</div>';

            $('body').dialog({
                title: data[dict.tagName],
                width: 600,
                height: 750,
                type: 'html',
                source: __html
            });
        },

        // 二手售后保障
        setErShouTab: function (img) {
            var tab = $('#detail').data('ETab')
            tab.triggers.eq(2).show()
            tab.items.eq(2).html('<div style="padding: 10px 0" class="ac"><img src="'+ img +'" /></div>')
        },
        setIconUrl: function(type, url) {
            this.showIcon(type).children().attr({
                target: '_blank',
                href: url
            })
        },
        showDialog: function(type, text) {
            var images = this.image[type]
            if (!images || !images.length) return false

            var tpl =
                '\
            <div style="width: 520px;height:720px;margin: 0 auto;overflow:hidden;overflow-y:auto;">{0}</div>'
            var html = ''
            var item = '<img style="display:block" width="500" src="{0}" />'

            if (type !== 'v2') {
                tpl = '<p style="padding: 10px 0" class="ac">卖家承诺：以下为实物拍摄，购买时仅供参考</p>' + tpl
            }

            for (var i = 0; i < images.length; i++) {
                var img = images[i]
                html += item.format(getImageSrc(img))
            }

            function getImageSrc(img) {
                return /^http:|https:|\/\//.test(img)
                    ? img
                    : '//img20.360buyimg.com/cms/s500x2000_' + img
            }

            $('body').dialog({
                title: text,
                width: 600,
                height: 750,
                type: 'html',
                source: tpl.format(html)
            })
        },
        // 质量承诺 iframe
        setQualityPromise: function(url, text) {
            $('body').dialog({
                width: 300,
                height: 300,
                title: text,
                type: 'iframe',
                autoIframe: false,
                source: url
            })
        },
        show: function() {
            this.$el.show()
        },
        showIcon: function(n) {
            return this.$el.find('li[data-type="' + n + '"]').show()
        }
    }

    function init(cfg) {
        cfg.qualityLife = new QualityLife(cfg)
    }

    module.exports.init = init
})
