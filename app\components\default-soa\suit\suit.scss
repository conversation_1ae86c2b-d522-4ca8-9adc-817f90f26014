@import '../common/lib';

//服装 优惠套装
.suit-content {
    .stab {
        overflow: hidden;
        padding-bottom: 15px;
    }

    .stab li {
        margin-left: -1px;
        white-space: nowrap;
        padding: 0 15px;
        height: 16px;
        cursor: pointer;
        border-left: 1px solid #D4D1C8;
        line-height: 16px;
        text-align: center;
        color: $colorLinkBlue;
        margin-bottom: 10px;
        &.scurr {
            color: $colorPriceRed
        }
    }

    #stabcon_suits {
        position: relative;
    }
    .stabcon {
        clear: both;
    }

    .master {
        float: left;
        padding: 0;
        text-align: center;
        overflow: hidden;
        width: 200px;
        .root61 & {
            padding: 0 0 0 10px;
        }
        .p-name {
            width: 140px;
            height: 36px;
            padding: 0 10px;
        }
        s {
            float: right;
            display: inline;
            width: 24px;
            height: 22px;
            margin-top: 100px;
            margin-right: 3px;
            background: url(i/newicon20140910.png) no-repeat 0 -260px;
        }
    }

    .pop-wrap {
        position: relative;
        float: left;
    }

    .suits {
        float: left;
        width: 606px;
        .root61 & {
            width: 788px;
        }
        ul {
            float: left;
            height: 100%;
            overflow: hidden;
            li {
                width: 197px;
                padding-left: 0;
                position: relative;
                s {
                    float: right;
                    display: inline;
                    width: 24px;
                    height: 22px;
                    background: url(i/newicon20140910.png) no-repeat 0 -260px;
                    margin-right: 3px;
                    margin-top: 100px;
                }
                .p-img {
                    position: relative;
                    padding: 5px 0;
                }
                .p-scroll {
                    width: 155px;
                    _width: 155px;
                    height: 29px;
                    background: #fff;
                    overflow: hidden;
                    padding: 5px 0;
                    border: 1px solid #fff;
                    border-bottom: 0;
                    .p-scroll-btn {
                        background-image: url(i/scroll-btns.png);
                        background-repeat: no-repeat;
                        float: left;
                        width: 16px;
                        height: 29px;
                        text-indent: -9999px;
                    }
                    .p-scroll-prev {
                        margin-right: 2px;
                        background-position: 0 0;
                        &.disabled {
                            background-position: 0 -31px;
                        }
                    }
                    .p-scroll-next {
                        background-position: -18px 0;
                        &.disabled {
                            background-position: -18px -31px;
                        }
                    }
                    .p-scroll-wrap {
                        float: left;
                        width: 186px;
                        height: 29px;
                        overflow: hidden;

                    }
                    .suits ul {
                        float: left;
                        height: 100%;
                        overflow: hidden;
                    }
                }

                li {
                    position: relative;
                    width: 30px;
                    height: 30px;
                    padding: 0;
                    a {
                        padding: 1px;
                        border: 1px solid #ddd;
                        float: left;
                        img {
                            width: 25px;
                            height: 25px;
                        }
                        &.curr {
                            border: 1px solid #e4393c;
                        }
                    }

                    .p-name {
                        width: 156px;
                        height: 36px;
                    }

                    .choose {
                        width: 156px;
                    }

                    .p-price strong {
                        color: #E4393C;
                    }
                }
                .p-choose .hl_blue {
                    cursor: pointer;
                }
            }
        }
        .last-item s {
            display: none;
        }
    }

    .infos {
        margin-top: 50px;
        float: right;
        width: 180px;
        line-height: 20px;
        .root61 & {
            width: 190px;
        }
        s {
            float: left;
            width: 24px;
            height: 22px;
            margin-top: 40px;
            background: url(i/newicon20140910.png) no-repeat -30px -260px;
        }
        .p-name {
            height: 36px;
            overflow: hidden;
            line-height: 13px;
            margin-left: 35px;
            margin-bottom: 0;
        }
        .p-price {
            margin-left: 35px;
            .p-simsun {
                font-family: simsun;
            }
            strong {
                color: #FF0F23;
            }
        }
        .p-saving {
            margin-left: 35px;
            .hl_green {
                color: #090;
            }
        }
        .btns {
            margin-left: 35px;
            color: #999;
        }

        .btn-buy {
            margin-top: 10px;
        }
    }
    .clb {
        clear: both;
    }

    #pop-box, #pop-box-suit { visibility:hidden; position:absolute; z-index:5; left:0px; top:169px; width:175px; overflow:hidden;  background:#fff; border: 1px solid #C4C4C4; -moz-box-shadow: 0 0 5px #ddd; -webkit-box-shadow: 0 0 5px #DDD; box-shadow: 0 0 5px #DDD;}
    #p-scroll, #p-scroll-suit { padding:10px 0  0 10px; }
    #stabcon_suits .p-scroll {
        width: 155px;
        _width: 155px;
        height: 29px;
        background: #fff;
        overflow: hidden;
        padding: 5px 0;
        border: 1px solid #fff;
        border-bottom: 0;
    }
    #stabcon_suits { position:relative; }
    #tab-suits { overflow:visible; }
    .stabcon_suits .choose { height:16px; }
    .p-choose .hl_blue { cursor:pointer; }
    #stabcon_suits .p-scroll {
        width: 155px;
        _width: 155px;
        height: 29px;
        background: #fff;
        overflow: hidden;
        padding: 5px 0;
        border: 1px solid #fff;
        border-bottom: 0;
    }

    #stabcon_suits .p-scroll-prev {
        margin-right: 2px;
        background-position: 0 0;
    }
    #stabcon_suits .p-scroll-next {
        background-position: -18px 0;
    }
    #stabcon_suits .p-scroll-btn {
        float: left;
        display: none;
        width: 16px;
        height: 29px;
        text-indent: -9999px;
    }
    #stabcon_suits .p-scroll-btn {
        background-image: url(i/scroll-btns.png);
        background-repeat: no-repeat;
    }
    .disabled {
        cursor: default;
    }
    #stabcon_suits .p-scroll-wrap {
        float: left;
        width: 186px;
        height: 29px;
        overflow: hidden;
    }
    #fitting-suit #stabcon_suits li {
        position: relative;
    }
    #fitting-suit #stabcon_suits .p-scroll li {
        width: 30px;
        height: 30px;
        padding: 0;
    }
    #stabcon_suits .p-scroll-wrap li, .p-scroll-wrap li a {
        float: left;
    }
    #stabcon_suits .p-scroll-wrap li a {
        padding: 1px;
        border: 1px solid #ddd;
    }
    #stabcon_suits .p-scroll-wrap li a.curr {
        border: 1px solid #e4393c;
    }
    #stabcon_pop .p-scroll-wrap li, .p-scroll-wrap li a {
        float: left;
    }
    #stabcon_suits .p-scroll-wrap li a img {
        width: 25px;
        height: 25px;
    }
    #p-size, #p-size-btn, #p-size-btn-suit, #p-size-suit, #p-tips, #p-tips-suit {
        padding: 0 10px;
    }
    #p-size-suit a {
        display: inline-block;
        padding: 2px 5px;
        border: 1px solid #ccc;
        margin: 0 5px 10px 0;
    }
    #stabcon_suits .selected {
        border-color: #e4393c;
    }
    #p-size-btn-suit {
        padding-bottom: 10px;
    }
    #p-size-btn a, #p-size-btn-suit a {
        display: inline-block;
        width: 50px;
        margin-right: 5px;
        height: 21px;
        line-height: 21px;
        background: url(i/scroll-btns.png) -36px 0 no-repeat;
        text-align: center;
    }
    .p-selected {
        background: #fffdee;
        width: 138px;
        border: 1px solid #edd28b;
        margin: 7px 0;
        padding: 4px 10px;
    }
    .p-selected a {
        white-space: nowrap;
    }
    .p-selected a, .p-selected a:hover, .p-selected a:visited {
        color: #005ea7;
    }
    #fitting-suit #stabcon_suits li .choose, #fitting-suit #stabcon_suits li .p-name {
        width: 156px;
    }
}
/*海外房产 专家推荐*/
.building{
    .recommend{
        #shop-reco{
            height: auto;
        }
        .plist-2{
            li{
                .p-name{
                    position: static;
                    background: none;
                    padding: 0;
                    filter: none;
                    a{
                        color: #333;
                        &:hover{
                            color: #e4393c;
                        }
                    }
                }
                .p-price{
                    strong{
                        vertical-align: -1px;
                    }
                }
            }
        }
    }
}
