/*!
	Zoom 1.7.18
	license: MIT
	http://www.jacklmoore.com/zoom
*/
;(function ($) {

    /**http://www.jacklmoore.com/zoom*/
    var defaults = {
        url: false,
        lazyUrl: false,
        callback: false,
        target: false,
        duration: 120,
        on: 'mouseover', // other options: grab, click, toggle
        touch: true, // enables a touch fallback
        onZoomIn: false,
        onZoomOut: false,
        magnify: 1
    };

    $.zoom = function(target, source, img, magnify) {
        var targetHeight,
            targetWidth,
            sourceHeight,
            sourceWidth,
            xRatio,
            yRatio,
            offset,
            position = $(target).css('position'),
            $source = $(source);

        target.style.position = /(absolute|fixed)/.test(position) ? position : 'relative';
        target.style.overflow = 'hidden';

        img.style.width = img.style.height = '';

        $(img)
            .addClass('zoomImg')
            .css({
                position: 'absolute',
                top: 0,
                left: 0,
                opacity: 0,
                width: img.width * magnify,
                height: img.height * magnify,
                border: 'none',
                maxWidth: 'none',
                maxHeight: 'none'
            })
            .appendTo(target);

        return {
            init: function() {
                targetWidth = $(target).outerWidth();
                targetHeight = $(target).outerHeight();

                if (source === target) {
                    sourceWidth = targetWidth;
                    sourceHeight = targetHeight;
                } else {
                    sourceWidth = $source.outerWidth();
                    sourceHeight = $source.outerHeight();
                }

                xRatio = (img.width - targetWidth) / sourceWidth;
                yRatio = (img.height - targetHeight) / sourceHeight;

                offset = $source.offset();
            },
            move: function (e) {
                var left = (e.pageX - offset.left),
                    top = (e.pageY - offset.top);

                top = Math.max(Math.min(top, sourceHeight), 0);
                left = Math.max(Math.min(left, sourceWidth), 0);

                img.style.left = (left * -xRatio) + 'px';
                img.style.top = (top * -yRatio) + 'px';
            }
        };
    };

    $.fn.zoom = function (options) {
        return this.each(function () {
            var
                settings = $.extend({}, defaults, options || {}),
                //target will display the zoomed image
                target = settings.target || this,
                //source will provide zoom location info (thumbnail)
                source = this,
                $source = $(source),
                img = document.createElement('img'),
                $img = $(img),
                mousemove = 'mousemove.zoom',
                clicked = false,
                touched = false,
                $urlElement;

            // If a url wasn't specified, look for an image element.
            if (!settings.url) {
                $urlElement = $source.find('img');
                if ($urlElement[0]) {
                    settings.url = $urlElement.data('src') || $urlElement.attr('src');
                }
                if (!settings.url) {
                    return;
                }
            }

            (function(){
                var position = target.style.position;
                var overflow = target.style.overflow;

                $source.one('zoom.destroy', function(){
                    $source.unbind(".zoom");
                    target.style.position = position;
                    target.style.overflow = overflow;
                    $img.remove();
                });

            }());

            img.onload = function () {
                var zoom = $.zoom(target, source, img, settings.magnify);

                function start(e) {
                    zoom.init();
                    zoom.move(e);

                    $img.stop()
                        .fadeTo($.support.opacity ? settings.duration : 0, 1, $.isFunction(settings.onZoomIn) ? settings.onZoomIn.call(img) : false);
                }

                function stop() {
                    $img.stop()
                        .fadeTo(settings.duration, 0, $.isFunction(settings.onZoomOut) ? settings.onZoomOut.call(img) : false);
                }

                // Mouse events
                if (settings.on === 'grab') {
                    $source
                        .bind('mousedown.zoom',
                            function (e) {
                                if (e.which === 1) {
                                    $(document).one('mouseup.zoom',
                                        function () {
                                            stop();

                                            $(document).unbind(mousemove, zoom.move);
                                        }
                                    );

                                    start(e);

                                    $(document).bind(mousemove, zoom.move);

                                    e.preventDefault();
                                }
                            }
                        );
                } else if (settings.on === 'click') {
                    $source.bind('click.zoom',
                        function (e) {
                            if (clicked) {
                                // bubble the event up to the document to trigger the unbind.
                                return;
                            } else {
                                clicked = true;
                                start(e);
                                $(document).bind(mousemove, zoom.move);
                                $(document).one('click.zoom',
                                    function () {
                                        stop();
                                        clicked = false;
                                        $(document).unbind(mousemove, zoom.move);
                                    }
                                );
                                return false;
                            }
                        }
                    );
                } else if (settings.on === 'toggle') {
                    $source.bind('click.zoom',
                        function (e) {
                            if (clicked) {
                                stop();
                            } else {
                                start(e);
                            }
                            clicked = !clicked;
                        }
                    );
                } else if (settings.on === 'mouseover') {
                    zoom.init(); // Preemptively call init because IE7 will fire the mousemove handler before the hover handler.

                    $source
                        .bind('mouseenter.zoom', start)
                        .bind('mouseleave.zoom', stop)
                        .bind(mousemove, zoom.move);
                }

                // Touch fallback
                if (settings.touch) {
                    $source
                        .bind('touchstart.zoom', function (e) {
                            e.preventDefault();
                            if (touched) {
                                touched = false;
                                stop();
                            } else {
                                touched = true;
                                start( e.originalEvent.touches[0] || e.originalEvent.changedTouches[0] );
                            }
                        })
                        .bind('touchmove.zoom', function (e) {
                            e.preventDefault();
                            zoom.move( e.originalEvent.touches[0] || e.originalEvent.changedTouches[0] );
                        });
                }

                if ($.isFunction(settings.callback)) {
                    settings.callback.call(img);
                }
            };

            img.src = settings.url;
        });
    };

    $.fn.zoom.defaults = defaults;
}(window.jQuery));
