define('MOD_ROOT/hotKey/hotKey', function(require, exports, module) {
    // var tools = require('MOD_ROOT/common/tools/tools');
    function init(cfg) {
        // try{
        //     function setPlaceholder(val) {
        //         $('#key').val(val)
        //         .bind('focus',function(){
        //             if (this.value==val){ this.value='';this.style.color='#333' }
        //         })
        //         .bind('blur',function(){
        //             if (this.value==''){ this.value=val;this.style.color='#999' }
        //         });
        //     }
        //     function render(data) {
        //         var r = data && data.resultKeywords
        //         if (!r || !r.length) return;
        //         var html = '';
        //         var el = document.getElementById('hotwords')
        //         for (var i = 0; i < r.length; i++) {
        //             var item = r[i];
        
        //             if (i === 0) {
        //                 setPlaceholder(item.keyword)
        //             } else {
        //                 html += '<a target="_blank" data-keywordType="'+ item.keywordType +'" href="//search.jd.com/Search?keyword='+ item.keyword +'&enc=utf-8">'+ item.keyword +'</a>'
        //             }
        //         }
        
        //         if (el) el.innerHTML = html
        //     }
        //     //https://qpsearch.jd.com/relationalSearch?skuid=10046945512608&ver=auto&uuid=517fdabab5b52f81&userpin=jd_62e30ab827615&rettype=json&client=pc&onlinehotword=false&onlinefallback=false
        //     $.ajax({
        //         url: '//qpsearch.jd.com/relationalSearch',
        //         data: { 
        //             skuid: cfg.skuid,
        //             uuid: "",
        //             userpin: "",
        //             ver: "auto",
        //             rettype: "json",
        //             client: "pc",
        //             onlinehotword: false,
        //             onlinefallback: false,
        //             num: 6
        //         },
        //         dataType: 'jsonp',
        //         success: render
        //     })
        // }catch(e){
        //     console.log("热词接口报错")
        // }
    }
    module.exports.__id = 'hotkey';
    module.exports.init = init;
})