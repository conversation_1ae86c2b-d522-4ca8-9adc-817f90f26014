@charset "utf-8";
@import '../common/lib';

$color_background: #E87D7F;

/**
 * 下拉菜单
 */
.menu-drop{
    display: inline-block;
    *display:inline;
    *zoom: 1;
    height: 24px;
    cursor: pointer;

    .trigger{
        display: inline-block;
        *display:inline;
        *zoom: 1;
        height: 22px;
        padding: 0 4px 0 8px;
        border: 1px solid #ddd;
        line-height: 22px;
        vertical-align: top;

        .curr{
            display: inline-block;
            vertical-align: top;
        }
    }

    .menu-drop-arrow{
        display: inline-block;
        width: 20px;
        height: 20px;
        vertical-align: top;
        background: url(i/item.ele.png) no-repeat 4px 7px;
        @include transition(background-position .15s ease-in-out);
    }

    &:hover{
        text-decoration: none;
        //color: $color01;

        .trigger{
            border-color: $color01;
        }
        .menu-drop-arrow{
            background-position: 4px -11px;
        }

    }
}
//下拉菜单展开部分
.menu-drop-main{
    display: none;
    width: 350px;
    padding: 10px 0 10px 8px;
    position: absolute;
    left:0;
    top: 23px;
    border: 1px solid #ddd;
    background-color: #fff;
}
.menu-drop-list{

    li{
        float: left;
        width: 65px;
        height: 24px;
        overflow: hidden;
        line-height: 24px;
        margin-right: 5px;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.curr{
            a{
                color:$color01;
                text-decoration: underline;
            }
        }

    }
}
//在 .menu-drop 上添加 class：.z-menu-drop-open 使下拉菜单展开
.z-menu-drop-open{
    position: relative;
    z-index: 5;

    .trigger{
        border-bottom-color: #fff;
        position: relative;
        z-index: 6;
    }
    .menu-drop-arrow{
        background-position: 4px -27px;

    }

    .menu-drop-main{
        display: block;
    }

    &:hover{

        .trigger{
            border-bottom-color: #fff;
        }

        .menu-drop-main{
            border-color: $color01;
        }

        .menu-drop-arrow{
            background-position: 4px -44px;
        }
    }

}


/**
 * 车管家
 */
.car-filter{
	width:100%;
	margin-bottom: 15px;
}

.car-filter-hd{
	height: 24px;
	line-height: 24px;
	margin-bottom: 8px;
	font-family: $font-yahei;

	strong{
		font-size: 18px;
		font-weight: bold;
		font-family: $font-yahei;
	}
	.sep{
		display: inline-block;
		padding: 0 5px;
		font-family: $font-st;
	}
}

.car-filter-bd{
	height: 36px;
	padding: 15px;	
	border: 1px solid #ddd;

	.menu-drop{
		float:left;
		height:36px;
		margin-right:10px;

		.trigger{
			position: relative;
			height: 34px;
			line-height: 34px;
			padding: 0 9px 0 10px;
			overflow: hidden;

			.curr{
				font-size: 14px;
				height: 34px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: block;			
			}
		}

		.menu-drop-arrow{
			position: absolute;
			right:10px;
			top: 5px;
		}
		.menu-drop-main{
			top:35px;
			padding-top: 8px;
			padding-left: 0;
		}
	}

	.menu-drop-list{
		width: 100%;
		overflow: hidden;
		li{
			width: 84px;
			padding-left:10px;
			margin-right: 0;
			height: 28px;
			line-height: 28px;
			a{
				color: #000000;
				&:hover{
					text-decoration: none;
				}
			}

			&.curr{
				a{
					text-decoration: none;
				}
			}
		}
	}	

	.car-filter-item1{
		width:113px;

		.trigger{
			width:92px;
			.curr{
				width: 52px;
			}
		}
		.menu-drop-main{
			width: 545px;
			height: 260px;
			overflow-x: hidden;
			overflow-y: auto;
			padding-left: 20px;
			padding-right: 0px;

		}
		.menu-drop-list {
			li {
				padding-left: 0;
				width: 118px;
				height: 48px;
				margin: 0 10px 10px 0;
				border: 1px solid #ddd;

				img {
					width: 48px;
					height: 48px;
					vertical-align: top;
					margin-right: 2px;
				}

				a {
					display:inline-block;
					font-family: simson;
					line-height: 48px;
				}

				&:hover, &.curr {
					border-color: $color01;
				}
			}
		}
	}	
	.car-filter-item2{
		width: 113px;

		.trigger{
			width:92px;
			.curr{
				width: 52px;
			}			
		}
		.menu-drop-main{
			width: 567px;
			max-height: 268px;
			overflow-x: hidden;
			overflow-y: auto;
			padding-left: 20px;	
			padding-top: 0;		
		}

		.menu-drop-list {
			li {
				padding-left: 0;
				width: 118px;
				height: 33px;
				margin: 0 10px 10px 0;
				border: 1px solid #ddd;
				text-align:center;
				a {
					font-family: simson;
					line-height: 33px;
				}

				&.curr, &:hover {
					border-color: $color01;
				}
			}
		}
		.menu-brand-title {
			height: 38px;
			line-height: 38px;
			font-family: simson;
			margin-bottom: 2px;
			position: relative;
			padding-left: 10px;

			i {
				position:absolute;
				left: 0;
				top:15px;
			    height:0;
			    width:0;
			    overflow: hidden;
			    font-size: 0;
			    line-height: 0;
			    border-color: transparent transparent transparent #000;
			    border-style: dashed dashed dashed solid;
			    border-width: 4px 5px;
			}
		}
	}

	.car-filter-item3{
		width: 113px;

		.trigger{
			width:92px;
			.curr{
				width: 52px;
			}			
		}
		.menu-drop-main{
			width: 169px;
			max-height: 260px;
			overflow-x: hidden;
			overflow-y: auto;			
		}

		.menu-drop-list{
			li{
				height: 30px;
				line-height: 30px;				
				width: 565px;

				&.curr, &:hover {
					a {
						color: #FFFFFF;
					}
					background: $color_background;
				}
			}
		}
	}

	.car-filter-item4{
		width: 113px;

		.trigger{
			width:92px;
			.curr{
				width: 52px;
			}			
		}
		.menu-drop-main{
			width: 450px;
			max-height: 260px;
			overflow-x: hidden;
			overflow-y: auto;			
		}

		.menu-drop-list{
			li{
				height: 30px;
				line-height: 30px;				
				width: 440px;
				color: #FFF;

				&.curr, &:hover {
					background: $color_background;
					a {
						color: #FFFFFF;
					}
				}
			}
		}
	}
	.z-menu-drop-open{
		z-index:6;
        .menu-drop-hint{
            display: block;
        }
	}
}

.menu-drop-letter-list{
	width:100%;
	overflow: hidden;
	padding: 0 0 10px;

	li{
		float:left;
		height: 22px;
		width: 18px;
		a{
			display: inline-block;
			height: 22px;
			padding: 0 6px;
			text-align: center;
			color: #000;
			font: 12px/20px simson;

			&:hover{
				color: #000;
				text-decoration: none;				
			}
		}

		&.fore0{
			width: 34px;
			a{
				padding: 0 5px;
			}
		}

		&.curr{
			background: $color01;
			a{
				color: #FFFFFF;
				text-decoration: none;
			}
		}
	}

}

.menu-drop-hint{
	display: none;
	padding-left: 5px;
	font: 14px/30px $font-yahei;
	color: #999;
}

/**
 * 前面下拉框未选择时提示选择前一个
 * @hook: .menu-drop-main
 */
.z-menu-drop-hint{
	.menu-drop-hint{
		display: block;
	}
	.menu-drop-list{
		display: none;
	}	
}

.car-filter-bd{
	.car-filter-item2,
	.car-filter-item3{
		.z-menu-drop-hint{
			height: 30px;
			overflow: hidden;	
		}
	}
}

.car-filter-btn{
	display:inline-block;
	float:left;
	width: 96px;
	height: 25px;
	line-height: 25px;
	padding: 5px 0 6px;
	text-align: center;
	color: #FFFFFF;
	background: #E74649;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#E74649',endColorstr='#E74649',GradientType=0);
	border: none;

	&:hover {
		color: #FFFFFF;
	}
}
.car-butler-index{
	float:left;
	*display: inline;
	width: 120px;
	height:34px;
	line-height: 34px;
	border-left: 1px solid #ddd;
	margin-left:15px;
	_margin-left:27px;
	text-align:center;

	.link{
		color: $color03;
	}
}

// responsive

// resp01 w1210
.root61 {
	.car-filter-bd{
		.menu-drop{
			width:170px;

			.trigger{
				width:150px;
				.curr{
					width: 129px;
				}				
			}
		}

		.car-filter-item1 {
			width: 170px;
		}
		.car-filter-item2{
			width: 170px;
		}
		.car-filter-item3 {
			width: 170px;
		}
		.car-filter-item4 {
			width: 170px;
		}

		.car-filter-item3 {
			.menu-drop-main{
				width: 169px;
			}
		}        
	}
}


