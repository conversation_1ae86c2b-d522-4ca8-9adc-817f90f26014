define('PUBLIC_ROOT/modules/detail/detail', function(require, exports, module) {
  var Event = window.pageConfig.eventTarget;
  var bookReadMore = require('PUBLIC_ROOT/modules/detail/bookReadMore')
  var G = require('PUBLIC_ROOT/modules/common/core')
  var Tools = require('PUBLIC_ROOT/modules/common/tools/tools')
  var QualityLife = require('PUBLIC_ROOT/modules/detail/qualitylife')
  var ToolsMod = require('MOD_ROOT/common/tools/tools')
  require('PUBLIC_ROOT/modules/ETab/ETab')
  require('PUBLIC_ROOT/modules/EDropdown/EDropdown')
  require('PUBLIC_ROOT/modules/ELazyload/ELazyload')
  require('PUBLIC_ROOT/plugins/jQuery.scroller')
  require('JDF_UI/elevator/1.0.0/elevator')
  require('JDF_UI/dialog/1.0.0/dialog')
  require('JDF_UI/pager/1.0.0/pager')
  //获取视频
  var videoBox = require('PUBLIC_ROOT/modules/videoBox/videoBox')
  var lockScrollTop = false; // 点击tab暂时锁死滚动锚点
  var topList = [];

  function miniCart(cfg) {
      var $addCartMini = $('.J-addcart-mini')

      Event.addListener('onStockReady', function(data) {
          var usedProductFaq = data && data.stock && data.stock.data && data.stock.data.usedProductFaq
          if(usedProductFaq){ // 二手FAQ售后保障动态配置
              $("#guarantee").find(".service-pic").html('<img width="750" src="'+usedProductFaq+'">')
          }
          if (cfg.havestock) {
              $addCartMini.removeAttr('data-disable')
          } else {
              $addCartMini.attr('data-disable', 'true')
          }
      })
  }

  function setFixedNav($detail) {
      var tabIsShow = false;
      var goodsbaseIsShow = false;
      var tabName = [];
      var tabtop = $('.tab-main').offset().top;
      var updateTabFlag = true
      
      // 更新tab内容
      function handleUpdateTabDom() {
        if (!updateTabFlag) {
          return
        }

        updateTabFlag = false

        $lis = $('.tab-main').find('li')

        $lis.each(function (index, item) {
          var $this = $(item);

          if ($this.css('display') === 'none') {
            return
          }

          tabName.push($this.text())
        });

        
      }

      handleUpdateTabDom()

      var $tabcon = $('.tab-con');
      var $tab = $('.tab-main');
      var tabHeight = Math.round($tab.height());
      var $lis = $('.tab-main').find('li');
      var $leftRoot = $('.preview-wrap')[0];

      // 跟随详情tab
      $detail.scroller({
          delay: 0,
          end: $('#GLOBAL_FOOTER'),
          stopThreshold: 120,
          onStart: function() {
            scaleImgText()

            handleUpdateTabDom()

            this.$el.find('[data-fixed]').addClass('pro-detail-hd-fixed');
            $tabcon.css('padding-top', tabHeight + 'px');

            // 左右滚动时动态设置tab的left值
            setFixBarLeft()
          },
          onEnd: function() {
              updateTabFlag = true

              this.$el.find('[data-fixed]').removeClass('pro-detail-hd-fixed')
              $tabcon.css('padding-top', 0)
              $tab.css('left', 0);
          },
          onScroll: function() {
            if (lockScrollTop) {
              return;
            }

            if (isClickAfterSales) {
              isClickAfterSales = false
              
              if ($afterSalesNode) {
                var $seat = $afterSalesNode.children('#after-sales-seat')
                if ($seat) {
                  $seat.remove()
                }
              }
            }

            if (topList.length) {
              var max = 0; 
              var scrollTop = Math.round(window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0);
              var clientHeight = document.documentElement.clientHeight;

              if (scrollTop >= tabtop - clientHeight && !tabIsShow) {
                tabIsShow = true
                ToolsMod.exposure({
                  functionName: 'PC_Productdetail_AnchorTab_Expo',
                  exposureData: ['mainskuid'],
                  extraData: {
                    tabName: tabName
                  },
                  errorTips: '快速锚定tab曝光-异常'
                });
              }


              topList.forEach(function (item, index) {
                if (scrollTop >= item.top) {
                  max = index
                }

                var isScrollDetail = item.title === '商品详情' && scrollTop >= item.top - clientHeight + 66

                // 滚动到商品介绍上报埋点
                if (isScrollDetail) {
                  if (!goodsbaseIsShow) {
                    scaleImgText()
                    goodsbaseIsShow = true

                    ToolsMod.exposure({
                      functionName: 'PC_Productdetail_DetailDescription_Expo',
                      exposureData: ['mainskuid'],
                      extraData: {},
                      errorTips: '图文详情楼层-整体曝光-异常'
                    });
                  }
                }
              });

              $lis.removeClass('current');
              $lis.each(function (index, item) {
                var $this = $(item)
                if ($this.text() === topList[max].title) {
                  $this.addClass('current')
                }
              })
            }
          }
      })

      // 窗口变化时调整位置
      $(window).resize(setFixBarLeft)

      function setFixBarLeft() {
        if ($leftRoot.getBoundingClientRect) {
          var tabLeft = $leftRoot.getBoundingClientRect().left
          if ($tab.hasClass('pro-detail-hd-fixed')) {
            $tab.css('left', tabLeft);
          }
        }
      }

      $('.detail').elevator({
          floorClass: 'detail-elevator-floor', //楼层className
          elevatorClass: 'detail-elevator', //电梯主体className
          handlerClass: 'detail-elevator-handler', //电梯按钮className
          selectClass: 'current' //电梯按钮被选中的className
      })
  }

  var eBookTPL =
      '\
  {if list.editorPick}\
  <div class="formwork_bt" id="editorPick" name="detail-tag-id-0" text="编辑推荐">\
      <div class="formwork_bt_it" ><span>编辑推荐</span></div>\
      <div class="con">${list.editorPick}</div>\
  </div>\
  {/if}\
  {if list.contentInfo}\
  <div class="formwork_bt" id="contentInfo" name="detail-tag-id-1" text="内容简介">\
      <div class="formwork_bt_it"><span>内容简介</span></div>\
      <div class="con">${list.contentInfo}</div>\
  </div>\
  {/if}\
  {if list.authorInfo}\
  <div class="formwork_bt" id="authorInfo" name="detail-tag-id-2" text="作者简介">\
      <div class="formwork_bt_it"><span>作者简介</span></div>\
      <div class="con">${list.authorInfo}</div>\
  </div>\
  {/if}\
  {if list.catalog}\
  <div class="formwork_bt" id="catalog" name="detail-tag-id-3" text="目录">\
      <div class="formwork_bt_it"><span>目录</span></div>\
      <div class="con">${list.catalog}</div>\
  </div>\
  {/if}\
  {if list.mediaComments}\
  <div class="formwork_bt" id="mediaComments" name="detail-tag-id-4" text="媒体评论">\
      <div class="formwork_bt_it"><span>媒体评论</span></div>\
      <div class="con">${list.mediaComments}</div>\
  </div>\
  {/if}\
  {if list.preface}\
  <div class="formwork_bt" id="preface" name="detail-tag-id-5" text="前言">\
      <div class="formwork_bt_it"><span>前言</span></div>\
      <div class="con">${list.preface}</div>\
  </div>\
  {/if}\
  {if list.digest}\
  <div class="formwork_bt" id="digest" name="detail-tag-id-6" text="精彩书摘">\
      <div class="formwork_bt_it"><span>精彩书摘</span></div>\
      <div class="con">${list.digest}</div>\
  </div>\
  {/if}\
  {if list.illustration}\
  <div class="formwork_bt" id="illustration" name="detail-tag-id-7" text="精彩插图">\
      <div class="formwork_bt_it"><span>精彩插图</span></div>\
      <div class="con">${list.illustration}</div>\
  </div>\
  {/if}'

  // 电子书评
  var BookComment = {
      inited: false,
      init: function(cfg) {
          this.cfg = cfg

          this.ITEMS_PER_PAGE = 6
          this.page = 1

          this.$el = $('#e-comment')
          this.$trigger = $('#detail [data-anchor="#e-comment"]')

          if (!this.inited) {
              this.bindEvent()
              this.get(this.page)
              this.inited = true
          }
      },
      bindEvent: function() {
          this.$el.delegate(
              '.J-more',
              'click',
              $.proxy(this.handleClick, this)
          )
      },
      handleClick: function(e) {
          var $this = $(e.currentTarget)
          var $cont = $this.prev()

          if ($cont.is('.close')) {
              $cont.removeClass('close').addClass('open')
              $this.addClass('hover').find('s').text('收起')
          } else {
              $cont.removeClass('open').addClass('close')
              $this.removeClass('hover').find('s').text('展开全文')
          }
      },
      getTPL: function() {
          var template =
              '\
          <div class="book-review">\
              <div class="J-book-review">\
                  {for item in reviews.list}\
                  <div class="review-item">\
                      <div class="user-line">\
                          <span class="avatar"><img src="${item.userHeadFullUrl}" alt=""/></span>\
                          <span class="name">${item.nickname}</span>\
                          <span class="star star${item.score}"></span>\
                          <span class="time">${item.creationTime|getTime}</span>\
                      </div>\
                      <div class="review-con">\
                          <i class="arrowUp"></i>\
                          <div class="review-content">${item.contents|stripUnsafeTags|lineToBr}</div>\
                          <div class="more EDropdown J-more hide">\
                              <a href="#none"><s>展开全文</s> <i class="arrow arr-close"></i></a>\
                          </div>\
                      </div>\
                  </div>\
                  {/for}\
              </div>\
              <div class="com-table-footer">\
                  <div class="ui-page-wrap clearfix">\
                      <div class="ui-page"></div>\
                  </div>\
              </div>\
          </div>'

          return template
      },
      get: function(page) {
          var body =
              '{"currentPage":' +
              page +
              ',"pageSize":' +
              this.ITEMS_PER_PAGE +
              ',"eBookId":' +
              this.cfg.skuid +
              '}'
          this.page = page || 1
          // //gw-e.jd.com/client.action?functionId=greatComments&body={"currentPage":1,"pageSize":10,"eBookId":30295088}
          $.ajax({
              url: '//gw-e.jd.com/client.action',
              data: {
                  functionId: 'greatJsonPComments',
                  body: body
              },
              cache: true,
              dataType: 'jsonp',
              jsonpCallback: 'func',
              scriptCharset: 'utf-8',
              success: $.proxy(this.set, this)
          })
      },
      set: function(r) {
          if (
              !this.$el.length || !r || !r.reviews || !r.reviews.list || !r.reviews.list.length
          ) {
              this.$trigger.hide()
              this.$el.hide()
              return false
          }
          this.$trigger.show()
          this.$el.show()

          r._MODIFIERS = {
              getTime: function(str) {
                  return str.substring(0, str.lastIndexOf(' '))
              },
              lineToBr: function(str) {
                  return str.replace(/\n/ig, '<br>')
              },
              stripUnsafeTags: function(str) {
                  return str.replace(
                      /<script>|<\/script>|<style>|<\/style>|<iframe>|<\/iframe>/g,
                      ''
                  )
              }
          }

          this.$el.find('.mc').html(this.getTPL().process(r))
          this.$trigger.find('s').html('(' + r.greatCount + ')')

          this.setPager(r.greatCount)
          this.setMore()
      },
      setMore: function() {
          var $els = this.$el.find('.review-item .review-content')
          $els.each(function() {
              var height = $(this).outerHeight()
              if (height > 120) {
                  $(this)
                      .attr('data-open', height)
                      .addClass('close')
                      .parents('.review-item')
                      .eq(0)
                      .addClass('has-more')
              }
          })
      },
      setPager: function(total) {
          var _this = this
          var $pager = this.$el.find('.ui-page')
          $pager.pager({
              total: total,
              pageSize: this.ITEMS_PER_PAGE,
              currentPageClass: 'ui-page-curr',
              currentPage: this.page,
              pageHref: '#e-comment',
              callback: function(pageId) {
                  _this.get(pageId)
              }
          })
      }
  }

  function getOld(cfg){
      setTecentVideo(cfg,"#tencent-video")
      if (cfg.isPop) {
      }
  }

  function getDetail(cfg) {
      // 新关联版式
      if(cfg.shopSwitch){
          var body = JSON.stringify({
          });
          var time = new Date().getTime()
          // 加固start
          var colorParm = {
              appid: 'item-v3',
              functionId: 'pc_structured',
              client: 'pc',
              clientVersion: '1.0.0',
              t: time,//生成当前时间毫秒数
              body: body,
          }
          try{
              var colorParmSign =JSON.parse(JSON.stringify(colorParm))
              colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
              window.PSign.sign(colorParmSign).then(function(signedParams){
                  colorParm['h5st']  = encodeURI(signedParams.h5st)
                  try{
                      getJsToken(function (res) {
                          if(res && res.jsToken){
                              colorParm['x-api-eid-token'] = res.jsToken;
                          }
                          colorParm['loginType'] = '3';
                          colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                          getStructuredData(colorParm);
                      }, 600);
                  }catch(e){
                      colorParm['loginType'] = '3';
                      colorParm['uuid'] = '';
                      getStructuredData(colorParm);
                      //烛龙上报
                      Tools.getJmfe(colorParm, e, "detail文件pc_structured接口设备指纹异常", 250)
                  }
              })
          }catch(e){
              colorParm['loginType'] = '3';
              colorParm['uuid'] = '';
              getStructuredData(colorParm);
              //烛龙上报
              Tools.getJmfe(colorParm, e, "detail文件pc_structured接口加固异常", 250)
          }            
          // 加固end
          function getStructuredData(colorParm){
              var host = '//api.m.jd.com'
              if(pageConfig.product && pageConfig.product.colorApiDomain){
                  host = pageConfig.product && pageConfig.product.colorApiDomain
              }
              colorParm['productId'] = cfg.mainSkuId || '',
              $.ajax({
                  url: host + '/structured',
                  data: colorParm,
                  dataType: 'json',
                  xhrFields: {
                      withCredentials: true,
                  },
                  success: function(r) {
                      var $topNew = $("#J-detail-pop-tpl-top-new");
                      var $topBottom = $("#J-detail-pop-tpl-bottom-new");
                      var $detailTop = $("#J-detail-top");
                      var $detailBottom = $("#J-detail-bottom");
                      var hasDa = 0;
                      if (r[0] && r[0].template && r[0].template.length>0) {
                          r[0].template.forEach(function(e){
                              if(e.moduleCode=="DA"){
                                  if(e.moduleNumber && e.moduleNumber.substring(0,1)==1){
                                      $topNew.append(e.htmlContent);
                                  }else if(e.moduleNumber && e.moduleNumber.substring(0,1)==2){
                                      $topBottom.append(e.htmlContent);
                                  }
                                  hasDa = 1;
                                  if($(".J_formatDynamic").length > 0){// 判断htmlContent是否下发容器
                                      setTecentVideo(cfg,".J_formatDynamic")
                                  }else{
                                      setTecentVideo(cfg,"#tencent-video")
                                  }
                              }else{
                                  if(e.moduleNumber && e.moduleNumber.substring(0,1)==1 && e.htmlContent && e.htmlContent!=null){
                                      $topNew.append(e.cssContent+"<script>"+e.jsContent+"</script>"+e.htmlContent);
                                  }else if(e.moduleNumber && e.moduleNumber.substring(0,1)==2 && e.htmlContent && e.htmlContent!=null){
                                      $topBottom.append(e.cssContent+"<script>"+e.jsContent+"</script>"+e.htmlContent);
                                  }
                                  // 关联版式接口下发311或者321 开头3 说明放在商品介绍内部 中间=1上方 =2下方
                                  if(e.moduleNumber && e.moduleNumber.substring(0,1)==3 && e.moduleNumber.substring(1,2)==1 && e.htmlContent && e.htmlContent!=null){
                                      $detailTop.append(e.cssContent+"<script>"+e.jsContent+"</script>"+e.htmlContent);
                                  }else if(e.moduleNumber && e.moduleNumber.substring(0,1)==3 && e.moduleNumber.substring(1,2)==2 && e.htmlContent && e.htmlContent!=null){
                                      $detailBottom.append(e.cssContent+"<script>"+e.jsContent+"</script>"+e.htmlContent);
                                  }
                              }
                          })
                          if(hasDa == 0)//有关联版式，未下发DA，调用原始视频展示
                          {
                              setTecentVideo(cfg,"#tencent-video")
                          }

                          restartScale()
                      }else{
                          getOld(cfg)
                      }
                  },
                  error:function(){
                      getOld(cfg)
                  }
              });
          }
      }else{
          getOld(cfg)
      }

      // 商品介绍接口调用
      desc(cfg)
  }

  function desc(cfg){
      if (/debug=disable_detail/.test(location.href)) {
          return false
      }
      if(cfg.desc && cfg.desc.indexOf("pc_description_channel") > 0){
          var body = JSON.stringify({
              "mainSkuId": G.serializeUrl(cfg.desc).param.mainSkuId || '',
              "skuId": G.serializeUrl(cfg.desc).param.skuId || ''
          });
          var time = new Date().getTime()
          // 加固start
          var colorParm = {
              appid: cfg.desc.indexOf("item-v3") > 0 ? 'item-v3' : 'jdw_pc', // 兼容全球购appid
              functionId: 'pc_description_channel',
              client: 'pc',
              clientVersion: '1.0.0',
              t: time,//生成当前时间毫秒数
              body: body,
          }
          try{
              var colorParmSign =JSON.parse(JSON.stringify(colorParm))
              colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
              window.PSign.sign(colorParmSign).then(function(signedParams){
                  colorParm['h5st']  = encodeURI(signedParams.h5st)
                  try{
                      getJsToken(function (res) {
                          if(res && res.jsToken){
                              colorParm['x-api-eid-token'] = res.jsToken;
                          }
                          colorParm['loginType'] = '3';
                          colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                          getDescriptionData(colorParm);
                      }, 600);
                  }catch(e){
                      colorParm['loginType'] = '3';
                      colorParm['uuid'] = '';
                      getDescriptionData(colorParm);
                      //烛龙上报
                      Tools.getJmfe(colorParm, e, "detail文件pc_structured接口设备指纹异常", 250)
                  }
              })
          }catch(e){
              colorParm['loginType'] = '3';
              colorParm['uuid'] = '';
              getDescriptionData(colorParm);
              //烛龙上报
              Tools.getJmfe(colorParm, e, "detail文件pc_structured接口加固异常", 250)
          }            
          // 加固end
          function getDescriptionData(colorParm){
              var host = '//api.m.jd.com'
              if(pageConfig.product && pageConfig.product.colorApiDomain){
                  host = pageConfig.product && pageConfig.product.colorApiDomain
              }
              colorParm['mainSkuId'] = G.serializeUrl(cfg.desc).param.mainSkuId || ''
              colorParm['skuId'] = G.serializeUrl(cfg.desc).param.skuId || ''
              colorParm['charset'] = 'utf-8'
              colorParm['cdn'] = '2'
              $.ajax({
                  url:  host + "/description/channel",
                  data: colorParm,
                  dataType: 'json',
                  xhrFields: {
                      withCredentials: true,
                  },
                  success: function(r) {
                      var wrap = $('#J-detail-content')

                      if (cfg.isEBook) {
                          wrap.html(eBookTPL.process({ list: r }))
                          // 增加监听事件，当图片加载完毕之后再进行图片折叠
                          $('#illustration img').load(function() {
                              appendMoreBtn($('#illustration'))
                          })
                          appendMoreBtn(wrap.find('.formwork_bt'))
                      } else {
                          wrap.html(r.content.replace(/data-lazyload="done"/g, ''))
                          if (cfg.cat[0] == 1713) {
                              if ( cfg.pType === 3 || cfg.pType === 4 ) {
                                  bookReadMore.bookInit();
                              }
                          }
                      }

                      wrap.ELazyload({
                          source: 'data-lazyload'
                      })

                      restartScale()
                  }
              })
          }
      }else if(cfg.desc){
          $.ajax({
              url: cfg.desc,
              dataType: 'jsonp',
              cache: true,
              jsonpCallback: 'showdesc',
              success: function(r) {
                  var wrap = $('#J-detail-content')

                  if (cfg.isEBook) {
                      wrap.html(eBookTPL.process({ list: r }))
                      // 增加监听事件，当图片加载完毕之后再进行图片折叠
                      $('#illustration img').load(function() {
                          appendMoreBtn($('#illustration'))
                      })
                      appendMoreBtn(wrap.find('.formwork_bt'))
                  } else {
                      wrap.html(r.content.replace(/data-lazyload="done"/g, ''))
                      if (cfg.cat[0] == 1713) {
                          if ( cfg.pType === 3 || cfg.pType === 4 ) {
                              bookReadMore.bookInit();
                          }
                      }
                  }

                  wrap.ELazyload({
                      source: 'data-lazyload'
                  })

                  restartScale()
              }
          })
      }
  }

  function stayPos($this) {
      var oTop = $this.parents('.formwork_bt').eq(0).offset().top
      if ($('body').scrollTop()) {
          $('body').scrollTop(oTop)
      } else {
          $('html').scrollTop(oTop)
      }
  }

  function appendMoreBtn($formworkBt) {
      return
      $formworkBt.each(function(i) {
          var more = $(this).find('.more'), con = $(this).find('.con')

          if (more.length != 0 && con.length != 0) {
              var h
              if (con.height() > 440) {
                  h = 440
                  con.css({ height: h, overflow: 'hidden' })
                  more.show().toggle(
                      function() {
                          $(this).html(
                              '<a href="javascript:void(0)">收起全部↑</a>'
                          )
                          con.css({ height: 'auto', overflow: 'hidden' })
                          stayPos($(this))
                      },
                      function() {
                          $(this).html(
                              '<a href="javascript:void(0)">查看全部↓</a>'
                          )
                          con.css({ height: h, overflow: 'hidden' })
                          stayPos($(this))
                      }
                  )
              }
          }
      })
  }

  // 家居家装 loc 地图
  function setMap(cfg) {
      var $el = $('#loc-map')

      if (!$el.length) {
          return false
      }
      function getProvinceName() {
          var ipLocation = readCookie('ipLocation')
          return ipLocation ? ipLocation : '%u5317%u4EAC'
      }
      function getProvinceId() {
          return cfg.decorationCurrentCity || Tools.getAreaId().areaIds[0]
      }
      function getUrl() {
          var host = '//api.m.jd.com'
          if(pageConfig.product && pageConfig.product.colorApiDomain){
          host = pageConfig.product && pageConfig.product.colorApiDomain
      }
          return (
              // '//cd.jd.com/store/template?' +
              host + '/store/template?'+
              $.param({
                  storeGroupId: cfg.storeGroupId || '',
                  venderId: cfg.venderId,
                  pName: getProvinceName(),
                  isNarrow: !(pageConfig.wideVersion &&
                      pageConfig.compatible),
                  provinceId: getProvinceId(),
                  appid: 'item-v3',
                  functionId: "pc_store_template"

              })
          )
      }
      var iframe =
          '<iframe id="loc-map-iframe" src="{0}" style="width:{1}px;height:{2}px" marginheight="0" frameborder="0" scrolling="no"></iframe>'
      var iHTML = iframe.format(
          getUrl(),
          pageConfig.wideVersion && pageConfig.compatible ? 990 : 750,
          526
      )
      $el.html(iHTML)

      Event.addListener('onAreaChange', function() {
          $('#loc-map-iframe').attr('src', getUrl())
      })
  }

  function setBookComment(cfg) {
      var $eComment = $('#e-comment')

      if ($eComment.length && cfg.isEBook) {
          BookComment.init(cfg)
      }
  }

  // 生鲜溯源 详情视频
  function setVideo(cfg) {
      Event.addListener('onVideoData', function(d) {
          require.async(['PUBLIC_ROOT/modules/detail/video'], function(Video) {
              Video.init(d.data, cfg)
          })
      })
  }

  function setTecentVideo(cfg,r) {
      var $el = $('#detail').find(r)
      if (!$el.length || !cfg.imageAndVideoJson.infoVideoId) return false

      // var iframe = '<iframe id="tencent-video-iframe" src="{src}" frameborder="0" scrolling="no" style=""></iframe>'

      function setData(data) {
          if (data && data.playUrl) {
              cfg.tecentVideoData = data.playUrl
              // $el.html(iframe.replace('{src}', cfg.tecentVideoData))
              videoBox.getVideojs({callback:callback})
              function callback() {
                  if(r == "#tencent-video"){
                      var videoTPL =
                      '\
                      <video id="detail-video-player"\
                          class="video-js vjs-default-skin"\
                          poster="{1}"\
                          width="750"\
                          height="422"\
                          controls>\
                          <source src="{0}" type="video/mp4"> </source>\
                          <p class="vjs-no-js"> 您的浏览器不支持 HTML 5 Video 标签，请升级浏览器。</p>\
                      </video>\
                      <style>#detail-video-player{display:block;margin: 0 auto;}</style>' 
                  }else{
                      var videoTPL =
                      '\
                      <video id="detail-video-player"\
                          class="video-js vjs-default-skin"\
                          poster="{1}"\
                          width="750"\
                          height="422"\
                          controls>\
                          <source src="{0}" type="video/mp4"> </source>\
                          <p class="vjs-no-js"> 您的浏览器不支持 HTML 5 Video 标签，请升级浏览器。</p>\
                      </video>'
                  }
                  var vOptions = {
                      autoplay: false,
                      controls: true,
                      preload: 'auto'
                  }
                  $el.html(
                      videoTPL.format(
                          data.playUrl,
                          data.imageUrl||'misc.360buyimg.com/lib/img/e/blank.gif'
                      )
                  )
                  var player  = videojs('detail-video-player',vOptions)
                  player.on('ready', function() {
                      this.addClass('vjs-has-started');
                  });
                  player.on('error', function (r) {
                      if (typeof console !== 'undefined') {
                          console.info('Video loaded Error.')
                      }
                  })

                  restartScale()
              }
          }
      }

      var host = '//api.m.jd.com'
      if(pageConfig.product && pageConfig.product.colorApiDomain){
          host = pageConfig.product && pageConfig.product.colorApiDomain
      }
      $.ajax({
          url: host + '/tencent/video_v2',
          dataType: 'jsonp',
          data: {
              vid: cfg.imageAndVideoJson.infoVideoId,
              type: 1,
              from: 1,
              // appid: 16,
              appid: 'item-v3',
              functionId: "pc_tencent_video_v2"
          },
          success: setData
      })
  }

  // 图文详情基础信息处理
  function initGoodsBase() {
    // 基础规格表格处理 ----开始
    var $goodsBase = $('.goods-base');
    var $items = $goodsBase.find('.item');
    var $tips = $goodsBase.find('.tips');
    var $names = $goodsBase.find('.name');
    var $texts = $goodsBase.find('.text');

    function showPopover($el,text) {
      var offset = $el[0].getBoundingClientRect();
      var w = $el.width();
      var h = $el.height();

      $el.append('\
        <div class="popover" style="left:'+ (offset.left - ((282 - w) / 2) ) +'px;top:'+ (offset.top + h + 4) +'px;">\
          <div class="popover-content">'+ ($el.attr('data-tips') || text) +'</div>\
        </div>\
      ')
    }

    function hidePopover($el) {
      $el.remove()
    }

    if ($items.length && $items.length % 2 === 0) {
      $goodsBase.children().last().before('<div class="item">\
        <div class="flex-center">\
          <div class="name"></div>\
        </div>\
        <div class="adaptive"><div class="text"></div></div>\
      </div>')
    }

    $tips.bind('mouseenter',function(){
      showPopover($(this));
    });

    $tips.bind('mouseleave',function(){
      hidePopover($(this).children('.popover'))
    });

    $names.bind('mouseenter',function(){
      var $el = $(this);
      var text = $el.text().trim();
      var name = $(this).parents('.item').find('.name').text();
      
      if (text.length >= 16 && name !== '包装清单') {
        showPopover($el, text);
      }
    });
    $names.bind('mouseleave',function(){
      hidePopover($(this).children('.popover'))
    });

    $texts.bind('mouseenter',function(){
      var $el = $(this);
      var name = $(this).parents('.item').find('.name').text();
      var text = $el.text().trim();
      if (text.length >= 30 && name !== '包装清单') {
        showPopover($el, text);
      }
      if (text.length >= 60 && name === '包装清单') {
        showPopover($el, text);
      } 
    });
    $texts.bind('mouseleave',function(){
      hidePopover($(this).children('.popover'))
    });
    // 基础规格表格处理 ----结束
  }

  var $afterSalesNode;
  var isClickAfterSales
  // 左侧tab锚点初始化处理
  function initAnchorPoint() {
    var $tabs = $('.tab-main').find('li');

    $tabs.click(function () {
      var $items = $('#detail .tab-con').children('div');
      var $this = $(this);

      if ($this.hasClass('current')) {
        return
      }

      if ($(this).text() === '售后保障') {
        isClickAfterSales = true
        // 获取售后tab节点
        if (!$afterSalesNode) {
          $('.module-title').each(function (index, el) {
            if ($(el).text() === '售后保障') {
              $afterSalesNode  = $(el).parent()
            }
          })
        }

        $afterSalesNode.prepend('<div style="margin-top:70px" id="after-sales-seat"></div>')
      }

      lockScrollTop = true;

      var top = Math.round($($items[$tabs.index($this)]).offset().top)

      $tabs.removeClass('current');
      $this.addClass('current');

      window.scrollTo(0, top - 66);

      
      if ($(this).text() === '商品详情') {
        scaleImgText()
      }

      
      setTimeout(function () {
        lockScrollTop = false
      }, 100);

      // 埋点-点击tab
      ToolsMod.landmine({
        functionName: 'PC_Productdetail_AnchorTab_Click',
        exposureData: ['mainskuid'],
        extraData: {
          tabName: $this.text()
        },
        errorTips: '快速锚定tab点击-异常'
      })
    })
  }

  // 缩放图文详情处理
  function scaleImgText() {
    var $imgTextWrap = $('#img-text-warp')
    var $imgText = $('#img-text')
    var $items = $('#detail .tab-con').children('div');
    var $lis = $('.tab-main').find('li');
    var parentWidth = $imgTextWrap.width()
    var prevMaxHeight = 0

    $imgText.find('.wrap-scale > div').each(function (index, el) {
      var $this = $(el)

      var max = $(this).width()

      if (max >= 624) {
        var value = parentWidth / max

        $this.css({
          'transform': 'scale(' + value + ')',
          'transform-origin': '0 0',
        });

        var maxHeight = el.getBoundingClientRect().height

        $this.parent().css({'height': maxHeight})
      } else {
        $this.parent().css({'height': 'auto'})
      }
    })

    var newTopList = []

    $items.each(function (index, item) {
      var $this = $(item)

      if ($this.css('display') === 'none') {
        return
      }
      
      newTopList.push({
        top: Math.round($this.offset().top - 66),
        title: $lis.eq(index).text()
      }) 
    });

    topList = newTopList
  }

  var scaleTimer;
  // 图文详情接口获取成功后自动缩放10s 
  function restartScale() {
    clearInterval(scaleTimer)
    var max = 10
    var count = 1

    scaleTimer =  setInterval(function () {
      if (count === max) {
        clearInterval(scaleTimer)
        return
      }
      count += 1

      scaleImgText()
    }, 1000)
  }

  // 获取左侧tab名称
  function expoTab($detail) {
    if ($detail.length) {
      var tabName = [];
      $detail.find('.tab-main li').each(function (index, item) {
        tabName.push($(item).text())
      });
    }
  }

  // 1.中版 2.宽版  获取窗口宽度并返回对应版式
  function getWidthType() {
    var w = window.innerWidth;

    if (w < 1680 && w >= 1280) {
      return 1
    }

    if (w >= 1680) {
      return 2
    }
  }

  // 图文详情调整窗口后重新缩放图文详情
  function resize() {
    var prevWidthType = getWidthType();

    window.addEventListener("resize", function () {
      var widthType = getWidthType();
      // 改变
      if ( widthType !== prevWidthType) {
        prevWidthType = widthType
        scaleImgText()
      }
    }, false);
  }

  // 活动专区
  function handleActivityZone(cfg) {
    try {
      var $new = $('#activity_header_new');

      if (!$new.length) {
        return
      }

      ToolsMod.observerElExpose($new, function () {
        
        var time = new Date().getTime()
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pc_item_getWareGraphic',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time, //生成当前时间毫秒数
            loginType: '3',
            body: JSON.stringify({skuId: cfg.skuid, area: ToolsMod.getAreaId().areaIds.join('_')}),
            uuid: Tools.getCookieNew("__jda") || ''
        }

        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
          host = pageConfig.product && pageConfig.product.colorApiDomain
        }

        $.ajax({
            url: host,
            data: colorParm,
            dataType: 'json',
            xhrFields: {
              withCredentials: true,
            },
            success: function(res) {
              if (res && res.data && res.data.graphicInfoList && res.data.graphicInfoList.length) {
                $new.html('')
                res.data.graphicInfoList.forEach(function (item, index) {
                  $new.append(item.html)
                  $new.find('img').eq(index).attr('data-bp',JSON.stringify({ mainskuid: cfg.skuid + '', activityid: item.activityId + '', card_pos: index }))
                });

                var $imgs = $new.find('img');
                
                $imgs.click(function () {
                  window.logJSON && window.logJSON('pcsx', 'PC_Productdetail_DetailDescriptionActivity_Click', $(this).attr('data-bp'));
                })
      
                ToolsMod.exposurePointObserve($imgs,0 ,'data-bp','pcsx', 'PC_Productdetail_DetailDescriptionActivity_Expo')
              }else {
                $new.html('')
              }
            },
            error: function () {
              $new.html('')
            }
        })
      })


    } catch (e) {
      console.error('活动专区异常', e)
    }
  }

  function init(cfg) {
      // 不记录上次滚动位置
      window.history.scrollRestoration = "manual";

      // Event.addListener('onPriceReady', onPriceReady)
      var $detail = $('#detail');

      // 缩放添加外层容器
      $detail.find('#img-text').children().each(function (index,el) {
        $(el).wrap('<div class="wrap-scale"></div>')
      })

      // 后台直出懒加载图片暂时先直接显示出来
      $detail.find('#img-text').find('img').each(function (index, item) {
        var lazyUrl = $(item).attr('data-lazyload')
        var src = $(item).attr('src')

        if (lazyUrl && !src) {
          $(item).attr('src', lazyUrl)
        }
      })

      // 埋点曝光tab
      expoTab($detail)

      // 窗口调整
      resize()
      setBookComment(cfg)

      miniCart(cfg)

      try {
        if (pageConfig.product.isEBook) {
          getDetail(cfg)
        }else {
          ToolsMod.observerElExpose($('#img-text-warp'), function () {
            getDetail(cfg)
          })
        }
      } catch (error) {
        console.warn('加载图文详情失败')
      }
      
      setFixedNav($detail)

      // setTecentVideo(cfg)

      setMap(cfg)

      setVideo(cfg)

      QualityLife.init(cfg)

      // 规格处理
      initGoodsBase(cfg)

      // 锚点处理
      initAnchorPoint(cfg)

      // 活动专区（之前逻辑在后端）
      handleActivityZone(cfg)
  }

  module.exports.__id = 'detail'
  module.exports.init = init
})
