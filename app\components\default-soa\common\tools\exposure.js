define('MOD_ROOT/common/tools/exposure', function(require, exports, module) {
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var allData = {
        mainskuid: '',
        type: [''], // 类型
        from_page: '', // 来源
        touchstone_expids: '', // 试金石/棱镜实验参数
        SgroupId: '', // 分组id
        price: '', // 价格聚合    
        belt_type: '',
    }
    var ready = false
    var taskList = []
    var landmineTaskList = []
    var exposureMap = {
        exposure: window.expLogJSON,
        // landmine: log
        landmine: window.logJSON
    }
    // 初始化曝光数据
    function initExposureData (cfg, callback) {
        allData.mainskuid = String(cfg.skuid)
        Event.addListener('onStockReady', function(data) {
            if (!data || !data.stock || !data.stock.data) {
                console.log('融合接口错误');
                return
            }
            exposureMap = { // 初始化时曝光方法可能还未加载
                exposure: window.expLogJSON,
                landmine: window.logJSON
            }
            data = data.stock.data
            var finalPrice = data.price && data.price.finalPrice || {}// 到手价的对象
            var jdPrice = data.price && data.price.p || ""// 京东价
            var doublePrice = data.price && data.price.doublePrice || {}// 双价格
            var abData = data.abData || {} // 埋点信息
            var beltBannerInfo = data.beltBannerInfo // 通用腰带数据
            
            var doublePriceUp = doublePrice && doublePrice.up || "" // 双价格up值
            var doublePriceP = doublePrice && doublePrice.price || ""  // 双价格价格
            var doublePriceContent = doublePrice && doublePrice.priceContent || ""  // 双价格文案
            var abTags = abData && abData.abTags // 试金石/棱镜实验参数聚合信息
            var item_gb_fastTest = abData && abData.item_gb_fastTest //实验子分组
            var item_gb_fastTest_label = item_gb_fastTest && item_gb_fastTest.label // 加车交互实验
            var item_pk = abData && abData.item_pk // PK实验子分组
            var item_pk_label = item_pk && item_pk.label // PK交互实验
            var abDataDsj = abData && abData.dsj // 到手价埋点信息
            var abDataBybt = abData && abData.bybt // 百亿补贴埋点信息
            var abDataGjbt = abData && abData.gjbt // 国补埋点信息
            var abNowBuy = abData && abData.nowBuy || false // 统一埋点信息
            var item_gb = abData && abData.item_gb || false // 商详改版实验
            var item_gb_label = item_gb && item_gb.label

            var beltType = beltBannerInfo && beltBannerInfo.type || "";// 埋点类型
            var fPage = document.referrer // 来源
            var type = [] // 类型
            // var touchstone_expids = [] // 试金石/棱镜实验参数
            var SgroupId = [] // 分组id
            var sku_type= [] // 商品类型
            var priceArr = [] // 价格聚合 
            cfg.item_gb_fastTest_label = item_gb_fastTest_label // 全局控制加车交互实验   
            cfg.item_gb_label = item_gb_label
            cfg.item_pk_label = item_pk_label // 全局控制PK交互实验   
            // 到手价上报信息聚合
            if(abDataDsj){
                if(abDataDsj.type){
                    var t = abDataDsj.type
                    type.push('2') 
                }
                // if(abDataDsj.abBuriedTag){
                //     var tag = abDataDsj.abBuriedTag
                //     touchstone_expids.push(tag) 
                // }
                if(abDataDsj.label){ //分组id
                    var l = abDataDsj.label
                    SgroupId.push('"' + l + '"') 
                }
                sku_type.push('"到手价"')
            }

            // 百亿补贴埋点信息聚合
            if(abDataBybt){
                if(abDataBybt.type){
                    var t = abDataBybt.type
                    type.push('1') 
                }
                // if(abDataBybt.abBuriedTag){
                //     var tag = abDataBybt.abBuriedTag
                //     touchstone_expids.push(tag) 
                // }
                if(abDataBybt.label){ //分组id
                    var l = abDataBybt.label
                    SgroupId.push('"' + l + '"') 
                }
                sku_type.push('"百亿补贴"')
            }

            // 国补埋点信息聚合
            if(abDataGjbt){
                if(abDataGjbt.type){
                    var t = abDataGjbt.type
                    type.push('3') 
                }
                // if(abDataGjbt.abBuriedTag){
                //     var tag = abDataGjbt.abBuriedTag
                //     touchstone_expids.push(tag) 
                // }
                if(abDataGjbt.label){ //分组id
                    var l = abDataGjbt.label
                    SgroupId.push('0') 
                }
                sku_type.push('"国家补贴"')
            }

            // 埋点信息聚合
            if(abNowBuy){
                if(abNowBuy.type){
                    var t = abNowBuy.type
                    type.push('0') 
                }
                // if(abNowBuy.abBuriedTag){
                //     var tag = abNowBuy.abBuriedTag
                //     touchstone_expids.push(tag) 
                // }
                if(abNowBuy.label){ //分组id
                    var l = abNowBuy.label
                    SgroupId.push('"' + l + '"') 
                }
                sku_type.push('"立即购买"')
            }

            // 商详改版实验
            if(item_gb){
                if(item_gb.type){
                    var t = item_gb.type
                    type.push(t) 
                }
                // if(item_gb.abBuriedTag){
                //     var tag = item_gb.abBuriedTag
                //     touchstone_expids.push(tag) 
                // }
                if(item_gb.label){ //分组id
                    var l = item_gb.label
                    SgroupId.push('"' + l + '"') 
                }
                sku_type.push('"商详改版实验"')
            }

            // 到手价
            if(finalPrice && finalPrice.price){
                priceArr.push({"price": finalPrice.price, "price_type": finalPrice.up, "clerk": finalPrice.priceContent})
            }
            // 京东价
            if(jdPrice){
                priceArr.push({"price": jdPrice, "price_type": "", "clerk":"京东价"})
            }
            // 双价格
            if(doublePriceP){
                priceArr.push({"price": doublePriceP, "price_type": doublePriceUp, "clerk": doublePriceContent})
            }

            // 统一上报参数
            
            allData.type = type // 类型
            allData.from_page = fPage // 来源
            allData.touchstone_expids = abTags // 试金石/棱镜实验参数
            allData.SgroupId = SgroupId // 分组id
            allData.price = priceArr // 价格聚合    
            // allData.bbskuType = sku_type // 商品类型
            allData.belt_type = beltType
            
            ready = true
            taskList.forEach(function(task) {
                baseExposure('exposure', task)
            })
            taskList = []
            landmineTaskList.forEach(function(task) {
                baseExposure('landmine', task)
            })
            landmineTaskList = []
            callback && callback(data)

            // 搜索logo 命中实验pre3 添加到顶部导航栏
            if (item_gb && (item_gb.label === 'pre3' || item_gb.label === 'test_show' || item_gb.label === 'test_show_1') && !window.newShopLogoIsInsert) {
              window.newShopLogoIsInsert = true
              $("#ttbar-home .dt").prepend('<a class="logo-icon" target="_blank" href="//www.jd.com" ></a>')
            } else if (!data.abData && !window.newShopLogoIsInsert) {
              window.newShopLogoIsInsert = true
              $("#ttbar-home .dt").prepend('<a class="logo-icon" target="_blank" href="//www.jd.com" ></a>')
            }
        })
        Event.addListener('onStockError', function(data) {
            ready = true
            taskList.forEach(function(task) {
                baseExposure('exposure', task)
            })
            taskList = []
            landmineTaskList.forEach(function(task) {
                baseExposure('landmine', task)
            })
            landmineTaskList = []
            callback && callback(data)
        })
    }
    // 曝光
    function exposure(params) {
        if (ready) {
            baseExposure('exposure', params)
        } else {
            taskList.push(params)
        }
    }
    // 埋点
    function landmine(params) {
        if(ready) {
            baseExposure('landmine', params)
        } else {
            landmineTaskList.push(params)
        }
    }
    

    function baseExposure (type, params) {
        var projectName = params.projectName || 'pcsx'
        var functionName = params.functionName // 功能标识
        var exposureData = params.exposureData || []// 曝光数据
        var extraData = params.extraData || {}// 附加数据
        var errorTips = params.errorTips || '埋点曝光错误'
        var finalData = {
            cv: '1',
            touchstone_expids: allData.touchstone_expids
          }

        exposureData.forEach(function(item)  {
            if (allData[item] === undefined || allData[item] === null) {
                console.log('埋点参数错误', item, '请使用 extraData 传入');
            } else {
                finalData[item] = allData[item]
            }
        });
        
        Object.assign(finalData, extraData)

        try {
            exposureMap[type](projectName, functionName, JSON.stringify(finalData))
        } catch (e) {
            if (typeof console !== 'undefined') {
                console.log(errorTips, functionName);
            }
        } 
    }
    exports.exposure = exposure;
    exports.landmine = landmine;
    exports.initExposureData = initExposureData;
})