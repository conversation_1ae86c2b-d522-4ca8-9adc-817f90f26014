define('MOD_ROOT/fittings/fittings', function(require, exports, module) {
    var Event = require('MOD_ROOT/common/tools/event').Event
    var tools = require('MOD_ROOT/common/tools/tools')
    var G = require('MOD_ROOT/common/core')
    require('MOD_ROOT/ETab/ETab')

    var templates =
        '\
    {for item in data}\
    <li data-push="${pageConfig[skuHooks].push(item.wid)}" class="p-list" onclick=\'log("gz_item", "gz_detail","02","tjpj_sp_${item_index}","","main")\'>\
        <div class="p-img">\
            <a href="//item.jd.com/${item.wid}.html" target="_blank" id="fittings0${item.wid}">\
                <img width="100" height="100" src="${pageConfig.FN_GetImageDomain(item.wid)}n4/${item.imageUrl}">\
            </a>\
        </div>\
        <div class="p-name">\
            <a href="//item.jd.com/${item.wid}.html" target="_blank" title="${item.wName}">${item.wName}</a>\
        </div>\
        <div class="p-price">\
            <input type="checkbox" data-sku="${item.wid}" id="inp-acc-${item.wid}" onclick=\'log("gz_item", "gz_detail","02","tjpj_fxk_${item_index}","","main")\'/>\
            <label for="inp-acc-${item.wid}"><strong class="J-p2-${item.wid}">￥.00</strong></label>\
        </div>\
    </li>\
    {/for}'

    var template =
        '\
    {for item in data}\
    <li data-push="${pageConfig[skuHooks].push(item.wid)}" class="p-list" onclick=\'log("gz_item", "gz_detail","02","tjpj_sp_${item_index}","","main")\'>\
        <div class="p-img">\
            <a href="//item.jd.com/${item.wid}.html" target="_blank" id="fittings${item.wid}">\
                <img width="100" height="100" src="${pageConfig.FN_GetImageDomain(item.wid)}n4/${item.imageUrl}">\
            </a>\
        </div>\
        <div class="p-name">\
            <a href="//item.jd.com/${item.wid}.html" target="_blank" title="${item.wName}">${item.wName}</a>\
        </div>\
        <div class="p-price">\
            <input type="checkbox" data-sku="${item.wid}" id="inp-acc-${item.wid}" onclick=\'log("gz_item", "gz_detail","02","tjpj_fxk_${item_index}","","main")\'/>\
            <label for="inp-acc-${item.wid}"><strong class="J-p2-${item.wid}">￥.00</strong></label>\
        </div>\
    </li>\
    {/for}'

    pageConfig.getAccSelectedSkus = function() {
        return pageConfig.accSelectedSkus.join('-') || G.sku
    }

    var FittingAcc = {
        init: function(cfg) {
            this.$el = $('#fittings')
            this.$more = this.$el.find('.J-more')
            this.$count = this.$el.find('.J-selected-cnt')
            this.$btn = this.$el.find('.J-btn')
            this.$cmbPrice = this.$el.find('.J_cal_jp')

            this.sku = cfg.skuid
            this.cat = cfg.cat
            this.cfg = cfg

            this.MORE_TAB_NUM = 5
            this.MAX_TAB = 10
            this.skuHooks = 'SKUS_Fitting'

            if (this.$el.length) {
                this.bindEvent(cfg)
            }
            return this
        },
        switchTo: function(n,d) {
            var $ele = this.$tab.items.eq(n)
            var data = this.data[n - 1]

            if (!$ele.data('loaded') && n !== 0) {
                // 清空已取过价格的sku
                pageConfig[this.skuHooks] = []
                $ele.find('ul').html(template.process(data))
                
                if (!this.cfg.isYuYue && !this.cfg.isYuShou && !this.cfg.isPinGou) {
                    this.imgScroll($ele)
                }
                try{// 浮层可能重复
                    var layerList = d.floatLayerList
                    if(layerList.length > 0){
                        tools.getPcSkuLayers(layerList, 100, 100, 'fittings', "2")
                    }
                }catch(e){
                    console.log("主图浮层初始化渲染",e)
                }
                $ele.data('loaded', true)
            }
        },
        bindEvent: function(cfg) {
            var _this = this
            this.$el
                .undelegate()
                .delegate('input', 'change', function() {
                    var sku = $(this).attr('data-sku')
                    _this.check(sku, $(this))
                })
                .delegate(
                    '#reco-more .content',
                    'click',
                    $.proxy(this.handleMoreClick, this)
                )

            this.$el.delegate('.J-btn', 'click', function() {
                _this.setLog()
            })

            Event.addListener('onStockReady', function() {
                if (cfg.havestock &&
                    _this.data &&
                    _this.data.length) {
                    // _this.$el.show() 
                } else {
                    _this.$el.hide()
                }
            })

            return this
        },
        handleMoreClick: function(e) {
            var $tar = $(e.target)

            if ($tar.is('[data-tab="trigger"]')) {
                this.$trggerMore.addClass('current')
                this.setMoreName($tar.text())
                this.$trggerMore.data('EDropdown').close()

                $tar
                    .parents('#reco-more')
                    .find('[data-tab="trigger"]')
                    .removeClass('current')
                $tar.addClass('current')
            }
        },
        check: function(sku, $el) {
            var buyUrl = '//cart.jd.com/reBuyForOrderCenter.action?'
            var totalPrice = Number(
                this.$el.find('.master input').eq(0).data('jp')
            )
            var skus = [this.cfg.skuid]
            this.$el.find('.suits input:checked').each(function() {
                var jp = Number($(this).data('jp'))
                var sku = Number($(this).data('sku'))
                if (!isNaN(sku)) {
                    skus.push(sku)
                }
                if (!isNaN(jp)) {
                    totalPrice += jp
                }
            })
            if ( !isNaN(totalPrice) ) { 
                this.$cmbPrice.html('￥' + totalPrice.toFixed(2)) 
            } else {
                this.$cmbPrice.html('￥暂无报价')
            }
            
            this.$count.html(skus.length - 1)
            this.$btn.attr(
                'href',
                buyUrl +
                    $.param({
                        wids: skus.join(','),
                        nums: 1
                    })
            )
            pageConfig.accSelectedSkus = skus
        },
        setLog: function() {
            var $panel = this.$tab.items.eq(this.$tab.index)
            var skus = []

            if ($panel) {
                $panel.find('input').each(function() {
                    var isChecked = $(this).attr('checked')

                    if (isChecked) {
                        skus.push($(this).attr('data-sku'))
                    }
                })
            }

            // 发送当前选中的sku log信息
            if (skus.length) {
                log(
                    'gz_item',
                    'gz_detail',
                    '02',
                    'tjpj_ycgm_ljgm',
                    this.cfg.skuid + '-' + skus.join('-'),
                    'main'
                )
            }
        },
        set: function(data) {
            var _this = this
            if (!data || !data.list || !this.$el.length) {
                return false
            }

            pageConfig[this.skuHooks] = [this.sku]

            // 挂载sku全局变量钩子
            data.skuHooks = this.skuHooks
            //data.moreLink = this.getMoreLink(this.cat);

            this.data = []

            // 默认第一个[精选配件]tag数据是每个分类下面第一条配件数据的集合
            var hotAcc = {
                skuHooks: this.skuHooks,
                data: []
            }
            var hasMore = false
            var tabContent = ''
            var tabNormalHTML = ''
            var tabMoreHTML = ''
            var itemHtml =
                '<{2} href="#none" data-tab="trigger" data-name="{0}" data-id="{1}" onclick=\'log("gz_item", "gz_detail","02","tjpj_pjfl_{0}","","main")\'>{0}</{2}>'
            var tabMoreTPL =
                '\
                <li id="reco-more" class="recommend-more EDropdown">\
                    <div class="head"><span class="J-text">更多</span><span class="arrow arr-close"></span></div>\
                    <div data-drop="content" class="content hide">{items}</div>\
                </li>'
            var contentHTML =
                '\
            <div class="switchable-wrap" data-tab="item" isLoad="false">\
                <div class="btns">\
                    <a href="javascript:void(0)" target="_self" class="prev-btn"></a>\
                    <a href="javascript:void(0)" target="_self" class="next-btn"></a>\
                </div>\
                <div class="lh-wrap">\
                    <ul class="lh clearfix"></ul>\
                </div>\
            </div>'
            for (var i = 0; i < data.list.length; i++) {
                var curr = data.list[i]
                var accList = curr.accessoryShows

                if (accList.length && accList[0].wid && i < this.MAX_TAB) {
                    accList[0].typeName = curr.typeName
                    accList[0].typeId = curr.typeId
                    hotAcc.data.push(accList[0])

                    this.data.push({
                        skuHooks: this.skuHooks,
                        data: accList,
                        typeId: curr.typeId,
                        typeName: curr.typeName
                    })

                    tabContent += contentHTML
                    if (i < this.MORE_TAB_NUM) {
                        tabNormalHTML += itemHtml.format(
                            curr.typeName,
                            curr.typeId,
                            'li'
                        )
                    } else {
                        hasMore = true
                        tabMoreHTML += itemHtml.format(
                            curr.typeName,
                            curr.typeId,
                            'a'
                        )
                    }
                }
            }
            if (hotAcc.data.length) {
                var tabHTML =
                    tabNormalHTML +
                    (hasMore ? tabMoreTPL.replace('{items}', tabMoreHTML) : '')
                this.$el.find('.tab-main ul').append(tabHTML)
                this.$el.find('.suits').append(tabContent)

                if (hasMore) {
                    this.$trggerMore = this.$el.find('#reco-more')
                    this.$trggerMore.EDropdown()
                }

                this.$el.ETab({
                    defaultIndex: 0,
                    onSwitch: function(n) {
                        _this.switchTo(n,data)
                        _this.getPrice(n,data)

                        var $curr = this.triggers.eq(this.index)

                        if (
                            $curr.parents('#reco-more').length < 1 &&
                            _this.$trggerMore
                        ) {
                            _this.$trggerMore
                                .removeClass('current')
                                .find('[data-tab="trigger"]')
                                .removeClass('current')
                            _this.setMoreName('更多')
                        }
                    }
                })

                this.$tab = this.$el.data('ETab')
                var $ele = this.$tab.items.eq(0)

                $ele.find('ul').html(templates.process(hotAcc))
                try{
                    var layerList = data.floatLayerList
                    if(layerList.length > 0){
                        tools.getPcSkuLayers(layerList, 100, 100, 'fittings0', "2")
                    }
                }catch(e){
                    console.log("主图浮层初始化渲染",e)
                }

                if (this.cfg.havestock) {
                    // this.$el.show()
                }

                if (!this.cfg.isYuYue && !this.cfg.isYuShou && !this.cfg.isPinGou) {
                    _this.imgScroll($ele)
                }

                // 老配件
                if (data.transform) {
                    this.$more.hide()
                } else {
                    this.$more.show()
                }
            }
        },
        setMoreName: function(name) {
            if (this.$trggerMore) {
                this.$trggerMore.find('.J-text').html(name)
            }
        },
        getPrice: function(n,data) {
            var _this = this

            // 获取价格
            // tools.priceNum({
            //     skus: pageConfig[this.skuHooks],
            //     $el: this.$tab.items.eq(n),
            //     callback: function(sku, r) {
            //         var currentEl = _this.$el.find(
            //             'input[data-sku="' + sku + '"]'
            //         )

            //         currentEl.attr('data-jp', r.p)
            //         currentEl.attr('data-mp', r.m)
            //     },
            //     onReady: function(r) {
            //         _this.check()
            //     }
            // })

            //价格渲染
            tools.priceNumRecommend({
                priceList: data.priceList,
                $el: this.$tab.items.eq(n),
                callback: function(sku, r) {
                    var currentEl = _this.$el.find(
                        'input[data-sku="' + sku + '"]'
                    )
                    var priceDom = r.finalPrice && r.finalPrice.estimatedPrice
                    if(priceDom){
                        currentEl.attr('data-jp', priceDom)
                    }else{
                        currentEl.attr('data-jp', r.p)
                    }
                    
                    currentEl.attr('data-mp', r.m)

                    // 人气配件当前sku的主图价格
                    var currentSku= _this.$el.find(
                        'input[data-sku="' + _this.sku + '"]'
                    )
                    if(_this.cfg.fPrice){
                        currentSku.attr('data-jp', _this.cfg.fPrice)
                    }else{
                        currentSku.attr('data-jp', _this.cfg.jp)
                    }
                    
                    currentSku.attr('data-mp', _this.cfg.mp)// 从模版数据里面获取的价格，如有问题可以拿融合接口里面的数据
                },
                onReady: function(r) {
                    _this.check()
                }
            });

            
            
        },
        //,
        //setMore: function (html) {
        //    this.$more.html(html);
        //}
        imgScroll: function($ele) {
            $ele.find('.lh-wrap').imgScroll({
                width: $ele.find('.lh li').eq(0).outerWidth(true),
                visible: G.wideVersion ? 5 : 4,
                showControl: true,
                step: G.wideVersion ? 5 : 4,
                loop: false,
                prev: $ele.find('.prev-btn'),
                next: $ele.find('.next-btn')
            })
        }
    }

    /// "人气配件"数据请求
    function getData(callback) {
        var cfg = window.pageConfig.product;
        
        var pType = cfg.pType;
        if (pType === 1) {
            var rId = 103003;
        } else if (pType === 2) {
            var rId = 102001;
        } else if (pType === 3) {
            var rId = 104001;
        } else if (pType === 4) {
            var rId = 104026;
        } else {
            var rId = '';
        }

        var parameters = {
            methods: 'accessories',
            p: rId,
            sku: cfg.skuid,
            cat: cfg.cat.join(','),
            lid: tools.getAreaId().areaIds[0],
            ck: 'pin,ipLocation,atw,aview',
            lim: cfg.wideVersion ? 6 : 5,
        };

        var body = JSON.stringify(parameters);

        tools.getJsTokenSign({
            body: body,
            appid: "item-v3",
            functionid: "pctradesoa_recommend",
            message1: "fitting人气配件接口设备指纹异常",
            message2: "fitting人气配件接口加固异常",
            render: function (colorParm) {
                getFittingsData(colorParm,function(res){
                    callback(res)
                });
            }
        });
       
    }

    function getFittingsData(colorParm,callback) {
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        $.ajax({
            url: host,
            data: colorParm,
            dataType: 'json',
            xhrFields: {
                withCredentials: true,
            }, 
            success: function(res) {
                if ($.isPlainObject(res) && !$.isEmptyObject(res)) {
                    FittingAcc.init(window.pageConfig.product).set(res)
                } 
                callback(res)
                // if (typeof onSuccess === 'function') {
                //     onSuccess.apply(null, arguments);
                // }
            },
            error: function() {
                console.log('人气配件数据请求错误',e)
                // if (typeof onError === 'function') {
                //     onFail.apply(null, arguments);
                // }
            }
        });
    }

    function initShopRecSuitTab() {
        //店长推荐
        var $shopRecSuit = $('#shopRecSuit')
        if ($shopRecSuit.length) {
            $shopRecSuit.ETab()
        }
    }
    
    /// “店长推荐”数据请求
    function getShopRecommendData(callback) {
        var parameters = {
            skuId: pageConfig.product.skuid,
            venderId: pageConfig.product.venderId 
        };

        var body = JSON.stringify(parameters);

        tools.getJsTokenSign({
            body: body,
            appid: "item-v3",
            functionid: "pctradesoa_shopRecommend",
            message1: "fitting店长推荐接口设备指纹异常",
            message2: "fitting店长推荐接口加固异常",
            render: function (colorParm) {
                getRecommendData(colorParm,function(res){
                    callback(res)
                });
            }
        });
    }

    /// “店长推荐”数据请求
    function getRecommendData(colorParm, callback) { 
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        $.ajax({
            url: host,
            data: colorParm,
            dataType: 'json',
            xhrFields: {
                withCredentials: true,
            }, 
            success: function(res) {
                callback(res)
            },
            error: function(e) {
                console.log('店长推荐数据请求错误',e)
            }
        });
    }

    /// “店长推荐”
    function renderRecommendMoudle($mount, data) {
        if (
            $mount.length &&
            data && 
            $.isArray(data.goodList) &&
            data.goodList.length
        ) {
            var __html = '\
            <ul class="plist plist-2" id="shop-reco">\
                {for item in data.goodList}\
                    {if item.extAttributes.sku_status!=0 && item.extAttributes.sku_status!=2 && item.extAttributes.sku_status!=10}\
                    <li class="fore${+item_index+1}" data-sku="${item.skuId}" onclick=\'log("dztj_pc","dztj_all_click",pageConfig.product.venderId, "${+item_index+1}","${item.skuId}",pageConfig.product.skuid, "${data.strategyId}")\'>\
                        <div class="p-img">\
                            <a href="${item.skuId|itemUrl}" title="${item.wname}" target="_blank" id="shop-reco-tj-${item.skuId}">\
                                <img width="160" height="160" alt="${item.wname}" class="" src="${item.skuId|imgDomain}n2/${item.imageurl}">\
                            </a>\
                        </div>\
                        <div class="p-name">\
                            <a href="${item.skuId|itemUrl}" target="_blank" title="${item.wname}">${item.wname}</a>\
                        </div>\
                        <div class="p-price">\
                            {if window.pageConfig.isOversea}意向金：{/if}<strong class="J-p2-${item.skuId}">￥</strong>\
                        </div>\
                    </li>\
                    {/if}\
                {/for}\
            </ul>';

            try {
                var $container = $('.J-shopRec-content', $mount);
                $container.html(__html.process({
                    data: data,
                    _MODIFIERS: tools.modifier
                }));

                if($container.find('li').length == 0)
                {   
                    $("#shopRecSuit").hide()
                }
                
                // 渲染价格数据
                var skus = [];
                $container.find('[data-sku]').each(function() {
                    skus.push($(this).data('sku'));
                });
                // tools.priceNum({
                //     skus: skus,
                //     $el: $container
                // });
                //价格渲染
                tools.priceNumRecommend({
                    priceList: data.priceList,
                    $el: $container
                });
                // 显示店长推荐模块
                $mount.removeClass('hide');
                $('.J-shopRec-trigger', $mount).removeClass('hide');
                $container.removeClass('hide');

                try{
                    var layerList = data.floatLayerList
                    if(layerList.length > 0){
                        tools.getPcSkuLayers(layerList, 160, 160, 'shop-reco-tj-', "2")
                    }
                }catch(e){
                    console.log("主图浮层初始化渲染",e)
                }
                

            } catch (err) {
                console && console.error(err);
            }
        } else {
            $mount.hide();
        }
    }

    /// “店铺广告”
    function renderAdMoucle($mount, data) {
        if (
            $mount.length &&
            $.isArray(data) &&
            data.length
        ) {
            var __html = '\
            {for item in data} \
                <a href="${item.address}" target="_blank" title="${item.name}" onclick=\'log("dngg_pc","dngg_all_click",pageConfig.product.venderId, "${item.id}","${item.address}")\'><img src="${item.image}" alt="${item.name}"/></a> \
            {/for}';

            try {
                $mount.html(__html.process({data: data}));
                $mount.show();
            } catch (err) {
                console && console.log(err);
            }
        } else {
            $mount.hide();
        }
    }

    /// `renderRecommendMoudle`的外观模式
    function renderRecommendMoudleFacade(res) {
        if (pageConfig.product.cat[0] == 737) {return;} // 家用电器分类的商品在侧边栏展示“店长推荐”
        initShopRecSuitTab();
        renderRecommendMoudle($('#shopRecSuit'), res && res.shopRec);
    }

    function init(cfg) {
        // getShopRecommendData(function(res){
        //     if ($('#fittings').length > 0) {  // “人气配件”模块
        //         // getData().
        //         // done(function (r) {
        //         //     if (
        //         //         !r ||
        //         //         $.isEmptyObject(r) ||
        //         //         $.isEmptyObject(r.accessories) ||
        //         //         $.isEmptyObject(r.accessories.data) ||
        //         //         $.isEmptyObject(r.accessories.data.list)
        //         //     ) {
        //         //         renderRecommendMoudleFacade(res); // “店长推荐”模块
        //         //     }
        //         // }).
        //         // fail(function(xhr, status, error){
        //         //     console && console.error('人气配件模块数据调用失败，具体如下：\n' + error);
        //         //     renderRecommendMoudleFacade(res); // “店长推荐”模块
        //         // }); 
        //         getData(function(r){
        //             if (
        //                 !r ||
        //                 $.isEmptyObject(r) ||
        //                 $.isEmptyObject(r.list)
        //             ) {
        //                 renderRecommendMoudleFacade(res); // “店长推荐”模块
        //             }
        //         })
        //     } else {
        //         renderRecommendMoudleFacade(res); // “店长推荐”模块
        //     }
        //     /// 店铺广告
        //     renderAdMoucle($('#sp-ad'), res && res.shopAds);
        // })
    }

    module.exports.__id = 'fittings'
    module.exports.init = init
})
