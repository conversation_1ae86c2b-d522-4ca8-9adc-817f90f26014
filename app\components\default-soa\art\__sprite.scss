
@mixin sprite-01-tips-icon {
    width: 11px;
    height: 11px;
    background-image: url(i/__sprite.png);
    background-position: -50px -30px;
}
@mixin sprite-02-arrowL-icon {
    width: 6px;
    height: 9px;
    background-image: url(i/__sprite.png);
    background-position: -18px -50px;
}
@mixin sprite-03-up {
    width: 9px;
    height: 10px;
    background-image: url(i/__sprite.png);
    background-position: -0px -50px;
}
@mixin sprite-04-middle {
    width: 10px;
    height: 9px;
    background-image: url(i/__sprite.png);
    background-position: -50px -41px;
}
@mixin sprite-05-down {
    width: 9px;
    height: 9px;
    background-image: url(i/__sprite.png);
    background-position: -9px -50px;
}
@mixin sprite-06-pop {
    width: 15px;
    height: 14px;
    background-image: url(i/__sprite.png);
    background-position: -50px -0px;
}
@mixin sprite-communication {
    width: 16px;
    height: 16px;
    background-image: url(i/__sprite.png);
    background-position: -0px -34px;
}
@mixin sprite-enter {
    width: 16px;
    height: 16px;
    background-image: url(i/__sprite.png);
    background-position: -16px -34px;
}
@mixin sprite-follow {
    width: 16px;
    height: 16px;
    background-image: url(i/__sprite.png);
    background-position: -34px -0px;
}
@mixin sprite-im-offline {
    width: 17px;
    height: 17px;
    background-image: url(i/__sprite.png);
    background-position: -17px -17px;
}
@mixin sprite-im {
    width: 17px;
    height: 17px;
    background-image: url(i/__sprite.png);
    background-position: -0px -17px;
}
@mixin sprite-phone {
    width: 10px;
    height: 16px;
    background-image: url(i/__sprite.png);
    background-position: -50px -14px;
}
@mixin sprite-pop-im {
    width: 17px;
    height: 17px;
    background-image: url(i/__sprite.png);
    background-position: -17px -0px;
}
@mixin sprite-popim-im {
    width: 17px;
    height: 17px;
    background-image: url(i/__sprite.png);
    background-position: -0px -0px;
}
@mixin sprite-qr {
    width: 16px;
    height: 16px;
    background-image: url(i/__sprite.png);
    background-position: -32px -34px;
}
@mixin sprite-telephone {
    width: 16px;
    height: 16px;
    background-image: url(i/__sprite.png);
    background-position: -34px -16px;
}

// 服务
@mixin sprite-01-quan {
    width: 3px;
    height: 22px;
    background-image: url(i/__sprite2.png);
    background-position: -40px -0px;
}
@mixin sprite-arr-close {
    width: 13px;
    height: 7px;
    background-image: url(i/__sprite2.png);
    background-position: -12px -16px;
}
@mixin sprite-arr-open {
    width: 13px;
    height: 7px;
    background-image: url(i/__sprite2.png);
    background-position: -27px -0px;
}
@mixin sprite-arrow-right {
    width: 12px;
    height: 11px;
    background-image: url(i/__sprite2.png);
    background-position: -0px -16px;
}
@mixin sprite-arrow {
    width: 11px;
    height: 6px;
    background-image: url(i/__sprite2.png);
    background-position: -27px -7px;
}
@mixin sprite-question {
    width: 16px;
    height: 16px;
    background-image: url(i/__sprite2.png);
    background-position: -0px -0px;
}
@mixin sprite-tips {
    width: 11px;
    height: 15px;
    background-image: url(i/__sprite2.png);
    background-position: -16px -0px;
}


// 说明温馨提示
@mixin sprite-01-nonsupport {
    width: 13px;
    height: 13px;
    background-image: url(i/__sprite3.png);
    background-position: -0px -0px;
}
@mixin sprite-02-service {
    width: 13px;
    height: 13px;
    background-image: url(i/__sprite3.png);
    background-position: -13px -0px;
}
@mixin sprite-03-arrowD {
    width: 9px;
    height: 5px;
    background-image: url(i/__sprite3.png);
    background-position: -0px -13px;
}
@mixin sprite-04-arrowU {
    width: 9px;
    height: 5px;
    background-image: url(i/__sprite3.png);
    background-position: -9px -13px;
}