define('PUBLIC_ROOT/modules/spu/spu', function(require, exports, module) {
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');
    var spuSort = {
            "1620": "1-家居家装",
            "5025": "1-钟表",
            "6219": "2-水具酒具",
            "6233": "1-玩具乐器",
            "6994": "1-宠物生活",
            "6196": "1-厨具",
            "1319": "1-母婴",
            "1320": "1-食品饮料、保健食品",
            "1315": "1-服饰内衣",
            "4837": "3-办公文具",
            "1466": "2-体育娱乐",
            "1467": "2-成人用品",
            "1463": "2-运动器械",
            "6728": "1-汽车用品",
            "1713": "1-图书"
        },
        pType = pageConfig.product.pType,
        spuServiceUrl = "//spu.jd.com/json.html?cond=",
        spuPageUrl = "//spu.jd.com/" + pageConfig.product.skuid + ".html";


    window.showProvinceStockDeliver = function(r) {
        if (!r || r.totalCount < 2) return;

        var iPoint = r.minPrice.toString().indexOf('.'),
            topCount = 0,
            cutCount = 0,
            spuVenderInfos = '',
            skuIds = [];

        r.minPrice += iPoint < 0 ? '.00' : (r.minPrice.length - iPoint - 1 > 1 ? '' : '0');


        //if($("#ypds-list").length == 0){
        //    $("#extInfo .jd-service").after('<div class="pop-store-list" id="ypds-list"></div>');
        //}

        if (pageConfig.product.cat[0] != 1713) {
            spuVenderInfos = '<div class="pop-store-item" clstag="shangpin|keycount|product|' + pType + '_onsale"><div class="c-left"><a href="' + spuPageUrl + '" class="hl_blue" target="_blank">' + (r.totalCount - cutCount) + '个卖家在售</a></div><div class="c-right"><span class="price">\u3000￥' + r.minPrice + '起</span></div></div>';
        }

        for (var i = 0, j = r.skuStockVenders.length; i < j; i++) {
            if (pageConfig.product.skuid.toString() == r.skuStockVenders[i].skuId || topCount >= 3) continue;

            if (r.skuStockVenders[i].venderId == 46875) {
                cutCount++;
            } else {
                spuVenderInfos += '<div class="pop-store-item" id="J_' + r.skuStockVenders[i].skuId + '"><div class="c-left"><a class="store-name" href="//item.jd.com/' + r.skuStockVenders[i].skuId + '.html" clstag="shangpin|keycount|product|' + pType + '_maijia' + (topCount + 1) + '" target="_blank">' + ((r.skuStockVenders[i].venderId && r.skuStockVenders[i].skuId.toString().length == 10) ? r.skuStockVenders[i].venderName : '京东商城') + '</a></div><div class="c-right"><span class="price"></span></div></div>';

                topCount++;
            }
        }

        spuVenderInfos += '<div class="btnbox" clstag="shangpin|keycount|product|' + pType + '_allsale"><a href="' + spuPageUrl + '" class="btn-def" target="_blank">查看全部卖家</a></div>';

        $("#ypds-list").html(spuVenderInfos);

        $('<div id="ypds-info" clstag="shangpin|keycount|product|' + pType + '_yipinduoshang"><a href="' + spuPageUrl + '" class="hl_blue" target="_blank">' + (r.totalCount - cutCount) + '个卖家在售</a><span class="hl_red">\u3000￥' + r.minPrice + '</span> 起</div>').insertAfter("#choose");

        $("#ypds-list .pop-store-item[id^='J_']").each(function(ind) {
            skuIds.push($(this).attr("id"));
        });

        if (skuIds.length) {
            var paramJson = {
                skuIds: skuIds.join(","),
                area: readCookie("ipLoc-djd") && readCookie("ipLoc-djd").replace(/-/g,"_") || '',
                pin: readCookie('pin') || '',
                fields: '11100000',
                source: 'pc-item'
            };
            var body = JSON.stringify(paramJson);
            var time = new Date().getTime()
            // 加固start
            var colorParm = {
                appid: 'item-v3',
                functionId: 'pctradesoa_getprice',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                                colorParm['loginType'] = '3';
                                colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                                getPriceData(colorParm);
                            }else{
                                colorParm['loginType'] = '3';
                                colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                                getPriceData(colorParm);
                            }
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getPriceData(colorParm);
                    }
                })
            }catch(e){
            }            
            // 加固end  
            function getPriceData(colorParm){ 
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                } 
                $.ajax({
                    url: host,
                    data: colorParm,
                    dataType: 'json',
                    xhrFields: {
                        withCredentials: true,
                    },  
                    success: function(r) {
                        if(r){
                            if(parseInt(r.code) < 10 && r.echo){
                                try {
                                    var functionId = "pctradesoa_getprice";
                                    var echoCode = r.echo.length>1000 ? r.echo.substring(0,999) : r.echo;
                                    // jmfe.jsagentReport(
                                    //     jmfe.JSAGENT_EXCEPTION_TYPE.business, //固定值不变
                                    //     751,  //固定值: 异常码
                                    //     '网关调用异常' + functionId ,  // 异常信息
                                    //     {
                                    //         fid: functionId , // 网关对应的functionid
                                    //         resp: echoCode, // 只上报code<10(目前有-1,1,2)的数据，上报echo字段
                                    //         body: body // body序列化后的字符；由于浏览器对url有长度限制，body约定限定在1000字符内是绝对满足上报条件的，超过部分前端自行截断。
                                    //     }
                                    // )
                                    window.customPointEvent(
                                        'item_exceptin',
                                        { code: echoCode, error_type_txt: '网关调用异常pctradesoa_getprice' },
                                        {
                                          functionId: functionId,
                                          request: r,
                                          error_msg:  JSON.stringify(echoCode), 
                                        }
                                    )
                                } catch(e) {
                                    console.log('上报pctradesoa_getprice错误',e)
                                }
                            }else{
                                for (var i = 0, j = r.length; i < j; i++) {
                                    $("#ypds-list #" + r[i].id + " .price").html(new Number(r[i].p) > 0 ? ("￥" + r[i].p) : "暂无报价");
                                }
                            }
                        }
                    },
                    error: function (e) {
                        try {
                            var functionId = "pctradesoa_getprice";
                            // jmfe.jsagentReport(
                            //     jmfe.JSAGENT_EXCEPTION_TYPE.business, //固定值不变
                            //     751,  //固定值: 异常码
                            //     '网关调用异常' + functionId ,  // 异常信息
                            //     {
                            //         fid: functionId , // 网关对应的functionid
                            //         resp: JSON.stringify(e), // 只上报code<10(目前有-1,1,2)的数据，上报echo字段
                            //         // xid: "190835604-10476-1676439710095", //网关Response Headers中获取x-api-request-id字段，方便后续从网关日志平台查询数据; 由于跨域请求，好像拿不到这个值。
                            //         body: body // body序列化后的字符；由于浏览器对url有长度限制，body约定限定在1000字符内是绝对满足上报条件的，超过部分前端自行截断。
                            //     }
                            // )
                            window.customPointEvent(
                                'item_exceptin',
                                { code: '751', error_type_txt: '网关调用异常pctradesoa_getprice' },
                                {
                                  functionId: functionId,
                                  request: "",
                                  error_msg:  JSON.stringify(e), 
                                }
                            )
                        } catch(er) {
                            console.log('上报pctradesoa_getprice错误',er)
                        }
                    }
                });
            }
        }


    }


    function init(cfg) {
        $.ajax({
            url: spuServiceUrl + "1_4_1_0_0_" + (pageConfig.product.cat[0] == 1713 ? "1" : "0") + "_" + pageConfig.product.skuid + "_1",
            dataType: 'jsonp',
            jsonpCallback: 'showProvinceStockDeliver',
            cache: true
        });
    }

    module.exports.__id = 'spu';
    module.exports.init = init;
});
