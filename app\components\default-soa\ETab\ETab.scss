@import "../common/lib";
/**
 * ETab tab-main 详情通用 tab 样式
 */
$tabBgRed: $colorPriceRed;
$tabHotRed: #c81623;

.ETab {

    div.small {
        border: 1px solid #f5f5f5;
        background-color: #fafafa;
        padding-left: 10px;
        li {
            padding: 5px 10px;
        }
        li.current,
        li.current a {
            background: #fafafa;
            color: $colorPriceRed;
        }
        .extra {
            .item {
                padding-top: 4px;
            }
        }
    }
    div.medium {
        li {
            padding: 5px 15px;
            font-size: 12px;
        }
    }
    div.large {
        li {
            padding: 10px 25px;
            font-size: 14px;
        }
    }
    .tab-main {
        position: relative;
        background-color: #f7f7f7;
        border: 1px solid #eee;
        border-bottom: 1px solid $tabBgRed;
        @include clearfix;
        ul {
            margin-top: -1px;
        }
        li {
            position: relative;
            @include inline-block;
            cursor: pointer;
            sup {
                background-color: $tabHotRed;
                color: #fff;
                padding: 0 4px;
                position: absolute;
                right: -5px;
                top: -1px;
                font: 12px/16px arial;
                b {
                    position: absolute;
                    left: 0;
                    top: 13px;
                    color: $tabHotRed;
                    font: 12px simsun;
                }
            }

            &:hover {
                color: $colorPriceRed;
            }
        }
        li.current {
            background-color: $tabBgRed;
            color:#fff;
            cursor: default;
        }
        .extra {
            position: absolute;
            top: 0;
            right: 0;
            s {
                font-family: $font-st;
            }
            .item {
                float: right;
                *display: inline;
                margin-right: 10px;
                padding-top: 8px;
            }
            .btn-primary {
                margin-top: -3px;
            }
        }
    }
    .tab-con {
        padding: 10px 0;
    }
}
.nav-qrcode {
    .content {
        padding: 15px;
    }
}
/*二手*/
.ershou{
    .ETab{
        .tab-main {
            border-bottom: 1px solid $baseColorErshou;
            li.current{
                background: $baseColorErshou;
            }
        }
        div.small {
            border: 1px solid #f5f5f5;
            background-color: #fafafa;
            padding-left: 10px;
            li.current,  li.current a {
                background: #fafafa;
                color: #e4393c;
            }
        }
    }
}

