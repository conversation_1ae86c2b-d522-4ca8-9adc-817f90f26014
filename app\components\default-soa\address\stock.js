define('MOD_ROOT/address/stock', function (require, exports, module) {
    var Conf = require('PUBLIC_ROOT/conf');
    var G = require('MOD_ROOT/common/core');
    var Tools = require('MOD_ROOT/common/tools/tools');
    var ABTest = require('MOD_ROOT/common/tools/abtest');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var addToCartBtn = require('MOD_ROOT/buybtn/buybtn').addToCartBtn;
    var Ad = require('MOD_ROOT/lazyinit/ad')
    // var Global=require('MOD_ROOT/address/getGlobal');
    var plantabid          = decodeURI(G.serializeUrl(location.href).param.purchasetab)// url上区分单品和企业计划购
    var plantabidNum       = plantabid && plantabid.indexOf("qyjhg") != -1  // url参数plantab只有等于qyjhg的时候调用
    function Stock(params, onSuccess, onError, onlyData) {
        var cfg = this.cfg = (window &&
            window.pageConfig &&
            window.pageConfig.product) || {};

        this.params = {};

        this.onSuccess = typeof onSuccess === "function" ?
            onSuccess :
            function () { };

        this.onError = typeof onError === "function" ?
            onError :
            function () { };

        this.onlyData = onlyData || false;

        var areaIds = this.areas = Tools.getAreaId().areaIds;

        /// 构建请求参数
        var defaults = {
            skuId: cfg.skuid || "",
            area: areaIds.join('_'),
            venderId: cfg.venderId || 0,
            buyNum: $("#buy-num").val() || 1,
            choseSuitSkuIds: cfg.suitSkuids || "",

            cat: cfg.cat instanceof Array ?
                cfg.cat.join(',') : "",

            extraParam: cfg.isHeYue ?
                '{"originid":"1","heYueJi":"1"}' :
                '{"originid":"1"}',

            fqsp: (G.onAttr("fqsp-1") && 1) ||
                (G.onAttr("fqsp-2") && 2) ||
                (G.onAttr("fqsp-3") && 3) || 0,

            pdpin: readCookie('pin') || "",
            pduid: Tools.getUUID(),
            ch: 1
        };

        //门店id
        if (cfg.locStoreId) {
            defaults["storeId"] = cfg.locStoreId;
        }

        var coord = readCookie('detailedAdd_coord');
        if (coord) {
            defaults["coord"] = coord;
        }

        var detailedAdd = readCookie('detailedAdd');
        if (detailedAdd) {
            defaults["detailedAdd"] = detailedAdd;
        }

        /// `detailedAdd_areaid`cookie在重新选择地址不会清除， `detailedAdd`cookie在重新选择地址会清除;
        /// 故采取双重校验以免出重新选择了地址后还使用常用地址的areaid
        var detailedAddAreaId = readCookie('detailedAdd_areaid');
        if (detailedAddAreaId && detailedAdd) {
            defaults["area"] = detailedAddAreaId.split('-').join('_');
        }

        this.params = $.extend(defaults, params);
        this.init();
    }

    try {
      var clearSkeletonTimer;
      // 融合接口返回清空骨架背景
      function clearSkeletonBackground() {
        clearSkeletonTimer && clearTimeout(clearSkeletonTimer)
        var $rightRoot = $('.itemInfo-wrap');
        $('#searchBar').css({ background: 'none' })
        $rightRoot.css({ background: 'none' })
        $rightRoot.find('.information-wrap').css({opacity: 1})
      }

      function ajaxTimeTimeoutClearSkeleton() {
        clearSkeletonTimer = setTimeout(function () {
          clearSkeletonBackground()
        }, 10000)
      }

      ajaxTimeTimeoutClearSkeleton()
    } catch (e) {
      console.error('骨架屏移除异常', e)
    }

    Stock.prototype = {
        constructor: Stock,

        init: function () {
            //Global.init
            this.bindEvent();
            var bbtfVal = G.serializeUrl(location.href).param.bbtf
            var bbTraffic = ""
            if(pageConfig.product.bybtShieldSwitch && bbtfVal && bbtfVal.length > 0){
                bbTraffic = "1"
            }
            // var soaRateVal = G.serializeUrl(location.href).param.soaRate // 切环境使用
            // var testVal = G.serializeUrl(location.href).param.test // 预发test=1
            var width = parseInt($("#spec-img").attr("width")) || 0
            var height = parseInt($("#spec-img").attr("height")) || 0 
            var paramJson = { 
                skuId: pageConfig.product.skuid,
                cat:  pageConfig.product.cat.join(','),
                area:  Tools.getAreaId().areaIds.join('_'),
                shopId: pageConfig.product.shopId,
                venderId: pageConfig.product.venderId,
                paramJson: pageConfig.product.paramJson,
                num: pageConfig.product.countNum || "1",
                bbTraffic: bbTraffic,
                canvasType: width == height ? 1 : 2, //1- 1:1 或者2- 3:4
                giftServiceIsSelected:  pageConfig.product.giftService || "", // 定制服务id：默认是"",0-为未选中定制服务，1-选中
                customInfoId: pageConfig.product.customId || "" // 定制服务器id
            }
            if(plantabidNum){ // 企业计划购的时候新增一个参数
                paramJson['entRecur'] = "1"
            }
            var body = JSON.stringify(paramJson);
            var time = new Date().getTime() || "1"
            // 加固start
            var colorParm = {
                appid: 'pc-item-soa',
                functionId: 'pc_detailpage_wareBusiness',
                // scval: pageConfig.product.skuid,
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            // if(soaRateVal && testVal && testVal == "1"){
            //     colorParm['soaRate'] = soaRateVal
            // }
            var that = this
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['scval'] = pageConfig.product.skuid;
                            colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                            that.getDataColor(colorParm); 
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['scval'] = pageConfig.product.skuid;
                        colorParm['uuid'] = '';
                        that.getDataColor(colorParm);
                        //烛龙上报
                        Tools.getJmfe(colorParm, e, "stock融合设备指纹调用异常", 200)
                    } 
                })
            }catch(e){
                colorParm['loginType'] = '3';
                colorParm['uuid'] = '';
                that.getDataColor(colorParm);
                //烛龙上报
                Tools.getJmfe(colorParm, e, "stock融合加固调用异常", 200)
            }
            
        },
        getDataColor: function (colorParm) {
            var _this = this;
            if (Conf.get('GLOBAL.isStockV2')) {
                return Tools.
                    getStockV2().done(function (res) {
                        res = res || {};
                        _this.handleData(res.stock);
                    }).fail(function (err) {
                        _this.error();
                        _this.onError.call(_this, err);
                    });
            }
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            return $.ajax({
                url: host,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                },
                headers: Tools.getUrlHeaders(),
                success: function (data) {
                    if (data) {
                        if(data.code && parseInt(data.code) < 10 && data.echo){
                            try {
                                var echoCode = data.echo.length > 1000 ? data.echo.substring(0,999) : data.echo;
                                //烛龙上报
                                Tools.getJmfe(colorParm, echoCode, "stock融合接口调用成功异常", 200)

                                
 
                            } catch(e) {
                                console.log('上报pc_detailpage_wareBusiness错误',e)
                            }
                        }else{
                            try{
                                itemEventBus && itemEventBus.setData('wareBusiness', data)
                                itemEventBus && itemEventBus.dispatchEmit('wareBusinessChange', data)

                                itemEventBus && itemEventBus.on('wareBusinessWatch', function () {
                                  itemEventBus && itemEventBus.dispatchEmit('wareBusinessChange', data)
                                })
                            }catch(e){
                                console.error("wareBusiness或者wareBusinessChange异常:",e)
                            }
                            try{
                                var noSaleReasonNum = data && data.promotion && data.promotion.limitBuyInfo && data.promotion.limitBuyInfo.resultExt && data.promotion.limitBuyInfo.resultExt.noSaleReason
                                var isQtpbsp = data && data.wareInfo && data.wareInfo.extend && data.wareInfo.extend.qtpbsp
                                if(noSaleReasonNum == "9" && pageConfig.product && pageConfig.product.noSaleReasonSwitch){//百补风控标-跳转首页
                                    window.location.href = '//www.jd.com?from=pc_item&reason=9';
                                }else if(isQtpbsp == "1"){
                                    window.location.href = '//www.jd.com?from=pc_item&qtpbsp=1';
                                }else{
                                    _this.handleData({
                                        type: 'onWareBusinessReady',
                                        data: data
                                    });
                                }
                            }catch(e){
                                _this.handleData({
                                    type: 'onWareBusinessReady',
                                    data: data
                                });
                            }

                            // 异常码上报用户之声
                            try{
                                if(data && data.fullErrorTag){
                                    pageConfig.product.fullErrorTag = data.fullErrorTag
                                    var cfg = this.cfg = (window &&
                                        window.pageConfig &&
                                        window.pageConfig.product) || {};
                                    Ad.init(cfg) // 初始化侧边栏
                                    //烛龙上报
                                    Tools.getJmfe(colorParm, {}, "stock融合接口调用fullErrorTag异常" + data.fullErrorTag, 200)
                                }
                            }catch(error){
                               console.log(error)
                            }
                        }
                    }
                },
                error: function (e) {
                    if (e.status === 403) {
                        window.location.href = '//www.jd.com?from=pc_item&reason=403';
                    }
                    //烛龙上报
                    Tools.getJmfe(colorParm, e, "stock融合接口调用error异常，状态码:" + e.status, 200)
                },
                complete: function() {
                    clearSkeletonBackground()
                }
            });

        },
        // getData: function () {
        //     var _this = this;

        //     if (Conf.get('GLOBAL.isStockV2')) {
        //         return Tools.
        //             getStockV2().done(function (res) {
        //                 res = res || {};
        //                 _this.handleData(res.stock);
        //             }).fail(function (err) {
        //                 _this.error();
        //                 _this.onError.call(_this, err);
        //             });
        //     }
        //     return $.ajax({
        //         url: '//item-soa.jd.com/getWareBusiness',
        //         cache: true,
        //         dataType: 'jsonp',
        //         data: {
        //             skuId: pageConfig.product.skuid + '',
        //             cat: pageConfig.product.cat.join(','),
        //             area: Tools.getAreaId().areaIds.join('_'),
        //             shopId: pageConfig.product.shopId,
        //             venderId: pageConfig.product.venderId + '',
        //             paramJson: pageConfig.product.paramJson,
        //             num: pageConfig.product.countNum
        //         },
        //         success: function (data) {
        //             if (data) {
        //                 // _this.handleData({
        //                 //     type: 'onWareBusinessReady',
        //                 //     data: data
        //                 // });
        //                 try{
        //                     var noSaleReasonNum = data && data.promotion && data.promotion.limitBuyInfo && data.promotion.limitBuyInfo.resultExt && data.promotion.limitBuyInfo.resultExt.noSaleReason
        //                     if(noSaleReasonNum == "9" && pageConfig.product && pageConfig.product.noSaleReasonSwitch){//百补风控标-跳转首页
        //                         window.location.href = '//www.jd.com?from=pc_item&reason=9';
        //                     }else{
        //                         _this.handleData({
        //                             type: 'onWareBusinessReady',
        //                             data: data
        //                         });
        //                     }
        //                 }catch(e){
        //                     _this.handleData({
        //                         type: 'onWareBusinessReady',
        //                         data: data
        //                     });
        //                 }

        //             }

        //         }
        //     });

        // },
        bindEvent: function () {
            // 预售接金融券(之前该代码在`handleData`方法中，没看到明确的依赖关系并且具体业务不明故把代码提到`bindEvent`方法中)
            var cfg = this.cfg;
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/new/cdpPromotionQuery?skuId=' + cfg.skuid + '&venderId='+ (cfg.venderId ? cfg.venderId : '') +'&source=PC&jdPin=',
                dataType: 'jsonp',
                data: {
                    appid: 'item-v3',
                    functionId: "pc_new_cdpPromotionQuery"
                },
                success: function (r) {
                    if (!r || !r.couponDesc || !r.jumpUrl) return;
                    var _html = [];
                    _html.push('<div id="summary-presale-wellfare" class="li" clstag="shangpin|keycount|product|jinrongquan_ys">');
                    _html.push('<div class="dt">预售福利</div>');
                    _html.push('<div class="dd"><a class="J-open-tb" href="' + r.jumpUrl + '" target="_blank"><span class="quan-item" title="' + r.couponDesc + '"><s></s><b></b><span class="text">' + r.couponDesc + '</span></span><span class="more-btn">更多&gt;&gt;</span></a></div>');
                    _html.push('</div>');
                    _html = _html.join('');

                    setTimeout(function () {
                        if ($('#summary-presale-wellfare').length > 0) {
                            $('#summary-presale-wellfare').remove();
                        }
                        $('.summary.p-choose-wrap').prepend(_html);
                    }, 200);
                }
            });
        },

        handleData: function (r) {
            var stock = r && r.data;
            if (!stock.stockInfo) {
                this.error();
                this.onError.call(this);
                return false;
            }
            var cfg = pageConfig.product;
            var state = stock.stockInfo.stockState;
            // 全局标识
            cfg._area = stock.stockInfo.area ? stock.stockInfo.area : 0;
            cfg.unSupportedArea = (stock.stockInfo.code === 2);
            // cfg.havestock = !cfg.isOver &&
            //                 state != -1 &&
            //                 state != 34 &&
            //                 state != 0;
            cfg.havestock = stock.stockInfo.isStock;


            // 海外售，有货但是不支持配送加入购物车置灰
            if (stock.stockInfo.supportHKMOShip === false) {
                cfg.havestock = false;
            }
            //定期购二期
            //新增显示/隐藏条件
            //秒杀进行中(下发字段存在)时，屏蔽定期购入口（秒杀预告期正常展示）。
            //独立秒杀(specialAttrs.isKO)，即商品有如下特殊属性，也屏蔽定期购入口。
            //闪购进行中（fsEndTime fsEndOffset）时，屏蔽定期购入口（闪购预告期正常展示）。

            // 可迁移逻辑
            var extraCondition = function () {
                //秒杀进行中
                if (cfg.koEndOffset) {
                    return true;
                }
                //独立秒杀
                if (cfg.specialAttrs && (cfg.specialAttrs instanceof Array) && cfg.specialAttrs.join(',').indexOf('isKO') != -1) {
                    return true;
                }
                //闪购进行中
                if (cfg.fsEndOffset) {
                    return true;
                }
                return false;
            };

            //定期购二期
            //通过异步查询库存接口返回数据进行判断是否显示
            //对于非生鲜的普通定期购商品，商品无货 (StockState=34)、限购时(code=2)，屏蔽定期购入口；
            //对于生鲜定期购商品，商品无货、限购、不支持配送时（同限购），屏蔽定期购入口。
            if (state == 34 || stock.stockInfo.code == 2 || extraCondition()) {
                $('#choose-period-buy').hide();
            } else {
                if (window.PeriodicalBuy) {
                    window.PeriodicalBuy.init(cfg);
                }
            }

            if (!this.onlyData) {
                this.set(stock);
            }

            this.onSuccess.call(this, r);
        },
        set: function (stock) {
            // this.setStockInfo(stock);
            // this.setSupportedServices(stock);
            // this.setSupportIcons(stock);
            // this.setDJDAreaSku(stock);
            // this.setLDP(stock);
            this.setStockInfo(stock);//库存
            this.setSupportedServices(stock);//支持服务
            this.setSupportIcons(stock);//服务icon app无下发
            this.setDJDAreaSku(stock);//大家电
            this.setLDP(stock.stockInfo);//落地配



            /// PLUS拉新券(PLUS礼包)
            if (Conf.get('GLOBAL.PLUS.giftPackageSignal')) {
                this.setOpenPlusGift(stock);
            }

            /// 商品保质期
            // this.setQualityGuaranteePeriod(stock);

        },
        // 库存接口失败异常
        error: function () {
            if (typeof console !== 'undefined') {
                console.error('Stock service maybe error or timeout.')
            }
        },
        setStockInfo: function (stock) {
            var $stockPrompt = $('#store-prompt')
            var $service = $('#summary-service')
            var $serviceTime = $("#summary-service-time")
            var $supply = $('#summary-supply')
            var $supplyTime = $('#summary-supply-time')
            var $dcashDesc = $('.J-dcashDesc')
            var stockDesc = ''
            var serviceInfo = ''
            var serviceInfoNew = ''
            var $cw = $('#summary-weight');

            if (!stock) {
                return false
            }

            // if (pageConfig.product.cat[0] == 6144) { // 珠宝首饰屏蔽“重量”信息
            //     $cw.hide();
            if (stock && stock.weightInfo && stock.weightInfo.content) {
                $cw.show().find('.dd').text(stock.weightInfo.content);

            } else {
                // if (stock && stock.weightValue) {
                //     $cw.show().find('.dd').text(stock.weightValue);
                // } else {
                $cw.hide().find('.dd').text('');
                // }
            }

            // start
            // 装修和游戏业务会展示特殊时效楼层
            // 「由 京东 发货并提供售后服务」,接口无下发
            if (stock.stockInfo && stock.stockInfo.serviceInfo) {
                serviceInfoNew += stock.stockInfo.serviceInfo
            }
            // 「23:00前完成下单,预计<b>03月03日(周四)</b>送达」
            if (stock.stockInfo && stock.stockInfo.promiseResult) {
                serviceInfoNew += stock.stockInfo.promiseResult
            }
            $serviceTime.html(serviceInfoNew)
            if (serviceInfoNew) {
                $supplyTime.show()
            } else {
                $supplyTime.hide()
            }
            // end

            // 「23:00前完成下单,预计<b>03月03日(周四)</b>送达」
            // var yuShou = stock.YuShouInfo
            
            if (stock.stockInfo && stock.stockInfo.promiseInfoText) { // 配送时效
                var text = stock.stockInfo.promiseInfoText
                // yuShou ? serviceInfo +=  '<span class="delivery"><b>现货</b>' + stock.stockInfo.promiseInfoText + '</span>' : serviceInfo +=  '<span class="delivery">' + stock.stockInfo.promiseInfoText + '</span>'
                serviceInfo +=  '<span class="delivery">' + text + '</span><span class="delivery-tips">' + text + '</span>'
            }
            
            if (stock.stockInfo && stock.stockInfo.dcashDescNew) { // 包邮
                if (stock.stockInfo.promiseInfoText) { // 如果同时有配送时效和包邮信息，中间增加竖线
                    serviceInfo += '<i style="border-left: 1px solid rgba(0,0,0,.06); padding-right: 8px; margin: 8px 0;"></i>'
                }
                serviceInfo += '<span class="free-shipping">' + stock.stockInfo.dcashDescNew + '</span>'
            }
            
            if (stock.stockInfo && stock.stockInfo.dcashDescDegrade) { // 降级
                $service.addClass('old')
            }
            // if (yuShou) { // 预售
            //     var text = ''
            //     if (yuShou.yushouStepType == 5 && yuShou.spotPresale == 1){
            //         text = '<div class=""><b>预售</b>' + yuShou.deliveryDate + '</div>'
            //     } else {
            //         text = '预计 最晚{0} 发货'
            //         text = '<div class=""><b>预售</b>' + text.format(yuShou.expectedDeliveryDate) + '</div>'
            //     }
            //     serviceInfo += text
            // } 
            
            //promise 「23:00前完成下单,预计<b>03月03日(周四)</b>送达」promise服务履约
            // if (stock.stockInfo && stock.stockInfo.stock) {
            //     serviceInfo += stock.stockInfo.stock
            // }
            $service.html(serviceInfo)
            // 包邮小 i 被 hover 时
            var tips = $('.free-shipping a span')
            $('.free-shipping').hover(function() {
                if (!tips.length) return
                var tipsLeft = tips.offset().left
                var tipsWidth = tips.outerWidth()
                var parent = $('.infomation')
                var parentLeft = parent.offset().left
                var parentWidth = parent.outerWidth()
                if (tipsWidth + tipsLeft > parentWidth + parentLeft) {
                    $('.free-shipping').css('position', 'static')
                    tips.css({
                        right: 0,
                        left: 'auto',
                        transform: 'none'
                    })
                }
            }, function() {
                $('.free-shipping').css('position', 'relative')
                tips.css({
                    left: '50%',
                    right: 'auto',
                    transform: 'translateX(-50%)'

                })
            })
            var delivery = $('.delivery')
            delivery.hover(function() {
                var tips = $('.delivery-tips')
                if (delivery[0].scrollWidth > delivery[0].clientWidth) { // 时效信息超长打点
                    delivery.addClass('more')
                    tips.show()
                } else {
                    delivery.removeClass('more')
                }
            }, function() {
                $('.delivery-tips').hide()

            })
            if(stock.isNoGoods) { // 无货
                $service.find('.delivery').css('color', '#e37d17')
            } else {
                $service.find('.delivery').css('color', '')
            }

            if (serviceInfo) {
                $supply.show()
                $('.free-shipping a') && $('.free-shipping a').attr('title', '')
                $("#J_LogisticsService .dt").remove()
                Tools.exposure({
                    functionName: 'PC_Productdetail_SZFloor_Expo',
                    exposureData: ['mainskuid'],
                    errorTips: '送至楼层曝光错误'
                })
                $("#J_LogisticsService .ui-area-text-wrap").click(function() {
                    Tools.landmine({
                        functionName: 'PC_Productdetail_SZFloor_Click',
                        exposureData: ['mainskuid'],
                        errorTips: '送至楼层埋点错误'
                    })
                })
                $("#J_LogisticsService .ns_services").click(function() {
                    Tools.landmine({
                        functionName: 'PC_Productdetail_SZFloor_Click',
                        exposureData: ['mainskuid'],
                        errorTips: '送至楼层埋点错误'
                    })
                })
            } else {
                $supply.hide()
                if($("#J_LogisticsService .dt").length == 0){
                    $("#J_LogisticsService .dd").before('<div class="dt">送至</div>')
                    $("#J_LogisticsService").css("padding-top","10px")
                }
            }
            // 地址组件 click 时，设置 overflow
            $('#J_LogisticsService').click(function(){
                if($('.information-wrap').scrollTop() > 0 && $('.information-wrap').hasClass('pro-detail-hd-fixed')) {
                    // console.log('吸顶并且已滚动');
                    return
                }
                
                $('.information-wrap').css('overflow', 'visible')
            },function() {
                
            })
            // 家装 #summary-stock
            var $stock = $('#summary-stock')
            // 家装服务
            if (serviceInfo && $stock.length) {
                $stock.show()
            } else {
                $stock.hide()
                $("#J_LogisticsService")
            }

            // 「<strong>有货</strong>，仅剩1件」，库存状态发货时间,中台拼接处理
            if (stock.stockInfo && stock.stockInfo.stockDesc) {
                stockDesc += stock.stockInfo.stockDesc
            }
            // 预订状态
            /*if (stock.Drd && stock.StockState === 36 && !pageConfig.product.isYuShou) { // 不是预售
                stockDesc = '<strong>预订，</strong>此商品为预订商品，下单后在' + stock.Drd
            }*/
            //接口无返回
            // if (stock.spIconDesc) {
            //     stockDesc += stock.spIconDesc
            // }
            $stockPrompt.html(stockDesc)

            // 「pop 免运费」app没返回
            if (stock.stockInfo && stock.stockInfo.dcashDesc) {
                $dcashDesc.html(stock.stockInfo.dcashDesc)
            }
            // 三个已合并 用上面即可
            // 山姆运费提示
            // if (stock.isSam) {
            //     $dcashDesc.html(stock.samProductFreight)
            // }
            // 沃尔玛运费提示
            // if (stock.isWalMar) {
            //     $dcashDesc.html(stock.walMarProductFreight)
            // }
        },
        setSupportIcons: function (stock) {
            var $support = $('#summary-support')
            if (!stock.stockInfo || !stock.stockInfo.support || !stock.stockInfo.support.length) {
                $support.hide()
                return false
            }

            pageConfig.__supportABTest = new ABTest(Tools.getUUID(), 0.5)
            var isHitVersion = pageConfig.__supportABTest.isHitVersion() === 'A'

            for (var i = 0; i < stock.stockInfo.support.length; i++) {
                var sup = stock.stockInfo.support[i]
                if (sup.id === 'baina') {
                    if (!isHitVersion) {
                        stock.stockInfo.support.splice(i, 1)
                        break
                    }
                }
            }
            if (/debug=zz/.test(location.href)) {
                stock.stockInfo.support.push({
                    id: 'zengzhi',
                    showName: '增值服务'
                })
            }
            var tpl =
                '\
            {for item in support}\
            <li id="support-${item.id}" clstag="shangpin|keycount|product|zhichi_${item.id}_${cat2}">\
                <a {if item.helpLink} target="_blank" href="${item.helpLink}" {else} href="#none" {/if} \
                    data-title="${item.iconTip}">\
                <i class="sprite-${item.id}"></i>\
                <span>${item.showName}</span>\
                </a>\
            </li>\
            {/for}'

            if (stock.stockInfo.support.length) {
                $support.show()
            } else {
                $support.hide()
            }

            $support.find('.choose-support').html(
                tpl.process({
                    support: stock.stockInfo.support,
                    cat2: pageConfig.product.cat[2]
                })
            )

            this.showTip($support)
            this.bindDialog()
        },
        showTip: function ($el) {
            $el.find('[data-title]').each(function () {
                if ($(this).data('title')) {
                    $(this).ETooltips({
                        pos: 'bottom',
                        zIndex: 10,
                        width: 200,
                        defaultTitleAttr: 'data-title'
                    })
                }
            })
        },
        setDJDAreaSku: function (stock) {
            if (!stock.stockInfo || !stock.stockInfo.realSkuId) return false

            var realSkuId = stock.stockInfo.realSkuId
            var realStockState = stock.stockInfo.realStockState // realStockState==36
            // 大家电分区 sku
            if (realSkuId != pageConfig.product.skuid) {
                var jt = [
                    G.isPop ? '0' : '1',
                    G.isPOPSku(realSkuId) ? '0' : '1'
                ].join('')
                var param = '?jt=' + jt
                if (location.search) {
                    if (/jt=\d+/.test(location.search)) {
                        param = location.search.replace(/jt=\d+/, 'jt=' + jt)
                    } else {
                        param = location.search + '&jt=' + jt
                    }
                }
                if (/debug=disableJump/.test(location.href)) {
                    return false
                }
                // 负卖（a=36）prd:https://joyspace.jd.com/pages/LXUVs4PcgTdqs63RNReE
                var tips = realStockState == 36 ? '您购买的商品需要等待，为您推荐现货商品无需等待，点击「切换相似商品」查看或者点击「取消」按钮购买当前商品。' : '该商品在当前区域暂不支持配送或无货，是否切换到相似商品。'
                var html =
                    '\
                <p style="padding: 40px 30px;font-size: 14px;font-weight: 600;color: #999;">' + tips + '</p>\
                <div class="btn" style="text-align: center;">\
                    <a href="#none" onclick="$.closeDialog()" style="height: 46px;line-height: 46px;font-weight: 700;padding: 0 26px;font-size: 18px;background-color: #df3033;color: #fff;display: inline-block;">取消</a>\
                    <a href='+ '//item.jd.com/' + realSkuId + '.html' + param + ' style="height: 46px;font-weight: 700;line-height: 46px;padding: 0 26px;font-size: 18px;background-color: #85C363;color: #fff;display: inline-block;">切换相似商品</a>\
                </div>'

                $('body').dialog({
                    width: 392,
                    title: '',
                    height: 200,
                    type: 'text',
                    maskClose: true,
                    source: html,
                    onReady: function () {
                    }
                })
                // window.location = '//item.jd.com/' + realSkuId + '.html' + param

            }
        },
        // 落地配
        setLDP: function (stock) {
            var $ldp = $('#choose-luodipei')

            if (/debug=ldp/.test(location.href)) {
                stock.luodipei = [
                    {
                        desc: '送货服务：电梯住户或7层以下步梯住户可享受此服务，7层以上步梯住户则需按配送公司规定加收费用。（瓷砖地板类，仅电梯住户或1层步梯住户可享）。\n安装服务：此类商品需专人安装，签收后请联系商家确认上门安装时间。\n如商家未履行服务，消费者可获200元/单的赔付。',
                        id: 1103,
                        url: '//help.jd.com/Vender/question-1043.html',
                        seq: 1,
                        name: '送货上门安装',
                        charge: 2
                    },
                    {
                        desc: '送货服务：电梯住户或7层以下步梯住户可享受此服务，7层以上步梯住户则需按配送公司规定加收费用。（瓷砖地板类，仅电梯住户或1层步梯住户可享）。\n如商家未履行服务，消费者可获200元/单的赔付。',
                        id: 1102,
                        url: '//help.jd.com/Vender/question-1043.html',
                        seq: 2,
                        name: '送货上门',
                        charge: 1
                    },
                    {
                        desc: '商品送至您下单地址所在地级市后，配送公司会通知您物流点地址，您需自行前往物流点提货，自行搬运货物并安装。\n如商家未履行服务，消费者可获200元/单的赔付。',
                        id: 1101,
                        url: '//help.jd.com/Vender/question-1043.html',
                        seq: 3,
                        name: '市区站点自提',
                        charge: 0
                    }
                ]
            }

            if (!$ldp.length || !stock || !stock.luodipei || !stock.luodipei.length) {
                $ldp.hide()
                return false
            }
            $ldp.show()

            var template =
                '\
            {for item in luodipei}\
            <div class="item {if Number(item_index)==0} selected{/if}">\
                <b></b>\
                <a href="#none" data-id="${item.platformPid}">${item.sortName} ￥${item.price}</a>\
                <script type="text/html">${item.desc} {if item.url }<a class="hl_blue" href="${item.url}" target="_blank">详情 &raquo;</a>{/if}</script>\
            </div>\
            {/for}'

            $ldp.find('.dd').html(template.process(stock))

            $ldp.find('.item').each(function () {
                var $this = $(this)
                var html = $this.find('script').html()

                html && $this.ETooltips({
                    autoHide: true,
                    close: false,
                    content: html,
                    width: 300,
                    pos: 'bottom',
                    zIndex: 30 // 右侧 z-index 为30，需要大于等于30
                })
            })

            function setLink(id) {
                id = id || $ldp.find('.selected a').attr('data-id')
                var href = addToCartBtn.$el.attr('href')
                var result = ''

                if (href && href != '#none') {
                    if (/did=/.test(href)) {
                        result = href.replace(/did=\d+/, 'did=' + id)
                    } else {
                        result = href + '&did=' + id
                    }

                    addToCartBtn.enabled(result)
                }

                Event.fire({
                    type: 'onLDPSelected',
                    did: id
                })
            }
            function setStyle($ele) {
                $ldp.find('.item').removeClass('selected')
                $ele.parent().addClass('selected')
            }
            function handleClick(e) {
                var $this = $(e.target)
                var id = $this.data('id')
                setStyle($this)
                setLink(id)
            }

            $ldp.delegate('.item a', 'click', handleClick)
            setLink($ldp.find('.selected a').attr('data-id'))

            Event.fire({
                type: 'onLDPSelected',
                did: stock.luodipei[0].platformPid
            })

            // platformPid会传undefined,上报个烛龙监控下
            try{
                if(!stock.luodipei[0].platformPid){
                    //烛龙上报
                    Tools.getJmfe({function:"onLDPSelected",body: stock && stock.luodipei}, "", "落地配platformPid字段异常", 300)
                }
            }catch(e){
                console.error("onLDPSelected上报错误platformPid")
            }
        },
        // 支持 套餐、增值服务 弹出层
        bindDialog: function () {
            this.suitDialog()
            this.zzDialog()
        },
        suitDialog: function () {
            var $tcbg = $('#support-tcbg')
            var aids = this.areas

            $tcbg.bind('click', function () {
                var url = '//ctc.jd.com/popupDialog.action?showSp=1|2&'

                var resUrl =
                    url +
                    $.param({
                        skuId: pageConfig.product.skuid,
                        provinceId: aids[0],
                        cityId: aids[1],
                        popId: pageConfig.product.venderId,
                        r: Math.random()
                    })

                pageConfig.bTypeIframe = $('body').dialog({
                    type: 'iframe',
                    width: 710,
                    height: 610,
                    title: '套餐变更',
                    autoIframe: false,
                    iframeTimestamp: false,
                    source: resUrl,
                    onReady: function () {
                        var _w = $(this.el).width()
                        $(this.el).addClass('popup-phone-service')
                        $(this.content).width(_w)
                    }
                })
            })
        },
        zzDialog: function () {
            var $zz = $('#support-zengzhi')
            $zz.bind('click', function () {
                var url = '//scp.jd.com/settlement.action?'

                var resUrl =
                    url +
                    $.param({
                        skuId: pageConfig.product.skuid,
                        pcount: $('#buy-num').val(),
                        venderId: pageConfig.product.venderId,
                        r: Math.random()
                    })

                var html =
                    '<div class="zengzhi-layer" style="width:630px;">\
                    <h3><i class="icon-zengzhi"></i>阿凡提主题馆“增值交易”模式说明</h3>\
                    <p class="zengzhi-intr">选择“增值交易”模式的客户，可以参与商家指定的相关交易服务。购买后请在个人中心左侧“特色服务”的“黄金|收藏服务”中查看交易凭证，凭交易凭证联系商家完成后续服务。</p>\
                    <div class="zengzhi-info">\
                        请注意：\
                        <ol>\
                            <li>1. 选择“增值交易”模式的客户，系统默认为不发货，如您希望提取货物，请您联系商家，凭交易凭证办理。</li>\
                            <li>2. 选择“增值交易”模式的客户，须为中华人民共和国公民，持有身份证、护照等有效身份证件。须年满18岁，为完全民事行为能力人。</li>\
                            <li>3. “增值交易”模式中所含服务包括但不限于：指定机构代理办理产品封装、交易托管、交易入库、交易提货、交易结算、二手转让等服务形式。该服务最终解释权归阿凡提主题馆所有。</li>\
                            <li>4. “增值交易”模式中所含服务对应的品种设立、交易托管、交易申购、交易流通等信息，均以国际版阿凡提主体文化交易平台对外公告为准。</li>\
                            <li>\
                                5. “增值交易”模式后续服务操作流程示意如下：\
                                <div class="process">\
                                    <div class="process-item">\
                                        客户在京东个人中心<span>【黄金|收藏服务】</span>张获取交易凭证\
                                    </div>\
                                    <i class="arrow"></i>\
                                    <div class="process-item">\
                                        客户通过微信关注<span>【国际版阿凡提主体文化交易平台】</span>\
                                    </div>\
                                    <i class="arrow"></i>\
                                    <div class="process-item">\
                                        在<span>【国际版阿凡提主体文化交易平台】</span>的<span>【客户服务】</span>中<span>【京东客户登记】</span>按系统指引完成后续操作\
                                    </div>\
                                </div>\
                            </li>\
                        </ol>\
                        <a clstag="shangpin|keycount|product|button_ZengZhiJiaoYi" target="_blank" href="' +
                    resUrl +
                    '" class="btn-confirm">增值交易</a>\
                    </div>\
                </div>'

                $('body').dialog({
                    type: 'html',
                    width: 690,
                    height: 600,
                    title: '增值交易',
                    autoIframe: false,
                    iframeTimestamp: false,
                    source: html,
                    onReady: function () { }
                })
            })
        },

        /// 支持的服务，放心购
        setSupportedServices: function (stock) {
            if (stock.stockInfo == undefined) {
                stock = {};
            }
            var isJdkd = stock.isJdkd;
            var isJdwl = stock.isJdwl;
            var isSfkd = stock.isSfkd;
            var isCsfh = stock.isCsfh;
            var csfhText = stock.csfhText;
            var isServiceJdkd = stock.isServiceJdkd;// 是否厂商发货
            var price = stock.price && stock.price.p;
            var basicIcons = $.isArray(stock.stockInfo.serverIcon && stock.stockInfo.serverIcon.basicNewIcons) ? stock.stockInfo.serverIcon.basicNewIcons : [];
            var trustworthyIcon = $.isArray(stock.stockInfo.serverIcon && stock.stockInfo.serverIcon.trustworthyIcon) ? stock.stockInfo.serverIcon.trustworthyIcon : [];
            var wlfwIcons = $.isArray(stock.stockInfo.serverIcon && stock.stockInfo.serverIcon.wlfwIcons) ? stock.stockInfo.serverIcon.wlfwIcons : [];
            // 数据处理（逻辑来自于老代码描述）
            // arr = $.map(arr, function(elem) {
            //     if (elem) {
            //         if (elem.code === "service_shangjiazx" &&
            //             typeof elem.helpLink === "string") {
            //             elem.helpLink = elem.helpLink.
            //                 replace(/(skuId=)[^&?#]*/, "$1" + pageConfig.product.skuid);
            //         }
            //         if (typeof elem.iconType === "number" &&
            //             elem.iconType !== 5) {
            //             return elem;
            //         }
            //     }
            // });

            // 硬编码数据
            // if (G.onAttr('isNSNGgoods-3')) {
            //     arr.push({
            //         iconTip: '针对不同产品，小京鱼可提供语音控制、远程管理、场景联动等不同智能服务',
            //         iconCode: 'nsng',
            //         showName: '搭载小京鱼助手',
            //         helpLink: '//smart.jd.com',
            //         picUrl: '//static.360buyimg.com/item/assets/picon/zhineng.png'
            //     });
            // }

            // markObjectElem(arr, "0");

            // var arr1 = [];
            // var promises = [];
            // $.each(arr, function (index, elem) {
            //     var list = [
            //         "service_popshantui",
            //         "PIC_5000",
            //         "pop_tsposunbaopei",
            //         "pop_tsguominwuyou",
            //         "POP_freeSend",
            //         "service_jingzunda",
            //         "sendpay_zhun",
            //         "sendpay_211",
            //         "free_delivery_zhong",
            //         "service_youxianpei",
            //         "service_sannianzhibao",
            //         "pop_quick_audit",
            //         "PIC_5005",
            //         "service_jiankangbz",
            //         "service_chongwucx",
            //         "hongpipi_wuyou",
            //         "service_30zhibao",
            //         "service_15baojia",
            //         "service_jinkousy",
            //         "service_caizhiyz",
            //         "service_huyanwy",
            //         "service_yuanchangj",
            //         "service_shangmencl",
            //         "service_chandijx",
            //         "service_1YZB",
            //         "service_2YZB",
            //         "service_QGZB",
            //         "service_7baojia",
            //         "service_30baojia",
            //         "pop_DRF",
            //         "pop_24HF",
            //         "service_delivery_to_door",
            //         "service_pinzhiyouxuan",
            //         "service_ydjsbz",
            //         "service_fsczbz",
            //         "service_xqwy",
            //         "service_spbh",
            //         "service_gtwy",
            //         "service_bywy"
            //     ];
            //     console.log("fxg",$.inArray(elem && elem.code, list))
            //     if (G.onAttr("fxg-1")) {
            //        arr1.push(elem);
            //     } else {
            //         promises.push(elem);
            //     }
            // });

            // var arr2 = [];
            var extraClassName = '';
            if (stock.stockInfo.serverIcon && stock.stockInfo.serverIcon.relaxType == 3) {
                extraClassName = "extra3";
            }
            // if ($.isArray(stock.stockInfo.serverIcon.trustworthyIcon) && stock.stockInfo.serverIcon.trustworthyIcon.length > 0) {
            //     var obj = stock.stockInfo.serverIcon.trustworthyIcon[0];

            //     if ($.isArray(obj.featureIconResults) &&
            //         obj.featureIconResults.length > 0) {
            //         arr2 = arr2.concat(obj.featureIconResults);
            //     }

            //     if ($.isArray(obj.baseServiceIconResults) &&
            //         obj.baseServiceIconResults.length > 0) {
            //         arr2 = arr2.concat(obj.baseServiceIconResults);
            //     }
            //     /// 自营放心购数据打标，方便区分处理
            //     markObjectElem(arr2, "1");

            //     if (obj.relaxType == 3) {
            //         extraClassName = "extra3";
            //     }
            // }
            // 合并服务icon
            // var services = [];
            // if (arr1.length > 0 && arr2.length > 0) {
            //     services = arr1.concat(arr2);
            // } else if (arr1.length > 0) {
            //     services = arr1;
            // } else if (arr2.length > 0) {
            //     services = arr2;
            // }

            /**
             * 给对象进行标记
             * @param {Object|Array} obj
             * @param {String} key
             * @param {Any} val
             */
            // function markObjectElem(obj, key, val) {
            //     if (obj == null ||
            //         typeof obj !== "object") {
            //         return;
            //     }

            //     if (val == undefined) {
            //         val = key;
            //         key = undefined;
            //     }

            //     if (key == undefined) {
            //         key = "__MARK__";
            //     }

            //     for (var k in obj) {
            //         if (obj.hasOwnProperty(k) &&
            //             obj[k] != null &&
            //             typeof obj[k] === "object") {
            //             obj[k][key] = val;
            //         }
            //     }
            //     return obj;
            // }

            /**
             * 一维数组转换为二维数组
             * @param {Array} array
             * @param {Number} size 子数组的长度
             * @returns {Array}
             */
            function makeArrayTo2Dimension(array, size) {
                if (!(array instanceof Array)) {
                    return [];
                }

                if (typeof size === "number" && size >= 1) {
                    size = size;
                } else {
                    size = 3;
                }

                var len = array.length;

                if (len <= size) {
                    return [array];
                }

                var arr = [];

                for (var i = 0; i < len; i += size) {
                    arr.push(array.slice(i, i + size));
                }

                return arr;
            }
            var logs = [
                "clstag='shangpin|keycount|product|promisefw_" + pageConfig.product.pType + "'",
                "onclick=\"log('PC_shangxiang', 'zyfxg_shangxiang', '', '', 'zyfxg_fuwu2', '" + pageConfig.product.skuid + "', '" + price + "')\""
            ];
            function generateHtmlStr(arr, separator, startStr, endStr) {
                if (!(arr instanceof Array) || arr.length === 0) {
                    return "";
                }

                separator = typeof separator === "string" ? separator : "";

                var hasWrap = (typeof startStr === "string" &&
                    typeof endStr === "string" &&
                    startStr.length > 0 &&
                    endStr.length > 0);

                var tmp = [];
                
                for (var i = 0; i < arr.length; i++) {
                    var obj = arr[i];

                    if (obj == undefined || !obj.tip || !obj.text) {
                        continue;
                    }

                    var log = logs[obj.__MARK__];
                    log = log ? log : "";

                    if (typeof obj.helpLink === "string" && obj.helpLink.length > 0) {
                        tmp.push("<a target='_blank' title='" + obj.tip + "' href='" + obj.helpLink + "'" + log + ">" + obj.text + "</a>");
                    } else {
                        tmp.push("<span title='" + obj.tip + "'" + log + ">" + obj.text + "</span>");
                    }
                }

                var str = "";

                if (tmp.length) {
                    str = tmp.join(separator);
                    if (hasWrap) {
                        str = startStr + str + endStr;
                    }
                }

                return str;
            }

            function generateListBoxHtmlStr(arr) {
                if (!(arr instanceof Array) || arr.length === 0) {
                    return "";
                }

                var str = "";

                for (var i = 0; i < arr.length; i++) {
                    str += generateHtmlStr(arr[i], "<i> · </i>", "<div>", "</div>");
                }

                if (str) {
                    if (arr.length > 1) {
                        str = "<div class='services services--more ns_services_info'>" + str + "<span class='arrow'></span></div>"
                    } else {
                        str = "<div class='services ns_services_info'>" + str + "</div>"
                    }
                }

                return str;
            }
            // 初始化服务楼层
            function initService() {
                basicIcons = basicIcons.filter(function(icon) {return icon.text}) // 过滤空对象
                var __html = "";
                // __html += generateListBoxHtmlStr(makeArrayTo2Dimension(basicIcons, 5));
                __html = basicIcons.map(function(obj) {
                    var log = logs[obj.__MARK__];
                    log = log ? log : "";
                    var tip = obj.tip ? "title='" + obj.tip + "'" : ''
                    if (typeof obj.helpLink === "string" && obj.helpLink.length > 0) {
                        return "<a target='_blank' " + tip + " href='" + obj.helpLink + "'" + log + ">" + obj.text + "</a><i> · </i>"
                    } else {
                        return "<span " + tip + log + ">" + obj.text + "</span><i> · </i>"
                    }
                }).join('') 
                __html = "<div class='services ns_services_info'><div>" + __html + "</div></div>"
                $promise.html(__html);
                $promise.show();
                
                $("#service-support").show();
                checkIfMultiPleLine()
            }
            function hideLast() { // 隐藏每行的最后一个点
                var dots = $('.ns_services_info').find('i')
                dots.each(function(index, item) {
                    $(item).css('visibility', 'visible')
                })
                var lastDots = [];
                var currentRowLastIndex = 0;
                for (var i = 0; i < dots.length; i++) {
                    var currentTop = dots[i].getBoundingClientRect().top;
                    var prevTop = dots[currentRowLastIndex].getBoundingClientRect().top;
                    if (currentTop!== prevTop) {
                        lastDots.push(i - 1);
                        currentRowLastIndex = i;
                    }
                    if (i === dots.length - 1) { // 最后一个
                        lastDots.push(i);
                    }
                }
                lastDots.forEach(function(dotIndex) {
                    $(dots[dotIndex]).css('visibility', 'hidden')
                })
                return lastDots
            }
            // 检查是否多行
            function checkIfMultiPleLine() {
                var wrapper = $('.ns_services_info').children('div')
                var lastDots = []
                if (wrapper && wrapper.height() > 28) { // 多行 mac 上为 27.5
                    // 多行时重新组织 dom
                    $('.ns_services_info').addClass('services--more')
                    lastDots = hideLast()
                    // lastDots.length = lastDots.length - 1
                    if (lastDots.length) {
                        var text = '<div>'
                        for (var index = 0; index < basicIcons.length; index++) {
                            var obj = basicIcons[index];
                            var log = logs[obj.__MARK__];
                            log = log ? log : "";
                            var lineBreak = lastDots.find(function(dot) {
                                return dot == index
                            })
                            var tip = obj.tip ? "title='" + obj.tip + "'" : ''
                            if (typeof obj.helpLink === "string" && obj.helpLink.length > 0) {
                                text += "<a target='_blank' " + tip + " href='" + obj.helpLink + "'" + log + ">" + obj.text + "</a>"
                            } else {
                                text += "<span " + tip + log + ">" + obj.text + "</span>"
                            }
                            
                            if (lineBreak) {
                                text += '</div><div>'
                            } else {
                                text += '<i> · </i>'
                            }
                        }
                        text += '</div>'
                        $('.ns_services_info').html(text)
                    }
                    $('.ns_services_info').children('div').each(function(index) {
                        if(index) {
                            // $(this).hide() // 超过一行的隐藏
                        }
                    })
                    $('.ns_services_info').toggleClass('expand')
                    $('.ns_services_info').append('<span class="arrow"></span>')
                    $('.ns_services_info').find('.arrow').click(function() {
                        $('.ns_services_info').toggleClass('expand')
                        if( $('.ns_services_info').hasClass('expand')) { // 展开
                            $('.ns_services_info').children('div').each(function(index) {
                                if(index) {
                                    $(this).show()
                                }
                            })
                        } else {
                            $('.ns_services_info').children('div').each(function(index) {
                                if(index) {
                                    $(this).hide()
                                }
                            })
                        }
                    })
                } else {
                    $('.ns_services_info').find('.arrow').remove()
                    $('.ns_services_info').removeClass('services--more')
                    hideLast()
                }
                
            }
            /**
             * 生成物流dom
             * @param {Array} list - 包含对象的数组，每个对象包含helpLink, tip, text属性
             * @returns {string} - 生成的HTML字符串
             */
            function generateLogisticList(list) {
                var first = list.slice(0, 2).map(function(item) {
                    return '<a target="_blank" href="' + item.helpLink + '" title="' + item.tip + '">' + item.text + '</a>'
                }).join('<i>·</i>')
                first = '<div class="logistic-basic">' + first 
                var second = ''
                if (list.length > 2) {
                    second = list.map(function(item) {
                        return '<a target="_blank" href="' + item.helpLink + '" title="' + item.tip + '">' + item.text + '</a>'
                    })
                    second.forEach(function(item, i) {
                        if ((i+1)%4 == 0) {
                            second[i] = item + '</div><div>'
                        }
                    });

                    second = '<div class="logistic-more"><div>' + second.join('') + '</div></div>'
                }
                
                return first + second + '</div>'
            }
            // 窄屏交换地址和物流楼层位置
            function exchangeLocation() {
                var address = $('#J_LogisticsService .store')
                if (window.innerWidth < 1680) {
                    $logisticsServices.find('.dd').append($('.ns_services')[0])
                    address.css('margin-bottom', '12px')
                    address.css('display', 'block') // 设置为 block, 防止地址太短与其他内容一行
                } else {
                    $logisticsServices.find('.dd').append(address[0])
                    address.css('margin-bottom', '0')
                    address.css('display', 'inline-block')
                }
            }
            // 计算物流字段宽度
            function calculateLogisticWidth() {
                var dom = $('.logistic-more')
                var width = dom.width()
                if (width > 282) {
                    dom.css({
                        width: '258px', // 282 - 24(padding)
                        wordBreak: 'break-all'
                    })
                }
            }
            var $promise = $(".J-promise-icon.promise-icon");
            var $services = $("#J_SelfAssuredPurchase");
            var $logisticsServices = $("#J_LogisticsService");

            /// 现场清理
            $promise.hide().html("");
            $services.hide().html("");
            $logisticsServices.find(".ns_services").html("").hide();


            /// 合约机和支持的服务互斥（逻辑来自于老代码描述）
            if (pageConfig.product.isHeYue) {
                return;
            }
            //京东物流和京东快递标签升级
            if (isJdwl || isJdkd || isSfkd) {
                var __html = "";
                if (isSfkd) {
                    __html = "<div class='icon-sf" + extraClassName +
                        "'>三方快递</div>";
                }
                if (isJdwl) {
                    __html = "<div class='icon-wl" + extraClassName +
                        "'>京东物流</div>";
                }
                if (isJdkd) {
                    __html = "<div class='icon-kd" + extraClassName +
                        "'>京东快递</div>";
                }
                
                // 厂商发货
                if(isCsfh && csfhText) {
                    __html += "<div class='icon-cs" + extraClassName +
                        "'>"+csfhText+"</div>";
                }
                if (wlfwIcons.length > 0 || isServiceJdkd || isSfkd) { //有物流服务icon信息或者厂商发货 展示独立楼层
                    __html += generateLogisticList(wlfwIcons);
                    // __html = "<div class='dd'>" + __html + "</div>";
                    $logisticsServices.find(".ns_services").html(__html).css({"display":"inline-block"});
                    // calculateLogisticWidth()
                    $logisticsServices.show();
                    // exchangeLocation()
                    // $(window).resize(exchangeLocation)
                } else {
                    $("#summary-service").prepend(__html)
                }
            }
            
            /// 服务支持渲染UI（自营放心购+services+promises）
            if (trustworthyIcon.length > 0) {
                var __html = "";
                // var iconb = pageConfig.product.freeBuyShow ? "icon-freeBuy" : "icon-SelfAssuredPurchase" //居家放心购替换图标icon
                var iconqy = "icon-qyService";
                var iconfreeBuy = "icon-freeBuy";
                var iconself = "icon-SelfAssuredPurchase";
                // if(pageConfig.product.isPop){
                //     if(stock.stockInfo.popFxgCode==1){
                //         __html = "<div class='"+ iconb + extraClassName + 
                //         "' title='无忧售后，放心选购（具体可享受服务以售后申请页为准）'></div>";
                //     }
                // }else{
                //     if(stock.stockInfo.fxgCode==2){
                //         __html = "<div class='"+ iconqy + extraClassName + 
                //         "' title='无忧售后，放心选购（具体可享受服务以售后申请页为准）'></div>";
                //     }else{
                //         __html = "<div class='"+ iconb + extraClassName + 
                //         "' title='无忧售后，放心选购（具体可享受服务以售后申请页为准）'></div>";
                //     }

                // }
                if (stock.stockInfo.fxgCode == 1) { //不分自营和pop 1是放心购，2是企悦服务，3是省心装服务
                    __html = "<div class='" + iconself + extraClassName +
                        "' title='无忧售后，放心选购（具体可享受服务以售后申请页为准）'></div>";
                } else if (stock.stockInfo.fxgCode == 2) {
                    __html = "<div class='" + iconqy + extraClassName +
                        "' title='让服务变得更简单'></div>";
                } else if (stock.stockInfo.fxgCode == 3) {
                    __html = "<div class='" + iconfreeBuy + extraClassName +
                        "' title='无忧售后，放心选购（具体可享受服务以售后申请页为准）'></div>";
                }
                __html += generateListBoxHtmlStr(makeArrayTo2Dimension(trustworthyIcon));
                __html += basicIcons.length > 0 ?
                    generateHtmlStr(basicIcons, "", "<div class='promises'>", "</div>") : "";
                __html = "<div class='dt'>服务支持</div><div class='dd'>" + __html + "</div>";
                $services.html(__html);
                $services.show();
            } else if (basicIcons.length > 0) {
                
                try{
                    initService()
                } catch(e) {
                    console.log(e);
                }
                $(window).resize(initService)
                
                Tools.exposure({
                    functionName: 'Productdetail_BasicServiceFloor_Expo',
                    exposureData: ['mainskuid'],
                    extraData: {
                        clerk: basicIcons.map(function(item) {
                            return item.text
                        })
                    }
                })
                $promise.find('a').each(function(index, ele){
                    $(ele).click(function() {
                        Tools.landmine({
                            functionName: 'Productdetail_BasicServiceFloor_Click',
                            exposureData: ['mainskuid'],
                            extraData: {
                                clerk: $(this).text().trim()
                            },
                            errorTips: '服务楼层埋点错误'
                        })
                    })
                })
            }else{
                $("#service-support").hide();
                $services.hide();
            }
        },

        /**
         * 商品保质期
         * @param {Object} stock 
         */
        setQualityGuaranteePeriod: function (stock) {
            var cfg = window.pageConfig.product;
            var isAvailableClass = (cfg.cat[2] == '1534');
            var isLabelHit = G.onAttr('zsscrq');
            var expiration = stock.expiration;
            var $weight = $('#summary-weight');
            var $qgp = $('#J_QualityGuaranteePeriod');

            if (
                isAvailableClass &&
                isLabelHit &&
                expiration
            ) {
                var arr = expiration.split('#');
                var date = arr[0];
                var days = arr[1];

                if (date && days) {
                    var text = '生产日期在' +
                        (''.format.apply('{0}年{1}月{2}日', $.map(date.split('-'), function (v) { return isNaN(+v) ? v : +v; }))) +
                        '之后，保质期' + days + '天';
                } else {
                    var text = '';
                }

                if (text) {
                    if ($qgp.length) {
                        $('.dd', $qgp).html(text);
                    } else {
                        if ($weight.length) {
                            $weight.after('<div id="J_QualityGuaranteePeriod" class="li"><div class="dt">日期</div><div class="dd">' + text + '</div></div>');
                        }
                    }
                } else {
                    $qgp.remove();
                }

            } else {
                $qgp.remove();
            }
        },

        /**
         * PLUS 拉新券需求(PLUS礼包) 产品：吴超 2018.12.26
         * 需求文档：https://cf.jd.com/pages/viewpage.action?pageId=144581334
         */
        setOpenPlusGift: function (stock) {
            var PB = {
                /**
                 * isWithinDate 判断是否在某个时间段内
                 * @param {String} begin 时间字符串 格式：'2018-23-12 12:21:12'
                 * @param {String} end   时间字符串
                 * @returns {Boolean}
                 */
                isWithinDate: function (begin, end) {
                    var DATE_REGEXP = /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/;
                    var getTimeStamp = function (y, m, d, h, min, s) { return +new Date(y, m - 1, d, h, min, s); }
                    if (DATE_REGEXP.test(begin) && DATE_REGEXP.test(end)) {
                        begin = getTimeStamp.apply(null, DATE_REGEXP.exec(begin).slice(1, 7));
                        end = getTimeStamp.apply(null, DATE_REGEXP.exec(end).slice(1, 7));
                        var now = +new Date();
                        if (now < begin || now > end) {
                            return false;
                        } else {
                            return true;
                        }
                    } else {
                        return false;
                    }
                }
            };

            // 30元无门槛卷 是在年货节期间(2019年1月8日-2月8日)
            var tipText = PB.isWithinDate('2019-01-08 00:00:01', '2019-02-08 23:59:59') ? '开通会员，享140元全品类券礼包+30元无门槛券' : '开通PLUS年卡，立享价值140元全品类券礼包';

            var cfg = pageConfig.product;
            // PLUS 拉新券 html 片段
            var _html = '<div id="J-summary-openPlusGift" class="open-plusgift">';
            _html += '   <div class="dt">PLUS礼包</div>';
            _html += '   <div class="dd">';
            _html += '      <a class="icon-plus" href="//plus.jd.com/index" target="_blank" clstag="shangpin|keycount|PC140PLUS_1545882013014|2"></a>';
            _html += '      <em>' + tipText + '</em>&nbsp;';
            _html += '      <a class="a-topluspage" href="//plus.jd.com/index" target="_blank" clstag="shangpin|keycount|PC140PLUS_1545882013014|2">详情 <s class="s-arrow">&gt;&gt;</s></a>';
            _html += '   </div>';
            _html += '</div>';

            // 获取用户信息
            var getUserInfo = function (cb) {
                $.ajax({
                    url: '//passport.jd.com/user/petName/getUserInfoForMiniJd.action',
                    dataType: 'jsonp',
                    success: function (r) {
                        if (typeof cb === 'function') {
                            cb(r);
                        }
                    },
                    error: function (err) {
                        if (typeof console !== 'undefined') {
                            console.log('获取用户信息失败');
                        }
                    }
                });
            }

            // 判断当前 sku 的价格是否符合 2000 - 6000 范围内 范围内的才显示拉新
            var isJdpriceInRange = (stock && stock.jdPrice && stock.jdPrice.p && Number(stock.jdPrice.p) >= 2000 && Number(stock.jdPrice.p) <= 6000) ? true : false;

            // 判断是否 不可用东券 商品，能使用 则展示，不能使用 则不展示; isCanUseJQ-0 为不可用东券标识
            // (如果没有 specialAttrs 字段 算是能使用东券)
            var isCanNotUseDQ = (cfg && cfg.specialAttrs && cfg.specialAttrs.join(',').indexOf('isCanUseJQ-0') !== -1) ? true : false;

            // 判断是否是 PLUS 价商品 非 PLUS 价商品 才展示拉新，tpp 字段为该sku的正式会员 + 试用会员 plus 价
            // (此处没获取到价格算是非 PLUS 价商品)
            var isPlusPriceGoods = (stock && stock.jdPrice && stock.jdPrice.tpp) ? true : false;

            // 判断三级分类 ID 排除以下 ID 的才展示拉新，
            // (此处没获取到分类算是符合拉新条件)
            var disableCatObj = {
                13922: 13922, 13923: 13923, 13924: 13924, 13925: 13925,
                13926: 13926, 13927: 13927, 13928: 13928, 13929: 13929,
                13930: 13930, 1444: 1444, 6151: 6151, 6152: 6152,
                13212: 13212, 13220: 13220, 13531: 13531, 13532: 13532,
                4835: 4835, 4833: 4833, 6980: 6980
            };
            var isDisableCat = (cfg && cfg.cat && cfg.cat[2] && disableCatObj[cfg.cat[2]] !== undefined) ? true : false;

            // 判断页面中是否已经存在 PLUS 礼包 的dom 节点
            // var hasOpenPlusGiftDom = $('#J-summary-openPlusGift').length > 0 ? true : false;

            // 依据以上条件判断是否展示拉新 (PLUS礼包)
            if (isJdpriceInRange && !isCanNotUseDQ && !isPlusPriceGoods && !isDisableCat) {
                // 获取用户信息 判断 plus 会员状态，除 正式中 或 正式过期 外 展示
                // plusStatus : 3 正式中 4 正式过期
                getUserInfo(function (userInfo) {
                    // 判断页面中是否已经存在 PLUS 礼包 的dom 节点
                    var hasOpenPlusGiftDom = $('#J-summary-openPlusGift').length > 0 ? true : false;
                    if (userInfo && !(userInfo.plusStatus == '3' || userInfo.plusStatus == '4') && !hasOpenPlusGiftDom) {
                        $('#summary-support').before(_html);
                        var skuId = pageConfig && pageConfig.product && pageConfig.product.skuid || '-';
                        try {
                            expLogJSON('PC140PLUS_1545882013014', '1', '{"sku": ' + skuId + '}');
                        } catch (e) {
                            if (typeof console !== 'undefined') {
                                console.log('PLUS礼包曝光埋点错误');
                            }
                        }
                    }
                });
            }
        }
    };

    module.exports = Stock;
})
