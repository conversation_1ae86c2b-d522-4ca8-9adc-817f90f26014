define('PUBLIC_ROOT/modules/contact/followshop', function(require, exports, module) {
    var login = require('JDF_UNIT/login/1.0.0/login');

    require('JDF_UNIT/trimPath/1.0.0/trimPath');

    var FollowShop = function(opts) {
        this.vids = opts.vids || [];
        this.$wrap = opts.$wrap || $('.pop-score-summary');
        this.$el = opts.$el || $('.J-follow-shop');

        // 是否已经设置过页面关注按钮状态
        this.checkStatus = false;

        this.onFollow = opts.onFollow || function () {};
        this.onunFollow = opts.onunFollow || function () {};

        this.init();
    };

    FollowShop.prototype = {
        init: function() {
            this.bindFollow();
            this.setStatus();
        },
        setStatus: function() {
            var _this = this;
            var currItem = null;
            var $fansPriceTxt = $(".J-fans-price .text");

            // 620503198510101214
            // 15293551766
            function setFollowStatus (r) {
                for (var i in r) {
                    if (r.hasOwnProperty(i)) {
                        currItem = _this.$wrap.find('[data-vid="'+ i +'"]');

                        currItem.attr('data-follow', !!r[i]);
                        if ( !!r[i] ) {
                            currItem.addClass('followed').attr('title', '点击取消关注');
                            currItem.find('span').html('已关注店铺');
                            pageConfig.product.isFans = true;
                            if (pageConfig.product.hasFansPrice) {
                                $fansPriceTxt.text("您已关注店铺，可享粉丝价");
                            }
                        } else {
                            currItem.removeClass('followed').attr('title', '点击关注');
                            currItem.find('span').html('关注店铺');
                            if (pageConfig.product.hasFansPrice) {
                                $fansPriceTxt.text("关注店铺，即享粉丝价");
                            } 
                        }
                    }
                }
            }
            this.checkStatus = true;

            $.ajax({
                url: '//fts.jd.com/follow/vender/batchIsFollow',
                data: {
                    venderIds: this.vids.join(','),
                    sysName: 'item.jd.com'
                },
                dataType: 'jsonp',
                success: function(r) {
                    if ( r && r.code === 'F10000' ) {
                        setFollowStatus(r.data);
                    } else if ( r && r.code === 'F10002' ) {  // 未登录
                        if (pageConfig.product.hasFansPrice) {
                            $fansPriceTxt.text("关注店铺，即享粉丝价");
                        } 
                    }
                }
            });
        },
        bindFollow: function() {
            var _this = this;

            this.$el.unbind()
                .bind('click', function () {
                    var $this = $(this);
                    var vid = $this.attr('data-vid');
                    var isFollow = $this.attr('data-follow') === 'true';

                    login({
                        modal: true,
                        complete: function() {
                            if (!_this.checkStatus) {
                                _this.setStatus();
                            }
                            if (isFollow) {
                                _this.unFollow(vid, $this);
                            } else {
                                _this.follow(vid, $this);
                            }
                        }
                    });
                });
        },
        follow: function(id, el) {
            var _this = this;
            if ( isNaN(parseInt(id)) ) {
                alert('不存在的商家');
                return false;
            }
            $.ajax({
                url: '//fts.jd.com/follow/vender/follow',
                dataType: 'jsonp',
                data: {
                    venderId: id,
                    sysName: 'item.jd.com'
                },
                success: function(r) {
                    if (!r) { return false; }

                    if (r.success && r.code === 'F10000') {
                        el.addClass('followed').attr('data-follow', 'true').attr('title', '点击取消关注');
                        el.find('span').html('已关注店铺');
                        _this.onFollow.call(_this);

                    } else if (r.code === 'F0401') {
                        alert('不存在的商家');
                    } else if (r.code === 'F0409') {
                        alert('已经关注过');
                    } else {
                        // alert('关注失败');
                    }
                }
            });
        },
        unFollow: function(id, el) {
            var _this = this;
            $.ajax({
                url: '//fts.jd.com/follow/vender/unfollow',
                dataType: 'jsonp',
                data: {
                    venderId: id,
                    sysName: 'item.jd.com'
                },
                success: function(r) {
                    if (r && r.code === 'F10000') {
                        el.removeClass('followed').attr('data-follow', 'false').attr('title', '点击关注');
                        el.find('span').html('关注店铺');
                        _this.onunFollow.call(_this);
                    } else {
                        // alert('关注失败');
                    }
                }
            });
        }
    };

    module.exports = FollowShop;
});
