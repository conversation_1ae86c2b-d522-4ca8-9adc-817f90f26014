define('PUBLIC_ROOT/modules/buybtn/bigouma', function(require, exports, module) {
    var login = require('JDF_UNIT/login/1.0.0/login');
    var G = require('PUBLIC_ROOT/modules/common/core');
    var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event;

    var bgm = {
        init: function (cfg) {
            this.sku = cfg.skuid;
            this.cfg = cfg;

            this.$cd = $('#choose-countdown');
            this.$cdTxt = this.$cd.find('#bgm-countdown');
            this.$txt = $('.J-bgm-text');
            this.$btn = $('#choose-btn-bgm');
            this.$btnWrap = $('#choose-btns');

            this.BTN_DISABLE = 'btn-disable';

            if (this.$btn.length) {
                this.onBGM();
                this.get();
                this.bindEvent();
            }

            return this;
        },

        bindLogin: function () {
            var _this = this;

            this.$btnWrap.delegate('.J-s-login', 'click', function () {
                _this.loginIframe();
                return false;
            });
        },

        bindEvent: function () {
            var _this = this;

            function getUrl($el, count) {
                var href = $el.attr('href') || '';
                return href.replace(/pcount=\d+/, 'pcount=' + count);
            }

            // 必购码按钮数量添加
            Event.addListener('onNumChange', function(data) {
                var $bind = _this.$txt.find('.J-bind');

                $bind.attr('href', getUrl($bind, data.count));
                if (_this.bgmEnabled) {
                    _this.enabled();
                }
            });

            Event.addListener('onStockReady', function (data) {
                if ( pageConfig.product.havestock ) {
                    if (_this.bgmEnabled) {
                        _this.enabled();
                    }
                } else {
                    _this.disabled();
                }
            });
        },

        loginIframe: function () {
            login({
                modal: true,
                complete: function() {
                    // remove hash
                    //window.location.href = window.location.href.replace(/#\w+$/, '');
                    window.location.reload(true);
                }
            });
        },

        onBGM: function () {
            this.$btn.show();
            this.cfg.addToCartBtn.hide();
        },

        get: function () {
            var _this = this;
            $.ajax({
                url: '//jcode.jd.com/jcodeinfo.action',
                data: {
                    sku: this.sku
                },
                dataType: 'jsonp',
                success: function (r) {
                    _this.set(r);
                }
            });
        },

        enabled: function (href) {
            this.bgmEnabled = true;

            if ( this.cfg.isClosePCShow || !this.cfg.havestock ) { return false; }

            this.$btn.removeClass(this.BTN_DISABLE);
            this.$btn.attr('href', href || this.cfg.addToCartBtn.href);
            this.$txt.addClass('bgm-text-active');
        },
        disabled: function () {
            this.$btn.attr('href', '#none');
            this.$btn.addClass(this.BTN_DISABLE)
        },

        set: function (r) {
            var hasAuthorize = r.cs && r.cs === 1;
            var start = r.d && r.d > 0;

            this.isLogin = r && r.lg && r.lg === 1;

            if (r.jtype === 2) {
                this.enabled();
                this.setBtnText('加入购物车');
                return false;
            }

            if (this.isLogin) {
                this.$btnWrap.undelegate('click');
            }

            if ( hasAuthorize ) {
                // 活动是否进行中
                if (r.status === 2 && start) {
                    this.setCountdown(r.d);
                }

                // 未登录
                if (!r.lg) {
                    this.setLoginTips();
                    this.bindLogin();
                    return false;
                }
                // 未绑定
                if (!r.b) {
                    return this.setBindTips(r.url);
                } else {
                    this.enabled();
                }
            } else {
                this.setSupportText(r&&r.source);
            }
        },
        setBtnText: function (text) {
            this.$btn.text(text || '必购码购买');
        },
        setSupportText: function (s) {
            s = s.substr(1);
            var support = [
                '京东app',
                '微信',
                '手机QQ'
            ];
            var supportText = [];
            if (s) {
                var str = s.split('');
                for (var i = 0; i < str.length; i++) {

                    if (str[i] === '1') {
                        supportText.push(support[i]);
                    }
                }
            }

            if (supportText.length > 0) {
                this.$txt.html('请到'+ supportText.join('、') +'购买');
            } else {
                this.$txt.html('');
            }

        },
        setLoginTips: function () {
            this.$txt.html('请 <span href="#none" class="hl_blue J-s-login" style="cursor:pointer">登录</span> 后确认购买资格');
        },
        setBindTips: function (url) {
            if (url.indexOf('pcount') < 0) {
                url += '&pcount=1';
            }

            var html = '\
            请先 <a href="'+ url +'" class="hl_blue J-s-login J-bind" style="display:inline;" target="_blank">绑定/领取必购码 </a>再购买。';
            //<a href="//yushou.jd.com/member/qualificationList.action" target="_blank" class="hl_blue" style="display:inline;">什么是必购码？</a>

            this.$txt.html(html);
        },
        setCountdown: function (second) {
            var _this = this;

            this.$cd.show();

            function formatTime(tpl, date) {
                return tpl.replace(/{d}/g, date.d)
                    .replace(/{h}/g, date.h)
                    .replace(/{m}/g, date.m)
                    .replace(/{s}/g, date.s);
            }
            G.Countdown.init(second, function(res) {
                _this.$cdTxt.html( formatTime('{d}天{h}小时{m}分{s}秒', res) );
            });
        }
    };

    module.exports = bgm;
    module.exports.__id = 'bgm';
});
