@import "./__sprite";

.brand-logo {
    text-align: center;
}

#track.hover {
    .extra {
        display: block;
        right:10px;
        left:-210px;
        box-shadow: 0 0 10px #f0f0f0;
    }
    .extra-trigger {
        display: none;
    }
}
    .track{
        display: none;
        background:#fff;
        position:absolute;
        *z-index: 6;
        right: 0;
        top:-10px;
        .extra-trigger{
            position: relative;
            z-index: 11;
            float: left;
            margin-right: -24px;
            width:23px;
            height: 519px;
            text-align: center;
            border-left:1px solid #f2f2f2;
            cursor: pointer;
            .sprite-extra{
                display: block;
                overflow: hidden;
                margin: 0 0 5px 6px;
                @include sprite-extra;
            }
            a{
                display: block;
                padding-top: 140px;
                &:hover{
                    color: #666;
                }
            }
        }
        .extra{
            display: none;
            width: 210px;
            // position: absolute;
            // top:0;
            // left:24px;
            // right:-210px;
            z-index:10;
            background: #fff;
        }
        .track-tit{
            position: relative;
            line-height: 50px;
            font-size: 12px;
            color: #666;
            font-weight: normal;
            text-align: center;
            span{
                position: absolute;
                z-index: 1;
                left: 0;
                right: 0;
                top:25px;
                height: 1px;
                background: #f2f2f2;
            }
            h3{
                position: relative;
                z-index: 2;
                display: inline-block;
                *display: inline;
                *zoom:1;
                background: #fff;
                padding: 0 25px;
            }
        }
        .track-con{
            width: 150px;
            // height: 510px;
            .cat-1-737 & {
                height: 340px;
            }
            overflow: hidden;
            margin: 0 auto;
            *float: right;
            li{
                position: relative;
                padding: 10px 0;
                a {
                    position: relative;
                    *zoom: 1;
                    *display: inline;
                    display: inline-block;
                }
            }
            .title {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                position: absolute;
                left: 0;
                right: 0;
                top: 126px;
                line-height: 12px;
                background: rgba(255, 255, 255, .9);
                filter:progid:DXImageTransform.Microsoft.gradient(startcolorstr=#9FFFFFFF,endcolorstr=#9FFFFFFF);
                padding: 6px;
                *width: 150px;
                z-index: 3;
            }
            p{  
                *width: 150px;
                line-height: 18px;
                color: #C81623;
                text-align: center;
            }
            img{
                width: 150px;
            }
        }
        .track-more{
            padding-top: 10px;
            text-align: center;
            clear:both;
            a{
                display: inline-block;
                margin: 0 10px;
                text-indent: -9999px;
                overflow: hidden;
            }
            .sprite-down{
                @include sprite-down;
            }
            .sprite-up{
                @include sprite-up;
            }
        }
    }
    .root61{
        .track{
            width:210px;
            position: static;
            float: right;
            right:auto;
            .extra-trigger{
                display: none;
            }
            .extra{
                display: block;
                right:auto;
                left: auto;
                box-shadow: none;
            }
            &:hover .extra{
                top:0;
            }
        }
    }
