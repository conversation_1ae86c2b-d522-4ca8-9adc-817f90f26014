define('MOD_ROOT/fittingSuit/accessories', function(require, exports, module) {
    var trimPath   = require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var switchable = require('JDF_UI/switchable/1.0.0/switchable');
    var lazyload   = require('JDF_UI/lazyload/1.0.0/lazyload');
    var tools = require('MOD_ROOT/common/tools/tools');
    var G = require('MOD_ROOT/common/core');
    
    // 推荐配件文件已下线，先注释

    // var templateFittingCommon = '\
    //     <ul class="stab lh">\
    //         <li class="J_fitting_switcher fore0 scurr" \
    //             onclick=\'log("gz_item", "gz_detail","02","tjpj_pjfl_全部配件","","main")\' \
    //             data-sort="0">全部配件</li>\
    //         {for item in fittingType}\
    //         <li class="J_fitting_switcher fore${item_index+1}" \
    //             onclick=\'log("gz_item", "gz_detail","02","tjpj_pjfl_${item.name}","","main")\' \
    //             data-sort="${item.sort}">${item.name}(${item.number})</li>\
    //         {/for}\
    //     </ul>\
    //     <div class="stabcon clearfix">\
    //         <div class="master"><s></s>\
    //             <div class="p-img">\
    //                 <a href="//item.jd.com/${master.skuid}.html" target="_blank">\
    //                     <img width="100" height="100" src="${pageConfig.FN_GetImageDomain(master.skuid)}n4/${master.pic}">\
    //                 </a>\
    //             </div>\
    //             <div class="p-name">\
    //                 <a href="//item.jd.com/${master.skuid}.html" target="_blank" title="${master.name}">${master.name}</a>\
    //             </div>\
    //             <div class="p-price hide"><input type="checkbox" data-sku="${master.skuid}" data-jp="${master.price}" data-mp="${master.discount}" checked></div>\
    //         </div>\
    //         <div class="suits">\
    //             <ul class="lh" style="width:${fittings.length*197}px">\
    //                 {for item in fittings}\
    //                 <li data-push="${pageConfig[skuHooks].push(item.skuid)}" \
    //                     data-sort="${item.sort}" \
    //                     onclick=\'log("gz_item", "gz_detail","02","tjpj_sp_${item_index}",${item.skuid},"main")\' \
    //                     class="fore${item_index} {if Number(item_index)+1==fittings.length} last-item{/if}">\
    //                     <s></s>\
    //                     <div class="p-img">\
    //                         <a href="//item.jd.com/${item.skuid}.html" target="_blank">\
    //                             <img width="100" height="100" src="${pageConfig.FN_GetImageDomain(item.skuid)}n4/${item.pic}">\
    //                         </a>\
    //                     </div>\
    //                     <div class="p-name">\
    //                         <a href="//item.jd.com/${item.skuid}.html" target="_blank" title="${item.name}">${item.name}</a>\
    //                     </div>\
    //                     <div class="choose">\
    //                         <input type="checkbox" \
    //                             data-sku="${item.skuid}" \
    //                             onclick=\'log("gz_item", "gz_detail","02","tjpj_sp_${item_index}",${item.skuid},"main")\' \
    //                             data-jp="${item.price}" data-mp="${item.discount}"/>\
    //                         <label class="p-price"><strong class="J-p-${item.skuid}">￥${item.price}</strong></label>\
    //                      </div>\
    //                 </li>\
    //                 {/for}\
    //             </ul>\
    //         </div>\
    //         <div class="infos">\
    //             <s></s>\
    //             <div class="p-name">\
    //                 <span class="J_selected" href="#">已选择0个配件</span>\
    //             </div>\
    //             <div class="p-price">搭&nbsp;配&nbsp;价：<strong class="J_cal_jp J-p-${G.sku}">￥${master.price}</strong>\
    //             </div>\
    //             <!--<div class="p-saving">获得优惠：<del class="J_cal_mp">￥${master.discount}</del></div>-->\
    //             <div class="btns"\
    //                 onclick=\'log("gz_item", "gz_detail","02","tjpj_ycgm_ljgm", pageConfig.getAccSelectedSkus() ,"main")\' >\
    //                 <a class="btn-buy" target="_blank" href="//cart.jd.com/reBuyForOrderCenter.action?wids=${master.skuid}&nums=1">立即购买</a>\
    //             </div>\
    //         </div>\
    //     </div>';


    // var templateFittingAcc = '\
    //     <ul class="stab lh">\
    //         {for item in list}\
    //         {if item.accessoryShows && item.accessoryShows.length > 0}\
    //         <li class="ui-switchable-item J_fitting_switcher fore${Number(item_index)+1}" \
    //             onclick=\'log("gz_item", "gz_detail","02","tjpj_pjfl_${item.typeName}","","main")\' \
    //             data-sort="${item.typeId}" \
    //             data-name="${item.typeName}">${item.typeName}</li>\
    //         {/if}\
    //         {/for}\
    //     </ul>\
    //     <div class="stabcon clearfix">\
    //         <div class="master">\
    //             <s></s>\
    //             <div class="p-img">\
    //                 <a href="//item.jd.com/${wid}.html" target="_blank">\
    //                     <img width="100" height="100" src="${pageConfig.FN_GetImageDomain(wid)}n4/${imageUrl}">\
    //                 </a>\
    //             </div>\
    //             <div class="p-name">\
    //                 <a href="//item.jd.com/${wid}.html" target="_blank" title="${wName}">${wName}</a>\
    //             </div>\
    //             <div class="p-price hide"><input type="checkbox" data-sku="${wid}" data-jp="" data-mp="" checked></div>\
    //         </div>\
    //         <div class="suits">\
    //             {for tab in list}\
    //                 {if tab.accessoryShows && tab.accessoryShows.length > 0}\
    //                 <div class="ui-switchable-panel">\
    //                 <ul class="J_fitting_con lh" style="width:${tab.accessoryShows.length*197}px">\
    //                     {for item in tab.accessoryShows}\
    //                     <li data-push="${pageConfig[skuHooks].push(item.wid)}" \
    //                         onclick=\'log("gz_item", "gz_detail","02","tjpj_sp_${item_index}",${item.wid},"main")\' \
    //                         data-sort="${item.thirdType}" \
    //                         data-ltag="02,sp_${item.typeName},0,main" \
    //                         class="{if Number(item_index)+1==tab.accessoryShows.length}last-item{/if}">\
    //                         <s></s>\
    //                         <div class="p-img">\
    //                             <a href="//item.jd.com/${item.wid}.html" target="_blank">\
    //                                 <img width="100" height="100" {if Number(tab_index)!=0}data-{/if}src="${pageConfig.FN_GetImageDomain(item.wid)}n4/${item.imageUrl}">\
    //                             </a>\
    //                         </div>\
    //                         <div class="p-name">\
    //                             <a href="//item.jd.com/${item.wid}.html" target="_blank" title="${item.wName}">${item.wName}</a>\
    //                         </div>\
    //                         <div class="choose">\
    //                             <input type="checkbox" data-sku="${item.wid}" data-jp="" data-mp="" \
    //                                 onclick=\'log("gz_item", "gz_detail","02","tjpj_fxk_${item_index}",${item.wid},"main")\' />\
    //                             <label class="p-price"><strong class="J-p-${item.wid}">￥</strong></label>\
    //                          </div>\
    //                         {if moreLink && Number(tab_index)==0}\
    //                         <div class="p-more">\
    //                             <a class="hl_link" data-stype="${item.typeId}" target="_blank" \
    //                                 onclick=\'log("gz_item", "gz_detail","02","tjpj_gd_${item_index}",${item.wid},"main")\' \
    //                                 href="${pageConfig.getMoreLink(moreLink[1],item.typeId)}">更多${item.typeName}</a>\
    //                         </div>\
    //                         {/if}\
    //                     </li>\
    //                     {/for}\
    //                 </ul>\
    //                 </div>\
    //                 {/if}\
    //             {/for}\
    //         </div>\
    //         <div class="infos">\
    //             <s></s>\
    //             {if moreLink}\
    //             <div id="more-fitting-link">\
    //                 <a class="hl_link" \
    //                     href="${moreLink[0]}" \
    //                     onclick=\'log("gz_item", "gz_detail","02","tjpj_ycgm_pjzx",null,"main")\' \
    //                     target="_blank">进入配件选购中心</a>\
    //                 <span>&gt;<!--<b></b>--></span>\
    //             </div>\
    //             {/if}\
    //             <div class="p-name">\
    //                 <span class="J_selected">已选择0个配件</span>\
    //             </div>\
    //             <div class="p-price">搭&nbsp;配&nbsp;价：<strong class="J_cal_jp">￥</strong></div>\
    //             <div class="p-saving hide">参&nbsp;考&nbsp;价：<del class="J_cal_mp">￥</del></div>\
    //             <div class="btns" \
    //                 onclick=\'log("gz_item", "gz_detail","02","tjpj_ycgm_ljgm", pageConfig.getAccSelectedSkus() ,"main")\' \ >\
    //                 <a class="btn-buy" target="_blank" href="//cart.jd.com/reBuyForOrderCenter.action?wids=${wid}&nums=1">立即购买</a>\
    //             </div>\
    //         </div>\
    //     </div>';

    // var moreLink = false;
    // var fType = '';

    // // 手机
    // if ( G.cat[2] === 655 ) {
    //     moreLink = [
    //         '//kong.jd.com/mobile/accyCenter?sku='+ G.sku,
    //         '//kong.jd.com/mobile/accyCenter?sku='+ G.sku +'&category={cat}'
    //     ];
    //     fType = 'phone';
    // }

    // // 数码相机
    // if ( new RegExp('-' + G.cat[2] + '-').test('-831-832-************-') ) {
    //     moreLink = [
    //         '//yxpeijian.jd.com/accyCenter/selfChoose?sku='+ G.sku,
    //         '//yxpeijian.jd.com/accyCenter/selfChoose?sku='+ G.sku +'&category={cat}'
    //     ];
    // }
    // // IT 配件
    // if ( new RegExp('-' + G.cat[2] + '-').test('-717-720-718-719-798-') ) {
    //     moreLink = [
    //         '//iaccy.jd.com/accessories/accessoryCenter/'+ G.sku +'.html',
    //         '//iaccy.jd.com/accessories/accessoryCenter/'+ G.sku +'-{cat}.html'
    //     ];
    // }
    // // 笔记本配件
    // if ( new RegExp('-' + G.cat[2] + '-').test('-672-6864-1105-') ) {
    //     moreLink = [
    //         '//kong.jd.com/mobile/notebookCenter?sku=' + G.sku,
    //         '//kong.jd.com/mobile/notebookCenter?sku='+ G.sku +'&category={cat}'
    //     ];
    // }

    // if (G.cat[2] == 2694) {
    //     moreLink = [
    //         '//yxpeijian.jd.com/accyCenter?sku=' + G.sku,
    //         '//yxpeijian.jd.com/accyCenter?sku='+ G.sku +'&category={cat}'
    //     ];
    // }

    // pageConfig.getMoreLink = function (link, cat) {
    //     return link.replace('{cat}', cat);
    // };
    // pageConfig.getAccSelectedSkus = function () {
    //     return pageConfig.accSelectedSkus.join('-') || G.sku;
    // };

//     var FittingAcc = {
//         skuHooks: 'SKUS_Fitting',
//         $el: $('#fitting-con'),
//         sku: G.sku,
//         moreLink: moreLink,
//         fType: fType,
//         bindEvent: function() {
//             var _this = this;

//             this.$el.delegate('.choose input', 'click', function() {
//                 var sku = $(this).attr('data-sku');
//                 _this.check(sku, $(this));
//             });
//             this.$el.delegate('.btn-buy', 'click', function () {
//                 _this.setLog();
//             });

//             return this;
//         },
//         check: function(sku, $el) {
//             var _this = this;
//             var buyUrl = '//cart.jd.com/reBuyForOrderCenter.action?';
// //            if (pageConfig.product.useOtcSuitBuyUrl) {
// //                buyUrl = '//cart.yiyaojd.com/reBuyForOrderCenter.action?';
// //            }
//             var buyEl = this.$el.find('.btn-buy');

//             $el = $el || this.$el.find('.J_fitting_con li').eq(0).find('input[data-sku]');
//             sku = sku || $el.attr('data-sku');

//             function onChecked(count, skus) {
//                 pageConfig.accSelectedSkus = skus;
//                 _this.$el.find('.J_selected').html('已选择'+ ( count-1 ) +'个配件');
//                 buyEl.attr('href', buyUrl + $.param({
//                     wids: skus.join(','),
//                     nums: 1
//                 }));
//             }

//             G.calPrice({
//                 $el: this.$el,
//                 sku: sku,
//                 input: $el,
//                 targetJP: this.$el.find('.infos .J_cal_jp'),
//                 targetMP: this.$el.find('.infos .J_cal_mp'),
//                 callback: onChecked
//             });
//         },
//         setLog: function () {
//             var $panel = this.$currentPanel;
//             var skus = [];

//             if ( $panel ) {
//                 $panel.find('input').each(function () {
//                     var isChecked = $(this).attr('checked');

//                     if ( isChecked ) {
//                         skus.push( $(this).attr('data-sku') );
//                     }
//                 });
//             }

//             // 发送当前选中的sku log信息
//             if ( skus.length ) {
//                 log(
//                     'gz_item',
//                     'gz_detail',
//                     G.sku,
//                     G.cat[0],
//                     G.cat[1],
//                     G.cat[2],
//                     1,
//                     7,
//                     6,
//                     skus.join('_')
//                 );
//             }
//         },
//         set: function(data) {
//             var _this = this;

//             pageConfig[this.skuHooks] = [G.sku];

//             // 挂载sku全局变量钩子
//             data.skuHooks = this.skuHooks;
//             data.moreLink = this.moreLink;
//             data.fType = this.fType;

//             // 默认第一个[精选配件]tag数据是每个分类下面第一条配件数据的集合
//             var result = [];
//             for (var i = 0; i < data.list.length; i++) {
//                 var curr = data.list[i];
//                 var accList = curr.accessoryShows;

//                 if (accList.length && accList[0].wid) {
//                     accList[0].typeName = curr.typeName;
//                     accList[0].typeId = curr.typeId;

//                     result.push(accList[0]);
//                 }
//             }

//             var firstTab = {
//                 typeId: 0,
//                 typeName: '精选配件',
//                 accessoryShows: result
//             };

//             var tabName = $('#fitting-suit .tab li a').eq(0).text();
//             if (tabName !== '精选配件') {
//                 firstTab.typeName = tabName;
//             }
//             data.list.unshift(firstTab);

//             this.$el.html(templateFittingAcc.process(data));

//             this.$el.switchable({
//                 event: 'click',
//                 delay: 0,
//                 navSelectedClass: 'scurr',
//                 callback: function(i) {
//                     var panel   = this.main.eq(i);
//                     var item    = this.nav.eq(i);
//                     var name    = item.attr('data-name');
//                     var sId     = item.attr('data-sort');

//                     var $target = _this.$el.find('#more-fitting-link a');
//                     _this.$currentPanel = panel;

//                     if ( i !== 0 ) {
//                         tools.triggerLazyImg(panel);
//                     }

//                     if ( _this.moreLink ) {
//                         if ( sId === '0' ) {
//                             $target.attr('href', moreLink[0]).html('配件选购中心');
//                         } else {
//                             $target.attr('href', pageConfig.getMoreLink(moreLink[1], sId)).html('进入' + name);
//                         }
//                     }
//                 }
//             });

//             // 获取价格
//             tools.priceNum({
//                 skus: pageConfig[this.skuHooks],
//                 $el: this.$el,
//                 callback: function(sku, r) {
//                     var currentEl = _this.$el.find('input[data-sku="'+ sku +'"]');

//                     currentEl.attr('data-jp', r.p);
//                     currentEl.attr('data-mp', r.m);
//                 },
//                 onReady: function (r) {
//                     _this.check();
//                 }
//             });
//         }
//     };

//     var FittingCommon = {
//         skuHooks: 'SKUS_Fitting',
//         $el: $('#fitting-con'),
//         sku: G.sku,
//         current: 'scurr',
//         bindEvent: function() {
//             var _this = this;

//             this.$el.delegate('.J_fitting_switcher', 'click', function() {
//                 var sId = $(this).attr('data-sort');
//                 _this.switchTab(sId, $(this));
//             });

//             this.$el.delegate('.choose input', 'click', function() {
//                 _this.check($(this));
//             });

//             return this;
//         },
//         switchTab: function(sid, $el) {
//             var fittingItems    = this.$el.find('.suits li');
//             var fittingTriggers = this.$el.find('.J_fitting_switcher');

//             fittingTriggers.removeClass(this.current);
//             $el.addClass(this.current);

//             // 全部配件
//             if ( sid === '0' ) {
//                 fittingItems.show();
//             } else {
//                 fittingItems.hide();
//                 fittingItems.filter('[data-sort="'+ sid +'"]').show().last().addClass('last-item');
//             }
//         },
//         check: function($el) {
//             var _this  = this;
//             var buyUrl = '//cart.jd.com/reBuyForOrderCenter.action?';

// //            if (pageConfig.product.useOtcSuitBuyUrl) {
// //                buyUrl = '//cart.yiyaojd.com/reBuyForOrderCenter.action?';
// //            }

//             var buyEl  = this.$el.find('.btn-buy');

//             var $targets = this.$el.find('input');
//             var price = 0, count = 0, skus = [];

//             $targets.each(function() {
//                 var jp = $(this).attr('data-jp');
//                 var sku = $(this).attr('data-sku');

//                 if ( $(this).attr('checked') ) {
//                     count++;
//                     if ( jp ) {
//                         jp = parseFloat(jp);
//                         price += jp;
//                     }
//                     if ( sku ) {skus.push(sku); }
//                 }
//             });

//             pageConfig.accSelectedSkus = skus;

//             this.$el.find('.J_cal_jp').html('￥' + price.toFixed(2));

//             this.$el.find('.J_selected').html('已选择'+ ( count - 1 ) +'个配件');
//             buyEl.attr('href', buyUrl + $.param({
//                 wids: skus.join(','),
//                 nums: 1
//             }));


//             // G.calPrice({
//             //     $el: this.$el,
//             //     sku: sku,
//             //     input: $el,
//             //     targetJP: this.$el.find('.infos .J_cal_jp'),
//             //     targetMP: this.$el.find('.infos .J_cal_mp'),
//             //     callback: onChecked
//             // });
//         },
//         get: function() {
//             var _this = this;

//             $.ajax({
//                 url: '//d.jd.com/fittingInfo/get',
//                 dataType: 'jsonp',
//                 scriptCharset: 'gb2312',
//                 data: {
//                     skuId: this.sku
//                 },
//                 success: function(r) {
//                     var hasData = !!(r && r.fittings && r.fittings.length);
//                     if ( hasData ) {
//                         _this.set(r);
//                     }

//                     _this.callback.apply(_this, [hasData, r]);
//                 }
//             });
//         },
//         set: function(data) {
//             var _this = this;

//             pageConfig[this.skuHooks] = [G.sku];

//             // 挂载sku全局变量钩子
//             data.skuHooks = this.skuHooks;
//             this.$el.html(templateFittingCommon.process(data));

//             // 获取价格
//             tools.priceNum({
//                 skus: pageConfig[this.skuHooks],
//                 $el: this.$el,
//                 callback: function(sku, r) {
//                     var currentEl = _this.$el.find('input[data-sku="'+ sku +'"]');

//                     currentEl.attr('data-jp', r.p);
//                 }
//             });
//         }
//     };

    function init(data) {
        // var hasData = !!(data && data.list && data.list.length)
        //     && pageConfig.fittingSuitTab.nav.eq(0).data('disabled') !== 1;

        // if ( hasData ) {
        //     FittingAcc.bindEvent().set(data);

        //     // pageConfig.fittingSuitTab.loaded[0] = hasData;
        //     pageConfig.fittingSuitTab.show(0);
        // }
        //
        //hasData = !!(data && data.fittings && data.fittings.length);
        //if ( hasData ) {
        //
        //    // 图书分类
        //    if (G.cat[0] === 1713) {
        //        return false;
        //    }
        //
        //    FittingCommon.bindEvent().set(data);
        //
        //    // pageConfig.fittingSuitTab.loaded[0] = hasData;
        //    pageConfig.fittingSuitTab.show(0);
        //}

    }

    module.exports = init;
});