define('MOD_ROOT/address/address', function (require, exports, module) {
    var Stock = require('MOD_ROOT/address/stock');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Tools = require('MOD_ROOT/common/tools/tools');
    
    var domain='jd.com';
    var oArea = null

    try{
        if (window.pageConfig &&
            typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
            domain = pageConfig.FN_getDomainNew();
        }else if (window.pageConfig &&
            typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
            domain = pageConfig.FN_getDomain();
        }else {
            domain = document.domain;
        }
    }catch(e){
        domain = document.domain;
    }
    /**
     * 构建地址组件
     * @param {jQuery} $mount jQuery对象
     * @param {Object} params 地址组件配置
     * @returns {Object}
     */
    function buildAddressComponent($mount, params) {
        var __html = '\
            <div class="ui-area-text-wrap"><!--展示内容主体-->\
                <div class="ui-area-text">--请选择--</div><!--显示被选中的地区-->\
                <b></b><!--小箭头-->\
            </div>\
            <div class="ui-area-content-wrap"><!--弹出内容主体-->\
                <div class="ui-area-tab"></div><!--省市区选择标签-->\
                <div class="ui-area-content"></div><!--地区内容-->\
            </div>\
        ';
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        var params = $.extend({
            scopeLevel: 4,
            hasOversea: true,
            hasCommonAreas: true,
            showAllCommonArea: true,
            commonAreaServiceUrl: host + '/usual/address?appid=item-v3&functionId=pc_usual_address'
        }, params);

        if ($mount.length > 0 && typeof $mount.area === 'function') {
            return $mount.addClass('ui-area-wrap').html(__html).area(params);
        } else {
            return null;
        }
    }

    /**
     * setUserLocationCookie
     * @param {Object} local
     * @param {Object} area
     */
    function setUserLocationCookie(area, local) {
        local = local || {};
        var data = local.commonAreaData || {};

        var gcLng = data.longitudeString || ""; // gcLng
        var gcLat = data.latitudeString || ""; // gcLat
        if (gcLng && gcLat) {
            createCookie(
                "detailedAdd_coord",
                gcLng + ',' + gcLat,
                10,
                '/;domain=' + domain
            );
        } else {
            createCookie(
                "detailedAdd_coord",
                "",
                -1,
                "/;domain=" + domain
            );
        }

        if (data.fullAddress) {
            createCookie(
                "detailedAdd",
                encodeURIComponent(data.fullAddress),
                10,
                "/;domain=" + domain
            );
            createCookie(
                "detailedAdd_areaid",
                this.data && this.data.tempLocalId && this.data.tempLocalId.join('-'),
                10,
                '/;domain=' + domain
            );
        } else {
            createCookie(
                "detailedAdd",
                "",
                -1,
                "/;domain=" + domain
            );
            createCookie(
                "detailedAdd_areaid",
                "",
                -1,
                "/;domain=" + domain
            );
        }
    }

    function callStock(fn) {
        new Stock({}, function (resp) {
            Event.fire({
                type: 'onStockReady',
                area: {
                    id: this.areas
                },
                stock: resp
            });
            (typeof fn === "function") &&
                fn.call(this, resp);
        }, function (resp) {
            Event.fire({
                type: 'onStockError',
                area: {
                    id: this.areas
                },
                stock: resp
            });
        });
    }

    function triggerOnAreaChange(resp){
        Event.fire({
            type: 'onAreaChange',
            area: {
                id: this.areas
            },
            stock: resp
        });
    }

    function fireAreaInfoEvent(area){
        Event.fire({
            type: 'onAreaInfo',
            area: area
        });
    }

    /**
     * 更新地址信息函数
     *
     * @param addressData 包含地址信息的对象
     */
    function updateArea (addressData) {
        if(!addressData || Object.keys(addressData).length == 0) return

        // 设置区域信息
        var provinceId = addressData.provinceId;
        var cityId = addressData.cityId;
        var countyId = addressData.countyId;
        var townId = addressData.townId || 0;
        // 拼接规则： 'provinceId-cityId-countyId-townId'
        var regionCode = provinceId + '-' + cityId + '-' + countyId + '-' + townId;
        // 获取经纬度信息
        var longitudeString = addressData.longitudeString;
        var latitudeString = addressData.latitudeString;
        var fullAddress = addressData.fullAddress;

        // 更新cookie信息
        if(typeof createCookie == 'function' ) {

            // 设置cookie
            setUserLocationCookie({}, {
                commonAreaData: {
                    longitudeString: longitudeString,
                    latitudeString: latitudeString,
                    fullAddress: fullAddress,
                }
            });

            // 设置cookie - ipLoc-djd
            createCookie(
                "ipLoc-djd",
                regionCode,
                30,
                '/;domain=jd.com'
            );
            
            // 更新地址
            callStock(triggerOnAreaChange)

            // 更新地址信息到组件上
            if(oArea 
                && oArea.areaInstance 
                && oArea.areaInstance.current 
                && typeof oArea.areaInstance.current.setValue === "function") {

                oArea.areaInstance.current.setValue(regionCode)

            } else {

                location.reload()
            }

        }

        
    }

    function init(cfg) {
        $("#J_LogisticsService").show()
        /// 初始化
        callStock(triggerOnAreaChange);
        
        Event.addListener("onNumChange", callStock);

        var $mount = $("#area-2024");
        if ($mount.length === 0) {
            return;
        }

        // 获取地址组件信息
        function getAreaInfo(){
            try{
                oArea = window.pageConfig && window.pageConfig.area && window.pageConfig.area($('#area-2024'), {
                    scopeLevel: 4,
                    hasOversea: true,
                    hasCommonAreas: true,
                    showAllCommonArea: true,
                    topClassName:'item',
                    isUpScroll: true, //商详地址面板距离底部小于95px时，屏幕自动向上滚动
                    onReady: function (area) {
                        /// 由于`//lapi.jd.com/locate`定位接口返回的区域信息有缺少三级地址的情况，
                        /// 导致stock接口不能准确的输出库存数据，故通过此方式进行修正；
                        var regionCode = readCookie("ipLoc-djd");
                        var hasTheThirdLevelAddress = regionCode ?
                            regionCode.split(/-|_/)[2] > 0 :
                            false;
                        
                        if (!hasTheThirdLevelAddress) {
                            createCookie(
                                "ipLoc-djd",
                                this.data.tempLocalId.join('-'),
                                30,
                                '/;domain=' + domain
                            );
                            callStock(triggerOnAreaChange);
                        }
                        
                        // 触发onAreaInfo事件， 对外暴露地址信息，便于其他组件使用
                        fireAreaInfoEvent(area);
                    },
                    onChange: function (local, area) {
                        setUserLocationCookie.call(this, local, area);
                        callStock(triggerOnAreaChange);
                        try{
                            Tools.landmine({
                                functionName: 'PC_Productdetail_SZFloat_Address_Click', 
                                exposureData: ['mainskuid'],
                                extraData: {
                                    tabName: $(".ui-area-content-tab-btn-current").html() || "",
                                    addrid: Tools.getAreaId().areaIds.join('_') || ""
                                },
                                errorTips: '地址选择点击错误'
                            })
                        }catch(e){
                            console.log("地址选择点击错误")
                        }
                        
                        // 触发onAreaInfo事件， 对外暴露地址信息，便于其他组件使用
                        fireAreaInfoEvent(area);
                    },
                    onTabClick: function(name) {
                        try{
                            Tools.landmine({
                                functionName: 'PC_Productdetail_SZFloat_Tab_Click', 
                                exposureData: ['mainskuid'],
                                extraData: {
                                    tabName: name
                                },
                                errorTips: '地址tab点击错误'
                            })
                        }catch(e){
                            console.log("地址tab点击错误")
                        }
                    }
                })
            }catch(e){
                //烛龙上报
               console.error("地址组件调用init异常:",e)
            }
        }
        var num = 1
        if(pageConfig.product && pageConfig.product.addressKsSwitch){ // addressKsSwitch 等于true的时候用轮询，false的时候走旧线上逻辑
            function checkCondition() {
                // 返回 true 或 false
                if(window.pageConfig && window.pageConfig.area){
                    return true;
                }else{
                    console.error("地址组件没有加载，轮询开始")
                    num+1;
                    return false;
                    
                }
            }
            var pollInterval = setInterval(function() {
                if (checkCondition() || num > 20) { // 公共组件已加载完成或者轮询3秒都会停止轮询
                    getAreaInfo() // 获取地址组件信息
                    clearInterval(pollInterval); // 停止轮询
                }
            }, 500) // 每500毫秒检查一次
        }else{
            getAreaInfo() // 获取地址组件信息
        }
    }

    module.exports.__id = 'address';
    module.exports.init = init;
    module.exports.callStock = callStock;
    module.exports.updateArea = updateArea;
});
