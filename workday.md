# 京东单品页前端项目工作日志

## 项目概述

### 基本信息
- **项目名称**: 通用版单品页前端源代码
- **项目类型**: 京东商城单品页面前端系统
- **当前版本**: 1.1.12
- **开发团队**: 京东前端团队（周琪力、金锴锋、朱志荣等）

### 技术栈总结
- **核心框架**: SeaJS + jQuery
- **构建工具**: wooo (京东自研打包工具)
- **样式预处理**: SCSS/Sass
- **模板引擎**: Nunjucks (基于layout.html布局)
- **包管理**: pnpm (v9.15.2)
- **测试框架**: Jasmine
- **部署方式**: OSS存储 + CDN分发

## 详细项目结构

### 目录架构
```
pc_item/
├── app/                          # 应用主目录
│   ├── components/               # 组件库
│   │   ├── default-soa/         # 默认SOA架构组件
│   │   └── public-soa/          # 公共SOA组件
│   ├── views/                   # 页面视图
│   │   ├── layout/              # 布局模板
│   │   ├── inc/                 # 公共包含文件
│   │   └── *.html               # 各类商品页面
│   └── tests/                   # 测试文件
├── scripts/                     # 构建脚本
├── config.js                    # 项目配置
├── package.json                 # 依赖管理
└── README.md                    # 项目文档
```

### 核心配置文件
- **config.js**: 项目构建配置，包含版本号、CDN路径、雪碧图配置等
- **package.json**: 依赖管理，主要依赖@jd/upload-oss-tools
- **pnpm-lock.yaml**: 锁定依赖版本

## 主要功能模块

### 1. 组件系统 (app/components/)

#### default-soa 组件 (业务组件)
- **ETab**: 选项卡组件，支持点击切换、延迟加载等
- **EDropdown**: 下拉菜单组件
- **ETooltips**: 提示框组件
- **ELazyload**: 图片懒加载组件
- **main**: 主入口模块，负责模块加载和初始化
- **common**: 公共核心模块，包含全局变量和工具函数
- **prom**: 促销价格模块，处理商品价格展示逻辑
- **spu**: SPU(标准产品单元)相关功能
- **pingou**: 预售业务模块（定金+尾款、三阶预售、梦想购）
- **colorsize**: 颜色尺寸选择器
- **buybtn**: 购买按钮相关功能
- **detail**: 商品详情模块
- **preview**: 商品预览功能
- **address**: 地址选择模块
- **contact**: 客服联系模块

#### public-soa 组件 (公共组件)
- **modules/common**: 公共工具和样式库
- **lib**: 基础样式库，定义颜色、字体等变量
- **plugins**: 插件集合

### 2. 页面视图系统 (app/views/)
- **index.html**: 主页面模板
- **book.html**: 图书类商品页面
- **clothing.html**: 服装类商品页面
- **building.html**: 建材类商品页面
- **decoration.html**: 装修类商品页面
- **suits.html**: 套装类商品页面
- **layout/layout.html**: 基础布局模板

### 3. 构建和部署系统
- **scripts/uploadOss.js**: OSS上传脚本，支持京东云存储
- **upload.js**: 静态资源上传到预发环境
- **wooo构建工具**: 支持SCSS编译、模板处理、文件打包

## 业务逻辑分析

### 核心业务流程
1. **页面初始化**: main.js作为入口，加载公共脚本和首屏模块
2. **模块化加载**: 基于SeaJS的AMD模块系统，按需异步加载
3. **商品信息处理**: 通过pageConfig全局配置传递商品基础信息
4. **价格计算**: prom模块处理复杂的价格逻辑（促销、会员价等）
5. **用户交互**: 各种UI组件处理用户操作（选择规格、加购物车等）

### 商品类型支持
- 3C数码产品（手机、电脑、摄影器材）
- 图书（自营图书、POP图书）
- 服装服饰
- 家居建材
- 母婴用品
- 食品饮料
- 汽车用品

### 特殊业务场景
- **预售商品**: 支持一阶预售、三阶预售、梦想购
- **POP商品**: 第三方商家商品
- **O2O商品**: 线上线下一体化商品
- **企业采购**: 面向企业客户的专属销售

## 开发工作重点

### 当前开发环境
- Node.js >= 4.0 (推荐v16.15.0)
- wooo集成开发工具
- pnpm包管理器

### 关键开发任务
1. **组件维护**: 持续优化ETab、EDropdown等核心UI组件
2. **性能优化**: 图片懒加载、模块按需加载
3. **兼容性处理**: 支持多浏览器兼容
4. **业务逻辑**: 复杂的价格计算和促销逻辑
5. **用户体验**: 交互优化和响应式设计

### 部署流程
1. **开发环境**: `wo start` 启动本地服务器
2. **构建打包**: `wo b` 打包文件
3. **测试部署**: `wo deploy` 上传到测试服务器
4. **生产部署**: 
   - OSS部署: `node scripts/uploadOss.js`
   - 静态预发: `node upload.js`

## 改进方向和待办事项

### 技术债务
1. **技术栈升级**: 考虑从SeaJS+jQuery迁移到现代前端框架
2. **构建工具**: 评估从wooo迁移到Webpack/Vite等主流工具
3. **代码规范**: 统一代码风格和ESLint规则
4. **TypeScript**: 引入类型系统提高代码质量

### 性能优化
1. **首屏加载**: 优化关键资源加载顺序
2. **图片优化**: WebP格式支持、响应式图片
3. **缓存策略**: 优化静态资源缓存机制
4. **代码分割**: 更细粒度的模块拆分

### 用户体验
1. **移动端适配**: 响应式设计优化
2. **无障碍访问**: ARIA标签和键盘导航支持
3. **加载状态**: 更好的loading和错误状态处理
4. **交互反馈**: 微交互和动画效果

### 开发效率
1. **组件文档**: 完善组件使用文档和示例
2. **自动化测试**: 扩展单元测试和E2E测试覆盖率
3. **开发工具**: 热重载、调试工具优化
4. **CI/CD**: 自动化构建和部署流程

### 业务扩展
1. **国际化**: 多语言支持准备
2. **个性化**: 基于用户行为的页面定制
3. **A/B测试**: 支持更灵活的实验框架
4. **数据埋点**: 完善用户行为分析

---

**最后更新**: 2025-07-08  
**维护团队**: 京东单品前端团队  
**联系方式**: 周琪力(bjzhouqili)、金锴锋(jinkaifeng5)、朱志荣(zhuzhirong)
