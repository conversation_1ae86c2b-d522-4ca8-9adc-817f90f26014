@import "../common/lib";

#shop-reco {
    height: 210px;
    overflow: hidden;
}
#reco-more {
    padding-right: 10px;
    &.hover .head {
        background-color: transparent;
    }
}

.fittings {
    .tab-main {
        *z-index: 1;
    }
    a.current {
        color: $colorPriceRed;
    }
    .acc-buy-center {
        @include inline-block;
        padding: 4px 10px;
        border: 1px solid #ccc;
        margin-top: 10px;
    }

    .tab-con {
        padding-top: 20px;
    }
    .item {
        @include clearfix;
    }
    .p-list {
        padding: 0 3px;
        width: 134px;
        height: 161px;
        position: relative;
        list-style: none;
        .p-img{ 
            width: 100px;
        }
        .p-name {
            margin-bottom: 0;
        }
        .p-price strong {
            font-size: 14px;
            line-height: 14px;
            // 人气配件楼层不支持到手价展示
            .price-dsj{
                display: none
            }
        }
        .plus {
            @include icons(23px, 23px);
            position: absolute;
            right: 8px;
            top: 38px;
            background:url(i/plus.png) 0 0 no-repeat;
        }
        input {
            vertical-align: middle;
        }
    }
    .master {
        float: left;
        *display: inline;
        width: 144px;
        .p-list {
            float: right;
        }
    }
    .suits {
        float: left;
        width: 640px;
        height: 187px;
        overflow: hidden;
        position: relative;

        .switchable-wrap {
            overflow: hidden;
            height: 161px;
            position: relative;

            .lh-wrap {
                margin: 0 38px 0 36px;
            }

            .prev-btn,
            .next-btn {
                background: url(i/page-btn.png) 0 -94px no-repeat;
                width: 23px;
                height: 73px;
                display: block;
                position: absolute;
                z-index: 1;
                left: 5px;
                top: 15px;
            }

            .next-btn {
                left: auto;
                right: 15px;
                background-position: 0 0;
            }

            .prev-btn.disabled {
                background-position: -94px -94px !important;
                cursor: default;
            }

            .next-btn.disabled {
                background-position: -94px 0 !important;
                cursor: default;
            }

            .prev-btn.hover,
            .prev-btn:hover {
                background-position: -46px -94px;
            }

            .next-btn.hover,
            .next-btn:hover {
                background-position: -46px 0;
            }
        }
    }
    .infos {
        position: relative;
        float: left;
        display: inline;
        width: 150px;
        padding-left: 50px;
        padding-top: 15px;

        .more-link {
            padding-bottom: 10px;
            a {
                color: $colorLinkBlue;
            }
        }
        .selected, .p-price {
            margin-bottom: 10px;
            strong {
                font-size: 18px;
            }
        }
        .equal {
            @include icons(23px, 23px);
            position: absolute;
            left: 0;
            top: 30px;
            background:url(i/equal.png) 0 0 no-repeat;
        }
        .btn-primary{
            padding: 0 17px;
        }
        .extra{
            width: 90px;
            border: 1px solid #e1e1e1;
            text-align: center;
            line-height: 26px;
            margin-top: 8px;
        }
    }
    .recommend-more{
        .head{
            height: auto;
            line-height: 18px;
            float: none;
            padding: 0;
        }
        .content{
            position: absolute;
            top: 30px;
            z-index: 3;
            border: 1px solid #ddd;
            padding: 6px 12px;
            width: 80px;
            a{
                display: block;
                font-size: 12px;
                padding: 6px 10px;
            }
        }
    }
}

.root61 {
    .fittings {
        .suits {
            width: 840px;
            .switchable-wrap {
                .prev-btn {
                    left: 22px;
                }
                .next-btn {
                    right: 15px;
                }
                .lh-wrap {
                    margin: 0 38px 0 68px;
                }
            }
        }
        .p-list {
            padding: 0 5px;
        }
    }
}


#suit-con {
    .stab {
        overflow: hidden;
        padding-bottom: 15px;
    }

    .stab li {
        margin-left: -1px;
        white-space: nowrap;
        padding: 0 15px;
        height: 16px;
        cursor: pointer;
        border-left: 1px solid #D4D1C8;
        line-height: 16px;
        text-align: center;
        color: #e4393c;
        margin-bottom: 10px;
    }

    #stabcon_suits {
        position: relative;
    }
    .stabcon {
        clear: both;
    }

    .master {
        float: left;
        padding: 0 0 0 10px;
        text-align: center;
        overflow: hidden;
        width: 164px;
        .p-name {
            width: 140px;
            height: 36px;
            padding: 0 10px;
        }
        s {
            float: right;
            display: inline;
            width: 24px;
            height: 22px;
            margin-top: 100px;
            margin-right: 3px;
            background: url(//static.360buyimg.com/item/main/1.0.14/css/i/newicon20140910.png) no-repeat 0 -260px;
        }
    }

    .pop-wrap {
        position: relative;
        float: left;
    }

    .suits {
        float: left;
        width: 788px;
        ul {
            float: left;
            height: 100%;
            overflow: hidden;
            li {
                width: 197px;
                padding-left: 0;
                position: relative;
                s {
                    float: right;
                    display: inline;
                    width: 24px;
                    height: 22px;
                    background: url(//static.360buyimg.com/item/main/1.0.13/css/i/newicon20140910.png) no-repeat 0 -260px;
                    margin-right: 3px;
                    margin-top: 100px;
                }
                .p-img {
                    position: relative;
                    padding: 5px 0;
                }
                .p-scroll {
                    width: 155px;
                    _width: 155px;
                    height: 29px;
                    background: #fff;
                    overflow: hidden;
                    padding: 5px 0;
                    border: 1px solid #fff;
                    border-bottom: 0;
                    .p-scroll-btn {
                        background-image: url(//static.360buyimg.com/item/main/1.0.13/css/i/scroll-btns.png);
                        background-repeat: no-repeat;
                        float: left;
                        width: 16px;
                        height: 29px;
                        text-indent: -9999px;
                    }
                    .p-scroll-prev {
                        margin-right: 2px;
                        background-position: 0 0;
                        &.disabled {
                            background-position: 0 -31px;
                        }
                    }
                    .p-scroll-next {
                        background-position: -18px 0;
                        &.disabled {
                            background-position: -18px -31px;
                        }
                    }
                    .p-scroll-wrap {
                        float: left;
                        width: 186px;
                        height: 29px;
                        overflow: hidden;

                    }
                    .suits ul {
                        float: left;
                        height: 100%;
                        overflow: hidden;
                    }
                }

                li {
                    position: relative;
                    width: 30px;
                    height: 30px;
                    padding: 0;
                    a {
                        padding: 1px;
                        border: 1px solid #ddd;
                        float: left;
                        img {
                            width: 25px;
                            height: 25px;
                        }
                        &.curr {
                            border: 1px solid #e4393c;
                        }
                    }

                    .p-name {
                        width: 156px;
                        height: 36px;
                    }

                    .choose {
                        width: 156px;
                    }

                    .p-price strong {
                        color: #E4393C;
                    }
                }
                .p-choose .hl_blue {
                    cursor: pointer;
                }
            }
        }
        .last-item s {
            display: none;
        }
    }

    .infos {
        margin-top: 50px;
        float: right;
        width: 190px;
        line-height: 20px;
        s {
            float: left;
            width: 24px;
            height: 22px;
            margin-top: 40px;
            background: url(//static.360buyimg.com/item/main/1.0.14/css/i/newicon20140910.png) no-repeat -30px -260px;
        }
        .p-name {
            height: 36px;
            overflow: hidden;
            line-height: 13px;
            margin-left: 35px;
            margin-bottom: 0;
        }
        .p-price {
            margin-left: 35px;
            .p-simsun {
                font-family: simsun;
            }
            strong {
                color: #E4393C;
            }
        }
        .p-saving {
            margin-left: 35px;
            .hl_green {
                color: #090;
            }
        }
        .btns {
            margin-left: 35px;
            color: #999;
        }

        .btn-buy {
            display: block;
            width: 77px;
            height: 25px;
            margin-top: 10px;
            text-align: center;
            line-height: 25px;
            color: #fff;
            font-weight: 700;
            background: url(//static.360buyimg.com/item/main/1.0.14/css/i/newicon20140910.png) no-repeat -166px -112px;
        }
    }
    .clb {
        clear: both;
    }

    #pop-box, #pop-box-suit { visibility:hidden; position:absolute; z-index:5; left:0px; top:169px; width:175px; overflow:hidden;  background:#fff; border: 1px solid #C4C4C4; -moz-box-shadow: 0 0 5px #ddd; -webkit-box-shadow: 0 0 5px #DDD; box-shadow: 0 0 5px #DDD;}
    #p-scroll, #p-scroll-suit { padding:10px 0  0 10px; }
    #stabcon_suits .p-scroll {
        width: 155px;
        _width: 155px;
        height: 29px;
        background: #fff;
        overflow: hidden;
        padding: 5px 0;
        border: 1px solid #fff;
        border-bottom: 0;
    }
    #stabcon_suits { position:relative; }
    #tab-suits { overflow:visible; }
    .stabcon_suits .choose { height:16px; }
    .p-choose .hl_blue { cursor:pointer; }
    #stabcon_suits .p-scroll {
        width: 155px;
        _width: 155px;
        height: 29px;
        background: #fff;
        overflow: hidden;
        padding: 5px 0;
        border: 1px solid #fff;
        border-bottom: 0;
    }

    #stabcon_suits .p-scroll-prev {
        margin-right: 2px;
        background-position: 0 0;
    }
    #stabcon_suits .p-scroll-next {
        background-position: -18px 0;
    }
    #stabcon_suits .p-scroll-btn {
        float: left;
        display: none;
        width: 16px;
        height: 29px;
        text-indent: -9999px;
    }
    #stabcon_suits .p-scroll-btn {
        background-image: url(//static.360buyimg.com/item/main/1.0.14/css/i/scroll-btns.png);
        background-repeat: no-repeat;
    }
    .disabled {
        cursor: default;
    }
    #stabcon_suits .p-scroll-wrap {
        float: left;
        width: 186px;
        height: 29px;
        overflow: hidden;
    }
    #fitting-suit #stabcon_suits li {
        position: relative;
    }
    #fitting-suit #stabcon_suits .p-scroll li {
        width: 30px;
        height: 30px;
        padding: 0;
    }
    #stabcon_suits .p-scroll-wrap li, .p-scroll-wrap li a {
        float: left;
    }
    #stabcon_suits .p-scroll-wrap li a {
        padding: 1px;
        border: 1px solid #ddd;
    }
    #stabcon_suits .p-scroll-wrap li a.curr {
        border: 1px solid #e4393c;
    }
    #stabcon_pop .p-scroll-wrap li, .p-scroll-wrap li a {
        float: left;
    }
    #stabcon_suits .p-scroll-wrap li a img {
        width: 25px;
        height: 25px;
    }
    #p-size, #p-size-btn, #p-size-btn-suit, #p-size-suit, #p-tips, #p-tips-suit {
        padding: 0 10px;
    }
    #p-size-suit a {
        display: inline-block;
        padding: 2px 5px;
        border: 1px solid #ccc;
        margin: 0 5px 10px 0;
    }
    #stabcon_suits .selected {
        border-color: #e4393c;
    }
    #p-size-btn-suit {
        padding-bottom: 10px;
    }
    #p-size-btn a, #p-size-btn-suit a {
        display: inline-block;
        width: 50px;
        margin-right: 5px;
        height: 21px;
        line-height: 21px;
        background: url(//static.360buyimg.com/item/main/1.0.14/css/i/scroll-btns.png) -36px 0 no-repeat;
        text-align: center;
    }
    .p-selected {
        background: #fffdee;
        width: 138px;
        border: 1px solid #edd28b;
        margin: 7px 0;
        padding: 4px 10px;
    }
    .p-selected a {
        white-space: nowrap;
    }
    .p-selected a, .p-selected a:hover, .p-selected a:visited {
        color: #005ea7;
    }
    #fitting-suit #stabcon_suits li .choose, #fitting-suit #stabcon_suits li .p-name {
        width: 156px;
    }

}

.shopRec-content {
    .plist {
        li {
            width: 160px;
            margin: 10px 19px;
            float: left;
            _display: inline;
            .p-price {
                text-align: center;
            }
            .p-name {
                z-index: 3
            }
        }
    }
}

.root61 .shopRec-content {
    .plist {
        li {
            margin: 10px 20px;
            _display: inline;//ie6双边距bug
        }
    }
}
#shopRecSuit{
    display: none;
}
#shopRecSuit .tab-main .hide {
    display: none;
}


