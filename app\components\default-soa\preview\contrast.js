define('MOD_ROOT/preview/contrast', function(require, exports, module) {
    require('PLG_ROOT/jQuery.imgScroll');
    require('jdf/1.0.0/unit/trimPath/1.0.0/trimPath');
    require('MOD_ROOT/preview/contrast.css');
    require('MOD_ROOT/ETab/ETab');

    var tools = require('MOD_ROOT/common/tools/tools');

    /*!
     * 2012-9-18
     */
    var ie = (function(){

        var undef,
            v = 3,
            div = document.createElement('div'),
            all = div.getElementsByTagName('i');

        while (
            div.innerHTML = '<!--[if gt IE ' + (++v) + ']><i></i><![endif]-->',
            all[0]
        );

        return v > 4 ? v : undef;
    }());

    var Contrast = {
        getPriceNum: function(skus, $wrap, perfix, callback) {
            // 获得数字价格
            /**
             How to use
              G.getPriceNum(pageConfig.product.fittingsAuto, targetElement, null, function(sku, price, res) {
                  targetElement.find('#inp_'+sku).attr('wmeprice', price);
              });
             */
            skus = typeof skus === 'string' ? [skus]: skus;
            $wrap = $wrap || $('body');
            perfix = perfix || 'J-p-';
            var skuArr = []
            for (var i = 0; i < skus.length; i++){
                skuArr.push({"skuId": skus[i]}) 
            }
            var paramJson = {
                skuPriceInfoRequestList: skuArr,
                area: readCookie("ipLoc-djd") && readCookie("ipLoc-djd").replace(/-/g,"_") || '',
                // pin: readCookie('pin') || '',
                // fields: '11100000',
                // source: 'pc-item1'
            };
            var body = JSON.stringify(paramJson);
            var time = new Date().getTime()
            // 加固start
            var colorParm = {
                appid: 'item-v3',
                functionId: 'mzhprice_getCustomRealPriceInfoForColor',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = tools.getCookieNew("__jda") || '';
                            getPriceData(colorParm);
                            
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getPriceData(colorParm);
                         //烛龙上报
                        tools.getJmfe(colorParm, e, "contrast价格接口设备指纹异常", 751)
                    }
                })
            }catch(e){
                colorParm['loginType'] = '3';
                colorParm['uuid'] = '';
                getPriceData(colorParm);
                //烛龙上报
                tools.getJmfe(colorParm, e, "contrast价格接口加固异常", 751)
            }            
            // 加固end     
            function getPriceData(colorParm){ 
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }             
                $.ajax({
                    url: host,
                    data: colorParm,
                    dataType: 'json',
                    xhrFields: {
                        withCredentials: true,
                    },
                    headers: tools.getUrlSdx(), 
                    success: function (r) {
                        if(r){
                            if(parseInt(r.code) < 10 && r.echo){
                                try {
                                    var echoCode = r.echo.length>1000 ? r.echo.substring(0,999) : r.echo;
                                    //烛龙上报
                                    tools.getJmfe(colorParm, echoCode, "contrast对比价格接口成功异常", 751)
                                } catch(e) {
                                    console.log('上报pctradesoa_getprice错误',e)
                                }
                            }else{
                                if ( r && r.skuPriceInfoResponseList&& r.skuPriceInfoResponseList.length>0 ) {
                                    var priceList = r.skuPriceInfoResponseList;
                                    for(var i=0,j=priceList.length;i<j;i++){
                                        var skuId = priceList[i].skuId;
                                        var priceResult = priceList[i].priceResult;
                                        var price = priceResult.jdPrice;// 取京东价，对应原来的p
                
                                        //转成数值计算 解决下发价格为-1 的情况
                                        $wrap.find('.'+ perfix + skuId).html("&yen;&nbsp;<strong>" + (Number(price) > 0 ? price : "暂无报价") + "</strong>" );
                                    }
                                }else{
                                      try {
                                            var body = JSON.parse(param['body']);
                                            if ( body && body.skuPriceInfoRequestList&& body.skuPriceInfoRequestList.length>0 ) {
                                                var skuList = body.skuPriceInfoRequestList;
                                                    for(var i=0,j=skuList.length;i<j;i++){
                                                        var skuId = skuList[i].skuId;
                                                        $wrap.find('.'+ perfix + skuId).html("<strong>暂无报价</strong>");
                                                    }
                                            }
                                            //烛龙上报
                                            tools.getJmfe(colorParm, echoCode, "'单品页对比价格调用网关异常,数据为空", 751)
                                      } catch(e) {
                                          console.log('上报mzhprice_getCustomRealPriceInfoForColor错误',e)
                                      }
                                }
                            }
                        }
                        
                    },
                    error: function (e) {
                        //烛龙上报
                        tools.getJmfe(colorParm, e, "contrast价格接口错误异常", 751)
                    }
                });
            }
        },
        TPL: {
            contrast: '<div id="pop-compare" data-load="false" class="pop-compare'+(pageConfig.wideVersion&&pageConfig.compatible ? '' : ' pop-compare-narrow')+'">'
                        +'<div class="pop-wrap">'
                        +   '<p class="pop-compare-tips"></p>'
                        +   '<div class="pop-inner">'
                        +       '<div class="diff-hd">'
                        +           '<ul class="tab-btns clearfix">'
                        +               '<li class="current" data-tab="trigger"><a href="javascript:;">对比栏</a></li>'
                        // +               '<li data-tab="trigger"><a href="javascript:;">最近浏览</a></li>'
                        +           '</ul>'
                        +           '<div class="operate">'
                        +               '<a href="javascript:;" class="hide-me">隐藏</a>'
                        +           '</div>'
                        +       '</div>'
                        +       '<div class="diff-bd tab-cons">'
                        +           '<div class="tab-con" data-tab="item">'
                        +               '<div id="diff-items" class="diff-items clearfix">'
                        +                   '<dl class="item-empty"><dt>1</dt><dd>您还可以继续添加</dd></dl>'
                        +                   '<dl class="item-empty"><dt>2</dt><dd>您还可以继续添加</dd></dl>'
                        +                   '<dl class="item-empty"><dt>3</dt><dd>您还可以继续添加</dd></dl>'
                        +                   '<dl class="item-empty"><dt>4</dt><dd>您还可以继续添加</dd></dl>'
                        +               '</div>'
                        +               '<div class="diff-operate">'
                        +                   '<a target="_blank" id="goto-contrast" href="#none" class="btn-compare-b">对比</a>'
                        +                   '<a class="del-items">清空对比栏</a>'
                        +               '</div>'
                        +           '</div>'
                        +           '<div class="tab-con tab-scroll" data-tab="item" style="display:none;">'
                        +               '<div class="scroll-item clearfix">'
                        +                   '<span id="sc-prev" class="scroll-btn sb-prev">&lt;</span>'
                        +                   '<span id="sc-next" class="scroll-btn sb-next">&gt;</span>'
                        +                   '<div class="scroll-con clearfix">'
                        +                       '<ul id="scroll-con-inner">'
                        +                           '<p class="scroll-loading ac">载入中...</p>'
                        +                       '</ul>'
                        +                   '</div>'
                        +               '</div>'
                        +           '</div>'
                        +       '</div>'
                        +   '</div>'
                        +'</div>'
                        +'</div>',
            item: '<dl class="hasItem" id="cmp_item_${sku}" fore="${ind}">'
                    +'  <dt>'
                    +'      <a target="_blank" href="//item.jd.com/${data[sku].skuId}.html"><img src="${pageConfig.FN_GetImageDomain(data[sku].skuId)}n5/${data[sku].imagePath}" width="50" height="50"></a>'
                    +'  </dt>'
                    +'  <dd>'
                    +'      <a target="_blank" class="diff-item-name" href="//item.jd.com/${data[sku].skuId}.html">${data[sku].name}</a>'
                    +'      <span class="p-price"><strong class="J-p-${data[sku].skuId}"></strong><a class="del-comp-item" skuid="${data[sku].skuId}">删除</a></span>'
                    +'  </dd>'
                    +'</dl>',
            recentItem: '{for item in data}'
                        +'<li id="rec_item_${item.sku}" data-tab="item" data-push="${pageConfig._contrast.push(item.sku)}">'
                        +'<div class="rec_item_wrap">'
                        +'  <div class="dt">'
                        +'      <a target="_blank" href="//item.jd.com/${item.sku}.html"><img src="${pageConfig.FN_GetImageDomain(item.sku)}n5/${item.img}" width="50" height="50"></a>'
                        +'  </div>'
                        +'  <div class="dd">'
                        +'      <a target="_blank" href="//item.jd.com/${item.sku}.html" class="diff-item-name">${item.t}</a>'
                        +'      <div class="btns clb">'
                        +'          <span class="p-price"><strong class="J-p-${item.sku}"></strong></span>'
                        +'          <a id="recent_${item.sku}" data-recent="true" data-sku="${item.sku}" skuid="${item.sku}" class="J_contrast btn-compare btn-compare-s"><span>对比</span></a>'
                        +'      </div>'
                        +'  </div>'
                        +'</div>'
                        +'</li>'
                        +'{/for}'
        },
        init: function( pageType, cookieName, recentCharset ) {

            this.cookieName = cookieName || '_contrast';
            this.recentCharset = recentCharset || 'utf-8';

            this.bindEvent( 'cmpBtn' ).btnStyle( null, 'set' );

            // if ( readCookie(this.cookieName + '_status') == 'side' && $('#side-cmp').length < 1 ) {

            //     $('#sidepanel').prepend('<span id="side-cmp"><a class="compareHolder" href="javascript:;"><b></b>对比栏</a></span>');

            // } else {
            //     if ( readCookie(this.cookieName) ) {
            //         this.showPopWin(null);
            //     }
            // }

            this.bindEvent( 'showWin' );

            try{
                if (window.pageConfig &&
                    typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
                        this.domain = pageConfig.FN_getDomainNew();
                }else if (window.pageConfig &&
                    typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
                        this.domain = pageConfig.FN_getDomain();
                }else {
                    this.domain = document.domain;
                }
            }catch(e){
                this.domain = document.domain;
            }
           


            if ( $('#J_goodsList').find('ul.gl-warp').attr('data-tpl')=='1' ) {
                $('#backtop').prepend('<div class="b-item"><a class="b-i-contrast" href="#none">商品对比</a></div>');
            }

            var _this = this;
            if ($('.J_contrast').length) {
                // this.loadResource({
                //   tagName:'script',
                //   src: 'https://storage.360buyimg.com/retail-mall/main_contrast/prod/0.0.15/assets/polyfills-legacy-k4mznpMC.js'
                // }, function () {
                //   _this.loadResource({
                //     tagName:'script',
                //     id: 'vite-legacy-entry',
                //     'data-src': 'https://storage.360buyimg.com/retail-mall/main_contrast/prod/0.0.15/assets/index-legacy--DF_rL1c.js',
                //     'textContent': "System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))"
                //   })
                // })
                try {
                  // 默认兜底
                  var pkjs = 'https://storage.360buyimg.com/retail-mall/main_contrast/prod/0.0.15/assets/index-legacy--DF_rL1c.js'

                  if (window.pageConfig && window.pageConfig.assets && window.pageConfig.assets.pkjs) {
                    pkjs = window.pageConfig.assets.pkjs
                  }
                  tools.observerElExpose($('.J_contrast'), function () {
                    _this.loadResource({
                      tagName:'script',
                      id: 'vite-legacy-entry-pk',
                      'data-src': pkjs,
                      'textContent': "System.import(document.getElementById('vite-legacy-entry-pk').getAttribute('data-src'))"
                    })
                  })
                } catch (error) {
                  console.error('pk半弹层曝光加载异常', JSON.stringify(error || {}))
                }
                tools.exposure({
                    functionName: 'PC_Productdetail_PKBtn_Expo',
                    exposureData: ['mainskuid', 'touchstone_expids'],
                    errorTips: 'PK按钮曝光报错'
                })
            }
            return this;
        },
        bindEvent: function( type ) {
            var btns = $('.J_contrast'),
                delBtn = $('.del-items'),
                _this = this;

            if ( type == 'cmpBtn') {
                $('body').undelegate('.J_contrast', 'click').delegate('.J_contrast', 'click', function() {
                    tools.landmine({
                        functionName: 'PC_Productdetail_PKBtn_click',
                        exposureData: ['mainskuid', 'touchstone_expids'],
                        errorTips: 'PK按钮点击报错'
                    })
                    tools.checkLogin(function (r) {
                        if(!(r && r.IsAuthenticated)){// 未登录
                            window.login && window.login()
                        }else{
                            var skuid = $(this).attr('data-sku'),
                            resSkuid = readCookie( _this.cookieName ) || '',
                            cfg = pageConfig.product,
                            skuLen = resSkuid.split('.').length;
    
                            if(cfg.item_pk_label == "test_show_1"){// test_show_1 新版商详新PK
                                window.itemEventBus.emit('PopupCompareClick', skuid) // 监听调用新版pk弹层入口
                            } else {// test_show_2 和 base 新版商详新PK
                                if ( skuLen < 4 ) {
                                    _this.showPopWin( skuid );// 监听调用旧版pk弹层入口
            
                                    if ( $(this).attr('data-recent') == 'true'  ) {
                                        _this.switchTab(0);
                                    }
                                } else {
                                    if ( !_this.hasCookie(skuid) ) {
                                        _this.showPopWin(null);// 监听调用旧版pk弹层入口
                                        _this.setMessage('对比栏已满，您可以删除不需要的栏内商品再继续添加哦！');
                                    } else {
                                        if ( $(this).attr('data-recent') == 'true'  ) {
                                            _this.switchTab(0);
                                        }
                                        _this.showPopWin( skuid );// 监听调用旧版pk弹层入口
                                    }
            
                                }
                            }
                        }
                    })
                   
                });

            }

            if ( type == 'delAll' ) {

                $('body').undelegate('.del-items', 'click').delegate('.del-items', 'click', function() {
                    _this.delContrastItem( null, true );

                    $('#goto-contrast').attr('href','#none');
                });
            }

            if ( type == 'delHover' ) {

                $('.hasItem').hover(function() {
                    $(this).find('.del-comp-item').css('visibility', 'visible');
                }, function() {
                    $(this).find('.del-comp-item').css('visibility', 'hidden');
                });

                //删除链接
                $('.hasItem .del-comp-item').bind( 'click', function() {
                    var skuid = $(this).attr('skuid');
                    _this.delContrastItem(skuid);
                });

                // 去对比按钮
                $('#goto-contrast').click(function() {

                    var resSkuid = readCookie( _this.cookieName ) || '',
                        skuArr = resSkuid.split('.');

                    if ( skuArr.length < 2 ) {
                        _this.setMessage('至少有两件商品才能对比哦！');
                        return false;
                    } else {
                        var sku = [0,0,0,0];

                        for ( var i = 0; i < skuArr.length; i ++ ) {
                            sku[i] = skuArr[i];
                        }
                        $('#goto-contrast').attr( 'href', '//item.jd.com/compare/' + sku.join('-') + '.html' );
                    }
                });
            }

            if ( type == 'hide' ) {
                $('body').undelegate('.diff-hd .hide-me', 'click').delegate('.diff-hd .hide-me', 'click', function() {
                    _this.hidePopWin();
                });
            }

            if ( type == 'showWin' ) {
                $('body').delegate('.b-i-contrast', 'click', function() {
                    _this.showPopWin( null, true );
                });

            }

            return this;
        },
        // 动态加载css和js
        loadResource: function (options, callback) { 
          var source = document.createElement(options.tagName);
          if (options.tagName == 'script') {
              if (options.src) {
                source.src = options.src
              }
              if (options['data-src']) {
                source.setAttribute('data-src', options['data-src'])
              }
              if (options.textContent) {
                source.textContent = options.textContent
              }
              if (options.id) {
                source.id = options.id
              }

          } else { // link
              source.rel = 'stylesheet';
              source.href = options.url
          }
          source.onload = function() {
              callback && callback()
          }
          source.onerror = function() {
              console.error(url + ' 资源加载出错');
          }
          document.body.appendChild(source);
        },
        switchTab: function(index) {

            $('.diff-hd li').eq(index).trigger('click');
        },
        btnStyle: function(skuid, type) {

            if ( !!skuid ) {

                // 更新单个按钮样式
                if ( type == 'set' ) {
                    $('.J_contrast').filter('[data-sku="'+ skuid +'"]').addClass('selected');
                    $('#comp_' + skuid + ',#recent_' + skuid).addClass('btn-compare-s-active');
                    $('#cmp_' + skuid).text('取消对比');
                }
                if ( type == 'del' ) {
                    $('.J_contrast').filter('[data-sku="'+ skuid +'"]').removeClass('selected');
                    $('#comp_' + skuid + ',#recent_' + skuid).removeClass('btn-compare-s-active');
                    $('#cmp_' + skuid).text('加入对比');
                }
            } else {

                // 更新所有有cookie记录的按钮样式
                var skuids = readCookie(this.cookieName) || '';
                skuids = skuids.split('.');

                if ( skuids.length < 5 ) {
                    for ( var i = 0; i < skuids.length; i++ ) {
                        if ( type == 'set' ) {
                            $('.J_contrast').filter('[data-sku="'+ skuids[i] +'"]').addClass('selected');
                            $('#comp_' + skuids[i] + ',#recent_' + skuids[i]).addClass('btn-compare-s-active');
                            $('#cmp_' + skuids[i]).text('取消对比');
                        }
                        if ( type == 'del' ) {
                            $('.J_contrast').filter('[data-sku="'+ skuids[i] +'"]').removeClass('selected');
                            $('#comp_' + skuids[i] + ',#recent_' + skuids[i]).removeClass('btn-compare-s-active');
                            $('#cmp_' + skuids[i]).text('加入对比');
                        }

                    }
                }

            }

            return this;
        },
        loadExistList: function( callback ) {
            // 显示所有存在cookie值的对比列表
            var skuids = readCookie(this.cookieName) || '';

            skuids = skuids.split('.');

            for ( var i = 0; i < skuids.length; i++ ) {
                // this.setContrastItem(skuids[i]);
                if ( (i+1) == skuids.length ) {
                    this.setContrastItem(skuids[i], callback);
                } else {
                    this.setContrastItem(skuids[i]);
                }
            }
        },
        showPopWin: function( skuid, side ) {
            var popCompare = $('#pop-compare'),
                _this = this;

            skuid = skuid || null;

            if ( $('#pop-compare').length < 1 ) {
                $('body').append( this.TPL.contrast );
            }

            if ( $('#diff-items .hasItem').length < 1 ) {

                if ( !!readCookie(this.cookieName) ) {
                    this.loadExistList(function() {
                        //窗口显示状态
                        if ( _this.hasCookie(skuid) ) {
                            _this.delContrastItem(skuid);
                        } else {
                            _this.setContrastItem(skuid);
                        }
                    });
                } else {
                    _this.setContrastItem(skuid);
                }

            }

            if ( $('#pop-compare').attr('data-load') == 'false' ) {

                // 窗口未显示
                //if ( readCookie(this.cookieName) ) {
                    $('#pop-compare').show()
                //}
                popCompare.attr('data-load', 'true');

                $('#pop-compare').ETab({
                    onSwitch: function (n) {
                        if ( n == 1 && $('.scroll-loading').length > 0 ) {
                            _this.getRecent(function(skus) {
                                _this.setRecentScroll();

                                _this.getPriceNum(skus, $('#pop-compare'), null, function(sku, price, res) {});
                            });
                        }
                    }
                });
                //$('#pop-compare').switchable({
                //    delay: 0,
                //    navSelectedClass: 'current',
                //    event: 'click',
                //    callback: function(n) {
                //
                //    }
                //});

                //$('#pop-compare').Jtab({
                //    currClass: 'current',
                //    compatible: true,
                //    event: 'click'
                //}, function( s, obj, n ) {
                //    if ( n == 1 && $('.scroll-loading').length > 0 ) {
                //        _this.getRecent(function(skus) {
                //            _this.setRecentScroll();
                //
                //            _this.getPriceNum(skus, $('#pop-compare'), null, function(sku, price, res) {});
                //        });
                //    }
                //
                //});

                if ( ie !== 6 ) {
                    $('#pop-compare').animate({
                        bottom: 0
                    }, 100);

                }

            } else {

                if ( readCookie( _this.cookieName + '_status' ) == 'side' ) {

                    $('#pop-compare').show().attr('data-load', 'true');

                    if ( ie !== 6 ) {
                        $('#pop-compare').show().animate({
                            bottom: 0
                        });
                    }
                    createCookie( _this.cookieName + '_status', 'show', 30, '/;domain=' + this.domain );

                }

                //窗口显示状态
                if ( _this.hasCookie(skuid) ) {
                    _this.delContrastItem(skuid);
                } else {
                    _this.setContrastItem(skuid);
                }
            }

            _this.bindEvent( 'delAll' ).bindEvent( 'hide' );
        },
        hidePopWin: function() {
            var _this = this;

            if ( $('#side-cmp').length < 1 ) {

                $('#sidepanel').prepend('<span id="side-cmp"><a class="compareHolder" href="javascript:;"><b></b>对比栏</a></span>');

            }

            if ( ie == 6 ) {

                $('#pop-compare').hide();
            } else {
                if ($('.pop-wrap').is(':animated')) {
                    return false;
                }

                $('#pop-compare').css('overflow', 'hidden').find('.pop-wrap').animate({
                    left: '990px'
                }, 100, function() {

                    $('#pop-compare').removeAttr('style').css({
                        'overflow': 'visible',
                        'bottom': '-200px'
                    }).hide().find('.pop-wrap').removeAttr('style').css('left', 0);
                });
            }
            _this.bindEvent( 'showWin' );
            createCookie( _this.cookieName + '_status', 'side', 30, '/;domain=' + this.domain );
        },
        setContrastItem: function(skuid, callback ) {
            var popCompare = $('#pop-compare'),
                resSkuid = readCookie( this.cookieName ) || '',
                skuLen = resSkuid.split('.').length,
                _this = this;


            if ( _this.hasCookie(skuid) && popCompare.attr('data-load') == 'true' ) {
                _this.delContrastItem(skuid);
            } else {
                if ( !skuid ) { return false; }
                var body = JSON.stringify({"ids":skuid});
                var time = new Date().getTime()
                var colorParm = {
                    appid: 'item-v3',
                    functionId: 'pctradesoa_ware_history',
                    client: 'pc',
                    clientVersion: '1.0.0',
                    t: time,//生成当前时间毫秒数
                    loginType: '3',
                    // uuid: tools.getCookieNew("__jda") || '',
                    // ids: skuid
                    body: body,
                }

                try{
                    colorParm['uuid'] = tools.getCookieNew("__jda")
                }catch(e){
                    colorParm['uuid'] = ''
                }

                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }
                $.ajax({
                    // url: '//fts.jd.com/ware/history?ids=' + skuid,//pctradesoa_ware_history
                    // dataType: 'jsonp',
                    url: host,
                    data: colorParm,
                    dataType: 'json',
                    xhrFields: {
                        withCredentials: true,
                    }, 
                    success: function(data) {

                        var ind = $('#diff-items dl').index($('#diff-items').find('.item-empty').eq(0));

                        var result = {
                            data: data,
                            sku: skuid,
                            ind: ind
                        };

                        if ( $('#cmp_item_'+skuid).length < 1 || !_this.hasCookie(skuid) ) {
                            popCompare.find('.item-empty').eq(0).replaceWith( _this.TPL.item.process(result) );
                            _this.getPriceNum(skuid, $('#pop-compare'), null, function(sku, price, res) {
                            });
                        }

                        _this.bindEvent('delHover').setCookie(skuid).btnStyle(skuid, 'set');

                        createCookie( _this.cookieName + '_status', 'show', 30, '/;domain=' + _this.domain );

                        if ( typeof callback == 'function' ) {
                            callback();
                        }
                        _this.setContrastBtn('add');
                        $('#pop-compare').attr('data-load', 'true');

                    }
                });

            }

            return this;
        },
        setContrastBtn: function(type) {
            var resSkuid = readCookie( this.cookieName ) || '',
            skuLen = resSkuid.split('.').length;

            if ( type == 'add' ) {
                if ( skuLen > 1 ) {
                    $('#goto-contrast').addClass('compare-active');
                }
            }
            if ( type == 'reduce' ) {
                if ( skuLen < 2 ){
                    $('#goto-contrast').removeClass('compare-active');
                }
            }
        },
        sortList: function() {
            // 删除对比栏的内容后重新排序列表
            var wrapDiv = $('#diff-items'),
                res$obj = [];

            wrapDiv.find('.hasItem').each(function() {
                res$obj.push( $(this).clone() );
            });

            wrapDiv.html('');

            for ( var i = 0; i < 4; i++ ) {

                if ( i > res$obj.length-1 ) {
                    $('#diff-items').append('<dl class="item-empty"><dt>' + (i+1) + '</dt><dd>您还可以继续添加</dd></dl>');

                } else {
                    $('#diff-items').append( res$obj[i] );
                }
            }

            this.bindEvent( 'delHover' );

            return this;
        },
        delContrastItem: function(skuid, All) {
            if ( All ) {

                // 删除所有
                $('#diff-items').html('');
                for ( var i = 1; i < 5; i++ ) {
                    $('#diff-items').append('<dl class="item-empty"><dt>' + i + '</dt><dd>您还可以继续添加</dd></dl>');
                }

                this.btnStyle( null, 'del' );
                $('#goto-contrast').removeClass('compare-active');
                $('.btn-compare').removeClass('btn-compare-s-active');
                $('#goto-contrast').unbind('click');
                createCookie( this.cookieName, '', -1, '/;domain=' + this.domain );
            } else {

                // 删除单个
                $('#cmp_item_' + skuid).replaceWith('<dl class="item-empty"><dt>' + (parseInt($('#cmp_item_' + skuid).attr('fore'), 10) + 1)  + '</dt><dd>您还可以继续添加</dd></dl>');

                this.sortList().delCookie(skuid).btnStyle( skuid, 'del' );

                this.setContrastBtn('reduce');
            }

            return this;
        },
        delCookie: function(skuid) {
            if ( this.hasCookie(skuid) && skuid !== null ) {
                var skuids = readCookie(this.cookieName);

                var resSkuids = skuids.replace( new RegExp(skuid + '\.|\.' + skuid + '|' + skuid), '' );

                createCookie( this.cookieName, resSkuids, 0, '/;domain=' + this.domain );
            }

            return this;
        },
        setCookie: function(skuid) {
            var sku = readCookie(this.cookieName) || '';
                skuArr = sku.split('.');

            if ( !this.hasCookie(skuid) && skuArr.length < 4 ) {

                if ( !!sku ) {
                    skuArr.push(skuid);
                    createCookie( this.cookieName, skuArr.join('.'), 1, '/;domain=' + this.domain);

                } else {
                    createCookie( this.cookieName, skuid, 1, '/;domain=' + this.domain );
                }
            }
            return this;
        },
        hasCookie: function(skuid) {
            if ( !!skuid ) {
                return new RegExp(skuid).test( readCookie(this.cookieName) );
            }
        },
        setRecentScroll: function() {
            var _this = this;

            //$(".tab-scroll").switchable({
            //    type:'carousel',
            //    hasPage: true,
            //    prevClass: 'sb-prev',
            //    nextClass: 'sb-next',
            //    autoPlay:false,
            //    visible: 4,
            //    step: 4
            //});

            //setTimeout(function() {
                $(".scroll-con").imgScroll({
                    visible: 4,
                    speed: 300,
                    step: 1,
                    loop: false,
                    direction: 'x',
                    evtType: 'click',
                    next: '.sb-next',
                    prev: '.sb-prev',
                    disableClass: 'disabled'
                });

                _this.bindEvent('cmpBtn');

            //}, 500);
        },
        getRecent: function(callback) {
            var _this = this;
            var iploc = tools.getAreaId().areaIds[0];
            var params = {
                p: 202001,
                lid: iploc,
                ck: 'pin,aview',
                lim: 23,
                ec: this.recentCharset,
                securityToken: readCookie("shshshfpb") || '',
                clientChannel: '3',
                clientPageId: 'item.jd.com',
            };
            var body = JSON.stringify(params);
            var time = new Date().getTime()
            // 加固start
            var colorParm = {
                appid: 'item-v3',
                functionId: 'recDivinerApi',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = tools.getCookieNew("__jda") || '';
                            getDivinerData(colorParm);
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getDivinerData(colorParm);
                        //烛龙上报
                        tools.getJmfe(colorParm, echoCode, "contrast接口设备指纹异常", 250)
                    }
                })
            }catch(e){
                colorParm['loginType'] = '3';
                colorParm['uuid'] = '';
                getDivinerData(colorParm);
                //烛龙上报
                tools.getJmfe(colorParm, e, "contrast接口加固异常", 250)
            } 
            // 加固end   

            function getDivinerData(colorParm){ 
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }             
                $.ajax({
                    url: host,
                    data: colorParm,
                    dataType: 'json',
                    xhrFields: {
                        withCredentials: true,
                    },
                    headers: tools.getUrlSdx(), 
                    success: function(data) {
                        if ( $('#scroll-con-inner p').length > 0 ) {
                            $('#scroll-con-inner p').remove();
                        }
    
                        pageConfig._contrast = [];
    
                        $('#scroll-con-inner').append( _this.TPL.recentItem.process(data) );
    
                        var cRecent = readCookie(_this.cookieName);
    
                        if ( cRecent ) {
                            var skuids = cRecent.split('.');
                            var len = skuids.length;
    
                            for (var i = 0; i < len; i++) {
                                if ( $('#rec_item_' + skuids[i]).length > 0 ) {
                                    _this.btnStyle( skuids[i], 'set' );
                                }
                            }
                        }
    
                        if ( typeof callback == 'function' ) {
                            callback(pageConfig._contrast);
                        }
                    },
                    error: function (e) {
                        //烛龙上报
                        tools.getJmfe(colorParm, e, "contrast接口错误异常", 250)
                    }
                });
            }
        },
        setMessage: function( text ) {
            $('.pop-compare-tips').text(text).fadeIn();

            setTimeout(function() {
                $('.pop-compare-tips').fadeOut();
            }, 8000);
        }
    };

    module.exports = Contrast;
});
