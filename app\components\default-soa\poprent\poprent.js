define('MOD_ROOT/poprent/poprent', function(require, exports, module) {
    var Event       = require('MOD_ROOT/common/tools/event').Event;
    var tools = require('MOD_ROOT/common/tools/tools');
    var login       = require('JDF_UNIT/login/1.0.0/login');
    var addToCartBtn = require("MOD_ROOT/buybtn/buybtn").addToCartBtn;
    // 租期加减
    window.setDays = {
        setCfg:function (cfg) {
            this.cfg = cfg;
            this.show();
        },
        init: function () {
            this.min = 1;
            this.max = 365;
            this.count = 3;
            this.disableAdd = false;
            this.disableReduce = true;
            this.$el = $('#choose-days');
            this.$rentDays = $('#rent-days');
            this.$buyBtn = $("#InitCartUrl,#InitCartUrl-mini");

            this.$add = $('#choose-days .btn-add');
            this.$reduce = $('#choose-days .btn-reduce');

            this.matchCountKey = ['days'];

            if (this.$rentDays.length < 1) return false;

            if (/debug=num/.test(location.href)) {
                this.$rentDays.attr('data-min', '5');
                this.$rentDays.attr('data-max', '10');
            }

            var minNum = this.$rentDays.data('min');
            var maxNum = this.$rentDays.data('max');

            if (maxNum) {
                this.max = maxNum;
            }

            // 最小购买数
            if (minNum) {
                this.min = minNum;
                this.count = minNum;
            }

            this.checkLimit();
            this.handleChange();
            this.bindEvent();
        },
        //只在pop租赁时显示
        isTarget:function () {
            return this.cfg && (this.cfg.specialAttrs instanceof Array) && (this.cfg.specialAttrs.join(',').indexOf('LeaseType-3') > -1)
        },
        show: function () {
            if (this.isTarget()) {
                this.$el.show();
            } else {
                this.hide();
            }
        },
        hide: function () {
            this.$el.hide();
        },
        bindEvent: function () {
            var _this = this;
            // _this.show();
            this.$rentDays.unbind('change keydown keyup')
                .bind('change keydown keyup', tools.throttle($.proxy(this.handleChange, this), 500));

            setTips(this.$reduce, '{0} 最少租 {1} 天');
            setTips(this.$add, '{0} 最多租 {1} 天');

            function setTips($el, template) {
                $el.ETooltips({
                    close: false,
                    content: '<div class="min-rentdays-tips"></div>',
                    width: 150,
                    position: 'bottom',
                    zIndex: 10,
                    onOpen: function () {
                        var img = '<img src="//img20.360buyimg.com/da/jfs/t2734/145/4239060100/1006/b6d0f0d8/57b4240fN9cc48b02.png" />';
                        this.$tooltips.find('.min-rentdays-tips').html(template.format(img, _this.$rentDays.val()));
                    }
                });
            }
        },
        disabledReduce: function (showTips) {
            this.disableReduce = true;
            this.disableAdd = false;
            this.$reduce.addClass('disabled');
            this.$add.removeClass('disabled');
            this.$add.attr('data-disabled', '1');

            if (showTips) {
                this.$reduce.removeAttr('data-disabled');
            } else {
                this.$reduce.attr('data-disabled', '1');
            }
        },
        disabledAdd: function (showTips) {
            this.disableAdd = true;
            this.disableReduce = false;
            this.$add.addClass('disabled');
            this.$reduce.removeClass('disabled');
            this.$reduce.attr('data-disabled', '1');

            if (showTips) {
                this.$add.removeAttr('data-disabled');
            } else {
                this.$add.attr('data-disabled', '1');
            }
        },
        enabledAll: function () {
            this.disableAdd = false;
            this.disableReduce = false;
            this.$reduce.removeClass('disabled').attr('data-disabled', '1');
            this.$add.removeClass('disabled').attr('data-disabled', '1');
        },
        getVal: function () { return this.$rentDays.val(); },
        setVal: function (val) { this.$rentDays.val(val); },
        checkLimit: function () {
            var min = this.$rentDays.data('min');
            var value = Number(this.getVal());

            // 默认值是1的时候只加禁用样式，不展示tips
            if (value <= 1) this.disabledReduce();
            if (value >= this.max) this.disabledAdd(true);
            if (value > 1 && value < this.max) this.enabledAll();

            if (min) {
                if (value === this.min) {
                    this.disabledReduce(true);
                }
            }
        },
        isEmpty: function (val) { return $.trim(val) == ''; },
        isFloat: function (n) { return Number(n) === n && n % 1 !== 0; },
        add: function() {
            var value = Number(this.getVal());
            if ( this.disableAdd || this.isEmpty(value) ) return false;

            if (value > this.min) {
                this.disableReduce = false;
            }

            if ( value >= this.max ) {
                this.setDisabled(this.$add);
                this.disableAdd = true;
                return false;
            } else {
                this.disableAdd = false;
                this.setEnabled(this.$add);
                this.count++;
            }

            this.setVal(this.count);
            this.checkLimit();
            this.setBuyLink();
        },
        reduce: function() {
            var value = Number(this.getVal());
            if ( this.disableReduce || this.isEmpty(value) ) return false;

            if (value < this.max) {
                this.disableAdd = false;
            }

            if ( value <= this.min ) {
                this.setDisabled(this.$reduce);
                this.disableReduce = true;
                return false;
            } else {
                this.setEnabled(this.$reduce);
                this.disableReduce = false;
                this.count--;
            }

            this.setVal(this.count);
            this.checkLimit();
            this.setBuyLink();
        },
        handleChange: function () {
            var value = this.getVal();
            var result = null;

            // 非法字符
            if (isNaN(Number(value)) || this.isEmpty(value) || this.isFloat(Number(value))) {
                result = this.count;
            } else {
                // 小于最小值
                if (value < this.min) {
                    result = this.min;
                    this.disabledReduce(result!==1);
                }
                // 大于最大值
                if (value > this.max) {
                    result = this.max;
                    this.disabledAdd(true);
                }
            }

            if (result) {
                this.count = result;
                this.$rentDays.val(result);
            } else {
                this.count = Number(value);
            }

            this.checkLimit();
            this.setBuyLink();
        },
        modify: function() {},
        setDisabled: function ($el) { $el.attr('data-disabled', 1); },
        setEnabled: function ($el) { $el.removeAttr('data-disabled'); },
        setBuyLink: function() {
            var _this = this;

            _this.$buyBtn.each(function() {
                var $this = $(this),
                    orginHref = $this.attr("href") != '#none'?$this.attr("href"):addToCartBtn.href,
                    param = orginHref.split("?")[1],
                    res, re;

                (function() {
                    for (var h = 0; h < _this.matchCountKey.length; h++) {
                        re = new RegExp(_this.matchCountKey[h] + "=\\d+");
                        if (re.test(param)) {
                            res = orginHref.replace(re, _this.matchCountKey[h] + "=" + _this.count);
                            // 或者判断 是否有'btn-disable'
                            if($this.attr("href") != '#none'){
                                $this.attr("href", res);
                            }
                            addToCartBtn.href = res;
                            return false
                        }
                    }
                })();
            });

            // Event.fire({
            //     type: 'onNumChange',
            //     count: this.count
            // });
        }
    };
    setDays.init();

    //pop租赁时立即租用旁边的问号提示
    var poptips = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('.J-poprent-tips');
            this.$wrapperEl  = $('.bt-info-tips');

            if (this.$el.length) {
                this.bindEvent();
            }
            return this;
        },
        //只在pop租赁时显示
        isTarget:function () {
            return this.cfg && (this.cfg.specialAttrs instanceof Array) && (this.cfg.specialAttrs.join(',').indexOf('LeaseType-3') > -1)
        },
        bindEvent: function() {

            this.show();

            var $poptips = this.$el;
            var html = '1.选租期 — 2.去支付（押金 + 日租金 x 租期）— 3.还设备 — 4.退押金<br />\
                如何租用商品？<br />\
                下单前请先选择租期，点击“立即租用”后系统将自动为您计算租金和押金总金额，支付完成后商家将为您发送商品。\
                 <div style="text-align: right;"><a style="color: #5e69ad;" href="//poplease.jd.com/pc/agreement.html" target="_blank">了解详情>></a></div>';
            seajs.use('MOD_ROOT/ETooltips/ETooltips', function () {
                $poptips
                    .ETooltips({
                        autoHide: true,
                        close: false,
                        content: html,
                        width: 300,
                        pos: 'right',
                        zIndex: 10
                    });
            });
        },
        show: function () {
            if (this.isTarget()) {
                this.$wrapperEl.show();
            } else {
                this.hide();
            }
        },
        hide: function () {
            this.$wrapperEl.hide();
        }
    };

    //显示押金
    //获取押金接口
    // function handleDeposit(cfg) {
    //     // Event.addListener('onPriceReady', handlePrice)
    // }
    var deposit = {
        init:function (cfg) {
            this.cfg = cfg;
            if(this.isTarget()){
                $('#yajin').show();
                this.ajaxGetDeposit();
            }else{
                $('#yajin').hide();
            }
        },
        show:function () {
            $('#yajin').show();
        },
        hide:function () {
            $('#yajin').hide();
        },
        //只在pop租赁时显示
        isTarget:function () {
            return this.cfg && (this.cfg.specialAttrs instanceof Array) && (this.cfg.specialAttrs.join(',').indexOf('LeaseType-3') > -1)
        },
        ajaxGetDeposit:function () {
            var _this = this;
            var url =  '//xbzuzuapi.jd.com/pcClient/pop/goods/fetchAttr';
            $.ajax({
                url: url,
                data: {
                    skuIds: _this.cfg.skuid,
                },
                cache: true,
                dataType: 'jsonp',
                success: function(r) {
                    if(Number(r.code) === 0 && r.data.length > 0){
                        _this.setRentDepositVal(r.data[0]);
                    }else{
                        _this.setRentDepositVal({});
                    }
                }
            })
        },
        setRentDepositVal:function (d) {
            (Number(d.unitRent) > 0)? $('.J-p-' + this.cfg.skuid).html(((Number(d.unitRent)).toFixed(2)+'<span>/天</span>')):$('.J-summary-price .p-price').html('暂无报价');
            (Number(d.unitRent) > 0)? (pageConfig.product.jp = (Number(d.unitRent)).toFixed(2)+'<span>/天</span>'):pageConfig.product.jp = '暂无报价';
            var tpl =  '<div class="dt">押金</div>\
                 <div class="dd">\
                    <span class="p-price"><span class="price">${price}</span></span>\
                 </div>';
            var html = tpl.process({
                price: (Number(d.guaranteeMoney) > 0?('￥'+(Number(d.guaranteeMoney)).toFixed(2)):'暂无报价')
            });
            $('#yajin').html(html);
        }
    }

    //立即租用按钮
    var popBtn = function (cfg) {
        var $btns = $("#InitCartUrl,#InitCartUrl-mini");

        var href = $btns.attr('href');

        $btns.click(function () {
            href = $btns.attr('href');
            if (!$(this).hasClass('btn-disable')) {
                login({
                    modal: true,
                    complete: function() {
                        location.href = href
                    }
                });
            }
            return false;
        })
    }
    function init(cfg) {
        setDays.setCfg(cfg);
        //价格加载完后请求押金接口
        deposit.init(cfg);
        // 租赁提示
        cfg.popTips = poptips.init(cfg);

        //立即租用登录确认;
        popBtn(cfg);
    }

    module.exports.__id = 'poprent';
    module.exports.init = init;
    module.exports.setDays = setDays;
});
