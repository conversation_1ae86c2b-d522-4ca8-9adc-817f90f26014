define('MOD_ROOT/common/common', function(require, exports, module) {
    require('MOD_ROOT/ETab/ETab');
    require('MOD_ROOT/ETooltips/ETooltips');
    require('MOD_ROOT/EDropdown/EDropdown');
    require('MOD_ROOT/ELazyload/ELazyload');
    var Conf = require('PUBLIC_ROOT/conf');
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var tools = require('MOD_ROOT/common/tools/tools');
    var ABTest = require('MOD_ROOT/common/tools/abtest');
    var Stock = require('MOD_ROOT/address/stock');
    var FollowShop = require('MOD_ROOT/contact/followshop');

    // 设置特殊属性 及 pageConfig 全局变量
    function setGlobals(cfg) {
        // 是否有货 stock.js 发送请求后会修改状态
        cfg.havestock = null
        cfg.isHeYue = null
        //cfg.isFeeType   = '-655-6881-6882-'.indexOf('-' + cfg.cat[2] + '-') > -1;
        cfg.isKO = G.onAttr('isKO')
        cfg.isBiGouMa = G.onAttr('isJMa')
        cfg.isTuanGou = G.onAttr('isGroupgoods')
        // 赛式报名
        cfg.isLOC3 = G.onAttr('isLOC-3')
        // RL Shop
        cfg.isRLShop = cfg.venderId === 686083
    }

    function getLoginStatus() {
        tools.checkLogin(function(r) {
            Event.fire({
                type: 'onLogin',
                login: r.IsAuthenticated
            })
        })
    }

    // 页面加载完默认定位到导航处
    function setDefaultScrollTop() {
        var win = window
        // 页面加载默认定位到面包屑的位置
        var $rootNav = $('.crumb-wrap')
        var $shopHeader = $('#shop-head')
        var $catNav = $('#nav-2014')
        var $ysNav = $('#discover-nav')
        var $tuanCatNav = $('#nav-2015')
        var sTop = 0
        var hashIsNone = win.location.hash === '#none'

        if (
            !$rootNav.length || win.location.hash === '#comment' || hashIsNone
        ) {
            return false
        }
        if ($rootNav.length) {
            sTop = $rootNav.offset().top
        }
        if ($ysNav.length) {
            sTop = $ysNav.offset().top
        }
        if ($catNav.length) {
            sTop = $catNav.offset().top
        }
        if ($tuanCatNav.length) {
            sTop = $tuanCatNav.offset().top
        }

        if ($shopHeader.length) {
            sTop = $shopHeader.offset().top
        }
        function jumpToCrumb() {
            $(win).scrollTop(($('#searchBar').offset().top - 16) || 0)
        }

        // 如果是刷新页面
        if (win.name) {
            // 如果页面在刷新前scrollTop有值，chrome会在刷新后自动定位到刷新前的位置
            // $(win).bind('beforeunload', jumpToCrumb)
        } else {
            try {
                win.name = G.name + '__scrollTop__flag'
                jumpToCrumb()
            } catch (err) {}
        }
    }

    // 设置大家电分区提示
    function setDJDTips(cfg) {
        if (!/jt=/.test(location.href)) return false

        var val = G.serializeUrl(location.href).param.jt
        var result = ''

        if (val === '10')
            // result = '由于您选择的地区京东自营暂时无货或不支持配送，已为您切换为第三方商家的相似商品，请关注。'
        if (val === '11' || val === '00')
            // result = '已为您切换在该地区售卖的相似商品，请关注。'
        if (val === '01')
            // result = '由于您选择的地区第三方商家暂时无货或不支持配送，已为您切换为京东自营相似商品，请关注。 '

        if (result) {
            $(".choose-btns-wrapper").prepend(
                '<div class="DJD-tips">{0} <i onclick="$(\'.DJD-tips\').remove()" class="sprite-close"></i></div>'.format(
                    result
                )
            )
        }
    }

    // 医药商品授权
    function authorize(cfg) {
        if ( cfg.isOtc ) {
            tools.checkLogin(function(res) {
                if (res.IsAuthenticated) {
                    var body = JSON.stringify({"pin": ''}); 
                    try{
                        body = JSON.stringify({"pin": tools.getCookieNew('pin') || ''});
                    }catch(e){
                        body = JSON.stringify({"pin": ''});
                    }
                    var time = new Date().getTime()
                    var colorParm = {
                        appid: 'item-v3',
                        functionId: 'pctradesoa_authorized_ajt',
                        client: 'pc',
                        clientVersion: '1.0.0',
                        t: time,//生成当前时间毫秒数
                        loginType: '3',
                        // uuid: tools.getCookieNew("__jda") || '',
                        // ids: skuid
                        body: body,
                    }

                    try{
                        colorParm['uuid'] = tools.getCookieNew("__jda")
                    }catch(e){
                        colorParm['uuid'] = ''
                    }
        

                    var host = '//api.m.jd.com'
                    if(pageConfig.product && pageConfig.product.colorApiDomain){
                        host = pageConfig.product && pageConfig.product.colorApiDomain
                    }
                    $.ajax({
                        // url: "//fts.jd.com/ajt/authorized/" + readCookie('pin'),
                        // dataType: "jsonp",
                        url: host,
                        data: colorParm,
                        dataType: 'json',
                        xhrFields: {
                            withCredentials: true,
                        }, 
                        success: function (r) {
                            // 未授权
                            if (r.code == 1) {
                                window.location.href = '//authorize.yiyaojd.com/nauth/auth?successReturnUrl='
                                 + encodeURIComponent(location.href);
                            }
                        }, 
                        error: function (e) {
                            console.log("接口异常",e)
                        }
                    });
                }
            });
        }
    }

    // 扩展语言功能
    function extendLang() {
        // String.format
        if (!String.prototype.format) {
            String.prototype.format = function() {
                var args = arguments
                return this.replace(/{(\d+)}/g, function(match, number) {
                    return typeof args[number] != 'undefined'
                        ? args[number]
                        : match
                })
            }
        }
    }

    /// 异步加载店铺头
    function loadShopHeader() {
        var $wrap = $("#J_ShopHead");
        var __html = $wrap.val();
        var $mount = $("#shop-head");
        if (__html && $mount.length) {
            $mount.html(__html);
            if (window.jshop &&
                window.jshop.module &&
                "function" === typeof window.__callJshopModules) {
                window.__callJshopModules(jQuery);
            }
        }
        $wrap.remove();
        setTimeout(function(){
            Event.removeListener("onPriceReady", loadShopHeader);
        });
    }

    Event.addListener("onPriceReady", loadShopHeader);

    /// 店铺关注
    function followShop(cfg) {
        var shopId = cfg.shopId;

        if (shopId <= 0) {
            return;
        }
        
        new FollowShop({
            venderId: shopId,
            onFollow: function () {
                sendRequest(0);
                setFansPriceText(this.getFollowState());
            },
            onUnFollow: function () {
                sendRequest(1);
                setFansPriceText(this.getFollowState());
            },
            onCheckFollowState: function () {
                setFansPriceText(this.getFollowState());
            }
        });
        
        function sendRequest(action) {
            var pin = readCookie('pin') || '';
            var uuid = readCookie('__jda') ?
                readCookie('__jda').split('.')[1] : '';
            G.sendRequest('//mercury.jd.com/log.gif?t=shop.100001&v=src=shop$shopid=' +
                cfg.shopId +
                '$action=' + action +
                '&pin=' + pin +
                '&uid=' + uuid +
                '&ver=1&rid=' + Math.random() +
                '&m=UA-J2011-1&ref=' + document.referrer);
        }

        /// 粉丝价文案
        function setFansPriceText(flag) {
            var $text = $(".J-fans-price .text");
            if ($text.length) {
                if (flag) {
                    $text.text("您已关注店铺，可享粉丝价");
                } else {
                    $text.text("关注店铺，即享粉丝价");
                }
            }
        }
    }

    /// 5G线上选号、变更套餐
    ;(function () {
        var tsop = G.specialAttrs["tsop"];

        if ( !(tsop == 1 || tsop == 2 || tsop == 7) ) {
            return;
        }

        function setButton() {
            var cfg = window.pageConfig.product;
            var arr = tools.getAreaId().areaIds;
            $.ajax({
                url: "//tstp.jd.com/isp/getSkipUrl",
                data: {
                    skuId: cfg.skuid,
                    tsop: tsop,
                    shopId: cfg.shopId,
                    provinceId: arr[0],
                    cityId: arr[1],
                    countyId: arr[2],
                    townId: arr[3],
                    source: 1
                },
                dataType: 'jsonp'
            }).done(function(res) {
                res = res || {};
                var url = res.data;
                var success = res.success;
                if (success &&
                    url.length > 0 &&
                    typeof url === "string") {
                    $("#InitCartUrl,#InitCartUrl-mini,#choose-baitiao").remove(); // 移除相关DOM
                    var __html = "<a href=" + url + " class='btn-special1 btn-lg'>" +
                            ({"1": "立即办理","2": "立即办理","7": "立即办理"})[tsop] +
                        "</a>";
                    $("#choose-btns").find(".J_choose_btn").html(__html);
                }
            }).fail(function() {
                console && console.log("//tstp.jd.com/isp/getSkipUrl接口调用失败");
            });
        }

        setButton();

        if (tsop == 1) {
            Event.addListener("onAreaChange", setButton);
        }

    })();

    function init(cfg) {
        extendLang()
        setGlobals(cfg)
        getLoginStatus()
        setDefaultScrollTop()
        setDJDTips(cfg)
        authorize(cfg);
        // followShop(cfg);
    }
    module.exports.__id = 'common'
    module.exports.init = init
})
