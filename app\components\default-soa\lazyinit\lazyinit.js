define('MOD_ROOT/lazyinit/lazyinit', function(require, exports, module) {
    var similar = require('MOD_ROOT/similar/similar')
    var Event = require('MOD_ROOT/common/tools/event').Event
    var Ad = require('MOD_ROOT/lazyinit/ad')
    var Try = require('MOD_ROOT/try/try')
    var Tools = require('MOD_ROOT/common/tools/tools');

    require('MOD_ROOT/try/try.css')
    require('MOD_ROOT/ELazyload/ELazyload')

    var load = function($el, cfg, callback) {
        if ($el.length) {
            $el.ELazyload({
                type: 'module',
                onAppear: function() {
                    callback($el, cfg)
                }
            })
        }
    }

    // 公共页面脚
    function setFooter(cfg) {
        var $el = $('#GLOBAL_FOOTER')

        load($el, cfg, function($el) {
            var type = $el.attr('type') || ''
            var url = cfg.foot || '//d.3.cn/footer?'

            $.ajax({
                url: url,
                dataType: 'jsonp',
                cache: true,
                scriptCharset: 'gb2312',
                jsonpCallback: 'showfooter',
                success: function(r) {
                    if (r && r.content) {
                        var content = r.content.replace(/data\-lazyload/g, 'src');
                        $el.html(content);
                    }
                }
            })
        })
    }

    // 脚印
    function setFootmark(cfg) {
        var $el = $('#footmark')

        load($el, cfg, function() {
            require.async(
                [
                    'MOD_ROOT/footmark/footmark',
                    'MOD_ROOT/footmark/footmark.css'
                ],
                function(footmark) {
                    footmark($el, cfg)
                }
            )
        })
    }

    function setComment(cfg) {
        var $el = $('#comment')

        load($el, cfg, function() {
            require.async(
                ['MOD_ROOT/comment/comment', 'MOD_ROOT/comment/comment.css'],
                function(Comment) {
                    Comment.init(cfg)
                }
            )
        })
    }

    // function setTryReport(cfg) {
    //     var $el = $('#comment')

    //     load($el, cfg, function() {
    //         Try.Report.init($('#try-report'), cfg)
    //         Try.Report.getData(0)
    //     })
    // }
    // function setTryEntry(cfg) {
    //     Try.Entry.init($('#try-entry'), cfg)
    // }

    function setConsult(cfg) {
        var $el = $('#consult')

        load($el, cfg, function() {
            require.async(
                ['MOD_ROOT/consult/consult', 'MOD_ROOT/consult/consult.css'],
                function(Consult) {
                    Consult.init(cfg)
                }
            )
        })
    }
    function setClub(cfg) {
        var $el = $('#club')

        load($el, cfg, function() {
            require.async(
                ['MOD_ROOT/club/club', 'MOD_ROOT/club/club.css'],
                function(Club) {
                    Club.init(cfg)
                }
            )
        })
    }

    function setSidereco(cfg) {
        var $el = $('.aside')

        load($el, cfg, function() {
            require.async(
                [
                    'MOD_ROOT/sidereco/sidereco',
                    'MOD_ROOT/sidereco/sidereco.css'
                ],
                function(Sidereco) {
                    Sidereco.init(cfg)
                }
            )
        })
    }

    function setAd(cfg) {
        Event.addListener('onPriceReady', function () {
            Ad.init(cfg)  // 初始化侧边栏
        })

    }

    function setSimilar(cfg) {
        var $el = $('#similar')
        similar.init(cfg, $el)
    }

    function setItemover(cfg) {
        if (!cfg.isOver) {
            return
        }

        var $el = $('#itemover')
        load($el, cfg, function() {
            require.async(
                [
                    'MOD_ROOT/itemover/itemover',
                    'MOD_ROOT/itemover/itemover.css'
                ],
                function(Itemover) {
                    Itemover.init(cfg)
                }
            )
        })
    }

    function setShopSimilar(cfg) {
        var $el = $('#shop-similar-promotion')
        load($el, cfg, function() {
            require.async(
                [
                    'MOD_ROOT/shopSimilar/shopSimilar',
                    'MOD_ROOT/shopSimilar/shopSimilar.css'
                ],
                function(ShopSimilar) {
                    if (!ShopSimilar.ShopSimilar.inited) {
                        ShopSimilar.init(cfg)
                    }
                }
            )
        })
    }

    function setAskAnswer(cfg) {
        var $el = $('#askAnswer')
        load($el, cfg, function() {
            require.async(
                [
                    'MOD_ROOT/askAnswer/askAnswer',
                    'MOD_ROOT/askAnswer/askAnswer.css'
                ],
                function(AskAnswer) {
                    AskAnswer.init(cfg)
                }
            )
        })
    }

    function setGlobalLog(cfg) {
        var globalType = null

        if (cfg.isYuYue) {
            globalType = 'yuyue'
        }
        if (cfg.isYuShou) {
            globalType = 'yushou'
        }
        if (cfg.isPinGou) {
            globalType = 'pin'
        }
        if (cfg.isTuanGou) {
            globalType = 'pc_tuangou'
        }

        if (globalType) {
            log('gz_item', 'gz_detail', '03', globalType)
        }
    }

    function goToComment() {
        $('#detail .tab-main li[data-anchor="#comment"]').trigger('click')
        if ($('#detail').length) {
            setTimeout(function() {
                $('html,body').scrollTop($('#detail').offset().top - 16)
            }, 200)
        }
    }

    function setDefaultAnchor() {
        var anchor = location.hash

        if (anchor === '#comment') {
            goToComment()
        }
    }

    // function setCarButler(cfg) {
    //     // var tpl = '\
    //     // <div id="iscgj" class="car-filter">\
    //     //     <div class="car-filter-hd"><strong>车管家</strong><span class="sep">&gt;</span>一键智能选购</div>\
    //     //     <div class="car-filter-bd">\
    //     //         <div class="menu-drop car-filter-item1">\
    //     //             <div class="trigger">\
    //     //                 <span class="curr">品牌</span><i class="menu-drop-arrow"></i>\
    //     //             </div>\
    //     //             <div class="menu-drop-main">\
    //     //                 <ul class="menu-drop-letter-list">\
    //     //                     <li class="fore0 curr"><a href="">热门</a></li>\
    //     //                     <li class="fore1"><a href="">A</a></li>\
    //     //                     <li class="fore2"><a href="">B</a></li>\
    //     //                     <li class="fore3"><a href="">C</a></li>\
    //     //                     <li class="fore4"><a href="">D</a></li>\
    //     //                     <li class="fore5"><a href="">E</a></li>\
    //     //                     <li class="fore6"><a href="">F</a></li>\
    //     //                     <li class="fore7"><a href="">G</a></li>\
    //     //                     <li class="fore8"><a href="">H</a></li>\
    //     //                     <li class="fore9"><a href="">I</a></li>\
    //     //                     <li class="fore10"><a href="">J</a></li>\
    //     //                     <li class="fore11"><a href="">K</a></li>\
    //     //                     <li class="fore12"><a href="">L</a></li>\
    //     //                     <li class="fore13"><a href="">M</a></li>\
    //     //                     <li class="fore14"><a href="">N</a></li>\
    //     //                     <li class="fore15"><a href="">O</a></li>\
    //     //                     <li class="fore16"><a href="">P</a></li>\
    //     //                     <li class="fore17"><a href="">Q</a></li>\
    //     //                     <li class="fore18"><a href="">R</a></li>\
    //     //                     <li class="fore19"><a href="">S</a></li>\
    //     //                     <li class="fore20"><a href="">T</a></li>\
    //     //                     <li class="fore21"><a href="">U</a></li>\
    //     //                     <li class="fore22"><a href="">V</a></li>\
    //     //                     <li class="fore23"><a href="">W</a></li>\
    //     //                     <li class="fore24"><a href="">X</a></li>\
    //     //                     <li class="fore25"><a href="">Y</a></li>\
    //     //                     <li class="fore26"><a href="">Z</a></li>\
    //     //                 </ul>\
    //     //                 <ul class="menu-drop-list">\
    //     //                 </ul>\
    //     //             </div>\
    //     //         </div>\
    //     //         <div class="menu-drop car-filter-item2">\
    //     //             <div class="trigger">\
    //     //                 <span class="curr">车系</span><i class="menu-drop-arrow"></i>\
    //     //             </div>\
    //     //             <div class="menu-drop-main">\
    //     //                 <div class="menu-drop-hint">请先选择品牌</div>\
    //     //                 <div class="menu-drop-list-container">\
    //     //                 </div>\
    //     //             </div>\
    //     //         </div>\
    //     //         <div class="menu-drop car-filter-item3">\
    //     //             <div class="trigger">\
    //     //                 <span class="curr">年款</span><i class="menu-drop-arrow"></i>\
    //     //             </div>\
    //     //             <div class="menu-drop-main">\
    //     //                 <div class="menu-drop-hint">请先选择品牌和车系</div>\
    //     //                 <ul class="menu-drop-list">\
    //     //                 </ul>\
    //     //             </div>\
    //     //         </div>\
    //     //         <div class="menu-drop car-filter-item4">\
    //     //             <div class="trigger">\
    //     //                 <span class="curr">车型</span><i class="menu-drop-arrow"></i>\
    //     //             </div>\
    //     //             <div class="menu-drop-main">\
    //     //                 <div class="menu-drop-hint">请先选择品牌，车系和年款</div>\
    //     //                 <ul class="menu-drop-list">\
    //     //                 </ul>\
    //     //             </div>\
    //     //         </div>\
    //     //         <a href="" target="_blank" class="btn btn-default car-filter-btn">查找配件</a>\
    //     //         <div class="car-butler-index">\
    //     //             <a href="//auto.jd.com" target="_blank" class="link">进入车管家首页>></a>\
    //     //         </div>\
    //     //     </div>\
    //     // </div>';
    //     //
    //     // $('#detail').before(tpl)

    //     // 车管家初始化
    //     //if (!$("#iscgj").length) return false;
    //     if (!$('#iscgj').length && !$('#choose-car').length) return false
    //     require.async(
    //         [
    //             'MOD_ROOT/carButler/carButler',
    //             'MOD_ROOT/carButler/carButler.css'
    //         ],
    //         function(CarButler) {
    //             new CarButler({
    //                 el: '#iscgj',
    //                 isGoToCarPage: true
    //             })
    //             $('#iscgj').show()
    //         }
    //     )
    // }

    function setIE6Tip() {
        if ($.browser.isIE6() || $.browser.isIE7()) {
            require.async(
                ['MOD_ROOT/ie6Tip/ie6Tip', 'MOD_ROOT/ie6Tip/ie6Tip.css'],
                function(ie6Tip) {
                    ie6Tip.init()
                }
            )
        }
    }

    function setBabyInfo(cfg) {
        var $el = $('#J-baby')

        if (!$el.length) return false
        // 母婴垂直化
        var source = ['MOD_ROOT/baby/baby', 'MOD_ROOT/baby/baby.css']
        require.async(source, function(Baby) {
            Baby.init($el)
        })
    }

    function init(cfg) {
        setSidereco(cfg)
        setComment(cfg)
        // setTryReport(cfg)
        // setTryEntry(cfg)
        setConsult(cfg)
        setClub(cfg)

        setSimilar(cfg)

        setFooter(cfg)
        setFootmark(cfg)

        setAd(cfg)
        setItemover(cfg)
        setShopSimilar(cfg)

        /// 问答楼层屏蔽
        // var $elements = $('#askAnswer');
        // if ($elements.length) {
        //     Tools.commentMeta({
        //         skus: [cfg.skuid],
        //         onlyData: true,
        //         callback: function(skuId, data){
        //             var isSensitive = data && data.SensitiveBook == 1;
        //             if (isSensitive) {
        //                 $elements.remove();
        //             } else {
        //                 setAskAnswer(cfg)
        //             }
        //         }
        //     });
        // }
        var $elements = $('#askAnswer');
        if ($elements.length) {
            Event.addListener('onCommentMeta', function(data){
                var isSensitive = data && data.commentMeta && data.commentMeta.SensitiveBook == 1;
                if (isSensitive) {
                    $elements.remove();
                } else {
                    setAskAnswer(cfg)
                }
            });
        }

        setDefaultAnchor(cfg)
        setGlobalLog(cfg)
        setIE6Tip()

        // setCarButler(cfg)
        setBabyInfo(cfg)

        // 老版埋点
        $('#InitCartUrl').bind('click', function() {
            JA && JA.tracker.ngloader('item.010002', {
                sku: cfg.skuid,
                jp: cfg.jp
            })
        })
    }

    module.exports.__id = 'lazyinit'
    module.exports.setShopSimilar = setShopSimilar
    module.exports.goToComment = goToComment
    module.exports.init = init
})
