{% import 'views/maco/default.html' as wow %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <title>{% block title %}手机单品页 - {% endblock %}</title>

    {% block style %}
        <link type="text/css" rel="stylesheet" href="//misc.360buyimg.com/jdf/1.0.0/unit/??ui-base/1.0.0/ui-base.css,shortcut/2.0.0/shortcut.css,global-header/1.0.0/global-header.css,myjd/2.0.0/myjd.css,nav/2.0.0/nav.css,shoppingcart/2.0.0/shoppingcart.css,global-footer/1.0.0/global-footer.css,service/1.0.0/service.css" />
        {{ Tag('link', '/components/main/main.css') }}
        {{ Tag('link', '/tests/jasmine/jasmine.css') }}

    {% endblock %}
    <script>
        window.pageConfig = {
            compatible: true,
            product: {
                modules: ['address', 'baitiao', 'buytype'],
                skuid: 1861096,
                name: '\u0041\u0070\u0070\u006c\u0065\u0020\u0069\u0050\u0068\u006f\u006e\u0065\u0020\u0036\u0073\u0020\u0070\u006c\u0075\u0073\u0020\u0028\u0041\u0031\u0036\u0039\u0039\u0029\u0020\u0036\u0034\u0047\u0020\u94f6\u8272\u0020\u79fb\u52a8\u8054\u901a\u7535\u4fe1\u0034\u0047\u624b\u673a',
                skuidkey:'D8FE29A0500C8F5053AF480926DD0056',
                href: '//item.jd.com/1861096.html',
                src: 'jfs/t2491/330/130347277/93583/10ac6d51/55f0e840N6609b12b.jpg',
                cat: [9987,653,655],
                brand: 14026,
                pType: 1,
                isClosePCShow: false,
                venderId:1000004067,
                shopId:'1000004067',
                commentVersion:'6256',                 specialAttrs:["isFlashPurchase-0","isSupportCard","isCanVAT","isHaveYB","packType","isCanUseDQ-1","IsNewGoods","isSelfService-0","isCanUseJQ-1","isWeChatStock-0","HYKHSP-0","is7ToReturn-1","isNSNGgoods-0","YYSLLZC-0"],
                recommend : [0,1,2,3,4,5,6,7,8,9],
                easyBuyUrl:"http://easybuy.jd.com/skuDetail/newSubmitEasybuyOrder.action",
                phoneNetwork:['移动4G','联通4G','电信4G','移动3G','联通3G','电信3G','移动2G/联通2G','电信2G'],                colorSize: [{"Color":"金色","Spec":"16GB","SkuId":1861091,"Size":"公开版"},{"Color":"银色","Spec":"16GB","SkuId":1861092,"Size":"公开版"},{"Color":"深空灰","Spec":"16GB","SkuId":1861093,"Size":"公开版"},{"Color":"玫瑰金","Spec":"16GB","SkuId":1861094,"Size":"公开版"},{"Color":"金色","Spec":"64GB","SkuId":1861095,"Size":"公开版"},{"Color":"金色","Spec":"16GB","SkuId":1866942,"Size":"移动购机送费"},{"Color":"银色","Spec":"16GB","SkuId":1866943,"Size":"移动购机送费"},{"Color":"深空灰","Spec":"16GB","SkuId":1866944,"Size":"移动购机送费"},{"Color":"玫瑰金","Spec":"16GB","SkuId":1866945,"Size":"移动购机送费"},{"Color":"金色","Spec":"16GB","SkuId":1867687,"Size":"联通0元购机"},{"Color":"银色","Spec":"16GB","SkuId":1867688,"Size":"联通0元购机"},{"Color":"深空灰","Spec":"16GB","SkuId":1867682,"Size":"联通0元购机"},{"Color":"玫瑰金","Spec":"16GB","SkuId":1867670,"Size":"联通0元购机"},{"Color":"金色","Spec":"16GB","SkuId":1867698,"Size":"电信送话费"},{"Color":"银色","Spec":"16GB","SkuId":1867704,"Size":"电信送话费"},{"Color":"深空灰","Spec":"16GB","SkuId":1867705,"Size":"电信送话费"},{"Color":"玫瑰金","Spec":"16GB","SkuId":1867707,"Size":"电信送话费"},{"Color":"银色","Spec":"64GB","SkuId":1861096,"Size":"公开版"},{"Color":"深空灰","Spec":"64GB","SkuId":1861097,"Size":"公开版"},{"Color":"玫瑰金","Spec":"64GB","SkuId":1861098,"Size":"公开版"},{"Color":"金色","Spec":"64GB","SkuId":1866996,"Size":"移动购机送费"},{"Color":"银色","Spec":"64GB","SkuId":1866997,"Size":"移动购机送费"},{"Color":"深空灰","Spec":"64GB","SkuId":1866998,"Size":"移动购机送费"},{"Color":"玫瑰金","Spec":"64GB","SkuId":1866999,"Size":"移动购机送费"},{"Color":"金色","Spec":"64GB","SkuId":1867039,"Size":"联通0元购机"},{"Color":"银色","Spec":"64GB","SkuId":1867047,"Size":"联通0元购机"},{"Color":"玫瑰金","Spec":"64GB","SkuId":1867048,"Size":"联通0元购机"},{"Color":"金色","Spec":"64GB","SkuId":1867053,"Size":"电信送话费"},{"Color":"银色","Spec":"64GB","SkuId":1867056,"Size":"电信送话费"},{"Color":"深空灰","Spec":"64GB","SkuId":1867054,"Size":"电信送话费"},{"Color":"玫瑰金","Spec":"64GB","SkuId":1867044,"Size":"电信送话费"},{"Color":"深空灰","Spec":"128GB","SkuId":1861101,"Size":"公开版"},{"Color":"玫瑰金","Spec":"128GB","SkuId":1861102,"Size":"公开版"},{"Color":"金色","Spec":"128GB","SkuId":1861099,"Size":"公开版"},{"Color":"银色","Spec":"128GB","SkuId":1861100,"Size":"公开版"},{"Color":"金色","Spec":"128GB","SkuId":1866866,"Size":"移动购机送费"}],                warestatus: 1,                                 tips: [{"order":3,"tip":"支持7天无理由退货"}],                                desc: '//d.3.cn/desc/1861096',                foot: '//d.3.cn/footer?type=common_config2'
            }
        };
    </script>
    <script src="//misc.360buyimg.com/??jdf/lib/jquery-1.6.4.js,jdf/1.0.0/unit/base/1.0.0/base.js,jdf/1.0.0/ui/ui/1.0.0/ui.js"></script>

    {{ Tag('script', '/tests/jasmine/jasmine.js') }}
    {{ Tag('script', '/tests/jasmine/jasmine-html.js') }}
    {{ Tag('script', '/tests/jasmine/boot.js') }}

    <script>
        seajs.config({
            paths: {
                'MISC' : '//misc.360buyimg.com',
                'MOD_ROOT' : '//static.360buyimg.com/item/1.1.0/components',
                'PLG_ROOT' : '//static.360buyimg.com/item/1.1.0/components/common/plugins',
                'JDF_UI'   : '//misc.360buyimg.com/jdf/1.0.0/ui',
                'JDF_UNIT' : '//misc.360buyimg.com/jdf/1.0.0/unit'
            },
            comboExcludes: /.*/,
            map: [
                ['//static.360buyimg.com/item/1.1.0/', '//' + location.host + '/']
            ]
        });
    </script>
</head>
<body>


    {% block body %} {% endblock %}


    {% block script %}{% endblock %}

</body>
</html>
