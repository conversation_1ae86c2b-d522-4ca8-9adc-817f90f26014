define('MOD_ROOT/fittingSuit/fittingSuit', function(require, exports, module) {
    var Tools = require('MOD_ROOT/common/tools/tools');

    var cfg = (window.pageConfig &&
        window.pageConfig.product) || {};

    // 推荐配件加载
    function handleCombo(url, colorParm, reqParam, callback) {
        var obj = Object.assign(colorParm, reqParam);
        $.ajax({
            url: url,
            data: obj,
            dataType: 'json',
            xhrFields: {
                withCredentials: true,
            },
            success: function (result) {
                if ( !result ) return false;

                for ( var key in result ) {
                    if ( result.hasOwnProperty(key) ) {
                        callback({
                            method: key,
                            data: result[key].data
                        });
                    }
                }
            }
        });
    }

    // 获取cookie uuid
    function getUid() {
        var __jda = readCookie('__jda');
        var uid = '';

        if ( __jda ) {
            if ( __jda.split('.')[1] == '-' ) {
                uid = -1;
            } else {
                uid = __jda.split('.')[1];
            }
        } else {
            uid = -1;
        }

        return uid;
    }
    // 获取cookie 地区id
    function getLid() {
       var __loc = readCookie('ipLoc-djd');
       var lid   = 1;
       if ( __loc && __loc.indexOf('-') > 0 ) {
           lid = __loc.split('-')[0];
       } else {
           lid = 1;
       }
    
       return lid;
    }

    function fittingSuit(pType) {
        var uid = getUid();
        var lid = Tools.getAreaId().areaIds[0];

        var rId = null;

        if ( pType === 1 ) { rId = 103003; }
        if ( pType === 2 ) { rId = 102001; }
        if ( pType === 3 ) { rId = 104001; }
        if ( pType === 4 ) { rId = 104026; }

        // 合并调用配件、套装、组合参数
        var reqParam = {
            // methods : 'accessories,suit,combination',
            methods: 'accessories,suit' + (!cfg.isPop ? 'v2' : ''),
            p      : rId,
            sku    : cfg.skuid,
            cat    : cfg.cat.join(','),
            lid    : lid,
            uuid   : uid,
            pin    : readCookie('pin') || '',
            ck     : 'pin,ipLocation,atw,aview',
            lim    : pageConfig.wideVersion && pageConfig.compatible ? 6 : 5,
            cuuid  : readCookie('__jdu'),
            csid   : readCookie('__jdb'),
            area   : Tools.getAreaId().areaIds.join('_')
        };

        var body = JSON.stringify({
            methods: 'accessories,suit' + (!cfg.isPop ? 'v2' : ''),
            p      : rId,
            sku    : cfg.skuid,
            cat    : cfg.cat.join(','),
            lid    : lid,
            uuid   : uid,
            pin    : readCookie('pin') || '',
            ck     : 'pin,ipLocation,atw,aview',
            lim    : pageConfig.wideVersion && pageConfig.compatible ? 6 : 5,
            cuuid  : readCookie('__jdu'),
            csid   : readCookie('__jdb'),
            area   : Tools.getAreaId().areaIds.join('_')
        });
        var time = new Date().getTime()
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pc_recommend',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                        getRecommendData(colorParm, reqParam);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getRecommendData(colorParm, reqParam);
                    //烛龙上报
                    Tools.getJmfe(colorParm, e, "detail文件pc_structured接口设备指纹异常", 250)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getRecommendData(colorParm, reqParam);
            //烛龙上报
            Tools.getJmfe(colorParm, e, "detail文件pc_structured接口加固异常", 250)
        }            
        // 加固end

        function getRecommendData(colorParm, reqParam){
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            handleCombo(host+'/recommend', colorParm , reqParam, function (res) {
                // 暂切换回前端调用组合
                if ( res.method && res.data ) {
                    require.async('MOD_ROOT/fittingSuit/' + res.method, function (method) {
                        if ( method ) {
                            method(res.data, colorParm, reqParam);
                        } else {
                            console.error('Method: ' + method + ' not found. Maybe combo serve error.');
                        }
                    });
                } else {
                    //var $trgMat = {
                    //    'accessories': 0,
                    //    'suit': 1
                    //};
                    //pageConfig.fittingSuitTab.nav.eq($trgMat[res.method]).hide();
                }
            });
        }
        
    }

    module.exports = fittingSuit;
});


