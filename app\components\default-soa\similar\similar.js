define('MOD_ROOT/similar/similar', function(require, exports, module) {
    var Recommend = require('MOD_ROOT/common/tools/recommend');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var G = require('MOD_ROOT/common/core');
    var Tools = require('MOD_ROOT/common/tools/tools');

    require('MOD_ROOT/similar/similar.css');
    require('PLG_ROOT/jQuery.imgScroll');

    var template = '\
        <ul class="plist">\
        {for item in data}\
        {if Number(item_index) < 30}\
        <li class="fore${Number(item_index)+1}" \
            data-clk="${item.clk}" \
            data-push="${pageConfig[skuHooks].push(item.sku)}">\
            <div class="p-img ac">\
                <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                    <img height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" alt="${item.t}" src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s${ext.imgWidth}x${ext.imgHeight}_${item.img}">\
                </a>\
            </div>\
            <div class="p-name"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
            <div class="p-price"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
        </li>\
        {/if}\
        {/for}\
        </ul>';

    function setReco(cfg, $el) {
        var paramObj = {
            ebook: {
                param: {
                    p: 619053,
                    sku: cfg.skuid,
                    ck: 'pin,bview',
                    lim: 18
                },
                ext: {
                    imgWidth : 100,
                    imgHeight: 100,
                    divId:"skus-ebook-",
                },
                height: 200
            },
            other: {
                param: {
                    p: cfg.pType === 2 ? 102000: 103000,
                    sku: cfg.skuid,
                    ck: 'pin',
                    lim: 12
                },
                ext: {
                    imgWidth : 100,
                    imgHeight: 100,
                    divId:"skus-other-",
                },
                height: 200
            }
        }

        var currType = '';

        if(cfg.isEBook) {
            currType = "ebook";
        } else {
            currType = "other";
        }

        new Recommend({
            $el: $('#similar .list'),
            skuHooks: 'SKUS_nostock',
            template: template,
            ext: paramObj[currType].ext,
            param: paramObj[currType].param,
            // loadPrice: !cfg.isEBook,
            callback: function(hasData, r) {
                var $scroll = $el.find('.list');
                var $prev = $el.find('.arrow-prev');
                var $next = $el.find('.arrow-next');

                var visibleNum = G.wideVersion ? 7 : 6;
                if (hasData) {
                    $el.show();

                    if(cfg.isEBook && r.success) {
                        var arr = [];
                        $.each(r.data, function (i, n) {
                            arr.push(n.sku);
                        });

                        // var priceNum = require('MOD_ROOT/ebook/ebook').priceNum;
                        // var opts = {
                        //     skus: arr,
                        //     $el: $el
                        // }

                        // priceNum(opts);
                    }
                } else {
                    $el.hide();
                }
                $scroll.imgScroll({
                    width: 156,
                    height: paramObj[currType].height,
                    visible: visibleNum,
                    showControl: true,
                    status: true,
                    statusWrapSelector: '.page-num',
                    step: visibleNum,
                    loop: false,
                    prev: $prev,
                    next: $next
                });

                try{
                    if(hasData){
                        // 主图浮层初始化渲染
                        // var skuArrs = []
                        // for(i = 0;i < r.data.length; i++){
                        //     skuArrs.push(r.data[i].sku)
                        // }
                        // Tools.getMainPic(r.ext.imgWidth, r.ext.imgHeight, r.ext.divId, "2", skuArrs)
                        var layerList = r.floatLayerList
                        if(layerList.length > 0){
                            Tools.getPcSkuLayers(layerList, r.ext.imgWidth, r.ext.imgHeight, r.ext.divId, "2")
                        } 
                    }
                }catch(e){
                    console.log("主图浮层初始化渲染",e)
                }
            }
        });
    }
    
    function init(cfg, $el) {
        return
        if(cfg.isEBook) {
            setReco(cfg, $el);
            return;
        }

        Event.addListener('onStockReady', function () {
            if (!cfg.havestock) {
                setReco(cfg, $el);//没库存时推荐
            } else {
                $el.hide();
            }
        });
    }

    module.exports.__id = 'similar';
    module.exports.init = init;
});
