{# custom nunjucks maco here #}

{% macro plist(id='plist', class='plist', source="data-lazy-img", length=5, width=100, height=100) -%}
<ul id="{{ id }}" class="{{ class }}">
    {% for i in range(0, length) %}
    <li class="fore{{ i }}">
        <div class="p-img">
            <a href="http://jd.com/" target="_blank">
                <img {{ source }}="http://img.la/border/{{ width }}x{{ height }}" width="{{ width }}" height="{{ height }}" alt=""/>
            </a>
        </div>
        <div class="p-name">
            <a href="http://jd.com/" target="_blank">
                商品名称（Apple iPhone 6s(A1700) 16G 玫瑰 金色 移动联通电信4G手机）
            </a>
        </div>
        <div class="p-price">
            <strong class="J-p-sku">￥578.00</strong>
        </div>
    </li>
    {% endfor %}
</ul>
{%- endmacro %}

{% macro plist_pop(id='plist', class='plist', source="data-lazy-img", length=5, width=100, height=100) -%}
<ul id="{{ id }}" class="{{ class }}">
    {% for i in range(0, length) %}
    <li class="fore{{ i }}">
        <div class="p-img">
            <a href="http://jd.com/" target="_blank">
                <img {{ source }}="http://img.la/border/{{ width }}x{{ height }}" width="{{ width }}" height="{{ height }}" alt=""/>
            </a>
        </div>
        <div class="p-name">
            <a href="http://jd.com/" target="_blank">
                商品名称（Apple iPhone 6s(A1700) 16G 玫瑰 金色 移动联通电信4G手机）
            </a>
        </div>
        <div class="p-info clearfix">
            <div class="p-count fl">
                热销<em>700</em>部
            </div>
            <div class="p-price fr">
                <strong class="J-p-sku">￥578.00</strong>
            </div>
        </div>
        <div class="p-num">{{ i+1 }}</div>
    </li>
    {% endfor %}
</ul>
{%- endmacro %}

{% macro product(fore=0, width=100, height=100) -%}
    <div class="p-img">
        <a href="http://jd.com/" target="_blank">
            <img src="http://img.la/border/{{ width }}x{{ height }}" width="{{ width }}" height="{{ height }}" alt=""/>
        </a>
    </div>
    <div class="p-name">
        <a href="http://jd.com/" target="_blank">
            商品名称（Apple iPhone 6s(A1700) 16G 玫瑰 金色 移动联通电信4G手机）
        </a>
    </div>
    <div class="p-price">
        <input type="checkbox" name="" id="inp-{{ fore }}"/>
        <label for="inp-{{ fore }}"><strong class="J-p-sku">￥578.00</strong></label>
    </div>
    <i class="plus">+</i>
{%- endmacro %}

{% macro img(source='src', width=160, height=160, alt='') -%}
<img {{ source }}="http://img.la/border/{{ width }}x{{ height }}" width="{{ width }}" height="{{ height }}" alt=""/>
{%- endmacro %}

{% macro imgLink(source='src', width=160, height=160, alt='') -%}
<a href="#none">
    <img {{ source }}="http://img.la/border/{{ width }}x{{ height }}" width="{{ width }}" height="{{ height }}" alt=""/>
</a>
{%- endmacro %}

{% macro button(name="btn", id="", class="btn-special1 btn-lg", style="") -%}
<a href="#none" id="{{ id }}" class="{{ class }}" style="{{ style }}">{{ name }}</a>
{%- endmacro %}

{% macro seajsConfig() -%}
<script>
    seajs.config({
        paths: {
            'MISC' : '//misc.360buyimg.com',
            'MOD_ROOT' : '//static.360buyimg.com/item/default/1.0.0/components',
            'PLG_ROOT' : '//static.360buyimg.com/item/default/1.0.0/components/common/plugins',
            'JDF_UI'   : '//misc.360buyimg.com/jdf/1.0.0/ui',
            'JDF_UNIT' : '//misc.360buyimg.com/jdf/1.0.0/unit'
        },
        comboExcludes: /.*/,
        map: [
            ['//static.360buyimg.com/item/default/1.0.0', '//' + location.host + '/']
        ]
    });
</script>
{%- endmacro %}
