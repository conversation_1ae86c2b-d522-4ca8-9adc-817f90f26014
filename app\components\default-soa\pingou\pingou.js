/**
 * 预售业务：一阶预售（定金+尾款或全款）、三阶预售、梦想购
 * `pageConfig.product.isYuShou` 值为`true`表示是“一阶预售”
 * `pageConfig.product.isPinGou` 值为`true`表示是“三阶预售”
 * `//yuding.jd.com/presaleInfo/getPresaleInfo.action`接口`ret.bizType`值为`1`时表示是“梦想购”
 * “梦想购”就是一阶梯、全款预售中的一种特殊情况，对外为了区分不同业务名称，设置了bizType = 1，对内，就按照一般的一阶梯全款预售来处理。
 */
define('MOD_ROOT/pingou/pingou', function(require, exports, module) {
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Tools = require('MOD_ROOT/common/tools/tools');
    var showInvite = require('MOD_ROOT/buybtn/reservation').showInvite;
    var addToCart = require('MOD_ROOT/buybtn/buybtn').addToCart;
    var O2O = require('MOD_ROOT/o2o/o2o');
    var Countdown = Tools.Countdown;

    // 梦想购逻辑处理
    var DreamPurchase = {
        
        setBanner: function ($banner, status,text2) {
            if ($banner.length) {
                $banner.addClass('dream-purchase-banner').find('.activity-type strong').html('抢购');
                if (status <= 0) {
                    $banner.find('.J-item-1').html('');
                } else {
                    $banner.find('.J-item-1').html(text2);
                }
            }
        },

        setSaleProcess: function ($mount, status, count, limit,dreamPurchase) {
            var PROCESS_TEMPLATE = '\
                <div class="dream-purchase-sale-process" style="margin-top:${status <= 0 ? 0 : 28}px"> \
                    <div class="dt">玩法说明</div> \
                    <div class="dd"> \
                        {if status <= 0} \
                            <div class="progress-bar"></div><div class="dream-purchase-sale-process-status do"></div> \
                            <div class="dream-purchase-sale-process-tips">抢购满${limit}件&nbsp;可生产发货</div> \
                        {else} \
                            {var ratio = count/limit} \
                            <div class="progress-bar"> \
                                <div class="progress-bar-inner" style="width:${ratio > 1 ? 100 : ratio*100}%;"> \
                                    <div class="progress-bar-text-wrap"> \
                                        <div class="progress-bar-text-wrap-inner"> \
                                            <div class="progress-bar-text">${text2}</div> \
                                        </div> \
                                    </div> \
                                </div> \
                            </div><div class="dream-purchase-sale-process-status ${ratio > 1 ? \'done\' : \'do\' }"></div> \
                            <div class="dream-purchase-sale-process-tips">${text1}</div> \
                        {/if} \
                    </div> \
                </div>';
            if ($mount.length && $(".dream-purchase-sale-process").length == 0) {
                $mount.after(PROCESS_TEMPLATE.process({
                    status: status,
                    count: count,
                    limit:limit,
                    text1:dreamPurchase && dreamPurchase.progressBarText1,
                    text2:dreamPurchase && dreamPurchase.progressBarText2
                }));
            }
        },

        setSaleProcedure: function ($mount, presaleStartTime, presaleEndTime, shipTime) {
            var PROCEDURE_TEMPLATE = ' \
                <h3>抢购流程</h3> \
                <div class="item"> \
                    <i class="sprite-pay"></i> \
                    <dl> \
                        <dt>付全款</dt> \
                        <dd>${presaleStartTime}-${presaleEndTime}</dd> \
                    </dl> \
                    <span class="sprite-arrow"></span> \
                </div> \
                <div class="item"> \
                    <i class="sprite-deliver"></i> \
                    <dl> \
                        <dt>抢购成功生产发货（不成功退款）</em></dt> \
                        <dd>预计${shipTime}开始发货</dd> \
                    </dl>\
                </div>';

            if ($mount.length) {
                $mount.html(PROCEDURE_TEMPLATE.process({
                    presaleStartTime: presaleStartTime,
                    presaleEndTime: presaleEndTime,
                    shipTime: shipTime
                }));
            }
        },
        
        setSaleSpecification: function ($mount,yushouRule) {
            var SPECIFICATION_TEMPLATE = '\
                <h3>抢购规则：</h3> \
                <ol> \
                    {for rule in yushouRule}\
                    <li>${rule}</li>\
                    {/for}\
                </ol> \
            ';
            if ($mount.length) {
                $mount.html(SPECIFICATION_TEMPLATE.process({
                    yushouRule:yushouRule
                }));
            }
        },

        setElemText: function() {
            $('#pingou-rules-tab, .J-tips').html('抢购说明');
        },

        setPurchaseButton: function ($btn, buyButton) {
            if ($btn.length) {
                $btn.html(buyButton);
            }
        },
        setShareButton: function ($btn, shareButton) {
            if ($btn.length) {
                $btn.removeClass('yuyue-share').addClass('btn-special3 btn-lg');
                $btn.html(shareButton);
            }
        }
    };
    
    var PinGou = {
        init: function (cfg) {
            /// `pageConfig.product.isYuShou`或
            /// `pageConfig.product.isPinGou` 值为`true`时渲染该DOM
            var $el = $("#pingou");
            if (!$el || !$el.length) {
                return;
            }

            this.cfg = cfg;
            this.$el = $el;
            this.$tag = $(".J-yy-category", $el);
            this.$banner = $('#pingou-banner');
            this.$process = $('#pingou-process');
            this.$btn = $('#btn-reservation,#btn-reservation-mini');
            this.$shipTime = $('#summary-yushou-ship');
            // this.$btnShare = $('#btn-pg-share');
            this.$pgtips = $('.J-pingou-tips');
            this.$member = $('#pingou-member-list');
            this.blockPresaleQuantity(); // 屏蔽苹果预售商品的数量
            this.bindEvent();
            this.get();
        },
        bindEvent: function () {
            var _this = this;
            var $member = $('#pingou-member-trigger'); // 该功能暂时没上

            // $(document).delegate('#btn-pg-share,.J-btn-invite,.J-yuyue-share', 'click.pingou_invite', function () {
            //     _this.invite();
            // });

            $member.bind('click', function () {
                _this.getMembers();
                _this.getRank();
            });

            // “预售说明”
            this.$el.delegate('.J-tips', 'click', function () {
                if ($('#detail').length) {
                    $('html,body').scrollTop($('#detail').offset().top);
                }
                $('#pingou-rules-tab').trigger('click');
            });

            Event.addListener('onStockReady', function () {
                if (_this.btnEnabled) {
                    _this.enabled();
                } else {
                    _this.disabled();
                }
            });
            Event.addListener('onNumChange', function () {
                if (_this.btnEnabled) {
                    _this.enabled();
                }
            });

            Event.addListener('onLDPSelected', function (data) {
                _this.LDP = data.did;
                _this.enabled()
            });
        },
        invite: function () {
            return showInvite(this.cfg);
        },
        getMembers: function () {
            var _this = this;
            $.ajax({
                //url: 'http://jsonpmock.xyz/d211c03ac995f325c3ea513f00a2559e',
                url: '//fenxiang.jd.com/shareCount/joinMemberShip.action',
                dataType: 'jsonp',
                data: {
                    sku: this.cfg.skuid,
                    pageno: 1
                },
                success: function (r) {
                    _this.setMemberList(r);
                }
            });
        },
        setMemberList: function (r) {
            var template = '\
            {for item in userInfoList}\
            <div class="user-item">\
                <img src="${item.headImg}" alt="${item.nickname}"/>\
                <dl class="user-info">\
                    <dt>${item.nickname}</dt>\
                    <dd>邀请数量:${item.barginCount}</dd>\
                    <dd>${item.createTime}入伙</dd>\
                </dl>\
            </div>\
            {/for}';

            this.$member.find('.J-invite-user-list').html(template.process(r));
        },
        getRank: function () {
            var _this = this;
            $.ajax({
                //url: 'http://jsonpmock.xyz/d211c03ac995f325c3ea513f00a2559e',
                url: '//fenxiang.jd.com/shareCount/daRenBang.action',
                sku: {
                    sku: this.cfg.skuid
                },
                dataType: 'jsonp',
                data: {
                    sku: this.cfg.skuid
                },
                success: function (r) {
                    _this.setRank(r);
                }
            })
        },
        setRank: function (r) {
            var template = '\
            {for item in userInfoList}\
            {if Number(item_index)<3}\
            <div class="invite-item">\
                <div class="invite-img">\
                    <i class="sprite-top${Number(item_index)+1}"></i>\
                    <img src="${item.headImg}" alt="${item.nickname}"/>\
                </div>\
                <dl class="invite-info">\
                    <dt>${item.nickname}</dt>\
                    <dd>已邀请<span>${item.barginCount}</span>人</dd>\
                </dl>\
            </div>\
            {/if}\
            {/for}';

            this.$member.find('.J-my-count').html(r.userCount);
            this.$member.find('.J-invite-tips').html(r.ruleDetail);
            this.$member.find('.J-invite-top').html(template.process(r));
        },
        get: function () {
            var _this = this;
            Event.addListener('onStockReady', function (r) {
                if (r.stock && r.stock.data && r.stock.data.YuShouInfo && r.stock.data.YuShouInfo.yuShou) {
                    _this.set(r.stock.data.YuShouInfo,r.stock.data);
                }else {
                    if(pageConfig.product.isYuShou){
                        window.location = pageConfig.product.href + "?yuyue=0"
                    }
                    _this.onError('');
                }
                
            });
            // $.ajax({
            //     url: '//item-soa.jd.com/getWareYuShou',
            //     dataType: 'jsonp',
            //     timeout: 6000,
            //     //cache: true,
            //     //jsonpCallback: 'fetchJSON',
            //     data: {
            //         skuId: this.cfg.skuid,
            //         cat: this.cfg.cat.join(','),
            //         area: Tools.getAreaId().areaIds.join('_'),
            //         shopId: this.cfg.shopId,
            //         venderId: this.cfg.venderId
            //     },
            //     error: function () {
            //         _this.onError();
            //     },
            //     success: function (r) {
            //         if (r && r.YuShouInfo && r.YuShouInfo.yuShou) {
            //             _this.set(r.YuShouInfo);
            //         }else {
            //             _this.onError('');
            //         }
            //     }
            // });


        },
        onError: function (type) {
            // 接口出错降级展示加入购物车按钮
            this.cfg.addToCartBtn.reInit(this.$btn.eq(0));
            G.log(null, 'pingou.js', 'pingou service return a error, Maybe service unavailable. type=['+ type +']');
        },
        set: function (res,data) {
            res = res || {};
            data = data || {};
            /// 预售支付类型枚举说明：
            /// 1-全款预售
            /// 2-定金、全款均可
            /// 3-三阶预售
            /// 5-一阶预售仅定金
            var type = '';
            var payType = +res.yushouStepType;

            /// 梦想购标识
            var bizType = +res.bizType;

            /// 预售状态0-不可支付 1可支付
            var status = +res.status;

            if(status == 0){
                this.disabled();
            }

            /// 渲染梦想购
            if (payType == 1) {
                type = 2;
                if(bizType == 1 ) {
                    type = 4;
                    var status = +res.status
                    var count = +res.yuShouNumOfPeople
                    var dreamPurchase = res && res.dreamPurchase;
                    var limit = dreamPurchase && dreamPurchase.produceLimitNum
                    DreamPurchase.setBanner(this.$banner, status,dreamPurchase && dreamPurchase.progressBarText2),
                    DreamPurchase.setSaleProcess($('.J-summary-price', this.$el), status, count, limit, dreamPurchase),
                    DreamPurchase.setSaleProcedure(this.$process, res.presaleStartTime, res.presaleEndTime, res.expectedDeliveryDate),
                    DreamPurchase.setSaleSpecification($('.pingou-rules'),dreamPurchase && dreamPurchase.yushouRule),
                    DreamPurchase.setElemText(),
                    DreamPurchase.setPurchaseButton(this.$btn, dreamPurchase && dreamPurchase.buyButton),
                    DreamPurchase.setShareButton($('.J-yuyue-share, #btn-pg-share'), dreamPurchase && dreamPurchase.shareButton)
                };
            }else if(payType == 2){
                type = 2;
            }

            /// 渲染三阶预售
            if (payType == 3) {
                res.yuShou3LadderData &&
                res.yuShou3LadderData.length &&
                this.setStep(res);
            }

            var promotionText;
            var minusFlag = false;
            if (payType == 5) {
                /// O2O模块初始化
                var cartBtn = this.cfg.addToCartBtn;
                if (cartBtn) {
                    cartBtn.$el = cartBtn.$el.add(this.$btn);
                    cartBtn.href = res.url;
                    cartBtn.originHref = res.url;
                    // O2O.init(this.cfg);
                }

                /// 预售促销文案
                var expAmount = res.expAmount;
                var oriPrice  = res.oriPrice;
                var discountType = res.discountType;
                var depositWorth = res.depositWorth;

                if (discountType == 1 && depositWorth) {
                    promotionText = "定金可抵" +
                        depositWorth + "元";
                } else if (discountType == 2 && expAmount) {
                    promotionText = "尾款立减" +
                        expAmount + "元";
                    minusFlag = true;
                } else if (expAmount && oriPrice
                    && depositWorth) {
                    promotionText = "定金可抵" +
                        depositWorth + "元";
                } else {
                    promotionText = '';
                }
            } else {
                promotionText = '';
                minusFlag = false;
            }

            /// 预售立减促销修改文案
            var $titleElem = $(".J-summary-price.pin-price .dt", this.$el);
            // if (minusFlag) {
                $titleElem.text("预售价");
            // } else {
            //     $titleElem.text("京 东 价");
            // }

            /// 预售价
            var $priceElem = $(".J-presale-price", this.$el);
            var $RMBElem = $priceElem.prev('span');
            var presalePrice = res.yuShouPrice.replace("¥","");

            if (presalePrice == '' || presalePrice == '待发布') {
                presalePrice = "待发布";
                $RMBElem.hide();
                $(".pricePresaleText").hide()
                pageConfig.product.isHidePrice = true;
            } else {
                $RMBElem.show();
                $(".pricePresaleText").show()
            }

            $priceElem.html(presalePrice);

            // 预售定金
            // var $earnestElem = $(".J-earnest", this.$el);
            var earnest = res.yuShouDeposit.replace("¥","")

            if (payType && payType != 1 ) {
                if (earnest > 0) {
                    // $earnestElem.html(earnest).show();
                    if($("#btn-reservation em").length == 0){
                        $("#btn-reservation").html("<p class='p1'>支付定金<span><em>¥</em>"+earnest+"</span></p><p class='p2' style='display:none'></p>")
                        $("#btn-reservation").addClass("btn-special4 pay-deposit")
                        $("#InitCartUrl").addClass("btn-special5")
                         /// 设置预售促销文案
                        this.setTag(res);
                    }
                    
                }
            }


            /// 京东价
            var jdPrice = res.jdPrice;
            var $jdPriceElemWrap = $(".p-price-jd", this.$el);
            var $jdPriceElem = $(".J-p-" + this.cfg.skuid, $jdPriceElemWrap);
            if (minusFlag) {
                if (jdPrice > presalePrice) {
                    $jdPriceElemWrap.show();
                } else {
                    $jdPriceElemWrap.hide();
                }
            } else {
                $jdPriceElemWrap.hide();
                $jdPriceElem.removeClass().text(presalePrice);
            }

            /// 设置预售促销文案
            // this.setTag(res);
            /// 设置预售PLUS价格
            this.setPlusPrice(res);
            /// 发货时间
            this.setShipTime(res);
            /// 购买按钮
            this.show(res);
            /// 倒计时提示
            this.setCountdown(res);
            /// 预售流程
            this.setProcess(res);
            /// 预售推荐
            this.getRecommend();

            /// 现货预售购物车按钮
            this.showCarbtn(res,data);

            /// 预售规则说明
            this.setSaleSpecification($('.pingou-rules ol'),type);



            // 美妆加赠+预约/预售， 展示美妆加赠腰带，原有预约/预售腰带整体屏蔽
            // this.$banner.hide()

        },
        showCarbtn: function (r,d) {
            $("#InitCartUrl").remove();
            var buyNum = $("#buy-num").val() || 1;
            var cfg = this.cfg
            var skuids = this.cfg.skuid;
            Tools.checkLogin(function (data) {
                if(!(data && data.IsAuthenticated)){// 未登录
                    var btnHTML = '<a href="#none" id="InitCartUrl" class="btn-special1 btn-lg" clstag="shangpin|keycount|product|加入购物车_1">加入购物车</a>';
                    if (r.yushouStepType == 5 && r.spotPresale == 1){
                        if ($('#InitCartUrl').length < 1) {
                            $("#btn-reservation").after(btnHTML);
                            $('#InitCartUrl').click(function (event) { //预售商品加入购物车
                                event.preventDefault()
                                window.login && window.login()
                            })
                        }
                    }
                } else {
                    if (d.stockInfo && d.stockInfo.isStock){
                        var btnHTML = '<a href="//cart.jd.com/gate.action?pid='+ skuids +'&pcount='+ buyNum +'&ptype=1" data-source="common" id="InitCartUrl" class="btn-special5 btn-lg" clstag="shangpin|keycount|product|加入购物车_1">加入购物车</a>';
                    }else{
                        var btnHTML = '<a href="#none" id="InitCartUrl" class="btn-special5 btn-lg btn-disable" clstag="shangpin|keycount|product|加入购物车_1">加入购物车</a>';
                    }
                    if (r.yushouStepType == 5 && r.spotPresale == 1){
                    // if (r.yushouStepType == 5){
                        if ($('#InitCartUrl').length < 1) {
                            $("#btn-reservation").before(btnHTML); // 增加加车按钮
                            Event.fire({ // 触发事件，调整按钮样式
                                type: 'onBtnChange',
                            })
                            $('#InitCartUrl').click(function (event) { //预售商品加入购物车
                                event.preventDefault()
                                if (d.stockInfo && d.stockInfo.isStock){
                                    addToCart.call(this, event, cfg)
                                }
                            })
                            $("#InitTradeUrl").remove() // 屏蔽立即购买
                        }
                    }
                }
            })
        },
        setShipTime: function (r) {
            if (r.yushouStepType == 5 && r.spotPresale == 1){
                var text = '{0}';
                if (r.deliveryDate) {
                    this.$shipTime.find('.dd').html(text.format(r.deliveryDate));
                    this.$shipTime.show();
                } else {
                    this.$shipTime.hide();
                }
            }else{
                var text = '预计 最晚{0} 发货';
                if (r.expectedDeliveryDate) {
                    this.$shipTime.find('.dd').html(text.format(r.expectedDeliveryDate));
                    this.$shipTime.show();
                } else {
                    this.$shipTime.hide();
                }
            }
        },
        show: function (r) {
            this.$btn.show();
            // this.$btnShare.show();
            this.$pgtips.show();

            this.btnEnabled = (+r.status) !== 0;
            this.buyUrl = r.url;

            if (this.btnEnabled) {
                this.enabled();
            } else {
                this.disabled();
            }
        },
        enabled: function () {
            var num = $('#buy-num').val();
            
            if (this.cfg.havestock && !this.cfg.isClosePCShow) {
                this.$btn.removeClass('btn-disable');
                var url = this.$btn.attr('href');
                if (url !== '#none') {
                    this.buyUrl = url;
                }
                if (this.buyUrl) {
                    if (num) {
                        this.buyUrl = this.buyUrl.replace(/(nums|pcount)=\d+/g, '$1' + '=' + num);
                    }
                    this.buyUrl = Tools.addUrlParam(this.buyUrl, 'did', this.LDP);
                    this.$btn.attr('href', this.buyUrl);
                }
            } else {
                this.disabled();
            }
        },
        disabled: function () {
            this.$btn.addClass('btn-disable').attr('href', '#none');
        },
        setCurrent: function (n) {
            var $processbar = $('.J-progress-bar li', this.$el);

            for (var i = 0; i < 3; i++) {
                if ( i <= n ) {
                    $processbar.eq(i).addClass('current');
                }
            }
        },
        setTotalCount: function (total) {
            var _this = this;
            var $total = this.$el.find('.J-total-num');
            var time = 460;
            var interval = time / 20;
            var start = 0;
            var step = Math.floor(total / interval);

            this.totalTimer = setInterval(function () {
                start = start + step;

                if (step < 1 || start >= total) {
                    $total.html(total);
                    return clearInterval(_this.totalTimer);
                }
                $total.html(start);
            }, interval);
        },
        setStep: function (r) {
            var $processbar = $('.J-progress-bar li', this.$el);
            var $count = $('.J-over-price .J-over-count em', this.$el);
            var $price = $('.J-over-price .price em', this.$el);
            var $arrow = $('.J-arrow', this.$el);
            var $stepItem = $('.J-over-price li');
            var itemWidth = $stepItem.eq(0).outerWidth() + 20;
            var count = +r.yuShouNumOfPeople
            if (count > 0) {
                this.setTotalCount(count);
                this.$el.find('.total-num').css('visibility', 'visible');
            } else {
                this.$el.find('.total-num').css('visibility', 'hidden');
            }
            var stepRule = r.yuShou3LadderData
            if (count < stepRule[1].c) {
                //if (r.count != 0) {
                    $stepItem.eq(0).addClass('current');
                //}
                $arrow.css('width', (count / stepRule[1].c) * itemWidth);
                this.setCurrent(0);
            }else if (count < stepRule[2].c) {
                // 二当前在第二阶的时候算进度条规则：
                // （当前总人数 - 二阶满人数）/ （三阶满人数 - 二阶满人数）* 1阶的宽度 + 1阶的宽度
                $stepItem.eq(1).addClass('current');
                $arrow.css('width', (count - stepRule[1].c) / (stepRule[2].c - stepRule[1].c) * itemWidth + itemWidth );
                this.setCurrent(1);
            }else if (count >= stepRule[2].c) {
                // 当前在第三阶直接显示在中间，因为不确定总预约人数
                $stepItem.eq(2).addClass('current');
                $arrow.css('width', itemWidth * 2.5);
                this.setCurrent(2);
            }

            if (count == 0) {
                $processbar.eq(0).find('i').hide();
            } else {
                $processbar.eq(0).find('i').show();
            }

            if (r.yuShouladder === "2" && stepRule) {
                for (var i = 0; i < stepRule.length; i++) {
                    var rule = stepRule[i];

                    $count.eq(i).html(rule.c);
                    $price.eq(i).html(rule.m);
                }
            }
        },
        setCountdown: function (r) {
            var seconds = +r.countdown;
            var $countdown = this.$banner.find('.J-time');
            var $text = this.$banner.find('.J-text');

            if (r.cdPrefix) {
                $text.html(r.cdPrefix);
            }
            /// 参与人数
            var count = +r.yuShouNumOfPeople;
            if (count >= 0) {
                this.$banner.find('.J-count').html(count);
                this.$banner.find('.J-item-1').show();
            } else {
                $('.J-item-1', this.$banner).hide();
            }

            if (seconds > 0) {
                this.$banner.find('.J-item-2').show();
                // 开始 倒计时
                new Countdown(seconds*1000, function(res) {
                    // 是否小于1天
                    if (res.d === 0) {
                        $countdown.html( res.h + '时' + res.m + '分' + res.s + '秒');
                    } else {
                        $countdown.html( res.d + '天' + res.h + '时' + res.m + '分' + res.s + '秒');
                    }
                });
            } else {
                this.$banner.find('.J-item-2').hide();
            }

        },
        setTag: function (r) {
            var yuShouButtonTextSwitch = pageConfig.product && pageConfig.product.yuShouButtonTextSwitch
            var yuShouDeposit = r.yuShouDeposit || '';
            yuShouDeposit = yuShouDeposit.startsWith('¥') ? yuShouDeposit : '¥' + yuShouDeposit;
            if(r.yushouStepType == 5 && r.spotPresale == 1 && r.actualPrice){
                // this.$tag.html(r.actualPrice).show();
                $("#btn-reservation .p2").html(r.actualPrice).show()
                $("#btn-reservation").css("line-height","15px")
            }else{
                if (r.yuShouText) {
                    if (yuShouButtonTextSwitch) {
                        $('.step1').append(' ' + yuShouDeposit)
                        $('.step2').append(' ' + r.yuShouText)
                    }
                    $("#btn-reservation .p2").html(r.yuShouText).show()
                    $("#btn-reservation").css("line-height","15px")
                } else {
                    $("#btn-reservation .p2").hide();
                    $("#btn-reservation .p1").removeClass("p1");
                }
            }
            
        },
        setPlusPrice: function (r) {
            if (r.plusPreSalePrice) {
                var text = '￥' + r.plusPreSalePrice;
                $(".J-ys-plus-price").css("display","inline-block");
                $(".J-ys-plus-price .price").html(text)
            } else {
                $(".J-ys-plus-price").hide();
            }
        },
        setProcess: function (r) {
            if(r.yushouStepType != 1){
                $("#yushourule").show()
                $("#yushourule").find(".stepT1").html(r.presaleEndTime+" 截止")
                $("#yushourule").find(".stepT2").html(r.tailMoneyStartTime+" 开始")
                adaptToScreenWidth();
            }
            // this.$process.show();
            // this.$process.find('.J-presale-time').html(r.presaleStartTime + '-' + r.presaleEndTime);
            // this.$process.find('.J-time').html(r.expectedDeliveryDate);
            // this.$process.find('.J-balance-time').html(r.tailMoneyStartTime + '-' + r.tailMoneyEndTime);
            
        },
        getRecommend: function () {
            var _this = this;
            $.ajax({
                //url: 'http://jsonpmock.xyz/1c1ff7b9fe8e03706ef6ae03b1c99a22',
                url: '//yuding.jd.com/recommend/presaleRecInfo.action',
                dataType: 'jsonp',
                data: {
                    sku: this.cfg.skuid,
                    wid: this.cfg.cat[2]
                },
                success: function (r) {
                    _this.setRecommend(r);
                }
            });
        },
        setRecommend: function (r) {
            var $el = $('#pingou-reco');
            var template = '\
            <ul class="plist">\
            {for item in data}\
            <li class="fore${Number(item_index)+1}">\
                <div class="p-img ac">\
                    <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">\
                        <img height="180" width="180" alt="${item.t}" src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s180x180_${item.img}">\
                    </a>\
                </div>\
                <div class="p-name"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
                <div class="p-price"><strong class="J-p-${item.sku}">￥${item.jp}</strong></div>\
            </li>\
            {/for}\
            </ul>';

            if (r && r.data && r.data.length) {
                $el.show()
                    .find('.mc').html(template.process(r));
            } else {
                $el.hide();
            }
        },

        /**
         * 设置“预售说明”
         * @param {jQuery} $mount
         * @param {Number} type
         */
        setSaleSpecification: function ($mount, type) {
            if ($mount && type) {
                if (this.cfg.apollPresaleSwitch) {
                    var host = '//api.m.jd.com'
                    if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
                    $.ajax({
                        url: host + '/cms/presale',
                        data: {
                            type: type,
                            appid: 'item-v3',
                            functionId: "pc_cms_presale"
                        },
                        dataType: 'jsonp',
                        scriptCharset: 'gb18030'
                    })
                    .done(function(res) {
                        if (res.success && res.obj) {
                            var content = res.obj.content;
                            if (typeof content === 'string') {
                                content = '<li>' + content.split(/[\n\r]+/gm).join('</li><li>') + '</li>';
                                $mount.html(content);
                            } else {
                                $mount.html('');
                            }
                        }
                    })
                    .fail(function(err) {
                        console && console.log(err);
                    }); 
                }
                
            }
        },

        /**
         * 屏蔽苹果预售商品的数量
         */
        blockPresaleQuantity: function() {
            var arr = [
                100000287133, 100000177748, 100000177750, 100000177734, 
                100000177762, 100000177696, 100000287111, 100000287109, 
                100000177698, 100000287145, 100000177764, 100000287147,
                100000287117, 100000287113, 100000177784, 100000177738,
                100000287135, 100000177740, 100000177756, 100000177758,
                100000177786, 100000287121, 100000177788, 100000177766,
                100000287115, 100000177760, 100000287141, 100000177782,
                100000177770, 100000287163, 100000177826, 100000177772,
                100000177774, 100000177802, 100000177776, 100000287165,
                3127829
            ];
            if ($.inArray(this.cfg.skuid, arr) !== -1) {
                $('.J-item-1', this.$banner).remove();
            }
        }
    };
    // 适配屏幕宽度 
    function adaptToScreenWidth() {
        if ($('#yushourule .ddnew').height() > 21 && $('#yushourule .ddnew br').length == 0) { //mac 单行 21 多行 31
            $('.step1').css({fontSize: '12px'}).after('<br>')
            $('.step2').css({fontSize: '12px'}).after('<br>')
        } else {
            $('#yushourule .ddnew br').remove();
            $('.step1').css({fontSize: '14px'})
            $('.step2').css({fontSize: '14px'})
        }
    }
    function init(cfg) {
        PinGou.init(cfg);
        $(window).resize(function() {
            pageConfig.product && pageConfig.product.yuShouButtonTextSwitch && adaptToScreenWidth();
        });
    }

    module.exports.init = init;
    module.exports.__id = 'PinGou';
});
