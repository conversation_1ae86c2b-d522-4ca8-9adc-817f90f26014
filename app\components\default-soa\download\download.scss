@import "./__sprite";
.product-intro{
    .download-hover {
        .download-con {
            display: block;
            padding: 0 20px 20px;
            right:10px;
            left:-210px;
            border: 1px solid #ccc;
        }
        .download-trigger {
            display: none;
        }
    }
}
.download{
    background:#fff;
    position:absolute;
    *z-index: 6;
    right: 0;
    top:-10px;
    .download-trigger{
        width:23px;
        height: 400px;
        text-align: center;
        .sprite-extra{
            display: block;
            overflow: hidden;
            margin: 0 0 5px 6px;
            @include sprite-extra;
        }
        a{
            display: block;
            padding-top: 140px;
            &:hover{
                color: #666;
            }
        }
    }
    .download-con{
        display: none;
        width: 210px;
        position: absolute;
        top:0;
        left:24px;
        right:-210px;
        z-index:2;
        background: #fff;
    }
    .download-tit{
        position: relative;
        line-height: 50px;
        text-align: center;
        span{
            position: absolute;
            z-index: 1;
            left: 0;
            right: 0;
            top:25px;
            height: 1px;
            background: #f2f2f2;
        }
        h3{
            position: relative;
            z-index: 2;
            display: inline-block;
            *display: inline;
            *zoom:1;
            background: #fff;
            padding: 0 25px;
            font-size: 12px;
            color: #999;
            font-weight: normal;
        }
    }
    .download-qrcode-tit h3 {
        color: #E4393C;
    }
    .device-list{
        margin-bottom: 10px;
        overflow: hidden;
        zoom: 1;
        li{
            float: left;
            width: 20%;
            text-align: center;
        }
        .sprite-jdRead{
            display: inline-block;
            @include sprite-jdRead;
        }
        .sprite-pc{
            display: inline-block;
            @include sprite-pc;
            margin-bottom: 2px;
        }
        .sprite-iphone{
            display: inline-block;
            @include sprite-iphone;
        }
        .sprite-ipad{
            display: inline-block;
            @include sprite-ipad;
        }
        .sprite-android{
            display: inline-block;
            @include sprite-android;
        }
        p{
            padding-top: 5px;
            color: #999;
        }
    }
    .qr-code{
        padding: 30px 0 10px;
        text-align: center;
        border: 1px solid #e4e4e4;
        p{
            font-size: 14px;
            color: #666;
            line-height: 20px;
            font-family: "Microsoft YaHei";
            margin: 10px 24px 0;
            white-space: nowrap;
        }
    }
}
.root61{
    .download{
        width:210px;
        position: static;
        float: right;
        right:auto;
        .extra-trigger{
            display: none;
        }
        .download-con{
            display: block;
            right:auto;
            left: auto;
            border: none;
            padding: 0;
        }
    }
}
