define('MOD_ROOT/jdservicePlus/jdservicePlus', function(require, exports, module) {
    require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var tools = require('MOD_ROOT/common/tools/tools');
    var buybtn = require('MOD_ROOT/buybtn/buybtn').addToCartBtn;
    var PREFIX_REG = /^(?:https?:)?(?:\/\/)?/;  // 用于替换链接前缀为`//`
    var globalConditionTrigger = tools.ConditionTrigger.globalConditionTrigger;
    var PROTOCOL_REGEXP = new RegExp('^' + location.protocol);

    var template ='\
        <div class="dt">京东服务</div>\
        <div class="dd">\
            <div class="service-type-yb-puls clearfix">\
                {for item in data}\
                    <div class="yb-item-cat">\
                        <div class="yb-item">\
                            <span class="name">${item.products[0].serviceSkuShortName}</span>\
                            <span class="name-tip"></span>\
                            <div class="after"></div>\
                        </div>\
                        <div class="more-jd-item">\
                        {for list in item.products}\
                        <div data-sku="${list.serviceSku}" class="more-item">\
                            <ul>\
                                <li data-sku="${list.serviceSku}">\
                                    <div class="title" clstag="shangpin|keycount|product|jingdongfuwujia_${list.serviceSku}">\
                                        <span class="choose-btn" clstag="shangpin|keycount|product|jingdongfuwujia_${list.serviceSku}">\
                                        <span class="name">${list.serviceSkuShortName}</span><span  class="name-tip" style="display: none">${list.tip}</span>{if (pageConfig.product.venderId == 1000000127 && list.sortName == "换修无忧")}<span class="price">￥${list.serviceSkuPrice}/月</span>{else}<span class="price">￥${list.serviceSkuPrice}</span>{/if}\
                                        </span>\
                                    </div>\
                                </li>\
                            </ul>\
                        </div>\
                        {/for}\
                        </div>\
                    </div>\
                {/for}\
            </div>\
            <div id="btn-open-div" class="btn-open-div" >\
                        <span id="btn-open" class="btn-open">\
                        展开全部\
                        </span>\
                        <img class="open-opens" style="margin-right: 8px" width="10" height="10" src="//img11.360buyimg.com/imagetools/jfs/t1/264116/11/14470/583/6790c6b0F446ef23c/4ae3a1af1d4ac2d4.png" />\
                        </div>\
                <div class="service-tips hide">\
        </div>';

    //////////  判断购买按钮的链接是否带有请求参数 //////////////
    function hasQueryParams(url, G) {  // 非通用函数
        // 如果url缺少协议部分填充协议，因为`serializeURL`方法序列化的是一个严格的url。
        if (!PROTOCOL_REGEXP.test(url)) {
            url = location.protocol + url
        }
        try {
            var urlDict = G.serializeURL(url);
            var query = urlDict.query;
            for (var k in query) {
                if (query.hasOwnProperty(k)) {
                    return true;
                }
            }
            return false;
        } catch (error) {
            console && console.log(error);
            return false;
        }
    }

    var isBuyUrlHasQueryParams = hasQueryParams(G.originBuyUrl, G);
    ////////////////////////////////////////////////////////////

    function modifyBuyUrlParam(key, value, G) {
        // var href = buybtn.$el.attr('href');
        var reservationBtn=$('#btn-reservation,#btn-reservation-mini')
        var href = buybtn.$el.length?buybtn.$el.attr('href'):reservationBtn.attr('href');
        var hasProtocol = PROTOCOL_REGEXP.test(href);
        var dict = {
            query: {}
        };

        if (!hasProtocol) {
            href = location.protocol + href;
        }

        if (G.is('', value)) {
            dict.query[key] = value;
        } else if (G.is([], value)) {
            dict.query[key] = value.join(',');
        }

        try {
            href = G.modifyURL(href, dict, true);
        } catch (error) {
            console && console.log(error);
        }

        if (!hasProtocol) {
            href = href.replace(PROTOCOL_REGEXP, '');
        }

        if (isBuyUrlHasQueryParams) {
            buybtn.enabled(href);
        }
        if(reservationBtn.length){
            reservationBtn.attr('href', href);
        }
        pageConfig.product.cartBuyUrlParam = href // 全局购物车链接参数
    }

    var JdService = {
        init: function(opts, cfg) {
            this.sku = opts.sku;
            this.cat = opts.cat;
            this.brand = opts.brand;
            this.$el   = opts.$el || $('#choose-service\\+');
            // this.callback = opts.callback || function(){};
            this.onSelected = opts.onSelected || function(){};  // 每次点击后的回调函数，返回(已选中的sku数组, 当前选中的sku, 当前点击元素(jQuery)对象)
            this.cfg = cfg;
            this.skuPrice = '';
            this.currSku = null;
            //this.bindServices();
            //this.get();
            this.bindEvent();
        },
        bindEvent: function() {
            var _this = this;

            // this.$el
            //     .undelegate('click')
            //     .delegate('.service__head', 'click', function() {
            //         var $this = $(this),
            //             $ybItemCat = $this.parents('.service')
            //         if ($ybItemCat.hasClass('selected')) {
            //             //已选中
            //             $ybItemCat
            //                 .find('li.selected .choose-btn')
            //                 .trigger('click')
            //             _this.updateFrist(
            //                 $ybItemCat.find('.service__body li:eq(0)'),
            //                 $ybItemCat.find('.service__head')
            //             )
            //         } else {
            //             //没选中
            //             $ybItemCat
            //                 .find('.service__body li:eq(0) .choose-btn')
            //                 .trigger('click')
            //         }
            //     })

            this.$el.delegate('.more-item', 'click', function() {
                // var $this = $(this);
                // var $thisLi = $this.parents('li');
                // var sku = $thisLi.attr('data-sku');
                // var $catEl = $this.parents('.service');
                // var $serviceHead = $catEl.find('.service__head');
                // var data   = _this.$el.data('data')[$catEl.index()];
                //
                // if ($thisLi.hasClass('selected')) {
                //     $thisLi.removeClass('selected');
                //     $catEl.removeClass('selected hover');
                //     $serviceHead.removeAttr('data-sku');
                //     // $serviceHead.find('.name').html(data.scName);
                //     // $serviceHead.find('.price').html('');
                // } else {
                //     $catEl.find('li').removeClass('selected');
                //     $thisLi.addClass('selected');
                //     $catEl.addClass('selected').removeClass('hover');
                //     _this.updateFrist($thisLi, $catEl.find('.service__head'));
                // }
                //
                // _this.currSku = sku;
                // _this.currEl = $this;
                // _this.calResult();
                // return false;
                tools.ClickMoreItem($(this),_this)
            });

            // this.$el.delegate('.more-item','mouseover', function() {
            //     // tooltip.style.visibility = 'visible';
            //     tools.mouseTip($(this),'mouseover',$(this).find('.name-tip').attr('tip-name'))
            //     // $(this).find('.title').attr('title','')
            // });
            //
            // this.$el.delegate('.more-item','mouseout', function() {
            //     // tooltip.style.visibility = 'visible';
            //     // console.log('到我这了777')
            //     tools.mouseTip($(this),'mouseout',$(this).find('.name-tip').attr('tip-name'))
            //     // $(this).find('.title').attr('title','')
            // });

            this.$el.delegate('.service', {
                mouseenter: function() {$(this).addClass('hover');},
                mouseleave: function() {$(this).removeClass('hover');}
            });

            var timeoutId = 0;
            this.$el.delegate('.service-tips', 'mouseenter', function() {
                clearTimeout(timeoutId);
                $(this).addClass('hover');
            });

            this.$el.delegate('.service-tips', 'mouseleave', function() {
                var $this = $(this);
                timeoutId = setTimeout(function() {
                    $this.removeClass('hover');
                }, 300);
            });

            // 京东服务+选择性屏蔽“一键购”和“白条分期”
            globalConditionTrigger
                .addReceiver('JDService', function (data) {
                    var TIPS_TEXT = '京东服务不支持一键打白条';
                    var __html= '<span id="J_JDSTips" style="color:#999; white-space: nowrap;">{0}</span>';
                    setTimeout(function(){
                        if (_this.cfg.baiTiaoFenQi) {  // 选中京东服务时的文本提示
                            var $baitiaoText = $('.J-baitiao-text');
                            var $JDSTips = $baitiaoText.next('#J_JDSTips');
                            if ($JDSTips.length > 0) {
                                if ($baitiaoText.find('em').length) {
                                    $JDSTips.html('，' + TIPS_TEXT);
                                } else {
                                    $JDSTips.html(TIPS_TEXT);
                                }
                            } else {
                                if ($baitiaoText.find('em').length) {
                                    $baitiaoText.after(__html.format('，' + TIPS_TEXT));
                                } else {
                                    $baitiaoText.after(__html.format(TIPS_TEXT));
                                }
                            }
                        }
                    }, 0);

                }, function (data) {
                    if (_this.cfg.baiTiaoFenQi) {
                        var $baitiaoText = $('.J-baitiao-text');
                        var $JDSTips = $baitiaoText.next('#J_JDSTips');
                        if ($JDSTips.length > 0) {  // 移除文本提示
                            $JDSTips.remove();
                        }
                    }
                }).addAction({
                success: function () {
                    if (_this.cfg.baiTiaoFenQi) {  // 屏蔽“白条分期”
                        _this.cfg.baiTiaoFenQi.disabled();
                        tools.showTradeUrl(_this) // 隐藏白条按钮需要看看立即展示逻辑
                    }
                },
                failure: function () {
                    if (_this.cfg.baiTiaoFenQi) {  // 启用“白条分期”
                        _this.cfg.baiTiaoFenQi.enabled();
                    }
                }
            });

            // 加载“京东服务”模块
            function loadJdService() {
                // 重置条件集状态
                globalConditionTrigger.emit('JDService', false);
                // 重置购物车链接上的“增值保障”参数
                modifyBuyUrlParam('jdhsid', '', G);
                if (_this.cfg.havestock && !_this.cfg.isHeYue) {
                    _this.get();
                } else {
                    _this.$el.hide().html();
                }
            }

            // 区域位置发生变化重新load
            Event.addListener('onAreaChange', function (data) {
                _this.stockData = data.stock
                loadJdService();
            });


            Event.addListener('onHeYueReady', function () {
                loadJdService();
            });

            // 数量变化时触发的stock接口，只根据库存状态展示或隐藏“增值保障”模块
            Event.addListener('onStockReady', function(r) {
                var stockData = r && r.stock && r.stock.data
                var jdSerPlusInfo = stockData && stockData.warrantyInfo && stockData.warrantyInfo.jdSerPlusInfo
                if (_this.cfg.havestock && !_this.cfg.isHeYue &&  (typeof jdSerPlusInfo != "undefined")) {
                    _this.$el.show();
                } else {
                    _this.$el.hide();
                }
            });

            // "白条分期"UI渲染完成后根据京东服务的状态屏蔽或启用“白条分期”
            Event.addListener('onBaiTiaoRender', function (data) {
                if (_this.cfg.havestock && !_this.cfg.isHeYue) {
                    globalConditionTrigger.callReceiverAction('JDService')
                    globalConditionTrigger.callActions();
                }
            });
        },
        get: function() {
            var stockData = this.stockData.data;
            var jdSerPlusInfo=stockData && stockData.warrantyInfo && stockData.warrantyInfo.jdSerPlusInfo
            this.set(jdSerPlusInfo);
            // Event.addListener('onWareBusinessReady', function(res) {
            //     console.log("res",res)
            //     _this.set(res.data.warrantyInfo.jdSerPlusInfo);
            // });
        },
        set: function(res) {
            var plus_data = res && res.serviceItems;
            this.$el.hide().html('');
            for (var m in plus_data) {
                var items = plus_data[m];
                //if (items.type === "2") {
                if ($.isArray(plus_data) && plus_data.length > 0) {
                    // 数组长度限制，最多展示3类，每类5个
                    var data = plus_data.slice(0, 3);
                    for (var i = 0, l = data.length; i < l; i+=1) {
                        var serviceSkus = data[i].products;
                        if (serviceSkus) {
                            serviceSkus = serviceSkus.slice(0, 5);
                        }
                        // data[i].imgUrl = data[i].imgUrl.replace(PREFIX_REG, '//');
                    }

                    this.$el.html(template.process({data: plus_data})).data('data', plus_data);
                    this.show();

                } else {
                    this.$el.hide().html('');
                }
                //}
            }
            var arr = []
            if ($('#choose-service\\+ .yb-item-cat').length > 3) {
                $('#choose-service\\+ .yb-item-cat').map(function(index, element) {
                    return arr.push(element)
                }).get()
            }
            if ( arr&&arr.length>3) {
                $('#choose-service\\+ .service-type-yb-puls').css({'height': $(arr[3])[0].offsetTop,'overflow': 'hidden'});
            }else {
                $('#choose-service\\+ .btn-open').css('display', 'none')
                $('#choose-service\\+ .open-opens').css('display','none')
            }
            tools.ServiceOpen($('#choose-service\\+ .service-type-yb-puls'),arr)
        },
        show: function() {
            if (this.cfg.havestock && !this.cfg.isHeYue) {
                this.$el.show();
            }
        },
        hide: function() {
            this.$el.hide();
        },
        clear: function() {
            this.$el.find('.yb-item').removeAttr('data-sku');
            this.$el.find('.yb-item-cat').removeClass('selected');
            this.$el.find('.yb-item-cat .more-item li').removeClass('selected');
        },
        calResult: function() {
            var selectedItems = this.$el.find('.selected');
            var skus = [];

            selectedItems.each(function() {
                var child = $(this).find('.yb-item');
                var sku = child.attr('data-sku');

                if (sku) {
                    skus.push(sku);
                }
            });

            if (typeof this.onSelected === 'function') {
                return this.onSelected.apply(this, [
                    skus, // 已选中的sku数组
                    this.currSku, // 当前选中的sku
                    this.currEl // 当前点击(jQuery)对象
                ]);
            }
        },
        setSkuPrice: function (stock) {
            if (stock && stock.jdPrice) {
                this.skuPrice = stock.jdPrice.p;
            } else {
                this.skuPrice = '';
            }
        }
    };

    function init(cfg) {
        var $service = $('#choose-service\\+');

        if ($service.length < 1) { return; }

        JdService.init({
            $el: $service,
            sku: cfg.skuid,
            cat: cfg.cat,
            brand: cfg.brand,
            onSelected: function(skus) {
                if (skus.length > 0) {
                    pageConfig.hasJDService = true;
                    globalConditionTrigger.emit('JDService', true);
                } else {
                    pageConfig.hasJDService = false;
                    globalConditionTrigger.emit('JDService', false);
                }
                modifyBuyUrlParam('jdhsid', skus, G);
                Event.fire({type: 'onServicePlusSelected', skus: skus});
            }
        },cfg);
    }

    module.exports.__id = 'jdservicePlus';
    module.exports.init = init;
});
