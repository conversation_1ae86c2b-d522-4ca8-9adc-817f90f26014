@import '../common/lib.scss';
@import './_sprite'; 

.choose-car {
    margin-bottom: 10px;
    display: none;
    .dt{
        height: 35px; // 选择车型
        line-height: 35px;
    }
    .icon-car,
    .icon-present {
        @include inline-block;
        vertical-align: middle;
    }
    .icon-car {
        @include sprite-car;
    }
    .icon-present {
        @include sprite-present;
        margin-left: 5px;
        display: none;
    }
    .reminder {line-height: normal}
    .guide {
        width: 100px;
        height: 34px;
        line-height: 34px;
        border: 0.5px solid rgba(0, 0, 0, 0.02);
        @include border-radius(4px);
        background: rgba(247, 248, 252, 1);
        vertical-align: middle;
        cursor: pointer;
        color: #1A1A1A;
        display: flex;
        justify-content: center;
        align-items: center;
        &:hover {
            background: rgba(255, 235, 241, 1);
            color: rgba(255, 15, 35, 1);
            border: 0.5px solid rgba(255, 15, 35, 1)
        }
    }
    .guide-text {
        margin-left: 4px;
        font-size: 15px;
        
    }
    .vehicles {
        // margin-bottom: -10px;
        font-size: 0;
        position: relative;
    }
    .vehicle {
        @include inline-block;
        height: 34px;
        line-height: 34px;
        padding: 0 12px;
        border: 0.5px solid rgba(0, 0, 0, 0.02);
        margin: 0 10px 10px 0;
        cursor: pointer;
        font-size: 15px;
        color: #1A1A1A;
        vertical-align: middle;
        background: rgba(247, 248, 252, 1);
        border-radius: 4px;
        &:hover {
            border: 0.5px solid #FF0F23;
            color: #FF0F23;
            background: #FFEBF1;
            border-radius: 4px;
        }
        .vehicle-logo {
            // @include inline-block;
            display: none;
            width: 32px;
            height: 32px;
            margin-right: 3px;
            vertical-align: middle;
        }
        .vehicle-inner {
            @include inline-block;
            // width: 110px;
            vertical-align: middle;
        }
        .vehicle-name ,
        .vehicle-model {
            overflow: hidden;
            line-height: 16px;
            white-space: nowrap;
            word-break: keep-all;
            text-overflow: ellipsis;
        }
        .vehicle-name {
        }
        .vehicle-model {
            display: none;
            color: #9e9e9e;
        }
        &.selected {
            border: 1px solid #FF0F23;
            color: #FF0F23;
            background: #FFEBF1;
        }
    }
    .other {
        font-size: 15px;
        display: inline-block;
        width: 30px;
        color: #505259;
        padding-right: 12px;
        background: url(https://img13.360buyimg.com/imagetools/jfs/t1/259181/16/14667/489/6790bd3aF60de783b/8ed74ff7b6ef4ed6.png) no-repeat;
        background-size: 8px;
        background-position: right center;
        // font-size: 12px;
        
        &:hover{
            color: #FF0F23;
        }
    }
}