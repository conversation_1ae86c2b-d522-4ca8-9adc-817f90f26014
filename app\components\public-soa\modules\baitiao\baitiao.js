define('PUBLIC_ROOT/modules/baitiao/baitiao', function(require, exports, module) {
    var G = require('PUBLIC_ROOT/modules/common/core')
    var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event
    var login = require('JDF_UNIT/login/1.0.0/login')
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools')


    require('PUBLIC_ROOT/modules/ETooltips/ETooltips')
    require('JDF_UNIT/trimPath/1.0.0/trimPath')

    var BaiTiao = function(opts, onSelected) {
        this.$el = opts.$el || $('#choose-baitiao')
        this.$btn = opts.$btn || $('#btn-baitiao')
        this.price = opts.price

        this.$jc = $('#choose-jincai')
        this.$jcBtn = $('#btn-jincai')

        this.cfg = opts.cfg

        this.onSelected = onSelected || function() {}

        this.sku = opts.sku
        this.cat = opts.cat
        this.shopId = opts.cfg.shopId

        this.enable = false
        this.isAva = null

        // 落地配 id
        this.did = ''

        this.disabledBT =
            G.onAttr('isXnzt') ||
            // G.onAttr('YuShou') ||
            this.cfg.isYuShou ||
            this.cfg.isBiGouMa ||
            this.cfg.isKO

        //this.URL = '//show.baitiao.jd.com/queryBtPlanInfo.do';
        this.URL = '//btshow.jd.com/queryBtPlanInfo.do'
        this.JSONP_CALLBACK_NAME = 'queryBtPlanInfo'

        window.queryBtPlanInfo = function() {}

        this.hideJCItem()
        this.hideJCBtn()

        this.init()
    }

    BaiTiao.TEMPLATE =
        '\
    <div class="dt">白条分期</div>\
    <div class="dd">\
        <div class="baitiao-list J-baitiao-list">\
            {for item in planInfos}\
            <div class="item disabled" \
                clstag="shangpin|keycount|product|baitiaofenqi_${item.plan}_${pageConfig.product.cat.join(\'_\')}" \
                data-snum="${item.plan}">\
                <b></b>\
                <a href="#none">\
                    <strong>\
                    {if item.plan===1} \
                        不分期 0服务费\
                    {else}\
                        ￥${item.curTotal}${(item.text ? item.text : "")}&times;${item.plan}期\
                    {/if}\
                    </strong>\
                    <span style="display:none;">\
                        {if item.isDiscount}<em>惠</em>{/if} \
                        {if item.fee>0}含{else}0{/if}服务费\
                    </span>\
                </a>\
                <div class="baitiao-tips hide">\
                    <ul>\
                        <li>\
                        {if item.fee>0}\
                            含服务费：费率${item.rate}%${(item.text ? item.text : "")}，￥${item.planFee}${(item.text ? item.text : "")}&times;${item.plan}期\
                        {else}\
                            无服务费\
                        {/if}\
                        </li>\
                    </ul>\
                </div>\
            </div>\
            {/for}\
            {if isDiscountAll}\
            <div class="bt-info-tips">\
                <a class="J-icon-hui prom icon fl" href="#none">　</a>\
            </div>\
            {/if}\
            <div class="bt-info-tips">\
                <a class="J-bt-tips question icon fl" href="#none">　</a>\
            </div>\
        </div>\
        <div class="baitiao-text J-baitiao-text"></div>\
    </div>'

    BaiTiao.prototype = {
        init: function() {
            this.bindEvent()
            this.get()
        },
        bindEvent: function() {
            var _this = this;
            //地址变化price更新后 会重复添加事件，此次undelegate
            this.$el.undelegate('.item', 'click');
            this.$el.delegate('.item', 'click', function() {
                var selected = $(this).hasClass('selected')
                var disabled = $(this).hasClass('disabled')

                if (!disabled) {
                    _this.$el.find('.item').removeClass('selected')

                    if (selected) {
                        $(this).removeClass('selected')
                        $("#InitTradeUrl").show()
                    } else {
                        $(this).addClass('selected')
                        $("#InitTradeUrl").hide()
                    }

                    _this.select($(this), !selected)
                }
            })
            this.$el.delegate('.J-login', 'click', function() {
                _this.loginIframe()
            })

            this.$el.delegate('.item', 'mouseenter', function() {
                $(this).addClass('hover')
            })
            this.$el.delegate('.item', 'mouseleave', function() {
                $(this).removeClass('hover')
            })

            this.$jc.undelegate('click').delegate('.J-jincai-list .item', 'click', $.proxy(this.handleJC, this))

            Event.addListener('onStockReady', function() {
                _this.showItem()
            })
            Event.addListener('onNumChange', function() {
                if (!_this.disabledBT) {
                    _this.get()
                }
            })
            Event.addListener('onGiftSelected', function() {
                if (!_this.disabledBT) {
                    _this.setBTLink()
                }
            })
            Event.addListener('onLDPSelected', function(data) {
                _this.did = data.did
                if (!_this.disabledBT) {
                    _this.setBTLink()
                }
            })
            Event.addListener('onHeYueReady', function() {
                _this.showItem()
            })

            Event.addListener('onStockReady', function () {
                if (_this.isHWAdd()) {
                    _this.$jcBtn.hide()
                    _this.hideJCItem()
                }
            })
        },
        isHWAdd: function () {
            var ids = Tools.getAreaId().areaIds
            return ids[0] === 53283 || ids[0] === 52993 || ids[0] === 32
        },
        log: function(msg) {
            if (typeof errortracker !== 'undefined') {
                errortracker.log({ filename: 'reservation.js', message: msg })
            }
        },
        loginIframe: function() {
            login({
                modal: true,
                complete: function() {
                    window.location.reload(true)
                }
            })
        },
        getNum: function() {
            var amount = $('#buy-num').val()
            var num = Number(amount)
            return isNaN(num) ? 1 : num
        },
        get: function(params) {
            var _this = this
            var num = this.getNum()
            var totalPrice = this.price.p * Number(num)
            var qs = $.extend({}, params, {
                sku: this.sku,
                cId: this.cat.join(','),
                num: this.getNum(),
                amount: totalPrice,
                sourceType: 'PC-XQ',
                shopId: pageConfig.product.venderId,
                ver: 1,
                areaId: Tools.getAreaId().areaIds[0],
                isJd: G.isPop
            })

            $.ajax({
                url: this.URL,
                dataType: 'jsonp',
                data: qs,
                scriptCharset: 'utf-8',
                jsonpCallback: this.JSONP_CALLBACK_NAME,
                timeout: 2000,
                error: function() {
                    if (typeof console !== 'undefined') {
                        console.error('Baitiao service error with timeout.')
                    }
                    G.log(
                        null,
                        'baitiao.js',
                        'Baitiao service error with timeout.'
                    )
                },
                success: function(r) {
                    if (/debug=bt/.test(location.href)) {
                        r.isDiscountAll = true
                        r.marketingText = '测试文字'
                    }
                    if (r && r.result && r.result.isSuccess) {
                        _this.set(r)
                    } else {
                        G.log(null, 'baitiao.js', 'Baitiao service error.')
                    }
                }
            })
        },
        destory: function () {
            this.hide()
            this.hideBtn()
            this.$el.html('')
        },
        set: function(data) {
            // 支持打白条时显示白条分期
            if (pageConfig.hasCarGift) { // 如果有车管家赠品，屏蔽白条，因为结算页中不能识别车管家赠品
                return;
            }
            if (data.planInfos && data.planInfos.length) {
                this.key = data.key
                this.isAva = data.isAva

                // 商品不支持打白条
                if (!data.isAva) {
                    this.destory()
                    return false
                }

                this.$el.html(BaiTiao.TEMPLATE.process(data))
                this.showItem()

                this.hideBtn()
                this.enabled()

                // this.setTips(data)
            }
            this.setBTLink()
        },
        // setTips: function(data) {
        //     var content =
        //         '\
        //     <div id="J-bt-tips">\
        //         <div class="g-tips-inner">\
        //             <i></i><em></em>\
        //             <ul>\
        //                 <li>1、白条的实际分期金额、分期服务费、可用额度及分期优惠以收银台展示为准。</li>\
        //                 <li>2、什么是白条分期？<br />\
        //                 白条是一种“先消费，后付款”的支付方式，使用白条可以享受先用后付，以及最长36期的分期还款。</li>\
        //             </ul>\
        //         </div>\
        //     </div>'

        //     if (this.$el.find('.J-bt-tips').length) {
        //         this.$el.find('.J-bt-tips').show().ETooltips({
        //             close: false,
        //             content: content,
        //             width: 300,
        //             pos: 'bottom',
        //             zIndex: 10
        //         })
        //     }

        //     if (this.$el.find('.J-icon-hui').length) {
        //         this.$el.find('.J-icon-hui').show().ETooltips({
        //             close: false,
        //             content: data.marketingText,
        //             pos: 'bottom',
        //             width: 200,
        //             zIndex: 10
        //         })
        //     }
        // },
        disabled: function() {
            this.$el.find('.item').addClass('disabled').removeClass('selected')

            this.showTips('yb')
            this.hideBtn()
        },
        enabled: function() {
            this.enable = true
            // 防止延保服务切换的时候调用直接设置禁用状态
            if (this.enable) {
                this.$el.find('.item').removeClass('disabled')
            }
            this.showTips('none')
        },
        select: function($ele, selected) {
            var snum = $ele.attr('data-snum')

            this.hasSelectedItem = !selected

            if (selected) {
                this.snum = snum
                this.showBtn()
                this.clearYbService()
            } else {
                this.snum = null
                this.hideBtn()
            }
            //this.showTips('note');

            this.setBTLink()

            Event.fire({
                type: 'onBaiTiaoSelect',
                isSelect: selected
            })
            this.onSelected(selected)
        },
        clearYbService: function() {
            $('#choose-service .item').each(function() {
                $(this).removeClass('selected')
            })
        },
        // 和白条逻辑排斥的关系
        isDisabledToShow: function(p) {
            return (
                p.isHeYue ||
                p.isYuShou ||
                p.isBiGouMa ||
                !p.havestock ||
                (p.__chooseShop && p.__chooseShop.selected) ||
                this.isHWAdd() ||
                p.isJinCai ||
                pageConfig.hasCarGift
            )
        },
        showItem: function() {
            if (!this.isDisabledToShow(this.cfg)) {
                this.show()
                // if (this.isAva) {
                //     this.getJinCai()
                // }
            } else {
                this.hide()
            }
        },
        // getJinCai: function () {
        //     if (!this.$jc.length) return
        //     $.ajax({
        //         url: host + '/userLevel/info',
        //         dataType: 'jsonp',
        //         data: {
        //             appid: 'item-v3',
        //             functionId: "pc_userLevel_info" 
        //         },
        //         success: $.proxy(this.setJinCai, this)
        //     })
        // },
        // setJinCai: function (r) {
        //     var isJC = this.cfg.isJinCai = r && r.userLevel && r.userLevel === 90
        //     if (isJC) {
        //         this.$jc.show()
        //         this.$jc.find('.J-bt-tips').ETooltips({
        //             close: false,
        //             content: '<strong>什么是企业金釆？</strong>企业金釆是为优质企业客户推出的一款“先采购、后付款”的信用支付产品。企业金釆客户可享受“周结21天免息”或“月结低息分期”的延期付款体验。',
        //             width: 300,
        //             pos: 'bottom',
        //             zIndex: 10
        //         })
        //         this.hide()
        //     } else {
        //         this.hideJCItem()
        //     }
        // },
        hideJCItem: function () {
            this.$jc.hide()
            this.$jc.find('.item').removeClass('selected')
        },
        showJCBtn: function () {
            this.$jcBtn.show()
        },
        handleJC: function (e) {
            var $this = $(e.currentTarget)
            $this.toggleClass('selected')

            var isSelected = $this.hasClass('selected')
            this.cfg.isJinCaiSelected = isSelected

            if (isSelected) {
                this.showJCBtn()
                $("#InitTradeUrl").hide()
            } else {
                this.hideJCBtn()
                $("#InitTradeUrl").show()
            }
        },
        hideJCBtn: function () {
            this.$jcBtn.hide()
        },
        show: function() {
            this.$el.show()
        },
        hide: function() {
            this.$el.hide()
        },
        hideBtn: function() {
            this.$btn.hide()
        },
        showBtn: function() {
            var chooseShopShowIndex = $('#choose-shop-show .selected').attr('data-index');
            if (!this.isDisabledToShow(this.cfg) && (chooseShopShowIndex != '0')) {
                this.$btn.show()
            }
            if(chooseShopShowIndex == 0){
                pageConfig.product.__chooseShop.showShopTips()
            }
        },
        setBTLink: function() {
            var url = '//bttrade.jd.com/shopping/order/getOrderInfo.action?'
            var gidStr = pageConfig.giftSelectedSkuids
                ? pageConfig.giftSelectedSkuids.join(',')
                : ''

            var params = {
                pid: this.sku,
                cid: this.cat[2],
                num: this.getNum(),
                snum: this.snum,
                key: this.key,
                gids: gidStr,
                did: this.did
            }

            // 给延保判断使用
            //pageConfig.isDBT = !!this.snum;

            if (this.snum) {
                this.$btn.attr('href', url + $.param(params))
            } else {
                this.$btn.attr('href', '#none')
            }
        },
        showTips: function(type) {
            var btNote =
                '<a clstag="shangpin|keycount|product|fenqijieshao" href="//help.jr.jd.com/show/helpcenter/148.html" target="_blank">什么是白条分期？</a>'
            var ybTip = pageConfig.hasYbService ? '<em>增值保障不支持一键打白条 </em>' : ''
            var message = {
                yb: ybTip,
                none: ''
                //note     : btNote + ybTip + '<em>实际分期金额及手续费以白条剩余额度及收银台优惠为准</em>',
                //login    : '<a href="#none" class="J-login">登录</a> <em>后确认是否享有白条服务 </em>' + btNote,
                //overLimit: '<em>您的额度不足，暂无法使用。</em> <a clstag="shangpin|keycount|product|zengjiaedu" href="//help.jr.jd.com/show/helpcenter/148.html" target="_blank">如何增加额度？</a>',
                //active   : '<a href="//baitiao.jd.com/v3/activity/open" target="_blank">激活</a> <em>白条享最长24期白条分期</em>' + btNote,
                //service  : '<em>暂不支持购买京东服务</em> <a href="#none">什么是白条分期？</a>'
            }

            this.$el.find('.J-baitiao-text').html(message[type])
        }
    }

    function init(cfg) {
        var $baiTiao = $('#choose-baitiao')

        if (!$baiTiao.length) return false

        Event.addListener('onPriceReady', function(data) {
            var p = data.price

            cfg.baiTiaoFenQi = new BaiTiao({
                $el: $baiTiao,
                price: p,
                sku: cfg.skuid,
                cat: cfg.cat,
                cfg: cfg
            })
        })
    }

    module.exports.__id = 'baitiao'
    module.exports.init = init
})
