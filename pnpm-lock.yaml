lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      '@babel/preset-env':
        specifier: ^7.26.9
        version: 7.26.9(@babel/core@7.26.10)
      '@jd/upload-oss-tools':
        specifier: ^1.1.23
        version: 1.1.25

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=, tarball: http://registry.m.jd.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=, tarball: http://registry.m.jd.com/@babel/code-frame/download/@babel/code-frame-7.26.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.8':
    resolution: {integrity: sha1-ghwdNWQcNVKE1KhwuKSnsMFB42c=, tarball: http://registry.m.jd.com/@babel/compat-data/download/@babel/compat-data-7.26.8.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.10':
    resolution: {integrity: sha1-XIdvg8jE3LIz7ktnDAYG8qwwAPk=, tarball: http://registry.m.jd.com/@babel/core/download/@babel/core-7.26.10.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.0':
    resolution: {integrity: sha1-dkOCtTkuW5r/k8rbGQ0HRYZsvCw=, tarball: http://registry.m.jd.com/@babel/generator/download/@babel/generator-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha1-2OrE0twNe24R+m5TUzLg0xhPBrQ=, tarball: http://registry.m.jd.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.0':
    resolution: {integrity: sha1-3gx1OxzR2atV1HPFpc9xcPCoGIA=, tarball: http://registry.m.jd.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.0':
    resolution: {integrity: sha1-UY+tajB8apb0SvFJErLCCr6b/DA=, tarball: http://registry.m.jd.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.27.0':
    resolution: {integrity: sha1-DkH304wuvgbr2c8OAvsmAZx3zZU=, tarball: http://registry.m.jd.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.27.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.4':
    resolution: {integrity: sha1-Feh0Y2i/pnF4X1km/3SzBkwpH6s=, tarball: http://registry.m.jd.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.4.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha1-nf/+RvcnAFpeopBRrINftzXkwaM=, tarball: http://registry.m.jd.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=, tarball: http://registry.m.jd.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=, tarball: http://registry.m.jd.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha1-MySuULrn4qs8M/YMmod7agFGtU4=, tarball: http://registry.m.jd.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.26.5':
    resolution: {integrity: sha1-GFgNAMmTQRetcZOSxPZYXJMzzDU=, tarball: http://registry.m.jd.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.26.5.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.25.9':
    resolution: {integrity: sha1-5TlWqz1bn7iL4Es+LzG1I6/TS5I=, tarball: http://registry.m.jd.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.26.5':
    resolution: {integrity: sha1-bLBOgq4pHa6OcjNd/kOLByXxTI0=, tarball: http://registry.m.jd.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.26.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha1-Cy4bYtVg1rGVSJP9K3BdwXyR8Mk=, tarball: http://registry.m.jd.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=, tarball: http://registry.m.jd.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=, tarball: http://registry.m.jd.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=, tarball: http://registry.m.jd.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.25.9':
    resolution: {integrity: sha1-2Z39WVMS5siUvX0jdHACXIXuqdA=, tarball: http://registry.m.jd.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.25.9.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.0':
    resolution: {integrity: sha1-U9FWCY3vqCQ+qw8y+hdYkHWhuAg=, tarball: http://registry.m.jd.com/@babel/helpers/download/@babel/helpers-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.0':
    resolution: {integrity: sha1-PX1u4mjkHSYACRy9ThRf/uhaROw=, tarball: http://registry.m.jd.com/@babel/parser/download/@babel/parser-7.27.0.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9':
    resolution: {integrity: sha1-zC5T6/CgNAd3//XtUhlD4lO02P4=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9':
    resolution: {integrity: sha1-r55PtjzLiry5I3Wy/P42tgx3TTA=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9':
    resolution: {integrity: sha1-6Nwm/NYW5sW/K9DVosFR1PkqkTc=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9':
    resolution: {integrity: sha1-gHpmf5FYrKxvYWS0vrha2evJ4dE=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9':
    resolution: {integrity: sha1-3nCT8efer2jq3XzGsH8quCVDJp4=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha1-eET5KJVG76n+usLeTP41igUL1wM=, tarball: http://registry.m.jd.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.26.0':
    resolution: {integrity: sha1-YgQSQFBY76VuSlZJA7eTVQIPRF8=, tarball: http://registry.m.jd.com/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.26.0':
    resolution: {integrity: sha1-OxQShHaZ7qc5tPJgLHTONvawsPc=, tarball: http://registry.m.jd.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=, tarball: http://registry.m.jd.com/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.25.9':
    resolution: {integrity: sha1-eCHUQQvuXaqtu0zdmmZJcE4XaEU=, tarball: http://registry.m.jd.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.26.8':
    resolution: {integrity: sha1-XjmRE147nG6q9e/1bRrloR30X/g=, tarball: http://registry.m.jd.com/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.26.8.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.25.9':
    resolution: {integrity: sha1-yAAI2srlFIJ5PlqcCLOaW+fhLXE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.26.5':
    resolution: {integrity: sha1-PcRAXTGtHL5FKTqlcgWm47AJ1T4=, tarball: http://registry.m.jd.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.26.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.27.0':
    resolution: {integrity: sha1-rMLA2Yp0ObveQkRYjdvUkEcB1H8=, tarball: http://registry.m.jd.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.27.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.25.9':
    resolution: {integrity: sha1-qM6E/tua1RJUmYQQH6hAgKn19R8=, tarball: http://registry.m.jd.com/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.26.0':
    resolution: {integrity: sha1-bI2iGfTrFcrpg07ENI/46eCWZKA=, tarball: http://registry.m.jd.com/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.25.9':
    resolution: {integrity: sha1-cVJFf3iAtZOmOt6Khh5uJqRGn1I=, tarball: http://registry.m.jd.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.25.9':
    resolution: {integrity: sha1-2zZJLHhGDlNLiFKx1b7+PJI+8Qs=, tarball: http://registry.m.jd.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.25.9':
    resolution: {integrity: sha1-lm6iWVxJgiQ0CINgLTz9egx5zqE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.25.9':
    resolution: {integrity: sha1-uteUXdB3NMpS/jrU6HK0DtCbsJo=, tarball: http://registry.m.jd.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.25.9':
    resolution: {integrity: sha1-iFDd9X3OKuu0OUu0NKdZgDEFnm0=, tarball: http://registry.m.jd.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha1-b3JZtN4SdyGgjx5RZbhS/KppbTE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.25.9':
    resolution: {integrity: sha1-I+kX3mPtI8ZgDF3QbZRmnc5597g=, tarball: http://registry.m.jd.com/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.26.3':
    resolution: {integrity: sha1-4p8Btt4wLHwseUJ3pI8Eqcp/A7w=, tarball: http://registry.m.jd.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.26.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.25.9':
    resolution: {integrity: sha1-kHRf5VBTOU9VTkBYTNqB8sikAqI=, tarball: http://registry.m.jd.com/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.26.9':
    resolution: {integrity: sha1-JyMfedUXDvM7URHwf+XK/rLJalY=, tarball: http://registry.m.jd.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.26.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.25.9':
    resolution: {integrity: sha1-k52VbmimBmYQBb/VUMT8Lvlfe5c=, tarball: http://registry.m.jd.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.25.9':
    resolution: {integrity: sha1-yG20B8uCfN7ZAqkMcH0ngaqolmA=, tarball: http://registry.m.jd.com/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.25.9':
    resolution: {integrity: sha1-GhxrTUqlm8TK1bazoiOgq9aFyd4=, tarball: http://registry.m.jd.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.25.9':
    resolution: {integrity: sha1-sZRBqMOaL9oJApALMG6gWuEFXbc=, tarball: http://registry.m.jd.com/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.25.9':
    resolution: {integrity: sha1-Y9/xl2PqZKMfXmwglX5qJeQe1d4=, tarball: http://registry.m.jd.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.25.9':
    resolution: {integrity: sha1-SbpHjyKVEBVEq9eUSGzTCI3dtsU=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.26.3':
    resolution: {integrity: sha1-jwEdRLINAsPeRNiFDZcdhJf5gfs=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.26.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.25.9':
    resolution: {integrity: sha1-i9G0ODYmnj0zMHFRoRS887pnk/g=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.25.9':
    resolution: {integrity: sha1-ZxAHnN18aU2zZSmh6EEeSfy/FMk=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha1-RUmQrmzCL9Kg+mCzosb2OjgGTmo=, tarball: http://registry.m.jd.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.25.9':
    resolution: {integrity: sha1-QuYXESlLEFwkgzbcsEt3BU6ovs0=, tarball: http://registry.m.jd.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6':
    resolution: {integrity: sha1-+/azySy1CeezGe5G49qJxb7dMf4=, tarball: http://registry.m.jd.com/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.26.6.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.25.9':
    resolution: {integrity: sha1-v+11hmJhqLZDRosMz9J18gMyFKE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.25.9':
    resolution: {integrity: sha1-AgNyUCUHQWSAi88aLPqQxlLJnxg=, tarball: http://registry.m.jd.com/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.25.9':
    resolution: {integrity: sha1-OF1d4TUWKTO+tKPSJ6K35Su0zwM=, tarball: http://registry.m.jd.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.25.9':
    resolution: {integrity: sha1-EOcNltUrsfEMXKqsWaxUXqK6f/M=, tarball: http://registry.m.jd.com/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.25.9':
    resolution: {integrity: sha1-4ULriZ0m73FUNfIBq24TlUHu590=, tarball: http://registry.m.jd.com/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.25.9':
    resolution: {integrity: sha1-uFaEIgWz534Yt6ehuUlYBpx7olc=, tarball: http://registry.m.jd.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.25.9':
    resolution: {integrity: sha1-hH9BOSY1d1JkVdfTIjzYvaUeO1c=, tarball: http://registry.m.jd.com/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.25.9':
    resolution: {integrity: sha1-nItz5k5sw8uydDYziFp90sOF/jM=, tarball: http://registry.m.jd.com/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.25.9':
    resolution: {integrity: sha1-1y1Yi9iLDeyLYuNvb9qRzt/ijj8=, tarball: http://registry.m.jd.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.27.0':
    resolution: {integrity: sha1-gi/uvvQ9almoH2lrJRLfWxaC2zE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.27.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regexp-modifiers@7.26.0':
    resolution: {integrity: sha1-L1g3pbXNOEKpGdgUfpkDzHRVuFA=, tarball: http://registry.m.jd.com/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.26.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-reserved-words@7.25.9':
    resolution: {integrity: sha1-A5iu0vHxC6P3ipPbIZsn70F/uc4=, tarball: http://registry.m.jd.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.25.9':
    resolution: {integrity: sha1-u3heYJH5n4JqlfmJT8Fv3mHBY/I=, tarball: http://registry.m.jd.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.25.9':
    resolution: {integrity: sha1-JKNRU5MbS6PRPOxKd0jCGrVRTvk=, tarball: http://registry.m.jd.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.25.9':
    resolution: {integrity: sha1-x/ArlE6YakF4F7ILosUE38FFPTI=, tarball: http://registry.m.jd.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.26.8':
    resolution: {integrity: sha1-lmsV0VOpkRcqVApprV4YRc7ZkLU=, tarball: http://registry.m.jd.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.26.8.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.27.0':
    resolution: {integrity: sha1-BEoIkPPKaUIHx4JtDHpl5awAiq4=, tarball: http://registry.m.jd.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.27.0.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.25.9':
    resolution: {integrity: sha1-p17zlHzhU2P8yqOOLdm8cLJ4i4I=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.25.9':
    resolution: {integrity: sha1-qQHpbywdBxsNG7XcDTyIDOj1PdM=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.25.9':
    resolution: {integrity: sha1-Xq50f+OerPE6i9AGpPsLXR+l6bE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.25.9':
    resolution: {integrity: sha1-ZRFMF7T/wg+lsWPGPHDA0lYh+r4=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.25.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.26.9':
    resolution: {integrity: sha1-LsZOkD0O/nQ2mfd6EL33lVwhI8M=, tarball: http://registry.m.jd.com/@babel/preset-env/download/@babel/preset-env-7.26.9.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=, tarball: http://registry.m.jd.com/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/runtime@7.27.0':
    resolution: {integrity: sha1-++58+XxwlRjswfWQmESB1UYNR2I=, tarball: http://registry.m.jd.com/@babel/runtime/download/@babel/runtime-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.0':
    resolution: {integrity: sha1-slPlQGzB3xxX3NGPEXYMLb9AwLQ=, tarball: http://registry.m.jd.com/@babel/template/download/@babel/template-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.0':
    resolution: {integrity: sha1-EdfmRHeeFmwEQvmgcnTQLNkdSnA=, tarball: http://registry.m.jd.com/@babel/traverse/download/@babel/traverse-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.0':
    resolution: {integrity: sha1-75rLawbDFz9mMtmT7LbUrkcLRVk=, tarball: http://registry.m.jd.com/@babel/types/download/@babel/types-7.27.0.tgz}
    engines: {node: '>=6.9.0'}

  '@jd/jmfe-node-jss@1.2.14':
    resolution: {integrity: sha1-aEg7LODQI+mlSH3Dd/Dobz+Ki/Y=, tarball: http://registry.m.jd.com/@jd/jmfe-node-jss/download/@jd/jmfe-node-jss-1.2.14.tgz}

  '@jd/upload-oss-tools@1.1.25':
    resolution: {integrity: sha1-6yV052THMVjaoZzGhA98GXyQMwk=, tarball: http://registry.m.jd.com/@jd/upload-oss-tools/download/@jd/upload-oss-tools-1.1.25.tgz}
    hasBin: true

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=, tarball: http://registry.m.jd.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=, tarball: http://registry.m.jd.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=, tarball: http://registry.m.jd.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=, tarball: http://registry.m.jd.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=, tarball: http://registry.m.jd.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz}

  '@types/bluebird@3.5.42':
    resolution: {integrity: sha1-fsBfHOmYbZIDE8E3elZisbVj02Y=, tarball: http://registry.m.jd.com/@types/bluebird/download/@types/bluebird-3.5.42.tgz}

  '@types/caseless@0.12.5':
    resolution: {integrity: sha1-25RoyxsbWpJbjzSCLxZp3wxUcvU=, tarball: http://registry.m.jd.com/@types/caseless/download/@types/caseless-0.12.5.tgz}

  '@types/glob@7.2.0':
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=, tarball: http://registry.m.jd.com/@types/glob/download/@types/glob-7.2.0.tgz}

  '@types/minimatch@5.1.2':
    resolution: {integrity: sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=, tarball: http://registry.m.jd.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz}

  '@types/node@14.18.63':
    resolution: {integrity: sha1-F4j6jag427X56plLg0J4IF22yis=, tarball: http://registry.m.jd.com/@types/node/download/@types/node-14.18.63.tgz}

  '@types/request-promise@4.1.51':
    resolution: {integrity: sha1-prtDKJVp3oQFUHN1fTeo/WFGv+M=, tarball: http://registry.m.jd.com/@types/request-promise/download/@types/request-promise-4.1.51.tgz}

  '@types/request@2.48.12':
    resolution: {integrity: sha1-D1kPYVoQ+H2hjpeQrJTCnsTF7zA=, tarball: http://registry.m.jd.com/@types/request/download/@types/request-2.48.12.tgz}

  '@types/tough-cookie@4.0.5':
    resolution: {integrity: sha1-y24qaRtwyxd8bjrpwdLosuqM0wQ=, tarball: http://registry.m.jd.com/@types/tough-cookie/download/@types/tough-cookie-4.0.5.tgz}

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: http://registry.m.jd.com/ajv/download/ajv-6.12.6.tgz}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=, tarball: http://registry.m.jd.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz}
    engines: {node: '>=8'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=, tarball: http://registry.m.jd.com/ansi-styles/download/ansi-styles-3.2.1.tgz}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: http://registry.m.jd.com/ansi-styles/download/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}

  asn1@0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=, tarball: http://registry.m.jd.com/asn1/download/asn1-0.2.6.tgz}

  assert-plus@1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=, tarball: http://registry.m.jd.com/assert-plus/download/assert-plus-1.0.0.tgz}
    engines: {node: '>=0.8'}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: http://registry.m.jd.com/asynckit/download/asynckit-0.4.0.tgz}

  at-least-node@1.0.0:
    resolution: {integrity: sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=, tarball: http://registry.m.jd.com/at-least-node/download/at-least-node-1.0.0.tgz}
    engines: {node: '>= 4.0.0'}

  aws-sign2@0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=, tarball: http://registry.m.jd.com/aws-sign2/download/aws-sign2-0.7.0.tgz}

  aws4@1.13.2:
    resolution: {integrity: sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=, tarball: http://registry.m.jd.com/aws4/download/aws4-1.13.2.tgz}

  babel-plugin-polyfill-corejs2@0.4.13:
    resolution: {integrity: sha1-fURfDgYH68j7awHX6PsCBpuR3Ys=, tarball: http://registry.m.jd.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.13.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.11.1:
    resolution: {integrity: sha1-Tk4YLxuzfHumLir4HY3QnfMTRPY=, tarball: http://registry.m.jd.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.11.1.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.4:
    resolution: {integrity: sha1-QoxhXTwXcpKiK0+T7ZnjWNeQaps=, tarball: http://registry.m.jd.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.4.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: http://registry.m.jd.com/balanced-match/download/balanced-match-1.0.2.tgz}

  bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=, tarball: http://registry.m.jd.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz}

  bluebird@3.7.2:
    resolution: {integrity: sha1-nyKcFb4nJFT/qXOs4NvueaGww28=, tarball: http://registry.m.jd.com/bluebird/download/bluebird-3.7.2.tgz}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=, tarball: http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz}

  browserslist@4.24.4:
    resolution: {integrity: sha1-xrKGWj8IvLhgoOgnOJADuf5obks=, tarball: http://registry.m.jd.com/browserslist/download/browserslist-4.24.4.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=, tarball: http://registry.m.jd.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  caniuse-lite@1.0.30001715:
    resolution: {integrity: sha1-vTJaN602bj/pCCfXQGKAejT7rrI=, tarball: http://registry.m.jd.com/caniuse-lite/download/caniuse-lite-1.0.30001715.tgz}

  caseless@0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=, tarball: http://registry.m.jd.com/caseless/download/caseless-0.12.0.tgz}

  chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=, tarball: http://registry.m.jd.com/chalk/download/chalk-2.4.2.tgz}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: http://registry.m.jd.com/chalk/download/chalk-4.1.2.tgz}
    engines: {node: '>=10'}

  chardet@0.7.0:
    resolution: {integrity: sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=, tarball: http://registry.m.jd.com/chardet/download/chardet-0.7.0.tgz}

  cli-cursor@3.1.0:
    resolution: {integrity: sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=, tarball: http://registry.m.jd.com/cli-cursor/download/cli-cursor-3.1.0.tgz}
    engines: {node: '>=8'}

  cli-width@3.0.0:
    resolution: {integrity: sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=, tarball: http://registry.m.jd.com/cli-width/download/cli-width-3.0.0.tgz}
    engines: {node: '>= 10'}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=, tarball: http://registry.m.jd.com/color-convert/download/color-convert-1.9.3.tgz}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: http://registry.m.jd.com/color-convert/download/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=, tarball: http://registry.m.jd.com/color-name/download/color-name-1.1.3.tgz}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: http://registry.m.jd.com/color-name/download/color-name-1.1.4.tgz}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: http://registry.m.jd.com/combined-stream/download/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: http://registry.m.jd.com/concat-map/download/concat-map-0.0.1.tgz}

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=, tarball: http://registry.m.jd.com/convert-source-map/download/convert-source-map-2.0.0.tgz}

  core-js-compat@3.41.0:
    resolution: {integrity: sha1-TN/OlfOajyd1m2Z89pPZbl3aPRc=, tarball: http://registry.m.jd.com/core-js-compat/download/core-js-compat-3.41.0.tgz}

  core-util-is@1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=, tarball: http://registry.m.jd.com/core-util-is/download/core-util-is-1.0.2.tgz}

  dashdash@1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=, tarball: http://registry.m.jd.com/dashdash/download/dashdash-1.14.1.tgz}
    engines: {node: '>=0.10'}

  debug@4.4.0:
    resolution: {integrity: sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=, tarball: http://registry.m.jd.com/debug/download/debug-4.4.0.tgz}
    engines: {node: '>=6.0'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: http://registry.m.jd.com/delayed-stream/download/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=, tarball: http://registry.m.jd.com/dunder-proto/download/dunder-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  ecc-jsbn@0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=, tarball: http://registry.m.jd.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz}

  electron-to-chromium@1.5.140:
    resolution: {integrity: sha1-kdknn+cpY/IsV4TMfzRhtf7TR4Y=, tarball: http://registry.m.jd.com/electron-to-chromium/download/electron-to-chromium-1.5.140.tgz}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz}

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=, tarball: http://registry.m.jd.com/error-ex/download/error-ex-1.3.2.tgz}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=, tarball: http://registry.m.jd.com/es-define-property/download/es-define-property-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=, tarball: http://registry.m.jd.com/es-errors/download/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=, tarball: http://registry.m.jd.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=, tarball: http://registry.m.jd.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz}
    engines: {node: '>= 0.4'}

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=, tarball: http://registry.m.jd.com/escalade/download/escalade-3.2.0.tgz}
    engines: {node: '>=6'}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=, tarball: http://registry.m.jd.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz}
    engines: {node: '>=0.8.0'}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: http://registry.m.jd.com/esutils/download/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}

  extend@3.0.2:
    resolution: {integrity: sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=, tarball: http://registry.m.jd.com/extend/download/extend-3.0.2.tgz}

  external-editor@3.1.0:
    resolution: {integrity: sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=, tarball: http://registry.m.jd.com/external-editor/download/external-editor-3.1.0.tgz}
    engines: {node: '>=4'}

  extsprintf@1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=, tarball: http://registry.m.jd.com/extsprintf/download/extsprintf-1.3.0.tgz}
    engines: {'0': node >=0.6.0}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: http://registry.m.jd.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: http://registry.m.jd.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz}

  figures@2.0.0:
    resolution: {integrity: sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=, tarball: http://registry.m.jd.com/figures/download/figures-2.0.0.tgz}
    engines: {node: '>=4'}

  figures@3.2.0:
    resolution: {integrity: sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=, tarball: http://registry.m.jd.com/figures/download/figures-3.2.0.tgz}
    engines: {node: '>=8'}

  find-up@2.1.0:
    resolution: {integrity: sha1-RdG35QbHF93UgndaK3eSCjwMV6c=, tarball: http://registry.m.jd.com/find-up/download/find-up-2.1.0.tgz}
    engines: {node: '>=4'}

  forever-agent@0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=, tarball: http://registry.m.jd.com/forever-agent/download/forever-agent-0.6.1.tgz}

  form-data@2.3.3:
    resolution: {integrity: sha1-3M5SwF9kTymManq5Nr1yTO/786Y=, tarball: http://registry.m.jd.com/form-data/download/form-data-2.3.3.tgz}
    engines: {node: '>= 0.12'}

  form-data@2.5.3:
    resolution: {integrity: sha1-+bz4dBjOdIUTwMNJS7SOwnDJesw=, tarball: http://registry.m.jd.com/form-data/download/form-data-2.5.3.tgz}
    engines: {node: '>= 0.12'}

  fs-extra@8.1.0:
    resolution: {integrity: sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=, tarball: http://registry.m.jd.com/fs-extra/download/fs-extra-8.1.0.tgz}
    engines: {node: '>=6 <7 || >=8'}

  fs-extra@9.1.0:
    resolution: {integrity: sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=, tarball: http://registry.m.jd.com/fs-extra/download/fs-extra-9.1.0.tgz}
    engines: {node: '>=10'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=, tarball: http://registry.m.jd.com/fs.realpath/download/fs.realpath-1.0.0.tgz}

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=, tarball: http://registry.m.jd.com/function-bind/download/function-bind-1.1.2.tgz}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=, tarball: http://registry.m.jd.com/gensync/download/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=, tarball: http://registry.m.jd.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=, tarball: http://registry.m.jd.com/get-proto/download/get-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  getpass@0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=, tarball: http://registry.m.jd.com/getpass/download/getpass-0.1.7.tgz}

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=, tarball: http://registry.m.jd.com/glob/download/glob-7.2.3.tgz}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=, tarball: http://registry.m.jd.com/globals/download/globals-11.12.0.tgz}
    engines: {node: '>=4'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=, tarball: http://registry.m.jd.com/gopd/download/gopd-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=, tarball: http://registry.m.jd.com/graceful-fs/download/graceful-fs-4.2.11.tgz}

  har-schema@2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=, tarball: http://registry.m.jd.com/har-schema/download/har-schema-2.0.0.tgz}
    engines: {node: '>=4'}

  har-validator@5.1.5:
    resolution: {integrity: sha1-HwgDufjLIMD6E4It8ezds2veHv0=, tarball: http://registry.m.jd.com/har-validator/download/har-validator-5.1.5.tgz}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported

  has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=, tarball: http://registry.m.jd.com/has-flag/download/has-flag-3.0.0.tgz}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: http://registry.m.jd.com/has-flag/download/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=, tarball: http://registry.m.jd.com/has-symbols/download/has-symbols-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=, tarball: http://registry.m.jd.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=, tarball: http://registry.m.jd.com/hasown/download/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  http-signature@1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=, tarball: http://registry.m.jd.com/http-signature/download/http-signature-1.2.0.tgz}
    engines: {node: '>=0.8', npm: '>=1.3.7'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=, tarball: http://registry.m.jd.com/iconv-lite/download/iconv-lite-0.4.24.tgz}
    engines: {node: '>=0.10.0'}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=, tarball: http://registry.m.jd.com/inflight/download/inflight-1.0.6.tgz}

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=, tarball: http://registry.m.jd.com/inherits/download/inherits-2.0.4.tgz}

  inquirer@7.3.3:
    resolution: {integrity: sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=, tarball: http://registry.m.jd.com/inquirer/download/inquirer-7.3.3.tgz}
    engines: {node: '>=8.0.0'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=, tarball: http://registry.m.jd.com/is-arrayish/download/is-arrayish-0.2.1.tgz}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=, tarball: http://registry.m.jd.com/is-core-module/download/is-core-module-2.16.1.tgz}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: http://registry.m.jd.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=, tarball: http://registry.m.jd.com/is-typedarray/download/is-typedarray-1.0.0.tgz}

  isstream@0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=, tarball: http://registry.m.jd.com/isstream/download/isstream-0.1.2.tgz}

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: http://registry.m.jd.com/js-tokens/download/js-tokens-4.0.0.tgz}

  jsbn@0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=, tarball: http://registry.m.jd.com/jsbn/download/jsbn-0.1.1.tgz}

  jsesc@3.0.2:
    resolution: {integrity: sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=, tarball: http://registry.m.jd.com/jsesc/download/jsesc-3.0.2.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=, tarball: http://registry.m.jd.com/jsesc/download/jsesc-3.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  json-parse-better-errors@1.0.2:
    resolution: {integrity: sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=, tarball: http://registry.m.jd.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz}

  json-schema@0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=, tarball: http://registry.m.jd.com/json-schema/download/json-schema-0.4.0.tgz}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=, tarball: http://registry.m.jd.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz}

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=, tarball: http://registry.m.jd.com/json5/download/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@4.0.0:
    resolution: {integrity: sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=, tarball: http://registry.m.jd.com/jsonfile/download/jsonfile-4.0.0.tgz}

  jsonfile@6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=, tarball: http://registry.m.jd.com/jsonfile/download/jsonfile-6.1.0.tgz}

  jsprim@1.4.2:
    resolution: {integrity: sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=, tarball: http://registry.m.jd.com/jsprim/download/jsprim-1.4.2.tgz}
    engines: {node: '>=0.6.0'}

  load-json-file@4.0.0:
    resolution: {integrity: sha1-L19Fq5HjMhYjT9U62rZo607AmTs=, tarball: http://registry.m.jd.com/load-json-file/download/load-json-file-4.0.0.tgz}
    engines: {node: '>=4'}

  locate-path@2.0.0:
    resolution: {integrity: sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=, tarball: http://registry.m.jd.com/locate-path/download/locate-path-2.0.0.tgz}
    engines: {node: '>=4'}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=, tarball: http://registry.m.jd.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: http://registry.m.jd.com/lodash/download/lodash-4.17.21.tgz}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=, tarball: http://registry.m.jd.com/lru-cache/download/lru-cache-5.1.1.tgz}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=, tarball: http://registry.m.jd.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=, tarball: http://registry.m.jd.com/mime-db/download/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=, tarball: http://registry.m.jd.com/mime-types/download/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=, tarball: http://registry.m.jd.com/mime/download/mime-1.6.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=, tarball: http://registry.m.jd.com/mimic-fn/download/mimic-fn-2.1.0.tgz}
    engines: {node: '>=6'}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=, tarball: http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: http://registry.m.jd.com/ms/download/ms-2.1.3.tgz}

  mute-stream@0.0.8:
    resolution: {integrity: sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=, tarball: http://registry.m.jd.com/mute-stream/download/mute-stream-0.0.8.tgz}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=, tarball: http://registry.m.jd.com/node-releases/download/node-releases-2.0.19.tgz}

  oauth-sign@0.9.0:
    resolution: {integrity: sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=, tarball: http://registry.m.jd.com/oauth-sign/download/oauth-sign-0.9.0.tgz}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=, tarball: http://registry.m.jd.com/once/download/once-1.4.0.tgz}

  onetime@5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=, tarball: http://registry.m.jd.com/onetime/download/onetime-5.1.2.tgz}
    engines: {node: '>=6'}

  os-tmpdir@1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=, tarball: http://registry.m.jd.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  p-limit@1.3.0:
    resolution: {integrity: sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=, tarball: http://registry.m.jd.com/p-limit/download/p-limit-1.3.0.tgz}
    engines: {node: '>=4'}

  p-locate@2.0.0:
    resolution: {integrity: sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=, tarball: http://registry.m.jd.com/p-locate/download/p-locate-2.0.0.tgz}
    engines: {node: '>=4'}

  p-try@1.0.0:
    resolution: {integrity: sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=, tarball: http://registry.m.jd.com/p-try/download/p-try-1.0.0.tgz}
    engines: {node: '>=4'}

  parse-json@4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=, tarball: http://registry.m.jd.com/parse-json/download/parse-json-4.0.0.tgz}
    engines: {node: '>=4'}

  path-exists@3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=, tarball: http://registry.m.jd.com/path-exists/download/path-exists-3.0.0.tgz}
    engines: {node: '>=4'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=, tarball: http://registry.m.jd.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=, tarball: http://registry.m.jd.com/path-parse/download/path-parse-1.0.7.tgz}

  performance-now@2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=, tarball: http://registry.m.jd.com/performance-now/download/performance-now-2.1.0.tgz}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=, tarball: http://registry.m.jd.com/picocolors/download/picocolors-1.1.1.tgz}

  pify@3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=, tarball: http://registry.m.jd.com/pify/download/pify-3.0.0.tgz}
    engines: {node: '>=4'}

  pkg-conf@2.1.0:
    resolution: {integrity: sha1-ISZRTKbyq/69FoWW3xi6V4Z/AFg=, tarball: http://registry.m.jd.com/pkg-conf/download/pkg-conf-2.1.0.tgz}
    engines: {node: '>=4'}

  psl@1.15.0:
    resolution: {integrity: sha1-vazjGJbx2XzsannoIkiYzpPZdMY=, tarball: http://registry.m.jd.com/psl/download/psl-1.15.0.tgz}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=, tarball: http://registry.m.jd.com/punycode/download/punycode-2.3.1.tgz}
    engines: {node: '>=6'}

  qs@6.5.3:
    resolution: {integrity: sha1-Ou7/yRln7241wOSI70b7KWq3aq0=, tarball: http://registry.m.jd.com/qs/download/qs-6.5.3.tgz}
    engines: {node: '>=0.6'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=, tarball: http://registry.m.jd.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=, tarball: http://registry.m.jd.com/regenerate/download/regenerate-1.4.2.tgz}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha1-NWreECY/aF3aElEAzYYsHbiVMn8=, tarball: http://registry.m.jd.com/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=, tarball: http://registry.m.jd.com/regenerator-transform/download/regenerator-transform-0.15.2.tgz}

  regexpu-core@6.2.0:
    resolution: {integrity: sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=, tarball: http://registry.m.jd.com/regexpu-core/download/regexpu-core-6.2.0.tgz}
    engines: {node: '>=4'}

  regjsgen@0.8.0:
    resolution: {integrity: sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=, tarball: http://registry.m.jd.com/regjsgen/download/regjsgen-0.8.0.tgz}

  regjsparser@0.12.0:
    resolution: {integrity: sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=, tarball: http://registry.m.jd.com/regjsparser/download/regjsparser-0.12.0.tgz}
    hasBin: true

  request-promise-core@1.1.4:
    resolution: {integrity: sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=, tarball: http://registry.m.jd.com/request-promise-core/download/request-promise-core-1.1.4.tgz}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      request: ^2.34

  request-promise@4.2.6:
    resolution: {integrity: sha1-fn5blXhjDm9ZjjgTwPjrNCon8KI=, tarball: http://registry.m.jd.com/request-promise/download/request-promise-4.2.6.tgz}
    engines: {node: '>=0.10.0'}
    deprecated: request-promise has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142
    peerDependencies:
      request: ^2.34

  request@2.88.2:
    resolution: {integrity: sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=, tarball: http://registry.m.jd.com/request/download/request-2.88.2.tgz}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=, tarball: http://registry.m.jd.com/resolve/download/resolve-1.22.10.tgz}
    engines: {node: '>= 0.4'}
    hasBin: true

  restore-cursor@3.1.0:
    resolution: {integrity: sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=, tarball: http://registry.m.jd.com/restore-cursor/download/restore-cursor-3.1.0.tgz}
    engines: {node: '>=8'}

  run-async@2.4.1:
    resolution: {integrity: sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=, tarball: http://registry.m.jd.com/run-async/download/run-async-2.4.1.tgz}
    engines: {node: '>=0.12.0'}

  rxjs@6.6.7:
    resolution: {integrity: sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=, tarball: http://registry.m.jd.com/rxjs/download/rxjs-6.6.7.tgz}
    engines: {npm: '>=2.0.0'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=, tarball: http://registry.m.jd.com/safe-buffer/download/safe-buffer-5.2.1.tgz}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=, tarball: http://registry.m.jd.com/safer-buffer/download/safer-buffer-2.1.2.tgz}

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=, tarball: http://registry.m.jd.com/semver/download/semver-6.3.1.tgz}
    hasBin: true

  signal-exit@3.0.7:
    resolution: {integrity: sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=, tarball: http://registry.m.jd.com/signal-exit/download/signal-exit-3.0.7.tgz}

  signale@1.4.0:
    resolution: {integrity: sha1-xL5YMC+wJirAD8PYhqfBE3WQQvE=, tarball: http://registry.m.jd.com/signale/download/signale-1.4.0.tgz}
    engines: {node: '>=6'}

  silly-datetime@0.1.2:
    resolution: {integrity: sha1-IZeOjo2EgWFgY6wRL/FGk/BuuFs=, tarball: http://registry.m.jd.com/silly-datetime/download/silly-datetime-0.1.2.tgz}

  sshpk@1.18.0:
    resolution: {integrity: sha1-FmPlXN301oi4aka3fw1f42OroCg=, tarball: http://registry.m.jd.com/sshpk/download/sshpk-1.18.0.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  stealthy-require@1.1.1:
    resolution: {integrity: sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=, tarball: http://registry.m.jd.com/stealthy-require/download/stealthy-require-1.1.1.tgz}
    engines: {node: '>=0.10.0'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz}
    engines: {node: '>=8'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}

  strip-bom@3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=, tarball: http://registry.m.jd.com/strip-bom/download/strip-bom-3.0.0.tgz}
    engines: {node: '>=4'}

  supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=, tarball: http://registry.m.jd.com/supports-color/download/supports-color-5.5.0.tgz}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: http://registry.m.jd.com/supports-color/download/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=, tarball: http://registry.m.jd.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  through@2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=, tarball: http://registry.m.jd.com/through/download/through-2.3.8.tgz}

  tmp@0.0.33:
    resolution: {integrity: sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=, tarball: http://registry.m.jd.com/tmp/download/tmp-0.0.33.tgz}
    engines: {node: '>=0.6.0'}

  tough-cookie@2.5.0:
    resolution: {integrity: sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=, tarball: http://registry.m.jd.com/tough-cookie/download/tough-cookie-2.5.0.tgz}
    engines: {node: '>=0.8'}

  tslib@1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=, tarball: http://registry.m.jd.com/tslib/download/tslib-1.14.1.tgz}

  tunnel-agent@0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=, tarball: http://registry.m.jd.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz}

  tweetnacl@0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=, tarball: http://registry.m.jd.com/tweetnacl/download/tweetnacl-0.14.5.tgz}

  type-fest@0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=, tarball: http://registry.m.jd.com/type-fest/download/type-fest-0.21.3.tgz}
    engines: {node: '>=10'}

  typescript@3.9.10:
    resolution: {integrity: sha1-cPORCselHta+952ngAaQsZv3eLg=, tarball: http://registry.m.jd.com/typescript/download/typescript-3.9.10.tgz}
    engines: {node: '>=4.2.0'}
    hasBin: true

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha1-yzFz/kfKdD4ighbko93EyE1ijMI=, tarball: http://registry.m.jd.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=, tarball: http://registry.m.jd.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=, tarball: http://registry.m.jd.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=, tarball: http://registry.m.jd.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz}
    engines: {node: '>=4'}

  universalify@0.1.2:
    resolution: {integrity: sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=, tarball: http://registry.m.jd.com/universalify/download/universalify-0.1.2.tgz}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=, tarball: http://registry.m.jd.com/universalify/download/universalify-2.0.1.tgz}
    engines: {node: '>= 10.0.0'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=, tarball: http://registry.m.jd.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: http://registry.m.jd.com/uri-js/download/uri-js-4.4.1.tgz}

  uuid@3.4.0:
    resolution: {integrity: sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=, tarball: http://registry.m.jd.com/uuid/download/uuid-3.4.0.tgz}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  verror@1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=, tarball: http://registry.m.jd.com/verror/download/verror-1.10.0.tgz}
    engines: {'0': node >=0.6.0}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=, tarball: http://registry.m.jd.com/wrappy/download/wrappy-1.0.2.tgz}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=, tarball: http://registry.m.jd.com/yallist/download/yallist-3.1.1.tgz}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.8': {}

  '@babel/core@7.26.10':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helpers': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1

  '@babel/generator@7.27.0':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.25.9':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/helper-compilation-targets@7.27.0':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.4
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.10)
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.27.0
      semver: 6.3.1

  '@babel/helper-create-regexp-features-plugin@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      regexpu-core: 6.2.0
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      debug: 4.4.0
      lodash.debounce: 4.0.8
      resolve: 1.22.10

  '@babel/helper-member-expression-to-functions@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.27.0

  '@babel/helper-optimise-call-expression@7.25.9':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/helper-plugin-utils@7.26.5': {}

  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-wrap-function': 7.25.9
      '@babel/traverse': 7.27.0

  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.27.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helper-wrap-function@7.25.9':
    dependencies:
      '@babel/template': 7.27.0
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0

  '@babel/helpers@7.27.0':
    dependencies:
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0

  '@babel/parser@7.27.0':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.27.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.10)

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.27.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10

  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-async-generator-functions@7.26.8(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.10)
      '@babel/traverse': 7.27.0

  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.10)

  '@babel/plugin-transform-block-scoped-functions@7.26.5(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-block-scoping@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-class-properties@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-class-static-block@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.10)
      '@babel/traverse': 7.27.0
      globals: 11.12.0

  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/template': 7.27.0

  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-dynamic-import@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-exponentiation-operator@7.26.3(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-export-namespace-from@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-for-of@7.26.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9

  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.27.0

  '@babel/plugin-transform-json-strings@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-logical-assignment-operators@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-member-expression-literals@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-modules-commonjs@7.26.3(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.27.0

  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-numeric-separator@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-object-rest-spread@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.10)

  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.10)

  '@babel/plugin-transform-optional-catch-binding@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-optional-chaining@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9

  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-private-methods@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-private-property-in-object@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-create-class-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-property-literals@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-regenerator@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-regexp-modifiers@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-reserved-words@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9

  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-template-literals@7.26.8(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-typeof-symbol@7.27.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-escapes@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-property-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-sets-regex@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-create-regexp-features-plugin': 7.27.0(@babel/core@7.26.10)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/preset-env@7.26.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/core': 7.26.10
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-option': 7.25.9
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.10)
      '@babel/plugin-syntax-import-assertions': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-syntax-import-attributes': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.26.10)
      '@babel/plugin-transform-arrow-functions': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-async-generator-functions': 7.26.8(@babel/core@7.26.10)
      '@babel/plugin-transform-async-to-generator': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-block-scoped-functions': 7.26.5(@babel/core@7.26.10)
      '@babel/plugin-transform-block-scoping': 7.27.0(@babel/core@7.26.10)
      '@babel/plugin-transform-class-properties': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-class-static-block': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-transform-classes': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-computed-properties': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-destructuring': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-dotall-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-duplicate-keys': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-dynamic-import': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-exponentiation-operator': 7.26.3(@babel/core@7.26.10)
      '@babel/plugin-transform-export-namespace-from': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-for-of': 7.26.9(@babel/core@7.26.10)
      '@babel/plugin-transform-function-name': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-json-strings': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-literals': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-logical-assignment-operators': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-member-expression-literals': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-amd': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-commonjs': 7.26.3(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-systemjs': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-modules-umd': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-new-target': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.26.6(@babel/core@7.26.10)
      '@babel/plugin-transform-numeric-separator': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-object-rest-spread': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-object-super': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-optional-catch-binding': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-private-methods': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-private-property-in-object': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-property-literals': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-regenerator': 7.27.0(@babel/core@7.26.10)
      '@babel/plugin-transform-regexp-modifiers': 7.26.0(@babel/core@7.26.10)
      '@babel/plugin-transform-reserved-words': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-shorthand-properties': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-spread': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-sticky-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-template-literals': 7.26.8(@babel/core@7.26.10)
      '@babel/plugin-transform-typeof-symbol': 7.27.0(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-escapes': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-property-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-unicode-sets-regex': 7.25.9(@babel/core@7.26.10)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.26.10)
      babel-plugin-polyfill-corejs2: 0.4.13(@babel/core@7.26.10)
      babel-plugin-polyfill-corejs3: 0.11.1(@babel/core@7.26.10)
      babel-plugin-polyfill-regenerator: 0.6.4(@babel/core@7.26.10)
      core-js-compat: 3.41.0
      semver: 6.3.1

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/types': 7.27.0
      esutils: 2.0.3

  '@babel/runtime@7.27.0':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0

  '@babel/traverse@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0
      debug: 4.4.0
      globals: 11.12.0

  '@babel/types@7.27.0':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@jd/jmfe-node-jss@1.2.14':
    dependencies:
      '@types/glob': 7.2.0
      '@types/node': 14.18.63
      '@types/request': 2.48.12
      '@types/request-promise': 4.1.51
      bluebird: 3.7.2
      fs-extra: 8.1.0
      glob: 7.2.3
      lodash: 4.17.21
      mime: 1.6.0
      request: 2.88.2
      request-promise: 4.2.6(request@2.88.2)

  '@jd/upload-oss-tools@1.1.25':
    dependencies:
      '@jd/jmfe-node-jss': 1.2.14
      fs-extra: 9.1.0
      glob: 7.2.3
      inquirer: 7.3.3
      signale: 1.4.0
      silly-datetime: 0.1.2
      typescript: 3.9.10

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@types/bluebird@3.5.42': {}

  '@types/caseless@0.12.5': {}

  '@types/glob@7.2.0':
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 14.18.63

  '@types/minimatch@5.1.2': {}

  '@types/node@14.18.63': {}

  '@types/request-promise@4.1.51':
    dependencies:
      '@types/bluebird': 3.5.42
      '@types/request': 2.48.12

  '@types/request@2.48.12':
    dependencies:
      '@types/caseless': 0.12.5
      '@types/node': 14.18.63
      '@types/tough-cookie': 4.0.5
      form-data: 2.5.3

  '@types/tough-cookie@4.0.5': {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  assert-plus@1.0.0: {}

  asynckit@0.4.0: {}

  at-least-node@1.0.0: {}

  aws-sign2@0.7.0: {}

  aws4@1.13.2: {}

  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.26.10):
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/core': 7.26.10
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.26.10)
      semver: 6.3.1

  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.26.10):
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.26.10)
      core-js-compat: 3.41.0

  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.26.10):
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.26.10)

  balanced-match@1.0.2: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  bluebird@3.7.2: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001715
      electron-to-chromium: 1.5.140
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  caniuse-lite@1.0.30001715: {}

  caseless@0.12.0: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chardet@0.7.0: {}

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-width@3.0.0: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  concat-map@0.0.1: {}

  convert-source-map@2.0.0: {}

  core-js-compat@3.41.0:
    dependencies:
      browserslist: 4.24.4

  core-util-is@1.0.2: {}

  dashdash@1.14.1:
    dependencies:
      assert-plus: 1.0.0

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  delayed-stream@1.0.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  ecc-jsbn@0.1.2:
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  electron-to-chromium@1.5.140: {}

  emoji-regex@8.0.0: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  escalade@3.2.0: {}

  escape-string-regexp@1.0.5: {}

  esutils@2.0.3: {}

  extend@3.0.2: {}

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  extsprintf@1.3.0: {}

  fast-deep-equal@3.1.3: {}

  fast-json-stable-stringify@2.1.0: {}

  figures@2.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  figures@3.2.0:
    dependencies:
      escape-string-regexp: 1.0.5

  find-up@2.1.0:
    dependencies:
      locate-path: 2.0.0

  forever-agent@0.6.1: {}

  form-data@2.3.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@2.5.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35
      safe-buffer: 5.2.1

  fs-extra@8.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2

  fs-extra@9.1.0:
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs.realpath@1.0.0: {}

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  getpass@0.1.7:
    dependencies:
      assert-plus: 1.0.0

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  har-schema@2.0.0: {}

  har-validator@5.1.5:
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  http-signature@1.2.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  inquirer@7.3.3:
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8

  is-arrayish@0.2.1: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-fullwidth-code-point@3.0.0: {}

  is-typedarray@1.0.0: {}

  isstream@0.1.2: {}

  js-tokens@4.0.0: {}

  jsbn@0.1.1: {}

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-parse-better-errors@1.0.2: {}

  json-schema-traverse@0.4.1: {}

  json-schema@0.4.0: {}

  json-stringify-safe@5.0.1: {}

  json5@2.2.3: {}

  jsonfile@4.0.0:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsprim@1.4.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  load-json-file@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0

  locate-path@2.0.0:
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0

  lodash.debounce@4.0.8: {}

  lodash@4.17.21: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  math-intrinsics@1.1.0: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mimic-fn@2.1.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  ms@2.1.3: {}

  mute-stream@0.0.8: {}

  node-releases@2.0.19: {}

  oauth-sign@0.9.0: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  os-tmpdir@1.0.2: {}

  p-limit@1.3.0:
    dependencies:
      p-try: 1.0.0

  p-locate@2.0.0:
    dependencies:
      p-limit: 1.3.0

  p-try@1.0.0: {}

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  path-exists@3.0.0: {}

  path-is-absolute@1.0.1: {}

  path-parse@1.0.7: {}

  performance-now@2.1.0: {}

  picocolors@1.1.1: {}

  pify@3.0.0: {}

  pkg-conf@2.1.0:
    dependencies:
      find-up: 2.1.0
      load-json-file: 4.0.0

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  punycode@2.3.1: {}

  qs@6.5.3: {}

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.27.0

  regexpu-core@6.2.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  request-promise-core@1.1.4(request@2.88.2):
    dependencies:
      lodash: 4.17.21
      request: 2.88.2

  request-promise@4.2.6(request@2.88.2):
    dependencies:
      bluebird: 3.7.2
      request: 2.88.2
      request-promise-core: 1.1.4(request@2.88.2)
      stealthy-require: 1.1.1
      tough-cookie: 2.5.0

  request@2.88.2:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  run-async@2.4.1: {}

  rxjs@6.6.7:
    dependencies:
      tslib: 1.14.1

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  semver@6.3.1: {}

  signal-exit@3.0.7: {}

  signale@1.4.0:
    dependencies:
      chalk: 2.4.2
      figures: 2.0.0
      pkg-conf: 2.1.0

  silly-datetime@0.1.2: {}

  sshpk@1.18.0:
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  stealthy-require@1.1.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-bom@3.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  through@2.3.8: {}

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  tough-cookie@2.5.0:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1

  tslib@1.14.1: {}

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tweetnacl@0.14.5: {}

  type-fest@0.21.3: {}

  typescript@3.9.10: {}

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  universalify@0.1.2: {}

  universalify@2.0.1: {}

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  uuid@3.4.0: {}

  verror@1.10.0:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  wrappy@1.0.2: {}

  yallist@3.1.1: {}
