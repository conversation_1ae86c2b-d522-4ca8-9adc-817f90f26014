define("MOD_ROOT/buytype/buytype.5g", function (require, exports, module) {
    "use strict";

    var Login = require("JDF_UNIT/login/1.0.0/login");
    var G = require("MOD_ROOT/common/core");
    var Event = require("MOD_ROOT/common/tools/event").Event;
    var Utils = require("MOD_ROOT/common/tools/tools");
    var GiftPool = require("MOD_ROOT/gift/gift");
    var isHyj = (G.specialAttrs["isHyj"] == 1);  // 是否是5G合约机
    var cfg = (window.pageConfig && window.pageConfig.product) || {};
    var noop = function () { }; // 空函数

    var $type = $("#choose-type");
    var $heyueButton = $("#btn-heyue").text("选择合约机");
    var $cartButton = $("#InitCartUrl, #InitCartUrl-mini");
    var $amount = $("#choose-btns .choose-amount");

    function navigateTo(url) {
        window.location = url;
    }

    /**
     * 获取办理5G合约机业务的落地页URL
     * @param {Object} params
     * @param {Function} onSuccess
     * @param {Function} onError
     * @returns Promise
     */
    function getURLData(params, onSuccess, onError) {
        var area = Utils.getAreaId().areaIds;

        params = $.extend({
            skuId: cfg.skuid || '',
            shopId: cfg.shopId || '',
            provinceId: area[0],
            cityId: area[1],
            countyId: area[2],
            townId: area[3],
            isHyj: 1,
            source: 1
        }, params);

        return $.ajax({
            dataType: "jsonp",
            url: "//tstp.jd.com/isp/getTreatyInfo",
            data: params,
            success: $.isFunction(onSuccess) ? onSuccess : noop,
            error: $.isFunction(onError) ? onError : noop
        });
    }

    /**
     * 渲染购机方式
     * @param {jQuery} $mount
     */
    function render$typeUI($mount) {
        getURLData().
            done(function (res) {
                res = res || {};
                render($mount, res.data);
            }).
            fail(function () {
                render($mount, []);
            });

        function render($mount, data) {
            if ($mount.length === 0) {
                return;
            }

            if ($.isArray(data) && data.length > 0) {
                try {
                    var __html = ('\
                    <div class="dt">购买方式</div> \
                    <div class="dd"> \
                        {for obj in data} \
                        <div class="J-btype-item item{if obj.type == 0} selected{/if}" \
                            data-type="${obj.type}" \
                            data-url="${obj.mUrl}"> \
                            <b></b><a href="#none" title="">${obj.name}</a> \
                        </div> \
                        {/for} \
                    </div>').process({ data: data });
                } catch (err) {
                    var __html = "";
                }
            } else {
                var __html = "";
            }

            $mount.html(__html).toggle(!!__html);
        }
    }

    /**
     * 切换按钮
     * @param {Boolean} sign
     */
    function toggleButton(sign) {
        $cartButton.toggle(sign);
        $heyueButton.toggle(!sign);
        $amount.css("visibility", sign ? "visible" : "hidden");
    }

    /**
     * `#choose-type`元素做事件委托处理子元素的点击事件
     */
    function $typeOnClick() {
        var $this = $(this);
        var data = $this.data();
        var SELECTED_CLASS = "selected";
        if ($this.hasClass(SELECTED_CLASS)) {
            return;
        }
        /// 切换“购买方式”的选中状态
        $this.addClass(SELECTED_CLASS).
            siblings().removeClass(SELECTED_CLASS);
        /// 切换按钮的展示状态
        var sign = (data.type == 0);
        toggleButton(sign);
        /// 设置“选择合约机”按钮的跳转链接
        !sign && $heyueButton.attr("href", data.url);
    }

    /**
     * "选择合约机"按钮点击事件
     */
    function $heyueButtonOnClick() {
        var $this = $(this);
        if ($this.hasClass("btn-disable")) {
            return false;
        }

        var arr = [""];
        var obj = getGiftPoolSkuPairs();
        $.each([
            NS.getybSkuPairs(),
            NS.getfwSkuPairs(),
            obj.giftIds,
            obj.giftItems], function (index, elem) {
            if (elem) { arr.push(elem); }
        });

        var url = $this[0].href + arr.join("&");
        Login({
            modal: true,
            complete: function (data) {
                if (data !== null &&
                    data.Identity &&
                    data.Identity.IsAuthenticated) {
                    url && navigateTo(url);
                }
            }
        });
        return false;
    }

    /**
     * 获取购物车中的数据
     * @param {Object} params
     * @param {Function} onSuccess
     * @param {Function} onError
     * @returns Promise
     */
    function getCartData(params, onSuccess, onError) {
        params = $.extend({
            pids: cfg.skuid || '',
            oft: 2,
        }, params);

        return $.ajax({
            dataType: "jsonp",
            url: "//cart.jd.com/getSimpleCart.action",
            data: params,
            success: $.isFunction(onSuccess) ? onSuccess : noop,
            error: $.isFunction(onError) ? onError : noop
        });
    }

    /**
     * 当前sku的颜色、版本信息
     */
    var ColorSize = (function (cfg) {
        var arr = cfg.colorSize || [];
        var obj = {},
            i = 0,
            len = arr.length;

        for (; i < len; i++) {
            if (arr[i].skuId == cfg.skuid) {
                obj = arr[i];
                break;
            }
        }

        var str0 = "\u7248\u672c";
        var str1 = "\u989c\u8272";
        return {
            __size: obj[str0] ? obj[str0] : "",
            __color: obj[str1] ? obj[str1] : ""
        };
    })(cfg);

    /**
     * 获取sku的名称信息
     * @param {Number} skuId
     * @param {Function} func
     * @returns Promise
     */
    function getSkuName(skuId, func) {
        var url = "//item.jd.com/" + skuId + ".html";
        return $.ajax({
            type: "get",
            url: url,
            dataType: "html",
            success: function (html) {
                try {
                    var $doc = $(html);
                    var name = $.trim($(".sku-name", $doc).text())
                } catch (error) {
                    var name = "";
                }
                $.isFunction(func) && func(name);
            },
            error: function () {
                $.isFunction(func) && func("");
            }
        });
    }

    /**
     * 
     * @param {Object} params
     */
    function openConfirmDialog(params) {
        var __html = '\
        <div class="cp-confirm-dialog">\
            <div class="cp-confirm-dialog__head">当前已选商品继续添加，将会替换购物车中已有合约机商品。</div>\
            <div class="cp-confirm-dialog__body">\
                <div class="cp-confirm-dialog__body__left">\
                    <div class="cp-confirm-dialog__title"><span class="icon-cart"></span>已选商品</div>\
                    <p>${data.__color}</p>\
                    <p>${data.__size}</p>\
                </div>\
                <div class="cp-confirm-dialog__body__right">\
                    <div class="cp-confirm-dialog__title"><span class="icon-flush"></span>替换商品</div>\
                    <p>${data.__color}</p>\
                    <p>${data.__size}</p>\
                    <p title="${data.skuName}">${data.skuName}</p>\
                    <p>${data.phoneNum}</p>\
                </div>\
                <span class="icon-replace"></span>\
            </div>\
            <div class="cp-confirm-dialog__foot">\
                <span class="cp-confirm-dialog__button cp-confirm-dialog__button--right cp-confirm-dialog__button--gray">我再想想</span>\
                <span class="cp-confirm-dialog__button cp-confirm-dialog__button--left cp-confirm-dialog__button--red">继续添加</span>\
            </div>\
        </div>';

        getSkuName(params.linkSku, func);

        var obj = $.extend({}, ColorSize, params);
        function func(name) {
            obj["skuName"] = name;
            $("body").dialog({
                width: 620,
                type: "html",
                extendMainClass: "g5-contract-phone",
                source: __html.process({data: obj}),
                maskClose: true,
                autoUpdate:true,
                onReady: function () {
                    var that = this;
                    var $root = this.el;
                    $(".cp-confirm-dialog__button--gray", $root).
                        unbind("click").bind("click", function () {
                        that.close();
                    });
                    $(".cp-confirm-dialog__button--red", $root).
                        unbind("click").bind("click", function () {
                        navigateTo(obj.url);
                        that.close();
                    });
                }
            });
        }
    }

    /**
     * "加入购物车"按钮附加点击事件
     */
    function $cartButtonOnClick() {
        var $this = $(this);
        
        if ($this.hasClass("btn-disable") ||
            (GiftPool.model && !GiftPool.model.getSelectedResult())
        ) { return false; }

        Login({
            modal: true,
            complete: function (data) {
                data = data || {};
                data.Identity &&
                data.Identity.IsAuthenticated &&
                checkCart();
            }
        });

        var url = $this[0].href;
        function checkCart() {
            getCartData().
            done(function (res) {
                try { /// try语句判定购物车中有合约机
                    var obj = res.cv.products[0].
                        businessLinkServiceVO.hyj.serviceSkus[0];
                    var linkSku = obj.id;
                    var phoneNum = obj.extFlag.cart_pn;
                } catch (err) {
                    var linkSku = "";
                    var phoneNum = "";
                }

                if (linkSku && phoneNum) {
                    openConfirmDialog({
                        url: url,
                        linkSku: linkSku,
                        phoneNum: phoneNum
                    });
                } else {
                    navigateTo(url);
                }
            }).
            fail(function () {
                navigateTo(url);
            });
        }

        return false;
    }

    function getGiftPoolSkuPairs() {
        var obj,
            model = GiftPool.model;
        if (model && (obj = model.getSelectedResult())) {
            var gids = obj.gids;
            var type = obj.giftPoolType;
            return {
                giftIds: type == 0 ?
                    "giftIds=" + gids :
                    type == 1 ? "giftIds=" + gids.replace(/_[^,]+/g, "") : "",
                giftItems: type == 0 ?
                    "giftItems=" + gids.replace(/([^,]+)/g, "$1" + "_" + $("#buy-num").val() || 1) :
                    type == 1 ? "giftItems=" + gids : ""
            };
        } else {
            return { giftIds: "", giftItems: "" };
        }
    }

    var NS = {},
        arr = ["ybSku", "fwSku"];
    for (var i = 0; i < arr.length; i++) {
        (function (key) {
            NS[key] = "";
            NS["get" + key + "Pairs"] = function () {
                var val = NS[key];
                return val ? key + "=" + val : "";
            };
            NS["set" + key] = function (obj) {
                obj = obj || {};
                var arr = obj.skus;
                return NS[key] = ({}).toString.call(arr) ===
                    "[object Array]" ? arr.join(",") : "";
            };
        })(arr[i]);
    }

    function init(cfg) {
        if (!isHyj) {
            return;
        }

        $type.delegate(".item", "click", $typeOnClick);
        $heyueButton.bind("click", $heyueButtonOnClick);
        $cartButton.bind("click.5g", $cartButtonOnClick);
        Event.addListener("onAreaChange", function () {
            render$typeUI($type);
            toggleButton(true);
        });
        /// 切换“选择合约机”按钮状态
        Event.addListener("onStockReady", function () {
            $heyueButton.toggleClass("btn-disable", !cfg.havestock);
        });
        Event.addListener("onYBSelected", NS.setybSku);
        Event.addListener("onServicePlusSelected", NS.setfwSku);
    }

    module.exports.__id = "buytype.5g";
    module.exports.isHyj = isHyj;
    module.exports.init = init;
});