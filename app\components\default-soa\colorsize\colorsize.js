define('MOD_ROOT/colorsize/colorsize', function(require, exports, module) {
    var Tools = require('MOD_ROOT/common/tools/tools');
    var ABTest = require('MOD_ROOT/common/tools/abtest');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var G = require("MOD_ROOT/common/core");
    var Login = require("JDF_UNIT/login/1.0.0/login");
    var Conf = require('PUBLIC_ROOT/conf');
    var purchasetab =  decodeURI(G.serializeUrl(location.href).param.purchasetab)
    require('JDF_UI/dialog/1.0.0/dialog')
    require('MOD_ROOT/ETooltips/ETooltips')

    var cfg           = pageConfig.product;
    var data          = cfg.colorSize || [];
    var stockNum      = cfg.stockSkuNum || 100 // 批量库存是否定制限制数量阀值
    var res           = {};
    var spliter       = '\u2299';
    var r             = {};
    var keys          = [];
    var selectedCache = [];
    var $el           = $('#choose-attrs');
    var $attr         = $el.find('[data-type]');
    var $res          = $('#choose-results');
    var selectedClass = 'selected';
    var disabledClass = 'disabled';
    var noStockClass  = 'no-stock';
    var attrHLClass   = 'item-hl-bg';
    // 维度超过 6 维，性能会有问题，直接回退，不检查路径是否可用&库存，匹配到就跳转
    var attrMax       = 6;
    var attrOverflow  = false;
    var debug         = false;

    if (/lessAttr/.test(location.href)) {
        attrMax = 2;
    }
    if (/selected/.test(location.href)) {
        debug = true;
    }

    /*
     * 解码 HTML 实体
     * encodeHTML('fdafbc&amp;')
     * > "fdafbc&"
     * encodeHTML('fdafbc&amp;&lt;&lt;')
     * > "fdafbc&<<"
     * encodeHTML('fdafbc&amp;&lt;&lt;fdas')
     * > "fdafbc&<<fdas"
    */
    function decodeHTML(str) {
        // HTML 实体转义
        // var HTML_ENTITY_ENCODE = {
        //     '&' : '&amp;',
        //     '<' : '&lt;',
        //     '>' : '&gt;',
        //     '\'': '&apos;',
        //     '"' : '&quot;'
        // };
        var HTML_ENTITY_DECODE = {
            '&amp;' : '&',
            '&lt;'  : '<',
            '&gt;'  : '>',
            '&apos;': '\'',
            '&quot;': '"',
            '&mdash;' : '—',
            '&phi;': 'φ',
            '&Phi;': 'Φ',
            '&deg;': '°' // 度
        };
        var res = str;
        for ( var k in HTML_ENTITY_DECODE ) {
            if (!HTML_ENTITY_DECODE.hasOwnProperty(k)) continue;
            res = res.replace(new RegExp(k, 'ig'), HTML_ENTITY_DECODE[k]);
        }
        return res;
    }

    /**
     * 数组是否存在元素
     * @param arr
     * @param item
     * @returns {boolean}
     */
    function hasItem(arr, item) {
        if (arr.indexOf) return arr.indexOf(item) > -1;

        for (var i = 0; i < arr.length; i++) {
            var curr = arr[i];
            if (item === curr) {
                return true
            }
        }
        return false
    }

    /**
     * 计算组合数据
     */
    function combineAttr(data, keys) {
        var allKeys = [];
        var result  = {};

        for (var i = 0; i < data.length; i++) {
            var item   = data[i];
            var values = [];

            for (var j = 0; j < keys.length; j++) {
                var key = keys[j];
                if (!result[key]) result[key] = [];
                if (!hasItem(result[key], item[key])) result[key].push(item[key]);
                // if (result[key].indexOf(item[key]) < 0) result[key].push(item[key]);
                values.push(item[key])
            }

            allKeys.push({
                path : values.join(spliter),
                sku  : item['skuId'],
                stock: item['stock'] ? 1 : 0
                // stock: 1
            });
        }
        return {
            result: result,
            items : allKeys
        }
    }

    function getAllKeys(arr) {
        var result = [];
        for (var i = 0; i < arr.length; i++) {
            result.push(arr[i].path)
        }
        return result
    }

    /**
     * 取得集合的所有子集「幂集」
     arr = [1,2,3]
     i = 0, ps = [[]]:
     j = 0; j < ps.length => j < 1:
     i=0, j=0 ps.push(ps[0].concat(arr[0])) => ps.push([].concat(1)) => [1]
     ps = [[], [1]]

     i = 1, ps = [[], [1]] :
     j = 0; j < ps.length => j < 2
     i=1, j=0 ps.push(ps[0].concat(arr[1])) => ps.push([].concat(2))  => [2]
     i=1, j=1 ps.push(ps[1].concat(arr[1])) => ps.push([1].concat(2)) => [1,2]
     ps = [[], [1], [2], [1,2]]

     i = 2, ps = [[], [1], [2], [1,2]]
     j = 0; j < ps.length => j < 4
     i=2, j=0 ps.push(ps[0].concat(arr[2])) => ps.push([3])    => [3]
     i=2, j=1 ps.push(ps[1].concat(arr[2])) => ps.push([1, 3]) => [1, 3]
     i=2, j=2 ps.push(ps[2].concat(arr[2])) => ps.push([2, 3]) => [2, 3]
     i=2, j=3 ps.push(ps[3].concat(arr[2])) => ps.push([2, 3]) => [1, 2, 3]
     ps = [[], [1], [2], [1,2], [3], [1, 3], [2, 3], [1, 2, 3]]
     */
    function powerset(arr) {
        var ps = [[]];
        for (var i = 0; i < arr.length; i++) {
            for (var j = 0, len = ps.length; j < len; j++) {
                ps.push(ps[j].concat(arr[i]))
            }
        }
        return ps
    }

    /**
     * 生成所有子集是否可选、库存状态 map
     */
    function buildResult(items) {
        var allKeys = getAllKeys(items);

        for (var i = 0; i < allKeys.length; i++) {
            var curr   = allKeys[i];
            var sku    = items[i].sku;
            var stock  = items[i].stock;
            var values = curr.split(spliter);

            // var allSets = getAllSets(values)
            var allSets = powerset(values);

            // 每个组合的子集
            for (var j = 0; j < allSets.length; j++) {
                var set = allSets[j];
                var key = set.join(spliter);

                if (!key) continue;

                var resultKey = decodeHTML(key);

                if (res[resultKey]) {
                    res[resultKey].skus.push(sku);
                    res[resultKey].stock += stock
                } else {
                    res[resultKey] = {
                        skus : [sku],
                        stock: stock
                    }
                }
            }
        }
    }

    function trimSpliter(str, spliter) {
        // ⊙abc⊙ => abc
        // ⊙a⊙⊙b⊙c⊙ => a⊙b⊙c
        var reLeft        = new RegExp('^' + spliter + '+', 'g');
        var reRight       = new RegExp(spliter + '+$', 'g');
        var reSpliter = new RegExp(spliter + '+', 'g');
        return str.replace(reLeft, '')
            .replace(reRight, '')
            .replace(reSpliter, spliter)
    }

    /**
     * 获取当前选中的属性
     */
    function getSelectedItem() {
        var result = [];
        $attr.each(function () {
            var $selected = $(this).find('.' + selectedClass);
            if ($selected.length) {
                result.push($selected.attr('data-value').toString())
            } else {
                result.push('')
            }
        });

        return result
    }

    function haveStock(state) {
        return state && state != -1 && state != 34 && state != 0
    }

    function appendStock(res) {
        var canAddCartNum = 0 // 可加车计算数初始值
        var resNum = 0 // 出参数量
        for (var i = 0; i < data.length; i++) {// data是pageConfig.product.colorSize全部值
            var item      = data[i];
            if (res && JSON.stringify(res) != "{}") {
                var currRes = res[item.skuId];

                if(currRes && currRes.StockState){// 判断是否有库存状态码
                    // stocks 接口限制sku个数超过 100 个返回空，如果为空默认认为商品有货
                    item['stock'] = currRes ? haveStock(currRes['StockState']) : true // 给pageConfig.product.colorSize内增加stock参数
                    resNum ++ // 没有状态码的时候 说明已经超过100个限制了，resNum最多100
                }

                if(currRes && currRes.canAddCart){// 判断是否有可加车字段
                    if(currRes['canAddCart'] == "1") // 可加车的sku数量，canAddCart=1说明可加车 
                    {
                        canAddCartNum++ 
                    }
                }
                
            } else {
                item['stock'] = true // // 接口无返回给pageConfig.product.colorSize内增加stock参数并都为true
            }
        }
        // 由于接口出参限制resNum和canAddCartNum最大数量最多100
        if(canAddCartNum != 0 && resNum == canAddCartNum && cfg.isColorAndSize){// isColorAndSize只有颜色和尺码的标识
            // $(".batch-purchase").show() // 都可加车展示批量切换功能
            $(".common-plan").show()
            $(".common-plan").find(".dzgm").show() // 展示定制购买
            $(".common-plan").find(".plgm").show() // 展示批量购买tab
            try {
                expLogJSON('smb_pc', 'Productdetail_PurchaseMethodExpo', '{"sku": ' + cfg.skuid + '}')
            } catch (e) {
                if (typeof console !== 'undefined') {
                    console.log('采购方式「单品购买」/「批量采购」曝光PV、UV曝光埋点错误');
                }
            }
        }else{
            // $(".batch-purchase").hide() // 有不可加车sku就隐藏批量tab
            $(".common-plan").find(".dzgm").hide() // 展示定制购买
            $(".common-plan").find(".plgm").hide()// 隐藏批量购买tab
            if(Tools.areAllChildrenHidden("#common-plan")){ // 判断tab都隐藏
                $(".common-plan").hide()
                Event.fire({
                    type: 'tabShow',
                })
            }
        }
        Tools.TabSwitch()// tab样式切换逻辑
        
    }

    function getStocks(callback) {
        var skus = [];
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            if (item.skuId) skus.push(item.skuId)
        }
        if (!skus.length) return
        var skuIdArr = chunk(skus, 100);// 超过100个sku 分批调用
        for(var i = 0; i < skuIdArr.length; i++){
            var skuArr = []
            for (var m = 0; m < skuIdArr[i].length; m++) {
                skuArr.push(skuIdArr[i][m])
            }
            getStockAjaxData(skuArr, callback)
        }
    }

    function getStockAjaxData(skuArr, callback){
        var body = JSON.stringify({
            type  : 'getstocks',
            area  : Tools.getAreaId().areaIds.join('_'),
        });
        var time = new Date().getTime()
        // 加固start
        var colorParm = {
            appid: 'item-v3',
            functionId: 'pc_stocks',
            client: 'pc',
            clientVersion: '1.0.0',
            t: time,//生成当前时间毫秒数
            body: body,
        }
        try{
            var colorParmSign =JSON.parse(JSON.stringify(colorParm))
            colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
            window.PSign.sign(colorParmSign).then(function(signedParams){
                colorParm['h5st']  = encodeURI(signedParams.h5st)
                try{
                    getJsToken(function (res) {
                        if(res && res.jsToken){
                            colorParm['x-api-eid-token'] = res.jsToken;
                        }
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                        getStocksData(colorParm);
                    }, 600);
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getStocksData(colorParm);
                    //烛龙上报
                    Tools.getJmfe(colorParm, e, "colorsize文件pc_stocks接口设备指纹异常", 250)
                }
            })
        }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getStocksData(colorParm);
            //烛龙上报
            Tools.getJmfe(colorParm, e, "colorsize文件pc_stocks接口加固异常", 250)
        }            
        // 加固end
        function getStocksData(colorParm){
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            // Event.addListener('onStockReady', function(data){// 定制商品需要判断是否都可加车，Bpin通过融合获取传给批量库存接口
                // var data = data.stock;
                // var isBpin = data && data.data && data.data.bPin ? "1" : "1";//是否Bpin TODO改
                // 单品才会调用原有库存，批量不会调用此处库存，调用custome.js内逻辑
                if(purchasetab && (purchasetab && purchasetab.indexOf("plgm") != -1)) return// url参数purchasetab不存在或者等于1的时候调用

                colorParm['type'] = 'getstocks';
                colorParm['skuIds'] = skuArr.join(',');
                colorParm['area'] = Tools.getAreaId().areaIds.join('_'); // 这三个参数是线上原本库存参数
                if(pageConfig && pageConfig.product.isColorAndSize && cfg.colorSize.length < parseInt(stockNum)){ // 是否仅有颜色尺码属性,当spu下sku小于100的时候传定制标识
                    colorParm['sceneFlag'] = '1' // 工服定制项目新增参数场景标，是否选中定制服务，首次进入1
                }
                $.ajax({
                    url: host + '/stocks',
                    data: colorParm,
                    dataType: 'json',
                    xhrFields: {
                        withCredentials: true,
                    },
                    headers: Tools.getUrlSdx(), 
                    success : function (r) {
                        try{
                            if (r) {// 批量接口有数据并且当前sku有库存数据返回
                                appendStock(r);
                                callback()
                            } else {// 批量库存接口限流或者返回异常，走托底逻辑，颜色尺码点击可以正常跳转
                                $("#choose-attrs .item").click(function(){
                                    var sku = $(this).attr("data-sku")
                                    location.href = '//'+ location.hostname +'/'+ sku +'.html' + location.search
                                })
                            }
                        }catch(e){
                            appendStock(r);
                            callback()
                        }
                    },
                    error: function (e) {
                        //烛龙上报
                        Tools.getJmfe(colorParm, e, "stocks批量库存接口调用error异常colorsizejs文件", 200)
                    }
                })
                
            // })
        }
    }


    function chunk(arr, size) {
        var newArray = [];
        for(var i=0; i<arr.length; i=i+size){
            newArray.push(arr.slice(i,i+size));
        }
        return newArray;
    };

    /**
     * 更新所有属性状态
     */
    function updateStatus(selected) {
        // setAllTitle();
        for (var i = 0; i < keys.length; i++) {
            var key       = keys[i];
            var data      = r.result[key];
            var copy      = selected.slice();
            for (var j = 0; j < data.length; j++) {
                var item = data[j];
                if (selected[i] == item) continue;
                copy[i] = item;
                var curr  = trimSpliter(copy.join(spliter), spliter);
                var $item = $attr.filter('[data-type="' + key + '"]').find('[data-value="' + item + '"]');

                // if (res[curr] && res[curr]['stock'] > 0) {
                copy[i]      = '「' + item + '」';
                var titleStr = copy.join('-');
                if (res[curr]) {
                    $item.removeClass(disabledClass);
                    var isIconLe = $item.find(".no-stock-icon").length
                    var isTipLe = $item.find(".no-stock-tip").length
                    var imgDom = $item.find("img")
                    var imgW = imgDom.width()
                    var imgH = imgDom.height()
                    if (res[curr].stock < 1) {
                        $item.addClass(noStockClass).attr('title', titleStr + ' 无货')
                        if(imgDom.length > 0 && imgW != imgH){
                            if(isIconLe == 0){
                                $item.append("<span class='no-stock-icon'></span>")
                            }
                        }else{
                            if(isTipLe == 0){
                                $item.append("<span class='no-stock-tip'>无货</span>")
                            }
                        }
                        
                    } else {
                        $item.removeClass(noStockClass);
                        if(imgDom.length > 0 && imgW != imgH){
                            if(isIconLe > 0){
                                $item.find(".no-stock-icon").remove()
                            }
                        }else{
                            if(isTipLe > 0){
                                $item.find(".no-stock-tip").remove()
                            }
                        }
                    }
                } else {
                    $item.addClass(disabledClass).attr('title', titleStr + ' 无此商品')
                }
            }
        }
    }

    /**
     * 正常属性点击
     */
    function handleUISelect($this) {
        $this.siblings().removeClass(selectedClass);
        $this.addClass(selectedClass)
    }

    function reverseSelect($this) {
        var isActive  = $this.hasClass(selectedClass);
        if (isActive) { // 点击的是选中的选项卡
            $this.removeClass(selectedClass);
        } else { // 点击的是未选中的选项卡
            $this.siblings().removeClass(selectedClass);
            $this.addClass(selectedClass);
            $this.parents(".p-choose").removeClass("item-hl-bg");
        }
    }

    /**
     * 无效属性点击
     */
    function handleDisableClick($this) {
        var $currAttr = $this.parents('[data-type]').eq(0);
        var idx       = $currAttr.data('idx');
        var type      = $currAttr.data('type');
        var value     = $this.attr('data-value');

        $this.removeClass(disabledClass);
        selectedCache[idx] = value;

        // 清空高亮行的已选属性状态（因为更新的时候默认会跳过已选状态）
        $attr.not($currAttr).find('[data-value]').removeClass(selectedClass);
        updateStatus(getSelectedItem());

        /**
         * 恢复原来已选属性
         * 遍历所有非当前属性行
         *   1. 与 selectedCache 对比
         *   2. 如果要恢复的属性存在（非 disable）且 和当前*未高亮行*已选择属性的*可组合*），高亮原来已选择的属性且更新
         *   3. 否则什么也不做
         */
        for (var i = 0; i < keys.length; i++) {
            var item  = keys[i];
            var $curr = $el.find('[data-type="' + item + '"]');
            if (item == type) continue;
            // console.log('Selected: %s | Key: %s | i: %s', JSON.stringify(selectedCache), item, i)

            var $lastSelected = $curr.find('[data-value="' + selectedCache[i] + '"]');

            // 缓存的已选属性没有 disabled (可以被选择)
            if (!$lastSelected.hasClass(disabledClass)) {
                $lastSelected.addClass(selectedClass)
                updateStatus(getSelectedItem())
            }
        }
    }

    /**
     * 不存在的属性点击时，设置禁用购买按钮样式
     */
    function onDisableClick() {
        cfg.addToCartBtn.disabled()
        if (cfg.baiTiaoFenQi) {
            cfg.baiTiaoFenQi.$btn.hide()
            cfg.baiTiaoFenQi.$el.hide()
        }
        $('#choose-service').hide()
    }
    function onDisableClear() {
        cfg.addToCartBtn.enabled()
        if (cfg.baiTiaoFenQi) {
            cfg.baiTiaoFenQi.$el.show()
        }
        $('#choose-service').show()
    }

    /**
     * 高亮当前属性区
     */
    function highLighAttr() {
        var isHighLigh = false;
        for (var i = 0; i < keys.length; i++) {
            var key   = keys[i];
            var $curr = $el.find('[data-type="' + key + '"]');
            if ($curr.find('.' + selectedClass).length < 1) {
                $curr.addClass(attrHLClass)
                isHighLigh = true;
                // 禁用“加入购物车”按钮和“打白条”按钮
                $("#InitCartUrl,#btn-baitiao,#InitCartUrl-mini").attr('href', '#none');
                // onDisableClick();
            } else {
                $curr.removeClass(attrHLClass)
            }
        }
        return isHighLigh;
    }

    function getItemSku(selected) {
        for (var j = 0; j < data.length; j++) {
            var d = data[j];
            var hasKey = true;

            for (var i = 0; i < keys.length; i++) {
                var key = keys[i];
                if (d[key] !== selected[i]) {
                    hasKey = false;
                    break;
                }
            }
            if (hasKey) {
                return d['skuId'];
            }
        }
        return false
    }

    function bindEvent() {
        function handleStateSelect($this) {
            var isDisable = $this.hasClass(disabledClass);

            if (isDisable) {
                handleDisableClick($this)
            } else {
                selectedCache[$this.parents('[data-type]').eq(0).data('idx')] = $this.val()
            }
            updateStatus(getSelectedItem());
            handleResult()
        }
        // 超限制维度，点击时不判断可选/无货状态，只在选择完所有属性后判定是否可以跳转
        function handleStatelessSelect() {
            var selectedCount = $el.find('.' + selectedClass).length;
            if (selectedCount >= keys.length) {
                var result = getSelectedItem();
                var sku = getItemSku(result);
                if (sku) {
                    goToPage(sku)
                } else {
                    showResult( '[' + result.join(' - ') + '] 不存在，请选择其它搭配')
                }
            }
        }


        if (Conf.get('GLOBAL.COLORSIZE.itemClickSignal')) {
            var dispatchEvent = function($this) {
                var isActive  = $this.hasClass(selectedClass);
                var isDisable = $this.hasClass(disabledClass);
                if ($(".choose-attr-clothes").length) { // 只有50%的流量支持反选
                    if (!isDisable) {
                        reverseSelect($this);
                        if (attrOverflow) {
                            handleStatelessSelect($this)
                        } else {
                            handleStateSelect($this)
                        }
                    }
                } else {
                    if (!isActive && !isDisable) {
                        handleUISelect($this); // 更新红框的选中状态
    
                        if (attrOverflow) {
                            handleStatelessSelect($this)
                        } else {
                            handleStateSelect($this)
                        }
                    }
                }
            };
        } else {
            var dispatchEvent = function($this) {
                if ($(".choose-attr-clothes").length) { // 只有50%的流量支持反选
                    reverseSelect($this);
                    if (attrOverflow) {
                        handleStatelessSelect($this)
                    } else {
                        handleStateSelect($this)
                    }
                } else {
                    var isActive  = $this.hasClass(selectedClass);
                    if (!isActive) {
                        handleUISelect($this); // 更新红框的选中状态
    
                        if (attrOverflow) {
                            handleStatelessSelect($this)
                        } else {
                            handleStateSelect($this)
                        }
                    }
                }
            };
        }

        $el.not(".choose-attr-clothes").undelegate().delegate('[data-value]', 'click', function () {
            dispatchEvent($(this))
        })

        $(".choose-attr-clothes").undelegate().delegate('[data-value]:not(".disabled,.no-stock")', 'click', function () {
            dispatchEvent($(this))
        })
    }
    function bindColorHover() {
        var $colors = $('#choose-attr-1');
        var $bigImg = $('#spec-img');
        var bWidth = $bigImg.attr('width')
        var bHeight = 0;
        var timer = null;

        if (cfg.cat[0] === 737) return false;

        if ($bigImg.attr('height')) {
            bHeight = $bigImg.attr('height')
        } else {
            var size = $bigImg.attr('data-origin').match(/s\d+x\d+/);
            if (size) {
                bHeight = size[0].replace(/s\d+x/i, '')
            }
        }

        $colors.bind('mouseleave', function () {
            timer = setTimeout(function () {
                $('#spec-img').attr('src', cfg.currentMainImage)
            }, 200)
        });
        $colors.delegate('.item', 'mouseenter', function () {
            var $img = $(this).find('img')
            var width = $img.attr('width')
            var height = $img.attr('height')
            var re = new RegExp('s' + width + 'x' + height, 'g')
            var reCC = new RegExp('cc_' + width + 'x' + height, 'g')

            clearTimeout(timer)

            if ($img.length) {
                var src = $img.attr('src');
                var newSrc = src
                    .replace(re, 's' + bWidth + 'x' + bHeight)
                    .replace(reCC, 'cc_' + bWidth + 'x' + bHeight)
                $('#spec-img').attr('src', newSrc)
            }
        })
    }

    // 从匹配表中查找更新非选中元素状态
    function checkExists() {
        $el.find('[data-value]').each(function () {
            var value = $(this).attr('data-value');
            if (!res[value] && !$(this).hasClass(selectedClass)) {
                $(this).addClass(disabledClass)
            }
        })
    }

    function goToPage(sku) {
        $('.crumb-wrap').attr('id', 'crumb-wrap');
        location.href = '//'+ location.hostname +'/'+ sku +'.html' + location.search + '#switch-sku'
    }
    function showResult(text) {
        $res.show().find('.dd').html(text)
    }
    function handleResult() {
        var result = getSelectedItem();
        var s      = [];

        for (var i = 0; i < result.length; i++) {
            var item = result[i];
            if (!!item) { s.push(item) }
        }

        if (s.length && s.length == keys.length) {
            var curr = res[s.join(spliter)];

            if (debug) {
                showResult(s.join(' - '))
            } else if (curr && cfg.skuid == curr.skus[0]) {
                onDisableClear();
            } else if (curr && cfg.skuid != curr.skus[0]) {
                goToPage(curr.skus[0]);
            }
        }
    }

    function getKeys() {
        $attr.each(function () {
            var type = $(this).data('type');
            if (type) {
                keys.push(type)
            }
        });
        if (keys.length > attrMax) {
            attrOverflow = true;
        }
    }

    function setTitle($el) {
        var title = $el.attr('data-value');
        if (title) $el.attr('title', title);
    }
    function setAllTitle() {
        $el.find('.item').each(function () {
            setTitle($(this))
        })
    }
    function reset() {
        res = {};
        keys = [];
        selectedCache = [];
        r = {};
        $el.find('[data-value]')
            .removeClass(disabledClass)
            .removeClass(noStockClass);

        $attr.removeClass(attrHLClass)
    }

    function exposure() {
        Tools.exposure({
            functionName: 'PC_Productdetail_Main',
            exposureData: ['mainskuid'],
            errorTips: '页面浏览曝光报错'
        })
        Tools.exposure({
            functionName: 'PC_Productdetail_Touchstone',
            exposureData: ['mainskuid', 'touchstone_expids'],
            errorTips: '试金石曝光报错'
        })
        Tools.exposure({
            functionName: 'PC_Productdetail_Specification_Expo',
            exposureData: ['mainskuid'],
            extraData: {
                spec_name: keys
            },
            errorTips: '选择规格曝光报错'
        })
        // 暂无点击埋点
        // $("#choose-attrs .item").click(function(){

        // })

    }

    function initColorSizeABTest() {
        cfg.__colorSizeABTest = new ABTest(Tools.getUUID(), 0.5);
        var version = cfg.__colorSizeABTest.isHitVersion();

        if (cfg.cat[0] === 1315 && version === 'A') {
            bindColorHover();
        }
    }

    function initAttrSelection() {
        if (!data) return false;

        reset();
        getKeys();
        bindEvent();
        initColorSizeABTest();

        if (!attrOverflow) {
            selectedCache = getSelectedItem();
            getStocks(function () {
                r = combineAttr(data, keys);
                buildResult(r.items);
                checkExists();
                updateStatus(getSelectedItem());
                if (debug) {
                    handleResult()
                }
                // bindEvent();//绑定点击颜色尺码事件
                if (/debug=res/.test(location.href)) {
                    console.log(res);
                    console.log(selectedCache);
                }
            })
        }
    }
    var SizeHelper = {
        init: function(cfg) {
            this.$el = $('#size-helper')
            this.cfg = cfg

            if (!this.$el.length) return

            this.get()
            this.bindEvent()
        },
        get: function() {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/sizeHelper/info',
                data: {
                    pin: readCookie('pin'),
                    wareId: this.cfg.mainSkuId,
                    appid: 'item-v3',
                    functionId: "pc_sizeHelper_info"
                },
                dataType: 'jsonp',
                success: $.proxy(this.handleData, this)
            })
        },
        bindEvent: function() {
            this.$el.delegate('a', 'click', $.proxy(this.openIframe, this))
        },
        openIframe: function() {
            var url = '//helper.jd.com/IDPlus/pchtml/size-layer.html?wareId=' + this.cfg.mainSkuId

            Login({
                modal: true,
                complete: function(r) {
                    if (r && r.Identity && r.Identity.IsAuthenticated) {
                        $('body').dialog({
                            type: 'iframe',
                            width: 700,
                            height: 520,
                            title: '尺码助手',
                            source: url
                        })
                    }
                }
            });

        },
        handleData: function(r) {
            if (!r) return
            this.$el.show()

            this.getSize(r.size)
        },
        getSize: function(size) {
            if (!size) return

            var s = size.substring(0, size.indexOf('/'))
            var $el = $('#choose-attr-2')

            if (/debug=size/.test(location.href)) {
                s = 'M'
            }
            var $tar = $el.find('[data-value="'+ s +'"]')

            if ($tar.length) {
                $tar.append('<i class="reco"></i>')
                this.setTips($tar)
            }
        },
        setTips: function($el) {
            $el.ETooltips({
                close: false,
                content: '你可能合适的尺码',
                pos: 'bottom',
                width: 120,
                zIndex: 10
            })
        }
    }


    function init(cfg) {
        if (!$el.length) return false;
        
        initAttrSelection()
        
        exposure()
        
        SizeHelper.init(cfg)
    }

    module.exports.highLighAttr = highLighAttr;
    // 以下方法供vehicle.js使用
    module.exports.initAttrSelection = initAttrSelection;
    module.exports.setData = function (properties) {
        data = properties;
    };
    module.exports.overrideGoToPageMethod = function(fn) {
        if (typeof fn === 'function') {
            goToPage = fn;
        }
    };
    module.exports.overrideGoToPageMethod.originMethod = goToPage;
    exports.init = init;
});
