define('MOD_ROOT/track/track', function(require, exports, module) {
    require('PLG_ROOT/jQuery.imgScroll');
    var tools = require('MOD_ROOT/common/tools/tools');
    var Recommend = require('MOD_ROOT/common/tools/recommend');

    /// "看了又看"模版字符串
    var template = '\
        <ul clstag="shangpin|keycount|product|kanleyoukan_2">\
            {for item in data}\
            <li data-clk="${item.clk}" \
                data-push="${pageConfig[skuHooks].push(item.sku)}">\
                <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                    <img height="150" width="150" alt="${item.t}" src="${pageConfig.FN_GetImageDomain(item.sku)}n1/s150x150_${item.img}">\
                    <div class="title">${item.t}</div> \
                    <p class="J-p2-${item.sku}">￥${item.jp}</p>\
                </a>\
            </li>\
            {/for}\
        </ul>';


     // “店长推荐”接口初始化
     function getShopRecommendData(callback) {
        var parameters = {
            skuId: pageConfig.product.skuid,
            venderId: pageConfig.product.venderId 
        };

        var body = JSON.stringify(parameters);

        tools.getJsTokenSign({
            body: body,
            appid: "item-v3",
            functionid: "pctradesoa_shopRecommend",
            message1: "book店长推荐接口设备指纹异常",
            message2: "book店长推荐接口加固异常",
            render: function (colorParm) {
                getRecommendData(colorParm,function(res){
                    callback(res)
                });
            }
        });
    }

    // “店长推荐”数据请求
    function getRecommendData(colorParm, callback) { 
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        $.ajax({
            url: host,
            data: colorParm,
            dataType: 'json',
            xhrFields: {
                withCredentials: true,
            }, 
            success: function(res) {
                callback(res)
            },
            error: function(e) {
                console.log('店长推荐数据请求错误',e)
            }
        });
    }

    /// “店长推荐”
    function renderRecommendMoudle($mount, data) {
        if (
            $mount.length &&
            data && 
            $.isArray(data.goodList) &&
            data.goodList.length
        ) {
            var __html = '\
            <ul>\
                {for item in data.goodList}\
                    {if item.extAttributes.sku_status!=0 && item.extAttributes.sku_status!=2 && item.extAttributes.sku_status!=10}\
                        <li class="fore${+item_index+1}" data-sku="${item.skuId}" onclick=\'log("dztj_pc","dztj_all_click",pageConfig.product.venderId, "${+item_index+1}","${item.skuId}",pageConfig.product.skuid, "${data.strategyId}")\'>\
                            <a href="${item.skuId|itemUrl}" title="${item.wname}" target="_blank" id="newdztj-${item.skuId}" style="position: relative;">\
                                <img width="150" height="150" alt="${item.wname}" class="" src="${item.skuId|imgDomain}n2/${item.imageurl}">\
                                <div class="title">${item.wname}</div>\
                                <p class="J-p2-${item.skuId}">￥</p>\
                            </a>\
                        </li>\
                    {/if}\
                {/for}\
            </ul>';

            try {
                $mount.html(__html.process({
                    data: data,
                    _MODIFIERS: tools.modifier
                }));

                // 渲染价格数据
                var skus = [];
                $mount.find('[data-sku]').each(function() {
                    skus.push($(this).data('sku'));
                });
                // tools.priceNum({
                //     skus: skus,
                //     $el: $mount
                // });
                //价格渲染
                tools.priceNumRecommend({
                    priceList: data.priceList,
                    $el: $mount
                });
                
                try{
                    var layerList = data.floatLayerList
                    if(layerList.length > 0){
                        tools.getPcSkuLayers(layerList, 150, 150, 'newdztj-', "2")
                    }
                }catch(e){
                    console.log("主图浮层初始化渲染",e)
                }

                // 显示店长推荐模块
                $mount.removeClass('hide').show();

                // try{
                //     if(skus && skus.length > 0){
                //         // 主图浮层初始化渲染
                //         tools.getMainPic(150, 150,"newdztj-","2",skus)
                //     }
                // }catch(e){
                //     console.log("主图浮层初始化渲染",e)
                // }

            } catch (err) {
                console && console.error(err);
            }
        } else {
            $mount.hide();
        }
    }

    function init(cfg) {
        var $track = $('#track');

        if ($track.length == 0) {
            return;
        }

        var $title = $('.track-tit h3', $track);
        var $el = $('.track-con', $track);
        var rid = $el.data('rid');

        function setScroll(hasData, r) {
            var $scroller = $track.find('.track-con');
            var $prev = $track.find('.J-prev');
            var $next = $track.find('.J-next');
            var isEle = (pageConfig.product.cat[0] == 737);
            $scroller.imgScroll({
                width: 150,
                height: 188,
                visible: isEle ? 2 : 3,
                showControl: false,
                step: isEle ? 2 : 3,
                direction: 'y',
                loop: true,
                prev: $prev,
                next: $next
            });
            try{
                if(hasData){
                    // 主图浮层初始化渲染
                    // var skuArrs = []
                    // for(i = 0;i < r.data.length; i++){
                    //     skuArrs.push(r.data[i].sku)
                    // }
                    // tools.getMainPic(150, 150,"zbklyk-","2",skuArrs)
                    var layerList = r.floatLayerList
                    if(layerList.length > 0){
                        tools.getPcSkuLayers(layerList, 150, 150,"zbklyk-","2")
                    }
                }
            }catch(e){
                console.log("主图浮层初始化渲染",e)
            }
        }
        
        /// "看了又看"
        function recommendFacade() {
            $title.html('看了又看');
            new Recommend({
                $el: $el,
                skuHooks: 'SKUS_track',
                template: template,
                ext: {
                    title: '看了又看',
                    imgWidth : 150,
                    imgHeight: 150,
                    divId: "zbklyk-"
                },
                param: {
                    p: rid,
                    sku: cfg.skuid,
                    lim: 15,
                    ck: 'pin,ipLocation,atw,aview',
                    pin: ''
                },
                callback: setScroll
            });
            if($title.length > 0){
                var skuId = pageConfig && pageConfig.product && pageConfig.product.skuid || '-';
                try {
                    expLogJSON('smb_pc', 'exposure_kanleyoukan', '{"sku": ' + skuId + '}')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('曝光埋点错误');
                    }
                } 
            }
            
        }

        if (cfg.cat[0] == 737) {  // 家用电器一级分类
            // getShopRecommendData().
            // done(function (res) {
            //     if (
            //         res && 
            //         $.isPlainObject(res.shopRec) && 
            //         !$.isEmptyObject(res.shopRec)
            //     ) {
            //         $title.html('店长推荐');
            //         renderRecommendMoudle($el, res.shopRec);
            //         setScroll();
            //     } else {
            //         recommendFacade(); // "看了又看"
            //     }
            // }).
            // fail(function (xhr, status, error) {
            //     console && console.error('店长推荐模块数据调用失败，具体如下：\n' + error);
            //     recommendFacade(); // "看了又看"
            // });
            // getShopRecommendData(function(res){
            //         if (
            //             res && 
            //             $.isPlainObject(res.shopRec) && 
            //             !$.isEmptyObject(res.shopRec)
            //         ) {
            //             $title.html('店长推荐');
            //             renderRecommendMoudle($el, res.shopRec);
            //             setScroll();
            //         } else {
            //             recommendFacade(); // "看了又看"
            //         }
            // })
        } else {
            recommendFacade(); // "看了又看"
        }
    }

    module.exports.init = init;
    module.exports.__id = 'track';
});
