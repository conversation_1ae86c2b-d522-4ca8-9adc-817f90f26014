
                    @mixin sprite-arrow {
                        width: 27px;
                        height: 61px;
                        background-image: url(i/__sprite.png);
                        background-position: -135px -0px;
                    }
                    @mixin sprite-clock {
                        width: 17px;
                        height: 18px;
                        background-image: url(i/__sprite.png);
                        background-position: -38px -135px;
                    }
                    @mixin sprite-deliver {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -90px -90px;
                    }
                    @mixin sprite-do {
                        width: 21px;
                        height: 21px;
                        background-image: url(i/__sprite.png);
                        background-position: -135px -82px;
                    }
                    @mixin sprite-done {
                        width: 21px;
                        height: 21px;
                        background-image: url(i/__sprite.png);
                        background-position: -135px -61px;
                    }
                    @mixin sprite-item {
                        width: 16px;
                        height: 15px;
                        background-image: url(i/__sprite.png);
                        background-position: -162px -0px;
                    }
                    @mixin sprite-pay {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -90px -0px;
                    }
                    @mixin sprite-person {
                        width: 13px;
                        height: 14px;
                        background-image: url(i/__sprite.png);
                        background-position: -162px -82px;
                    }
                    @mixin sprite-person2 {
                        width: 19px;
                        height: 13px;
                        background-image: url(i/__sprite.png);
                        background-position: -72px -135px;
                    }
                    @mixin sprite-pingou {
                        width: 18px;
                        height: 18px;
                        background-image: url(i/__sprite.png);
                        background-position: -20px -135px;
                    }
                    @mixin sprite-seckill {
                        width: 20px;
                        height: 20px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -135px;
                    }
                    @mixin sprite-seckilling {
                        width: 20px;
                        height: 20px;
                        background-image: url(i/__sprite.png);
                        background-position: -135px -103px;
                    }
                    @mixin sprite-share {
                        width: 15px;
                        height: 14px;
                        background-image: url(i/__sprite.png);
                        background-position: -162px -15px;
                    }
                    @mixin sprite-step1 {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -45px -90px;
                    }
                    @mixin sprite-step2 {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -90px;
                    }
                    @mixin sprite-step3 {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -45px -45px;
                    }
                    @mixin sprite-time {
                        width: 17px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -55px -135px;
                    }
                    @mixin sprite-top1 {
                        width: 16px;
                        height: 13px;
                        background-image: url(i/__sprite.png);
                        background-position: -162px -55px;
                    }
                    @mixin sprite-top2 {
                        width: 16px;
                        height: 13px;
                        background-image: url(i/__sprite.png);
                        background-position: -162px -42px;
                    }
                    @mixin sprite-top3 {
                        width: 16px;
                        height: 13px;
                        background-image: url(i/__sprite.png);
                        background-position: -162px -29px;
                    }
                    @mixin sprite-yy-step1 {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -0px;
                    }
                    @mixin sprite-yy-step2 {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -45px -0px;
                    }
                    @mixin sprite-yy-step3 {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -45px;
                    }
                    @mixin sprite-yy-step4 {
                        width: 45px;
                        height: 45px;
                        background-image: url(i/__sprite.png);
                        background-position: -90px -45px;
                    }
                    @mixin sprite-yy {
                        width: 14px;
                        height: 14px;
                        background-image: url(i/__sprite.png);
                        background-position: -162px -68px;
                    }