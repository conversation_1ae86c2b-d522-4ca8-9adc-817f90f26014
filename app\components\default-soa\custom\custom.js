define("MOD_ROOT/custom/custom", function (require, exports, module) {
  var Tools = require("MOD_ROOT/common/tools/tools");
  var G = require("MOD_ROOT/common/core");
  var Event = require('MOD_ROOT/common/tools/event').Event;
  var buybtn = require('MOD_ROOT/buybtn/buybtn').addToCartBtn;
  require('MOD_ROOT/ETooltips/ETooltips')

  var customtabid          = decodeURI(G.serializeUrl(location.href).param.purchasetab)// url上区分单品和批量
  var InitCartUrlBtn       = $("#InitCartUrl,#InitCartUrl-mini"); // 旧购物车按钮
  var InitCartUrlCustomBtn = $("#InitCartUrlCustom"); // 新购物车按钮
  var chooseCustom         = $("#choose-custom") // 工服定制楼层容器
  var btnGocustom          = $("#btn-gocustom") // 去定制按钮
  var customUpdate         = $(".custom-update") // 工服定制修改按钮
  var customCancel         = $(".custom-cancel") // 工服定制取消按钮
  var stylesList           = $("#stylesList") // 批量下所有尺码的最外围容器
  var cfg                  = pageConfig && pageConfig.product
  var skuid                = cfg.skuid || "" // 当前skuid
  var countNum             = cfg.countNum || "" // 当前总数量
  var colorSizeData        = cfg.colorSize || []
  var stockNum             = cfg.stockSkuNum || 100 // 批量库存是否定制限制数量阀值
  var customtabidNum       = customtabid && customtabid.indexOf("plgm") != -1  // url参数customtab只有等于批量购买的时候调用
  var customtabFirst       = customtabid && customtabid.indexOf("dzgm") != -1  // url参数customtab只有等于定制购买的时候调用
  var PROTOCOL_REGEXP      = new RegExp('^' + location.protocol);
  // 定制器弹层
  var dialog;
  // 默认定制器id为0
  cfg.customId = "0"

  cfg.allSkuIdNo = false //批量下所有sku不可定制标

  //当前颜色下所有尺码sku
  var skuData = []
  $("#stylesList li").each(function(){
    skuData.push($(this).attr("data-sku"))
  })

  //当前页面所有颜色尺码sku
  var allSkus = [];
  for (var i = 0; i < colorSizeData.length; i++) {
    var item = colorSizeData[i];
    if (item.skuId) allSkus.push(item.skuId)
  }

  //////////  判断购买按钮的链接是否带有请求参数 //////////////
  function hasQueryParams(url, G) {  // 非通用函数
    // 如果url缺少协议部分填充协议，因为`serializeURL`方法序列化的是一个严格的url。
    if (!PROTOCOL_REGEXP.test(url)) {
      url = location.protocol + url
    }
    try {
      var urlDict = G.serializeURL(url);
      var query = urlDict.query;
      for (var k in query) {
        if (query.hasOwnProperty(k)) {
          return true;
        }
      }
      return false;
    } catch (error) {
      console && console.log(error);
      return false;
    }
  }
  var isBuyUrlHasQueryParams = hasQueryParams(G.originBuyUrl, G);

  function modifyBuyUrlParam(key, value, G) {
    // var href = buybtn.$el.attr('href');
    var reservationBtn=$('#btn-reservation,#btn-reservation-mini')
    var href = buybtn.$el.length ? buybtn.$el.attr('href') : reservationBtn.attr('href');
    var hasProtocol = PROTOCOL_REGEXP.test(href);
    var dict = {
      query: {}
    };

    if (!hasProtocol) {
      href = location.protocol + href;
    }

    if (G.is('', value)) {
      dict.query[key] = value;
    } else if (G.is([], value)) {
      dict.query[key] = value.join(',');
    }

    try {
      href = G.modifyURL(href, dict, true);
    } catch (error) {
      console && console.log(error);
    }

    if (!hasProtocol) {
      href = href.replace(PROTOCOL_REGEXP, '');
    }

    if (isBuyUrlHasQueryParams) {
      buybtn.enabled(href);
    }
    if(reservationBtn.length){
      reservationBtn.attr('href', href);
    }

    pageConfig.product.cartBuyUrlParam = href // 全局购物车链接参数

  }
  ////////////////////////////////////////////////////////////

  var customObj = {
    init: function () {
      var _this = this

      // 根据url参数customtab判断命中单品还是批量
      _this.loadPage()

      // 定制服务楼层展示
      _this.showCustom()

      // 绑定尺码加减和输入事件
      _this.bindEvent()

      // 切换单个和批量tab
      _this.changeTab();

      // 和定制器通信
      if(chooseCustom && chooseCustom.length > 0){// 有定制服务楼层才会通信
        _this.getMessage()
      }
      // 选择和修改工服定制
      _this.chooseCustomInfo()
    },
    loadPage: function () { // 根据url参数customtab判断命中单品还是批量
      if(customtabidNum){// 批量场景tab下
        $("#choose-attrs-batch").show()// 批量尺码容器展示
        $("#choose-attrs").remove()// 单品尺码容器清除
        $(".choose-amount").remove()// 单品购物车数量清除
        $("#size-helper,#choose-service,#choose-serviceyc,#btn-inventory").remove() //批量状态屏蔽尺码助手、增值保障、京选服务、采购清单按钮
        $(".common-plan .plgm").addClass("curr").show().siblings().removeClass("curr")// 批量按钮的切换样式，命中采购批发
        $("#choose-attr-1 .item").click(function(){// 批量场景下颜色都可点击跳转，没有无效状态
          var sku = $(this).attr("data-sku")
          location.href = '//'+ location.hostname +'/'+ sku +'.html' + location.search
        })

          // $('.purchase-tab').each(
          //     function(index,item)
          //     {if(index===1){
          //       $(item).addClass('b')
          //       console.log(tabType,150150)
          //     }})
          customObj.getStocksInfo("1")// 批量场景下 调用批量库存
          customObj.skusFun()// 批量场景下 超过3个尺码展示查看更多按钮
          customObj.batchAddToCart()// 购物车链接通用组装方法
      }else if(customtabFirst){
        $(".common-plan .dzgm").addClass("curr").show().siblings().removeClass("curr")// 批量按钮的切换样式，命中单品购买
        $("#choose-attrs-batch").remove()// 批量尺码容器隐藏
        $("#choose-attrs").show()// 单品尺码容器显示
      }

      // 校验单品是否有库存，是否屏蔽批量tab
      // Event.addListener('onStockReady', function(data){
      //   var data = data.stock;
      //   var stockInfo = data && data.data && data.data.stockInfo // 融合库存信息
      //   if(stockInfo && stockInfo.stockState == 34){// 无货状态
      //     if(customtabidNum){// 批量场景tab下
      //       var tipDialog = customObj.dialogTip("当前商品不支持批量")
      //       // 批量状态下，不支持批量的时候跳转到单品
      //       setTimeout(function(){
      //         tipDialog.close()
      //         location.href = G.modifyURL(location.href, {
      //           query: {
      //             customtab: '1'
      //           }
      //         })
      //       }, 1500);
      //       $(".batch-purchase").hide()// 批量切换隐藏，不可批量
      //     }else{
      //       $(".batch-purchase").hide()// 批量切换隐藏，不可批量
      //     }
      //   }
      // })
    },
    getMessage: function () {// 监听定制器确认和取消
      window.addEventListener('message',function(event){
        try {
          var originUrl =  window.location.protocol + "//customized-b-confirm-pro.pf.jd.com" // iframe的正式域名
          if (pageConfig.product && pageConfig.product.colorApiDomain == '//api.jdtest.net') {
            originUrl = window.location.protocol + "//customized-b-confirm-95069.pf.jdtest.net" // iframe的测试域名
          }
          if(event && event.origin == originUrl){ //信任服饰定制的消息来源
            // var customCheck = $("#choose-custom .item")// 工服定制主按钮
            var obj = event && event.data && JSON.parse(event.data)
            //判断是否有定制器id
            if(obj && obj.customId && obj.customId.length > 0){
              cfg.customId = obj.customId // 更新定制器id
              cfg.giftService = "1" // 融合接口定制器选中1
              // cfg.sceneFlag = "2"// 选中定制器场景标2
              Event.fire({
                type: 'onNumChange',
                giftServiceIsSelected: "1"
              });// 调用融合接口
              // customObj.getStocksInfo("2")// 调用批量库存
              // customCheck.find("a").html('已定制 ¥'+obj.servicePrice)// 更新工服定制楼层价格
              // customCheck.addClass("custom-check")// 更新工服定制楼层选中状态
              // customUpdate.removeClass("hide")// 展示修改按钮
              // customCancel.removeClass("hide")// 展示取消按钮
              btnGocustom.hide()// 去定制按钮隐藏

            }else{// 第一次点击定制器并且直接取消定制
              cfg.customId = "0" // 清空定制器id
              cfg.giftService = "0"// 融合接口定制器反选0
              // cfg.sceneFlag = "3"// 反选定制器场景标3
              Event.fire({
                type: 'onNumChange',
                giftServiceIsSelected: "0"
              });// 调用融合接口
              // customObj.getStocksInfo("3")// 调用批量库存
              // customCheck.removeClass("custom-check")// 更新工服定制楼层选中状态
              // customUpdate.addClass("hide")// 隐藏修改按钮
              // customCancel.addClass("hide")// 隐藏取消按钮
              btnGocustom.show()// 去定制按钮展示
            }
            customObj.batchAddToCart()// 更新购物车按钮链接
            dialog.close()// 关闭定制器弹框
          }else{
            //烛龙上报
            Tools.getJmfe({functionId:"message"}, event, "不信任的消息来源", 231)
          }

        } catch (error) {
          //烛龙上报
          Tools.getJmfe({functionId:"message"}, event, "custom文件message交互数据有问题", 230)
        }
      })
    },
    bindEvent: function () { // 绑定尺码加减和输入事件
      $("body").delegate('.i-add', 'click', function(e) {// 点击增加数量
        customObj.addCount(e)
      })
      $("body").delegate('.i-reduce', 'click', function(e) {// 点击减少数量
        customObj.reduceCount(e)
      })

      $("body").delegate('.ipt-count', 'keyup', function(e) {// 输入数量
        customObj.inputKeyupHandler(e)
      });
    },
    checkAllService: function (listNum) { // 定制服务 查看全部
      var customDownBtn = chooseCustom.find(".custom-down")// 查看全部按钮
      if(listNum > 4){
        customDownBtn.show()
        customDownBtn.click(function(){
          $(this).parents().find("ul").addClass("down");
          $(this).hide()
        });
      }else{
        customDownBtn.hide()
      }
    },
    cancelCustomInfo: function () {
      var html =
          '\
          <p><i></i></p>\
          <p style="text-align: left;;font-size: 18px;font-weight: 600;color: rgba(51, 51, 51, 1);">取消定制吗？</p>\
          <p style="padding: 10px 0px 16px;text-align: left;font-size: 14px; color: rgba(102, 102, 102, 1);">取消后，已定制内容将全部删除</p>\
          <div class="btn" style="text-align: right;font-size: 14px;height: 34px;line-height: 32px;">\
              <a href="#none" onclick="$.closeDialog()" style="border-radius: 6px; padding: 0 12px; border: 1px solid rgba(194, 196, 204, 1);background-color: #ffffff;color: rgba(26, 26, 26, 1);display: inline-block;">我在想想</a>\
              <a href="#none" id="cancelCustomBtn" style="border-radius: 6px; padding: 0 12px; background: linear-gradient(90deg, #FF475D 0%, #FF0F23 100%);color: #fff;display: inline-block;">立即取消</a>\
          </div>'

      $('body').dialog({
        width: 372,
        title: '',
        height: 'auto',
        padding: 0,
        type: 'text',
        maskClose: true,
        source: html,
        onReady: function () {
          $("#cancelCustomBtn").click(function(){
            customObj.confirmCustom()
            chooseCustom.find(".dd").removeClass("after-custom")
          })
        }
      })
    },
    confirmCustom: function () {
      cfg.customId = "0"
      cfg.giftService = "0"
      cfg.allSkuIdNo = false// 隐藏 抱歉，此商品不支持定制购买！

      Event.fire({
        type: 'onNumChange',
        giftServiceIsSelected: '0'
      });
      // customObj.getStocksInfo("3")// 调用批量库存
      customUpdate.addClass("hide") // 隐藏工服修改按钮
      customCancel.addClass("hide") // 隐藏工服取消按钮
      btnGocustom.show()// 去定制按钮展示
      customObj.batchAddToCart()
      $.closeDialog()// 关闭弹层

    },
    changeTab: function () {// 单品和批量Tab切换跳转
      $(".common-plan .purchase-tab").click(function(){
        var $this = $(this)
        var type = $this.attr("data-type")
        if (type == "gfgm") {// 官方购买
          location.href = G.modifyURL(location.href, {// 跳转批量tab下
            query: {
              purchasetab: 'gfgm'
            }
          })

        } else if(type == "yjhx") {// 以旧换新
          location.href = G.modifyURL(location.href, {
            query: {
              purchasetab: 'yjhx'
            }
          })
        }else if(type == "dzgm") {// 定制购买
          location.href = G.modifyURL(location.href, {
            query: {
              purchasetab: 'dzgm'
            }
          })
        } else if(type == "plgm") {//批量定制
          location.href = G.modifyURL(location.href, {
            query: {
              purchasetab: 'plgm'
            }
          })
        } else if(type == "qyjhg") {//企业计划购
          location.href = G.modifyURL(location.href, {
            query: {
              purchasetab: 'qyjhg'
            }
          })
        }
        var List = []
        var arr = ["gfgm","yjhx","dzgm","plgm","qyjhg"]
        $('.purchase-tab').map(function(index, element) {
          if ( $(element).css('display') === 'block') {
            List.push({tabName:$(element)[0].innerText,tabIndex:index})
          }
        }).get()
        // let tabIndex = arr.findIndex(function (item){
        //  return  item === type
        // })
        try {
          Tools.landmine({
            functionName: 'PC_Productdetail_PurchaseMethodTab_Click',
            exposureData: ['mainskuid'],
            extraData: {
              tabName: type,
              tabIndex:arr.findIndex(function (item){
                return item === type
              })
            },
            errorTips: '购买方式楼层-tab点击-上报错误'
          })
        }catch (error) {
          console.log(error)
        }
      })
    },
    chooseCustomInfo: function () {// 点击工服定制按钮和修改 
      var url = "https://ling-clothing-customizer-pc-pro.pf.jd.com" // 线上环境定制器
      if(pageConfig.product && pageConfig.product.colorApiDomain == '//api.jdtest.net'){
        url = "https://ling-clothing-customizer-pc-94630.local-pf.jd.com" // 测试环境定制器
      }
      if((chooseCustom && chooseCustom.length > 0) && (btnGocustom && btnGocustom.length > 0)){ // 工服定制楼层和去定制按钮
        btnGocustom.click(function(){ // 去定制
          var sourceUrl = url + "?skuId=" + skuid + "&source=item-pc&skuNum=" + (cfg.countNum || "1")
          if(!$(this).hasClass("btn-disable-new")){
            dialog = $('body').dialog({
              width: window.innerWidth * 90 / 100, // 获取窗口的宽90%高80%
              height: window.innerHeight * 80 / 100,
              overflowY: 'scroll',
              title: '定制服务',
              type: 'iframe',
              autoIframe: false,
              source: sourceUrl,
              onReady: function() {
              }
            });
          }else{
            var tipDialog = customObj.dialogTip("数量不能为0")
            setTimeout(function(){
              tipDialog.close()
            }, 1500);
          }

          try {
            var selectedSkuid = Tools.getSelectedSkuid()
            Tools.otherBtnClick($(this).text(), selectedSkuid)
            log('smb_pc', 'Productdetail_ButtonToCustomize', '{"skuid": ' + skuid + '}')
          } catch (e) {
            if (typeof console !== 'undefined') {
              console.log('去定制点击埋点错误');
            }
          }
          
        })

        customCancel.click(function(){ // 取消定制按钮
          customObj.cancelCustomInfo()
          try {
            log('smb_pc', 'Productdetail_CancelCustomize', '{"skuid": ' + skuid + '}')
          } catch (e) {
            if (typeof console !== 'undefined') {
              console.log('去定制点击埋点错误');
            }
          }
        })
      }
      // 点击工服定制按钮旁边修改 
      customUpdate.click(function(){
        var updateUrl = url+ "?skuId=" + skuid + "&customId=" + cfg.customId + "&source=item-pc&skuNum=" + cfg.countNum || "1" // 修改需要带之前选中的定制id
        dialog = $('body').dialog({
          width: window.innerWidth * 90 / 100, // 获取窗口的宽90%高80%
          height: window.innerHeight * 80 / 100,
          title: '定制服务',
          type: 'iframe',
          autoIframe: false,
          source: updateUrl,
          onReady: function() {
          }
        });
        try {
          log('smb_pc', 'Productdetail_ReviseCustomize', '{"skuid": ' + skuid + '}')
        } catch (e) {
          if (typeof console !== 'undefined') {
            console.log('点击修改埋点错误');
          }
        }
      })
    },
    addCount: function (e) { // 批量场景下 尺码加+数量
      var _this = $(e.target)
      var $this = _this.parent().find(".ipt-count")
      var maxBuyNum = $this.data("max-num") || "200" // 最大限购数量
      var minBuyNum = $this.data("min-num") || "0"  // 最小限购数量 
      var reduceDisabled =  _this.parent().find(".i-reduce") // 减按钮
      var inputVal = parseInt($this.val());// 具体值
      var tipColor = _this.parents(".content").find(".tip-color") // 红字提示容器
      var type = tipColor.attr("data-type") // 不可定制、无货、无此商品标识
      if(!type || (type && type == "0")){
        if (!_this.hasClass("disabled")) { // 增加按钮可点击状态
          if (inputVal < maxBuyNum) {
            inputVal++;
          }
        }
        if (_this.hasClass("disabled")) {// 增加按钮禁用状态
          var content = "最多购买"+maxBuyNum+"件"
          tipColor.removeClass("hide").html(content)
          setTimeout(function(){
            tipColor.addClass("hide").html('')
          }, 1000);
        }
        // 改变数量通用逻辑判断
        customObj.changeNum($this, inputVal, minBuyNum, maxBuyNum, reduceDisabled, _this)
      }
    },
    reduceCount: function (e) {// 批量场景下 尺码减-数量
      var _this = $(e.target)
      var $this = _this.parent().find(".ipt-count") // 输入框
      var minBuyNum = $this.data("min-num") || "0"  // 最小限购数量
      var maxBuyNum = $this.data("max-num") || "200" // 最大限购数量
      var inputVal = parseInt($this.val());// 输入框值
      var addDisabled = _this.parent().find(".i-add") // 加按钮
      var tipColor = _this.parents(".content").find(".tip-color") // 红字提示容器
      var type = tipColor.attr("data-type") // 不可定制、无货、无此商品标识
      if(!type || (type && type == "0")){
        if (_this.hasClass("disabled") || inputVal==minBuyNum) {
          if(minBuyNum > 1){
            var content = "最小购买"+minBuyNum+"件"
            tipColor.removeClass("hide").html(content)
            setTimeout(function(){
              tipColor.addClass("hide").html('')
            }, 1000);
          }
          $this.val("0");
          _this.addClass("disabled");
          addDisabled.removeClass("disabled");
          customObj.batchAddToCart()
        }else{
          if (inputVal >= minBuyNum) {
            inputVal--;
          }
          customObj.changeNum($this, inputVal, minBuyNum, maxBuyNum, _this ,addDisabled)
        }
      }

    },
    inputKeyupHandler: function (e) {// 批量场景下 尺码输入数量
      var $this =  $(e.target)
      var minBuyNum = $this.data("min-num") || "0" // 最小限购数量
      var maxBuyNum = $this.data("max-num") || "200" // 最大限购数量
      var reduceDisabled =  $this.parents(".content").find(".i-reduce") // 减按钮
      var addDisabled =  $this.parents(".content").find(".i-add") // 加按钮
      var tipColor = $this.parents(".content").find(".tip-color") // 红字提示容器
      var val = parseInt($this.val()) // 输入的值

      if(minBuyNum > val){ // 输入数量小于最小限购数量或者输入空格
        var content = "最小购买"+minBuyNum+"件"
        tipColor.removeClass("hide").html(content)
        setTimeout(function(){
          tipColor.addClass("hide").html('')
        }, 1000);
        $this.val("0");// 输入框置0
      }else if(val >= maxBuyNum){// 输入的值大于等于最大限购数量
        var content = "最多购买"+maxBuyNum+"件"
        tipColor.removeClass("hide").html(content)
        setTimeout(function(){
          tipColor.addClass("hide").html('')
          $this.val(maxBuyNum);
          reduceDisabled.removeClass("disabled");
          addDisabled.addClass("disabled");
        }, 1000);
      }
      customObj.changeNum($this, val, minBuyNum, maxBuyNum, reduceDisabled, addDisabled)
    },
    changeNum: function($this, val, minBuyNum, maxBuyNum, reduceDisabled, addDisabled){// 批量场景下 尺码改变数量通用方法
      if (!$this.hasClass("disabled")) {
        $this.val(
            $this
                .val()
                .replace(/[^0-9]/g, "") &&
            parseInt(
                $this
                    .val()
                    .replace(/[^0-9]/g, "")
            ) &&  $this
                .val()
                .replace(/^0+/gi, "")
        );

        if (val > 0 && val < minBuyNum) { // 输入的值大于0并且小于最小限购数量
          $this.val(minBuyNum);// 展示最小数量
          reduceDisabled.removeClass("disabled");
          addDisabled.removeClass("disabled");
        } else if (val > minBuyNum && val < maxBuyNum) { // 区间内展示用户输入数量
          $this.val(val);
          reduceDisabled.removeClass("disabled");
          addDisabled.removeClass("disabled");
        } else if (val >= maxBuyNum) { // 展示最大数量
          $this.val(maxBuyNum);
          reduceDisabled.removeClass("disabled");
          addDisabled.addClass("disabled");
        }else if(isNaN($.trim(val)) || val == "0") {
          $this.val("0");// 输入框置0
          reduceDisabled.addClass("disabled");
          addDisabled.removeClass("disabled");
        }else{
          $this.val(val);
          reduceDisabled.removeClass("disabled");
          addDisabled.removeClass("disabled");
        }
        customObj.batchAddToCart()
      }
    },
    skusFun: function () {// 批量场景下 超过3个尺码展示查看更多按钮
      var stylesCount = $("#stylesList > li[filter]").length;
      if( stylesCount > 3){ // 超过3个尺码
        $(".show-or-hide-other-styles i").css("display", "inline-block")
        $("#stylesList > li[filter]:eq(2)").nextAll().hide()// 尺码超出3个其余隐藏
        $(".show-or-hide-other-styles .drop-down").bind("click", function(){ // 点击查看全部按钮
          $("#stylesList > li[filter]:eq(2)").nextAll("li[filter]").show()
          $(".show-or-hide-other-styles .drop-down").css("display", "none")
        })
      }else{
        $(".show-or-hide-other-styles .drop-down").css("display", "none") // 小于3个不展示查看全部按钮
      }
      stylesList.css("height", "auto")
    },
    batchAddToCart: function () {// 批量场景下 加入购物车按钮链接的拼接逻辑
      var isNotXnztLen = $("#stylesList > li").length,// 当前颜色下尺码的个数
          isNotXnztCountArr = [],
          isNotXnztSkusArr = [],
          allNum = 0;
      for (var i = 0; i < isNotXnztLen; i++) { // 遍历颜色下所有尺码下不为0的情况
        if ($("#stylesList> li").eq(i).find(".ipt-count").val() > 0) {
          isNotXnztCountArr.push(parseInt($("#stylesList > li").eq(i).find(".ipt-count").val())); // 遍历颜色下所有尺码下不为0的值
          isNotXnztSkusArr.push($("#stylesList > li").eq(i).data("sku"));// 遍历颜色下所有尺码下不为0的商品sku
          allNum = allNum + parseInt($("#stylesList > li").eq(i).find(".ipt-count").val())
        }
      }

      var batchAddToCartParams = {
        pids: isNotXnztSkusArr.join(",") || "",// 购物车批量商品sku
        pnums: isNotXnztCountArr.join(","),// 购物车批量数量
        type: 1,// 购物车默认值
        customGiftInfoId: cfg.customId || ""// 定制器ID
      };

      //批量tab下尺码选择的时候
      if(customtabidNum){ // 批量场景下
        InitCartUrlBtn.remove()// 删除主站原有的购物车按钮
        if (isNotXnztSkusArr.length == 0){//尺码数量是否为0
          InitCartUrlCustomBtn.show().addClass("btn-disable-new");// 新购物车按钮禁止点击样式
          if(cfg.customId == "0"){ // 无定制器id的时候
            btnGocustom.addClass("btn-disable-new");// 去定制按钮禁止点击样式
          }
          InitCartUrlCustomBtn.attr('href','#none');// 新购物车按钮链接清空
          InitCartUrlCustomBtn.click(function(){// 新购物车点击事件
            if($(this).hasClass("btn-disable-new")){
              var tipDialog = customObj.dialogTip("数量不能为0")
              setTimeout(function(){
                tipDialog.close()
              }, 1500);
            }
          })
        } else {//尺码数量是否为0
          InitCartUrlCustomBtn.show().removeClass("btn-disable-new");// 新购物车按钮放开点击样式
          if(cfg.customId == "0"){ // 无定制器id的时候
            btnGocustom.removeClass("btn-disable-new");// 去定制按钮放开点击样式
          }
          var url = "//cart.jd.com/gate.action?pids=" + batchAddToCartParams.pids + "&pnums=" + batchAddToCartParams.pnums + "&type=" + batchAddToCartParams.type
          // 拼接并替换购物车链接，只拼接尺码sku和数量以及定制id
          if(batchAddToCartParams.customGiftInfoId.length > 1){ // 批量定制器id正常返回的时候
            InitCartUrlCustomBtn.attr('href', url + "&customGiftInfoId=" + batchAddToCartParams.customGiftInfoId);// 直接拼接定制器id
          }else{
            InitCartUrlCustomBtn.attr('href', url);// 无定制器id
          }
        }
        if(cfg.isFsAndCustom && cfg.customId == "0" && customtabidNum){ // 命中厂直定制、无定制id、批量tab下屏蔽加车按钮
          InitCartUrlCustomBtn.hide()// 新购物车按钮隐藏
        }

        if(cfg.allSkuIdNo){ // 批量下所有sku不可定制的情况
          InitCartUrlCustomBtn.hide()// 新购物车按钮隐藏
          $(".m-itemover").show() // 批量场景，当前颜色所有尺码都不可定制时，定制完成之后展示：抱歉，此商品不支持定制购买！提示屏蔽加入购物车
        }
      } else { // 单品购物车按钮链接
        if(batchAddToCartParams.customGiftInfoId.length > 1){// 单品定制器id正常返回的时候
          modifyBuyUrlParam('customGiftInfoId', batchAddToCartParams.customGiftInfoId, G);// 通过modifyBuyUrlParam方法拼接定制器id
        }else{
          modifyBuyUrlParam('customGiftInfoId', '', G);// 通过modifyBuyUrlParam方法重置定制器id参数
        }
      }
      if(customtabidNum){// 批量状态下的尺码总数量
        cfg.countNum = allNum || "1";
      }else{
        cfg.countNum = $("#buy-num").val() || "1";
      }
    },
    getStocksInfo: function(sceneFlag){// 调用批量库存入口
      customObj.getStocks(allSkus, skuData, sceneFlag)
    },
    getStocks: function (allSkus, skuData, sceneFlag) {// 批量场景下 获取批量库存接口信息
      if (!allSkus.length) return // 无sku返回
      if(sceneFlag == "1"){// 批量场景下刷新页面
        var skuIdArr = customObj.chunk(allSkus, 100);
        for(var i = 0; i < skuIdArr.length; i++){// 超过100个sku 分批调用
          var skuArr = []
          for (var m = 0; m < skuIdArr[i].length; m++) {
            skuArr.push(skuIdArr[i][m])
          }
          customObj.getAjaxData(skuArr, skuData, sceneFlag) // 超过100个sku 分批调用
        }
      }else {// 定制服务选中和反选的时候不用考虑全量sku超过100的情况
        customObj.getAjaxData(allSkus, skuData, sceneFlag) // 接口只解析当前颜色下尺码
      }
    },
    getAjaxData: function(newASllSkus, skuData, sceneFlag) { // 阿波罗批量库存接口调用
      var body = JSON.stringify({
        type  : 'getstocks',
        area  : Tools.getAreaId().areaIds.join('_'),
      });
      var time = new Date().getTime()
      // 加固start
      var colorParm = {
        appid: 'item-v3',
        functionId: 'pc_stocks',
        client: 'pc',
        clientVersion: '1.0.0',
        t: time,//生成当前时间毫秒数
        body: body,
      }
      try{
        var colorParmSign =JSON.parse(JSON.stringify(colorParm))
        colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
        window.PSign.sign(colorParmSign).then(function(signedParams){
          colorParm['h5st']  = encodeURI(signedParams.h5st)
          try{
            getJsToken(function (res) {
              if(res && res.jsToken){
                colorParm['x-api-eid-token'] = res.jsToken;
              }
              colorParm['loginType'] = '3';
              colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
              getStocksData(colorParm);
            }, 600);
          }catch(e){
            colorParm['loginType'] = '3';
            colorParm['uuid'] = '';
            getStocksData(colorParm);
            //烛龙上报
            Tools.getJmfe(colorParm, e, "custom文件pc_stocks接口设备指纹异常", 250)
          }
        })
      }catch(e){
        colorParm['loginType'] = '3';
        colorParm['uuid'] = '';
        getStocksData(colorParm);
        //烛龙上报
        Tools.getJmfe(colorParm, e, "custom文件pc_stocks接口加固异常", 250)
      }
      // 加固end
      function getStocksData(colorParm){
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
          host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        if(customtabidNum){// 批量场景才会调用新库存
          colorParm['type'] = 'getstocks';
          colorParm['skuIds'] = newASllSkus.join(',');
          if(skuData.length > 0){
            colorParm['customSkuIds'] = skuData.join(',');
          }
          colorParm['area'] = Tools.getAreaId().areaIds.join('_');
          if(pageConfig && pageConfig.product.isColorAndSize && colorSizeData.length < parseInt(stockNum)){// 是否仅有颜色尺码属性,当spu下sku小于100的时候传定制标识
            colorParm['sceneFlag'] = sceneFlag // 工服定制项目新增参数场景标，是否选中定制服务，首次进入1
          }
          $.ajax({
            url: host + '/stocks',
            data: colorParm,
            dataType: 'json',
            xhrFields: {
              withCredentials: true,
            },
            headers: Tools.getUrlSdx(),
            success : function (r) {
              if(r){
                if(sceneFlag=="1" || sceneFlag=="0"){
                  customObj.appendStock(r, newASllSkus, "0", sceneFlag);// 初始化刷新页面
                }else{
                  customObj.appendStock(r, skuData, "1", sceneFlag);// 选择定制服务，type：0是全量sku 1是当前颜色下定制sku
                }
              }
            }
          })
        }
      }
    },
    chunk: function(arr, size) { // 拆分sku方法
      var newArray = [];
      for(var i=0; i< arr.length; i = i+size){
        newArray.push(arr.slice(i, i+size));
      }
      return newArray;
    },
    haveStock: function (state) {// 批量库存有货状态码
      return state && state != -1 && state != 34 && state != 0
    },
    appendStock: function (data, skus, type, sceneFlag) {// 批量场景下 批量库存接口出参数据逻辑，
      var canAddCartNum = 0 // 是否可加车
      var canDcNum = 0// 定制商品是否有效
      var resNum = 0 // 出参数量
      for (var i = 0; i < skus.length; i++) {
        var item = skus && skus[i];
        var currRes = data && data[item];
        var state = currRes && currRes.StockState
        var dc = currRes && currRes.dc
        var isCanAddCart = currRes && currRes.canAddCart
        var stylesListItem = stylesList.find('[data-sku="' + item + '"]')// 尺码的对应sku容器
        if(state){// 库存状态码
          if(!customObj.haveStock(state) && sceneFlag == "1"){ // 是否有库存，初始化刷新的时候判断无货
            customObj.showTip(stylesListItem,"无货")
          }
          resNum ++ // 没有状态码的时候 说明已经超过100个限制了，resNum最多100
        }

        if(dc){// 判断定制商品是否有效,只有场景标为2的是判断dc
          if(dc == "-1" && type=="1" && sceneFlag == "2"){
            canDcNum++
          }
        }

        if(isCanAddCart){// 只有刷新页面的时候校验是否可加车
          if(isCanAddCart == "1" && type == "0")
          {
            canAddCartNum++
          }
        }

      }

      // 批量下只有初始化的时候判断是否可加车
      if(sceneFlag == "1" && type == "0"){
        if(resNum != canAddCartNum){// 刷新页面颜色尺码sku出参数量和可加车数量对比
          var tipDialog = customObj.dialogTip("当前商品不支持批量")
          // 批量状态下，不支持批量的时候跳转到单品
          setTimeout(function(){
            tipDialog.close()
            location.href = G.modifyURL(location.href, {
              query: {
                purchasetab: 'dzgm'
              }
            })
          }, 1500);
        }else if(canAddCartNum != 0 && resNum == canAddCartNum && (pageConfig && pageConfig.product.isColorAndSize)){//批量sku都可以加车并且只有颜色尺码
          // $(".batch-purchase").show()// 批量切换展示，可批量
          $(".common-plan").show()
          $(".common-plan").find(".plgm").show()
          $(".common-plan").find(".dzgm").show() // 展示定制购买
        }else{
          $(".common-plan").find(".plgm").hide()
          $(".common-plan").find(".dzgm").hide() 
          if(Tools.areAllChildrenHidden("#common-plan")){ // 判断tab都隐藏
            $(".common-plan").hide()
            Event.fire({
                type: 'tabShow',
            })
          }
        }
        Tools.TabSwitch()// tab样式切换逻辑
      }

      // 选中定制器之后所有尺码是否都支持定制判断
      if(type == "1"){
        if (sceneFlag == "2") {// 选中定制并且是颜色下尺码sku
          if (canDcNum != skus.length) { // 遍历的DC和颜色下所有尺码sku不一致
            for (var i = 0; i < skus.length; i++) {
              var item = skus && skus[i];
              var dc = data && data[item] && data[item].dc
              var stylesListItem = stylesList.find('[data-sku="' + item + '"]')
              if(dc && dc == "-1"){// 判断定制商品是否有效-1无效 1有效
                customObj.showTip(stylesListItem, "此商品不支持定制")
              }
            }
            customObj.batchAddToCart() // 加车链接
          }else{
            var tipDialog = customObj.dialogTip("当前商品不支持定制")
            setTimeout(function(){
              tipDialog.close()
            }, 1500);
            // 都不定制的时候 清除所有尺码上定制提示并删除定制服务楼层
            customObj.clearTip() // 清除红字提示，放开切换数量
            // chooseCustom.hide()// 隐藏定制楼层
            // chooseCustom.find(".item").removeClass("custom-check")// 取消选中
            // customUpdate.addClass("hide") // 修改按钮隐藏
            cfg.customId = "0" // 都不支持定制 购物车链接清除定制器id
            cfg.allSkuIdNo = true // 当前商品不支持定制标
            // $(".m-itemover").show() // 批量场景，当前颜色所有尺码都不可定制时，定制完成之后展示：抱歉，此商品不支持定制购买！提示屏蔽加入购物车
            // InitCartUrlCustomBtn.hide()// 新购物车按钮放开点击样式
            for (var i = 0; i < skus.length; i++) {// 在判断是否有库存
              var item = skus && skus[i];
              var state = data && data[item] && data[item].StockState
              var stylesListItem = stylesList.find('[data-sku="' + item + '"]')// 尺码的对应sku容器
              if(state && !customObj.haveStock(state)){ // 是否有库存，初始化和反选的时候判断无货
                customObj.showTip(stylesListItem,"无货")
              }
            }
            customObj.batchAddToCart()// 购物车链接通用逻辑
          }
        }else if(sceneFlag == "3"){
          customObj.clearTip()// 反选先清除红字提示，放开切换数量
          for (var i = 0; i < skus.length; i++) {// 在判断是否有库存
            var item = skus && skus[i];
            var state = data && data[item] && data[item].StockState
            var stylesListItem = stylesList.find('[data-sku="' + item + '"]')// 尺码的对应sku容器
            if(state && !customObj.haveStock(state)){ // 是否有库存，初始化和反选的时候判断无货
              customObj.showTip(stylesListItem,"无货")
            }
          }
        }
      }

    },
    dialogTip: function(tip) { // 异常托底提示
      return $("body").dialog({
        title: "提示",
        source: "<div style='color:#e4393c;text-align:center;font-size:16px'>"+tip+"</div>",
        width: 350,
      });
    },
    clearTip: function() {// 放开切换数量功能，清除红字提示
      stylesList.find(".tip-color").addClass("hide").attr("data-type","0").html("");
      stylesList.find('.ipt-count').attr("disabled",false)// 放开输入框
      stylesList.find('.i-add').removeClass("disabled")
      stylesList.find('.i-reduce').removeClass("disabled")
    },
    showTip: function(stylesListItem,tip) {// 禁止切换数量功能，显示红字提示
      stylesListItem.find(".tip-color").removeClass("hide").attr("data-type","1").html(tip)
      stylesListItem.find('.ipt-count').attr({"value":"0","disabled":true})// 禁用输入框和置0
      stylesListItem.find('.i-add').addClass("disabled")
      stylesListItem.find('.i-reduce').addClass("disabled")
    },
    setTips: function() { // hover展示说明
      var timeoutId = 0
      chooseCustom.delegate('.custom-tips', 'mouseenter', function() {
        clearTimeout(timeoutId)
        $(this).addClass('hover')
      })

      chooseCustom.delegate('.custom-tips', 'mouseleave', function() {
        var $this = $(this)
        timeoutId = setTimeout(function() {
          $this.removeClass('hover')
        }, 300)
      });
    },
    // 判断是否为空对象
    isEmptyObject: function(obj) {
      return Object.keys(obj).length === 0;
    },
    showCustom: function () {// 融合下发定制信息展示工服定制楼层
      // if(cfg.customId == "0"){// 无定制器id情况下渲染融合下发的默认工服定制楼层信息
      Event.addListener('onStockReady', function(data){
        var data = data.stock;
        var customInfo = data && data.data && data.data.customInfo || {} // 融合定制信息
        var customedInfo = data && data.data && data.data.customInfo && data.data.customInfo.customedInfo || {}// 融合定制之后的定制信息
        var showSize = data && data.data && data.data.showSize // 是否展示尺码工作台
        var isFsAndCustom = data && data.data && data.data.isFsAndCustom // 是否命中厂直+定制
        pageConfig.product.isFsAndCustom = isFsAndCustom // 全局定义是否厂直+定制

        // 校验单品是否有库存，是否屏蔽批量tab
        var stockInfo = data && data.data && data.data.stockInfo // 融合库存信息
        if(stockInfo && stockInfo.stockState == 34){// 无货状态
          if(customtabidNum){// 批量场景tab下
            var tipDialog = customObj.dialogTip("当前商品不支持批量")
            // 批量状态下，不支持批量的时候跳转到单品
            setTimeout(function(){
              tipDialog.close()
              location.href = G.modifyURL(location.href, {
                query: {
                  purchasetab: 'dzgm'
                }
              })
            }, 1500);
            // $(".batch-purchase").hide()// 批量切换隐藏，不可批量
            if(Tools.areAllChildrenHidden("#common-plan")){ // 判断tab都隐藏
              $(".common-plan").hide()
              Event.fire({
                  type: 'tabShow',
              })
            }
          }else{
            if(Tools.areAllChildrenHidden("#common-plan")){ // 判断tab都隐藏
              $(".common-plan").hide()
              Event.fire({
                type: 'tabShow',
              })
            }
            var currentUrl = location.href;
            var url = new URL(currentUrl);
            var purchasetab = url.searchParams.get('purchasetab');
            // var list = ['dzgm', '']
            if (purchasetab && purchasetab != 'dzgm' && purchasetab != 'qyjhg') { // url 里有 purchasetab，说明原先显示 tab,(tab 由显示变为隐藏)(定制购买和企业计划购按钮)
              var newURL = G.modifyURL(location.href, {
                query: {
                  purchasetab: null
                },
              }, true)
              location.href = newURL
            }
          }
          // 地址切换时，tab 隐藏，隐藏 tab-copy
          Event.fire({
            type: 'tabHide',
          })
          
          Tools.TabSwitch()// tab样式切换逻辑
        }
        // 容器存在并且融合下发服务信息并且无定制器id的情况下
        // 展示工服定制楼层的逻辑：工服容器+融合下发customInfo + 融合下发customInfo.serviceName + 有customInfo.customedInfo + 无customInfo.customedInfo.customServicePrice
        // 定制完成之后展示逻辑：工服容器+融合下发customInfo + 融合下发customInfo.serviceName + 有customInfo.customedInfo + 有customInfo.customedInfo.customServicePrice
        // 命中厂直+定制：有定制服务-->展示定制楼层和去定制按钮，无定制服务-->不展示定制楼层和去定制按钮，展示不可购买的楼层（有无定制服务判断：融合下发customInfo + 融合下发customInfo.serviceName）
        if(chooseCustom.length > 0 && !customObj.isEmptyObject(customInfo)&& customObj.isEmptyObject(customedInfo)){ // 定制服务楼层数据下发、有serviceName、没有选中没有服务具体信息
          // $(".common-plan").find(".dzgm").show()
          // if(customtabFirst || customtabidNum){
            if(customInfo.minServicePrice && customInfo.minServicePrice != undefined) // 融合定制信息有无价格判断托底
            {
              // $(".common-plan").find(".dzgm").show()
              var customPromotionType = customInfo.customPromotionType ? customInfo.customPromotionType :""
              var djyh = customPromotionType.includes("多件优惠")
              var zx = customPromotionType.includes("专享")
              var iframeH = "330px"
              if(djyh && zx){ // 多件优惠+专享 iframe高度430
                iframeH = "430px"
              }else if(djyh && !zx){// 仅多件优惠 iframe高度330
                iframeH = "330px"
              }else if(zx && !djyh){// 仅专享 iframe高度130
                iframeH = "130px"
              }

              var tip = '<div class="custom-tips">\
                    <a href="#none" class="question"><i class="sprite-question"></i></a>\
                    <div class="tips">\
                        <div class="sprite-arrow"></div>\
                        <div class="content">\
                        <iframe src=\''+customInfo.customStageUrl+'\' style="width:100%;border:none;height:'+iframeH+'"></iframe>\
                        </div>\
                    </div>\
                </div>'
              var icon = customInfo.customStageUrl ? tip : ""
              chooseCustom.find("a").html("<span class='minServicePrice'> +￥" + Number(customInfo.minServicePrice) + "元起/件 "+ customPromotionType + "</span>"  + icon)
            }else{
              chooseCustom.find("a").html(customInfo.serviceName)
            }

            customObj.setTips()

            if(isFsAndCustom){// 是否命中厂直+定制
              $("#choose-btns").show();// 底部按钮整体容器展示
              $(".m-itemover").hide()// 展示不可加车文案提示
              btnGocustom.siblings().hide(); // 去定制按钮同级其他按钮隐藏
              $(".choose-amount").show()// 切换数量功能展示
              if(customtabidNum){// 批量tab下
                InitCartUrlCustomBtn.hide()// 新购物车按钮隐藏
              }
            }
            btnGocustom.show()// 去定制按钮展示
            var serviceskuid = Tools.getServiceSkuid(data && data.data && data.data.warrantyInfo)
            Tools.otherBtnExpo(btnGocustom.text(), serviceskuid)
            // $("#InitCartUrl").show()// 购物车按钮展示
            $("#InitTradeUrl").remove()// 工服定制不支持立即购买按钮
            // InitCartUrlBtn.hide()// 隐藏原有的购物车按钮
            chooseCustom.show()// 信息渲染完成展示楼层
            chooseCustom.find(".custom-list ul").html('') // 清理服务列表信息
            chooseCustom.find(".custom-list").hide() // 服务列表信息隐藏
            chooseCustom.find(".custom-down").hide()// 展开全部按钮隐藏
            customUpdate.addClass("hide")// 隐藏修改按钮
            customCancel.addClass("hide")// 隐藏取消按钮
            try {
              expLogJSON('smb_pc', 'Productdetail_CustomizeFloorExpo', '{"skuid": ' + skuid + '}')
            } catch (e) {
              if (typeof console !== 'undefined') {
                console.log('新增定制楼层曝光埋点错误');
              }
            }
          // }

        }else if(chooseCustom.length > 0 && !customObj.isEmptyObject(customInfo) && !customObj.isEmptyObject(customedInfo)){ // 选中定制服务楼层并数据下发返回价格和具体服务
          chooseCustom.find("ul").removeClass("down");// 初始化定制服务信息高度
          if(customedInfo && customedInfo.customServicePrice != undefined) // 选中之后融合定制信息有无价格判断托底
          {
            var customServiceOriginPriceDom = customedInfo.customServiceOriginPrice ? "<del class='customServiceOriginPrice'>￥" + Number(customedInfo.customServiceOriginPrice) + "元/件</del>" : ""
            chooseCustom.find("a").html("<span class='minServicePrice'>定制金额 +￥" + Number(customedInfo.customServicePrice) + "元/件 </span>" + customServiceOriginPriceDom)
          }else{
            chooseCustom.find("a").html(customInfo.serviceName)
          }
          chooseCustom.find(".dd").addClass("after-custom")// 定制前后样式不一样，添加 class 区分
          var customServiceInfoList = customedInfo && customedInfo.customServiceInfoList
          if(customServiceInfoList && customServiceInfoList.length > 0) // 选中之后融合定制信息有无价格判断托底
          {
            var serviceInfoList = ""
            for(var i = 0; i < customServiceInfoList.length; i++){
              var item = customServiceInfoList[i]
              serviceInfoList += item.attrName ? "<li><span title='"+ item.attrName +"'>" + item.attrName +"：</span> <span title='"+ item.attrValueName +"'>"+ item.attrValueName + " </span><span>x" + item.attrValueNum + "</span></li>" : ""
            }
            chooseCustom.find(".custom-list ul").html(serviceInfoList)
            chooseCustom.find(".custom-list").show() // 服务列表信息展示
            // 定制服务 查看全部
            customObj.checkAllService(customServiceInfoList.length)
          }

          if(isFsAndCustom){// 是否命中厂直+定制
            $("#choose-btns").show();// 底部按钮整体容器展示
            $(".m-itemover").hide()// 展示不可加车文案提示
            $("#InitCartUrl").siblings().hide(); // 加入购物车按钮其他同级按钮隐藏
            $("#InitCartUrl").show()// 购物车按钮展示
            if(customtabidNum){// 批量tab下
              InitCartUrlCustomBtn.show()// 新购物车按钮放开点击样式
            }
            $(".choose-amount").show()// 切换数量功能展示
          }
          chooseCustom.show()// 信息渲染完成展示楼层
          btnGocustom.hide()// 去定制按钮隐藏
          $("#InitCartUrl").show()// 购物车按钮展示
          $("#InitTradeUrl").remove()// 工服定制不支持立即购买按钮
          customUpdate.removeClass("hide")// 展示修改按钮
          customCancel.removeClass("hide")// 展示取消按钮

        }else if(chooseCustom.length > 0 && customObj.isEmptyObject(customInfo)){ // 选择配送地址或者单品数量不返回定制服务数据隐藏定制服务楼层
          // cfg.customId = "0"
          chooseCustom.find(".item").removeClass("custom-check")// 取消选中
          customUpdate.addClass("hide")// 修改按钮隐藏
          chooseCustom.hide()// 定制服务楼层隐藏
          btnGocustom.hide()// 去定制按钮隐藏
          chooseCustom.find(".custom-list ul").html('') // 清理服务列表信息
          chooseCustom.find(".custom-list").hide() // 服务列表信息隐藏
          chooseCustom.find(".custom-down").hide()// 展开全部按钮隐藏
          customUpdate.addClass("hide")// 隐藏修改按钮
          customCancel.addClass("hide")// 隐藏取消按钮
          if(isFsAndCustom){// 是否命中厂直+定制
            $("#choose-btns").hide();// 底部按钮整体容器隐藏
            $(".m-itemover").show()// 展示不可加车文案提示
          }
        }
        // customObj.batchAddToCart()// 加车按钮链接逻辑通用方法
        // 是否展示尺码工作台
        if(showSize){
          $(".show-or-hide-other-styles .drop-down-link").show() // 工作台链接和文案都是后端配置
        }
        // cfg.giftService = ""// 融合接口调用完成重置定制器入参数
        if(cfg.giftService=="1"){
          customObj.getStocksInfo("2")// 调用批量库存
        }else if(cfg.giftService=="0"){
          customObj.getStocksInfo("3")// 调用批量库存
        }
      })
      // }
    }
  };

  function init() {
    customObj.init(); // 初始化
  }

  module.exports.__id = "custom";
  module.exports.init = init;
});
