/* muying */
@import "../common/Jcal";

#muying {
    input {
        height: 18px;
        line-height: 18px;
        border: 1px solid #ccc;
        padding: 2px 4px;
    }
    .dd > input, label > input {
        vertical-align: middle;
    }
    #baby-girl, #baby-boy, #baby-unknow {
        border: none;
    }
    .illegal-value {
        input {
            border: 1px solid #e4393c;
        }
        .error-msg {
            display: block;
        }
    }
    .mc {
        background: url(i/bg.png) no-repeat 18px bottom;
    }
    li {
        height: 30px;
        line-height: 30px;
        padding: 20px 0 20px 15px;
        *white-space: nowrap;
        *zoom: 1;
    }
    .muying-t1, .muying-t2 {
        margin-left: 130px;
    }
    .dt {
        float: left;
        height: 24px;
        line-height: 24px;
        *white-space: nowrap;
        *zoom: 1;
    }
    .dd {
        float: left;
        height: 24px;
        line-height: 24px;
        *white-space: nowrap;
        *zoom: 1;
        position: relative;
    }
    .dt .required {
        color: #f60;
    }
    .dd {
        i {
            font-style: normal;
            display: inline-block;
            *zoom: 1;
            *vertical-align: top;
        }
        .error-msg {
            position: absolute;
            top: 24px;
            padding: 0 5px;
            color: #e4393c;
            border: 1px solid #ffbdbe;
            background: #ffebeb;
            white-space: nowrap;
        }
    }
    .baby-name {
        width: 6em;
    }
    .baby-size {
        width: 2em;
    }
    .baby-schoolAge {
        width: 7.4em;
        height: 24px;
        border: 1px solid #ccc;
        padding: 2px 4px;
    }
}

#J-cal-baby {
    width: 95px;
}

#muying.result-info {
    a {
        margin: 0 5px;
    }
    b {
        color: #333;
    }
    .btn-gray {
        margin: 0 5px;
        vertical-align: -9px;
    }
}
