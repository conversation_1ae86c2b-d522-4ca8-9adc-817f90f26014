@import '../common/lib';

#shop-similar-promotion {
    &.img-square .item-plist-2 .p-img {
        height: 220px;
        img {
            height: 220px;
        }
    }
    .item-plist-2{
        .p-img{
            width:218px;
            height: 282px;
            padding:0;
            margin-bottom:8px;
            overflow: hidden;
            img{
                width:220px;
                height: 282px;
                margin-left: -1px;
            }
        }
        .p-name{
            width:200px;
            height: auto;
            max-height: none;
            line-height: 16px;
            padding: 0 9px;
            margin-bottom: 5px;
        }
        .p-focus{
            .i-focus{
                display: inline-block;
                width: 14px;
                height: 13px;
                margin-top:2px;
                margin-right: 5px;
                vertical-align:top;
                background: url(i/follow-sku.png) no-repeat 0 0;
            }
            &:hover .i-focus {
                background: url(i/follow-sku-hover.png) no-repeat 0 1px;
            }
            .text{
                display: inline-block;
                height: 16px;
                line-height: 16px;
                vertical-align:top;
            }
        }
        .p-price{
            width: 200px;
            height: 16px;
            line-height: 16px;
            padding: 0 9px;
            margin-bottom:8px;
            overflow: hidden;
            .p-focus{
                float:right;
                width:70px;
                height: 16px;
                text-align:right;
            }
            .price{
                font-family: verdana;
            }
        }
        .p-comment{
            width:200px;
            max-height: 48px;
            padding: 9px;
            background:#f7f7f7;
            .inner{
                width: 100%;
                max-height: 48px;
                line-height: 16px;
                overflow: hidden;
            }
        }
        li{
            float:left;
            width:220px;
            //height: 420px;
            position: relative;
            padding-bottom: 10px;
            .pro-wrap{
                width:218px;
                border:1px solid #f5f5f5;
                //height: 418px;
                @include box-shadow(0 0 1px rgba(0,0,0,0.09));
            }

            &:hover{
                .pro-wrap{
                    position: absolute;
                    z-index:2;
                    height: auto;
                    border: 1px solid #e7e7e7;
                    @include box-shadow(0 0 10px rgba(0,0,0,0.25));
                    .p-comment{
                        //min-height: 48px;
                        height: auto;
                        max-height: none;
                        .inner{
                            height: auto;
                            max-height: none;
                            //height: 81px;
                        }
                    }
                }
            }
        }
    }

    .viewmore-box{
        margin: 0 24px 20px;
        height: 31px;
        .morelink{
            display: block;
            height: 29px;
            line-height: 29px;
            border:1px solid #ddd;
            background:#f7f7f7;
            color:#666;
            text-align: center;
            &:hover{
                background:#fff;
                color:#333;
                text-decoration: none;
            }
        }
    }
    .shop-similar-promo-list{
        _overflow:hidden;
        margin-top: 20px;
        li{
            //height: 440px;
            a:hover {
                text-decoration: none;
            }
        }
    }
}
