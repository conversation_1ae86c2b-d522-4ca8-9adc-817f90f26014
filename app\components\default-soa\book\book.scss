@charset "utf-8";
@import "fitting.scss";
@import "detailContent.scss";
@import "../common/lib.scss";

/**
 * extInfo 右侧商家信息
 *
 */
 .extInfo{
    width:210px;
    position: absolute;
    top:0;
    right:0;
    /*品牌logo*/
    .brand-logo{
        padding: 10px 0;
        //height: 25px;
        overflow: hidden;
        //border-bottom:solid 1px #eee;

        a{
            display: block;
            text-align: center;
        }
    }


    //增值保障
    .jd-service{
        width: 210px;
        margin-right: -23px;
        zoom:1;
        overflow: hidden;
        padding:0 0 10px 9px;

        dt{
            clear:both;
            padding-bottom: 10px;
        }

        dd{
            width: 95px;
            height: 16px;
            float: left;
            padding: 0 10px 10px 0;
        }
    }

    .customer-service{
        //border-bottom: 1px solid #eee;
        //padding-bottom: 10px;
        margin-bottom: 10px;
        line-height: 24px;

        .label{
            width: 60px;
            height: 24px;
            line-height: 24px;
            float:left;
            display: none;
        }
        .service{
            float:left;
            width: 187px;
            .item{
                width: 86px;
                height: 25px;
                margin-right: 4px;
                float:left;
                overflow:hidden;
                a:hover{
                    text-decoration: none;
                }
            }
            .jd-jimi {
                line-height:100px;
            }
        }
    }
}

/**
 * 一品多商商家列表
 */
 .pop-store-list{
    width: 200px;
    padding: 10px 0;
    border-top: 1px solid #eee;

    .pop-store-item{
        width: 100%;
        height: 26px;
        line-height: 26px;
        overflow: hidden;

        .stores{
            font-weight: bold;
            //  color: $color03;
            color: #005aa0;
        }
        .store-name{
            color: #999;
        }
        .store-name:hover{
            color: #e4393c;
        }
        .price{
            // color: $color01;
            color: #e4393c;
        }
        .c-left{
            width: 125px;
            float:left;

        }
        .c-right{
            width: 72px;
            float:right;
            text-align: right;
        }

    }

    .btnbox{
        padding: 15px 0 15px;
        text-align: center;
    }

}

/*右侧卖家信息*/
.product-intro {
  width: 100%;
  position: relative;
  z-index: 3;
  *zoom: 1;
  //padding-bottom:15px;
  //min-height:474px;
  //height: auto !important;
  //height:474px;

  .m-item-inner {
    width: 588px;
    float: left;
    // 不能加overflow:hidden 否则送货地址浮层被截断
    //overflow: hidden;
    //padding-left:378px;
    padding-right: 24px;
  }
  .m-item-ext {
    display: none;
    width: 244px;
    float: left;
    margin-left: -244px;
    overflow: hidden;
    background: #fff;
    .extra-infor-show-trigger {
      display: none;
      width: 23px;
      text-align: center;
      position: absolute;
      left: 0;
      top: 210px;
      cursor: pointer;
      .i-arrow {
        display: block;
        width: 11px;
        height: 11px;
        margin: 0 auto 10px;
        overflow: hidden;
        background: url(../../css/i/item.icons.png) no-repeat 1px 0;
      }
    }
  }
  &.z-item-ext-type2 {
    // 添加overf:hidden后一定测下各子元素的absolute浮层有没有被截断
    // preview左上角图标被overflow:hidden截断
    //overflow: hidden;
    .m-item-inner {
      // 添加padding-bottom值保证窄版下选择地址下拉框不被截断
      //padding-bottom: 100px;
    }
    .m-item-ext {
      display: block;
      width: 23px;
      margin-left: -24px;
      position: relative;
      min-height: 580px;
      z-index: 5;
      border-left: 1px solid #f2f2f2;

      // float:none;
      // position: absolute;
      // right:0;
      // top:0;
      // height: 100%;
      .extra-infor-show-trigger {
        display: block;
      }
      .extInfo {
        visibility: hidden;
      }

      &.z-item-ext-hover {
        width: 240px;
        margin-left: -241px;
        // @include box-shadow(0px 1px 10px #999);
        background: rgba(255, 255, 255, 0.95);

        .extra-infor-show-trigger {
          display: none;
        }
        .extInfo {
          visibility: visible;
        }
      }
    }

  }
}
.btn-def{
  height: 26px;
  line-height: 26px;
	padding: 0 16px;
	display: inline-block;
	border: 1px solid #ccc;
	color: #666;
	background:#fff;
}
.btn-def:hover {
  border-color: rgb(228, 57, 60);
}
// 图书试读图标
.i-book-sample{
  width: 137.5px;
  height: 105.5px;
	display: inline-block;
  background: url(https://img10.360buyimg.com/imagetools/jfs/t1/262475/9/6040/21051/6773bf59F2529ff03/823d9a08ed2eb127.png) left top / 100% 100% no-repeat;
  margin-top: 250px;
	// background: url(i/main-circles.png) no-repeat -110px -55px;
	// &:hover {
	// 	background: url(i/main-circles.png) no-repeat -165px -55px;
	// }
}


.btn-ebook {
    display: block;
	height: 52px;
    line-height: 52px;
    background-color: #ffebf1;
    color: #ff0f23;
    // font-size: 20px;
    font-weight: 600;
    // padding: 0 15px;
    // float: left;
    //   margin-right: 10px;
    border-radius: 4px;
	span {
		margin-left: 2px;
		font-family: verdana;
	}
}

.root61 {
  .product-intro {
    .m-item-inner {
      padding-right: 244px;
    }
    .m-item-ext {
      display: block;
    }
  }
}
// 试读
// 图书样品试读
.i-book-sample{
  text-indent: -9999px;
}

.p-author{
  color: #666;
}

/**
 * 组合套箭头
 */
 #choose-suit {
  *position: relative;
  *z-index:1;
  .dd {
    overflow: visible;
  }
  .item {
    position: relative;
  }

  .suit-wrap a {
    padding: 0;
    display: inline;
    border: 0;
    white-space: normal;
  }
}
.hover .suit-wrap {
  display: block;
}

.suit-wrap {
  position: absolute;
  z-index: 2;
  top: 24px;
  padding-top: 13px;
  left: 0;
  display: none;
  background: url(//misc.360buyimg.com/lib/img/e/blank.gif);

  .s-img {
    float: left;
    margin: 1px 5px 1px 1px;
  }
  .s-name {
    height: 35px;
    line-height: 1.5em;
    overflow: hidden;
  }
  .s-price {
    color: #e4393c;
    line-height: 16px;
  }
}

.suit-arrow {
  height: 52px;
  position: relative;
  width: 300px;
  background: #ffffff;
  border: 1px solid #cecbce;
}

.suit-arrow:after, .suit-arrow:before {
  bottom: 100%;
  left: 10%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.suit-arrow:after {
  border-color: rgba(255, 255, 255, 0);
  border-bottom-color: #ffffff;
  border-width: 5px;
  margin-left: -5px;
}

.suit-arrow:before {
  border-color: rgba(206, 203, 206, 0);
  border-bottom-color: #cecbce;
  border-width: 6px;
  margin-left: -6px;
}

%sprite-ico{
	//@include ico-common(./i/sprite.png);
	display: inline-block;
	background-image: url(i/sprite.png);
	background-repeat: no-repeat;
}
@mixin ico-size($width,$height){
	width: $width;
	height: $height;
}




/**
 * 热门推荐
 */
 

 %book-rec-1208{
  width: 1148px;
  padding-left: 30px;
  padding-right: 30px;

  .book-rec-list{
      li{
          padding: 10px 13px;
      }
  }
}

%book-rec-988{
  width: 924px;
  padding-left: 32px;
  padding-right: 32px;

  .book-rec-list{
      li{
          padding: 10px 8px;
      }
  }
}

%book-rec-768{
  width: 704px;
  padding-left: 32px;
  padding-right: 32px;

  .book-rec-list{
      li{
          padding: 10px 19px;
      }
  }
}
.book-rec-box{
  margin-bottom: 10px;
  border-right: 1px solid #dedfde;
  border-left: 1px solid #dedfde;
  border-bottom: 1px solid #dedfde;
  .m-box-hd {
      height: 30px;
      line-height: 30px;
      border-top: 2px solid #999;
      overflow: hidden;
      background: #f7f7f7;
      border-bottom: 1px solid #dedfde;
      .title {
        float: left;
        padding-left: 10px;
        font-size: 14px;
        font-family: "microsoft yahei";
    }
  }
}
.book-rec-switch{
  height: 184px;

  .m-switch-main{
      height: 100%;
  }

  .m-switch-prev{
      left:15px;
  }
  .m-switch-next{
      right:15px;
  }
}

.book-rec-list{
  float:left;
  li{
      width: 138px;
      height: 164px;
      float:left;
      overflow: hidden;
      text-align: center;
  }

  .p-name{
      height: 18px;
      overflow: hidden;
      min-height: 18px;
  }
  .p-comment{
      width: 120px;
      height: 18px;
      line-height: 18px;
      overflow: hidden;
      margin: 0 auto;

      .number{
          color: $color03;
      }
  }
  .p-price{
      height: 18px;
      line-height: 18px;
      overflow: hidden;
      color: $color01;

      .number{
          font-weight: bold;
          font-family: verdana;
      }
  }
}

.book-rec-switch{
  @extend %book-rec-988;
}
.right{
  .book-rec-switch{
      @extend %book-rec-768;
  }
}

/* 左右箭头 及其相关状态 */
.m-switch{
  position: relative;
  overflow: hidden;
}
.m-switch-main{
  width: 100%;
  overflow: hidden;
  position: relative;
}
.i-prev-btn,.m-switch-prev{
	@extend %sprite-ico;
	@include ico-size(15px,25px);
	background-position: 0 -30px;
	cursor: pointer;

	&:hover{
		background-position: 0 -70px;
	}
}

.i-next-btn,.m-switch-next{
	@extend %sprite-ico;
	@include ico-size(15px,25px);
	background-position: -30px -30px;
	cursor: pointer;

	&:hover{
		background-position: -30px -70px;
	}
}

.i-prev-btn{

	&.i-prev-disable{
		background-position: 0 -110px;
		cursor: default;
		&:hover{
			background-position: 0 -110px;
		}
	}
}

.i-next-btn{

	&.i-next-disable{
		background-position: -30px -110px;
		cursor: default;
		&:hover{
			background-position: -30px -110px;
		}
	}
}


.m-switch-prev{
  position: absolute;
  text-indent: -9999px;
  top: 50%;
  margin-top: -13px;
	&.m-switch-prev-disable{
		background-position: 0 -110px;
		cursor: default;
		&:hover{
			background-position: 0 -110px;
		}
	}
}

.m-switch-next{
  position: absolute;
  text-indent: -9999px;
  top: 50%;
  margin-top: -13px;
	&.m-switch-next-disable{
		background-position: -30px -110px;
		cursor: default;
		&:hover{
			background-position: -30px -110px;
		}
	}
}




// 左侧达人选购和看了又看
.aside .m .mt{font:14px/30px 'microsoft yahei'; /*overflow: visible;*/ }
.aside .m2 .mt { height:37px; line-height:37px; }
.aside .m2 .mc { border:1px solid #ddd; border-top:none; }
.aside .m2 .mt { border:1px solid #ddd; }
.m1 .mt h2,.m2 .mt h2,.m2 .mt h3,.mt .extra {font-weight: 400;}
h2,h3 {font-size: 14px}
h4,h5,h6 {font-size: 12px}
/*related-buy*/
.related-buy .loading-empty { background:none; padding-left:0; }
.related-buy ul{padding:0 8px;overflow:hidden;zoom:1;}
.related-buy li{padding:8px 0;border-top:1px dotted #DEDEDE;}
.related-buy .fore1{border-top:none;}
.related-buy .p-name strong{color:#CE2C08;}
.related-buy .p-img,
.related-buy .p-price{text-align:center;}
.related-buy .extra{height:30px;line-height:28px;padding-right:8px;text-align:right;}
.related-buy .extra a{color:#005aa0;}

/* #sp-host */
.m3 .p-img { text-align:center; }
.m3 .p-info { position:relative; }
.m3 .p-info b { padding-left:25px; font-weight:normal; color:#666; }
.m3 .p-info s { display:block; position:absolute; width: 18px;height:20px; overflow:hidden; text-align: center; background-position:-256px -322px; color:#cdcdcd; text-decoration:none; }
.m3 .fore1 .p-info s,
.m3 .fore2 .p-info s,
.m3 .fore3 .p-info s { background-position:-232px -322px; color:#e53c3f; }
.m3 .mc { margin-top:-1px; }
.m3 li { padding-top:10px; border-top:1px dotted #ccc; margin:0 10px 10px 10px; }
.m3 li .p-name { line-height:1.5em; height:3em; }
.m3 li .p-img,
.m3 li .p-name,
.m3 li .p-info { padding:7px 12px 0; }
.m3 li .p-info {height:20px;}
.m3 li .p-img, .m3 li .p-info, .m3 li .p-name {
  padding: 7px 12px 0px;
}
#sp-reco .p-info, #sp-reco .p-name, #summary-presell .item.curr em, .btn-gray, .m3 .p-img, .m3 .p-info s, .presell-stage-list, .share-bubble {
  text-align: center;
}
.related-buy .mc,#sp-reco .mc{padding: 0;}





/*related-sorts*/
#related-sorts ul{padding:4px 0 4px 6px;overflow:hidden;zoom:1;}
#related-sorts li{float:left;width:94px;height:18px;padding:3px 6px 3px 0;overflow:hidden;}

/* ypds */
#ypds-list { width:155px; margin-top:10px; }
#ypds-list .mt { border:1px solid #DEDFDE; }
#ypds-list .mc { border:1px solid #DEDFDE; border-top:none; }
#ypds-list .mt span { height:35px; line-height:35px; padding:0 10px; }
#ypds-list .mc li { height:30px; margin:0 5px -1px 5px; padding:0 5px; line-height:30px; border-bottom:1px dotted #dedfde; clear:both; }
#ypds-list .mc li .lh { text-align:right; }


.root61 #ypds-list { width: 210px; }

//主图浮层
.related-buy .tpl-pop-book-side .center-layer{margin-left: 46px;}

// 人气单品、新书热卖榜到手价样式
#combine-con .price-dsj, #hotbook-con .price-dsj{
  display: block;
}

#summary-order{
  .dd{
    color: #1a1a1a;
    font-size: $baseFontSize;
    margin-bottom: 32px;
    height: 18px;
    line-height: 18px;
  }
}