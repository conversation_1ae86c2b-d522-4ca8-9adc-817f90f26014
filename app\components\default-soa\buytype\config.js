module.exports = {
    _layout: 'views/layout/layout.html',
    _blocks: {
        style: '\
        {% block style %}\
            {{ super() }}\
            <!--after-->\
            {{ Tag("link", "./buytype.css") }}\
        {% endblock %}',
        script: '\
        {% block script %}\
            <!--before-->\
            {{ super() }}\
            {{ Tag("script", "./buytype.js") }}\
        {% endblock %}'
    },
    data: {
        'id': 'mod-default',
        'class': 'mod-def'
    }
};
