define('PUBLIC_ROOT/modules/comment/showImgSwitch', function(require, exports, module) {
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');

    var ShowImgSwitch = function(opts) {
        this.init(opts)
    }

    ShowImgSwitch.prototype = {
        $showImgSwitch: null,
        $thumbList: null,
        $thumbListUl: null,
        $photoImg: null,

        imgNum: 0, //一次展示多少个
        url: '', //请求地址
        currentPage: 1, //页码
        dataPage: 1, //数据页码
        totalPage: 0, //一共多少页
        currIndex: 0, //当前是第几个

        imgCommentCount: 0, //图片总数
        imgArr: [], //本地图片数组

        onUpdate: $.noop,
        onReady: $.noop,

        template: {},
        isInit: true,

        init: function(opts) {
            var me = this

            opts.showImgSwitch =
                opts.showImgSwitch || '.comments-showImgSwitch-wrap'
            opts.thumbList = opts.thumbList || '.thumb-list' //缩略图列表
            opts.thumbPrevBtn = opts.thumbPrevBtn || '.J-thumb-prev' //缩略图左箭头
            opts.thumbNextBtn = opts.thumbNextBtn || '.J-thumb-next' //缩略图右箭头
            opts.cursorLeft = opts.cursorLeft || '.J-cursor-left' //大图左按钮
            opts.cursorRight = opts.cursorRight || '.J-cursor-right' //大图右按钮
            opts.photoImg = opts.photoImg || '.J-photo-img'

            me.$showImgSwitch = $(opts.showImgSwitch)
            me.$thumbList = $(opts.thumbList)
            me.$thumbListUl = me.$thumbList.find('ul')
            me.thumbPrevBtn = opts.thumbPrevBtn
            me.thumbNextBtn = opts.thumbNextBtn
            me.$photoImg = $(opts.photoImg) //大图
            me.cursorLeft = opts.cursorLeft
            me.cursorRight = opts.cursorRight

            me.template.thumbLi =
                '<li><a href="javascript:;"><img width="76" height="76" src="${imageUrl}"></a></li>'

            me.onUpdate = opts.onUpdate || me.onUpdate
            me.onReady = opts.onReady || me.onReady

            me.currIndex = 0
            me.imgNum = opts.imgNum //一次展示多少个 默认10
            me.url = opts.url //请求地址
            me.wideVersion = opts.wideVersion
            // 清空数组
            me.imgArr = []
            me._getData()
        },
        _getData: function() {
            var me = this
            if (/debug=showImgSwitch/.test(location.href)) {
                console.log('开始获取数据 页码：' + me.dataPage)
            }
            var time = new Date().getTime()
            var paramJson = {
                // appid: 'item-v3',
                // functionId: me.url.functionId,
                // client: 'pc',
                // clientVersion: '1.0.0',
                // t: time,//生成当前时间毫秒数
                // loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                page: me.dataPage,
                pageSize: me.imgNum,
                productId: me.url.productId,
                isShadowSku: me.url.isShadowSku,
            }

            var body = JSON.stringify(paramJson);
            // 加固start
            var colorParm = {
                appid: 'item-v3',
                functionId: me.url.functionId,
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                            getShowImgCommentData(colorParm);
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getShowImgCommentData(colorParm);
                        //烛龙上报
                        Tools.getJmfe(colorParm, e, "showImgSwitch评价图片列表设备指纹异常",751)
                    }
                })
            }catch(e){
                colorParm['loginType'] = '3';
                colorParm['uuid'] = '';
                getShowImgCommentData(colorParm);
                //烛龙上报
                Tools.getJmfe(colorParm, e, "showImgSwitch评价图片列表加固异常",751)
            }            
            // 加固end

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            function getShowImgCommentData(colorParm){
                $.ajax({
                    url: host,
                    data: colorParm,
                    dataType: 'json',
                    contentType: "application/json;charset=gbk",
                    xhrFields: {
                        withCredentials: true,
                    }, 
                    success: function(data) {
                        me._setData(data)
                    }
                })
            }
        },
        _setData: function(data) {
            var me = this
            if (/debug=showImgSwitch/.test(location.href)) {
                console.log('结束获取数据 页码：' + me.dataPage)
            }
            var imgComments = data.imgComments
            me.imgCommentCount = imgComments.imgCommentCount
            me.totalPage = Math.ceil(imgComments.imgCommentCount / me.imgNum)
            var appendLiStr = ''
            //放入图片数组
            for (var i in imgComments.imgList) {
                me.imgArr.push(imgComments.imgList[i])
                imgComments.imgList[i].imageUrl = me._replaceThumbImgSize(
                    imgComments.imgList[i].imageUrl
                )
                appendLiStr += me.template.thumbLi.process(
                    imgComments.imgList[i]
                )
            }
            me._appendThumb(appendLiStr)
            if (me.isInit == true) {
                me._chooseIndex(me.currIndex)
                me._bindEvents()
                me.isInit = false
            }

            me.onReady(me.imgCommentCount)
        },
        _bindEvents: function() {
            var me = this
            me.$showImgSwitch.delegate(me.cursorLeft, 'click', function() {
                if (me.currIndex > 0) {
                    me.currIndex--
                }
                me._chooseIndex(me.currIndex)
            })

            me.$showImgSwitch.delegate(me.cursorRight, 'click', function() {
                if (me.currIndex + 1 < me.imgArr.length) {
                    me.currIndex++
                    me._chooseIndex(me.currIndex)
                } else {
                    if (/debug=showImgSwitch/.test(location.href)) {
                        console.log('不能再往后选了')
                    }
                }
            })

            me.$showImgSwitch.delegate('li', 'click', function() {
                me.currIndex = $(this).index()
                me._chooseIndex(me.currIndex)
            })

            me.$showImgSwitch.delegate(me.thumbPrevBtn, 'click', function() {
                if (me.currentPage > 1) {
                    me.currentPage--
                    me.currIndex = (me.currentPage - 1) * me.imgNum
                    me._chooseIndex(me.currIndex)
                }
            })

            me.$showImgSwitch.delegate(me.thumbNextBtn, 'click', function() {
                var localPage = Math.ceil(me.imgArr.length / me.imgNum)
                if (me.currentPage < me.totalPage) {
                    if (me.currentPage + 1 <= localPage) {
                        me.currentPage++
                        me.currIndex = (me.currentPage - 1) * me.imgNum
                        me._chooseIndex(me.currIndex)
                    }
                }
            })

            $(window).bind('keydown', $.proxy(me._onKeydownHandler, me))
        },
        _appendThumb: function(appendLiStr) {
            var me = this
            me.$thumbListUl.append(appendLiStr)
        },
        _chooseIndex: function(index) {
            var me = this
            var imgObj = me.imgArr[index]
            if (!imgObj) {
                return false
            }
            //计算当前页码
            me.currentPage = Math.ceil((index + 1) / me.imgNum)
            //更新大图
            me._updatePhoto(imgObj, index)
            //更新缩略图
            me._checkThumbList(index)
            //判断缓存数是否小于页数
            me._checkNeedGetData()
            //执行更新回调
            me.onUpdate(imgObj)
        },
        _updatePhoto: function(imgObj, index) {
            var me = this
            var imageUrl = me._replaceImgSize(imgObj.imageUrl)
            me.$photoImg.attr('src', imageUrl)
            me.$showImgSwitch
                .find(me.cursorLeft)
                .toggleClass('disable', index == 0)
            me.$showImgSwitch
                .find(me.cursorRight)
                .toggleClass('disable', index == me.imgCommentCount - 1)
        },
        _checkThumbList: function(index) {
            var me = this
            me.$thumbListUl.find('li').removeClass('selected')
            me.$thumbListUl.find('li:eq(' + index + ')').addClass('selected') //选中缩略图

            //根据当前页码 算出UL位移
            var marginLeft =
                -((me.currentPage - 1) * (me.imgNum + 1)) *
                me.$thumbListUl.find('li').width()
            //me.$thumbListUl.css("marginLeft", w);

            me.$thumbListUl.stop().animate({ marginLeft: marginLeft })

            //更新翻页按钮状态
            var $prevBtn = me.$showImgSwitch.find(me.thumbPrevBtn),
                $nextBtn = me.$showImgSwitch.find(me.thumbNextBtn)
            if (me.currentPage <= 1) {
                $prevBtn.addClass('i-prev-disable')
            } else {
                $prevBtn.removeClass('i-prev-disable')
            }

            if (me.currentPage >= me.totalPage) {
                $nextBtn.addClass('i-next-disable')
            } else {
                $nextBtn.removeClass('i-next-disable')
            }
        },
        _checkNeedGetData: function() {
            var me = this
            if (me.currentPage < me.totalPage) {
                if (me.dataPage < me.currentPage + 1) {
                    me.dataPage++
                    me._getData()
                }
            } else {
                if (/debug=showImgSwitch/.test(location.href)) {
                    console.log('最后一页')
                }
            }
        },
        _replaceThumbImgSize: function(url) {
            url = url.replace('shaidan', 'n1')
            return url.replace('jfs', 's76x76_jfs')
        },
        _replaceImgSize: function(url) {
            var newSize = this.wideVersion ? 's760x500_jfs' : 's542x500_jfs'
            return url.replace('s76x76_jfs', newSize)
        },
        _onKeydownHandler: function(e) {
            var me = this
            if (e.keyCode == 37) {
                me.$showImgSwitch.find(me.cursorLeft).trigger('click')
            } else if (e.keyCode == 39) {
                me.$showImgSwitch.find(me.cursorRight).trigger('click')
            }
        }
    }

    return ShowImgSwitch
})
