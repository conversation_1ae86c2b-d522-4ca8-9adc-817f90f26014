define('MOD_ROOT/contact/im', function(require, exports, module) {
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Tools = require('MOD_ROOT/common/tools/tools');

    var IM = function(opts) {
        this.cfg = opts.cfg;
        this.sku = opts.cfg.skuid;
        this.$el = opts.$el || $('<div></div>');
        this.trigger = opts.trigger || '.J-im-button';
        this.offlineClass = opts.offlineClass || 'jd-im-offline';
        this.template = opts.template || '<a href="#none" class="J-im-button"><b></b>{text}</a>';
        this.debug = opts.debug || false;

        this.init();
    };

    IM.prototype = {
        init: function() {
            this.bindEvent();
            this.get();
        },
        bindEvent: function() {
            var _this = this;
            this.$el.delegate(this.trigger, 'click', function() {
                var domain = $(this).attr('data-domain');
                _this.open(domain);
            });
        },
        open: function(domain, params) {
            params = params || {};
            var defaultParams = {
                pid: this.cfg.skuid
            }
            $.extend(params, defaultParams);
            var chatUrl = '//{0}/index.action?'.format(domain);
            chatUrl += $.param(params);

            open(chatUrl, '京东咚咚在线客服');
        },
        exists: function (r) {
            return r && r.code && (r.code == 1 || r.code == 2 || r.code == 3 || r.code == 9);
        },
        get: function() {
            var _this = this;

            var time = new Date().getTime()
            var paramJson = {
                source: 'jd_pc_item',
                key: 'JDPC_baf0bd4ca77d4e09847b97504b8763cf',
                pid: _this.sku,
                returnCharset: 'utf-8'
            };
            var body = JSON.stringify(paramJson);
            var colorParm = {
                appid: 'item-v3',
                functionId: 'checkChat',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                body: body,
            }

            try{
                colorParm['uuid'] = Tools.getCookieNew("__jda")
            }catch(e){
                colorParm['uuid'] = ''
            }

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host,
                data: colorParm,
                // dataType: "jsonp",
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function (res) {
                    try {
                      if (window.itemEventBus) {
                        window.itemEventBus.emit('chatChange', res)
  
                        window.itemEventBus.on('chatWatch', function () {
                          window.itemEventBus.emit('chatChange', res)
                        })
                      }
                    } catch (e) {
                      console.error('客服bus异常')
                    }

                    var exists = _this.exists(res);

                    if (exists) {
                        _this.set(res);
                    }
                    
                    //自营无客服时隐藏左侧店铺评分模块
                    if( res && !res.code) {
                        if(G.isJd) {
                            $("#popbox").remove();
                            
                            
                        }
                    }
                    
                    // 接口未下发客服地址移除店铺客服
                    if (!res || res && !res.chatUrl) {
                      // 隐藏店铺客服
                      var $customer = $('.shop-information .customer-service');
                      if ($customer.length) {
                        $customer.remove()
                      }
                    }

                    Event.fire({
                        type: 'onIMReady',
                        exists: exists
                    });
                },
                error: function () {
                    Event.fire({
                        type: 'onIMReady',
                        exists: false
                    });
                }
            });

        },
        set: function(r) {
            var code = r.code;   // 1表示客服在线，2表示客服离线，3或9表示用户可以留言
            var seller = r.seller;

            // var text = this.$el.attr('data-name') || name;
            // var title = this.$el.attr('data-name') || name;
            // var cls = '';

            if( seller && seller != '' ){
                seller = seller.replace("&qt;", "'").replace("&dt;", "\"");
            }

            // if ( code == 1 ) {
            //     text = G.isJd?'联系供应商':'联系卖家';
            //     title = G.isJd?'联系供应商':'联系卖家';
            //     cls = G.isJd?'gys-im':'pop-im';

            //     if(pageConfig.product.isOtc && G.isJd || pageConfig.product.isCfy && G.isJd) {//otc 和 处方药
            //         text = '联系客服';
            //         title = '联系客服';
            //     }
            // }

            // if(G.onAttr("ContactJD")) {
            //     cls = "newjd-im";
            // }

            // if ( code == 2 ) {
            //     text = '留言咨询';
            //     cls = this.offlineClass;
            //     title = G.isJd ?
            //         '客服目前不在线！购买之前，如有问题，请在此页“全部购买咨询”中向京东客服发起咨询'
            //         : '第三方卖家客服目前不在线，您可以点击此处给第三方卖家商家留言，并在【我的京东-消息精灵】中查看回复';
            // }
            // if ( code == 3 || code == 9 ) {
            //     text = '留言咨询';
            //     cls = this.offlineClass;
            //     title = G.isJd ?'客服目前不在线，您可以点击此处给商家留言，并在【我的京东->消息精灵】中查看回复':'第三方卖家客服目前不在线，您可以点击此处给第三方卖家商家留言，并在【我的京东-消息精灵】中查看回复';
            //     code = 3;
            // }

            var text = '联系客服';
            var title = text;

            var result = $(this.template.replace('{text}', text)).attr({
                'title': title,
                'data-seller': seller,
                'data-code': code,
                'data-domain': r.chatDomain
            });

            // result.addClass(cls);
            this.$el.html(result);
        }
    };

    module.exports = IM;
});