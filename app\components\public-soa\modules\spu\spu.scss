// Base colors
$color01: #e4393c;
$color02: #005aa0;
$color03: #005aa0;

/* ypds */
#ypds-list { width:155px; margin-top:10px; }
#ypds-list .mt { border:1px solid #DEDFDE; }
#ypds-list .mc { border:1px solid #DEDFDE; border-top:none; }
#ypds-list .mt span { height:35px; line-height:35px; padding:0 10px; }
#ypds-list .mc li { height:30px; margin:0 5px -1px 5px; padding:0 5px; line-height:30px; border-bottom:1px dotted #dedfde; clear:both; }
#ypds-list .mc li .lh { text-align:right; }

.root61 #ypds-list { width: 210px; }


/**
 * 一品多商商家列表
 */
.pop-store-list{
    width: 200px;
    padding: 10px 0;
    border-top: 1px solid #eee;

    .pop-store-item{
        width: 100%;
        height: 26px;
        line-height: 26px;
        overflow: hidden;

        .stores{
            font-weight: bold;
            color: $color03;
        }
        .store-name{
            color: #999;
        }
        .store-name:hover{
            color: #e4393c;
        }
        .price{
            color: $color01;
        }
        .c-left{
            width: 125px;
            float:left;

        }
        .c-right{
            width: 72px;
            float:right;
            text-align: right;
        }

    }

    .btnbox{
        padding: 15px 0 15px;
        text-align: center;
    }

}


// 一品多商信息

.ypds-wrap {

}

#ypds-info {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    border: 1px solid #DEDFDE;
    padding: 4px 10px;
    margin: 0 0 20px 10px;
    white-space: nowrap;
}
