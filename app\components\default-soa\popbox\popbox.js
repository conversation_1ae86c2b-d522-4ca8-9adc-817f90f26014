define('MOD_ROOT/popbox/popbox', function(require, exports, module) {
    require('PLG_ROOT/jQuery.scroller');
    var G = require('MOD_ROOT/common/core');
    var IM = require('MOD_ROOT/contact/im');

    function init(cfg) {
        // im 留言咨询
        // cfg.IMContact = new IM({
        //     cfg: cfg,
        //     $el: $('.J-popbox-im'),
        //     trigger: '.im',
        //     template: '<div class="im customer-service"><i class="sprite-im"></i></div>'
        // });

        setFixedNav($("#popbox"));
    }

    function setFixedNav($popbox) {
        $popbox.scroller({
            delay: 0,
            end: $('#footmark'),
            onStart: function() {
                this.$el.find('[data-fixed]').addClass('pro-detail-hd-fixed');
            },
            onEnd: function() {
                this.$el.find('[data-fixed]').removeClass('pro-detail-hd-fixed');
            }
        });
    }

    module.exports.__id = 'popbox';
    module.exports.init = init;
});
