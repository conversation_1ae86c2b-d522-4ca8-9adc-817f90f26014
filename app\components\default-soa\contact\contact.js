define('MOD_ROOT/contact/contact', function(require, exports, module) {
    require('MOD_ROOT/EDropdown/EDropdown');
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var IM = require('MOD_ROOT/contact/im');

    function jimi(cfg, callback) {
        if (cfg.jimiData) {
            return callback(cfg.jimiData);
        }

        var param = {
            skuId: cfg.skuid,
            c1: cfg.cat[0],
            c2: cfg.cat[1],
            c3: cfg.cat[2],
            venderId: cfg.venderId
        };

        $.ajax({
            url: '//entry-jimi.jd.com/iadshop/checkJimiStatus?' + $.param(param),
            dataType: 'jsonp',
            scriptCharset: 'utf-8',
            cache: true,
            success: function(r) {
                if (/debug=im/.test(location.href)) {
                    alert(r.code);
                }
                cfg.jimiData = r;
                callback(r);
            }
        })
    }

    function renderJimi($el, text, cfg, r, exists) {
        var html = '\
            <div class="jimi customer-service">\
                <a href="//jimi.jd.com/index.action?productId={0}&source={1}" target="_blank">\
                    <i class="sprite-jimi"></i>' + text + '\
                </a> \
            </div>';

        if (exists) {
            $el.html(html.format(cfg.skuid, G.isPop ? 'productpop' : 'productself'));
        }
    }

    function weichatQr(cfg) {
        var $qrWD = $('.J-wd-qrcode')
        var $qrM = $('.J-m-qrcode')

        getWDQrcode()
        getMQrcode()

        function getMQrcode() {
            var $wrap = $qrM.find('.J-m-wrap')
            var url = 'https://item.m.jd.com/product/{0}.html?pc_source=pc_productDetail_{0}'.format(
                cfg.skuid
            )
            var img = '<img src="//qrimg.jd.com/{0}-118-1-4-0.png?ltype=0" width="118" height="118" />'.format(
                encodeURIComponent(url)
            )
            $wrap.html('').html(img)
        }
        function getWDQrcode() {
            var $img = $qrWD.find('img')
            $.ajax({
                url: '//wq.jd.com/weidian/weixin/GetQRCode?venderid=' +
                    cfg.venderId,
                dataType: 'jsonp',
                cache: true,
                jsonpCallback: 'getQRCode',
                success: function(r) {
                    if (r && r.errcode == 0 && r.data.qrurl != '') {
                        $img.attr('src', r.data.qrurl)
                        $qrWD.show()
                    }
                }
            })
        }
    }

    function showContactText() {
        $('.J-contact-text').show()
    }

    function init(cfg) {
        var $root = $('#crumb-wrap');
        
        $(".goodshop", $root).EDropdown();

        $('.J-hove-wrap', $root).EDropdown({
            onOpen: function() {
                if (!this.$el.attr('opened')) {
                    weichatQr(cfg)
                    this.$el.attr('opened', '1')
                }
            }
        });

        // im 留言咨询
        // pageConfig.IMContact = new IM({
        //     cfg: cfg,
        //     $el: $('.J-im-btn'),
        //     trigger: '.im',
        //     template: '<div class="im customer-service"><i class="sprite-im"></i>{text}</div>'
        // });

        Event.addListener('onIMReady', function(d) {
            if (d.exists) {
                showContactText();
                $('.J-im-item').show();
            } else {
                // jimi(cfg, function(r) {
                //     var exists = (r && r.code === 1);
                //     renderJimi($('.J-jimi-btn').eq(0), '联系客服', cfg, r, exists);
                //     renderJimi($('.J-jimi-btn').eq(1), '联系客服', cfg, r, exists);
                //     Event.fire({
                //         type: 'onJimiReady',
                //         exists: exists
                //     });
                // });
            }
        });

        // Event.addListener('onJimiReady', function(d) {
        //     if (d.exists) {
        //         showContactText();
        //         $('.J-jimi-item').show();
        //     }
        // });

    }

    module.exports.__id = 'contact'
    module.exports.init = init
})
