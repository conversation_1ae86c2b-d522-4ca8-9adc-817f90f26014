define('MOD_ROOT/city/city', function(require, exports, module) {
    var G = require('MOD_ROOT/common/core')
    var Event = require('MOD_ROOT/common/tools/event').Event
    //var Area = require('MOD_ROOT/address/area')
    require('//static.360buyimg.com/item/assets/address/area');
    var Area =  common_getAreaMap();
    var cityMap = common_getCityMap();
    // var cityMap = {
    //     '朝阳区': '1|72',
    //     '海淀区': '1|2800',
    //     '西城区': '1|2801',
    //     '东城区': '1|2802',
    //     '崇文区': '1|2803',
    //     '宣武区': '1|2804',
    //     '丰台区': '1|2805',
    //     '石景山区': '1|2806',
    //     '门头沟': '1|2807',
    //     '房山区': '1|2808',
    //     '通州区': '1|2809',
    //     '大兴区': '1|2810',
    //     '顺义区': '1|2812',
    //     '怀柔区': '1|2814',
    //     '密云区': '1|2816',
    //     '昌平区': '1|2901',
    //     '平谷区': '1|2953',
    //     '延庆县': '1|3065',
    //     '徐汇区': '2|2813',
    //     '长宁区': '2|2815',
    //     '静安区': '2|2817',
    //     '闸北区': '2|2820',
    //     '虹口区': '2|2822',
    //     '杨浦区': '2|2823',
    //     '宝山区': '2|2824',
    //     '闵行区': '2|2825',
    //     '嘉定区': '2|2826',
    //     '浦东新区': '2|2830',
    //     '青浦区': '2|2833',
    //     '松江区': '2|2834',
    //     '金山区': '2|2835',
    //     '奉贤区': '2|2837',
    //     '普陀区': '2|2841',
    //     '崇明县': '2|2919',
    //     '黄浦区': '2|78',
    //     '东丽区': '3|51035',
    //     '和平区': '3|51036',
    //     '河北区': '3|51037',
    //     '河东区': '3|51038',
    //     '河西区': '3|51039',
    //     '红桥区': '3|51040',
    //     '蓟县': '3|51041',
    //     '静海县': '3|51042',
    //     '南开区': '3|51043',
    //     '塘沽区': '3|51044',
    //     '西青区': '3|51045',
    //     '武清区': '3|51046',
    //     '津南区': '3|51047',
    //     '汉沽区': '3|51048',
    //     '大港区': '3|51049',
    //     '北辰区': '3|51050',
    //     '宝坻区': '3|51051',
    //     '宁河县': '3|51052',
    //     '万州区': '4|113',
    //     '涪陵区': '4|114',
    //     '梁平县': '4|115',
    //     '南川区': '4|119',
    //     '潼南县': '4|123',
    //     '大足区': '4|126',
    //     '黔江区': '4|128',
    //     '武隆县': '4|129',
    //     '丰都县': '4|130',
    //     '奉节县': '4|131',
    //     '开县': '4|132',
    //     '云阳县': '4|133',
    //     '忠县': '4|134',
    //     '巫溪县': '4|135',
    //     '巫山县': '4|136',
    //     '石柱县': '4|137',
    //     '彭水县': '4|138',
    //     '垫江县': '4|139',
    //     '酉阳县': '4|140',
    //     '秀山县': '4|141',
    //     '璧山县': '4|48131',
    //     '荣昌县': '4|48132',
    //     '铜梁县': '4|48133',
    //     '合川区': '4|48201',
    //     '巴南区': '4|48202',
    //     '北碚区': '4|48203',
    //     '江津区': '4|48204',
    //     '渝北区': '4|48205',
    //     '长寿区': '4|48206',
    //     '永川区': '4|48207',
    //     '江北区': '4|50950',
    //     '南岸区': '4|50951',
    //     '九龙坡区': '4|50952',
    //     '沙坪坝区': '4|50953',
    //     '大渡口区': '4|50954',
    //     '綦江区': '4|50995',
    //     '渝中区': '4|51026',
    //     '高新区': '4|51027',
    //     '北部新区': '4|51028',
    //     '城口县': '4|4164',
    //     '石家庄市': '5|142',
    //     '邯郸市': '5|148',
    //     '邢台市': '5|164',
    //     '保定市': '5|199',
    //     '张家口市': '5|224',
    //     '承德市': '5|239',
    //     '秦皇岛市': '5|248',
    //     '唐山市': '5|258',
    //     '沧州市': '5|264',
    //     '廊坊市': '5|274',
    //     '衡水市': '5|275',
    //     '太原市': '6|303',
    //     '大同市': '6|309',
    //     '阳泉市': '6|318',
    //     '晋城市': '6|325',
    //     '朔州市': '6|330',
    //     '晋中市': '6|336',
    //     '忻州市': '6|350',
    //     '吕梁市': '6|368',
    //     '临汾市': '6|379',
    //     '运城市': '6|398',
    //     '长治市': '6|3074',
    //     '郑州市': '7|412',
    //     '开封市': '7|420',
    //     '洛阳市': '7|427',
    //     '平顶山市': '7|438',
    //     '焦作市': '7|446',
    //     '鹤壁市': '7|454',
    //     '新乡市': '7|458',
    //     '安阳市': '7|468',
    //     '濮阳市': '7|475',
    //     '许昌市': '7|482',
    //     '漯河市': '7|489',
    //     '三门峡市': '7|495',
    //     '南阳市': '7|502',
    //     '商丘市': '7|517',
    //     '周口市': '7|527',
    //     '驻马店市': '7|538',
    //     '信阳市': '7|549',
    //     '济源市': '7|2780',
    //     '沈阳市': '8|560',
    //     '大连市': '8|573',
    //     '鞍山市': '8|579',
    //     '抚顺市': '8|584',
    //     '本溪市': '8|589',
    //     '丹东市': '8|593',
    //     '锦州市': '8|598',
    //     '葫芦岛市': '8|604',
    //     '营口市': '8|609',
    //     '盘锦市': '8|613',
    //     '阜新市': '8|617',
    //     '辽阳市': '8|621',
    //     '朝阳市': '8|632',
    //     '铁岭市': '8|6858',
    //     '长春市': '9|639',
    //     '吉林市': '9|644',
    //     '四平市': '9|651',
    //     '辽源市': '9|2992',
    //     '通化市': '9|657',
    //     '白山市': '9|664',
    //     '松原市': '9|674',
    //     '白城市': '9|681',
    //     '延边州': '9|687',
    //     '鹤岗市': '10|727',
    //     '双鸭山市': '10|731',
    //     '鸡西市': '10|737',
    //     '大庆市': '10|742',
    //     '伊春市': '10|753',
    //     '牡丹江市': '10|757',
    //     '佳木斯市': '10|765',
    //     '七台河市': '10|773',
    //     '黑河市': '10|776',
    //     '绥化市': '10|782',
    //     '大兴安岭地区': '10|793',
    //     '哈尔滨市': '10|698',
    //     '齐齐哈尔市': '10|712',
    //     '呼和浩特市': '11|799',
    //     '包头市': '11|805',
    //     '乌海市': '11|810',
    //     '赤峰市': '11|812',
    //     '乌兰察布市': '11|823',
    //     '锡林郭勒盟': '11|835',
    //     '呼伦贝尔市': '11|848',
    //     '鄂尔多斯市': '11|870',
    //     '巴彦淖尔市': '11|880',
    //     '阿拉善盟': '11|891',
    //     '兴安盟': '11|895',
    //     '通辽市': '11|902',
    //     '南京市': '12|904',
    //     '徐州市': '12|911',
    //     '连云港市': '12|919',
    //     '淮安市': '12|925',
    //     '宿迁市': '12|933',
    //     '盐城市': '12|939',
    //     '扬州市': '12|951',
    //     '泰州市': '12|959',
    //     '南通市': '12|965',
    //     '镇江市': '12|972',
    //     '常州市': '12|978',
    //     '无锡市': '12|984',
    //     '苏州市': '12|988',
    //     '济宁市': '13|2900',
    //     '济南市': '13|1000',
    //     '青岛市': '13|1007',
    //     '淄博市': '13|1016',
    //     '枣庄市': '13|1022',
    //     '东营市': '13|1025',
    //     '潍坊市': '13|1032',
    //     '烟台市': '13|1042',
    //     '威海市': '13|1053',
    //     '莱芜市': '13|1058',
    //     '德州市': '13|1060',
    //     '临沂市': '13|1072',
    //     '聊城市': '13|1081',
    //     '滨州市': '13|1090',
    //     '菏泽市': '13|1099',
    //     '日照市': '13|1108',
    //     '泰安市': '13|1112',
    //     '黄山市': '14|1151',
    //     '滁州市': '14|1159',
    //     '阜阳市': '14|1167',
    //     '亳州市': '14|1174',
    //     '宿州市': '14|1180',
    //     '池州市': '14|1201',
    //     '六安市': '14|1206',
    //     '宣城市': '14|2971',
    //     '铜陵市': '14|1114',
    //     '合肥市': '14|1116',
    //     '淮南市': '14|1121',
    //     '淮北市': '14|1124',
    //     '芜湖市': '14|1127',
    //     '蚌埠市': '14|1132',
    //     '马鞍山市': '14|1137',
    //     '安庆市': '14|1140',
    //     '宁波市': '15|1158',
    //     '衢州市': '15|1273',
    //     '丽水市': '15|1280',
    //     '台州市': '15|1290',
    //     '舟山市': '15|1298',
    //     '杭州市': '15|1213',
    //     '温州市': '15|1233',
    //     '嘉兴市': '15|1243',
    //     '湖州市': '15|1250',
    //     '绍兴市': '15|1255',
    //     '金华市': '15|1262',
    //     '福州市': '16|1303',
    //     '厦门市': '16|1315',
    //     '三明市': '16|1317',
    //     '莆田市': '16|1329',
    //     '泉州市': '16|1332',
    //     '漳州市': '16|1341',
    //     '南平市': '16|1352',
    //     '龙岩市': '16|1362',
    //     '宁德市': '16|1370',
    //     '孝感市': '17|1432',
    //     '黄冈市': '17|1441',
    //     '咸宁市': '17|1458',
    //     '恩施州': '17|1466',
    //     '鄂州市': '17|1475',
    //     '荆门市': '17|1477',
    //     '随州市': '17|1479',
    //     '神农架林区': '17|3154',
    //     '武汉市': '17|1381',
    //     '黄石市': '17|1387',
    //     '襄阳市': '17|1396',
    //     '十堰市': '17|1405',
    //     '荆州市': '17|1413',
    //     '宜昌市': '17|1421',
    //     '潜江市': '17|2922',
    //     '天门市': '17|2980',
    //     '仙桃市': '17|2983',
    //     '长沙市': '18|1482',
    //     '株洲市': '18|1488',
    //     '湘潭市': '18|1495',
    //     '衡阳市': '18|1501',
    //     '邵阳市': '18|1511',
    //     '岳阳市': '18|1522',
    //     '常德市': '18|1530',
    //     '张家界市': '18|1540',
    //     '郴州市': '18|1544',
    //     '益阳市': '18|1555',
    //     '永州市': '18|1560',
    //     '怀化市': '18|1574',
    //     '娄底市': '18|1586',
    //     '湘西州': '18|1592',
    //     '广州市': '19|1601',
    //     '深圳市': '19|1607',
    //     '珠海市': '19|1609',
    //     '汕头市': '19|1611',
    //     '韶关市': '19|1617',
    //     '河源市': '19|1627',
    //     '梅州市': '19|1634',
    //     '揭阳市': '19|1709',
    //     '惠州市': '19|1643',
    //     '汕尾市': '19|1650',
    //     '东莞市': '19|1655',
    //     '中山市': '19|1657',
    //     '江门市': '19|1659',
    //     '佛山市': '19|1666',
    //     '阳江市': '19|1672',
    //     '湛江市': '19|1677',
    //     '茂名市': '19|1684',
    //     '肇庆市': '19|1690',
    //     '云浮市': '19|1698',
    //     '清远市': '19|1704',
    //     '潮州市': '19|1705',
    //     '崇左市': '20|3168',
    //     '南宁市': '20|1715',
    //     '柳州市': '20|1720',
    //     '桂林市': '20|1726',
    //     '梧州市': '20|1740',
    //     '北海市': '20|1746',
    //     '防城港市': '20|1749',
    //     '钦州市': '20|1753',
    //     '贵港市': '20|1757',
    //     '玉林市': '20|1761',
    //     '贺州市': '20|1792',
    //     '百色市': '20|1806',
    //     '河池市': '20|1818',
    //     '来宾市': '20|3044',
    //     '南昌市': '21|1827',
    //     '景德镇市': '21|1832',
    //     '萍乡市': '21|1836',
    //     '新余市': '21|1842',
    //     '九江市': '21|1845',
    //     '鹰潭市': '21|1857',
    //     '上饶市': '21|1861',
    //     '宜春市': '21|1874',
    //     '抚州市': '21|1885',
    //     '吉安市': '21|1898',
    //     '赣州市': '21|1911',
    //     '凉山州': '22|2103',
    //     '成都市': '22|1930',
    //     '自贡市': '22|1946',
    //     '攀枝花市': '22|1950',
    //     '泸州市': '22|1954',
    //     '绵阳市': '22|1960',
    //     '德阳市': '22|1962',
    //     '广元市': '22|1977',
    //     '遂宁市': '22|1983',
    //     '内江市': '22|1988',
    //     '乐山市': '22|1993',
    //     '宜宾市': '22|2005',
    //     '广安市': '22|2016',
    //     '南充市': '22|2022',
    //     '达州市': '22|2033',
    //     '巴中市': '22|2042',
    //     '雅安市': '22|2047',
    //     '眉山市': '22|2058',
    //     '资阳市': '22|2065',
    //     '阿坝州': '22|2070',
    //     '甘孜州': '22|2084',
    //     '三亚市': '23|3690',
    //     '文昌市': '23|3698',
    //     '五指山市': '23|3699',
    //     '临高县': '23|3701',
    //     '澄迈县': '23|3702',
    //     '定安县': '23|3703',
    //     '屯昌县': '23|3704',
    //     '昌江县': '23|3705',
    //     '白沙县': '23|3706',
    //     '琼中县': '23|3707',
    //     '陵水县': '23|3708',
    //     '保亭县': '23|3709',
    //     '乐东县': '23|3710',
    //     '三沙市': '23|3711',
    //     '海口市': '23|2121',
    //     '琼海市': '23|3115',
    //     '万宁市': '23|3137',
    //     '东方市': '23|3173',
    //     '儋州市': '23|3034',
    //     '贵阳市': '24|2144',
    //     '六盘水市': '24|2150',
    //     '遵义市': '24|2155',
    //     '铜仁市': '24|2169',
    //     '毕节市': '24|2180',
    //     '安顺市': '24|2189',
    //     '黔西南州': '24|2196',
    //     '黔东南州': '24|2205',
    //     '黔南州': '24|2222',
    //     '迪庆州': '25|4108',
    //     '昆明市': '25|2235',
    //     '曲靖市': '25|2247',
    //     '玉溪市': '25|2258',
    //     '昭通市': '25|2270',
    //     '普洱市': '25|2281',
    //     '临沧市': '25|2291',
    //     '保山市': '25|2298',
    //     '丽江市': '25|2304',
    //     '文山州': '25|2309',
    //     '红河州': '25|2318',
    //     '西双版纳州': '25|2332',
    //     '楚雄州': '25|2336',
    //     '大理州': '25|2347',
    //     '德宏州': '25|2360',
    //     '怒江州': '25|2366',
    //     '阿里地区': '26|3970',
    //     '林芝地区': '26|3971',
    //     '拉萨市': '26|2951',
    //     '那曲地区': '26|3107',
    //     '山南地区': '26|3129',
    //     '昌都地区': '26|3138',
    //     '日喀则地区': '26|3144',
    //     '延安市': '27|2428',
    //     '汉中市': '27|2442',
    //     '榆林市': '27|2454',
    //     '商洛市': '27|2468',
    //     '安康市': '27|2476',
    //     '西安市': '27|2376',
    //     '铜川市': '27|2386',
    //     '宝鸡市': '27|2390',
    //     '咸阳市': '27|2402',
    //     '渭南市': '27|2416',
    //     '庆阳市': '28|2525',
    //     '陇南市': '28|2534',
    //     '武威市': '28|2544',
    //     '张掖市': '28|2549',
    //     '酒泉市': '28|2556',
    //     '甘南州': '28|2564',
    //     '临夏州': '28|2573',
    //     '定西市': '28|3080',
    //     '兰州市': '28|2487',
    //     '金昌市': '28|2492',
    //     '白银市': '28|2495',
    //     '天水市': '28|2501',
    //     '嘉峪关市': '28|2509',
    //     '平凉市': '28|2518',
    //     '西宁市': '29|2580',
    //     '海东地区': '29|2585',
    //     '海北州': '29|2592',
    //     '黄南州': '29|2597',
    //     '海南州': '29|2603',
    //     '果洛州': '29|2605',
    //     '玉树州': '29|2612',
    //     '海西州': '29|2620',
    //     '银川市': '30|2628',
    //     '石嘴山市': '30|2632',
    //     '吴忠市': '30|2637',
    //     '固原市': '30|2644',
    //     '中卫市': '30|3071',
    //     '五家渠市': '31|4110',
    //     '博尔塔拉蒙古自治州阿拉山口口岸': '31|4163',
    //     '阿拉尔市': '31|15945',
    //     '图木舒克市': '31|15946',
    //     '乌鲁木齐市': '31|2652',
    //     '克拉玛依市': '31|2654',
    //     '石河子市': '31|2656',
    //     '吐鲁番地区': '31|2658',
    //     '哈密地区': '31|2662',
    //     '和田地区': '31|2666',
    //     '阿克苏地区': '31|2675',
    //     '喀什地区': '31|2686',
    //     '克孜勒苏州': '31|2699',
    //     '巴音郭楞州': '31|2704',
    //     '昌吉州': '31|2714',
    //     '博尔塔拉州': '31|2723',
    //     '伊犁州': '31|2727',
    //     '塔城地区': '31|2736',
    //     '阿勒泰地区': '31|2744',
    //     '台湾市': '32|2768',
    //     '钓鱼岛': '84|1310',
    //     '香港特别行政区': '52993|52994',
    //     '澳门特别行政区': '52993|52995'
    // }

    var City = {
        init: function(cfg) {
            this.$deco = $('#choose-decorate-city')
            this.$el = $('#choose-decorate-city .J-city-wrap')
            this.$curr = this.$el.find('.J-selected')

            this.cfg = cfg

            if (this.$el.length) {
                if (cfg.colorSize.length) {
                    this.bindEvent()
                    this.renderAreas(cfg.colorSize)
                } else {
                    this.$deco.hide()
                }
                this.setServices(cfg)
            }
        },
        bindEvent: function() {
            this.$el.ETab()
            this.tab = this.$el.data('ETab')

            this.$el.delegate(
                '[data-val]',
                'click',
                $.proxy(this.handleAreaClick, this)
            )

            this.$el.hover(
                function() {
                    $(this).addClass('hover')
                },
                function() {
                    $(this).removeClass('hover')
                }
            )
        },
        handleAreaClick: function(e) {
            var $this = $(e.currentTarget)
            var $par = $this.parents('[data-idx]').eq(0)
            if ($par.length) {
                var idx = $par.attr('data-idx')
                if (idx !== '1') {
                    this.switchTo($this.attr('data-val'))
                    this.tab.go(1)
                }
            }
        },
        switchTo: function(pid) {
            var $items = this.tab.items.eq(1).find('[data-pid')
            $items.each(function() {
                if ($(this).attr('data-pid') === pid) {
                    $(this).show()
                } else {
                    $(this).hide()
                }
            })
        },
        setServices: function(cfg) {
            // function setSpecialServices(services) {
            //     var $el = $('#summary-ss .dd')
            //     var html = ''
            //     for (var i = 0; i < services.length; i++) {
            //         var service = services[i]
            //         html += '<span>' + service + '</span>'
            //     }
            //     $el.html(html).parent().show()
            // }

            // // 首屏“特色服务”
            // $.ajax({
            //     url: '//dx.3.cn/decorate/' + cfg.venderId + '-' + cfg.mainSkuId,
            //     dataType: 'jsonp',
            //     jsonpCallback: 'showDecorate',
            //     success: function(r) {
            //         if (r) {
            //             if (r.specialServices && r.specialServices.length) {
            //                 setSpecialServices(r.specialServices)
            //             }
            //         }
            //     }
            // });

            /**
             * 轮询函数
             * @param {Number} delay 轮询频次
             * @param {Function} callback
             */
            function rolling(delay, callback) {
                if (typeof delay === 'number' &&
                    typeof callback === 'function') {
                    var timer = setTimeout(function () {
                        callback(timer);
                        rolling(delay, callback);
                    }, delay);
                } else {
                    throw '参数错误';
                }
            }

            // 更改“装修案例”模块内容
            var $decorationCase = $('#decoration-case');
            var $iframe = $('<iframe>').attr({
                    src: '//ihome.jd.com/vender/zxList?venderid=' + cfg.venderId,
                    width: '100%',
                    height: '0',  // 屏蔽滚动条次体验效果
                    scrolling: 'no',
                    frameborder: 0
                });

            $iframe.load(function () {
                var $iframeBody = $iframe.contents().find('body');
                var heightFlag = 0;
                $iframeBody.find('.fitment-con').css('margin-top', 0); // hack TabBar遮挡问题
                rolling(200, function (timer) {
                    var h = $iframeBody.height();
                    if (h !== heightFlag) {
                        heightFlag = h;
                        $iframe.height(h + parseInt($iframeBody.find('.fitment-con').css('margin-top'))); // 由于浏览器的bug需要加个偏移量
                    }
                });
                $iframeBody.delegate('.pager a', 'click', function () {
                    var $this = $(this);
                    if (!$this.hasClass('cur')) {
                        $(window).scrollTop($decorationCase.offset().top);
                    }
                });
            });

            $decorationCase.empty().append($iframe);

        },
        // 反向获取一二级地址数据
        getCityData: function(colors) {
            var res = {
                areaMap: {},
                data: {
                    '0': [],
                    '1': []
                },
                skuMap: {}
            }
            for (var i = 0; i < colors.length; i++) {
                var c = colors[i]
                var color = c['颜色']
                var sku = c['skuId']
                var d = cityMap[c['颜色']]
                var ids = d ? d.split('|') : null
                if (ids) {
                    res.skuMap[color] = sku
                    if (!res.areaMap[ids[0]]) {
                        res.areaMap[ids[0]] = true
                        res.data['0'].push({
                            id: ids[0],
                            name: Area.provinceMap[[ids[0]]]
                        })
                    }
                    if (!res.areaMap[ids[1]]) {
                        res.areaMap[ids[1]] = true
                        res.data['1'].push({
                            id: ids[1],
                            name: color
                        })
                    }
                } else {
                    if (typeof console !== 'undefined') {
                        console.warn('没有找到[颜色]属性key')
                    }
                }
                if (ids && sku === this.cfg.skuid) {
                    this.setCurrent(ids, color)
                }
            }
            return res
        },
        setCurrent: function(id, color) {
            this.$curr.attr('data-value', id[1]).text(color)

            // 添加全局变量
            this.cfg.decorationCurrentCity = this.getCityId(color)
            this.cfg.decorationCurrentCityName = color
            this.cfg.decorationCurrentCityIds = id
        },
        getCityId: function(name) {
            var d = cityMap[name]
            return d ? d.split('|')[0] : null
        },
        getUrl: function(sku) {
            var des = '//{0}/{1}.html'

            return des.format(location.hostname, sku) + location.search
        },
        renderAreas: function(colors) {
            var tpl = {
                '0': '<li data-val="{0}"><a href="#none">{1}</a></li>',
                '1': '<li data-val="{0}" data-pid="{1}"><a href="{2}">{3}</a></li>'
            }
            var html = {}
            var r = this.getCityData(colors)
            var data = r.data
            var skuMap = r.skuMap

            for (var d in data) {
                if (!data.hasOwnProperty(d)) break
                html[d] = ''

                for (var i = 0; i < data[d].length; i++) {
                    var area = data[d][i]
                    if (d !== '0') {
                        html[d] += tpl[d].format(
                            area.id,
                            this.getCityId(area.name),
                            this.getUrl(skuMap[area.name]),
                            area.name
                        )
                    } else {
                        html[d] += tpl[d].format(area.id, area.name)
                    }
                }
                this.tab.items.eq(d).html(html[d])
            }
        }
    }

    function init(cfg) {
        City.init(cfg)
        // setDecorateArea(cfg)
    }

    module.exports.__id = 'address'
    module.exports.init = init
})
