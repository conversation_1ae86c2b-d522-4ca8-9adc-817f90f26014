define('MOD_ROOT/videoBox/videoAnchorPoint', function(require, exports, module) {
    //视频锚点
    require('./videoAnchorPoint.css')

    // 页面配置
    var config = {
        // 插件默认参数配置
        defaults: {
            width: 450, //视频默认宽度
            height: 300, //视频默认高度
            currentIndex: -1,
            player: null,
            b_version: 10

        },
    };


    // 一些通用的方法
    var tools = {
        // 添加事件
        EventUtil: {
            addEvent:function(element,type,handler){ //添加绑定
                element = Array.prototype.slice.apply(element);
                for (var i=0; i<element.length; i++) {
                    if(element[i].addEventListener){
                        element[i].addEventListener(type,handler,false);
                    }else if(element[i].attachEvent){
                        element[i].attachEvent('on'+type,handler);
                    }else{
                        element[i]['on'+type]=handler;
                    }
                }

            },
            getEvent:function(event){ //返回事件对象引用
                return event?event:window.event;
            },
            getTarget:function(event){ //返回事件源目标
                return event.target||event.srcElement;
            },
            preventDefault:function(event){ //取消默认事件
                if(event.preventDefault){
                    event.preventDefault();
                }else{
                    event.returnValue=false;
                }
            },
            stoppropagation:function(event){ //阻止事件流
                if(event.stoppropagation){
                    event.stoppropagation();
                }else{
                    event.canceBubble=false;
                }
            }
        },
        // 对象扩展
        extendObj: function(deep, target, options) {
            var me = this;
            if (!options) {
                return target;
            }
            for (var name in options) {
                var copy = options[name];
                if (deep && copy instanceof Array) {
                    target[name] = me.extendObj(deep, [], copy);
                } else if (deep && copy instanceof Object) {
                    target[name] = me.extendObj(deep, {}, copy);
                } else {
                    target[name] = options[name];
                }
            }
            return target;
        },
        // 判断是否是IE并且大于IE9
        getIeBrowserVersion: function () {
            var userAgent = navigator.userAgent;
            var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !(userAgent.indexOf("Opera") > -1);

            if (isIE) {
                var IE5 = IE55 = IE6 = IE7 = IE8 = false;
                var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                // reIE.test(userAgent);
                var fIEVersion = parseFloat(RegExp["$1"]);
                IE55 = fIEVersion == 5.5;
                IE6 = fIEVersion == 6.0;
                IE7 = fIEVersion == 7.0;
                IE8 = fIEVersion == 8.0;
                if (IE55) {
                    return 5;
                }
                if (IE6) {
                    return 6;
                }
                if (IE7) {
                    return 7;
                }
                if (IE8) {
                    return 8;
                } else {
                    return 9
                }
            } else {
                return 10
            }
        },
        // 添加class
        addClass: function (eleList, cls){
            eleList = Array.prototype.slice.apply(eleList);
            var add = function (obj) {
                var obj_class = obj.className; //获取 class 内容.
                var blank = (obj_class != '') ? ' ' : '';//判断获取到的 class 是否为空, 如果不为空在前面加个'空格'.
                var added = obj_class + blank + cls;//组合原来的 class 和需要添加的 class.
                obj.className = added;//替换原来的 class.
            }
            if (eleList instanceof Array) {
                for (var i=0; i<eleList.length; i++) {
                    add(eleList[i]);
                }
            } else {
                add(eleList);
            }
        },
        // 移除class
        removeClass: function (eleList, cls) {
            eleList = Array.prototype.slice.apply(eleList);
            var remove = function (obj) {
                var obj_class = ' '+obj.className+' ';
                obj_class = obj_class.replace(/(\s+)/gi, ' ');
                var removed = obj_class.replace(' '+cls+' ', ' ');
                removed = removed.replace(/(^\s+)|(\s+$)/g, '');
                obj.className = removed;//替换原来的 class.
            }
            if (eleList instanceof Array) {
                for (var i=0; i<eleList.length; i++) {
                    remove(eleList[i]);
                }
            } else {
                remove(eleList)
            }

        },
        // 类选择器
        getElementsByClassName: function (className,context,tagName) {

            if(typeof context == 'string'){
                tagName = context;
                context = document;
            }else{
                context = context || document;
                tagName = tagName || '*';
            }
            if(context.getElementsByClassName){
                return context.getElementsByClassName(className);
            }
            var nodes = context.getElementsByTagName(tagName);
            var results= [];
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                var classNames = node.className.split(' ');
                for (var j = 0; j < classNames.length; j++) {
                    if (classNames[j] == className) {
                        results.push(node);
                        break;
                    }
                }
            }
            return results;
        },
        // 获取数据
        getData: function (options) {
            //格式化参数
            var formatParams = function (data) {
                var arr = [];
                for (var name in data) {
                    arr.push(encodeURIComponent(name) + '=' + encodeURIComponent(data[name]));
                }
                return arr.join('&');
            };
            var jsonp = function (options) {
                options = options || {};
                if (!options.url || !options.callback) {
                    throw new Error("参数不合法");
                }
                //创建 script 标签并加入到页面中
                var callbackName = ('jsonp_' + Math.random()).replace(".", "");
                var oHead = document.getElementsByTagName('head')[0];
                options.data[options.callback] = callbackName;
                var params = formatParams(options.data);
                var _oScript = document.createElement('script');
                oHead.appendChild(_oScript);

                //创建jsonp回调函数
                window[callbackName] = function (json) {
                    oHead.removeChild(_oScript);
                    clearTimeout(_oScript.timer);
                    window[callbackName] = null;
                    options.success && options.success(json);
                };

                //发送请求
                _oScript.src = options.url + '?' + params;

                //超时处理
                if (options.time) {
                    _oScript.timer = setTimeout(function () {
                        window[callbackName] = null;
                        oHead.removeChild(_oScript);
                        options.fail && options.fail({ message: "超时" });
                    }, options.time);
                }
            }
            jsonp(options);
        },
        //videojs版本兼容
        getVideoElement:function (player,elClass) {
            // || videojs.players["video-player"].controlBar.progressControl.seekBar.b
            //    找属性，不具有公用性，废弃
            if(typeof player.$ == 'function'){
                return player.$('.' + elClass);
            }else{
                return this.getElementsByClassName(elClass)[0];
            }
        },
        getVideoDom:function (player) {
           return player.el_ || player.b || null;
        }

    };

    config.defaults.b_version = tools.getIeBrowserVersion();

    // videojs相关参数
    var videoAnchorPoint = {
        // 视屏打点
        videoDot:function(player ,lists){
            var player         = player;
            // var progressHolder = player.$('.vjs-progress-holder');
            // var progressControl = player.$('.vjs-progress-control');
            // var controlBar = player.$('.vjs-control-bar');
            var progressHolder = tools.getVideoElement(player,'vjs-progress-holder') ;
            var progressControl = tools.getVideoElement(player,'vjs-progress-control');

            if(tools.getVideoElement(player,'st-video-dotCon') || !progressHolder ) return false;

            var progressWidth  = progressHolder.clientWidth;

            var dotArray       = lists;
            var duration       = player.duration();

            var geneditDots  = function(){

                /* Dots Div */
                // var dotDiv = player.$('.st-video-dotCon') || undefined;
                // var dotTipWrap = player.$('.st-video-dotTipWrap') || undefined;

                var dotDiv = tools.getVideoElement(player,'st-video-dotCon')|| undefined;
                var dotTipWrap = tools.getVideoElement(player,'st-video-dotTipWrap') || undefined;

                if(!dotDiv){
                    dotDiv = document.createElement('div');
                    dotTipWrap = document.createElement('div');
                }
                else{
                    dotDiv.innerHTML = "";
                    dotTipWrap.innerHTML = "";
                }
                dotDiv.setAttribute('class', 'st-video-dotCon');
                dotTipWrap.setAttribute('class', 'st-video-dotTipWrap');

                var dotTipCon = document.createElement('div');
                dotTipCon.setAttribute('class', 'st-video-dotTipCon');
                /* Dots */
                for(var i = 0; i < dotArray.length; i++){
                    var dotEl = document.createElement('div');

                    dotEl.setAttribute('class', 'st-video-dot');
                    dotEl.setAttribute('data-index', i);
                    dotEl.style.left = (((dotArray[i].startTime / duration * progressWidth )/progressWidth)*100 - 3/progressWidth *100) + '%';
                    dotDiv.appendChild(dotEl);


                    /* dotTip */
                    var dotTip = document.createElement('div');
                    dotTip.setAttribute('class', 'st-video-dottip st-video-dottip-' + i);
                    dotTip.setAttribute('data-index', i);
                    dotTip.innerHTML = dotArray[i].mark;
                    dotTipCon.appendChild(dotTip);

                }
                dotTipWrap.appendChild(dotTipCon);
                progressControl.appendChild(dotTipWrap);

                return dotDiv;
            }

            progressHolder.appendChild(geneditDots());

        },

        // 视屏处理事件
        videoReady: function(player, lists){

            var me = this;
            var playerDom = tools.getVideoDom(player);
            // 设置当前标签
            var setActiveTip = function () {
                var currentTime = player.currentTime();
                var index =  getMarkIndex(lists, currentTime);
                if (index != parseInt(config.defaults.currentIndex)) {
                    tools.removeClass(
                        tools.getElementsByClassName('st-video-dottip',playerDom,'div'),
                        'st-video-dottip-active'
                    );
                    tools.addClass(
                        tools.getElementsByClassName('st-video-dottip-' + index,playerDom,'div'),
                        'st-video-dottip-active'
                    );
                    if (tools.getElementsByClassName('st-video-dottip',playerDom,'div').length > 0) {
                        config.defaults.currentIndex = index;
                    }
                }
            };
            // 获取当前播放所处的标签位
            var getMarkIndex = function (list, time) {
                var k = -1;
                for (var i=0; i<list.length; i++) {
                    if (time >= list[i].startTime) {
                        k = i
                    } else {
                        return k;
                    }
                }
                return k;
            }


            // 网速慢时
            player.on('loadeddata', function(){
                // captureImage();
                if(!me.ifPoint) return false;
                me.videoDot(player,lists);
            });
            // // 网速快时
            // player.on('firstplay', function(){
            //     // captureImage();
            //     if(!me.ifPoint) return false;
            //     me.videoDot(player,lists);
            // });
            // // 鼠标移入视屏
            player.on('useractive', function () {
                if(!me.ifPoint) return false;
                setActiveTip();
            });

            // 时间更改
            // player.on('durationchange', function () {
            //     // console.log('timeupdate',player.currentTime())
            // });
            // 播放时一直触发(待优化)
            player.on('timeupdate', function () {
                // if (config.defaults.currentIndex == -1) {
                //     me.videoDot(player,lists);
                //     setTimeout(function () {
                //         setActiveTip();
                //     }, 0)
                // } else {
                if(!me.ifPoint) return false;
                setActiveTip();
                // }
            });
            // 点击标签事件
            tools.EventUtil.addEvent(
                tools.getElementsByClassName('vjs-progress-control', playerDom,'div'),
                'click',
                function (event) {
                    var target = tools.EventUtil.getTarget(event);
                    if(target.className && target.className.indexOf('st-video-dottip') == -1) return false;
                    var parent = tools.getElementsByClassName('vjs-progress-control', playerDom,'div')[0];
                    while (target.className&&target.className.indexOf('st-video-dottip') == -1) {
                        if (target == parent) {
                            target = null;
                            break;
                        }
                        target = target.parentNode;
                    }
                    if (target) {
                        var index = target.getAttribute('data-index');
                        player.currentTime(lists[index].startTime);
                        setActiveTip()
                        player.play();
                    } else {
                        // console.log(target);
                    }
                }
            );
            // ie下隐藏全屏按钮
            // if (config.defaults.b_version <= 9) {
            //     player.controlBar.fullscreenToggle.hide()
            // }

            // 视频暂停
            // player.on("pause", function(){
            //     setActiveTip();
            // });
        },
        disabledPoint:function () {
            this.ifPoint = false;
        },
        enabledPoint:function () {
            this.ifPoint = true;
        },
        // 初始化
        init: function (player, options) {
            var me = this;
            me.ifPoint = true;//是否打点，默认打点
            var list = [];
            var limitNum = options.limitLabelNum ? options.limitLabelNum : 3;
            if (limitNum <=1 ) {
                limitNum = 1;
            }
            if (options.list && options.list.length) {
                if (options.list.length > limitNum) {
                    list = options.list.slice(0,limitNum)
                } else if (options.list.length > 1) {
                    list = options.list;
                }
            }else{
                return false;
            }
            // if (config.defaults.b_version < 9) {
            //     return false;
            // }

            function compare(property){
                return function(a,b){
                    var value1 = a[property];
                    var value2 = b[property];
                    return value1 - value2;
                }
            }
            list.sort(compare('startTime'));
            // player.ready(function () {
            //     me.videoReady(player, list);
            // })
            me.videoReady(player, list);
            return player;
        }

    };
    module.exports = videoAnchorPoint
})



