//服装 优惠套装
define('MOD_ROOT/suit/suit', function(require, exports, module) {
    var trimPath   = require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var switchable = require('JDF_UI/switchable/1.0.0/switchable');
    var tools      = require('MOD_ROOT/common/tools/tools');
    var G          = require('MOD_ROOT/common/core');
    // http://item.jd.com/1725652834.html
    // http://cd.jd.com/stocks?callback=showStock&type=batchstocks&skuIds=1068421,1309655&area=1_0_0_0

    var SuitPop = {
        sku: G.sku,
        $el: $('.J-suit-content'),
        tabInd: 0,
        setBuyLink: function(wrap) {
            var $obj = this.currTab || $('#stab-suits0'),
                btn = $obj.find('.btn-buy'),
                pCount = $('#buy-num').val() || 1,
                pId = $obj.attr('data-packid'),
                resSku = [G.sku];


            $obj.find('[data-holder="popwin"]').each(function() {
                if ( $(this).is('input') ) {
                    if ( $(this).attr('checked') == 'checked' ) {
                        resSku.push( $(this).attr('skuid') );
                    }
                } else {
                    if ( $(this).attr('skuid') ) {
                        resSku.push( $(this).attr('skuid') );
                    }
                }
            });

            return '//cart.jd.com/gate.action?pcount=' + pCount + '&ptype=3&pid=' + pId + '&sku=' + resSku.join(',');
        },
        calPrice: function() {
            var $obj = this.currTab || $('#stab-suits0'),
                items = $obj.find('.pop-list'),
                resSkus = [G.sku],
                suitsId = $obj.attr('data-packid'),
                desPrice = $obj.find('.infos').eq(0),
                suitsPrice = desPrice.find('.p-price').eq(0).find('strong'),
                orginPrice = desPrice.find('.p-price').eq(1).find('del'),
                discount = desPrice.find('.p-saving').eq(0).find('span');

            $obj.find('[data-holder="popwin"]').each(function() {
                if ( $(this).is('input') ) {
                    if ( $(this).attr('checked') == 'checked' ) {
                        resSkus.push( $(this).attr('skuid') );
                    }
                } else {
                    if ( $(this).attr('skuid') ) {
                        resSkus.push( $(this).attr('skuid') );
                    }
                }
            });
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/suit/suitprice',
                data: {
                    suitId: suitsId,
                    skuIds: resSkus.join(','),
                    origin: 1,
                    webSite: 1,
                    appid: 'item-v3',
                    functionId: "pc_suit_suitprice"
                },
                dataType: 'jsonp',
                success: function(r) {
                    if (!!r&&r.packPromotionPrice) {
                        suitsPrice.html('￥ ' + r.packPromotionPrice.toFixed(2));
                        orginPrice.html('￥ ' + r.packOriginalPrice.toFixed(2));
                        discount.html('￥ ' + r.suitDiscount.toFixed(2));
                    } else {
                        suitsPrice.html('暂无报价');
                        orginPrice.html('暂无报价');
                        discount.html('暂无报价');
                    }
                }
            });
        },
        set: function(cfg, r) {
            this.cfg = cfg;
            var _this = this;
            var targetElement = this.$el;
            // r.packList[0].poolList[0].selectState = 1;
            // r.packList[1].poolList[0].selectState = 1;
            r.master = {
                wid: cfg.skuid,
                imgurl: cfg.src,
                name: cfg.name
            };
            //r.packList[0].poolList[0].selectState = 1;
            pageConfig.product.suits = [cfg.skuid];

            this.$el.attr('loaded', true).html( templateSuitPop.tab.process(r) + templateSuitPop.con.process(r) );

            _this.currTab = $('#stab-suits0');

            tools.priceNum({
                skus: pageConfig.product.suits,
                $el: targetElement,
                callback: function(sku, r) {
                    targetElement.find('input[skuid="'+ sku +'"]').attr('wmeprice', r.p);
                }
            });

            // $('#stabcon_suits .lh').each(function() {
            //     if ( $(this).find('li[data-nocolor="false"]').length < 1 ) {
            //         // sku!==spu 只有一种颜色不进入过渡页
            //         $(this).attr('data-gocart', true);
            //     } else {
            //         $(this).attr('data-gocart', false);
            //     }
            // });

            this.$el.switchable({
                compatible: true,
                event: 'click',
                delay: 0,
                navSelectedClass: 'scurr',
                callback: function (i) {
                    _this.tabInd = i;
                    _this.currTab = $('#stab-suits' + i);
                    setTimeout(function () {
                        _this.clearPopBox();
                    }, 500);
                }
            });

            //_this.setBuyLink(_this.currTab)

            G.setScroll('#stabcon_suits');

            _this.bindEvent(0);

            $('#stabcon_suits .p-scroll').each(function(i) {

                var that = $(this),
                    bigImg = that.prev('.p-img').find('img'),
                    thumbnail = that.find('.p-scroll-wrap a');

                G.thumbnailSwitch(thumbnail, bigImg, '/n2/', 'current' );

                if ( thumbnail.length > 1 ) {
                    thumbnail.click(function() {
                        var popList = $(this).parents('.pop-list').eq(0),
                            currSku = $(this).attr('data-sku'),
                            parentEl = $(this).parents('.stabcon').eq(0);

                        popList.attr('data-sku', currSku);
                        //_this.setBuyLink();
                        _this.calPrice();
                    });
                }
            });
        },
        clearPopBox: function() {
            var _this = this;
            var popBox = $('#pop-box-suit');

            if ( popBox.is(':visible') ) {
                var ind = parseInt(popBox.attr('data-ind'));
                _this.currTab.find('.suit-list-'+ind).find('input:checkbox').attr('checked', false);
                _this.popBox.clear().hide();
            }
        },
        bindEvent: function() {
            var _this = this;
            var popBox = {
                show: function(posIndex, callback) {

                    $('#pop-box-suit').css({
                        // left: posIndex*$('#stabcon_suits .pop-list').outerWidth() - $('#stabcon_suits .suits').scrollLeft() +215,
                        left: posIndex*_this.currTab.find('.pop-list').outerWidth() - _this.currTab.find('.suits').scrollLeft() + 215,
                        visibility: 'visible'
                    });

                    if ( typeof callback == 'function' ) {
                        callback(posIndex);
                    }
                },
                hide: function() {
                    $('#pop-box-suit').css('visibility', 'hidden');
                    return this
                },
                clear: function() {
                    var popBox = $('#pop-box-suit'),
                        contents = $('#p-scroll-suit,#p-size-suit,#p-tips-suit');

                    contents.html('');

                    if ( imgHover ) {
                        imgHover.set();
                    }
                    return this
                }
            };
            var modify = {
                set: function(ind, data) {
                    // console.log(ind);
                    // console.log(data);
                    var selected = _this.currTab.find('.p-selected-suit-'+ind);
                    var currList = _this.currTab.find('.suit-list-'+ind);
                    if ( selected.length < 1 ) {
                        currList.find('.p-scroll').hide().before('<div class="p-selected-suit-'+ind+' p-selected">已选择：'+data.split('|')[1] + '，' + data.split('|')[2] + ' <a data-ind="'+ind+'" class="p-modify" href="#none">修改</a></div>');
                    } else {
                        selected.html('已选择：'+data.split('|')[1] + '，' + data.split('|')[2] + ' <a data-ind="'+ind+'" class="p-modify" href="#none">修改</a>');
                    }

                    $('.p-modify').unbind('click').bind('click', function() {
                        pop( parseInt( this.getAttribute('data-ind')), data );
                    });
                    selected.attr('data-res', data);
                    currList.find('.p-choose span').hide();
                }
            };
            var imgHover = {
                set: function(obj) {
                    var item = obj || $('#stabcon_suits .pop-list');

                    item.find('.p-scroll').each(function() {
                        var that = $(this),
                            bigImg = that.prev('.p-img').find('img'),
                            thumbnail = that.find('.p-scroll-wrap a');

                        G.thumbnailSwitch(thumbnail, bigImg, '/n2/', 'curr' );
                    });
                },
                del: function(obj) {
                    var item = obj || $('#stabcon_suits .pop-list'),
                        selected = null;

                    item.find('.p-scroll').each(function() {
                        $(this).find('.p-scroll-wrap img').unbind('mouseover');
                    });
                }
            };

            var scroll = {
                init: function(isPop) {
                    if ( isPop ) {
                        var wrap = $('#pop-box-suit .p-scroll-wrap'),
                            next = $('#pop-box-suit .p-scroll-next'),
                            prev = $('#pop-box-suit .p-scroll-prev');


                        if ( wrap.find('li').length > 4 ) {
                            wrap.imgScroll({
                                showControl: true,
                                width:30,
                                height:30,
                                visible: 4,
                                step: 4,
                                prev: prev,
                                next: next
                            });
                        }
                    } else {
                        G.setScroll('#stabcon_suit');
                    }
                },
                get: function(ind) {
                    $('#p-scroll-suit').append( _this.currTab.find('.suit-list-'+ind).find('.p-scroll').clone().show() );
                    this.set(ind);
                },
                set: function(ind) {
                    this.init( true );
                }
            };
            var sizeList = {
                sClick: function(ind, size) {
                    var popWin = $('#pop-box-suit'),
                        sizeList = $('#p-size-suit a'),
                        currList = _this.currTab.find('.suit-list-'+ind),
                        that = this;

                    sizeList.click(function() {
                        var sizeThis = $(this),
                            resSku = $(this).attr('data-resku'),
                            resColor = $('#p-scroll-suit .p-scroll-wrap .curr').attr('title'),
                            resSize = $(this).attr('title'),
                            wmaprice = $(this).attr('wmaprice'),
                            wmeprice = $(this).attr('wmeprice');

                        sizeList.removeClass('selected');
                        $(this).addClass('selected');

                        // G.getPriceNum(pageConfig.suitsSkus, readCookie('ipLoc-djd'), popWin, null, function(sku, price) {
                        //     popWin.find('#p-size-suit a[data-resku="'+ sku +'"]').attr('wmeprice', price.p);
                        //     popWin.find('#p-size-suit a[data-resku="'+ sku +'"]').attr('wmaprice', price.m);

                        // });

                        that.clearTips('#p-noselected');
                        currList.attr('data-res', [resSku, resColor, resSize, sizeThis.attr('wmaprice'), sizeThis.attr('wmeprice')].join('|'));

                    });

                    if ( size ) {
                        $('#p-size-suit a').each(function() {

                            if ( $(this).attr('title') == size ) {
                                $(this).trigger('click');
                            }
                        });
                    }
                },
                noSize: function(ind, data) {
                    // 只有颜色没有尺寸
                    $('#p-size-suit').addClass('nosizes').html('');
                    _this.currTab.find('.suit-list-'+ind).attr('data-res', [
                            data.Subcodesku[0].skuId,
                            $('#pop-box-suit .curr').attr('title'), '无尺码',
                            data.Subcodesku[0].WMeprice,
                            data.Subcodesku[0].WMaprice
                        ].join('|')
                    );

                },
                get: function(sku, name, ind, size, fn) {

                    var that = this;
                    var sizeArrStr = $('#pop-box-suit').find('.p-scroll-wrap a.curr .p-size-pack').attr('data-size');
                    var sizeObj = sizeArrStr ? JSON.parse(sizeArrStr) : null;

                    var data = {
                        Subcodesku: sizeObj
                    };
                    // console.log(sku + '-----ind-----' + name + '-----ind-----' + ind);
                    // console.log(data.Subcodesku[0]);

                    if ( data.Subcodesku.length < 1 ) {
                        that.setTips('<p id="p-nostock-suit">该商品已下架或无货</p>');
                        $('#p-size-suit').html('');
                    } else if ( data.Subcodesku.length == 1 && (!!data.Subcodesku[0].size == false || data.Subcodesku[0].size=='无') ) {
                        // console.log('nosize');
                        that.noSize(ind, data);
                        that.clearTips('#p-nostock-suit');
                    } else {
                        $('#p-size-suit').removeClass('nosizes');
                        that.set(data, sku, ind, size);
                        that.clearTips('#p-nostock-suit');
                    }

                    if ( typeof fn == 'function' ) {
                        fn(data);
                    }

                },
                set: function(data, sku, ind, size) {
                    pageConfig.suitsSkus = [];
                    var resLink = '{for list in Subcodesku}<a data-push="${pageConfig.suitsSkus.push(list.skuId)}" href="#none" data-resku="${list.skuId}" wmaprice="" wmeprice="" title="${list.size}">${list.size}</a>{/for}';

                    $('#p-size-suit').html(resLink.process(data));
                    this.sClick(ind, size);
                },
                setTips: function(text) {

                    if ( $('#p-tips-suit').html() == '' ) {
                        $('#p-tips-suit').html(text);
                    }
                },
                clearTips: function(selector) {
                    $('#pop-box-suit').find(selector).remove();
                }
            };
            var btnEvent = function(ind) {
                var ind = ind;

                $('#p-selected-ok-suit').unbind('click').bind('click', function() {
                    var wrap = $('#pop-box-suit'),
                        nosize = $('#pop-box-suit .curr').attr('data-nosize') === "true",
                        selectedColor = wrap.find('#p-scroll-suit .curr').attr('title');

                    if ( $('#pop-box-suit .curr').length < 1 ) {
                        sizeList.setTips('<p id="p-noselected">请选择颜色</p>');
                    } else if ( $('#p-size-suit .selected').length < 1 && !$('#p-size-suit').hasClass('nosizes') ) {
                        sizeList.setTips('<p id="p-noselected">请选择尺码</p>');
                    } else {

                        modify.set(ind, _this.currTab.find('.suit-list-'+ind).attr('data-res'));
                        popBox.hide().clear();

                        // 改成真实sku对应的价格图片
                        var currItem = _this.currTab.find('.suit-list-'+ind),
                            resData = currItem.attr('data-res').split('|'),
                            currSku = resData[0],
                            wmaprice = resData[3],
                            wmeprice = resData[4],
                            currPrice = currItem.find('.p-price img'),
                            currPriceSrc = currPrice.attr('src'),

                            holder = _this.currTab.find('[data-state="1"]'),
                            hasChecked;

                        // currPrice.attr('src', currPriceSrc.replace(/\d{10}/, currSku));

                        // 更新真实市场价及京东价 用于计算结果
                        currItem.attr('data-sku', currSku).find('[data-holder="popwin"]').attr({
                            skuid: currSku,
                            wmaprice: wmaprice,
                            wmeprice: wmeprice,
                            checked: true
                        });

                        // G.getPriceNum(currSku, readCookie('ipLoc-djd'), currItem, null, function(p, r) {
                        // });
                        //currItem.attr('data-sku', currSku)

                        // 计算出最终真实价格
                        // G.calculatePrice(_this.currTab.find('.suit-list-'+ind).find('input:checkbox')[0],'#tab-hot');

                        // 只有选择了必选商品时才计算价格，否则可能显示“暂无报价”
                        if ( holder.find('[data-holder="popwin"]').length == holder.find('[skuid]').length ) {
                            _this.calPrice();
                        }

                        //_this.setBuyLink();

                        // 调试用
                        if ( /isdebug/.test(location.href) ) {
                            _this.currTab.find('.infos .p-name strong').html('DEBUG<br>' + currItem.attr('data-res'));
                        }
                    }
                });

                $('#p-selected-cancel-suit').click(function() {
                    popBox.hide().clear();
                    if ( _this.currTab.find('.p-selected-suit-'+ind).length < 1 ) {
                        _this.currTab.find('.suit-list-'+ind).find('input:checkbox').attr('checked', false);
                    }
                });
            };

            function setClick(ind, selectedColor) {

                var thumbnailLink = $('#p-scroll-suit .p-scroll-wrap a'),
                    bigImg = _this.currTab.find('.suit-list-'+ind).find('.p-img img'),
                    sku = _this.currTab.find('.suit-list-'+ind).attr('data-sku');

                thumbnailLink.unbind('click').bind('click', function() {
                    var that = $(this),
                        currIMG = $(this).find('img').attr('src'),
                        selectedColor = $(this).attr('title'),
                        nosize = $(this).attr('data-nosize') === 'true';

                    // 添加高亮显示当前状态
                    thumbnailLink.removeClass('curr');
                    $(this).addClass('curr');

                    sizeList.get(sku, selectedColor, ind, null);

                    // hover 时修改大图
                    bigImg.attr( 'src', currIMG.replace('\/n5\/', '\/n2\/') );

                    // 清空已选择数据
                    if ( $('#p-size-suit').hasClass('nosize') && _this.currTab.find('.suit-list-'+ind).attr('data-res') ) {
                        _this.currTab.find('.suit-list-'+ind).removeAttr('data-res');
                    }
                });


                if ( selectedColor ) {
                    thumbnailLink.each(function() {
                        if ( $(this).attr('title') == selectedColor ) {
                            $(this).trigger('click');
                        }
                    });
                }
            }
            function pop(ind, cacheData) {
                var ind = ind;
                if ( cacheData ) {
                    var res = cacheData.split('|'),
                        cacheColor = res[1],
                        cacheSize = res[2];
                }
                if ( $('#pop-box-suit').length > 0 ) {
                    $('#pop-box-suit').attr('data-ind', ind);
                }

                popBox.clear().show(ind, function() {
                    var currItem = _this.currTab.find('.suit-list-'+ind),
                        currSku = currItem.attr('data-sku'),
                        selectedColor = currItem.find('a.curr').attr('title');

                    btnEvent(ind);
                    imgHover.del();
                    scroll.get(ind);
                    setClick(ind, cacheColor||null);

                    sizeList.get( currSku, cacheColor||selectedColor, ind, cacheSize );
                });
            }

            _this.popBox = popBox;
            _this.$el.find('[data-holder="popwin"]').unbind('click').bind('click', function() {
                var that = $(this),
                    noColor = that.attr('data-nocolor'),
                    noSize = that.attr('data-nosize'),
                    item = that.parents('.pop-list').eq(0),
                    ind = that.attr('data-ind');

                if ( $('#pop-box-suit').css('visibility') == 'visible' ) {
                    var index = parseInt($('#pop-box-suit').attr('data-ind'));
                    _this.currTab.find('.suit-list-'+index).find('input:checkbox').attr('checked', false);
                    popBox.clear().hide();
                }

                if ( noColor == 'true' && noSize == 'true' ) {
                    // 推荐了一些没有颜色、尺码的
                    // G.calculatePrice(that[0], '#tab-hot');
                    _this.calPrice();
                } else {
                    if ( that.attr('checked') == 'checked' || that.attr('data-checked') == 'true' ) {
                        pop(ind);
                    } else {
                        if ( _this.currTab.find('.p-selected-suit-'+ind) ) {
                            _this.currTab.find('.p-selected-suit-'+ind).remove();
                            _this.currTab.find('.suit-list-'+ind).find('.p-scroll').show();
                            imgHover.set(_this.currTab.find('.suit-list-'+ind));
                        }
                        // G.calculatePrice(that[0], '#tab-hot');
                    }
                }
            });

            $('#stabcon_suits .suits').scroll(function() {
                _this.clearPopBox();
            });

            this.$el.delegate('.btn-buy', 'click', function() {
                var ind = $(this).attr('data-ind');
                var reqField = _this.currTab.find('span[data-holder="popwin"]');
                var reqFieldNext = reqField.not('[skuid]').eq(0);
                var ind = reqFieldNext.attr('data-ind');
                var disabled;
                var flag = G.wideVersion?3:2;

                if ( $('#pop-box-suit').css('visibility') == 'visible' ) {
                    return false;
                }

                if ( _this.currTab.find('.choose[data-state="1"]').length < 1 && _this.currTab.find('.suits input:checked').length < 1 ) {
                    alert('至少选择2个商品才能享受套装优惠，请重新选择后购买');
                    return false;
                }

                if ( reqField.length === reqField.filter('[skuid]').length ) {
                    disabled = false;
                } else {
                    disabled = true;
                }

                if ( disabled ) {
                    _this.currTab.find('.suits').scrollLeft((Number(ind)+1-flag)*_this.currTab.find('.pop-list').outerWidth());
                    setTimeout(function() {
                        reqFieldNext.trigger('click');
                    }, 500);
                    return false;
                }
                // alert(_this.setBuyLink());
                location.href = _this.setBuyLink();

            });
        }
    };

    var templateSuitPop = {
        tab: '<div class="tab-cat stab">'
        +'    <ul>'
        +'        {for item in packList}'
        +'        <li data-suits="${item.packId}" class="ui-switchable-item fl{if parseInt(item_index)==0} scurr{/if}" data-ind="${item_index}">优惠套装${parseInt(item_index)+1}</li>'
        +'        {/for}'
        +'    </ul>'
        +'</div>',
        con: '<div id="stabcon_suits">'
        +'    <div id="pop-box-suit" class="">'
        +'        <div id="p-scroll-suit"></div>'
        +'        <div id="p-size-suit"></div>'
        +'        <div id="p-tips-suit"></div>'
        +'        <div id="p-size-btn-suit">'
        +'            <a href="#none" id="p-selected-ok-suit">确定</a><a id="p-selected-cancel-suit" href="#none">取消</a>'
        +'        </div>'
        +'    </div>'
        +'    {for tab in packList}'
        +'    <div id="stab-suits${tab_index}" data-ptype="${tab.packType}" data-packid="${tab.packId}" class="ui-switchable-panel stabcon{if parseInt(tab_index)!=0} none{/if}">'
        +'        <div class="master">'
        +'            <s class="hide"></s>'
        +'            <div class="p-img">'
        +'                <a href="//item.jd.com/${master.wid}.html" target="_blank"><img src="${pageConfig.FN_GetImageDomain(master.wid)}n2/${master.imgurl}" data-img="1" height="160" width="160"></a>'
        +'            </div>'
        +'            <div class="p-name">'
        +'                <a href="//item.jd.com/${master.wid}.html" target="_blank" title="${master.name}">${master.name}</a>'
        +'            </div>'
        +'        </div>'
        +'        <div class="pop-wrap">'
        +'            <div class="suits" style="position:relative;overflow-x:auto;overflow-y:hidden;*height:300px;">'
        +'                <ul class="lh" style="width:${tab.poolList.length*202}px">'
        +'                    {for item in tab.poolList}'
        +'                    <li class="suit-list-${item_index} pop-list {if parseInt(item_index)+1==parseInt(tab.poolList.length)} last-item{/if}" data-sku="${item.colorList[0].skuList[0].skuId}" data-ind="${item_index}" data-num="${item.colorList[0].skuList[0].number!=1}" data-push="${pageConfig.product.suits.push(item.colorList[0].skuList[0].skuId)}">'
        +'                        <s class="hide"></s>'
        +'                        <div class="p-img">'
        +'                            <a href="//item.jd.com/${item.colorList[0].skuList[0].skuId}.html" target="_blank"><img id="${item.colorList[0].skuList[0].skuId}" src="${pageConfig.FN_GetImageDomain(item.colorList[0].skuList[0].skuId)}n2/${item.colorList[0].skuList[0].image}" data-img="1" alt="" height="160" width="160"></a>'
        +'                        </div>'
        +'                        <div class="p-scroll">'
        +'                            <a href="javascript:;" class="p-scroll-btn p-scroll-prev">&lt;</a>'
        +'                            <div class="p-scroll-wrap">'
        +'                                <ul>'
        +'                                {for img in item.colorList}'
        +'                                    <li>'
        +'                                        <a href="javascript:;" class="{if parseInt(img_index)==0}curr{/if}" data-nosize="${img.skuList[0].size==""}" data-sku="${img.skuList[0].skuId}" title="${img.skuList[0].color}"><img data-img="1" width="25" height="25" alt="${img.skuList[0].name}" src="${pageConfig.FN_GetImageDomain(img.skuList[0].skuId)}n5/${img.skuList[0].image}" data-img="1">'
        +'                                            <div class="p-size-pack hide" data-size=\'${JSON.stringify(img.skuList).replace(/\'/g,"`")}\'></div>'
        +'                                        </a>'
        +'                                    </li>'
        +'                                {/for}'
        +'                                </ul>'
        +'                            </div>'
        +'                            <a href="javascript:;" class="p-scroll-btn p-scroll-next">&gt;</a>'
        +'                        </div>'
        +'                        <div class="p-name">'
        +'                            <a href="//item.jd.com/${item.colorList[0].skuList[0].skuId}.html" target="_blank" title="${item.colorList[0].skuList[0].name}">${item.colorList[0].skuList[0].name}</a>'
        +'                        </div>'
        +'                        <div class="choose p-price" data-state="${item.selectState}">'
        +'                            {if item.selectState!=1}<input data-ind="${item_index}" data-nosize="${item.colorList[0].skuList[0].size==""}" {if item.colorList[0].skuList[0].color==""}data-nocolor="true" {/if} data-holder="popwin" skuid="${item.colorList[0].skuList[0].skuId}" type="checkbox" wmeprice="" >{/if}'
        +'                            <label><strong class="J-p-${item.colorList[0].skuList[0].skuId}"></strong></label>'
        +'                        </div>'
        +'                        {if item.selectState==1}'
        +'                        <div class="p-choose" data-state="${item.selectState}" style="clear:both">'
        +'                            <span class="hl_blue" data-ind="${item_index}" {if item.colorList[0].skuList[0].size==""&&item.colorList[0].skuList[0].color==""} style="display:none;" skuid="${item.colorList[0].skuList[0].skuId}"{/if}  data-nosize="${item.colorList[0].skuList[0].size==""}" {if item.colorList[0].skuList[0].color==""}data-nocolor="true" {/if} data-checked="true" data-holder="popwin">选择商品规格</span>'
        +'                        </div>'
        +'                        {/if}'
        +'                    </li>'
        +'                    {/for}'
        +'                </ul>'
        +'            </div>'
        +'        </div>'
        +'        <div class="infos">'
        +'            <s></s>'
        +'            <div class="p-name hl_blue"><strong><span>${tab.packName}</span></strong></div>'
        +'            <div class="p-price"><em class="p-simsun">套&nbsp;装&nbsp;价：</em>'
        +'                <strong>{if !!tab.packPrice}￥ ${tab.packPrice.packPromotionPrice.toFixed(2)}{else}暂无报价{/if}</strong>'
        +'            </div>'
        +'            <div class="p-price hide">原\u3000\u3000价：'
        +'                <del>{if !!tab.packPrice}￥ ${tab.packPrice.packOriginalPrice.toFixed(2)}{else}暂无报价{/if}</del>'
        +'            </div>'
        +'            <div class="p-saving hide">立即节省：'
        +'                <span class="hl_green">{if !!tab.packPrice}￥ ${tab.packPrice.suitDiscount.toFixed(2)}{else}暂无报价{/if}</span>'
        +'            </div>'
        +'            <div class="btns">'
        +'                <a class="btn-buy btn-primary" data-ind="${tab_index}" clstag="shangpin|keycount|product|popbuysuit2" href="javascript:;">购买套装</a>'
        +'            </div>'
        +'        </div><div class="clb"></div>'
        +'    </div>'
        +'    {/for}'
        +'</div>'
    };

    function init(cfg, data) {
        // POP套装
        var hasData = !!(data && data.packList && data.packList.length);
        if ( hasData ) {

            $("#shopRecSuit").removeClass("hide");
            $(".J-suit-trigger").removeClass("hide");

            SuitPop.set(cfg, data);
            var $shopReco = $('#shop-reco');//店长推荐
            if(!$shopReco.length) {
                var $shopRecSuit = $("#shopRecSuit");
                if($shopRecSuit.length) {
                    $shopRecSuit.data("ETab").go(1);
                }
            }
        }
    }

    module.exports.__id = 'suit';
    module.exports = init;
});
