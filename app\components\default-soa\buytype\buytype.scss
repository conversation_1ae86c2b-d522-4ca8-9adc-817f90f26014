@import '../common/lib';
@import './__sprite';

.buytype { text-align: left; }
#choose-type-hy {
    .icon {
        @include icons(16px, 16px);
        margin-top: 8px;
        margin-right: 5px;
    }
    .question {
        background-image: url(i/question.png);
    }
}

// 加价购弹层
#shf-feetype {
    padding: 0 30px;
    dt, .dl-1 dd {
        font-size: 14px;
    }
    dl {
        margin-bottom: 10px;
    }
    dd {
        width: 410px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    ul {
        margin-bottom: 5px;
    }
    .lh li {
        float: none;
        width: 400px;
        background: #f3f3f3;
        margin-bottom: 1px;
        padding: 0 5px;
        height: 2.5em;
        line-height: 2.5em;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        em {
            color: #999;
        }
    }
    table {
        background: #f3f3f3;
        border-collapse: collapse;
        margin-top: 5px;
        td {
            width: 193px;
            height: 2em;
            border: 1px solid #ddd;
            padding: 5px;
        }
    }
}
.shf-buy-now {
    padding-top: 10px;
    a {
        display: inline-block;
        *zoom: 1;
        height: 30px;
        padding: 0 20px;
        font: bold 14px/30px simsum;
        &:hover {
            text-decoration: none;
        }
    }
}

#J_ContractPhoneQrcodeLink {
    position: relative;
    display: block;
    width: 369px;
    height: 120px;
    background: url(//img10.360buyimg.com/devfe/jfs/t1/51414/30/11979/41836/5d8cc5e6E79d5ec4c/187ae9e7b40a3d35.png) no-repeat;
    img {
        position: absolute;
        top: 15px;
        left: 15px;
        width: 90px;
        height: 90px;
    }
}

// <div class="cp-confirm-dialog">
//     <div class="cp-confirm-dialog__head">当前已选商品继续添加，将会替换购物车中已有合约机商品。</div>
//     <div class="cp-confirm-dialog__body">
//         <div class="cp-confirm-dialog__body__left">
//             <div class="cp-confirm-dialog__title"><span class="icon-cart">已选商品</span></div>
//             <p>白色</p>
//             <p>8GB+12GB</p>
//         </div>
//         <div class="cp-confirm-dialog__body__right">
//             <div class="cp-confirm-dialog__title"><span class="icon-flush"></span>替换商品</div>
//             <p>白色</p>
//             <p>8GB+12GB</p>
//             <p>电信办卡两年优惠</p>
//             <p>13811812345</p>
//         </div>
//         <span class="icon-replace"></span>
//     </div>
//     <div class="cp-confirm-dialog__foot">
//         <span class="cp-confirm-dialog__button cp-confirm-dialog__button--right cp-confirm-dialog__button--gray">我再想想</span>
//         <span class="cp-confirm-dialog__button cp-confirm-dialog__button--left cp-confirm-dialog__button--red">继续添加</span>
//     </div>
// </div>
// 构建弹框UI
.ui-dialog.g5-contract-phone {
    border: none;
    border-radius: 0;
    .ui-dialog-title,
    .ui-dialog-close {
        display: none;
    }
    .ui-dialog-content {
        padding: 0;
    }
}
.cp-confirm-dialog {
    padding: 51px 40px 36px 40px;
    background-color: #fff;
    &__head {
        line-height: 28px;
        text-align: center;
        color: #333;
        font-size: 20px;
    }
    &__body {
        position: relative;
        overflow: hidden;
        margin-top: 30px;
        margin-bottom: 33px;
        text-align: center;
    }

    &__body .icon-replace {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
    }

    &__body p {
        width: 200px;
        line-height: 22px;
        margin: 10px auto;
        font-size: 16px;
        overflow: hidden;
        white-space: nowrap;
        word-break: keep-all;
        text-overflow: ellipsis;
    }

    &__body__left {
        float: left;
        width: 284px;
        height: 238px;
        background: url(./i/left_bg.png) no-repeat center;
        margin-right: -31px;
    }

    &__body__left p {
        color: #9B9B9B;
    }

    &__body__right {
        float: left;
        width: 286px;
        height: 238px;
        background: url(./i/right_bg.png) no-repeat center;
    }
   
    &__body__right p {
        color: #333;
    }
    
    &__title {
        line-height: 26px;
        color: #333;
        font-size: 20px;
        font-weight: bold;
        margin-top: 30px;
        span {
            margin-right: 9px;
            vertical-align: bottom;
        }
    }
    &__foot {
        text-align: center;
    }
    &__button {
        @include inline-block;
        line-height: 18px;
        padding: 11px 86px 10px 86px;
        background-color: #fff;
        border: 1px #999 solid;
        border-radius: 4px;
        color: #333;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        white-space: nowrap;
        word-break: keep-all;
    }

    &__button--gray {
        border: 1px solid #E3E3E3;
        color: #4E4E4E;
    }

    &__button--red {
        border: 1px solid #df3033;
        color: #fff;
        background-color: #df3033;
    }

    &__button--left {
        margin-left: 14px;
    }

    &__button--right {
        margin-right: 14px;
    }
    
    .icon-cart {
        @include inline-block;
        @include sprite-cart;
    }
    .icon-flush {
        @include inline-block;
        @include sprite-flush;
    }
    .icon-replace {
        @include inline-block;
        @include sprite-replace;
    }
}
