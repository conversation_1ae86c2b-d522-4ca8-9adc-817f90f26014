define('MOD_ROOT/buildingInfo/buildingInfo', function(require, exports, module) {
    var login = require('JDF_UNIT/login/1.0.0/login');
    var Event = require('MOD_ROOT/common/tools/event').Event;

    function addEvents(cfg) {
        var $buyUrl = $('#CartUrl,#CartUrl-mini');

        Event.addListener('onStockReady', onStockReady);

        function onStockReady() {
            if(cfg.havestock) {
                $buyUrl.click(handleBuyClick);
            } else {
                $buyUrl.addClass('btn-disable');
            }
        }
    }

    function popLogin(callback) {
        login({
            modal: true,
            complete: function() {
                callback();
            }
        });
    }
    
    function popPresale(href) {
        return function () {
            $('body').dialog({
                width: 400,
                height: 250,
                title: '免费预约咨询',
                type: 'iframe',
                autoIframe: false,
                source: href
            });
        }
    }
    
    function handleBuyClick(e) {
        var $this = $(e.target);
        var href = $this.data('href');
        popLogin(popPresale(href));
    }

    function getPrice() {
        var oversea = pageConfig.product.oversea;
        if(!oversea.housePriceType) {//如果是空的 那就有错
            $(".J-building-price .p-price").html("￥暂无报价");
            return;
        }
        $.ajax({
            url: "//cd.jd.com/oversea/rate",
            data: {
                priceType: oversea.housePriceType,
                money: oversea.housePrice
            },
            dataType: "jsonp",
            success: function(r) {
                var totalStr = "￥暂无报价";
                if(r.cny_money != undefined && r.source_money != undefined) {
                    if(r.cny_money != 'No') {
                        totalStr = "约￥" + r.cny_money + "（" + oversea.housePriceType + " " + r.source_money + "）";
                    } else {
                        totalStr = oversea.housePriceType + " " + r.source_money;
                    }
                }
                $(".J-building-price .p-price").html(totalStr);
            }
        });
    }

    function init(cfg) {
        addEvents(cfg);

        getPrice();
    }
    module.exports.__id = 'buildingInfo';
    module.exports.init = init;
});
