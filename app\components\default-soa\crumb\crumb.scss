@import '../common/lib';
#shortcut-2024 {
    margin-bottom: 24px; // 兼容无面包屑的情况
}
.layout-area{
    z-index:9 !important;
    position:relative;
    //overflow: hidden;
}
#shop-head{
    contain: content;
    display: none;
}
#shop-head:hover{
    contain: none;
}
.crumb-wrap {
    background: #f6f7fb;
    position: relative;
    *z-index: 2;
    z-index: 5;
    clear: both;
    margin-top: -24px;
    .fl {
        float: none;
    }
}

.crumb {
    position: relative;
    z-index: 5;
    height: 14px;
    line-height: 14px;
    padding: 16px 0 20px;
    a {
        color: rgba(80, 82, 89, 1);
        &:hover {
            color: rgba(255, 15, 35, 1);
        }
    }
    .item {
        color: rgba(80, 82, 89, 1);
        float:left;
    }
    .sep {
        font-family: $font-st;
        padding: 0 10px;
    }
}

.hover div.border .head {
    border-bottom: none;
    padding-bottom: 1px;
}
.crumb-br {
    top: -2px;
    .inner {
        _float: left;
        _position: relative;
    }
    .arrow {
        margin-left: 0;
    }
    .border .head {
        border-bottom: 1px solid #ccc;
        height: 20px;
        line-height: 20px;
        background-color: #fff;
    }
    .hover .head {
        border-bottom: none;
    }
    .head {
        a {
            padding: 0 5px;
        }
    }
    .plist-1 {
        padding-bottom: 10px;
        margin-top: 10px;
        margin-bottom: 10px;
        border-bottom: 1px dotted #dfdfdf;
        display: none;
    }
    .br-reco {
        li {
            width: 206px;
            height: 65px;
            margin-left: 5px;
            clear: none;
        }
    }
    .content {
        top: 20px;
        width: 660px;
        padding: 10px 0;
    }
    .br-list {
        .more {

        }
        li {
            @include inline-block;
            width: 110px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding-left: 15px;
            margin-top: 5px;
        }
    }
}
.ellipsis{
    width:120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

