define('MOD_ROOT/jdserviceF/jdserviceF', function(require, exports, module) {
    require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var G = require('MOD_ROOT/common/core');
    var tools = require('MOD_ROOT/common/tools/tools');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var buybtn = require('MOD_ROOT/buybtn/buybtn').addToCartBtn;
    var globalConditionTrigger = tools.ConditionTrigger.globalConditionTrigger;
    var PROTOCOL_REGEXP = new RegExp('^' + location.protocol);
    
    var template ='\
        <div class="dt">增值服务</div>\
        <div class="dd">\
            <div class="service-type-yb-ff clearfix">\
                {for item in data}\
                    <div class="yb-item-cat">\
                        <div class="yb-item">\
                            <span class="name">${item.name}</span>\
                            <span class="name-tip"></span>\
                            <div class="after"></div>\
                        </div>\
                        <div class="more-jd-item">\
                        {for list in item.products}\
                        <div data-sku="${list.skuId}" class="more-item">\
                            <ul>\
                                <li data-sku="${list.skuId}">\
                                    <div class="title" clstag="shangpin|keycount|product|jingdongfuwu_${list.skuId}" >\
                                        <span class="choose-btn" clstag="shangpin|keycount|product|jingdongfuwu_${list.skuId}">\
                                        <span class="name">${list.serviceChildName}</span><span  class="name-tip" style="display: none">${list.tip}</span>{if (pageConfig.product.venderId == 1000000127 && list.sortName == "换修无忧")}<span class="price">￥${list.price}/月</span>{else}<span class="price">￥${list.price}</span>{/if}\
                                        </span>\
                                    </div>\
                                </li>\
                            </ul>\
                        </div>\
                        {/for}\
                        </div>\
                    </div>\
                {/for}\
            </div>\
            <div id="btn-open-div" class="btn-open-div" >\
                        <span id="btn-open" class="btn-open">\
                        展开全部\
                        </span>\
                        <img class="open-opens" style="margin-left: 8px" width="10" height="10" src="//img11.360buyimg.com/imagetools/jfs/t1/264116/11/14470/583/6790c6b0F446ef23c/4ae3a1af1d4ac2d4.png" />\
                        </div>\
                <div class="service-tips hide">\
        </div>';

    //////////  判断购买按钮的链接是否带有请求参数 //////////////
    function hasQueryParams(url, G) {  // 非通用函数
        // 如果url缺少协议部分填充协议，因为`serializeURL`方法序列化的是一个严格的url。
        if (!PROTOCOL_REGEXP.test(url)) {
            url = location.protocol + url
        }
        try {
            var urlDict = G.serializeURL(url);
            var query = urlDict.query;
            for (var k in query) {
                if (query.hasOwnProperty(k)) {
                    return true;
                }
            }
            return false;
        } catch (error) {
            console && console.log(error);
            return false;
        }
    }
    
    var isBuyUrlHasQueryParams = hasQueryParams(G.originBuyUrl, G);
    ////////////////////////////////////////////////////////////

    function modifyBuyUrlParam(key, value, G) {
        var href = buybtn.$el.attr('href');
        var hasProtocol = PROTOCOL_REGEXP.test(href);
        var dict = {
            query: {}
        };

        if (!hasProtocol) {
            href = location.protocol + href;
        }

        if (G.is('', value)) {
            dict.query[key] = value;
        } else if (G.is([], value)) {
            dict.query[key] = value.join(',');
        }

        try {
            href = G.modifyURL(href, dict, true);
        } catch (error) {
            console && console.log(error);
        }
        
        if (!hasProtocol) {
            href = href.replace(PROTOCOL_REGEXP, '');
        }

        if (isBuyUrlHasQueryParams) {
            buybtn.enabled(href);
        }
        pageConfig.product.cartBuyUrlParam = href // 全局购物车链接参数
    }

    var JdService = {
        init: function(opts, cfg) {
            this.sku = opts.sku;
            this.venderId = opts.venderId;
            this.appreTemplateId = opts.appreTemplateId;
            this.$el   = opts.$el || $('#choose-serviceF');
            // this.callback = opts.callback || function(){};
            this.onSelected = opts.onSelected || function(){};  // 每次点击后的回调函数，返回(已选中的sku数组, 当前选中的sku, 当前点击元素(jQuery)对象)
            this.cfg = cfg;
            this.currSku = null;
            this.bindEvent();
            this.get();
        },
        bindEvent: function() {
            var _this = this;

            // this.$el
            //     .undelegate('click')
            //     .delegate('.service__head', 'click', function() {
            //         var $this = $(this),
            //             $ybItemCat = $this.parents('.service')
            //         if ($ybItemCat.hasClass('selected')) {
            //             //已选中
            //             $ybItemCat
            //                 .find('li.selected .choose-btn')
            //                 .trigger('click');
            //         } else {
            //             //没选中
            //             $ybItemCat
            //                 .find('.service__body li:eq(0) .choose-btn')
            //                 .trigger('click');
            //         }
            //     });

            this.$el.delegate('.more-item', 'click', function() {
                // var $this = $(this);
                // var $thisLi = $this.parents('li');
                // var sku = $thisLi.attr('data-sku');
                // var $catEl = $this.parents('.service');
                // var $serviceHead = $catEl.find('.service__head');
                // var data   = _this.$el.data('data')[$catEl.index()];
                //
                // if ($thisLi.hasClass('selected')) {
                //     $thisLi.removeClass('selected');
                //     $catEl.removeClass('selected hover');
                //     $serviceHead.removeAttr('data-sku');
                //     $serviceHead.find('.name').html(data.name).removeAttr('title');
                //     $serviceHead.find('.price').html('￥' + data.charge);
                // } else {
                //     $catEl.find('li').removeClass('selected');
                //     $thisLi.addClass('selected');
                //     $catEl.addClass('selected').removeClass('hover');
                //     _this.updateFrist($thisLi, $catEl.find('.service__head'));
                // }
                //
                // _this.currSku = sku;
                // _this.currEl = $this;
                // _this.calResult();
                // return false;
                tools.ClickMoreItem($(this),_this)
            });

            // this.$el.delegate('.more-item','mouseover', function() {
            //     // tooltip.style.visibility = 'visible';
            //     tools.mouseTip($(this),'mouseover',$(this).find('.name-tip').attr('tip-name'))
            //     // $(this).find('.title').attr('title','')
            // });
            //
            // this.$el.delegate('.more-item','mouseout', function() {
            //     // tooltip.style.visibility = 'visible';
            //     // console.log('到我这了777')
            //     tools.mouseTip($(this),'mouseout',$(this).find('.name-tip').attr('tip-name'))
            //     // $(this).find('.title').attr('title','')
            // });

            // this.$el.delegate('.service', {
            //     mouseenter: function() {$(this).addClass('hover');},
            //     mouseleave: function() {$(this).removeClass('hover');}
            // });
            //
            // var timeoutId = 0;
            // this.$el.delegate('.service-tips', 'mouseenter', function() {
            //     clearTimeout(timeoutId);
            //     $(this).addClass('hover');
            // });

            this.$el.delegate('.service-tips', 'mouseleave', function() {
                var $this = $(this);
                timeoutId = setTimeout(function() {
                    $this.removeClass('hover');
                }, 300);
            });

            // 京东服务+选择性屏蔽“一键购”和“白条分期”
            globalConditionTrigger
            .addReceiver('JDServiceF', function (data) {
                var TIPS_TEXT = '京东服务不支持一键打白条';
                var __html= '<span id="J_JDSFTips" style="color:#999; white-space: nowrap;">{0}</span>';
                setTimeout(function(){
                    if (_this.cfg.baiTiaoFenQi) {  // 选中京东服务时的文本提示
                        var $baitiaoText = $('.J-baitiao-text');
                        var $JDSTips = $baitiaoText.next('#J_JDSFTips');
                        if ($JDSTips.length > 0) {
                            if ($baitiaoText.find('em').length) {
                                $JDSTips.html('，' + TIPS_TEXT);
                            } else {
                                $JDSTips.html(TIPS_TEXT);
                            }
                        } else {
                            if ($baitiaoText.find('em').length) {
                                $baitiaoText.after(__html.format('，' + TIPS_TEXT));
                            } else {
                                $baitiaoText.after(__html.format(TIPS_TEXT));
                            }
                        }
                    }
                }, 0);
                
            }, function (data) {
                if (_this.cfg.baiTiaoFenQi) {
                    var $baitiaoText = $('.J-baitiao-text');
                    var $JDSTips = $baitiaoText.next('#J_JDSFTips');
                    if ($JDSTips.length > 0) {  // 移除文本提示
                        $JDSTips.remove();
                    }
                }
            }).addAction({
                success: function () {
                    if (_this.cfg.baiTiaoFenQi) {  // 屏蔽“白条分期”
                        _this.cfg.baiTiaoFenQi.disabled();
                        tools.showTradeUrl(_this) // 隐藏白条按钮需要看看立即展示逻辑
                    }
                },
                failure: function () {
                    if (_this.cfg.baiTiaoFenQi) {  // 启用“白条分期”
                        _this.cfg.baiTiaoFenQi.enabled();
                    }
                }
            });

             // 加载“京东服务”模块
             function loadJdService() {
                // 重置条件集状态
                globalConditionTrigger.emit('JDServiceF', false);
                // 重置购物车链接上的“增值保障”参数
                modifyBuyUrlParam('fsIds', '', G);
                if (_this.cfg.havestock && !_this.cfg.isHeYue) {
                    _this.get();
                } else {
                    _this.$el.hide().html();
                }
            }

            // 区域位置发生变化重新load
            Event.addListener('onAreaChange', function () {
                loadJdService();
            });
            
            Event.addListener('onHeYueReady', function () {
                loadJdService();
            });
           
            // 数量变化时触发的stock接口，只根据库存状态展示或隐藏“增值保障”模块
            Event.addListener('onStockReady', function() {
                if (_this.cfg.havestock && !_this.cfg.isHeYue) {
                    _this.$el.show();
                } else {
                    _this.$el.hide();
                }
            });

            // "白条分期"UI渲染完成后根据京东服务的状态屏蔽或启用“白条分期”
            Event.addListener('onBaiTiaoRender', function (data) {
                if (_this.cfg.havestock && !_this.cfg.isHeYue) {
                    globalConditionTrigger.callReceiverAction('JDServiceF')
                    globalConditionTrigger.callActions();
                }
            });
        },
        get: function() {
            var _this = this;
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/popAppreTemplate',
                data: {
                    skuId: _this.sku,
                    venderId: _this.venderId,
                    appreTemplateId: _this.appreTemplateId,
                    area: tools.getAreaId().areaIds.join('_'),
                    fromSource: 34,
                    priceSource: 0,
                    priceOrigin: '',
                    businessId: '',
                    appid: 'item-v3',
                    functionId: "pc_popAppreTemplate" 
                },
                dataType: 'jsonp',
                success: function(res) {
                    _this.set(res);
                }
            });
        },
        set: function(res) {
            var d = $.isArray(res) && res.length > 0 ? res[0].items : false;
        
            if ($.isArray(d) && d.length > 0) {
                // 数组长度限制，最多展示3类，每类4个
                var data = d.slice(0, 3);

                for (var i = 0, l = data.length; i < l; i+=1) {
                    var serviceSkus = data[i].serviceSkuInfoList;
                    if (serviceSkus) {
                        serviceSkus = serviceSkus.slice(0, 5);
                    }
                }
                
                this.$el.html(template.process({data: data})).data('data', data);
                this.show();

            } else {
                this.$el.hide().html('');
            }
            var arr = []
            if ($('#choose-serviceF .yb-item-cat').length > 3) {
                $('#choose-serviceF .yb-item-cat').map(function(index, element) {
                    return arr.push(element)
                }).get()
            }
            if (arr.length>3) {
                $('#choose-serviceF .service-type-yb-ff').css({'height': $(arr[3])[0].offsetTop,'overflow': 'hidden'});
            }else {
                $('#btn-open').css('display', 'none')
                $('#choose-serviceF .open-opens').css('display','none')
            }
            tools.ServiceOpen($('#choose-serviceF .service-type-yb-ff'),arr)
        },

        show: function() {
            if (this.cfg.havestock && !this.cfg.isHeYue) {
                this.$el.show();
            }
        },
        hide: function() {
            this.$el.hide();
        },
        clear: function() {
            this.$el.find('.yb-item').removeAttr('data-sku')
            this.$el.find('.yb-item-cat').removeClass('selected')
            this.$el.find('.yb-item-cat .more-item li').removeClass('selected')
        },
        calResult: function() {
            var selectedItems = this.$el.find('.selected');
            var skus = [];

            selectedItems.each(function() {
                var child = $(this).find('.yb-item');
                var sku = child.attr('data-sku');

                if (sku) {
                    skus.push(sku);
                }
            });

            if (typeof this.onSelected === 'function') {
                return this.onSelected.apply(this, [
                    skus, // 已选中的sku数组
                    this.currSku, // 当前选中的sku
                    this.currEl // 当前点击(jQuery)对象
                ]);
            }
        }
    };

    function init(cfg) {
        var $service = $('#choose-serviceF');
        var zzfwmb = cfg.specialAttrs.join('|').match(/zzfwmb\-([^\|]*)/);
        if (!$service.length || !zzfwmb) { return; }

        JdService.init({
            $el: $service,
            sku: cfg.skuid,
            venderId: cfg.venderId,
            appreTemplateId: zzfwmb ? zzfwmb[1] : '',
            onSelected: function(skus) {
                if (skus.length > 0) {
                    pageConfig.hasJDServiceF = true;
                    globalConditionTrigger.emit('JDServiceF', true);
                } else {
                    pageConfig.hasJDServiceF = false;
                    globalConditionTrigger.emit('JDServiceF', false);
                }
                modifyBuyUrlParam('fsIds', skus, G);
                Event.fire({type: 'onServiceFSelected', skus: skus});
            }
        }, cfg);
    }

    module.exports.__id = 'jdserviceF';
    module.exports.init = init;
});
