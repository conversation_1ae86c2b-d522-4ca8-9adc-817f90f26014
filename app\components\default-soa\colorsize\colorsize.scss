@import '../common/lib';
@import './__sprite';

//.clothing {
//    #choose-color {
//        .item {
            //height: auto;
            //a {
                //height: auto;
                //img {
                    //width: 60px;
                    //height: 76px;
//                }
//            }
//        }
//    }
//}
#choose-attrs {
    margin-bottom: 16px; // 无规格时也会显示此上边距
    .no-stock a,.no-stock a:hover {
        color: #999;
        //border: 1px solid #ccc
    }
    .no-stock{
        .no-stock-tip{
            position: absolute;
            right: 0;
            top: -5px;
            background-color: #888B94;
            padding: 3px 4px;
            height: 10px;
            line-height: 10px;
            border-bottom-left-radius: 2px;
            border-top-right-radius: 2px;
            color: #fff;
            font-size: 12px;
            border-top-left-radius: 2px;
        }
        .no-stock-icon{
            position: absolute;
            left: 50%;
            top: 15px;
            margin-left: -25px;
            height: 9px;
            line-height: 9px;
            width: 50px;
            height: 50px;
            background: url(https://img13.360buyimg.com/imagetools/jfs/t1/232396/20/12285/2241/65b21de4F4978208d/cc7262ce1f3225cd.png) 0 0 no-repeat;
            background-size: 100%;
        }
    }
    .item.disabled{
        a{
            color: #C2C4CC;
            border: 1px #eee solid;
            text-decoration: line-through;
            img{
                opacity: 0.6;
            }
        }
        .no-stock-tip,.no-stock-icon{
            display: none;
        }
    }
}
.choose-attr-clothes{
    .item{
        &.disabled,&.no-stock{
            a,a:hover{
                opacity: 0.5;
                border: 1px solid #898989;
                cursor: not-allowed;
                color: #999;
            }
        }
    }
}
//#choose-color
#choose-attr-1 {
    line-height: 0px;
    .item {
        //_width:100px;
        //height: 40px;
        // background-color: #F7F7F7;
        margin-bottom: 8px; // 设计要求改为8px
        a {
            //height: 40px;
            padding: 0;
            height: 34px;
            line-height: 34px;
            overflow: hidden;
            i {
                margin: 0 8px;
            }
            img{
                margin-left: 3px;
                margin-top: -3px;
            }
        }
    }
}
#choose-attr-2 {
    i.reco {
        position: absolute;
        right: 0;
        top: 0;
        width: 13px;
        height: 13px;
        line-height: 13px;
        background:url(//img12.360buyimg.com/devfe/jfs/t5896/169/7905619409/187/3c4afdd2/5975bbe5Nc1b752e2.png) 0 0 no-repeat;
    }
    
}

/*尺码助手*/
.size-assistant{
    display: inline-block;
    *display: inline;
    *zoom: 1;
    margin-bottom: 4px;
    line-height: 28px;
    border: 1px solid #ced2e6;
    border-radius: 14px;
    background: #fff;
    .sprite-size{
        display: inline-block;
        margin-right: 8px;
        vertical-align: -4px;
        @include sprite-size;
    }
    span{
        margin-left: 5px;
        // font-family: simsun;
    }
    a{
        display: inline-block;
        padding: 0 12px;
        line-height: 28px;
        color: #6067aa;
    }
}

.ebook #choose-color .item {
    height: 43px;
}

//.choose-version {
//    .item {
//        padding: 0 20px;
//    }
//}
//
//.choose-spec {
//    .item {
//        padding: 0 13px;
//    }
//}

/*book*/
.ebook{
    #choose-series{
        line-height: 40px;
        margin-top: 14px;
        .item a{
            padding: 0;
            i{
                margin: 0 8px;
            }
        }
    }
    #choose-color{
        margin-top: 15px;
    }
}
/*building*/
.building{
    #choose-color{
        padding-top: 15px;
        border-top: 1px dotted #dfdfdf;
    }
}




