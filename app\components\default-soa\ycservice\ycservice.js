define('MOD_ROOT/ycservice/ycservice', function(require, exports, module) {
    require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var G = require('MOD_ROOT/common/core');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var buybtn = require('MOD_ROOT/buybtn/buybtn').addToCartBtn;
    var tools = require('MOD_ROOT/common/tools/tools');
    var globalConditionTrigger = tools.ConditionTrigger.globalConditionTrigger;
    var PROTOCOL_REGEXP = new RegExp('^' + location.protocol);

    var template = '\
        <div class="dt" data-yb="new_yb_server">${title}</div>\
        <div class="dd">\
            <div class="service-type-yb clearfix">\
                {for item in data}\
                    <div class="yb-item-cat">\
                        <div class="yb-item">\
                            <span class="name">${item.scName}</span>\
                            <span class="name-tip"></span>\
                            <div class="after"></div>\
                        </div>\
                        <div class="more-jd-item">\
                        {for list in item.products}\
                        <div data-sku="${list.serviceSku}" class="more-item">\
                            <ul>\
                                <li data-sku="${list.serviceSku}">\
                                    <div class="title" clstag="shangpin|keycount|product|jingdongfuwu_${list.serviceSku}" >\
                                        <span class="choose-btn" clstag="shangpin|keycount|product|jingdongfuwu_${list.serviceSku}">\
                                        <span class="name">${list.serviceSkuName}</span><span class="name-tip" style="display: none">${list.tip}</span>{if (pageConfig.product.venderId == 1000000127 && list.serviceSkuName == "换修无忧")}<span class="price">￥${list.serviceSkuPrice}/月</span>{else}<span class="price">￥${list.serviceSkuPrice}</span>{/if}\
                                        </span>\
                                    </div>\
                                </li>\
                            </ul>\
                        </div>\
                        {/for}\
                        </div>\
                    </div>\
                {/for}\
            </div>\
            <div id="btn-open-div" class="btn-open-div" >\
                        <span id="btn-open" class="btn-open">\
                        展开全部\
                        </span>\
                        <img class="open-opens" style="margin-left: 8px" width="10" height="10" src="//img11.360buyimg.com/imagetools/jfs/t1/264116/11/14470/583/6790c6b0F446ef23c/4ae3a1af1d4ac2d4.png" />\
                        </div>\
                <div class="service-tips hide">\
        </div>';


    //////////  判断购买按钮的链接是否带有请求参数 //////////////
    function hasQueryParams(url, G) {  // 非通用函数
        // 如果url缺少协议部分填充协议，因为`serializeURL`方法序列化的是一个严格的url。
        if (!PROTOCOL_REGEXP.test(url)) {
            url = location.protocol + url
        }
        try {
            var urlDict = G.serializeURL(url);
            var query = urlDict.query;
            for (var k in query) {
                if (query.hasOwnProperty(k)) {
                    return true;
                }
            }
            return false;
        } catch (error) {
            console && console.log(error);
            return false;
        }
    }

    var isBuyUrlHasQueryParams = hasQueryParams(G.originBuyUrl, G);
    ////////////////////////////////////////////////////////////

    function modifyBuyUrlParam(key, value, G) {
        // var href = buybtn.$el.attr('href');
        var reservationBtn=$('#btn-reservation,#btn-reservation-mini')
        var href = buybtn.$el.length?buybtn.$el.attr('href'):reservationBtn.attr('href');
        var hasProtocol = PROTOCOL_REGEXP.test(href);
        var dict = {
            query: {}
        };

        if (!hasProtocol) {
            href = location.protocol + href;
        }

        if (G.is('', value)) {
            dict.query[key] = value;
        } else if (G.is([], value)) {
            dict.query[key] = value.join(',');
        }

        try {
            href = G.modifyURL(href, dict, true);
        } catch (error) {
            console && console.log(error);
        }

        if (!hasProtocol) {
            href = href.replace(PROTOCOL_REGEXP, '');
        }

        if (isBuyUrlHasQueryParams) {
            buybtn.enabled(href);
        }
        if(reservationBtn.length){
            reservationBtn.attr('href', href);
        }

        pageConfig.product.cartBuyUrlParam = href // 全局购物车链接参数

    }

    var YcService = {
        init: function(opts, cfg) {
            this.sku = opts.sku;
            this.cat = opts.cat;
            this.brand = opts.brand;
            this.$el = opts.$el || $('#choose-serviceyc');
            this.onSelected = opts.onSelected || function() {};
            this.cfg = cfg;
            this.currSku = null;
            //this.get();
            this.bindEvent(opts,cfg);
        },
        bindEvent: function(opts,cfg) {
            var _this = this;
            //这里注释主要是为了点击标题名字的时候不会默认选择第一个
            // this.$el.delegate('.yb-item', 'click', function() {
            //     var $this = $(this),
            //         $ybItemCat = $this.parents('.yb-item-cat')
            //     if ($ybItemCat.hasClass('selected')) {
            //         //已选中
            //         $ybItemCat
            //             .find('li.selected .choose-btn')
            //             .trigger('click')
            //         _this.updateFrist(
            //             $ybItemCat.find('.more-item li:eq(0)'),
            //             $ybItemCat.find('.yb-item')
            //         )
            //     } else {
            //         //没选中
            //         $ybItemCat
            //             .find('.more-item li:eq(0) .choose-btn')
            //             .trigger('click')
            //     }
            // });
            //dom choose-btn 更改为 more-item更改主要是改变点击区域
            this.$el.delegate('.more-item', 'click', function() {
                tools.ClickMoreItem($(this),_this)
            })

            // this.$el.delegate('.more-item','mouseover', function() {
            //     // tooltip.style.visibility = 'visible';
            //     tools.mouseTip($(this),'mouseover',$(this).find('.name-tip').attr('tip-name'))
            //     // $(this).find('.title').attr('title','')
            // });
            //
            // this.$el.delegate('.more-item','mouseout', function() {
            //     // tooltip.style.visibility = 'visible';
            //     // console.log('到我这了777')
            //     tools.mouseTip($(this),'mouseout',$(this).find('.name-tip').attr('tip-name'))
            //     // $(this).find('.title').attr('title','')
            // });

            // this.$el.delegate('.yb-item-cat', 'mouseenter', function() {
            //     $(this).addClass('hover')
            // })

            // this.$el.delegate('.yb-item-cat', 'mouseleave', function() {
            //     $(this).removeClass('hover')
            // })



            var timeoutId = 0
            this.$el.delegate('.service-tips', 'mouseenter', function() {
                clearTimeout(timeoutId)
                $(this).addClass('hover')
            })

            this.$el.delegate('.service-tips', 'mouseleave', function() {
                var $this = $(this)
                timeoutId = setTimeout(function() {
                    $this.removeClass('hover')
                }, 300)
            });

            // 选择性屏蔽“白条分期”
            globalConditionTrigger
                .addReceiver('YCService', function () {
                    if ('JDService' in globalConditionTrigger.receivers) {
                        globalConditionTrigger.callReceiverAction('JDService');
                    }
                    // if ('YCService' in globalConditionTrigger.receivers) {
                    //     globalConditionTrigger.callReceiverAction('YCService');
                    // }
                    if ('JDServiceF' in globalConditionTrigger.receivers) {
                        globalConditionTrigger.callReceiverAction('JDServiceF');
                    }
                }, function name(data) {
                    if ('JDService' in globalConditionTrigger.receivers) {
                        globalConditionTrigger.callReceiverAction('JDService');
                    }
                    // if ('YCService' in globalConditionTrigger.receivers) {
                    //     globalConditionTrigger.callReceiverAction('YCService');
                    // }
                    if ('JDServiceF' in globalConditionTrigger.receivers) {
                        globalConditionTrigger.callReceiverAction('JDServiceF');
                    }
                }).addAction({
                    success: function () {
                        if (_this.cfg.baiTiaoFenQi) {
                            _this.cfg.baiTiaoFenQi.disabled();
                            tools.showTradeUrl(_this) // 隐藏白条按钮需要看看立即展示逻辑
                        }
                    },
                    failure: function () {
                        if (_this.cfg.baiTiaoFenQi) {
                            _this.cfg.baiTiaoFenQi.enabled();
                        }
                    }
                });



            // 加载“增值保障”模块
            function loadYB() {
                // 重置条件集状态
                globalConditionTrigger.emit('YCService', false);
                // 重置购物车链接上的“增值保障”参数
                modifyBuyUrlParam('jd3csid', '', G);
                if (_this.cfg.havestock && !_this.cfg.isHeYue) {
                    _this.get();
                } else {
                    _this.cfg.hasYcInfo = false;
                    _this.$el.hide().html();
                }
            }

            // 区域位置发生变化重新load
            Event.addListener('onAreaChange', function (data) {
                _this.stockData = data.stock
                loadYB();
            });


            // 合约机不支持“增值保证”
            Event.addListener('onHeYueReady', function () {
                loadYB();
            });


            // 数量变化时触发的stock接口，只根据库存状态展示或隐藏“增值保障”模块
            Event.addListener('onStockReady', function(r) {
                var stockData = r && r.stock && r.stock.data
                var originalFactoryServiceVo = stockData && stockData.warrantyInfo && stockData.warrantyInfo.originalFactoryServiceVo
                if (_this.cfg.havestock && !_this.cfg.isHeYue && (typeof originalFactoryServiceVo != "undefined")) {
                    _this.$el.show();
                } else {
                    _this.$el.hide();
                }
            });




            // "白条分期"UI渲染完成后根据京东服务的状态屏蔽或启用“白条分期”
            Event.addListener('onBaiTiaoRender', function (data) {
                if (_this.cfg.havestock && !_this.cfg.isHeYue) {
                    globalConditionTrigger.callReceiverAction('YCService');
                    globalConditionTrigger.callActions();
                }
            });
        },
        get: function() {
            var _this = this;
            var warrantyInfo=this.stockData.data && this.stockData.data.warrantyInfo
            // $.ajax({
            //     url: '//cd.jd.com/bindServices',
            //     data: {
            //         skuId: _this.sku,
            //         cat: _this.cat.join(','),
            //         brandId: _this.brand,
            //         area: tools.getAreaId().areaIds.join('_'),
            //         price: _this.skuPrice,
            //         from: "80880C513849235B2FDCE5CF719C29D7"
            //     },
            //     dataType: 'jsonp',
            //     scriptCharset: 'gbk',
            //     success: function(res) {
            //         _this.set(res);
            //         _this.getService(res);

            //     }
            // });
            this.set(warrantyInfo);
            // Event.addListener('onWareBusinessReady', function (res) {
            //     console.log("yanbao-data",res)
            //     _this.set(res.data.warrantyInfo);
            // });

        },
        getService:function(res){
            Event.fire({
                type: 'getService',
                data: res
            })
        },
        set: function(res) {
            var data = res && res.originalFactoryServiceVo && res.originalFactoryServiceVo.serviceItems;
            var title = res && res.originalFactoryServiceVo && res.originalFactoryServiceVo.title;
            this.$el.hide().html('');
            for (var m in data) {
                var items = data[m];
                //if (items.type === "1") {
                    if ($.isArray(data) && data.length > 0) {
                        // 最多3项服务，每项6条内容
                        // if (data.length > 3) {// 数量由接口控制
                        //     data.length = 3;
                        // }

                        // for (var i in items) {
                        //     var item = items[i];
                        //     if (item.products && item.products.length > 6) {
                        //         item.products.length = 6;
                        //     }
                        // }

                        var result = '';

                        try {
                            result = template.process({
                                data: data,
                                title: title
                            });
                            this.$el.html(result);
                            this.show();
                            this.cfg.hasYcInfo = true;

                        } catch (err) {
                            var errMsg = result.match(/\[ERROR.+\]/);
                            if (errMsg && errMsg.length) {
                                console.error('Template Render Error @ [jdService.js]. >>>>> \n   %s', errMsg[0]);
                            }
                        }
                    } else {
                        this.$el.hide().html('');
                        this.cfg.hasYcInfo = false;
                    }
                //}
            }
            try {
                var arrItem = []
                if ($('#choose-serviceyc').css('display') === 'block') {
                    arrItem.push({serviceType:'京选服务'})
                }
                if ($('#choose-service').css('display') === 'block') {
                    arrItem.push({serviceType:'增值保障'})
                }
                if ($('#choose-service\\+').css('display') === 'block') {
                    arrItem.push({serviceType:'京东服务'})
                }
                if ($('#choose-serviceF').css('display') === 'block') {
                    arrItem.push({serviceType:'增值服务'})
                }
                if (arrItem.length !== 0) {
                    tools.exposure({
                        functionName: 'PC_Productdetail_PayServiceFloor_Expo',
                        exposureData: ['mainskuid'],
                        extraData: {
                            servicesku:arrItem
                        },
                        errorTips: '服务楼层曝光-异常'
                    })
                }
            }catch (e) {
                console.log(e)
            }
            var arr = []
            if ($('#choose-serviceyc .yb-item-cat').length > 3) {
                $('#choose-serviceyc .yb-item-cat').map(function(index, element) {
                    return arr.push(element)
                }).get()
            }

            if (arr.length > 3) {
                $('#choose-serviceyc .service-type-yb').css({'height': $(arr[3])[0].offsetTop, 'overflow': 'hidden'});
            }else {
                $('#btn-open').css('display', 'none')
                $('#choose-serviceyc .open-opens').css('display','none')
            }
            tools.ServiceOpen($('#choose-serviceyc .service-type-yb'),arr)



        },
        show: function() {
            if (this.cfg.havestock) {
                this.$el.show()
            }
        },
        hide: function() {
            this.$el.hide()
        },
        clear: function() {
            this.$el.find('.yb-item').removeAttr('data-sku')
            this.$el.find('.yb-item-cat').removeClass('selected')
            this.$el.find('.yb-item-cat .more-item li').removeClass('selected')
        },
        calResult: function() {
            var selectedItems = this.$el.find('.selected')
            var skus = []

            selectedItems.each(function() {
                var child = $(this).find('.yb-item')
                var sku = child.attr('data-sku')

                if (sku) {
                    skus.push(sku)
                }
            })

            if (typeof this.onSelected === 'function') {
                return this.onSelected.apply(this, [
                    skus, // 已选中的sku数组
                    this.currSku, // 当前选中的sku
                    this.currEl // 当前点击(jQuery)对象
                ])
            }
        },
        setData: function (stock) {
            this.stockData = stock
        }

    }

    function init(cfg) {
        var $yb = $('#choose-serviceyc');
        if ($yb.length < 1) { return; }

        YcService.init({
            sku: cfg.skuid,
            cat: cfg.cat,
            brand: cfg.brand,
            onSelected: function(skus) {
                if (skus.length > 0) {
                    pageConfig.hasYcService = true;
                    globalConditionTrigger.emit('YCService', true);
                } else {
                    pageConfig.hasYcService = false;
                    globalConditionTrigger.emit('YCService', false);
                }
                modifyBuyUrlParam('jd3csid', skus, G);
                Event.fire({type: 'onYBSelected', skus: skus});
            }
        }, cfg);
    }

    module.exports.__id = 'ycservice'
    module.exports.init = init
})
