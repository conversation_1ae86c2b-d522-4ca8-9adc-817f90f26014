define('PUBLIC_ROOT/modules/club/club', function(require, exports, module) {

    require('JDF_UNIT/trimPath/1.0.0/trimPath');
    require('PUBLIC_ROOT/modules/ETab/ETab');

    var hotPostTpl = '\
    <ul class="clist-1" clstag="shangpin|keycount|product|taolunquan__${pageConfig.product.pType}">\
        {for item in result.groupThreads}\
        <li class="consult0">\
            {if item.threadImgUrl}\
            <div class="c-pic" data-url="${item.threadImgUrl}">\
                <a href="${item.threadUrl}" target="_blank">\
                    <img src="${item.threadImgUrl}" width="120" height="120" alt="">\
                </a>\
            </div>\
            {/if}\
            <div class="p-cont">\
                <a href="${item.threadUrl}" target="_blank">\
                    <h6>${item.subject}</h6>\
                    <p>${item.threadDesc} 详情&gt;&gt;</p>\
                </a>\
                <div class="c-details">\
                    <div class="c-issue">\
                        <span class="time">${item.created}</span>\
                        <em class="tel">${item.nickName}</em>\
                    </div>\
                    <div class="c-operate">\
                        <span class="">回复（${item.replies}）</span>\
                        <span class="">赞（${item.digest}）</span>\
                    </div>\
                </div>\
            </div>\
        </li>\
        {/for}\
    </ul>\
    <div class="c-total">共有<em class="c-totaliNum">${result.groupThreads.length}</em>个帖子 <a href="${result.siteUrl}" target="_blank">查看全部帖子&gt;&gt;</a></div>';

    var showPostTpl = '\
    <table width="100%" cellspacing="0" cellpadding="0" border="0" clstag="shangpin|keycount|product|shaidantie__${pageConfig.product.pType}">\
        <tfoot>\
            <tr>\
                <td colspan="4">\
                    <div class="c-total">\
                    共有<em class="c-totaliNum">${discussComments.CommentCount}</em>个帖子 \
                    <a href="//club.jd.com/bbs/${referenceId}-1-0-${ReferenceType}.html" target="_blank">查看全部帖子&gt;&gt;</a>\
                    </div>\
                </td>\
            </tr>\
        </tfoot>\
        <thead>\
            <tr>\
                <th class="col1">主题</th>\
                <th class="col2">回复/浏览</th>\
                <th class="col3">作者</th>\
                <th class="col4">发表时间</th>\
            </tr>\
        </thead>\
        <tbody>\
            {for item in discussComments.Comments}\
            <tr>\
                <td class="col1">\
                    <div class="topic">\
                        <a href="//club.jd.com/bbsDetail/${item.referenceId}_${item.id}_1.html" target="_blank">${item.title}</a>\
                    </div>\
                </td>\
                <td class="col2">${item.replyCount}/${item.viewCount}</td>\
                <td class="col3">\
                    <div class="u-name">\
                        <a target="_blank" title="${item.uRemark}" href="//me.jd.com/${item.uid}.html">{if item.uRemark}${item.uRemark}{else}${item.userId}{/if}</a>\
                    </div>\
                </td>\
                <td class="col4">${item.creationTime}</td>\
            </tr>\
            {/for}\
        </tbody>\
    </table>';

    var Club = {
        inited: false,
        init: function (cfg, sku, $el) {
            this.$el = $el;
            this.$postforumBtn = $el.find(".J-postforum");
            this.sku = sku;
            this.cfg = cfg;

            this.hotPostHref = '';

            this.bindEvent();
            this.getHotPost();

            this.inited = true;
        },
        bindEvent: function () {
            var _this = this;
            this.$el.ETab({
                onSwitch: function (n) {
                    if (n == 0) {
                        if(_this.hotPostHref) {
                            _this.$postforumBtn.attr("href", _this.hotPostHref);
                        }
                    }else if (n == 1) {
                        _this.getShowPost();
                    }
                }
            });
            this.tab = this.$el.data('ETab');
        },
        getHotPost: function () {
            var _this = this;
            var sku = typeof _this.cfg.isShadowSku != 'undefined' ? _this.cfg.isShadowSku : _this.sku;
            $.ajax({
                url: '//group.jd.com/sku/item.htm',
                //url: 'http://jsonpmock.xyz/a97c5d3a7a31b41a98de2a1b05562152',
                data: {
                    sku: sku
                },
                dataType: 'jsonp',
                success: function (r) {
                    _this.setHotPost(r);
                }
            })
        },
        setHotPost: function (r) {
            var $target = this.tab.items.eq(0);
            if (!r || !r.success || r.result.groupThreads.length < 1) {
                return $target.html('<div class="ac">「暂无热门帖」</div>');
            }

            $target.html(hotPostTpl.process(r));

            this.hotPostHref = r.result.siteUrl;
            this.$postforumBtn.attr("href", this.hotPostHref);
        },
        getShowPost: function () {
            var _this = this;

            var sku = typeof _this.cfg.isShadowSku != 'undefined' ? _this.cfg.isShadowSku : _this.sku;

            $.ajax({
                url: '//club.jd.com/clubservice/newcomment-Order-{0}.html'.format(sku),
                dataType: 'jsonp',
                jsonpCallback: 'fetchJSON_Discuss',
                success: function (r) {
                    _this.setShowPost(r);
                }
            });
        },
        setShowPost: function (r) {
            var $target = this.tab.items.eq(1);
            if (!r || !r.discussComments || r.discussComments.Comments.length < 1) {
                return $target.html('「暂无晒单贴」');
            }

            $target.html(showPostTpl.process(r));
            var link = "//club.jd.com/bbs/{0}-1.html".format(r.referenceId);
            this.$postforumBtn.attr("href", link);
        }
    };

    function init(cfg) {
        Club.init(cfg, cfg.skuid, $('#club'));
    }
    
    module.exports.__id = 'club';
    module.exports.init = init;
    module.exports.Club = Club;
});
