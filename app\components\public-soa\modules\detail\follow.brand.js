define('PUBLIC_ROOT/modules/detail/follow.brand', function(require, exports, module) {
    var login = require('JDF_UNIT/login/1.0.0/login');
    // 关注品牌
    var FollowBrand = {
        init: function($el, brandId) {
            this.$el = $el;
            this.brandId = brandId || this.$el.attr('data-bid');

            this.followed = false;
            this.ROUTE = '//follow-soa.jd.com/rest/brand';

            this.getFollowStatus();
            this.bindEvent();
        },
        bindEvent: function() {
            this.$el.unbind('click')
                .bind('click', $.proxy(this.handleClick, this));
        },
        handleClick: function(e) {
            if (this.followed) {
                this.unfollow();
            } else {
                login({
                    modal: true,
                    complete: $.proxy(this.follow, this)
                });
            }
        },
        getFollowStatus: function() {
            $.ajax({
                url: this.ROUTE + '/isFollowByBrandId',
                data: {
                    sysName: 'item.jd.com',
                    brandId: this.brandId
                },
                dataType: "jsonp",
                success: $.proxy(this.setFollowStatus, this)
            });
        },
        setFollowStatus: function(r) {
            // {code: "F10000", msg: null, data: {}, success: true}
            if (r && r.success && r.data) {
                this.setFollow();
            } else {
                this.setUnfollow();
            }
        },
        setFollow: function() {
            this.$el.html('<b>&hearts;</b>已关注');
            this.followed = true;
        },
        setUnfollow: function() {
            this.$el.html('<b>&hearts;</b>关注')
            this.followed = false;
        },
        follow: function() {
            $.ajax({
                url: this.ROUTE + '/follow',
                dataType:"jsonp",
                data: {
                    sysName: 'item.jd.com',
                    brandId: this.brandId
                },
                success: $.proxy(this.handleFollow, this)
            });
        },
        unfollow: function() {
            $.ajax({
                url: this.ROUTE + '/unfollowByBrandId ',
                dataType:"jsonp",
                data: {
                    sysName: 'item.jd.com',
                    brandId: this.brandId
                },
                success: $.proxy(this.handleUnfollow, this)
            });
        },
        handleFollow: function(r) {
            if (r && r.success && r.data) {
                this.setFollow();
            }
        },
        handleUnfollow: function(r) {
            if (r && r.success && r.data) {
                this.setUnfollow();
            }
        }
    };

    module.exports = FollowBrand;
});
