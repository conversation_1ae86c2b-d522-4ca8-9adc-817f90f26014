define('MOD_ROOT/info/info', function(require, exports, module) {
    var Event = require('MOD_ROOT/common/tools/event').Event
    var Tools = require('MOD_ROOT/common/tools/tools')

    // 设置温馨提示
    // usage: pageConfig.Tip.set(n, html);
    function Tips($el) {
        this.$el = $el
        // this.len = len

        this.items = []
        // this.items.length = len
    }
    Tips.prototype = {
        set: function(n, text) {
            // if (n > this.len) {
            //     throw new Error('index error.')
            // }
            if (text) {
                this.items[n] = text
                this.show()
            }

            return this
        },
        del: function(n) {
            this.items[n] = null
            this.show(n)
        },
        show: function() {
            var len = this.items.length
            var i = 0
            var html = []
            var $target = this.$el.find('ol')

            for (i; i < len; i++) {
                if (this.items[i]) {
                    if(this.items[i].indexOf('完成预约') >= 0 && pageConfig.product.skuMarkJson.pg){
                        continue;
                    }else{
                        html.push('<li class="local-txt">' + this.items[i] + '<em>|</em></</li>')
                    }
                }
            }

            if (html.length > 0) {
                $target.html(html.join(''))
                this.$el.show()
            } else {
                this.$el.hide()
            }
        },
        hideLast: function() { // 隐藏每行的最后一个竖杠
            var tips = $('.local-txt')
            var lastTips = [];
            var currentRowLastIndex = 0;
            for (var i = 0; i < tips.length; i++) {
                var currentTop = tips[i].getBoundingClientRect().top;
                var prevTop = tips[currentRowLastIndex].getBoundingClientRect().top;
                if (currentTop!== prevTop) {
                    lastTips.push(tips[i - 1]);
                    currentRowLastIndex = i;
                }
                if (i === tips.length - 1) { // 最后一个
                    lastTips.push(tips[i]);
                }
            }
            // console.log('lastTips', lastTips);
            lastTips.forEach(function(tip, index) {
                $(tip).find('em').hide()
            })
            
        }
    }

    /**
     * 0 - xx地区支持礼品包装
     * 1 - 本商品不能使用 东券
     * 2 - 请完成预约后及时抢购！
     * 3 - 支持7天无理由退货
     * 4 - 购买eBay商品特殊条款
     * 5 - 优惠券信息请以结算页为准
     * 6 - 请仔细阅读产品说明书或者在药师指导下购买和使用
     * 7 - 请阅读合约机购买说明
     * 8 - 在线支付订单需要在6小时内支付，否则订单将被取消
     * 9 - 该商品需要实名认证才可抢购
     * 10 - 该商品超时未付款，订单将被自动取消
     * 11 - 保健食品是食品，不是药品，不具有疾病预防、治疗功能，不能替代药物。
     * 12 - 工业品发票温馨提示
     * 53 - 百亿补贴限购详情提示，后续增加都从序号53开始
     * 100 以上 - 属于后端配置
     */
    var Tip = (pageConfig.__tips = new Tips($('#summary-tips')))

    /**
     * 0 - cms 数据1
     * 1 - cms 数据2
     * 2 - promiseIcon Type== 5
     */
    //var LocalTip = pageConfig.__localTip = new Tips($('#local-tips'), 5);

    function setLocalAct(cfg) {
        var $el = $('#local-tips')
        var tpl =
            '<li>·<a data-aid="{0}" href="{1}" target="_blank" clstag="shangpin|keycount|product|bendihuodong-{2}">{2} &gt;&gt;</a></li>'

        function setTip(data) {
            if (!data || !data.stock || !data.stock.data) return false;

            var stock = data.stock.data.stockInfo
            var cla = stock.cla

            if (!cla || !cla.length) {
                $el.hide().find('.tips-list').html('')
            } else {
                var html = ''
                for (var j = 0; j < cla.length; j++) {
                    var currAct = cla[j]
                    html += tpl.format(currAct.id, currAct.url, currAct.name)
                }
                $el.show().find('.tips-list').html(html)
            }
        }

        // setTip()

        // Event.addListener('onStockReady', setTip)// cla功能下线
    }

    function setTips(cfg) {
        var tips = cfg.tips
        var hasTips = tips && tips.length

        setConfigTips()

        function setConfigTips() {
            // 设置温馨提示
            if (hasTips) {
                for (var i = 0; i < tips.length; i++) {
                    if (tips[i].order == 1) {
                        // pass 屏蔽主数据下发的优惠券信息
                    } else {
                        Tip.set(tips[i].order, tips[i].tip)
                    }
                }
            }
        }

        function setBuyTypeTips(r) {
            var isHeYue = cfg.isHeYue

            if (isHeYue && !r.noBuyType) {
                Tip.set(
                    7,
                    '<a target="_blank" href="//sale.jd.com/act/IgFRVepkJKr.html">请阅读合约机购买说明 &gt;&gt;</a>'
                )
            } else {
                Tip.del(7)
            }
        }
        function setKOTips(data) {
            var link =
                '//authpay.jd.com/auth/toAuthPage.action?source=62&directReturnUrl=' +
                location.href
            var html = '该商品需要实名认证才可抢购，<a target="_blank" href="{0}">去实名 &gt;&gt;</a>'.format(
                link
            )
            if (data.r && data.r.shiming === '1') {
                Tip.set(9, html)
            } else {
                Tip.del(9)
            }
        }

        // 地区检测
        function detectArea(data) {
            var stock = data && data.stock && data.stock.data && data.stock.data.stockInfo;
            var pid = stock && stock.area && stock.area.provinceId;
            // 1. 港澳 不支持 tips 3
            if (pid === 52993 || pid === 53283 || pid === 32) {
                Tip.set(0, '不支持7天无理由退货');
            } else {
                if (stock && stock.is7ToReturn && (typeof stock.is7ToReturn === 'string')) {
                    // Tip.set(0, stock.is7ToReturn);
                } else {
                    Tip.del(0);
                }
            }
        }

        Event.addListener('onHeYueReady', setBuyTypeTips);
        Event.addListener('onKOReady', setKOTips);
        Event.addListener('onStockReady', detectArea);
       
        Event.addListener('onStockReady', function (data) {
             // 优惠券提示信息由促销接口下发
            var promotion = data && data.stock && data.stock.data && data.stock.data.promotion;
            var prompt = promotion && promotion.prompt;
            if (prompt  && (typeof prompt === 'string')) {
                Tip.set(1, prompt);
            } else {
                Tip.del(1);
            }
            
            // 工业品发票温馨提示
            var gongyepin = data && data.stock && data.stock.data && data.stock.data.gongyepin;
            var invoiceTip = gongyepin && gongyepin.invoiceTip;
            if (invoiceTip  && (typeof invoiceTip === 'string')) {
                Tip.set(12, invoiceTip);
            } else {
                Tip.del(12);
            }

            // 百亿补贴限购温馨提示
            var stockData = data && data.stock && data.stock.data && data.stock.data
            var bybtInfo = stockData.bybtInfo// 百亿补贴对象
            var bybtInfoBybt = bybtInfo && bybtInfo.bybt;//百亿补贴商品标标识
            var bybtInfoLimit = bybtInfo && bybtInfo.limit;// 百亿补贴限购信息
            if(bybtInfoBybt && bybtInfoLimit && bybtInfoLimit.flag){// 命中百亿补贴并限购信息有效
                var scene = bybtInfoLimit.scene || "";// 限购场景，1:pin维度限购 2：设备号维度限购
                var limitType = bybtInfoLimit.limitType || "";// 限购类型，1:商品池限购 0:商品单品限购
                var day = bybtInfoLimit.day || "";// 限购天数
                var num = bybtInfoLimit.num || "";// 限购件数
                var List = bybtInfoLimit.orderInfo || [];// 订单信息
                var tip1 = ''// 温馨提示文案
                var tip2 = ''// 点击详情顶部文案
                var listHtml = ''// 循环商品数据
                if(scene == "pin" && limitType == "1"){// pin维度限购+商品池限购
                    if(List && List.length > 0){ // 历史订单有数据才展示详情入口
                        tip1 = '<span style="color: rgba(250, 44, 25, 1);">同一活动商品'+day+'天共限购'+num+'件，已达购买上限，<a href="#none" id="checkDetail" style="color:#e4393c">查看详情 &gt;</a></span>'
                        tip2 = '该商品与以下商品属于同一类活动，'+day+'天共限购'+num+'件，以下为历史订单'
                    }else{
                        tip1 = '<span style="color: rgba(250, 44, 25, 1);">同一活动商品'+day+'天共限购'+num+'件，已达购买上限<span>'
                    }
                    
                }else if(scene == "pin" && limitType == "0"){// pin维度限购+商品单品限购
                    if(List && List.length > 0){ // 历史订单有数据才展示详情入口
                        tip1 = '<span style="color: rgba(250, 44, 25, 1);">该商品'+day+'天内限购'+num+'件，已达购买上限，<a href="#none" id="checkDetail" style="color:#e4393c">查看详情 &gt;</a></span>'
                        tip2 = '该商品'+day+'天内限购'+num+'件，以下为历史订单'
                    }else{
                        tip1 = '<span style="color: rgba(250, 44, 25, 1);">该商品'+day+'天内限购'+num+'件，已达购买上限<span>'
                    }
                }else if(scene == "device" && limitType == "1"){// 设备号维度限购+商品池限购
                    tip1 = '<span style="color: rgba(250, 44, 25, 1);">同一设备下，同一活动商品'+day+'天共限购'+num+'件，您可更换设备再次尝试购买</span>'
                }else if(scene == "device" && limitType == "0"){//设备号维度限购+商品单品限购
                    tip1 = '<span style="color: rgba(250, 44, 25, 1);">同一设备下，该商品'+day+'天内限购'+num+'件，您可更换设备再次尝试购买</span>'
                }
                
                if (tip1  && (typeof tip1 === 'string')) {
                    Tip.set(53, tip1);
                } else {
                    Tip.del(53);
                }

                if(List && List.length > 0){
                    for (var i = 0; i < List.length; i++) {
                        if(i < 30 && List[i].title) {// 历史订单信息展示限制30条并且没有标题不展示这条订单信息
                            listHtml+= '<li>\
                                <div class="good-img">\
                                    <img src="'+List[i].img+'" width="48"/>\
                                </div>\
                                <div class="good-content">\
                                    <div class="good-title" title="'+List[i].title+'">'+List[i].title+'</div>\
                                    <div class="good-date">\
                                        <div class="good-time">下单时间：'+List[i].date+'</div>\
                                        <div class="good-num">共'+List[i].num+'件</div>\
                                    </div>\
                                </div>\
                            </li>'
                        }
                    }
                }

                $("body").find("#checkDetail").click(function(){
                    var template = '\
                    <div class="bybt-detail">\
                        <div class="goods-info">\
                        <i></i>'+tip2+'\
                        </div>\
                        <div class="goods-list">\
                        <ul>\
                            '+listHtml+'\
                        </ul>\
                        </div>\
                    </div>';

                    $("body").dialog({
                        title: "百亿补贴限购提示", 
                        width: 480, 
                        height: 420,
                        source: "<div class='dialogDom'>"+template+"</div>",
                        onReady: function () {
                            
                        }
                    });
                    // try {// 企业计划购按钮点击埋点
                    //     log('smb_pc', 'eppbutton', '{"skuid ":' + cfg.skuid + '}')
                    // } catch (e) {
                    //     if (typeof console !== 'undefined') {
                    //         console.log('企业计划购按钮点击埋点错误');
                    //     }
                    // }
                })
            }
            
            Tip.hideLast()
        });
    }

    function setMbuyOnly() {
        // 手机专享二维码
        var $mobileOnly = $('.J-mobile-only')
        var $qrcode = $mobileOnly.find('.qrcode')

        if ($mobileOnly.length) {
            var url = $qrcode.attr('data-url')
            var qrcodeUrl =
                url ||
                '//m.jd.com/product/' +
                    pageConfig.product.skuid +
                    '.html?from=qrcode'

            require.async('PLG_ROOT/jQuery.qrcode', function() {
                $qrcode.jdQrcode({
                    render: 'image',
                    ecLevel: 'L',
                    size: 80,
                    text: qrcodeUrl
                })
            })
        }
    }

    // function setErShouNew(cfg) {
    //     var $el = $('#er-same')
    //     if (!cfg.isErshou || !$el.length) return

    //     function set(r) {
    //         if (!r || r.message == '无关联数据') return

    //         $el.show().find('a').attr('href', '//item.jd.com/'+ r +'.html')
    //         Tools.priceNum({
    //             skus: [r],
    //             $el: $el,
    //             callback: function (sku, d) {
    //                 if (d && d.p && Number(d.p) > 0) {
    //                     $el.find('.price').html('￥' + d.p)
    //                 } else {
    //                     $el.find('.price').html('暂无报价')
    //                 }
    //             }
    //         });
    //     }

    //     $.ajax({
    //         url: '//cd.jd.com/second?skuId=' + cfg.skuid,
    //         dataType: 'jsonp',
    //         success: set
    //     })
    // }

    function setCfyTip(cfg){// 命中处方药需要在标题下面设置一个特殊的提示
        var isCfy = cfg.isCfy
        if(isCfy){
            $(".sku-name").after("<div class='cfy-tips'><span>处方药</span>处方药须凭处方在药师指导下购买和使用</div>")
        }
    }

    function init(cfg) {
        setTips(cfg)
        //setGift(cfg);
        setLocalAct(cfg)
        setMbuyOnly(cfg)
        // setErShouNew(cfg) //阿波罗接口已经下线
        setCfyTip(cfg)
    }

    module.exports.__id = 'info'
    module.exports.init = init
    module.exports.Tip = Tip
    //module.exports.LocalTip = LocalTip;
})
