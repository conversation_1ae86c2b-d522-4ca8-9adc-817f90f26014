define('MOD_ROOT/contact/followshop', function(require, exports, module) {
    var login = require("JDF_UNIT/login/1.0.0/login");
    var Tools = require('MOD_ROOT/common/tools/tools');
    var FollowShop = function(params) {
        function noop(){};
        this.settings = $.extend({
            $el: $('.J-follow-shop'),
            venderId: "",
            onFollow: noop,
            onUnFollow: noop,
            onCheckFollowState: noop
        }, params);
        this.followState = false;
        this.init();

        var locname = window.location.hostname
        if(locname == "item.jdh.com"  || locname == "item.jingdonghealth.cn"){// 健康域名屏蔽关注
            $('.J-follow-shop').hide()
        }
        
    };

    FollowShop.prototype = {
        init: function() {
            this.checkFollowState();
            this.bindEvent();
        },
        
        bindEvent: function() {
            var that = this;
            var hasCalled = false
            var locname = window.location.hostname
            var loginUrl =
                        'https://passport'+locname.split("item")[1]+'/new/login.aspx?ReturnUrl=' +
                        encodeURIComponent(location.href)
            // function onComplete() {
            //     if (!onComplete.hasCalled) {
            //         this.checkFollowState();
            //         onComplete.hasCalled = true;
            //     }
            //     if (this.getFollowState()) {
            //         this.unFollow();
            //     } else {
            //         this.follow();
            //     }
            // }


            

            function handleClick() {
                // login({
                //     modal: true,
                //     complete: $.proxy(onComplete, that)
                // });
                Tools.checkLogin(function (r) {
                        if(r && r.IsAuthenticated){
                            that.checkFollowState();
                            if (that.getFollowState()) {
                                that.unFollow();
                            } else {
                                that.follow();
                            }
                        }else{
                            window.location.href = loginUrl
                        }
                        
                })
            }

            this.settings.
            $el.unbind().bind("click", handleClick);
        },

        getFollowState: function() {
            return this.followState;
        },

        setFollowState: function(value) {
            this.followState = value;
        },

        request: function(url, params, onSuccess, onError) {
            return $.ajax({
                url: url,
                // data: $.extend({
                //     // venderId: "",
                //     // venderIds: "",
                //     sysName: "item.jd.com"
                // }, params),
                data: params,
                // dataType: 'jsonp',
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                },
                success: function() {
                    typeof onSuccess === "function" &&
                    onSuccess.apply(null, arguments);
                },
                error: function() {
                    typeof onError === "function" &&
                    onError.apply(null, arguments);
                }
            });
        },

        follow: function () {
            var that = this;
            var body = JSON.stringify({
                venderId: that.settings.venderId,
                sysName: "item.jd.com"
            });
            var time = new Date().getTime()
            var colorParm = {
                appid: 'item-v3',
                functionId: 'pctradesoa_vender_follow',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                // ids: skuid
                body: body,
            }
            try{
                colorParm['uuid'] = Tools.getCookieNew("__jda")
            }catch(e){
                colorParm['uuid'] = ''
            }
           

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            // var url = "//fts.jd.com/follow/vender/follow";
            that.request(host, colorParm).done(function (res) {
                res = res || {};
                if (res.success) {
                    that.setFollowState(true);
                    that.settings.onFollow.call(that);
                }
                that.render();
            }).fail(function (){
                console && 
                console.log(url + "接口调用失败");
            });
        },

        unFollow: function () {
            var that = this;
            // var url = "//fts.jd.com/follow/vender/unfollow";
            var body = JSON.stringify({
                venderId: that.settings.venderId,
                sysName: "item.jd.com"
            });
            var time = new Date().getTime()
            var colorParm = {
                appid: 'item-v3',
                functionId: 'pctradesoa_vender_unfollow',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                // ids: skuid
                body: body,
            }

            try{
                colorParm['uuid'] = Tools.getCookieNew("__jda")
            }catch(e){
                colorParm['uuid'] = ''
            }

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            that.request(host, colorParm).done(function (res) {
                res = res || {};
                if (res.success) {
                    that.setFollowState(false);
                    that.settings.onUnFollow.call(that);
                }
                that.render();
            }).fail(function (){
                console && 
                console.log(url + "接口调用失败");
            });
        },

        checkFollowState: function () {
            var that = this;
            // var url = "//fts.jd.com/follow/vender/batchIsFollow";
            var body = JSON.stringify({
                venderIds: that.settings.venderId,
                sysName: "item.jd.com"
            });
            var time = new Date().getTime()
            var colorParm = {
                appid: 'item-v3',
                functionId: 'pctradesoa_vender_batchIsFollow',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                // ids: skuid
                body: body,
            }

            try{
                colorParm['uuid'] = Tools.getCookieNew("__jda")
            }catch(e){
                colorParm['uuid'] = ''
            }

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            that.request(host, colorParm).done(function (res) {
                res = res || {};
                if (res.success &&
                    res.data &&
                    res.data[that.settings.venderId]) {
                    that.setFollowState(true);
                } else {
                    that.setFollowState(false);
                }

                that.render();
                that.settings.onCheckFollowState.call(that);
            }).fail(function (){
                console && 
                console.log(url + "接口调用失败");
            });
        },

        render: function () {
            var $el = this.settings.$el;
            var state = this.getFollowState();
            $el.toggleClass("followed", state).
                attr({"title": state ? "点击取消收藏" : "点击收藏",
                    "data-follow": '' + state}).html(state ? "<i></i>已收藏": "<i></i>收藏");
        }
    };

    module.exports = FollowShop;
});
