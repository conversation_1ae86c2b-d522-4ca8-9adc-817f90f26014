define('PUBLIC_ROOT/modules/ebook/ebook', function(require, exports, module) {
    var dialog = require('JDF_UI/dialog/1.0.0/dialog');
    var G = require("PUBLIC_ROOT/modules/common/core");

    // function getEbookeCart(cfg) {
    //     $.ajax({
    //         url: '//gw-e.jd.com/shoppingCart/shoppingCart_shoppingCart.action',
    //         data: {
    //             pin: readCookie('pin')
    //         },
    //         dataType: 'jsonp',
    //         cache: true,
    //         success: function (data) {
    //             if(data.code == 0) {
    //                 if(data.result.map.result && data.result.map.result.count) {
    //                     $("#ebook-cart .cw-icon").append('<i class="ci-count" id="ebook-cart-amount">' + data.result.map.result.count + '</i>');
    //                 }
    //             }
    //         }
    //     });
    // }

    function getPrice(cfg) {
        var price = data && data.stock && data.stock.data && data.stock.data.price
        if (cfg.cat[1] == 5276) {
            $('.J-summary-price .p-price span').html('');
            $('.J-summary-price .p-price .price').html('免费');
            return;
        }
        getJDPrice(cfg, price.p);
    }

    function getJDPrice(cfg, p3cnPrice) {
        // 获取京东原始价格
        $.ajax({
            url: '//gw-e.jd.com/forBookCode/forBookCode_getEbookInFoAndOrginPrices4JSONP.action?bookCodes=' + cfg.skuid,
            dataType: 'jsonp',
            scriptCharset: 'utf-8',
            success: function (data) {
                var $pPirce = $('.J-summary-price .p-price');
                // 免费书分类
                if (cfg.cat[1] == 5276) {
                    $pPirce.find('span').html('');
                    $pPirce.find('.price').html('免费');
                } else {
                    var rList = data.result.resultList
                    if (rList.length) {
                        var jdPrice = rList[0].jdPrice;
                        if (p3cnPrice < jdPrice && p3cnPrice > 0) {
                            $('.J-prom-price .p-price .price').html(p3cnPrice);
                            $pPirce.find('.price').html(jdPrice.toFixed(2));
                            $('.J-summary-price .p-price').addClass("del");
                            $('.J-prom-price').show();
                        } else {
                            //京东价
                            $('.J-summary-price .p-price .price').html(jdPrice.toFixed(2));
                        }
                    }
                }
                //男生原创 女生原创 5272,10941 5272,14155
               if(cfg.cat[0] == 5272){
                   if(cfg.cat[1] == 10941 || cfg.cat[1] == 14155){
                       var _ebookPrice = '<em style="color:#e4393c;">¥ </em><span style="color:#e4393c;font-size:16px;">0.05/千字</span>';
                       $('.J-summary-price .p-price').html(_ebookPrice);
                       $('.extra .p-info .p-price').html(_ebookPrice);
                   }

               }
            }
        });
    }

    function addEvents(cfg) {
        $("#btn-onlineread").click(function () {
            openEBookVipDialog(cfg);
        });

        if (!G.wideVersion) {
            $(".download").hover(function () {
                $(this).addClass('download-hover');
            }, function () {
                $(this).removeClass('download-hover');
            });
        }

        $("#InitCartUrl, #InitCartUrl-mini").click(function () {
            if(G.cat[1] == 5276) {
                openFreeEBookDialog(cfg);
                return;
            }

            location.href = cfg.buyurl;
        });

        $("#btn-buynow").click(function () {
            location.href = cfg.buynowlink;
        });

    }

    function openEBookVipDialog(cfg) {
        var noCardtpl= '\
        <div class="bookLayer">\
            <p>很遗憾，您目前没有可用的“畅读VIP”服务</p>\
            <div class="btn-wrap">\
                <a href="//sale.jd.com/act/MypqiIJPYx.html" class="red" target="_blank">立即开通畅读</a>\
                <a class="cancel-btn" href="javascript:;">暂不开通</a>\
            </div>\
        </div>';


        seajs.use('jdf/1.0.0/unit/login/1.0.0/login.js',function(login){
            login({
                modal: true,
                complete: function(result) {
                    var dialog;
                    if (result != null && result.Identity.IsAuthenticated != null && result.Identity.IsAuthenticated) {
                        $.ajax({
                            url: "//cread.jd.com/openread/openRead.action",
                            data: {
                                bookId: cfg.skuid,
                                readType: 0
                            },
                            dataType: "jsonp",
                            success: function (r) {
                                var isVIP = r.code != null && r.code == "0";
                                // var tplStr = isVIP ? haveCardtpl : noCardtpl;

                                if (isVIP) {
                                    location.href = 'https://cread.jd.com/read/startRead.action?bookId='+ cfg.skuid +'&readType=0'
                                } else {
                                    dialog = $('body').dialog({
                                        width: 442,
                                        height: 120,
                                        title: '温馨提示',
                                        type: 'html',
                                        source: noCardtpl,
                                        onReady: function() {
                                            var $bookLayer = $(".bookLayer");
                                            $bookLayer.find(".cancel-btn").click(function () {
                                                dialog.close();
                                            });
                                        }
                                    });
                                }
                            }
                        });
                    }
                }
            });
        });
    }


    function openFreeEBookDialog(cfg) {
        // var isBoughtEBookDialogtpl= '\
        // <div class="bookLayer">\
        //     <p>请使用<b>京东阅读客户端</b>下载阅读</p>\
        //     <div class="btn-wrap">\
        //         <a href="//sale.jd.com/act/W5hugLDc1R.html" class="red" target="_blank">立即安装</a>\
        //         <a href="LEBK:///Bought">启动客户端</a>\
        //     </div>\
        // </div>';

        seajs.use('jdf/1.0.0/unit/login/1.0.0/login.js',function(login){
            login({
                modal: true,
                complete: function(result) {
                    // var dialog;
                    if (result != null && result.Identity.IsAuthenticated != null && result.Identity.IsAuthenticated) {
                        $.ajax({
                            url: '//gw-e.jd.com/downrecord/downrecord_insert.action?ebookId=' + cfg.skuid,
                            dataType: 'jsonp',
                            cache: true,
                            success: function (r) {
                                if(r.code == 1) {
                                    location.href = 'https://cread.jd.com/read/startRead.action?bookId='+ cfg.skuid +'&readType=3';
                                    // dialog = $('body').dialog({
                                    //     width: 442,
                                    //     height: 120,
                                    //     title: '温馨提示',
                                    //     type: 'html',
                                    //     source: isBoughtEBookDialogtpl
                                    // });
                                } else if (r.code == 0) {
                                    alert("系统错误，稍后再试");
                                }
                            }
                        });
                    }
                }
            });
        });
    }


    function getAd() {
        var ads = ['0_0_8249', '0_0_8250'];
        $.ajax({
            url: '//nfa.jd.com/loadFa_toJson.js?aid=' + ads.join('-') + '&ver=20131107',
            dataType: 'script',
            cache: true,
            success: function () {
            }
        });
    }

    function getTxt(cfg) {
        $.ajax({
            url: '//gw-e.jd.com/client.action?functionId=getScanDesc&body={ebookId:' + cfg.skuid + ',sourceType:0}',
            dataType: "jsonp",
            cache: true,
            success: function (data) {
                if(data.resultCode == 0) {
                    $(".download .download-qrcode-tit h3").html(data.map.scanBook.scanTypeStr);
                    $(".download .qr-code p").html(data.map.scanBook.scanDesc);
                }
            }
        });
    }

    function init(cfg) {
        Event.addListener('onStockReady', function (data) {
            getPrice(cfg, data);
        });
        // getEbookeCart();
        addEvents(cfg);
        getAd();
        getTxt(cfg);
    }

    module.exports.__id = 'ebook';
    module.exports.init = init;
});
