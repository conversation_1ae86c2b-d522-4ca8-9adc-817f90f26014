{% import '../../views/maco/user.html' as wow %}

{% include "./address.html" %}

<script>

    seajs.use('MOD_ROOT/address/address', function (Address) {
        Address.init();
    });

    describe("Write ipLoc-djd cookie.", function () {
        it("Location will be [北京 朝阳区 三环到四环之间]", function (done) {

            $('[data-value="1"]').trigger('click');
            $('[data-value="72"]').trigger('click');

            setTimeout(function () {
                $('[data-value="2819"]').trigger('click');

                expect(readCookie('ipLoc-djd')).toEqual("1-72-2819-0");

                done();
            }, 2000);
        });
    });
</script>
