@import './__sprite';
.city{
    margin-bottom:15px;
    .city-wrap{
        position: relative;
        width: 150px;
        height: 32px;
        border: 1px solid #ccc;
        *z-index: 1;
        .current-city{
            font-size: 14px;
            font-family: microsoft yahei;
            padding-left: 10px;
            width: 120px;
            float: left;
            *zoom: 1;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .sprite-location{
            @include sprite-location;
            float: right;
            margin: 8px 10px 0 0;
        }
        .city-layer{
            position: absolute;
            left: -1px;
            top: 32px;
            z-index: 4;
            width: 458px;
            height: 235px;
            border: 1px solid #ccc;
            background: #fff;
            font-size: 14px;
            display: none;
            .city-tit{
                background: #f3f3f3;
                padding-bottom: 33px;
                border-bottom: 1px solid #ccc;
                ul{
                    margin-left: -1px;
                }
                li{
                    float: left;
                    padding: 0 25px;
                }
                .current{
                    background: #fff;
                    height: 34px;
                    border: 1px solid #ccc;
                    border-top: none;
                    border-bottom: none;
                    a{
                        color: #e4393c;
                    }
                }
            }
            .city-con{
                height: 195px;
                padding-top: 6px;
                overflow-y: auto;
                li{
                    padding-left: 25px;
                    float: left;
                    width: 85px;
                }
            }
        }
    }
    .hover{
        .city-layer{
            display: block;
        }
    }
}
.store{
    cursor: pointer;
    .sprite-map{
        display: inline-block;
        @include sprite-map;
        vertical-align: -2px;
        margin-left: 2px;
    }
    &:hover{
        color: #e4393c;
        .sprite-map{
            display: inline-block;
            @include sprite-map-hover;
            vertical-align: -2px;
            margin-left: 2px;
        }
    }
}
