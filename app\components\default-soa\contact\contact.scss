@import '../common/lib';
@import '__sprite.scss';
.contact {
    // padding: 13px 0 0;
    padding: 13px 0 9px;
    position: relative;
    z-index: 4;
    .item {
        float: left;
        *display: inline;
    }
    .name {
        float: left;
        _float: none;
        margin-right: 10px;
        .u-jd {
            display: inline-block;
            height: 14px;
            padding: 1px;
            line-height: 14px;
            font-family: arial, simsun;
            color: #fff;
            background: #e4393c;
            margin-left: 6px;
            cursor: pointer;
            span{
                display: inline-block;
                padding: 0 1px;
                background: #fff;
                color: #e4393c;
                font-weight: bold;
                margin-right: 2px;
            }
        }
        img{
            margin-right: 6px;
            vertical-align: -4px;
        }
    }

    //.sprite-diamond {
    //    @include inline-block;
    //    @include sprite-diamond;
    //    vertical-align: middle;
    //}

    .jimi, .im, .follow, .phone {
        cursor: pointer;
        float: left;
        margin-right: 10px;
        white-space: nowrap;

        i {
            @include inline-block;
            vertical-align: middle;
            margin-right: 5px;
        }
        &:hover {
            color: #E3393C;
        }
    }

    .jimi {
        .sprite-jimi {
            @include sprite-jimi;
            margin-top: -3px;
        }
        &.customer-service {
            .sprite-jimi {
                @include sprite-communication;
                margin-top: 0;
                vertical-align: text-top;
            }
        }
    }

    .im {
        .sprite-im {
            width: 16px;
            height: 20px;
            @include inline-block;
        }

        &.gys-im {
            .sprite-im {
                background: url(i/sprite-gys-im.gif) no-repeat 0 0;
            }
        }

        &.pop-im {
            .sprite-im {
                background: url(i/sprite-pop-im.gif) no-repeat 0 0;
            }
        }

        &.newjd-im {
            .sprite-im {
                background: url(i/sprite-jd-im.gif) no-repeat 0 0;
            }
        }

        &.jd-im-offline {
            .sprite-im {
                @include sprite-im-offline;
            }
        }
        &.customer-service {
            .sprite-im {
                @include sprite-communication;
                vertical-align: text-top;
            }
        }
    }

    .phone {
        .sprite-phone {
            @include sprite-phone;
        }
    }

    .J-doctor-item{
        .J-doctor-icon{
            background: url(//m.360buyimg.com/cc/jfs/t1/19447/4/11258/1664/63fed73cF844ea051/27eda18d6c7e9844.png);
            width: 16px;
            vertical-align: text-top;
            display: inline-block;
            height: 16px;
            background-size: 100%;
            margin-right: 5px;
        }
    }
    
    .follow {
        .sprite-follow {
            position: relative;
            top: -1px;
            @include sprite-follow;
        }
    }

    .weichat-sp {
        .EDropdown {
            //*float:left;
            //*width: 58px;
            //*border: 1px solid red;
        }
        margin-top: -3px;
        .hover .head {
            padding: 0 4px;
        }
        .head {
            padding: 1px 5px;

        }
        .content {
            padding: 10px;
            width: 100px;
            margin-left: -64px;
        }
        i {
            @include inline-block;
            vertical-align: middle;
        }
        .sprite-qr {
            @include sprite-qr;
        }
        .text {
            @include inline-block;
        }
    }
}
.nav-im {
    .im {
        cursor: pointer;
        .sprite-im {
            width: 16px;
            height: 20px;
            vertical-align: middle;
            margin-right:5px;
            @include inline-block;
        }

        &.gys-im {
            .sprite-im {
                background: url(i/sprite-gys-im.gif) no-repeat 0 0;
            }
        }

        &.pop-im {
            .sprite-im {
                background: url(i/sprite-pop-im.gif) no-repeat 0 0;
            }
        }

        &.newjd-im {
            .sprite-im {
                background: url(i/sprite-jd-im.gif) no-repeat 0 0;
            }
        }

        &.jd-im-offline {
            .sprite-im {
                @include sprite-im-offline;
            }
        }
        &.customer-service {
            .sprite-im {
                @include sprite-communication;
                vertical-align: text-top;
            }
        }
    }
}
.contact{
    .hover {
        z-index: 5;
    }
}
.sprite-im-offline {
    @include sprite-im-offline;
}



.phone {
    cursor: default;
}
.contact-layer{
    .content{
        display:none;
        .hover & {
            left: auto;
            right:0;
            display: block;
        }
        top: 27px;
        width: 215px;
        border: 1px solid #ccc;
    }
}

.narrow-contact-layer {
    .content {
        width: 152px;
    }
}
.pop-score {
    margin-right: 10px;
    position: relative;
    top:-1px;

    .EDropdown {
        .head {
            z-index: 6;
        }
        .content {
            width: 210px;
            z-index: 5;
        }
    }

    .pop-head {
        .name {
            float: left;
            margin-right: 5px;
            max-width:120px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .star {
            float: left;
            .heart-white,.heart-up,.heart-down,.heart-eq {
                @include inline-block;
                height:16px;

                background:url(i/grade-20170307.png) 0 0 no-repeat;
                _background:url(i/grade-ie6.png) 0 0 no-repeat;
            }
            .heart-white {
                position:relative;
                top:2px;
                float:left;
                overflow: hidden;
                width: 74px;
                background-position: 0 0;
            }
            .heart-up,.heart-eq {
                position:absolute;
                width:70px;
                background-position:0 -23px;
            }
            .heart-down {
                position:absolute;
                width:70px;
                background-position:0 -49px;
            }
            .evaluate-grade {
                vertical-align: -1px;
                color: #999;
                .sprite-down {
                    display: inline-block;
                    margin-left: 3px;
                    @include sprite-down;
                }
                .sprite-up {
                    display: inline-block;
                    margin-left: 3px;
                    @include sprite-up;
                }
                .up,eq{
                    color: #e3393c;
                }
                .down{
                    color: #009900;
                }
            }
            .h10 {
                left:0;
            }
            .h9 {
                left:-10px;
            }
            .h8 {
                left:-20px;
            }
            .h7 {
                left:-30px;
            }
            .h6 {
                left:-40px;
            }
            .h5 {
                left:-50px;
            }
            .h4 {
                left:-60px;
            }
        }
    }

    .score-body {
        padding: 6px 9px;
    }
}

.pop-score-detail {
    padding: 12px 15px 8px;
    .score-title {
        margin-bottom: 8px;
        .col1 {
            float: left;
            font-weight: bold;
        }
        .col2 {
            float: right;
            color: #999;
        }
    }
    .score-info {
        margin-bottom: 6px;
        display: block;
        margin-bottom: 6px;
        .score-desc, .score-detail {
            float: left;
        }
        .score-trend, .score-change {
            float: right;
        }
        .score-desc {
            color: #999;
            a:hover & {
                .score-desc {
                    color: #C81623;
                }
            }
        }
        .score-trend {
            margin-left: 5px;
        }
    }
}
.score-trend {
    .sprite-down {
        @include sprite-down;
    }
    .sprite-up {
        @include sprite-up;
    }
    .sprite-middle {
        @include sprite-middle;
    }

    i {
        @include inline-block;
    }
}
.pop-shop-im{
    border-top: 1px dotted #e6e6e6;
    margin: 0 15px;
    padding: 10px 0;
    @include clearfix;
    .J-im-btn,.J-jimi-btn{
        padding: 2px 0;
    }
    .phone-num{
        padding-top: 4px;
        white-space: nowrap;
    }
    .sprite-telephone{
        display: inline-block;
        @include sprite-telephone;
        margin-right: 5px;
        vertical-align: -3px;
    }
}
.pop-shop-qr-code{
    border-top: 1px dotted #e6e6e6;
    margin: 0 15px;
    padding: 14px 0 10px;

    .qr-code{
        //float: left;
        text-align: center;
        img{
            //padding: 3px;
            border: 1px solid #cecece;
        }
        p{
            padding-top: 3px;
        }
    }
    .J-wd-qrcode img {
        width: 118px;
        height: 118px;
    }
    .J-m-wrap {
        //padding: 7px;
        margin: 0 auto;
        img {
            width: 118px;
            height: 118px;
            border: 1px solid #cecece;
        }
    }
}
.pop-shop-detail {
    border-top: 1px dotted #e6e6e6;
    margin: 0 15px;
    padding: 10px 0;
    @include clearfix;
    .item {
        margin-bottom: 5px;
        line-height: 20px;
        .text {
            color: #999;
        }
        .shop-name {
            color: #999;
        }
    }
}
.contact .followed {
    i {
        width:13px;
        height:10px;
        background: url(i/followed.png) 0 0 no-repeat;
    }
    span{
        color: #999;
    }
}
.contact .btns {
    background: #f6f6f6;
    font-size: 0;
    text-align: center;
    .separator{
        margin: 0 8px;
        background: #d1d1d1;
        width: 1px;
        height: 14px;
        overflow: hidden;
        display: inline-block;
        vertical-align: middle;
    }

    .btn-def {
        height: 34px;
        line-height: 34px;
        padding: 0;
        margin-right: 8px;
        font-size: 12px;
        _padding-top: 10px;
        _height: 24px;
        border: none;
        background: #f6f6f6;
        &:hover{
            color: #e3393c
        }
    }

    i {
        @include inline-block;
        vertical-align: -2px;
    }

    .sprite-enter {
        margin-right: 5px;
        @include sprite-enter;
    }
    .sprite-follow {
        margin-right: 5px;
        @include sprite-follow;
    }

    .follow-shop {
        margin-right: 0;
    }
}
/*二手*/
.ershou{
    .contact{
        .name{
            .u-jd{
                background: $baseColorErshou;
            }
        }
    }
}
/* 好店hover背景图 */
.goodshop {
    position: relative;
    cursor: pointer;
}

.goodshop-bg {
    width: 256px;
    height: 249px;
    position: absolute;
    background: url(//newbuz.360buyimg.com/pcItem/default/goodshop-hover.png);
    display: none;
    left: 50%;
    top: 20px;
    margin-left: -128px;
    z-index: 10;
    cursor: default;
}
.goodshop.EDropdown .content{
    background-color: transparent;
}

.root61 .goodshop.EDropdown .content {
    left: 50%;
    margin-left: -130px;
}