define('PUBLIC_ROOT/modules/address/stock', function(require, exports, module) {
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools')
    var ABTest = require('PUBLIC_ROOT/modules/common/tools/abtest')
    var G = require('PUBLIC_ROOT/modules/common/core')
    var addToCartBtn = require('PUBLIC_ROOT/modules/buybtn/buybtn').addToCartBtn;
    var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event
    var Login = require('JDF_UNIT/login/1.0.0/login')
    var Conf = require('PUBLIC_ROOT/conf');


    require('PUBLIC_ROOT/modules/summary/summary')

    function Stock(param, onSuccess, onError, onlyData) {
        this.areas = Tools.getAreaId().areaIds
        var getNum = function() {
            var $buyNum = $('#buy-num')
            return $buyNum.length ? $buyNum.val() : 1
        }
        var defaults = {
            skuId: pageConfig.product.skuid,
            area: this.areas.join('_'),
            venderId: pageConfig.product.venderId || 0,
            cat: pageConfig.product.cat.join(','),
            buyNum: getNum(),
            choseSuitSkuIds: pageConfig.product.suitSkuids || '',
            extraParam: pageConfig.product.isHeYue
                ? '{"originid":"1","heYueJi":"1"}'
                : '{"originid":"1"}',
            ch: 1,
            //fqsp: G.onAttr('fqsp') ? 1: 0,
            fqsp: (G.onAttr('fqsp-1') && 1) || (G.onAttr('fqsp-2') && 2) || (G.onAttr('fqsp-3') && 3) || 0,
            pduid: Tools.getUUID(),
            pdpin: readCookie('pin') || ''
        }
        this.param = $.extend(defaults, param)
        this.onSuccess = onSuccess || function() {}
        this.onError = onError || function() {}
        // 只获取数据，不进行 Stock.set 相关操作
        this.onlyData = onlyData || false

        this.init()
    }

    Stock.prototype = {
        init: function() {
            this.bindEvent()
            this.get()
        },
        bindEvent: function() {
            //var _this = this;
            //Event.addListener('onAreaChange', function () {
            //    _this.setLDP();
            //});
        },
        get: function() {
            var _this = this
            //window.fetchJSON_STOCK = function() {};
            //将用户点选的详细地址传给cd.jd.com王琨
            this.param.detailedAdd= readCookie('detailedAdd');
            if(this.param.detailedAdd){
                this.param.area=readCookie('detailedAdd_areaid').split('-').join('_');
            }
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            $.ajax({
                url: host + '/stock?' +
                    decodeURIComponent($.param(this.param)),
                dataType: 'jsonp',
                data: {
                    appid: 'item-v3',
                    functionId: "pc_stock" // 阿波罗那边反馈已下线
                },
                cache: true,
                timeout: 6000,
                scriptCharset: 'gbk',
                //jsonpCallback: 'fetchJSON_STOCK',
                error: function(err) {
                    _this.error()
                    _this.onError.call(_this, err)
                },
                success: $.proxy(this.handleData, this)
            })
        },
        handleData: function(r) {
            var state = r.stock.StockState

            pageConfig.product._area = r.stock.area

            // 全局标识
            pageConfig.product.unSupportedArea = r.stock.code === 2
            pageConfig.product.havestock =
                !pageConfig.product.isOver &&
                state != -1 &&
                state != 34 &&
                state != 0



            //定期购二期
            //新增显示/隐藏条件
            //秒杀进行中(下发字段存在)时，屏蔽定期购入口（秒杀预告期正常展示）。
            //独立秒杀(specialAttrs.isKO)，即商品有如下特殊属性，也屏蔽定期购入口。
            //闪购进行中（fsEndTime fsEndOffset）时，屏蔽定期购入口（闪购预告期正常展示）。

            var cfg = pageConfig.product;
            var extraCondition = function(){


                //秒杀进行中
                if(cfg.koEndOffset){
                    return true;
                }

                //独立秒杀
                if(cfg.specialAttrs && (cfg.specialAttrs instanceof Array) && cfg.specialAttrs.join(',').indexOf('isKO') != -1){
                    return true;
                }

                //闪购进行中
                if(cfg.fsEndOffset){
                    return true;
                }

                return false;

            };


            //定期购二期
            //通过异步查询库存接口返回数据进行判断是否显示
            //对于非生鲜的普通定期购商品，商品无货 (StockState=34)、限购时(code=2)，屏蔽定期购入口；
            //对于生鲜定期购商品，商品无货、限购、不支持配送时（同限购），屏蔽定期购入口。
            if(r.stock){
                if (r.stock.StockState == 34 || r.stock.code==2 || extraCondition()) {
                    $('#choose-period-buy').hide();
                }else {
                    if(PeriodicalBuy){
                        PeriodicalBuy.init(cfg);
                    }
                }
            }




            // 海外售，有货但是不支持配送加入购物车置灰
            if (r.stock.supportHKMOShip === false) {
                pageConfig.product.havestock = false
            }

            // 接口返回错误
            if (!r || !r.stock) {
                this.error()
                this.onError.call(this)
            } else {
                if (!this.onlyData) {
                    this.set(r)
                }
                this.onSuccess.call(this, r)
            }



            /*
             7天无理由提示
             温馨提示
             */

            var _pid = Tools.getAreaId().areaIds[0];
            var unSupportedArea = (_pid === 52993 || _pid === 53283 || _pid === 32);
            if(unSupportedArea) return;
            var $tipsDiv = $('#summary-tips .tips-list');
            if(r.stock && r.stock.promiseMark){


                if($tipsDiv.length != 0 && r.stock.promiseMark != ''){


                    //_txt += '<div id="summary-tips" class="summary-tips" clstag="shangpin|keycount|product|wenxintishi_2" style="">';
                    //_txt += '    <div class="dt">温馨提示</div>';
                    //_txt += '    <div class="dd">';
                    //_txt += '       <ol class="tips-list clearfix"><li>'+ r.stock.promiseMark +'</li></ol>';
                    //_txt += '   </div>';
                    //_txt += '</div>';


                    var _txt = '<li class="tips7days">'+ r.stock.promiseMark +'</li>';


                    setTimeout(function(){
                        if( $('.tips7days').length == 0){
                            $tipsDiv.prepend(_txt);//插在最前面
                        }else{
                            $('.tips7days').html(_txt);
                        }
                        $('#summary-tips').show();
                    },500);

                }
            }else{
                $('.tips7days').remove();
            }

            if(!unSupportedArea){

                $tipsDiv.find('.local-txt').each(function(){
                    var _this = $(this);
                    if(_this.html().indexOf('不支持7天无理由退货') != -1){
                        _this.remove();
                    }
                });
            }







            /*
             预售接金融券
           */
            var _venderId = cfg.venderId;
            if(typeof _venderId == 'undefined'){
                _venderId = '';
            }
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            var url = host + '/new/cdpPromotionQuery?skuId='+cfg.skuid+'&venderId='+ _venderId +'&source=PC&jdPin='
            $.ajax({
                url: url,
                dataType: 'jsonp',
                data: {
                    appid: 'item-v3',
                    functionId: "pc_new_cdpPromotionQuery"
                },
                success: function(r){
                    if (!r || !r.couponDesc || !r.jumpUrl) return;
                    var _html = [];
                    _html.push('<div id="summary-presale-wellfare" class="li" clstag="shangpin|keycount|product|jinrongquan_ys">');
                    _html.push('<div class="dt">预售福利</div>');
                    _html.push('<div class="dd"><a class="J-open-tb" href="'+ r.jumpUrl +'" target="_blank"><span class="quan-item" title="'+ r.couponDesc+'"><s></s><b></b><span class="text">'+r.couponDesc+'</span></span><span class="more-btn">更多&gt;&gt;</span></a></div>');
                    _html.push('</div>');
                    _html = _html.join('');

                    setTimeout(function(){

                        if($('#summary-presale-wellfare').length > 0){
                            $('#summary-presale-wellfare').remove();
                        }
                        $('.summary.p-choose-wrap').prepend(_html);

                    },200);


                }
            });








        },
        set: function(r) {
            this.setStockInfo(r.stock)
            this.setPromiseIcon(r.stock)
            //this.setRelativeAct(r.stock.ir);
            this.setDefaultAreaName(r.stock.area)
            this.setSupportIcons(r.stock)
            this.setDJDAreaSku(r.stock)
            this.setLDP(r.stock)
            // PLUS 拉新券(PLUS礼包)
            if (Conf.get('GLOBAL.PLUS.giftPackageSignal')) {
                this.setOpenPlusGift(r.stock);
            }
            //this.resetCookie(r.stock.area)
        },
        // 库存接口失败异常
        error: function() {
            if (typeof console !== 'undefined') {
                console.error('Stock service maybe error or timeout.')
            }
            this.setDefaultAreaName()
        },
        resetCookie: function(a) {
            // 北京朝阳区三环以内
            if (typeof a === 'undefined' || !a.countyName || !a.cityName) {
                var domain='jd.com';
                try{
                    if (window.pageConfig &&
                        typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
                        domain = pageConfig.FN_getDomainNew();
                    }else if (window.pageConfig &&
                        typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
                        domain = pageConfig.FN_getDomain();
                    }else {
                        domain = document.domain;
                    }
                }catch(e){
                    domain = document.domain;
                }

                createCookie('ipLocation', '北京', 30, '/;domain=' + domain)
                createCookie('areaId', 1, 10, '/;domain=' + domain)
                createCookie(
                    'ipLoc-djd',
                    '1-72-55653-0',
                    30,
                    '/;domain=' + domain
                )

                new Stock(
                    {
                        area: '1_72_55653_0'
                    },
                    function(r) {}
                )
            }
        },
        // 设置默认的 cookie 地区id对应的中文名称
        setDefaultAreaName: function(area) {
            var text = ''
            var $el = $('#stock-address .text')

            if (area) {
                text =
                    area.provinceName +
                    area.cityName +
                    area.countyName +
                    area.townName
                pageConfig._CURR_AREA = area
            } else {
                // 库存加载失败或者异常时：按 cookie id 值取对应的一、二级中文名称
                var aids = Tools.getAreaIds()
                var $pTab = $('.J-address-tab [data-tab="item"]').eq(0)
                var pName = $pTab.find('[data-value="' + aids[0] + '"]').text()
                // 通过当前省份id 查找 cookie 中对应的市级名称
                //var cName = getCityInfoByPid(aids[0], aids[1]).name;
                var cName = unescape(readCookie('ipLocation'))
                pageConfig._CURR_AREA = {
                    provinceName: pName,
                    cityName: cName,
                    countyName: ''
                }
                text = pName + cName
            }
            $el.text(text)
        },
        setStockInfo: function(stock) {
            var $stockPrompt = $('#store-prompt')
            var $service = $('#summary-service')
            var $supply = $('#summary-supply')
            var $dcashDesc = $('.J-dcashDesc')
            var stockDesc = ''
            var serviceInfo = ''

            if (!stock) {
                return false
            }

            // 「由 京东 发货并提供售后服务」
            // if (stock.serviceInfo) {
            //     serviceInfo += stock.serviceInfo
            // }
            // 「23:00前完成下单,预计<b>03月03日(周四)</b>送达」
            if (stock.promiseResult) {
                serviceInfo += stock.promiseResult
            }
            $service.html(serviceInfo)

            if (serviceInfo) {
                $supply.show()
            } else {
                $supply.hide()
            }

            // 家装 #summary-stock
            var $stock = $('#summary-stock')
            // 家装服务
            if (serviceInfo && $stock.length) {
                $stock.show()
            } else {
                $stock.hide()
            }

            // 「<strong>有货</strong>，仅剩1件」
            if (stock.stockDesc) {
                stockDesc += stock.stockDesc
            }
            // 预订状态
            if (stock.Drd && stock.StockState === 36 && !pageConfig.product.isYuShou) { // 不是预售
                stockDesc = '<strong>预订，</strong>此商品为预订商品，下单后在' + stock.Drd
            }
            if (stock.spIconDesc) {
                stockDesc += stock.spIconDesc
            }
            $stockPrompt.html(stockDesc)

            // 「pop 免运费」
            if (stock.dcashDesc) {
                $dcashDesc.html(stock.dcashDesc)
            }

            // 山姆运费提示
            if (stock.isSam) {
                $dcashDesc.html(stock.samProductFreight)
            }
            // 沃尔玛运费提示
            if (stock.isWalMar) {
                $dcashDesc.html(stock.walMarProductFreight)
            }
        },
        setPromiseIcon: function(stock) {
            var $promise = $('.J-promise-icon')
            var result = []
            var eir = false
            var ir = false
            var tpl =
                '\
            <a target="_blank" title="${item.iconTip}" href="${item.helpLink}" class="${item.iconCode}" clstag="shangpin|keycount|product|promisefw_${pageConfig.product.pType}">\
                ${item.showName}\
            </a>'

            var hasNSNGIcon =
                G.onAttr('isNSNGgoods-3') || /nsng/.test(location.href)
            var nsngIcon = {
                iconTip: '针对不同产品，小京鱼可提供语音控制、远程管理、场景联动等不同智能服务',
                iconCode: 'nsng',
                showName: '搭载小京鱼助手',
                helpLink: '//smarthome.jd.com',
                picUrl: '//static.360buyimg.com/item/assets/picon/zhineng.png'
            }

            function getResult(items) {
                var result = []
                for (var i = 0; i < items.length; i++) {
                    var item = items[i]
                    if (item.iconType !== 5) {
                        result.push(item)
                    }
                }

                return result
            }

            if (stock.eir && stock.eir.length) {
                result = result.concat(stock.eir)
                eir = true
            }

            if (hasNSNGIcon) {
                result.push(nsngIcon)
            }

            if (stock.ir && stock.ir.length) {
                var res = getResult(stock.ir)
                result = result.concat(res)
                ir = true
            }

            // 合约机和 promiseicon 互斥
            if ((eir || ir) && !pageConfig.product.isHeYue) {
                $promise.show()
            } else {
                $promise.hide()
            }

            if (result.length > 4) {
                $promise.addClass('promise-icon-more')
                $promise.removeAttr('data-disable')
                $promise.EDropdown()
                if ($promise.find('.icon-list i').length === 0) {
                    $promise.find('.icon-list').append('<i></i>')
                }
            } else {
                $promise.attr('data-disable', 'disable')
                $promise.removeClass('promise-icon-more')
            }
            var arr = []
            $.each(result, function(i, n) {
                arr.push(tpl.process({ item: n }))
            })

            var $line1 = $('<div class="line1 clearfix"></div>')
            var $line2 = $('<div class="line2 clearfix"></div>')

            $.each(arr, function(i, n) {
                var $n = $(n)

                if (i < 4) {
                    $line1.append($n)
                } else {
                    $line2.append($n)
                }
            })

            $line1.find('a:last').addClass('noborder')
            $line2.find('a:last').addClass('noborder')

            $promise.find('ul').html('').append($line1, $line2)
        },
        setRelativeAct: function(list) {
            var result = []
            var tpl = '<a href="{0}" target="_blank">{1} &gt;&gt;</a>'

            if (!list || !list.length) {
                return false
            }

            for (var i = 0; i < list.length; i++) {
                if (list[i].iconType == 5 && result.length < 2) {
                    result.push(list[i])
                }
            }

            if (result.length) {
                var html = tpl.format(result[0].helpLink, result[0].showName)
                pageConfig.__localTip.set(2, html)
            } else {
                pageConfig.__localTip.del(2)
            }
        },
        setSupportIcons: function(stock) {
            var $support = $('#summary-support')

            if (!stock || !stock.support || !stock.support.length) {
                $support.hide()
                return false
            }

            pageConfig.__supportABTest = new ABTest(Tools.getUUID(), 0.5)
            var isHitVersion = pageConfig.__supportABTest.isHitVersion() === 'A'

            for (var i = 0; i < stock.support.length; i++) {
                var sup = stock.support[i]
                if (sup.id === 'baina') {
                    if (!isHitVersion) {
                        stock.support.splice(i, 1)
                        break
                    }
                }
            }
            if (/debug=zz/.test(location.href)) {
                stock.support.push({
                    id: 'zengzhi',
                    showName: '增值服务'
                })
            }
            var tpl =
                '\
            {for item in support}\
            <li id="support-${item.id}" clstag="shangpin|keycount|product|zhichi_${item.id}_${cat2}">\
                <a {if item.helpLink} target="_blank" href="${item.helpLink}" {else} href="#none" {/if} \
                    data-title="${item.iconTip}">\
                <i class="sprite-${item.id}"></i>\
                <span>${item.showName}</span>\
                </a>\
            </li>\
            {/for}'

            if (stock.support.length) {
                $support.show()
            } else {
                $support.hide()
            }

            $support.find('.choose-support').html(
                tpl.process({
                    support: stock.support,
                    cat2: pageConfig.product.cat[2]
                })
            )

            this.showTip($support)
            this.bindDialog()
        },
        showTip: function($el) {
            $el.find('[data-title]').each(function() {
                if ($(this).data('title')) {
                    $(this).ETooltips({
                        pos: 'bottom',
                        zIndex: 10,
                        width: 200,
                        defaultTitleAttr: 'data-title'
                    })
                }
            })
        },
        setDJDAreaSku: function(stock) {
            if (!stock || !stock.realSkuId) return false

            var realSkuId = stock.realSkuId
            // 大家电分区 sku
            if (realSkuId != pageConfig.product.skuid) {
                var jt = [
                    G.isPop ? '0' : '1',
                    G.isPOPSku(realSkuId) ? '0' : '1'
                ].join('')
                var param = '?jt=' + jt
                if (location.search) {
                    if (/jt=\d+/.test(location.search)) {
                        param = location.search.replace(/jt=\d+/, 'jt=' + jt)
                    } else {
                        param = location.search + '&jt=' + jt
                    }
                }
                if (/debug=disableJump/.test(location.href)) {
                    return false
                }
                var html =
                '\
                <p style="padding: 40px 30px;font-size: 14px;font-weight: 600;color: #999;">该商品在当前区域暂不支持配送或无货，是否切换到相似商品。</p>\
                <div class="btn" style="text-align: center;">\
                    <a href="#none" onclick="$.closeDialog()" style="height: 46px;line-height: 46px;font-weight: 700;padding: 0 26px;font-size: 18px;background-color: #df3033;color: #fff;display: inline-block;">取消</a>\
                    <a href='+'//item.jd.com/' + realSkuId + '.html' + param+' style="height: 46px;font-weight: 700;line-height: 46px;padding: 0 26px;font-size: 18px;background-color: #85C363;color: #fff;display: inline-block;">切换相似商品</a>\
                </div>'  

                $('body').dialog({
                    width: 392,
                    title: '',
                    height: 200,
                    type: 'text',
                    maskClose: true,
                    source: html,
                    onReady: function() {
                    }
                })
               // window.location = '//item.jd.com/' + realSkuId + '.html' + param

            }
        },
        // 落地配
        setLDP: function(stock) {
            var $ldp = $('#choose-luodipei')

            if (/debug=ldp/.test(location.href)) {
                stock.vsc = [
                    {
                        desc: '送货服务：电梯住户或7层以下步梯住户可享受此服务，7层以上步梯住户则需按配送公司规定加收费用。（瓷砖地板类，仅电梯住户或1层步梯住户可享）。\n安装服务：此类商品需专人安装，签收后请联系商家确认上门安装时间。\n如商家未履行服务，消费者可获200元/单的赔付。',
                        id: 1103,
                        url: '//help.jd.com/Vender/question-1043.html',
                        seq: 1,
                        name: '送货上门安装',
                        charge: 2
                    },
                    {
                        desc: '送货服务：电梯住户或7层以下步梯住户可享受此服务，7层以上步梯住户则需按配送公司规定加收费用。（瓷砖地板类，仅电梯住户或1层步梯住户可享）。\n如商家未履行服务，消费者可获200元/单的赔付。',
                        id: 1102,
                        url: '//help.jd.com/Vender/question-1043.html',
                        seq: 2,
                        name: '送货上门',
                        charge: 1
                    },
                    {
                        desc: '商品送至您下单地址所在地级市后，配送公司会通知您物流点地址，您需自行前往物流点提货，自行搬运货物并安装。\n如商家未履行服务，消费者可获200元/单的赔付。',
                        id: 1101,
                        url: '//help.jd.com/Vender/question-1043.html',
                        seq: 3,
                        name: '市区站点自提',
                        charge: 0
                    }
                ]
            }

            if (!$ldp.length || !stock || !stock.vsc || !stock.vsc.length) {
                $ldp.hide()
                return false
            }
            $ldp.show()

            var template =
                '\
            {for item in vsc}\
            <div class="item {if Number(item_index)==0} selected{/if}">\
                <b></b>\
                <a href="#none" data-id="${item.id}">${item.name} ￥${item.charge}</a>\
                <script type="text/html">${item.desc} {if item.url }<a class="hl_blue" href="${item.url}" target="_blank">详情 &raquo;</a>{/if}</script>\
            </div>\
            {/for}'

            $ldp.find('.dd').html(template.process(stock))

            $ldp.find('.item').each(function() {
                var $this = $(this)
                var html = $this.find('script').html()

                $this.ETooltips({
                    autoHide: true,
                    close: false,
                    content: html,
                    width: 300,
                    pos: 'bottom',
                    zIndex: 10
                })
            })

            function setLink(id) {
                id = id || $ldp.find('.selected a').attr('data-id')
                var href = addToCartBtn.$el.attr('href')
                var result = ''

                if (href && href != '#none') {
                    if (/did=/.test(href)) {
                        result = href.replace(/did=\d+/, 'did=' + id)
                    } else {
                        result = href + '&did=' + id
                    }

                    addToCartBtn.enabled(result)
                }

                Event.fire({
                    type: 'onLDPSelected',
                    did: id
                })
            }
            function setStyle($ele) {
                $ldp.find('.item').removeClass('selected')
                $ele.parent().addClass('selected')
            }
            function handleClick(e) {
                var $this = $(e.target)
                var id = $this.data('id')
                setStyle($this)
                setLink(id)
            }

            $ldp.delegate('.item a', 'click', handleClick)
            setLink($ldp.find('.selected a').attr('data-id'))

            Event.fire({
                type: 'onLDPSelected',
                did: stock.vsc[0].id
            })
        },
        // 支持 套餐、增值服务 弹出层
        bindDialog: function() {
            this.suitDialog()
            this.zzDialog()
        },
        suitDialog: function() {
            var $tcbg = $('#support-tcbg')
            var aids = this.areas

            $tcbg.bind('click', function() {
                var url = '//ctc.jd.com/popupDialog.action?showSp=1|2&'

                var resUrl =
                    url +
                    $.param({
                        skuId: pageConfig.product.skuid,
                        provinceId: aids[0],
                        cityId: aids[1],
                        popId: pageConfig.product.venderId,
                        r: Math.random()
                    })

                pageConfig.bTypeIframe = $('body').dialog({
                    type: 'iframe',
                    width: 710,
                    height: 610,
                    title: '套餐变更',
                    autoIframe: false,
                    iframeTimestamp: false,
                    source: resUrl,
                    onReady: function() {
                        var _w = $(this.el).width()
                        $(this.el).addClass('popup-phone-service')
                        $(this.content).width(_w)
                    }
                })
            })
        },
        zzDialog: function() {
            var $zz = $('#support-zengzhi')
            $zz.bind('click', function() {
                var url = '//scp.jd.com/settlement.action?'

                var resUrl =
                    url +
                    $.param({
                        skuId: pageConfig.product.skuid,
                        pcount: $('#buy-num').val(),
                        venderId: pageConfig.product.venderId,
                        r: Math.random()
                    })

                var html =
                    '<div class="zengzhi-layer" style="width:630px;">\
                    <h3><i class="icon-zengzhi"></i>阿凡提主题馆“增值交易”模式说明</h3>\
                    <p class="zengzhi-intr">选择“增值交易”模式的客户，可以参与商家指定的相关交易服务。购买后请在个人中心左侧“特色服务”的“黄金|收藏服务”中查看交易凭证，凭交易凭证联系商家完成后续服务。</p>\
                    <div class="zengzhi-info">\
                        请注意：\
                        <ol>\
                            <li>1. 选择“增值交易”模式的客户，系统默认为不发货，如您希望提取货物，请您联系商家，凭交易凭证办理。</li>\
                            <li>2. 选择“增值交易”模式的客户，须为中华人民共和国公民，持有身份证、护照等有效身份证件。须年满18岁，为完全民事行为能力人。</li>\
                            <li>3. “增值交易”模式中所含服务包括但不限于：指定机构代理办理产品封装、交易托管、交易入库、交易提货、交易结算、二手转让等服务形式。该服务最终解释权归阿凡提主题馆所有。</li>\
                            <li>4. “增值交易”模式中所含服务对应的品种设立、交易托管、交易申购、交易流通等信息，均以国际版阿凡提主体文化交易平台对外公告为准。</li>\
                            <li>\
                                5. “增值交易”模式后续服务操作流程示意如下：\
                                <div class="process">\
                                    <div class="process-item">\
                                        客户在京东个人中心<span>【黄金|收藏服务】</span>张获取交易凭证\
                                    </div>\
                                    <i class="arrow"></i>\
                                    <div class="process-item">\
                                        客户通过微信关注<span>【国际版阿凡提主体文化交易平台】</span>\
                                    </div>\
                                    <i class="arrow"></i>\
                                    <div class="process-item">\
                                        在<span>【国际版阿凡提主体文化交易平台】</span>的<span>【客户服务】</span>中<span>【京东客户登记】</span>按系统指引完成后续操作\
                                    </div>\
                                </div>\
                            </li>\
                        </ol>\
                        <a clstag="shangpin|keycount|product|button_ZengZhiJiaoYi" target="_blank" href="' +
                    resUrl +
                    '" class="btn-confirm">增值交易</a>\
                    </div>\
                </div>'

                $('body').dialog({
                    type: 'html',
                    width: 690,
                    height: 600,
                    title: '增值交易',
                    autoIframe: false,
                    iframeTimestamp: false,
                    source: html,
                    onReady: function() {}
                })
            })
        },

        /**
         * PLUS 拉新券需求(PLUS礼包) 产品：吴超 2018.12.26
         * 需求文档：https://cf.jd.com/pages/viewpage.action?pageId=144581334
         */
        setOpenPlusGift: function (stock) {
            var PB = {
                /**
                 * isWithinDate 判断是否在某个时间段内
                 * @param {String} begin 时间字符串 格式：'2018-23-12 12:21:12'
                 * @param {String} end   时间字符串
                 * @returns {Boolean}
                 */
                isWithinDate: function (begin, end) {
                    var DATE_REGEXP = /(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})/;
                    var getTimeStamp = function(y, m, d, h, min, s) {return +new Date(y, m-1, d, h, min, s);}
                    if (DATE_REGEXP.test(begin) && DATE_REGEXP.test(end)) {
                        begin = getTimeStamp.apply(null, DATE_REGEXP.exec(begin).slice(1, 7));
                        end = getTimeStamp.apply(null, DATE_REGEXP.exec(end).slice(1, 7));
                        var now = +new Date();
                        if (now < begin || now > end) {
                            return false;
                        } else {
                            return true;
                        }
                    } else {
                        return false;
                    }
                }
            };

            // 30元无门槛卷 是在年货节期间(2019年1月8日-2月8日)
            var tipText = PB.isWithinDate('2019-01-08 00:00:01', '2019-02-08 23:59:59') ? '开通会员，享140元全品类券礼包+30元无门槛券' : '开通PLUS年卡，立享价值140元全品类券礼包';

            var cfg = pageConfig.product;
            // PLUS 拉新券 html 片段
            var _html =  '<div id="J-summary-openPlusGift" class="open-plusgift">';
                _html += '   <div class="dt">PLUS礼包</div>';
                _html += '   <div class="dd">';
                _html += '      <a class="icon-plus" href="//plus.jd.com/index" target="_blank" clstag="shangpin|keycount|PC140PLUS_1545882013014|2"></a>';
                _html += '      <em>' + tipText + '</em>&nbsp;';
                _html += '      <a class="a-topluspage" href="//plus.jd.com/index" target="_blank" clstag="shangpin|keycount|PC140PLUS_1545882013014|2">详情 <s class="s-arrow">&gt;&gt;</s></a>';
                _html += '   </div>';
                _html += '</div>';

            // 获取用户信息
            var getUserInfo = function (cb) {
                $.ajax({
                    url: '//passport.jd.com/user/petName/getUserInfoForMiniJd.action',
                    dataType: 'jsonp',
                    success: function (r) {
                        if (typeof cb === 'function') {
                            cb(r);
                        }
                    },
                    error: function (err) {
                        if (typeof console !== 'undefined') {
                            console.log('获取用户信息失败');
                        }
                    }
                });
            }

            // 判断当前 sku 的价格是否符合 2000 - 6000 范围内 范围内的才显示拉新
            var isJdpriceInRange = (stock && stock.jdPrice && stock.jdPrice.p && Number(stock.jdPrice.p) >= 2000 && Number(stock.jdPrice.p) <= 6000) ? true : false;

            // 判断是否 不可用东券 商品，能使用 则展示，不能使用 则不展示; isCanUseJQ-0 为不可用东券标识
            // (如果没有 specialAttrs 字段 算是能使用东券)
            var isCanNotUseDQ = (cfg && cfg.specialAttrs && cfg.specialAttrs.join(',').indexOf('isCanUseJQ-0') !== -1) ? true : false;

            // 判断是否是 PLUS 价商品 非 PLUS 价商品 才展示拉新，tpp 字段为该sku的正式会员 + 试用会员 plus 价
            // (此处没获取到价格算是非 PLUS 价商品)
            var isPlusPriceGoods = (stock && stock.jdPrice && stock.jdPrice.tpp) ? true : false;

            // 判断三级分类 ID 排除以下 ID 的才展示拉新，
            // (此处没获取到分类算是符合拉新条件)
            var disableCatObj = {
                13922: 13922, 13923: 13923, 13924: 13924, 13925: 13925,
                13926: 13926, 13927: 13927, 13928: 13928, 13929: 13929, 
                13930: 13930, 1444: 1444, 6151: 6151, 6152: 6152, 
                13212: 13212, 13220: 13220, 13531: 13531, 13532: 13532, 
                4835: 4835, 4833: 4833, 6980: 6980
            };
            var isDisableCat = (cfg && cfg.cat && cfg.cat[2] && disableCatObj[cfg.cat[2]] !== undefined) ? true : false;

            // 判断页面中是否已经存在 PLUS 礼包 的dom 节点
            // var hasOpenPlusGiftDom = $('#J-summary-openPlusGift').length > 0 ? true : false;

            // 依据以上条件判断是否展示拉新 (PLUS礼包)
            if (isJdpriceInRange && !isCanNotUseDQ && !isPlusPriceGoods && !isDisableCat) {
                // 获取用户信息 判断 plus 会员状态，除 正式中 或 正式过期 外 展示
                // plusStatus : 3 正式中 4 正式过期
                getUserInfo(function (userInfo) {
                    // 判断页面中是否已经存在 PLUS 礼包 的dom 节点
                    var hasOpenPlusGiftDom = $('#J-summary-openPlusGift').length > 0 ? true : false;
                    if (userInfo && !(userInfo.plusStatus == '3' || userInfo.plusStatus == '4') && !hasOpenPlusGiftDom) {
                        $('#summary-support').before(_html);
                        var skuId = pageConfig && pageConfig.product && pageConfig.product.skuid || '-';
                        try {
                            expLogJSON('PC140PLUS_1545882013014', '1', '{"sku": ' + skuId + '}');
                        } catch(e) {
                            if (typeof console !== 'undefined') {
                                console.log('PLUS礼包曝光埋点错误');
                            }
                        }
                    }
                });
            }
        }
    }

    module.exports = Stock
})
