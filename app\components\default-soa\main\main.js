define('MOD_ROOT/main/main', function(require, exports, module) {
    var Tools = require('MOD_ROOT/common/tools/tools');
    // 隐藏登录入口
    // function hideLoginBtns() {
    //     var dividers = $('#shortcut-2024 .fr>.spacer')
    //     var cartAndOrder = $('#shortcut-2024 .fr>.fore2') // 购物车和我的订单
    //     var myJD = $('#shortcut-2024 .fr>.fore3 .dt')
    //     var jdVIP = $('#Me') // 右侧我的
    //     var shoppingCart = $('#Cart') // 右侧购物车
    //     dividers.eq(0).hide()
    //     dividers.eq(1).hide()
    //     dividers.eq(2).hide()
    //     cartAndOrder.hide()
    //     myJD.hide()
    //     jdVIP.hide()
    //     shoppingCart.hide()
    //     // myOrder.attr('href', 'javascript:login();')
    //     // myOrder.attr('target', '_self')
    //     // myJD.attr('href', 'javascript:login();')
    //     // myCart.attr('href', 'javascript:login();')
    // }
    var ssoDomains = [];
    var bakSsoDomains = ["sso.jingdong.com","sso.jd.hk","sso.jdpay.com","sso.healthjd.com","sso.jdh.com","sso.jdl.com","sso.jddj.com","sso.vipmro.com","sso.jingdonghealth.cn"]
    // 获取写登录态域名列表
    function getSsoDomains(callback) { 
        if (ssoDomains.length > 0) {
            callback && callback(ssoDomains);
            return;
        }
        var ReturnUrl = encodeURIComponent(location.href);
        var r = Math.random();
        $.ajax({
                url: 'https://passport.jd.com/ssoDomain/getList',
                data: {
                    r: r,
                    ReturnUrl: ReturnUrl
                },
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                },
                success: function (data) {
                    ssoDomains = data;
                    callback && callback(data);
                },
                error: function (jqXHR, textStatus, errorThrown) { 
                    console.log('获取登录态域名列表失败', textStatus);
                    callback && callback(bakSsoDomains);
                }
        })
    }   
    function wechatLogin(data) {
        var ReturnUrl = encodeURIComponent(location.href);
        var eq = readCookie('3AB9D23F7A4B3C9B') || '';
        var ps = 'passport';
        var pst = 'c';
        var ssoDomains = (data && data.join(',')) || bakSsoDomains
        var url = 'https://qq.jd.com/new/wx/login.aspx?ReturnUrl=' + ReturnUrl + '&eq=' + eq + '&ps=' + ps + '&pst=' + pst + '&ssoDomains=' + ssoDomains;
        window.location.href = url;
    }
    function init(config) {
        try {
            Tools.checkLogin(function (r) {
                if(!(r && r.IsAuthenticated)){// 未登录
                    // hideLoginBtns()
                    var locname = window.location.hostname
                    var isInJDHealth = locname=="item.yiyaojd.com" || locname=="item.jkcsjd.com" || locname=="item.jdh.com" || locname=="item.jingdonghealth.cn"
                    var isInWeChat = /micromessenger/.test(navigator.userAgent.toLowerCase());
                    if (isInWeChat) {
                        if(window.wxLogin) {
                            window.login = window.newLogin = window.wxLogin // 使用公共组件中的微信登录方法
                            return
                        }
                        getSsoDomains()
                        window.login = function() {
                            getSsoDomains(wechatLogin)
                        }
                        if (isInJDHealth) {
                            window.newLogin = function() {
                                getSsoDomains(wechatLogin)
                            }
                        }
                    } else {
                        if(isInJDHealth){
                            window.newLogin = function() {
                                return location.href = "https://sso" + locname.split("item")[1] + "/sso/login?ReturnUrl=" + encodeURIComponent(location.href).replace(/\//g, "%2F"), !1
                            };
                        }
                    }
                }
            })
            
        } catch (error) {
            console.log("新登录方式报错", error)
        }
        /**
         * 模块入口（1. 公共脚本 2. 首屏模块资源 3. 非首屏「后加载模块」）
         */
        var entries = [];

        // 页面公共脚本样式
        entries.push('common');
        // 页面使用到的首屏模块(开发根据页面不同配置需要调用的模块)
        entries = entries.concat(config.modules);
        // 非首屏「后加载模块」
        entries.push('lazyinit');

        var PublicModules = {
            detail: 'detail'
        };

        for (var i = 0; i < entries.length; i++) {
            if (entries[i] in PublicModules) {
                entries[i] = 'PUBLIC_ROOT/modules/' + entries[i] + '/' + entries[i];
            } else {
                entries[i] = 'MOD_ROOT/' + entries[i] + '/' + entries[i];
            }
        }

        if (/debug=show_modules/.test(location.href)) {
            console.log(entries);
        }
        require.async(entries, function() {
            var modules = Array.prototype.slice.call(arguments);
            var len = modules.length;

            for (var i = 0; i < len; i++) {
                var module = modules[i];

                if (module && typeof module.init === 'function') {
                    module.init(config);
                } else {
                    console.warn('Module[%s] must be exports a init function.', entries[i]);
                }
            }
        });
        var enterTime = Date.now()
        
        window.addEventListener('beforeunload', function (e) {
            // PC_Productdetail_TimeExpo
            var stayTime = Date.now() - enterTime
            console.log('停留时间', stayTime);
            Tools.exposure({
                functionName: 'PC_Productdetail_TimeExpo',
                exposureData: [],
                extraData: {
                    stayTime: stayTime / 1000 + '秒'
                },
                errorTips: '页面离开曝光错误'
            })
            // e.preventDefault();
            // return '您还有未保存的数据，确定要离开吗？';
        });
    }

    module.exports.__id = 'main';
    module.exports.init = init;
});


