<!--
文件ID: 248
系统: 单品页
类型: Toolbar
描述: 单品页Toolbar文件
-->
<div id="J-global-toolbar"></div>
<script>
    seajs.use(['product/toolbar/1.0.1/js/main.js'], function(toolbar) {
        var sid = pageConfig.product.cat[2] === 832 ? '737542' : '992349';

        pageConfig.toolbar = new toolbar({
            pType: 'item',
            bars: {
                jimi: {
                    iframe: '//jimi.jd.com/index.action?productId='+ pageConfig.product.skuid +'&source=jdhome'
                }
            },
            links: {
                feedback: {
                    href: '//surveys.jd.com/index.php?r=survey/index/sid/{sid}/lang/zh-Hans'.replace('{sid}', sid)
                },
                top:{
                    anchor:"#"
                }
            },
            ad: {
                id: "0_0_6913",
                startTime: 1432483200,
                endTime: 1434816000
            }
        });
    });
</script>