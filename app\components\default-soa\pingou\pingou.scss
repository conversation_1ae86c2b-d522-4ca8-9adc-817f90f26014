@import '../common/lib';
@import './__sprite';

.yyp {
    .choose-btns, .summary-stock {
        border-top: none;
        border-bottom: none;
    }
    .choose-btns {
        bottom: 0;
        margin-top: 0;
        // padding: 10px 10px 0;
    }
    .summary-stock {
        // margin-bottom: 0;
    }
    //.summary-line {
    //    margin-bottom: 15px;
    //    border-bottom: 1px dotted #dfdfdf;
    //    height:0;
    //    overflow: hidden;
    //    clear: both;
    //}
    .yy-category {
        position: relative;
        top: -3px;
    }
    .btn-lg.btn-special3,
    .btn-lg.btn-disable {
        height: 52px;
        line-height: 52px;
    }
}

// top banner
.activity-banner{
    height: 52px;
    line-height: 52px;
    padding:0 10px;
    background: #e4393c url(i/activity-banner.png) no-repeat right top;
    font-family: "Microsoft YaHei";
    overflow: hidden;
    zoom: 1;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    i{
        display: inline-block;
    }
    .sprite-pingou{
        @include sprite-pingou;
        vertical-align: -4px;
        margin-right: 5px;
    }
    .sprite-yy{
        @include sprite-yy;
        vertical-align: -2px;
        margin-right: 5px;
    }
    .sprite-person{
        @include sprite-person;
        vertical-align: -2px;
        margin-right: 4px;
    }
    .sprite-item{
        @include sprite-item;
        vertical-align: -2px;
        margin-right: 4px;
    }
    .sprite-time{
        @include sprite-time;
        vertical-align: -3px;
        margin-right: 4px;
    }
}
.activity-type{
    float: left;
    strong{
        font-size: 16px;
        color: #ffe134;
        font-weight: normal;
    }
    span{
        margin-left: 8px;
        font-size: 12px;
        color: #fff;
        font-weight: bold;
    }
}
.left-message-info{
    float: right;
    margin-top: -6px;
    .pay-right-img{
        padding-right: 8px;
    }
    .middleIcon{
        display: inline-block;
    }
    .activity-message{
        color: #fff;
        font-size: 14px;
        display: inline-block;
        font-family: 'JDZhengHeiVRegular2-1';
        .item {
            display: none;
            margin-left: 30px;
        }
        em{
            color:#ffe134;
        }
        .J-count{
            font-weight: bold;
        }
    }
}

.seckill{
    background: #f0f0f0 url(i/seckill.png) no-repeat;
    .sprite-seckill{
        @include sprite-seckill;
        vertical-align: -4px;
        margin-right: 5px;
    }
    .activity-type{
        strong{
            color: #e4393c;
        }
    } 
    .activity-message{
        color: #666;
        font-size: 12px;
    }
}
.seckilling{
    background: #e84540 url(i/seckilling.png) no-repeat;
    .sprite-seckilling{
        @include sprite-seckilling;
        vertical-align: -4px;
        margin-right: 5px;
    }
    .activity-type{
        strong{
            color: #fff;
        }
    }
    .activity-message{
        color: #fbe2e2;
        font-size: 12px;
        span{
            font-size: 14px;
            color: #fff;
            display: inline-block;
            width: 22px;
            line-height: 24px;
            text-align: center;
            background: #443b3b;
            margin: 0 4px;
            border-radius: 2px;
        }
    }
}

.yuyue,.yushou {
    .arrow {
        font-family: $font-st;
    }
    .yuyue-share {
        line-height: 50px;
        cursor: pointer;
        i {
            @include inline-block;
            margin-right: 3px;
        }

        .sprite-share{
            vertical-align: middle;
            @include sprite-share;
        }
    }
}
.yuyue,.yushou,.pingou {
    .fittings {
        .master {
            display: none;
        }
        .infos {
            display: none;
        }
        .suits {
            width: 990px;
            height: 163px;

            overflow: hidden;

            .btns {
                display: none;
            }

            li {
                input, i {
                    display: none;
                }

            }
            .root61 & {
                width: 1210px;
            }
        }
    }
}

// .root61 .yuyue .fittings .suits .switchable-wrap .lh-wrap {
//     margin: 0;
// }
// .root61 .yushou .fittings .suits .switchable-wrap .lh-wrap {
//     margin: 0;
// }
// .root61 .pingou .fittings .suits .switchable-wrap .lh-wrap {
//     margin: 0;
// }
.root61 {
    .yuyue,.yushou,.pingou {
        .fittings .suits .switchable-wrap .lh-wrap {
            margin: 0;
        }
    }
}

// #pingou {
//     .ys-price .price { font-size: 16px; }
//     .pg-price .price { font-size: 16px; }
//     .dj-price .price { font-size: 22px; }
// }
.pingou{
    margin-bottom: 5px;

    &-rules {
        padding: 20px;
        ol {
            padding-left: 0;
            list-style: none;
        }
        h3 {
            padding-bottom:10px;
        }
        li {
            padding: 5px 0;
        }
    }
    .pingou-summary{
        padding-bottom: 2px;
        // background: #f3f3f3;
        .summary-price-wrap{
            margin-bottom: 0;
            background:none;
            .pricePresaleText{
                font-family: 'PingFang SC'!important;
                background: #ff0f23;
                color: #fff;
                padding: 4px;
                border-radius: 2px;
                font-size: 13px!important;
                line-height: 14px;
                height: 14px;
                margin-left: 8px;
                margin-right: 6px;
                display: inline-block;
                transform: translateY(-5px); // 预售价 10129434137186
            }
        }
        .pin-price{
            // .dd{
            //     padding: 5px 30px 0;
            // }
            .p-price{
                line-height: 160%;
                .price{
                    // margin-left: 3px;
                }
            }
        }
        .p-price-jd {
            color:#999;
            padding-left: 1em;
            @include inline-block;
            position: relative;
            top: -2px;
        }
    }
}

.yushou .pingou-rules ol, 
.pingou .pingou-rules ol {
    padding-left: 0;
    list-style: none;
}


.pingou-price{
    padding: 35px 8% 15px;
    margin: 0 10px 10px 10px;
    background: #fff;
    .progress{
        padding: 0 15px;
        .del {
            text-decoration: line-through;
        }
    }
    .progress ul{
        padding-bottom: 8px;
        overflow: hidden;
        zoom:1;
    }
    .progress li{
        width: 31.6%;
        float: left;
        font-size: 14px;
        color: #999;
    }
    .progress .current{
        color: #f0999a;
    }
    .progress-bar{
        position: relative;
        height: 5px;
        background: #f0f0f0;
        ul{
            position: absolute;
            left: -8px;
            right: -0px;
            top: -4px;
            z-index: 2;
        }
        li {
            width: 33%;
            line-height: 10px;
            float: left;
            _overflow:hidden;
            i{
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 6px;
                margin-left: 2px;
                background: #dedede;
                _overflow:hidden;
            }
        }
        .current i{
            background: #e4393c url(i/arrow.png) no-repeat -15px 0;
        }
        .inner-bar{
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
            height: 5px;
            _overflow: hidden;
            text-align: right;
            background: #e4393c;
            @include transition(width 0.5s ease-in-out);
            i {
                width: 4px;
                height: 5px;
                background: url(i/arrow.png) no-repeat;
                position: absolute;
                right: 0;
                top: 0;
            }
            .total-num {
                position: absolute;
                top: -26px;
                padding: 2px 7px;
                border-radius: 4px;
                background: #fff0f0;
                right: -20px;
                white-space: nowrap;
                font: bold 12px $font-st;
                color:#f0999a;
                i{
                    width: 7px;
                    height: 5px;
                    position: absolute;
                    top: auto;
                    right: auto;
                    bottom: -5px;
                    left: 50%;
                    margin-left: -3px;
                    background: url(i/arrow.png) no-repeat -6px 0;
                }
            }
        }

    }
    .price-list {
        margin-top: 10px;
        padding: 0 15px;
        overflow: hidden;
        zoom:1;
        li {
            width: 31.3%;
            text-align: center;
            float: left;
            margin: 0 6px;
            padding: 12px 0;
            background: #f1f1f1;
            font-size: 14px;
            color: #999;
            &.current {
                background: #fff0f0;
                color: $colorPriceRed;
                font-weight: bold;
            }
        }
    }
}

.presale-tips {
    padding: 5px;
    ul {
        padding-left: 15px;
    }
    b {
        margin-left: -15px;
        padding-right: 5px;
    }
}

.yy-category {
    color: #fff;
    background: rgba(255, 15, 35, 1);
    font-size: 13px;
    font-weight: 600;
    padding: 4px;
    border-radius: 2px;
    position: relative;
    top: -3px;
}
.yy-process {
    .item {
        width: 20%;
    }
    .item1,.item3 { width: 18%; }
    .item2,.item4 { width: 22%; }
}
.pingou-process {
    .item {
        width: 26%;
        *width:27%;
    }
}
.pingou-process, .yy-process{
    margin-bottom: 10px;
    border: 1px solid #eee;
    background: #f7f7f7;
    overflow: hidden;
    *zoom: 1;
    h3{
        width:12.9%;
        float: left;
        line-height: 60px;
        border-right: 1px solid #eee;
        text-align: center;
        font-size: 20px;
        color: #e4393c;
        font-family: $font-yahei;
    }
    .item{
        position: relative;
        padding-left: 1%;
        float: left;
        i{
            display: inline-block;
            float: left;
            margin-top: 7px;
        }
        .sprite-step1{
            @include sprite-step1;
        }
        .sprite-step2{
            @include sprite-step2;
        }
        .sprite-step3{
            @include sprite-step3;
        }
        .sprite-yy-step1{
            @include sprite-yy-step1;
        }
        .sprite-yy-step2{
            @include sprite-yy-step2;
        }
        .sprite-yy-step3{
            @include sprite-yy-step3;
        }
        .sprite-yy-step4{
            @include sprite-yy-step4;
        }
        .sprite-pay {
            @include sprite-pay;
        }

        .sprite-deliver {
            @include sprite-deliver;
        }

        dl{
            margin: 15px 0 0 55px;
        }
        dt{
            font:bold 14px/18px $font-yahei;
            color:#666;
            font-weight: bold;
            em {
                font-size: 12px;
                font-weight: normal;
            }
        }
        dd{
            font-size: 12px;
            color:#999;
        }
        .sprite-arrow{
            position: absolute;
            right:0;
            top: 0;
            @include sprite-arrow;
        }
        .sprite-step1 {
            @include sprite-step1;
        }
        .sprite-step2 {
            @include sprite-step2;
        }
        .sprite-step3 {
            @include sprite-step3;
        }
    }
}

.partners{
    padding-top: 20px;
    .ui-page{
        float: right;
        margin: 15px 0 25px;
    }
}
.invite-tips{
    line-height: 46px;
    padding: 0 16px;
    background: #efefef;
    font-size: 12px;
    color: #666;
    span{
        margin: 0 5px;
    }
    em{
        color: #e6251e;
        margin: 0 5px;
    }
}
.invite-con{
    padding: 18px 0;
    border: 1px solid #efefef;
    .invite-tit{
        width: 105px;
        text-align: center;
        font-size: 14px;
        color: #666;
        vertical-align: 12px;
        display: inline-block;
        *display: inline;
        *zoom:1;
    }
    .invite-top {
        display: inline-block;
        *display: inline;
        *zoom: 1;
        margin: 0 10px;
    }
    .invite-item{
        margin: 0 10px;
        display: inline-block;
        *display: inline;
        *zoom:1;
        .invite-img{
            width: 56px;
            height: 56px;
            position: relative;
            float: left;
            img{
                width: 56px;
                height: 56px;
                border-radius: 28px;
            }
            i{
                position: absolute;
                left: -2px;
                top: -4px;
            }
            .sprite-top1{
                @include sprite-top1;
            }
            .sprite-top2{
                @include sprite-top2;
            }
            .sprite-top3{
                @include sprite-top3;
            }
        }
        .invite-info{
            min-width: 120px;
            margin: 5px 0 0 66px;
            line-height: 200%;
            span{
                margin: 0 5px;
            }
        }
    }
    .invite-op{
        display: inline-block;
        *display: inline;
        *zoom:1;
        .sprite-person2{
            display: inline-block;
            margin-right: 5px;
            @include sprite-person2;
        }
        .btn-invite,.btn-view{
            display: inline-block;
            height: 28px;
            line-height: 28px;
            padding: 0 15px;
            font-size: 12px;
            border:1px solid #fff;
            margin: 8px 10px 0 0;
        }
        .btn-invite{
            background: #e43a3d;
            color: #fff;
            border-color: #e43a3d;
        }
        .btn-view{
            background: #f7f7f7;
            border-color: #eee;
            color: #999;
        }
    }
}
.invite-user-list{
    overflow: hidden;
    *zoom:1;
    .user-item{
        width: 33.3%;
        float: left;
        margin: 20px 0;
        img{
            width: 76px;
            height: 76px;
            border-radius: 38px;
            float: left;
        }
        .user-info{
            margin: 4px 0 0 90px;
            line-height: 200%;
            dt{
                font-size: 14px;
            }
            dd{
                color: #676767;
            }
        }
    }
}
.root61 .pingou-price .price-list li{
    width: 30.6%;
}

// 梦想购
.dream-purchase {}

.activity-banner.dream-purchase-banner {
    background: url(i/dream-purchase-banner.png) no-repeat left top;
    .activity-type {
        .sprite-yy {
            @include sprite-clock;
            vertical-align: middle;
        }
        strong {
            color: #fff;
            vertical-align: middle;
        }
    }
    .activity-message {
        em { color: #fff;}
    }
}

.dream-purchase-sale-process {
    .progress-bar {
        *display: inline;
        *zoom: 1;
        display: inline-block;
        width: 500px;
        height: 3px;
        background-color: #b5b5b5;
        vertical-align: middle;
        .progress-bar-inner {
            position: relative;
            width: 0%;
            height: 100%;
            background-color: #ff2980;
        }
        .progress-bar-text {
            *display: inline;
            *zoom: 1;
            display: inline-block;
            position: relative;
            padding: 2px 5px;
            border: 1px solid #f00097;
            border-radius: 50px;
            background-color: #fff;
            color: #f00097;
            font-size: 12px;
            white-space: nowrap;
            word-break: keep-all;
            // “向下空心三角形”绘制
            &::before,
            &::after {
                overflow: hidden;
                position: absolute;
                left: 0;
                right: 0;
                margin: auto;
                bottom: -6px;
                content: '';
                width: 0;
                height: 0;
                border: 6px solid #f00097;
                border-bottom: none;
                border-left-color: transparent;
                border-right-color: transparent;
            }
    
            &::after {
                bottom: -5px;
                border-top-color: #fff;
            }
        }
        // “三角形的尖部对准进度条右侧边”定位处理
        .progress-bar-text-wrap {
            position: absolute;
            right: 0;
        }
    
        .progress-bar-text-wrap-inner {
            position: absolute;
            left: -400px;
            right: -400px;
            margin: auto;
            top: -35px;
            text-align: center;
        }
    }
    &-status {
        *display: inline;
        *zoom: 1;
        display: inline-block;
        vertical-align: middle;
    }
    &-status.do {
        @include sprite-do;
    }
    &-status.done {
        @include sprite-done;
    }
    &-tips {
        width: 521px;
        text-align: right;
        color: #999;
        font-size: 12px;
    }
}

.summary-yushou-ship{
    .dd{
        color: #1a1a1a;
        font-size: 15px;
        word-break: keep-all;
        padding: 0 10px;
    }
}

#yushourule{
    margin-bottom: 20px;
    height: 15px;
    line-height: 20px;
    .ddnew {
        display: flex;
        align-items: center;
    }
    .step1{
        color: #FF7219;
        font-size: 14px;
        font-weight: 500;
        margin-right: 8px;
    }
    .stepT1{
        color: #FF7219;
        font-size: 12px;
        font-weight: 500;
        margin-right: 12px;
    }
    .line{
        display: inline-block;
        width: 18px;
        height: 11px;
        margin-right: 12px;
        vertical-align: middle;
        background: url(https://img14.360buyimg.com/imagetools/jfs/t1/250213/21/33904/454/6782705eFa70ba5a6/06bfe5ac3f82f2c6.png);
        background-size: 100%;
        vertical-align: middle; 
    }
    .step2{
        color: #505259;
        font-size: 14px;
        font-weight: 500;
        margin-right: 8px;
    }
    .stepT2{
        color: #505259;
        font-size: 12px;
        font-weight: 500;
    }
}
