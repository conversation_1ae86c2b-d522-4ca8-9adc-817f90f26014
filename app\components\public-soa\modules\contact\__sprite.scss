
                    @mixin sprite-communication {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -34px -16px;
                    }
                    @mixin sprite-diamond {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -34px;
                    }
                    @mixin sprite-down {
                        width: 9px;
                        height: 9px;
                        background-image: url(i/__sprite.png);
                        background-position: -50px -9px;
                    }
                    @mixin sprite-enter {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -16px -18px;
                    }
                    @mixin sprite-follow {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -34px -0px;
                    }
                    @mixin sprite-im-offline {
                        width: 17px;
                        height: 17px;
                        background-image: url(i/__sprite.png);
                        background-position: -17px -0px;
                    }
                    @mixin sprite-jimi {
                        width: 17px;
                        height: 18px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -0px;
                    }
                    @mixin sprite-middle {
                        width: 10px;
                        height: 9px;
                        background-image: url(i/__sprite.png);
                        background-position: -50px -0px;
                    }
                    @mixin sprite-phone {
                        width: 10px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -32px -34px;
                    }
                    @mixin sprite-qr {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -16px -34px;
                    }
                    @mixin sprite-telephone {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -18px;
                    }
                    @mixin sprite-up {
                        width: 9px;
                        height: 9px;
                        background-image: url(i/__sprite.png);
                        background-position: -50px -18px;
                    }