/**
 * 母婴垂直化
 */
define('MOD_ROOT/baby/baby', function(require, exports, module) {
    var trimPath = require('JDF_UNIT/trimPath/1.0.0/trimPath')
    var login = require('JDF_UNIT/login/1.0.0/login')

    require('PLG_ROOT/jQuery.Jcal')

    var template =
        '\
    <div id="muying" class="m m-content {if !ext.t1} result-info{/if}">\
        <div class="mt"><h3>{if ext.t1}陪伴计划·好券任领·尊享福利价{else}陪伴计划·享特价·领好礼·惠挑选{/if}</h3></div>\
        <div class="mc">\
            <ul class="lh muying-t1 {if !ext.t1} hide{/if}">\
                <li>\
                    <div class="dt"><span class="required">*</span> <strong>生日/预产期：</strong></div>\
                    <div class="dd">\
                        <input type="text" id="J-cal-baby" class="J-inp-cal" value="${ext.dateS}" />\
                    </div>\
                </li>\
                <li>\
                    <div class="dt"><strong>昵称：</strong></div>\
                    <div class="dd">\
                        <input type="text" name="nickname" class="baby-name" value="${ext.nickname}" maxlength="6" />\
                    </div>\
                </li>\
                <li>\
                    <div class="dt"><span class="required">*</span> <strong>性别：</strong></div>\
                    <div class="dd">\
                        <label for="baby-girl"><input type="radio" name="gender" value="1" id="baby-girl" {if ext.sex==1}checked{/if} /> 王子</label>\
                        <label for="baby-boy"><input type="radio" name="gender" value="2" id="baby-boy" {if ext.sex==2}checked{/if} /> 公主</label>\
                        <label for="baby-unknow"><input type="radio" name="gender" value="0" id="baby-unknow" {if ext.sex==0}checked{/if} /> 未知</label>\
                    </div>\
                </li>\
                {if pageConfig.product.isBookMvd4Baby}\
                <li>\
                    <div class="dt"><strong>学龄：</strong></div>\
                    <div class="dd">\
                        <select id="schoolAge" name="schoolAge" class="baby-schoolAge">\
                            <option value="">选择学龄</option>\
                            <option value="-3" {if ext.schoolAge==-3}selected="selected"{/if}>学龄前</option>\
                            <option value="0" {if ext.schoolAge==0}selected="selected"{/if}>幼儿园</option>\
                            <option value="1" {if ext.schoolAge==1}selected="selected"{/if}>小学一年级</option>\
                            <option value="2" {if ext.schoolAge==2}selected="selected"{/if}>小学二年级</option>\
                            <option value="3" {if ext.schoolAge==3}selected="selected"{/if}>小学三年级</option>\
                            <option value="4" {if ext.schoolAge==4}selected="selected"{/if}>小学四年级</option>\
                            <option value="5" {if ext.schoolAge==5}selected="selected"{/if}>小学五年级</option>\
                            <option value="6" {if ext.schoolAge==6}selected="selected"{/if}>小学六年级</option>\
                            <option value="7" {if ext.schoolAge==7}selected="selected"{/if}>初中一年级</option>\
                            <option value="8" {if ext.schoolAge==8}selected="selected"{/if}>初中二年级</option>\
                            <option value="9" {if ext.schoolAge==9}selected="selected"{/if}>初中三年级</option>\
                            <option value="10" {if ext.schoolAge==10}selected="selected"{/if}>高中一年级</option>\
                            <option value="11" {if ext.schoolAge==11}selected="selected"{/if}>高中二年级</option>\
                            <option value="12" {if ext.schoolAge==12}selected="selected"{/if}>高中三年级</option>\
                        </select>\
                    </div>\
                </li>\
                {/if}\
                {if false}\
                <li>\
                    <div class="dt"><strong>体重：</strong></div>\
                    <div class="dd">\
                        <input type="text" id="baby-weight" class="baby-size" {if !ext.isBrithday} disabled {/if} value="${ext.w}" /> <i>kg</i>\
                        <div class="error-msg hide"> 1~100 之间哦</div>\
                    </div>\
                </li>\
                <li>\
                    <div class="dt"><strong>身高：</strong></div>\
                    <div class="dd">\
                        <input type="text" id="baby-height" class="baby-size" {if !ext.isBrithday} disabled {/if} value="${ext.h}" /> <i>cm</i>\
                        <div class="error-msg hide"> 20~200 之间哦</div>\
                    </div>\
                </li>\
                {/if}\
                <li>\
                    <a href="#none" target="_self" clstag="shangpin|keycount|product|addbabyinfo" class="btn-primary muying-submit">快速加入</a>\
                </li>\
            </ul>\
            <ul class="lh muying-t2 {if !ext.t2} hide{/if}"">\
                <li><b>${ext.result}啦，成长专区惊喜等你来哦 </b><b></b><a href="#none" clstag="shangpin|keycount|product|updatebabyinfo" class="hl_blue modify-info">[修改信息]</a><a href="${ext.link}" target="_blank" clstag="shangpin|keycount|product|tobaby" class="btn-primary">进入陪伴计划</a></li>\
            </ul>\
        </div>\
    </div>'

    var URLS = {
        UPDATE: '//up.jd.com/child/update',
        SAVE: '//up.jd.com/child/save',
        GET: '//up.jd.com/child/queryChildProfileByPin',
        GET_SCHOOL_AGE: '//up.jd.com/child/getSchoolAge'
    }

    var Baby = {
        init: function($wrap) {
            var _this = this
            _this.$el = $wrap || $('#J-baby')
            _this.link = _this.$el.data('url')
            _this.isCreate = false
            _this.babyId = null
            _this.isSaving = false
            _this.isLogin = false
            _this.isBookMvd4Baby = pageConfig.product.isBookMvd4Baby

            _this.checkLogin(function(r) {
                if (r && r.IsAuthenticated) {
                    _this.isLogin = true
                    _this.get()
                } else {
                    _this.isLogin = false
                    _this.setEdit([])
                }
            })
        },
        get: function() {
            var _this = this

            $.ajax({
                url: URLS.GET,
                dataType: 'jsonp',
                data: { origin: 'item_pc', originDevice: 1 },
                scriptCharset: 'utf-8',
                success: function(r) {
                    if (r.success == false) {
                        if (r.errCode == '0001') {
                            _this.setEdit([])
                        }
                        return false
                    }
                    if (r.data && r.data.length > 0) {
                        _this.set(r.data)
                    } else {
                        _this.setEdit(r.data)
                    }
                }
            })
        },
        set: function(d) {
            var data = d[0]
            var nickname = data.nickname
            var birthday = data.birthday
            var birthdayDate = new Date(data.birthday)
            var birthdayStr =
                birthdayDate.getFullYear() +
                '-' +
                (birthdayDate.getMonth() + 1) +
                '-' +
                birthdayDate.getDate()

            var height = data.height ? Number(data.height) : ''
            var weight = data.weight ? Number(data.weight) : ''
            var sex = Number(data.sex)
            var schoolAge = data.schoolAge ? Number(data.schoolAge) : ''
            if (data.id) {
                this.babyId = data.id
            }

            //var res = d.babyinfo.split('-');
            //var dateM = Number(res[0]);
            //var dateS = new Date(dateM).getFullYear() + '-' + (new Date(dateM).getMonth()+1) + '-' + new Date(dateM).getDate();
            //var height = !isNaN(parseFloat(res[1])) ? parseFloat(res[1]) : '';
            //var weight = !isNaN(parseFloat(res[2])) ? parseFloat(res[2]) : '';
            //var sex = Number(res[3]);

            var currentDate = new Date()
            var isToday = this.isSameDay(birthday, currentDate)
            d.ext = {
                result: '', // 显示文字
                dateS: birthdayStr, // 已填时间,
                nickname: nickname, // 昵称
                h: height || '', // 身高
                w: weight || '', // 体重
                sex: sex, // 性别
                t1: false, // 编辑状态
                t2: false // 怀孕状态
            }
            if (schoolAge) {
                //学龄
                d.ext.schoolAge = schoolAge
            }
            d.ext.isBrithday = birthdayDate < currentDate

            // 如果用户已填数据大于当前时间，表示宝宝即将出生日期、否则就是生日
            d.ext.t2 = true
            if (birthdayDate > currentDate) {
                var res = this.formatTime(birthdayDate)
                d.ext.result =
                    '宝宝 <span class="curr-age">' + res.tip + '</span> 后就要出生'
            } else {
                if (isToday) {
                    d.ext.result = '宝宝 <span class="curr-age">即将出生</span> '
                } else {
                    var res = this.formatTime(birthdayDate)
                    d.ext.result =
                        '宝宝 <span class="curr-age">' + res.tip + '</span> '
                }
            }

            d.ext.link = this.link

            this.$el.html(template.process(d))
            this.bindEvent()
            this.detectStatus(birthday, false)
        },
        isSameDay: function(ms1, ms2, flag) {
            if (!flag) {
                return (
                    new Date(ms1).getFullYear() ===
                        new Date(ms2).getFullYear() &&
                    new Date(ms1).getMonth() === new Date(ms2).getMonth() &&
                    new Date(ms1).getDate() === new Date(ms2).getDate()
                )
            } else {
                if (flag === 'y') {
                    return (
                        new Date(ms1).getFullYear() ===
                        new Date(ms2).getFullYear()
                    )
                } else if (flag === 'm') {
                    return new Date(ms1).getMonth() === new Date(ms2).getMonth()
                } else if (flag === 'd') {
                    return new Date(ms1).getDate() === new Date(ms2).getDate()
                }
            }
        },
        isLeapYear: function(year) {
            return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
        },
        formatTime: function(birth) {
            var now = new Date()
            var birthday = new Date(birth * 1), res = { tip: '' }, t1, t2
            t1 = birthday < now ? birthday : now
            t2 = birthday < now ? now : birthday
            var y = t2.getYear() - t1.getYear(),
                m = t2.getMonth() - t1.getMonth(),
                d = t2.getDate() - t1.getDate()
            if (d < 0) {
                d += 30
                m -= 1
            }
            if (m < 0) {
                m += 12
                y -= 1
            }
            if (!y && !m && !d) {
                res = { tip: '即将出生', status: 'now' }
            } else if (birthday < now) {
                res['status'] = 'past'
                if (y) {
                    res.tip = y + '岁' + (m ? m + '个月' : '')
                } else {
                    if (m) {
                        res.tip = m + '个月'
                    }
                    if (d) {
                        res.tip += d + '天'
                    }
                }
            } else {
                res['status'] = 'future'
                if (y) {
                    res.tip = y + '年' + (m ? m + '个月' : '')
                } else {
                    if (m) {
                        res.tip = m + '个月'
                    }
                    if (d) {
                        res.tip += d + '天'
                    }
                }
            }

            return res
        },
        setEdit: function(d) {
            d.ext = {}
            d.ext.t1 = true
            d.ext.link = this.link
            this.$el.html(template.process(d))
            this.bindEvent()
        },
        setCalendar: function() {
            var _this = this
            var maxYear = new Date().getFullYear() + 1
            var minYear = new Date().getFullYear() - 14

            $('#J-cal-baby').Jcal({
                start: minYear + '-1-1',
                end: maxYear + '-12-31',
                onSelected: function(r) {
                    var date = new Date(r.year, r.month - 1, r.date).getTime()
                    _this.detectStatus(date, true)
                }
            })
        },
        detectStatus: function(r, isUserSelect) {
            var _this = this
            var inputs = _this.el.find('#baby-weight,#baby-height')

            if (new Date(r) > new Date()) {
                _this.el.find('#baby-weight,#baby-height').val('')
                _this.el.find('li').removeClass('illegal-value')
                inputs.attr('disabled', true)
            } else {
                inputs.removeAttr('disabled')
            }

            if (_this.isBookMvd4Baby && isUserSelect) {
                //拼日期字符串
                var birthday = _this.el.find('#J-cal-baby').val()
                var birthdayArr = birthday.split('-')
                var year = birthdayArr[0]
                var month = birthdayArr[1].length == 1
                    ? '0' + birthdayArr[1]
                    : birthdayArr[1]
                var day = birthdayArr[2].length == 1
                    ? '0' + birthdayArr[2]
                    : birthdayArr[2]
                var birthdayStr = year + '-' + month + '-' + day

                $.ajax({
                    url: URLS.GET_SCHOOL_AGE,
                    dataType: 'jsonp',
                    data: {
                        origin: 'item_pc',
                        originDevice: 1,
                        birthdayStr: birthdayStr
                    },
                    success: function(r) {
                        if (r && r.success == true) {
                            if (r.data < 0) {
                                $('#schoolAge').val(-3)
                            } else {
                                $('#schoolAge').val(r.data)
                            }
                        }
                    }
                })
            }
        },
        bindEvent: function() {
            var _this = this
            var muying = _this.$el.find('#muying')
            var modify = muying.find('.modify-info')
            var weight = muying.find('#baby-weight')
            var height = muying.find('#baby-height')

            var sub = muying.find('.muying-submit')
            var t1 = muying.find('.muying-t1')
            var t2 = muying.find('.muying-t2')

            this.el = muying
            this.t1 = t1
            this.t2 = t2

            modify.bind('click', function() {
                t1.show()
                muying.find('.mt h3').html('陪伴计划·好券任领·尊享福利价')
                t2.hide()
                muying.removeClass('result-info')
            })

            sub.bind('click', function(e) {
                var birthdayVal = muying.find('#J-cal-baby').val()
                var nickname = muying.find('input[name="nickname"]').val()
                var sex = muying.find('input[name="gender"]:checked').val()
                var hasValidated = muying.find('.illegal-value').length < 1 //身高体重验证

                if (!birthdayVal) {
                    alert('请填写宝宝生日/预产期')
                    return false
                }

                /*
                if (!nickname) {
                    alert('请填写宝宝昵称')
                    return false
                }
                */

                if (!sex) {
                    alert('请填写宝宝性别')
                    return false
                }

                if (!hasValidated) {
                    return false
                }

                if (_this.isSaving) {
                    return
                }

                login({
                    modal: true,
                    complete: function() {
                        $.ajax({
                            url: URLS.GET,
                            dataType: 'jsonp',
                            data: { origin: 'item_pc', originDevice: 1 },
                            scriptCharset: 'utf-8',
                            success: function(r) {
                                if (r.success == false) {
                                    alert(r.message)
                                    return false
                                }
                                if (r.data && r.data.length > 0) {
                                    _this.babyId = r.data[0].id
                                    _this.isCreate = false
                                } else {
                                    _this.isCreate = true
                                }
                                _this.save()
                            }
                        })
                    }
                })

                _this.$el.find('.Jcalendar').hide()
            })

            weight.bind('blur', function() {
                var inp = $(this)
                var val = Number(inp.val())
                var isEmpty = inp.val() === ''
                var isNumber = !isNaN(parseInt(val)) && !isEmpty

                if (isNumber) {
                    if (val < 1 || val > 100) {
                        inp.parents('li').eq(0).addClass('illegal-value')
                    } else {
                        inp.parents('li').eq(0).removeClass('illegal-value')
                        inp.val(
                            inp.val().substring(0, inp.val().indexOf('.') + 3)
                        )
                    }
                } else {
                    if (isEmpty) {
                        inp.parents('li').eq(0).removeClass('illegal-value')
                    } else {
                        inp.parents('li').eq(0).addClass('illegal-value')
                    }
                }
            })
            height.bind('blur', function() {
                var inp = $(this)
                var val = Number(inp.val())
                var isEmpty = inp.val() === ''
                var isNumber = !isNaN(parseInt(val)) && !isEmpty

                if (isNumber) {
                    if (val < 20 || val > 200) {
                        inp.parents('li').eq(0).addClass('illegal-value')
                    } else {
                        inp.parents('li').eq(0).removeClass('illegal-value')
                        inp.val(
                            inp.val().substring(0, inp.val().indexOf('.') + 3)
                        )
                    }
                } else {
                    if (isEmpty) {
                        inp.parents('li').eq(0).removeClass('illegal-value')
                    } else {
                        inp.parents('li').eq(0).addClass('illegal-value')
                    }
                }
            })
            this.setCalendar()
        },
        save: function() {
            var _this = this
            var birthday = this.el.find('#J-cal-baby').val()
            var nickname = this.el.find('input[name="nickname"]').val()
            var sex = this.el.find('input[name="gender"]:checked').val()
            var weight = this.el.find('#baby-weight').val() || ''
            var height = this.el.find('#baby-height').val() || ''
            var schoolAge = this.el.find('#schoolAge').val()

            //var result = new Date(date.split('-')[0], Number(date.split('-')[1])-1, date.split('-')[2]).getTime() + '-' + height + '-' + weight + '-' + sex;

            //拼日期字符串
            var birthdayArr = birthday.split('-')
            var year = birthdayArr[0]
            var month = birthdayArr[1].length == 1
                ? '0' + birthdayArr[1]
                : birthdayArr[1]
            var day = birthdayArr[2].length == 1
                ? '0' + birthdayArr[2]
                : birthdayArr[2]
            var birthdayStr = year + '-' + month + '-' + day

            var data = {}
            data.origin = 'item_pc'
            data.originDevice = 1
            data.nickname = nickname
            data.birthdayStr = birthdayStr
            if (this.babyId) {
                data.id = this.babyId
            }
            if (sex) {
                data.sex = sex
            }
            if (height) {
                data.height = height
            }
            if (weight) {
                data.weight = weight
            }
            if (schoolAge) {
                data.schoolAge = schoolAge
            }

            var url = _this.isCreate ? URLS.SAVE : URLS.UPDATE
            if (_this.isSaving) {
                return
            }
            _this.isSaving = true
            $.ajax({
                url: url,
                dataType: 'jsonp',
                data: data,
                scriptCharset: 'utf-8',
                success: function(r) {
                    _this.isSaving = false
                    if (r.success == true) {
                        _this.isCreate = false
                        _this.el.remove()

                        var date = new Date(year, month - 1, day)
                        data.birthday = date.getTime()
                        _this.set([data])
                    } else {
                        alert(r.message)
                    }
                }
            })
        },
        checkLogin: function(cb) {
            if (typeof cb !== 'function') {
                return
            }
            var locname = window.location.hostname
            var locnameNum =  locname.split("item")[1] || ".jd.com"
            $.ajax({
                url: '//passport'+locnameNum+'/loginservice.aspx?method=Login',
                dataType: 'jsonp',
                success: function(r) {
                    if (r.Identity) {
                        cb(r.Identity)
                    }
                }
            })
        }
    }

    return Baby
})
