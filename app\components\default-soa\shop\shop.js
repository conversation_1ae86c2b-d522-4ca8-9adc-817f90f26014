define('MOD_ROOT/shop/shop', function (require, exports, module) {
  var IM = require('MOD_ROOT/contact/im');
  var Tools = require('MOD_ROOT/common/tools/tools')

  function init(cfg) {
    $('#similar').remove(); // 删除为你推荐

    var $shopInformation = $('.shop-information');
    var $shop = $shopInformation.children('.detail-shop');
    var $nameTags = $shop.find('.name-tag .flex');
    var shopUrl = $shop.attr('data-shop-url') // 直出的店铺地址
    var floorStyle = '0'

    // 店铺及标题隐藏
    $shop.hide().prev().hide()

    // 客服
    pageConfig.IMContact = new IM({
      cfg: cfg,
      $el: $('.customer-service'),
      trigger: '.im',
      template: '<div class="im">\
                  <img src="https://img14.360buyimg.com/imagetools/jfs/t1/266742/10/565/941/6764d381Fa0e75793/0906b84427a472cb.png" alt="" class="shop-icon">\
                  <div class="text">联系客服</div>\
                 </div>'
    });
    
    // 店铺标签先隐藏
    $nameTags.hide();

    // 点击图片和名称进入店铺页面
    $shop.click(function(e){
      if (['logo','name'].indexOf(e.target.className) > -1 && shopUrl) {
        window.open(shopUrl);
        if (['logo'].indexOf(e.target.className) > -1) {
          Tools.landmine({
            functionName: 'PC_Productdetail_ShopFloor_Click',
            exposureData: ['mainskuid'],
            extraData: {
              shopId: window.pageConfig.product.shopId,
              shopType: window.pageConfig.product.isSelf ? '0' : '1',
              floorStyle: floorStyle,
              buttonName: '店铺logo'
            },
            errorTips: '店铺楼层点击-异常'
          });
        }
        if (['name'].indexOf(e.target.className) > -1) {
          Tools.landmine({
            functionName: 'PC_Productdetail_ShopFloor_Click',
            exposureData: ['mainskuid'],
            extraData: {
              shopId: window.pageConfig.product.shopId,
              shopType: window.pageConfig.product.isSelf ? '0' : '1',
              floorStyle: floorStyle,
              buttonName: '店铺名称'
            },
            errorTips: '店铺楼层点击-异常'
          });
        }
      }
    })
    // 查看店铺
    $shop.find('.stroll').click(function(){
      shopUrl && window.open(shopUrl);

      Tools.landmine({
        functionName: 'PC_Productdetail_ShopFloor_Btn_Click',
        exposureData: ['mainskuid'],
        extraData: {
          shopId: window.pageConfig.product.shopId,
          shopType: window.pageConfig.product.isSelf ? '0' : '1',
          floorStyle: floorStyle,
          buttonName: '进店看看'
        },
        errorTips: '店铺楼层-进店看看按钮点击-异常'
      })
    });
    // 联系客服
    $shop.find('.customer-service').click(function(){
      Tools.landmine({
        functionName: 'PC_Productdetail_ShopFloor_Btn_Click',
        exposureData: ['mainskuid'],
        extraData: {
          shopId: window.pageConfig.product.shopId,
          shopType: window.pageConfig.product.isSelf ? '0' : '1',
          floorStyle: floorStyle,
          buttonName: '联系客服'
        },
        errorTips: '店铺楼层-联系客服按钮点击-异常'
      });
    });
    try{
      // 融合接口数据下发处理
      itemEventBus && itemEventBus.once('wareBusinessChange', function(wareBusiness){
        // 未下发店铺信息 移除相关dom
        if (!wareBusiness.itemShopInfo) {
          $('.tab-main').find('li').each(function (index, item) {
            var $this = $(item);
            if (($this.text() || '').indexOf('店铺') > -1) {
              $this.hide();
            }
          });
          $('.shop-information').hide();
          return;
        }


        var itemShopInfo = wareBusiness.itemShopInfo || {}

        // 店铺标签
        if (itemShopInfo.shopTabList) {
          var tagsTpl = '';
          itemShopInfo.shopTabList.forEach(function(item, index){
            if (index > 0) {
              tagsTpl = tagsTpl + '<div class="line"></div><div class="tag">'+ item.tabData +'</div>';
            }else {
              tagsTpl += '<div class="tag">'+ item.tabData +'</div>'
            }
          })

          $nameTags.show().children('.tag-list').append(tagsTpl)
        }else {
          $nameTags.show().children('.tag-list').remove()
        }

        // 店铺logo
        if (itemShopInfo.shopSquareLogo || itemShopInfo.shopLongLogo) {
          $shop.find('.logo').attr('src', itemShopInfo.shopSquareLogo || itemShopInfo.shopLongLogo)
        }else {
          $shop.find('.border-logo').hide();
        }
        
        // 店铺名称
        $shop.find('.name').html(itemShopInfo.shopName).attr('title', itemShopInfo.shopName)

        // pop店铺
        if (itemShopInfo.venderType === '0') {
          var shopGrade = itemShopInfo.shopGrade;
          if (shopGrade) {
            // 评价 物流 客服
            var taglist = [];
            if (shopGrade.evaluateGrade) {
              taglist.push({
                name: '商品评价',
                value: shopGrade.evaluateGrade
              })
            }
            if (shopGrade.logisticsGrade) {
              taglist.push({
                name: '物流速度',
                value: shopGrade.logisticsGrade
              })
            }
            if (shopGrade.customServiceGrade) {
              taglist.push({
                name: '客服表现',
                value: shopGrade.customServiceGrade
              })
            }

            var popTpl = '';
            if (taglist.length) {
              taglist.forEach(function (item,index) {
                if (index === 0) {
                  popTpl = '\
                    <div class="item">\
                      <div class="flex">\
                        <div class="text">'+ item.name +'</div>\
                        <div class="level">'+ item.value +'</div>\
                      </div>\
                    </div>'
                }else {
                  popTpl = popTpl + '\
                    <div class="line"></div>\
                    <div class="item">\
                      <div class="flex">\
                        <div class="text">'+ item.name +'</div>\
                        <div class="level">'+ item.value +'</div>\
                      </div>\
                    </div>'
                }
              })
            }
            
            $shop.find('.advantage').html(popTpl);

            try {
              // pop店铺星级
              var scoreRankRateGrade = shopGrade.scoreRankRateGrade;
              if (scoreRankRateGrade) {
                var $name = $shop.find('.name')
                if (itemShopInfo.isYearFiveStarTab) { // 年度五星店铺
                  $name.after('<img src="https://img12.360buyimg.com/imagetools/jfs/t1/254770/30/15677/8075/67a5ae91F42a60c9c/3937b9c4272edaab.png" class="year-five-star" />')
                } else if (scoreRankRateGrade == 5) { // 五星店铺
                  $name.after('<img src="//m.360buyimg.com/cc/jfs/t1/197706/29/27826/4384/63314bc6E21b4c3e9/8b4d5fc44efc5b3f.png" class="five-star" />')
                } else { // 默认展示星级
                  var decimalPart = scoreRankRateGrade.toString().split(".");
                  var complete = decimalPart[0];
                  var decimal = decimalPart[1];
                  var grayNum = 5 - complete;
                  var tpl = ''

                  if (decimal == 0) {
                    for (var index = 0; index < complete; index++) {
                      tpl += '<div class="star star-light"></div>';
                    }
                    for (var index = 0; index < grayNum; index++) {
                      tpl += '<div class="star star-gray"></div>';
                    }
                  }else {
                    for (var index = 0; index < complete; index++) {
                      tpl += '<div class="star star-light"></div>';
                    }
                    // 半颗星
                    tpl += '<div class="star star-half"></div>';
                    if (complete < 4) {
                      for (var index = 0; index < grayNum; index++) {
                        tpl += '<div class="star star-gray"></div>';
                      }
                    }
                  }
                  
                  $shop.find('.name').after('<div class="star-level">'+ tpl +'<div class="star-num">'+ scoreRankRateGrade +'</div></div>')
                }
              }
            } catch (error) {
              
            }
            
          } else {
            $nameTags.hide()
          }
        }

        // 自营 
        if (itemShopInfo.venderType === '1') {
          // 店铺关注数
          if (itemShopInfo.followCount) {
            var shopCountTpl = '';
            var shopCount = itemShopInfo.followCount;
            if(shopCount >= 100000000){
              shopCount = ((shopCount / 100000000).toFixed(1)).replace('.0', '') + '亿';
            } else if(shopCount >= 10000) {
              shopCount = ((shopCount / 10000).toFixed(1)).replace('.0', '') + '万';
            }

            shopCountTpl = '\
              <div class="item">\
                <div class="flex">\
                  <div class="text">店铺关注</div>\
                </div>\
                <div class="value">' + shopCount + '</div>\
              </div>';
            
            $shop.find('.advantage').append(shopCountTpl);
          }
        }

        // 商品分类
        if (itemShopInfo.hotCates) {
          floorStyle = '2'
          var listTpl = '';
          var hotCatesLen = itemShopInfo.hotCates.length;

          itemShopInfo.hotCates.forEach(function(item, index){
            listTpl += '\
              <li class="item hot-cate">\
                <div class="cate-img-wrap"></div>\
                <div class="name">'+ item.cname +'</div>\
              </li>';
          })

          var rightArrowTpl = '\
            <div class="hot-arrow right-arrow" id="cls-right-arrow">\
              <img src="https://img11.360buyimg.com/imagetools/jfs/t1/254955/16/4963/530/676e670fF0f862f1b/970f561bf58c4294.png" alt="" class="arrow" />\
            </div>'
          var leftArrowTpl = '\
            <div class="hot-arrow left-arrow" id="cls-left-arrow">\
              <img src="https://img13.360buyimg.com/imagetools/jfs/t1/268159/30/3998/538/676e66fcF50d57130/aa1646e03da3be5b.png" alt="" class="arrow" />\
            </div>'

          if (hotCatesLen <= 6) {
            leftArrowTpl = ''
            rightArrowTpl = ''
          }

          var cateTpl ='\
            <div class="detail-classification">\
              '+ leftArrowTpl +'\
              <div class="shop-cate-wrap">\
                <ul class="shop-cate-list">'+listTpl+'</ul>\
              </div>\
              '+ rightArrowTpl +'\
            </div>'
          
          $shopInformation.append(cateTpl);

          itemShopInfo.hotCates.forEach(function(item, index){
            var $newImage = $('<img>').addClass('cate-img').attr('src', item.imgPath || 'https://img10.360buyimg.com/imagetools/jfs/t1/253171/39/7055/1843/677352b3Fa5275ef9/fa6f23eec6222494.jpg');
            $newImage.bind('error', function() {
              $(this).unbind('error');
              $(this).attr('src','https://img10.360buyimg.com/imagetools/jfs/t1/253171/39/7055/1843/677352b3Fa5275ef9/fa6f23eec6222494.jpg');
            });
            $('.cate-img-wrap').eq(index).append($newImage);
          });

          if (hotCatesLen > 6) {
            $shopInformation.find('.shop-cate-wrap').imgScroll({
              width: 125,
              height: 171,
              visible: 6,
              direction: 'x',
              showControl: true,
              step: 6,
              loop: false,
              prev: '#cls-left-arrow',
              next: '#cls-right-arrow'
            })
          }

          var $hotcate = $shopInformation.find('.hot-cate');
          $hotcate.click(function () {
            shopUrl && window.open(shopUrl);

            var index = $hotcate.index($(this));
            Tools.landmine({
              functionName: 'PC_Productdetail_ShopFloor_Classification_Click',
              exposureData: ['mainskuid'],
              extraData: {
                shopId: window.pageConfig.product.shopId,
                classifyName: $(this).find('.name').text(),
                index: index
              },
              errorTips: '店铺楼层-店内商品分类-单分类点击-异常'
            });
          });

          var current = 1;

          function uploadData() {
            
            var start =(current - 1) * 6;
            var list = itemShopInfo.hotCates.slice((current - 1) * 6, current * 6);

            list.forEach(function (item, index) {
              Tools.exposure({
                functionName: 'PC_Productdetail_ShopFloor_Classification_Expo',
                exposureData: ['mainskuid'],
                extraData: {
                  shopId: window.pageConfig.product.shopId,
                  classifyName:item.cname,
                  index: start + index
                },
                errorTips: '商品主图区-首帧主图曝光异常'
              })
            })
          }

          uploadData()

          $shopInformation.find('.hot-arrow').click(function () {
            
            var buttonName = ''
            if ($(this).hasClass('left-arrow') && !$(this).hasClass('disabled')) {
              buttonName = 'forward'
            }
            if ($(this).hasClass('right-arrow') && !$(this).hasClass('disabled')) {
              buttonName = 'next'

              current += 1;
              uploadData()
            }

            if (!buttonName) {
              return;
            }

            Tools.landmine({
              functionName: 'PC_Productdetail_ShopFloor_RollBtn_Click',
              exposureData: ['mainskuid'],
              extraData: {
                buttonName: buttonName,
                shopId: window.pageConfig.product.shopId,
                shopType: window.pageConfig.product.isSelf ? '0' : '1',
                floorStyle: floorStyle
              },
              errorTips: '店铺楼层-店内商品-翻页按钮点击-异常'
            });
          });
          
        } else {
          // 店铺推荐商品
          if (itemShopInfo.shopGoods) {
            floorStyle = '1'
            var goodsTpl = ''
            var shopGoodsLen = itemShopInfo.shopGoods.length;

            itemShopInfo.shopGoods.forEach(function(item, index){
              goodsTpl += '\
                <li class="item">\
                  <div class="tag">上新</div>\
                  <div class="goods-img-wrap">\
                  </div>\
                  <div class="flex">\
                    <div class="price">'+ item.skuPrice +'</div>\
                    <img src="https://img14.360buyimg.com/imagetools/jfs/t1/261097/38/4038/959/676e6755F87046d57/add1052dd717c216.png" alt="" class="add-cart" id="goods-add-cart'+ index +'" skuId="'+ item.skuId +'"/>\
                  </div>\
                </li>';
            })

            var rightArrowTpl = '\
              <div class="goods-arrow right-arrow" id="goods-right-arrow">\
                <img src="https://img11.360buyimg.com/imagetools/jfs/t1/254955/16/4963/530/676e670fF0f862f1b/970f561bf58c4294.png" alt="" class="arrow" />\
              </div>'
            var leftArrowTpl = '\
              <div class="goods-arrow left-arrow" id="goods-left-arrow">\
                <img src="https://img13.360buyimg.com/imagetools/jfs/t1/268159/30/3998/538/676e66fcF50d57130/aa1646e03da3be5b.png" alt="" class="arrow" />\
              </div>'
            
              if (shopGoodsLen <= 6) {
                leftArrowTpl = ''
                rightArrowTpl = ''
              }

            var tpl = '\
              <div class="detail-goods">\
              '+ leftArrowTpl +'\
                <div class="shop-goods-wrap">\
                  <ul class="shop-goods-list">'+ goodsTpl +'</ul>\
                </div>\
              '+ rightArrowTpl +'\
              </div>';
      
            $shopInformation.append(tpl);

            itemShopInfo.shopGoods.forEach(function(item, index){
              var $newImage = $('<img>').addClass('goods-img').attr({
                'src':item.skuPic || 'https://img10.360buyimg.com/imagetools/jfs/t1/253171/39/7055/1843/677352b3Fa5275ef9/fa6f23eec6222494.jpg',
                skuId:item.skuId
              });
              $newImage.bind('error', function() {
                $(this).unbind('error');
                $(this).attr('src','https://img10.360buyimg.com/imagetools/jfs/t1/253171/39/7055/1843/677352b3Fa5275ef9/fa6f23eec6222494.jpg');
              });
              $('.goods-img-wrap').eq(index).append($newImage);
            });

            if (shopGoodsLen > 6) {
              $shopInformation.find('.shop-goods-wrap').imgScroll({
                width: 125,
                height: 171,
                visible: 6,
                direction: 'x',
                showControl: true,
                step: 6,
                loop: false,
                prev: '#goods-left-arrow',
                next: '#goods-right-arrow'
              })
            }

            // 加车
            var $addcart = $shopInformation.find('.add-cart')
            $addcart.click(function(){
              var skuId = $(this).attr('skuId');
              try {
                pageConfig.flyToCart && pageConfig.flyToCart({
                  img: $(this).parent().prev().find('img').attr('src'),
                  id: $(this).attr('id'),
                  apiBody: {
                    appid: 'item-v3',
                    directOperation: {
                      source: 'common',
                      theSkus: [{ skuId: skuId, num: '1' }]
                    }
                  }
                });
              } catch (e) {
                console.error('店铺商品推荐加车异常:', e)
              }

              var index = $addcart.index($(this));
              Tools.landmine({
                functionName: 'PC_Productdetail_ShopFloor_AddCart_Click',
                exposureData: ['mainskuid'],
                extraData: {
                  shopId: window.pageConfig.product.shopId,
                  skuid: skuId,
                  index: index
                },
                errorTips: '店铺楼层-店内商品推荐-单个商品加购按钮点击-异常'
              });
            })

            // 点击主图
            var $goodsimg = $shopInformation.find('.goods-img');
            $goodsimg.click(function(){
              
              var skuId = $(this).attr('skuId');
              skuId && window.open(location.origin + '/' + skuId +'.html');
              
              var index = $goodsimg.index($(this));
              Tools.landmine({
                functionName: 'PC_Productdetail_ShopFloor_Product_Click',
                exposureData: ['mainskuid'],
                extraData: {
                  shopId: window.pageConfig.product.shopId,
                  skuid: skuId,
                  index: index
                },
                errorTips: '店铺楼层-店内商品推荐-单个商品点击-异常'
              });
            })

            var current = 1;

            function uploadData() {
              var start =(current - 1) * 6;
              var list = itemShopInfo.shopGoods.slice((current - 1) * 6, current * 6);
    
              list.forEach(function (item, index) {
                Tools.exposure({
                  functionName: 'PC_Productdetail_ShopFloor_Product_Expo',
                  exposureData: ['mainskuid'],
                  extraData: {
                    shopId: window.pageConfig.product.shopId,
                    skuid: item.skuId + '',
                    index: start + index
                  },
                  errorTips: '店铺楼层-店内商品推荐-单商品曝光-异常'
                })
              })
            }
    
            uploadData()

            // 左右箭头点击
            $shopInformation.find('.goods-arrow').click(function () {
              var buttonName = ''
              if ($(this).hasClass('left-arrow') && !$(this).hasClass('disabled')) {
                buttonName = 'forward'
              }
              if ($(this).hasClass('right-arrow') && !$(this).hasClass('disabled')) {
                buttonName = 'next'

                current += 1;
                uploadData()
              }

              if (!buttonName) {
                return;
              }
    
              Tools.landmine({
                functionName: 'PC_Productdetail_ShopFloor_RollBtn_Click',
                exposureData: ['mainskuid'],
                extraData: {
                  buttonName: buttonName,
                  shopId: window.pageConfig.product.shopId,
                  shopType: window.pageConfig.product.isSelf ? '0' : '1',
                  floorStyle: floorStyle
                },
                errorTips: '店铺楼层-店内商品-翻页按钮点击-异常'
              });
            });
          }
        }

        $shop.show().prev().show()

        Tools.exposure({
          functionName: 'PC_Productdetail_ShopFloor_Expo',
          exposureData: ['mainskuid'],
          extraData: {
            shopId: window.pageConfig.product.shopId,
            shopType: window.pageConfig.product.isSelf ? '0' : '1',
            floorStyle: floorStyle
          },
          errorTips: '店铺楼层整体曝光-异常'
        })
      });
    }catch(e){
      console.error("店铺报错wareBusinessChange异常:",e)
    }
    try{
      // 监听评价接口
      itemEventBus && itemEventBus.once('commentChange', function(comment) {
        // 店铺评价数
        if (window.pageConfig.product.isSelf && comment && comment.allCntStr) {
          var pjCountTpl = '';

          pjCountTpl = '\
            <div class="item">\
              <div class="flex">\
                <div class="text">累计评价</div>\
              </div>\
              <div class="value">'+ comment.allCntStr +'</div>\
            </div>'

          $shop.find('.advantage').prepend(pjCountTpl+ '<div class="line"></div>');
        }
      });
    }catch(e){
      console.error("监听评价接口wcommentChange异常:",e)
    }
    try{
      itemEventBus && itemEventBus.dispatchEmit('commentGet')
    }catch(e){
      console.error("commentGet异常:",e)
    }
  }

  module.exports.init = init;
});


