define('PUBLIC_ROOT/modules/ie6Tip/ie6Tip', function(require, exports, module) {

    var dialog = require('JDF_UI/dialog/1.0.0/dialog');

    var template = '\
    <div id="ie6tip">\
        <div class="left-con">\
            <div></div>\
        </div>\
        <div class="right-con">\
            <div class="tip1">您的浏览器版本过低，可能存在安全风险</div>\
            <div class="tip2">建议您使用以下浏览器：</div>\
            <ul class="clearfix">\
                <li class="chrome">\
                    <a class="img" href="http://www.google.cn/chrome/browser" target="_blank"></a>\
                    <div class="title">谷歌浏览器</div>\
                    <div class="btn"><a href="http://www.google.cn/chrome/browser" target="_blank">下载</a></div>\
                </li>\
                <li class="qq">\
                    <a class="img" href="http://browser.qq.com" target="_blank"></a>\
                    <div class="title">QQ浏览器</div>\
                    <div class="btn"><a href="http://browser.qq.com" target="_blank">下载</a></div>\
                </li>\
                <li class="ie">\
                    <a class="img" href="http://www.microsoft.com/zh-cn/download/internet-explorer.aspx" target="_blank"></a>\
                    <div class="title">IE浏览器</div>\
                    <div class="btn"><a href="http://www.microsoft.com/zh-cn/download/internet-explorer.aspx" target="_blank">下载</a></div>\
                </li>\
            </ul>\
        </div>\
    </div>';

    function initTip() {
        this.dialog1 = $('body').dialog({
            width: 690,
            height: 320,
            title: '安全提示',
            type: 'html',
            source: template
        });
    }

    function init() {
        initTip();
    }

    module.exports.__id = 'ie6Tip';
    exports.init = init;
});
