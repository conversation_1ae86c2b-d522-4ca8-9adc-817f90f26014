{% extends './layout/layout.html' %}

{% block style %}
    {{ super() }}
    {{ _components | source('link') }}
{% endblock %}

{% block script %}
    {{ super() }}
{% endblock %}

{% block bodyClass %}decoration{% endblock %}

{# 测试商品 - pop手机 - 「赛博宇华(SOP) M6睿变 移动4G手机 双卡双待 土豪金」 #}
{# setGlobal
    skuid = 1550694404,
    name = "赛博宇华(SOP) M6睿变 移动4G手机 双卡双待 土豪金",
    cat = [9987, 653, 655],
    brand = 102501,
    venderId = 116458,
    shopId = 114288,
    colorSize = [{"SkuId":1550694404,"Color":"土豪金","Size":"*","Spec":"*"},{"SkuId":10022305517,"Color":"玫瑰金","Size":"*","Spec":"*"}],
#}

{% setGlobal
    skuid = 1578303025,
    name = "Apple iPhone 6s Plus (A1699) 64G 玫瑰金色 移动联通电信4G手机",
    cat = [9987,653,655],
    brand = 14026,
    venderId = 0,
    shopId = 0,
    colorSize = [{"Color":"金色","Spec":"16GB","SkuId":1861091,"Size":"公开版"},{"Color":"银色","Spec":"16GB","SkuId":1861092,"Size":"公开版"},{"Color":"深空灰","Spec":"16GB","SkuId":1861093,"Size":"公开版"},{"Color":"玫瑰金","Spec":"16GB","SkuId":1861094,"Size":"公开版"},{"Color":"金色","Spec":"16GB","SkuId":1861121,"Size":"移动4G"},{"Color":"银色","Spec":"16GB","SkuId":1861122,"Size":"移动4G"},{"Color":"深空灰","Spec":"16GB","SkuId":1861123,"Size":"移动4G"},{"Color":"玫瑰金","Spec":"16GB","SkuId":1861124,"Size":"移动4G"},{"Color":"银色","Spec":"64GB","SkuId":1861096,"Size":"公开版"},{"Color":"深空灰","Spec":"64GB","SkuId":1861097,"Size":"公开版"},{"Color":"金色","Spec":"64GB","SkuId":1861095,"Size":"公开版"},{"Color":"玫瑰金","Spec":"64GB","SkuId":1861098,"Size":"公开版"},{"Color":"深空灰","Spec":"64GB","SkuId":1861127,"Size":"移动4G"},{"Color":"深空灰","Spec":"128GB","SkuId":1861101,"Size":"公开版"},{"Color":"玫瑰金","Spec":"128GB","SkuId":1861102,"Size":"公开版"},{"Color":"金色","Spec":"128GB","SkuId":1861099,"Size":"公开版"},{"Color":"银色","Spec":"128GB","SkuId":1861100,"Size":"公开版"}]
%}

{% setGlobal specialAttrs = [
"isFlashPurchase-0","isSupportCard","isCanVAT","isPickingGoods-0",
"isHaveYB","packType","isCanUseDQ-1","IsNewGoods",
"isSelfService-0","isCanUseJQ-1","isWeChatStock-0","HYKHSP-0",
"isOverseaPurchase-0","is7ToReturn-1","isNSNGgoods-0","YYSLLZC-0"] %}

{% block pageConfig %}
    {#{% inject 'http://baidu.com' %}#}
    <script>
        var pageConfig = {
            compatible: true,
            product: {
                skuid: {{ skuid }},
                name: "{{ name }}",
                skuidkey:'BDAAEDC50DD82AEA7933E809E90D9CBB',
                href: '//item.jd.com/{{ skuid }}.html',
                src: 'jfs/t979/184/783186292/132287/74a8f387/5549d1dfNae298611.jpg',
                cat: {{ cat | dump }},
                brand: {{ brand }},
                pType: 1,
                isClosePCShow: false,
                venderId: {{ venderId }},
                shopId:'{{ shopId }}',
                commentVersion:'212',
                specialAttrs: {{ specialAttrs | dump }},
                easyBuyUrl:"//easybuy.jd.com/skuDetail/newSubmitEasybuyOrder.action",
                colorSize: {{ colorSize | dump }},
                isOtc: false,
                desc: '//d.3.cn/desc/{{ skuid }}?cdn=1',
                foot: '//d.3.cn/footer?type=common_config2',
                modules: [ 'address', 'prom', 'pingou', 'colorsize', 'buytype', 'baitiao', 'buybtn',
                    'suits', 'crumb', 'fittings', 'detail', 'sidereco', 'contact', 'info', 'popbox', 'preview' ]
            }
        };
    </script>
{% endblock %}

{% block body %}

    <div class="crumb-wrap">
        <div class="w">
            {% component 'crumb' %}
            {% component 'contact' %}
            <div class="clr"></div>
        </div>
    </div>

    <div class="product-intro">
        <div class="w clearfix">
            <div class="preview-wrap">
                {% component 'preview' %}
            </div>
            <div class="itemInfo-wrap">
                {% component 'prom' %}
                <div class="summary p-choose-wrap">
                    {% component 'address' %}
                    {% component 'colorsize' %}
                    {% component 'buytype' %}
                    {% component 'suits' %}
                    {% component 'jdservice' %}

                    <div id="choose-luodipei" class="choose-luodipei li">
                        <div class="dt">选择服务</div>
                        <div class="dd"></div>
                    </div>

                    {% component 'baitiao' %}
                    {% component 'city' %}
                    {% component 'buybtn' %}
                    {% component 'info' %}
                </div>
            </div>
        </div>
    </div>



    <div class="w">
        {% component 'similar' %}
    </div>

    <div class="w">
        {% component 'fittings' %}
    </div>

    <div class="w">
        <div class="aside">
            {% component 'popbox' %}
            {% component 'imcenter' %}
            {% component 'seek' %}
            {% component 'sidereco' %}
        </div>
        <div class="detail">
            {% component 'detail' %}

            {% component 'guarantee' %}

            {% component 'comment' %}

            {% component 'consult' %}

            {% component 'club' %}
            <!--问答-->
            {% component 'askAnswer' %}
        </div>
        <div class="clb"></div>
    </div>

    <div id="placeholder-floatnav-stop"></div>
{% endblock %}


