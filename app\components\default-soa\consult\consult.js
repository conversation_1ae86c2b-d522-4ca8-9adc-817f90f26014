define('MOD_ROOT/consult/consult', function(require, exports, module) {
    var pager = require('JDF_UI/pager/1.0.0/pager');

    require('MOD_ROOT/ETab/ETab');

    var consult_TPL = '\
    <ul>\
        {for item in Consultations}\
        <li>\
            <dl class="ask">\
                <dt>咨询内容：</dt>\
                <dd>\
                    <a class="u-content" target="_blank" href="//club.jd.com/consultation/${item.ProductId}-${item.Id}.html">\
                        ${item.Content}\
                    </a>\
                    <span class="u-name">\
                        （${item.UNickNme}\
                        <span class="u-level" style="color:${item.UserLevelColor}"> ${item.UserLevelName} </span>）\
                    </span>\
                </dd>\
                <dd class="u-date">${item.CreationTime}</dd>\
            </dl>\
            <dl class="answer">\
                {for Reply in item.Replies}\
                <dt>{if Reply.sst == 2}卖家 {else}京东{/if}回复：</dt>\
                <dd class="u-content">${Reply.sword}</dd>\
                <dd class="u-date">${Reply.sinsdate}</dd>\
                {/for}\
            </dl>\
        </li>\
        {forelse}\
        <div class="norecode">暂无该类咨询！</div>\
        {/for}\
    </ul>\
    <div class="c-total">共有<em class="c-totaliNum">${SearchParameter.Count}</em>个咨询 <a href="//club.jd.com/allconsultations/${SearchParameter.ProductId}-1-1.html" target="_blank">查看全部咨询 &gt;&gt;</a></div>';


    var consult_search_TPL = '\
    <ul>\
        {for item in list}\
        <li>\
            <dl class="ask">\
                <dt>咨询内容：</dt>\
                <dd>\
                    <span class="u-content" target="_blank">\
                        ${item.sword}\
                    </span>\
                </dd>\
                <dd class="u-date">${item.sindate}</dd>\
            </dl>\
            <dl class="answer">\
                <dt>京东回复：</dt>\
                <dd class="u-content">${item.sword2}</dd>\
                <dd class="u-date">${item.sindate2}</dd>\
            </dl>\
        </li>\
        {/for}\
    </ul>\
    <div id="consult-pagenav" class="ui-page-wrap hide clearfix"><div class="ui-page"></div></div>';

    var Consult = {
        init: function (cfg, $el, sku) {
            this.$el = $el;
            this.sku = sku;
            this.cfg = cfg;

            this.bindEvent();

            return this;
        },
        bindEvent: function() {
            var _this = this;

            this.$el.ETab({
                onSwitch: function (n) {
                    if (!this.items.eq(n).data('loaded') && n < 5) {
                        _this.getData(n);
                        this.items.eq(n).data('loaded', true)
                    }
                }
            });
            this.tab = this.$el.data('ETab');

            this.$el.find('.search')
                .undelegate()
                .delegate('#btnReferSearch', 'click', function() {
                    _this.search( _this.sku, $('#txbReferSearch').val(), 1, 6 );
                })
                .delegate('#backConsultations', 'click', function() {
                    _this.tab.go(0);
                });
        },
        getData: function(n) {
            var _this = this;

            var sku = typeof _this.cfg.isShadowSku != 'undefined' ? _this.cfg.isShadowSku : _this.sku;

            $.ajax({
                url: '//club.jd.com/clubservice/newconsulation-' + sku + '-' + (n+1) + '.html',
                dataType: 'jsonp',
                jsonpCallback: 'fetchJSON_Consult',
                cache: true,
                success: function(data) {
                    _this.setData(n, data);
                }
            });
        },
        setData: function(n, data) {
            if(data.Consultations && data.Consultations.length > 5) {
                data.Consultations.length = 5;
            }
            this.tab.items.eq(n).html( consult_TPL.process(data) );

            //var im = new IM({
            //    sku: G.sku,
            //    $el: $('#J-im-extra'),
            //    trigger: '.item',
            //    template: '<span class="item"><a id="j-im" class="jd-im" href="#none"><b>{text}</b></a></span>'
            //});

        },
        setExtraData: function(n, obj) {
            // 后三个tab内容
            $.jmsajax({
                url: "/newsserver.asmx",
                method: "PayExplain",
                data: {
                    id: 'A-product-0' + (n-3)
                },
                success: function (data) {
                    if (data != null) {
                        var iframeHeight = pageConfig.compatible && pageConfig.wideVersion ? 770 : 1450;
                        if (n===6) {
                            $('#consult-6').html('<iframe src="//psfw.jd.com/help/deliveryService.html" style="margin-top:10px;width:100%;height:'+ iframeHeight +'px;border:none;"></iframe>');
                        } else {
                            obj.html(data)
                        }
                    }
                }
            });
        },
        search: function(wid, keyword, page) {
            var _this = this;
            page = page || 0;
            wid = wid || this.sku;

            $.ajax({
                url: '//search.jd.com/sayword?',
                dataType: 'jsonp',
                data: {
                    wid: wid,
                    keyword: encodeURI(keyword),
                    page: page,
                    ps: 5
                },
                success: function( data ) {
                    _this.setSearchData(page, data);
                }
            });
        },
        setSearchData: function (page, data) {
            var searchResultTpl = '\
                <div id="consult-result" class="result clearfix">\
                    <div class="fl">\
                        <p>为您找到相关咨询<strong>{0}</strong>条 \
                        <a id="backConsultations" href="#none">返回</a></p>\
                    </div>\
                </div>';

            var $target = this.tab.items.eq(5);
            var _this = this;

            if ( !data.length ) {
                $target.html('<div class="norecode">暂无该类咨询！</div>');
            }

            var searchResult = searchResultTpl.format(data[0].total);
            var result = searchResult + consult_search_TPL.process(data[0]);

            this.tab.go(5);
            $target.html(result);

            var $pager = $('#consult-pagenav');
            $pager.show();
            $pager.find('.ui-page').pager({
                total: data[0].total,
                pageSize:10,
                currentPageClass:'ui-page-curr',
                currentPage: page,
                prevClass:'ui-pager-prev',
                nextClass:'ui-pager-next',
                prevText : '上一页',
                nextText : '下一页',
                pageHref: '#consult',
                callback:function(pageId){
                    _this.search( _this.sku, $('#txbReferSearch').val(), pageId, 6 );
                }
            });


        }
    };

    function init(cfg) {
        Consult.init(cfg, $('#consult'), cfg.skuid).getData(0);
    }

    module.exports.__id = 'consult';
    module.exports.init = init;
});
