// @import '../common/pager';
// @import '../common/lib';
// @import './__sprite';

// #comment {
//     .more-link {
//         float: right;
//         margin-top: -19px;
//     }
// }
// .comment {
//     .tag-available span {
//         cursor: pointer;
//         &.tag-1:hover,&.tag-2:hover {
//             color: #666;
//             border-color: #666;
//             background-color: transparent;
//         }
//         &.tag-1 {
//             color: #999;
//             border-color: #e0e0e0;
//         }
//         &.tag-1.selected {
//             color: #e4393c;
//             border-color: #e4393c;
//         }
//         &.tag-2 {
//             background: #ebebeb;
//         }
//         &.tag-2.selected,&.tag-2.selected:hover {
//             color: #088000;
//             border-color: #088000;
//             background-color: transparent;
//         }
//     }
//     .tag-list {
//         height: 22px;
//         overflow: hidden;
//         span {
//             display: inline-block;
//             line-height: 20px;
//             padding: 0 9px;
//             border: 1px solid #e0e0e0;
//             border-radius: 2px;
//             margin-right: 10px;
//             margin-bottom: 10px;
//             color: #999;
//         }
//         .default {
//             background: #ebebeb;
//             &:hover {
//                 color: #666;
//                 border-color: #666;
//                 background: #fff;
//             }
//             &.selected {
//                 color: #088000;
//                 border-color: #088000;
//                 background: #fff;
//             }
//         }
//     }

//     .comment-info {
//         overflow: hidden;
//         zoom: 1;
//         padding: 25px 0;
//         .comment-percent {
//             width: 90px;
//             padding: 15px 0 0 40px;
//             float: left;
//         }
//         .percent-tit {
//             font-size: 12px;
//             color: #666;
//             font-weight: normal;
//         }
//         .percent-con {
//             line-height: 110%;
//             font-size: 45px;
//             color: #E4393C;
//             font-family: arial;
//             span {
//                 font-size: 23px;
//             }
//         }
//         .percent-info {
//             margin-left: 90px;
//             .tag-list {
//                 height: auto;
//                 overflow: visible;
//             }
//         }
//         .notag {
//             background: url(i/notag-logo.png) no-repeat;
//             width: 320px;
//             height: 58px;
//             margin: 16px auto 0 auto;
//             padding-top: 19px;
//             padding-left: 98px;
//             color: #999;
//         }
//         .empty-rate {
//             padding-top: 34px;
//         }
//         .percent-rate {
//             padding-top: 15px;
//             height: 60px;
//             overflow: hidden;
//             zoom: 1;
//         }
//         .rate-item {
//             width: 145px;
//             margin-right: 15px;
//             float: left;
//             _display: inline;
//             strong {
//                 font-weight: normal;
//             }
//         }
//         .rate-wrap {
//             height: 10px;
//             margin: 7px 0;
//             overflow: hidden;
//             border-radius: 5px;
//             background: #ebebeb;
//         }
//         .inner-rate {
//             height: 10px;
//             overflow: hidden;
//             border-radius: 5px;
//             background: #e4393c;
//         }
//         .rate-info {
//             color: #e4393c;
//             span {
//                 color: #999;
//                 float: right;
//             }
//         }
//     }
//     .comments-list {
//         min-height: 93px;
//         .ui-page-wrap {
//             text-align: right;
//             padding-top: 15px;
//         }
//     }
//     .sort-select {
//         position: relative;
//         white-space: nowrap;
//         line-height: 30px;
//         margin-right: 10px;
//         .current {
//             margin-right: 10px;
//             padding: 0 10px;
//             span {
//                 display: inline-block;
//                 *display: inline;
//                 *zoom: 1;
//                 height: 30px;
//                 vertical-align: top;
//             }
//             i {
//                 display: inline-block;
//                 *display: inline;
//                 *zoom: 1;
//                 width: 16px;
//                 height: 30px;
//                 background: url(i/commentsListIcons1.png) no-repeat 1px -19px;
//                 _background: url(i/commentsListIcons1-8.png)
//                     no-repeat
//                     1px -19px;
//                 vertical-align: top;
//             }
//         }
//         &:hover {
//             .others {
//                 display: block;
//             }
//         }
//         .others {
//             position: absolute;
//             top: -1px;
//             right: 9px;
//             background: #fff;
//             border: 1px solid #ccc;
//             box-shadow: 0px 0px 2px 0px #888;
//             display: none;
//             .curr {
//                 padding: 0 10px;
//                 span {
//                     display: inline-block;
//                     *display: inline;
//                     *zoom: 1;
//                     height: 30px;
//                     vertical-align: top;
//                 }
//                 i {
//                     display: inline-block;
//                     *display: inline;
//                     *zoom: 1;
//                     width: 16px;
//                     height: 30px;
//                     background: url(i/commentsListIcons1.png)
//                         no-repeat
//                         1px -42px;
//                     _background: url(i/commentsListIcons1-8.png)
//                         no-repeat
//                         1px -42px;
//                     vertical-align: top;
//                 }
//             }
//             ul {
//                 margin-top: 0;
//                 li {
//                     cursor: pointer;
//                     padding: 0 20px 0 10px;
//                     display: block;
//                     &:hover {
//                         color: #e4393c;
//                         background-color: #e6e6e6;
//                     }
//                 }
//             }
//         }
//         &.disable {
//             .current i {
//                 visibility: hidden;
//             }
//             .others {
//                 display: none;
//             }
//         }
//     }
//     .comment-item {
//         zoom: 1;
//         padding: 15px;
//         border-bottom: 1px solid #ddd;
//         .tag-list {
//             padding-bottom: 12px;
//             span {
//                 cursor: text;
//                 &:hover {
//                     color: #999;
//                     border-color: #e0e0e0;
//                 }
//             }
//         }
//         .user-column {
//             width: 140px;
//             float: left;
//         }
//         .user-info {
//             white-space: nowrap;
//             text-overflow: ellipsis;
//             overflow: hidden;
//             img {
//                 border-radius: 50%;
//                 margin-right: 5px;
//             }
//         }
//         .user-level {
//             padding-top: 3px;
//             span {
//                 color: #088000;
//                 margin-right: 8px;
//             }
//             .comment-plus-icon {
//                 display:inline-block;
//                 padding: 0 4px;
//                 font-size:12px;
//                 line-height:18px;
//                 background:#414141;
//                 color:#fff38f;
//                 border-radius: 1px;
//                 margin:5px 5px 0 0;
//             }
//         }
//         .comment-column {
//             margin-left: 150px;
//         }
//         .comment-star {
//             width: 78px;
//             height: 14px;
//             background: url(i/star.png) no-repeat;
//         }
//         .star0 {
//             background-position: -80px 0;
//         }
//         .star1 {
//             background-position: -64px 0;
//         }
//         .star2 {
//             background-position: -48px 0;
//         }
//         .star3 {
//             background-position: -32px 0;
//         }
//         .star4 {
//             background-position: -16px 0;
//         }
//         .star5 {
//             background-position: 0 0;
//         }
//         .comment-con {
//             font-size: 14px;
//             padding: 10px 0;
//             line-height: 180%;
//             color: #333;
//         }
//         .order-info {
//             float: left;
//             color: #999;
//             span {
//                 margin-right: 20px;
//             }
//             .user-access {
//                 margin-right: 0;
//             }
//             a {
//                 color: #999;
//                 &:hover {
//                     color: #e4393c;
//                 }
//             }
//             .source {
//                 position: relative;
//             }
//             .user-item {
//                 display: inline;
//             }
//             .p-tooltips {
//                 position: relative;
//                 margin-right: 0;
//                 span {
//                     margin-right: 0;
//                 }
//             }
//             .source-layer {
//                 display: none;
//                 width: 438px;
//                 height: 209px;
//                 padding: 15px 0 15px 15px;
//                 position: absolute;
//                 left: -150px;
//                 top: 22px;
//                 background: #fff;
//                 border: 1px solid #cecbce;
//                 color: #666;
//                 box-shadow: 0 0 2px 2px #eee;
//             }
//             .sprite-arrow {
//                 @include sprite-arrow;
//                 position: absolute;
//                 top: -6px;
//                 left: 40%;
//             }
//             .hover .source-layer {
//                 display: block;
//                 z-index: 1;
//             }
//         }
//         .comment-op {
//             float: right;
//             i {
//                 margin-right: 5px;
//                 vertical-align: 1px;
//                 vertical-align: -2px;
//             }
//             a {
//                 display: inline-block;
//                 margin-left: 25px;
//                 color: #666;
//                 &:hover {
//                     color: #e4393c;
//                     .sprite-praise {
//                         @include sprite-praised;
//                     }
//                     .sprite-comment {
//                         display: inline-block;
//                         @include sprite-commented;
//                     }
//                 }
//             }
//             .praised {
//                 color: #e4393c;
//                 .sprite-praise {
//                     @include sprite-praised;
//                 }
//             }
//         }
//         .sprite-praise {
//             display: inline-block;
//             @include sprite-praise;
//         }
//         .sprite-comment {
//             display: inline-block;
//             @include sprite-comment;
//         }

//         .pic-list {
//             padding-bottom: 15px;
//             a{
//                 display: inline-block;
//                 position: relative;
//             }
//             img {
//                 border: 1px solid #e2e2e2;
//                 padding: 1px;
//                 margin-right: 6px;
//             }
//             .video-icon{
//                 display: inline-block;
//                 width:26px;
//                 height:26px;
//                 position: absolute;
//                 top:50%;
//                 left:50%;
//                 margin-top:-13px;
//                 margin-left:-15px;
//                 background: url(i/main-circles.png) 50% 50% no-repeat;
//             }
//             .current {
//                 img {
//                     border: 2px solid #e53e41;
//                     padding: 0;
//                 }
//             }
//         }
//         .video-js{
//             height:420px;
//             width:370px;
//         }
//         .video-js:focus{
//             height:420px;
//             width:370px;
//             outline: none;
//         }
//         .pic-view {
//             position: relative;
//             border: 1px solid #e3e3e3;
//             margin: 7px 0 11px;
//             float: left;
//             img {
//                 max-width: 370px;
//                 max-height: 478px;
//             }
//             .cursor-prev, .cursor-next, .cursor-small {
//                 width: 100px;
//                 position: absolute;
//                 height: 100%;
//                 top: 0;
//                 background: fixed url(about:blank);
//                 z-index: 3;
//             }
//             .cursor-prev {
//                 left: 0;
//                 cursor: url(i/prev2.cur), auto;
//                 :root & {
//                     cursor: url(i/prev.cur), auto;
//                 }
//             }
//             .cursor-next {
//                 right: 0;
//                 cursor: url(i/next2.cur), auto;
//                 :root & {
//                     cursor: url(i/next.cur), auto;
//                 }
//             }
//             .cursor-small {
//                 width: 100%;
//                 left: 0;
//                 cursor: url(i/small.cur), auto;
//                 z-index: 2;
//             }
//             .pic-op {
//                 position: absolute;
//                 left: 5px;
//                 top: 5px;
//                 z-index: 6;
//                 a {
//                     display: inline-block;
//                     height: 26px;
//                     line-height: 26px;
//                     padding: 0 5px;
//                     border-radius: 4px;
//                     background: rgba(0, 0, 0, .5);
//                     margin-right: 12px;
//                     color: #fff;
//                     *backgdround: #000;
//                     *filter: alpha(opacity=50);
//                 }
//                 .sprite-turn-right, .sprite-turn-left {
//                     display: inline-block;
//                     margin-right: 5px;
//                     vertical-align: -3px;
//                 }
//                 .sprite-turn-right {
//                     @include sprite-turn-right;
//                 }
//                 .sprite-turn-left {
//                     @include sprite-turn-left;
//                 }
//             }
//         }
//         .recomment-con {
//             font-size: 14px;
//             margin-top: 12px;
//             border-top: 1px solid #f0f0f0;
//         }
//         .recomment {
//             padding: 12px 0 5px;
//             color: #ff561c;
//             line-height: 170%;
//         }
//         .comment-time {
//             color: #999;
//             clear: both;
//         }
//         .append-comment {
//             border-top: 1px solid #f0f0f0;
//             margin-top: 12px;
//             padding-top: 12px;
//             .comment-con {
//                 padding-top: 5px;
//             }
//             .pic-list {
//                 padding-bottom: 8px;
//             }
//         }
//         .append-time {
//             color: #999;
//         }
//     }
//     .comment-message:after {
//         content: ".";
//         height: 0;
//         visibility: hidden;
//         display: block;
//         clear: both;
//     }
// }
// .comment-more {
//     position: relative;
//     height: 46px;
//     font-size: 0;
//     margin: 10px 0;
//     background: #f7f7f7;
//     text-align: center;
//     line-height: 46px;
//     span {
//         display: inline-block;
//         color: #999;
//         font-size: 12px;
//     }
//     a {
//         display: inline-block;
//         margin-left: 11px;
//         font-size: 12px;
//     }
//     .comment-up-triangle {
//         position: absolute;
//         width: 0;
//         height: 0;
//         top: -12px;
//         left: 50%;
//         margin-left: -6px;
//         border-width: 0 12px 12px;
//         border-color: transparent transparent #f7f7f7;
//         border-style: solid;
//     }
//     .sprite-up, .sprite-down {
//         display: inline-block;
//         margin-left: 5px;
//     }
//     .sprite-up {
//         @include sprite-up;
//     }
//     .sprite-down {
//         @include sprite-down;
//     }
// }
// .comment-default-good-reputation {
//     height: 42px;
//     line-height: 42px;
//     text-align: center;
//     margin-bottom: -10px;
//     span {
//         color: #999;
//     }
// }
// #fold-comment.comment-layer {
//     overflow-y: auto;
//     overflow-x: hidden;
//     height: 100%;
//     .J-nice,
//     .J-report {
//         display: none;
//     }
//     .comment-item {
//         padding: 10px;
//     }
//     .comment-con {
//         line-height: 1.5em;
//         // white-space: nowrap;
//         // width: 660px;
//         overflow: hidden;
//         // -ms-text-overflow: ellipsis;
//         // text-overflow: ellipsis;
//     }
//     .com-table-footer {
//         text-align: right;
//         margin-top: 15px;
//     }
// }
// .comments-showImgSwitch-wrap {
//     padding: 12px 0;
//     margin: 0 10px 10px 10px;

//     .thumbnails {
//         height: 85px;
//         margin: 0 auto 10px;
//         position: relative;

//         .thumb-list {
//             width: 610px;
//             height: 85px;
//             margin: 0 auto;
//             overflow: hidden;
//             ul {
//                 width: 100000px;

//                 li {
//                     float: left;
//                     width: 80px;
//                     height: 85px;
//                     margin-right: 8px;

//                     a {
//                         display: block;
//                         width: 76px;
//                         height: 76px;
//                         padding: 1px;
//                         border: 1px solid #eee;
//                         overflow: hidden;
//                         img {
//                             display: block;
//                             vertical-align: top;
//                             width: 76px;
//                             height: 76px;
//                         }

//                         &:hover, &.selected {
//                             a {
//                                 padding: 0;
//                                 border: 2px solid #e4393c;
//                             }
//                         }
//                     }

//                     &:hover, &.selected {
//                         background: url(i/commentsListIcons2.png)
//                             no-repeat -374px
//                             73px;
//                         _background: url(i/commentsListIcons2-8.png)
//                             no-repeat -374px
//                             73px;
//                         a {
//                             padding: 0;
//                             border: 2px solid #e4393c;
//                         }
//                     }
//                 }
//             }
//             .i-prev-btn {
//                 display: inline-block;
//                 width: 38px;
//                 height: 76px;
//                 background: #f9f9f9
//                     url(i/commentsListIcons2.png)
//                     no-repeat
//                     6px
//                     24px;
//                 _background: #f9f9f9
//                     url(i/commentsListIcons2-8.png)
//                     no-repeat
//                     6px
//                     24px;
//                 cursor: pointer;
//                 border: 1px solid #dfdfdf;
//                 position: absolute;
//                 top: 1px;
//                 left: 0;
//                 &:hover {
//                     background-position: -80px 24px;
//                 }
//             }

//             .i-next-btn {
//                 display: inline-block;
//                 width: 38px;
//                 height: 76px;
//                 background: #f9f9f9
//                     url(i/commentsListIcons2.png)
//                     no-repeat -37px
//                     24px;
//                 _background: #f9f9f9
//                     url(i/commentsListIcons2-8.png)
//                     no-repeat -37px
//                     24px;
//                 cursor: pointer;
//                 border: 1px solid #dfdfdf;
//                 position: absolute;
//                 top: 1px;
//                 right: 0;
//                 &:hover {
//                     background-position: -124px 24px;
//                 }
//             }

//             .i-prev-disable {
//                 background-position: -167px 24px;
//                 &:hover {
//                     background-position: -167px 24px;
//                 }
//             }

//             .i-next-disable {
//                 background-position: -213px 24px;
//                 &:hover {
//                     background-position: -213px 24px;
//                 }
//             }
//         }
//     }

//     .showContent-viewer {
//         .photo-viewer {
//             width: 542px;
//             height: 500px;
//             border: 1px solid #dfdfdf;
//             overflow: hidden;
//             background: #fff;
//             float: left;
//             .photo-wrap {
//                 width: 100%;
//                 height: 100%;
//                 text-align: center;
//                 font-size: 0;
//                 position: relative;
//                 i {
//                     display: inline-block;
//                     width: 1px;
//                     height: 100%;
//                     overflow: hidden;
//                     vertical-align: middle;
//                 }
//                 img {
//                     vertical-align: middle;
//                     margin-left: -1px;
//                 }

//                 @mixin cursor {
//                     position: absolute;
//                     height: 100%;
//                     top: 0;
//                     background: fixed url(about:blank);
//                 }
//                 .cursor-left {
//                     width: 270px;
//                     left: 0;
//                     cursor: url(i/pic-prev.cur), auto;
//                     z-index: 3;
//                     @include cursor;

//                     &.disable {
//                         cursor: auto !important;
//                     }
//                 }
//                 .cursor-right {
//                     width: 270px;
//                     right: 0;
//                     cursor: url(i/pic-next.cur), auto;
//                     z-index: 3;
//                     @include cursor;

//                     &.disable {
//                         cursor: auto !important;
//                     }
//                 }
//             }
//         }

//         .info-viewer {
//             float: right;
//             width: 195px;
//             height: 500px;
//             overflow: hidden;

//             .p-comment {
//                 margin-top: 10px;
//                 padding-right: 5px;
//                 padding-bottom: 18px;
//                 border-bottom: 1px solid #eee;
//                 font-size: 12px;
//                 line-height: 20px;
//                 color: #999;
//             }

//             .features-wrap {
//                 padding-top: 10px;

//                 .p-features {
//                     float: left;
//                     width: 105px;
//                     li {
//                         color: #999;
//                         line-height: 20px;
//                         font-family: "\5b8b\4f53";
//                         margin-top: 3px;
//                         overflow: hidden;
//                         white-space: nowrap;
//                         text-overflow: ellipsis;
//                     }
//                 }

//                 .user-item-wrap {
//                     float: right;
//                     width: 80px;
//                     .user-item {
//                         width: 80px;
//                         line-height: 25px;
//                         height: 25px;
//                         overflow: hidden;
//                         .user-ico {
//                             float: left;
//                             margin-right: 5px;
//                             border-radius: 50%;
//                         }
//                         .user-name {
//                             line-height: 25px;
//                             word-break: break-all;
//                         }
//                     }
//                 }

//                 .type-item {
//                     color: #999;
//                     line-height: 22px;
//                     font-family: "\5b8b\4f53";
//                 }
//             }
//         }
//     }
// }

// .root61 {
//     .comment {
//         .comment-info {
//             .tag-list {
//                 padding-left: 20px;
//             }
//             .rate-item {
//                 width: 170px;
//                 margin: 0 25px 0 20px;
//             }
//         }
//     }

//     .comments-showImgSwitch-wrap {
//         .thumbnails {
//             .thumb-list {
//                 width: 872px;
//             }
//         }
//         .showContent-viewer {
//             .photo-viewer {
//                 width: 760px;
//                 .photo-wrap {
//                     .cursor-left {
//                         width: 380px;
//                     }
//                     .cursor-right {
//                         width: 380px;
//                     }
//                 }
//             }
//         }
//     }
// }
// /*举报浮层*/
// .jubao {
//     .wrap-tags {
//         padding-left: 20px;
//         // height: 51px;
//         overflow: hidden;
//         span.selected {
//             color: #e4393c;
//             border: 1px solid #e4393c;
//         }
//         .tag {
//             display: inline-block;
//             padding: 0 10px;
//             margin-right: 10px;
//             line-height: 34px;
//             margin-bottom: 15px;
//             height: 34px;
//             border: 1px solid #e0e0e0;
//             cursor: pointer;
//         }
//     }
//     .f-textarea {
//         margin: 0 20px 20px;
//         padding: 5px 10px;
//         border: 1px solid #e0e0e0;
//         textarea {
//             width: 100%;
//             height: 36px;
//             resize: vertical;
//             resize: none;
//             outline: medium none;
//             line-height: 18px;
//             color: #333;
//             border: none;
//         }
//         .textarea-ext {
//             line-height: 24px;
//             text-align: right;
//             color: #999;
//             font-family: Verdana;
//         }
//     }
//     .fop-tip {
//         clear: both;
//         position: relative;
//         top: -15px;
//         margin-left: 20px;
//         color: #e4393c;
//         .tip-icon {
//             display: inline-block;
//             width: 16px;
//             height: 16px;
//             margin-right: 5px;
//             vertical-align: top;
//             background: url(http://misc.360buyimg.com/user/myjd/comment/1.0.0/css/i/sprite-tip.png)
//                 no-repeat
//                 0 -80px;
//         }
//         .tip-text {
//             display: inline-block;
//             height: 16px;
//             line-height: 16px;
//             vertical-align: top;
//         }
//     }
//     .btns {
//         padding-right: 20px;
//         padding-bottom: 10px;
//         .btn_cancle {
//             height: 34px;
//             padding: 0 32px;
//             display: block;
//             float: right;
//             margin-left: 15px;
//             background-color: #f5f5f5;
//             line-height: 34px;
//             border: 1px solid #ddd;
//             cursor: pointer;
//             border-radius: 2px;
//             -webkit-transition: .2s;
//             transition: .2s;
//             color: #333;
//         }
//         .btn_sure {
//             height: 34px;
//             padding: 0 32px;
//             display: block;
//             float: right;
//             background-color: #e4393c;
//             line-height: 34px;
//             border: 1px solid #e4393c;
//             color: #fff;
//             cursor: pointer;
//             border-radius: 2px;
//             -webkit-transition: .2s;
//             transition: .2s;
//         }
//     }
// }
// .dialog_jubao_fail, .dialog_jubao_suc {
//     text-align: center;
//     .icon {
//         width: 48px;
//         height: 48px;
//         display: block;
//         margin: 15px auto 15px;
//         background: url(//misc.360buyimg.com/user/myjd/comment/1.0.0/css/i/sprite-tip.png)
//             no-repeat;
//     }
//     .tt {
//         line-height: 26px;
//         font-size: 16px;
//         color: #666;
//         font-weight: 700;
//     }
//     .def {
//         line-height: 30px;
//         color: #999;
//     }
//     .btn_close {
//         width: 90px;
//         height: 34px;
//         display: block;
//         margin: 8px auto 27px;
//         line-height: 34px;
//         border: 1px solid #ddd;
//         border-radius: 2px;
//         background-color: #f5f5f5;
//         cursor: pointer;
//     }
// }
// .dialog_jubao_fail {
//     .icon {
//         background-position: -100px -88px;
//     }
// }
// .dialog_jubao_suc {
//     .icon {
//         background-position: -152px -88px;
//     }
// }
