{% import '../../views/maco/user.html' as wow %}
<div id="fittings" class="fittings ETab hide">
    <div class="tab-main large">
        <ul>
            <li data-tab="trigger" class="current" data-name="人气配件">人气配件</li>
            <li class="recommend-more EDropdown">
                <div class="head">更多<span class="arrow arr-close"></span></div>
                <div class="content">
                    <a href="#none">移动电源</a>
                    <a href="#none">电源线</a>
                    <a href="#none">手机保护膜</a>
                    <a href="#none">电源线</a>
                </div>
            </li>
        </ul>
    </div>
    <div class="tab-con J_fitting_con clearfix">
        <div class="master">
            <div class="p-list">
                <div class="p-img">
                    <a href="//jd.com/" target="_blank">
                        <img data-img="1" src="https://placeholdit.imgix.net/~text?txtsize=20&txt=Product&w=100&h=100" width="100" height="100" alt=""/>
                    </a>
                </div>
                <div class="p-name">
                    <a href="//jd.com/" target="_blank">
                        商品名称（Apple iPhone 6s(A1700) 16G 玫瑰 金色 移动联通电信4G手机）
                    </a>
                </div>
                <div class="p-price">
                    <input style="display:none" type="checkbox" data-sku="1856584" id="inp-acc-master" checked/>
                    <label for="inp-acc-master"><strong class="J-p-1856584 hide">￥578.00</strong></label>
                </div>
                <i class="plus">+</i>
            </div>
        </div>
        <div class="suits">
            <ul class="lh clearfix" data-tab="item">
                {#
                    {% for item in range(5) %}
                        <li class="p-list {% if loop.last %}last{% endif %}">{{ wow.product(loop.index) }}</li>
                    {% endfor %}
                #}
            </ul>
        </div>

        <div class="infos">
            <div class="selected">已选择<em class="J-selected-cnt">0</em>个配件</div>
            <div class="p-price">
                <span>组合价</span>
                <strong class="J_cal_jp">￥暂无报价</strong>
            </div>
            <div class="btn">
                <a href="#none" class="btn-primary J-btn">立即购买</a>
            </div>
            <div class="extra">
                <a href="#none" class="J-more">配件选购中心</a>
            </div>
            <i class="equal">=</i>
        </div>
    </div>
</div>
<div id="fitting-suit">
    <div id="suit-con" class="suit-content"></div>
</div>
