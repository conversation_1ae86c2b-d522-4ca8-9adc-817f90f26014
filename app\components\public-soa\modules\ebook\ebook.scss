$tv:"?t=1106";
.bookLayer {
    text-align: center;
    padding: 30px 0;
    p {
        font-size: 14px;
    }
    strong {
        margin: 0 8px;
    }
    .btn-wrap {
        margin-top: 20px;
        a {
            display: inline-block;
            width: 88px;
            height: 28px;
            line-height: 28px;
            border: 1px solid #CCC;
            color: #666;
            margin: 0 6px;
            &:hover {
                color: #E4393C;
                border-color: #E4393C;
            }
        }

        .red {
            color: #E4393C;
            border-color: #E4393C;
        }

        .cancel-btn {
            color: #5e69ad;
            border: none;
            &:hover {
                color: #e4393c;
            }
        }
    }
}
/*ebook header*/
.ebook{
    #logo-2014{
        .logo{
            width: 167px;
            float: left;
            background: url(i/jdLogo.new.png) no-repeat;
        }
        .ebookLogo{
            float: left;
            width: 103px;
            height: 60px;
            background: url('i/ebookLogo.png#{$tv}') no-repeat;
            text-indent: -9999px;
        }
    }
    #search-2014{
        width: 300px;
        .text{
            width: 210px;
        }
    }
    #myd-2014 {
        float: right;
        z-index: 11;
        height: 36px;
        margin-top: 25px;
        margin-right: 10px;
        .cw-icon {
            width: 75px;
            height: 34px;
            border: 1px solid #DFDFDF;
            padding: 0 28px 0 36px;
            background: #F9F9F9;
            text-align: center;
            line-height: 34px;
            i{
                display: block;
                position: absolute;
                overflow: hidden;
            }
            .ci-left{
                top: 9px;
                left: 18px;
                width: 18px;
                height: 16px;
                background: url(i/sprite-ebook.jpg) no-repeat;
            }
            .ci-right {
                top: 11px;
                right: 10px;
                width: 7px;
                height: 13px;
                overflow: hidden;
                font: 400 13px/13px simsun;
                color: #999;
            }
        }
    }
    #navitems-2014{
        float: none;
    }
    #navitems-group2{
        padding-top: 16px;
        float: right;
        overflow: hidden;
        li{
            margin-right: -1px;
        }
        a{
            font: normal 12px/12px "microsoft yahei";
            color: #999;
            border-right: 1px solid #ccc;
            padding: 0 5px 0 6px;
            height: 13px;
        }
    }
    /*cart button*/
    #ebook-cart {
        float: right;
        z-index: 11;
        height: 36px;
        margin-top: 25px;
        .cw-icon {
            width: 95px;
            height: 34px;
            border: 1px solid #DFDFDF;
            padding: 0 28px 0 36px;
            background: #F9F9F9;
            text-align: center;
            line-height: 34px
        }
        .ci-left {
            top: 9px;
            left: 18px;
            width: 18px;
            height: 16px;
            background: url(//misc.360buyimg.com/jdf/1.0.0/unit/globalImages/1.0.0/jd2015img.png) 0 -58px no-repeat;
            _background: url(//misc.360buyimg.com/jdf/1.0.0/unit/globalImages/1.0.0/jd2015img.png) 0 -91px no-repeat
        }
        .ci-right {
            top: 11px;
            right: 10px;
            width: 7px;
            height: 13px;
            overflow: hidden;
            font: 400 13px/13px simsun;
            color: #999
        }
        .ci-count {
            position: absolute;
            top: -4px;
            left: 127px;
            display: inline-block;
            *zoom: 1;
            *display: inline;
            padding: 1px 2px;
            font-size: 12px;
            line-height: 12px;
            color: #fff;
            background-color: #c81623;
            border-radius: 7px 7px 7px 0;
            min-width: 12px;
            text-align: center
        }
        .prompt {
            padding: 10px 15px
        }
        .nogoods {
            padding-left: 30px;
            height: 49px;
            line-height: 49px;
            overflow: hidden;
            color: #999
        }
        .nogoods b {
            float: left;
            width: 56px;
            height: 49px;
            background-image: url(//misc.360buyimg.com/jdf/1.0.0/unit/shoppingcart/2.0.0/i/settleup-nogoods.png)
        }
        .dt-mz:hover {
            background: #fff
        }
        .dt-mz a {
            color: #999
        }
        .dt-mz {
            color: #999;
            width: 310px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap
        }
        &.hover {
            .cw-icon, .dorpdown-layer, .cw-icon, .dorpdown-layer {
                background: #fff;
                border: 1px solid #ddd;
                box-shadow: 0 0 5px rgba(0, 0, 0, .2)
            }
            .dorpdown-layer {
                display: block;
                right: 0;
                _right: -1px;
                width: 308px
            }
            .spacer {
                position: absolute;
                right: 0;
                top: -7px;
                width: 139px;
                height: 12px;
                background: #fff
            }
            .ci-left {
                _background: url(//misc.360buyimg.com/jdf/1.0.0/unit/globalImages/1.0.0/jd2015img.png) 0 -116px no-repeat
            }
        }
    }
}

.root61{
    .ebook{
        #search-2014{
            width:462px;
            .text{
                width: 370px;
            }
        }
        #ebook-cart{
            margin-right: 0;
        }
        #navitems-group2{
            a{
                padding: 0 9px 0 11px;
            }
        }
    }
}
