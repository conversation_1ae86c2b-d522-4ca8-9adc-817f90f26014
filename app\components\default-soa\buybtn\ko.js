define('MOD_ROOT/buybtn/ko', function(require, exports, module) {
    var Event = require('MOD_ROOT/common/tools/event').Event
    var Login = require('JDF_UNIT/login/1.0.0/login')
    var G = require('MOD_ROOT/common/core')
    var Countdown = require('MOD_ROOT/common/tools/tools').Countdown;

    var KO = {
        init: function(cfg) {
            $('#choose-btn-ko').after(
                '<span class="yuyue-text J-yuyue-text"></span>'
            )
            this.cfg = cfg
            this.$el = $('#choose-btn-ko')
            this.$textTip = $('.J-yuyue-text')

            if (!cfg.isKO) return false;

            // 如果存在预售， 则走预售流程
            // if ($.inArray('YuShou', cfg.specialAttrs) != -1) {
            //    return false;
            // }
            //预售属性判断方式更改
            if (cfg.isYuShou) {
                return false;
            }


            this.isPlusProduct = false;

            this.bindEvent()
            this.get()

            return this
        },
        bindEvent: function() {
            var _this = this

            Event.addListener('onStockReady', function(data) {
                //console.log('Enabled: %s | Stock: %s', _this.isEnabled, pageConfig.product.havestock);
                if (_this.isEnabled) {
                    _this.enabled()
                } else {
                    _this.disabled()
                }
            })
        },
        get: function() {
            // var url = 'http://www.mocky.io/v2/58f7344f100000e51624f073'
            var url = '//itemko.jd.com/itemShowBtn'
            $.ajax({
                url: url,
                data: {
                    skuId: this.cfg.skuid,
                    from: 'pc'
                },
                dataType: 'jsonp',
                success: $.proxy(this.set, this)
            })
        },
        set: function(r) {
            if (!r || r.type !== '3') {
                this.disabled()
                this.hide()
                return false
            }

            this.buyUrl = r.url

            this.show()

            this.setPlus(
                r,
                $.proxy(function() {
                    this.setBtnText(this.getButtonText(r))
                    this.checkState(r)
                }, this)
            )

            Event.fire({
                type: 'onKOReady',
                r: r
            })
        },
        setPlus: function(r, callback) {
            var plusType = r.pt
            var isPlusTime = r.ps > 0
            this.isPlusProduct = plusType === 1 || plusType === 2
            // 等待抢购阶段 || 预约阶段
            if (!this.isPlusProduct || !isPlusTime) {
                callback()
                return false
            }

            var isPlusStart = (+new Date()) > r.st * 1000

            Login.isLogin(
                $.proxy(function(isLogin) {
                    var userType = this.getUserType()
                    var loginUrl =
                        'https://passport.jd.com/new/login.aspx?ReturnUrl=' +
                        encodeURIComponent(location.href)

                    var tipText = '';
                    if (isLogin) {
                        // 有资格用户 showPlusTime
                        // plusType 1 正式
                        // plusType 2 正式 + 试用
                        // userType 2 正式期
                        // userType 1 试用期
                        this.showPlusTime =
                            (plusType === 1 && userType === 2) ||
                            (plusType === 2 && userType === 2) ||
                            (plusType === 2 && userType === 1)
                        
                        // console.log('PlusType: ' + plusType);
                        // console.log('userType: ' + userType);
                        // console.log(this.showPlusTime);
                        if (this.showPlusTime) {
                            tipText = '您是PLUS会员，提前抢购剩余时间：'
                        } else {
                            if (plusType === 1) {
                                tipText = '开通PLUS正式会员可立即开抢'
                            }
                            if (plusType === 2) {
                                if (userType !== 2 && userType !== 1) {
                                    tipText = '开通PLUS会员可立即开抢'
                                }
                            }

                            tipText +='，<a target="_blank" href="//plus.jd.com/index">去开通 <span class="arrow">&gt;&gt;</span></a>'
                        }
                    } else {
                        if (plusType === 1) {
                            tipText = 'PLUS正式会员专享，请 <a href="{0}" class="J-plus-login">登录</a> 确认购买资格'.format(loginUrl)
                        }else{
                        tipText = 'PLUS会员已提前开抢，请 <a href="{0}" class="J-plus-login">登录</a> 确认购买资格'.format(loginUrl)
                    }
                    }

                    // console.log('是否登录: ' + isLogin);
                    // console.log('Plus抢购是否开始: ' + isPlusStart);
                    // console.log('是否符合资格: ' + this.showPlusTime);
                    if (isPlusStart) {
                        if (this.showPlusTime) {
                            this.enabled()
                            this.setBtnText('抢购')
                            this.setPlusCountdown(r.ps, tipText)

                            //Plus 优先购
                            /*
                            当商品有特殊属性isko且在预售接口中有isplus属性时，
                            且命中状态（即下图中的第二行左侧，正式plus用户登录打开仅正式plus可享），
                            屏蔽掉抢购按钮旁边文案。
                             范围：部分sku
                             5131287
                             5131225
                             5131311
                             5131309
                             测试sku  4068636   海南三亚有货
                             */

                            function isTargetSKU(sku,range){
                                var result = false;
                                for(var i=0;i<range.length; i++){
                                    if(sku == range[i]){
                                        result = true;
                                        break;
                                    }
                                }
                                return result;
                            }

                            //console.log('plusType = ' + plusType);
                            //console.log('userType = ' + userType);
                            //console.log('pageConfig.specialAttrs = ' + pageConfig.product.specialAttrs.indexOf('isKO'));
                            //console.log('sku='+pageConfig.product.skuid);

                            //判断独立秒杀
                            if(pageConfig.product.specialAttrs instanceof Array  && pageConfig.product.specialAttrs.join(',').indexOf('isKO') != -1 && (plusType === 1 && userType === 2) ){

                                var _range = ['5131287','5131225','5131311','5131309','4068636'];
                                var _sku = pageConfig.product.skuid;
                                //console.log('OK!!!');
                                if(isTargetSKU(_sku,_range)){

                                    //隐藏文案及时间
                                    this.$textTip.hide();
                                    //tipText = 'PLUS正式会员专享';
                                    //yuyue-text J-yuyue-text
                                    this.$textTip.after('<span class="yuyue-text plus-yuyue-text">PLUS正式会员专享</span>');

                                }
                            }




                        } else {
                            this.disabled()
                            this.setBtnText('等待抢购')
                            this.setTextTips(tipText, r)
                        }
                    } else {
                        this.disabled()
                        this.setBtnText('抢购未开始')
                    }
                }, this)
            )
        },
        setPlusCountdown: function (d, prefix) {
            if (!d || d < 1) return

            var $time = this.$textTip
            new Countdown(d*1000, function(res) {
                if ( res.d < 1 ) {
                    $time.html( prefix + res.h + '小时' + res.m + '分' + res.s + '秒');
                } else {
                    $time.html( prefix + res.d + '天' + res.h + '小时' + res.m + '分' + res.s + '秒');
                }
            });
        },
        setTextTips: function(text) {
            if (this.isPlusProduct) {
                this.$textTip.html(text)
            }
        },
        getUserType: function() {
            // http://cf.jd.com/pages/viewpage.action?pageId=57215567
            var ceshi3 = readCookie('ceshi3.com')
            if (ceshi3) {
                var firstChar = ceshi3.substr(0, 1)
                // 1 试用期
                // 2 正式期
                if (firstChar === '1' || firstChar === '2') {
                    return parseInt(firstChar)
                } else {
                    return 'N'
                }
            }
            return 'NO_LOGIN'
        },
        checkState: function(r) {
            // state: 11表示未开始，12表示进行中，13表示结束
            this.isEnabled = r.state === '12'
            if (this.isEnabled) {
                this.enabled()
            } else {
                this.disabled()
            }
        },
        enabled: function() {
            if (this.cfg.havestock) {
                this.$el.removeClass('btn-disable')
                this.$el.attr('href', this.buyUrl)
            } else {
                this.disabled()
            }
        },
        disabled: function() {
            this.$el.addClass('btn-disable')
            this.$el.attr('href', '#none')
            $("#InitTradeUrl").hide() // 隐藏立即购买按钮
        },
        show: function() {
            this.$el.show()
            this.cfg.addToCartBtn.hide()
        },
        hide: function() {
            this.$el.hide()
            this.cfg.addToCartBtn.show()
        },
        setBtnText: function(text) {
            this.$el.html(text)
        },
        getButtonText: function(r) {
            switch (r.state) {
                case '11':
                    return '抢购未开始'
                case '12':
                    return '抢购'
                case '13':
                    return '抢购已结束'
                default:
                    return '抢购'
            }
        }
    }

    module.exports = KO
    module.exports.__id = 'ko'
})
