
                    @mixin sprite-baina {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -16px -48px;
                    }
                    @mixin sprite-close {
                        width: 12px;
                        height: 12px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -64px;
                    }
                    @mixin sprite-giftbuy {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -32px -48px;
                    }
                    @mixin sprite-info {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -16px -32px;
                    }
                    @mixin sprite-info2 {
                        width: 14px;
                        height: 14px;
                        background-image: url(i/__sprite.png);
                        background-position: -64px -48px;
                    }
                    @mixin sprite-jnbt {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -48px -32px;
                    }
                    @mixin sprite-old2new {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -32px;
                    }
                    @mixin sprite-question {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -48px;
                    }
                    @mixin sprite-SelfAssuredPurchase {
                        width: 84px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -16px;
                    }
                    @mixin sprite-SelfAssuredPurchase3 {
                        width: 85px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -0px;
                    }
                    @mixin sprite-sjwx {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -64px -32px;
                    }
                    @mixin sprite-tcbg {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -32px -32px;
                    }
                    @mixin sprite-zengzhi {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png);
                        background-position: -48px -48px;
                    }