@import '../common/lib';
@import './__sprite.scss';

#itemover {
    border: 1px solid #eee;

    .mt {
        margin: 10px;
        color: #999;
        h3 {
            font-size: 12px;
            font-weight: normal;
        }
        .page-num1 {
            .curr {
                color: #e3393c;
            }
        }
    }

    .mc {
        height: 535px;
        padding: 10px 46px;
        position: relative;

        .lists {
            width: 392px;
            height: 508px;
            margin: 0 auto;
            overflow: hidden;
            .list {
                width: 10000px;
                overflow: hidden;
                .plist {
                    width: 392px;
                    _width: 392px;
                    float: left;
                    margin-bottom: 0;
                }

                li {
                    float: left;
                    width: 185px;
                    margin: 10px 5px 30px;
                    .p-img {
                        width: 150px;
                        height: 150px;
                        margin: 0 auto;
                    }

                    .p-price {
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .arrow {
        position: absolute;
        top: 242px;
        width: 22px;
        height: 32px;
        cursor: pointer;
        i {
            @include inline-block;
            vertical-align: middle;
            margin-right: 5px;
        }
    }

    .ui-switchable-next {
        @extend .arrow;
        .sprite-arrow-next {
            @include sprite-arrow-next;
        }
        right: 12px;
    }

    .ui-switchable-prev {
        @extend .arrow;
        .sprite-arrow-prev {
            @include sprite-arrow-prev;
        }
        left: 12px;
    }
}

.itemover-tip {
    color: #999;
    margin-bottom: 5px;
}

.root61 {
    #itemover {
        .mc {
            .lists {
                width: 592px;
                .list .plist {
                    width: 592px;
                }
            }
        }
    }
}

.clothing {
    #itemover {
        .mc {
            .lists {
                width: 392px;
                _width: 412px;
                .list .plist {
                    width: 392px;
                    _width: 412px;
                }
            }
        }
    }
}
