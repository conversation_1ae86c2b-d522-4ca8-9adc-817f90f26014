define('MOD_ROOT/preview/panorama.3d', function (require, exports, module) {
function build3D(a, b) { return jdpanorama.viewer(a, { defaults: { firstScene: "pond", sceneFadeDuration: 2e3, autoLoad: !0 }, fallback: "https://www.jd.com", showControls: !1, autoRotate: 5, orientationOnByDefault: !1, scenes: { pond: { panorama: b } } }) } window.libpanorama = function (a, b, c) { "use strict"; function d(d) { function g(a, b) { return 1 == a.level && 1 != b.level ? -1 : 1 == b.level && 1 != a.level ? 1 : b.timestamp - a.timestamp } function k(a, b) { return a.level != b.level ? a.level - b.level : a.diff - b.diff } function l() { if (!C.drawInProgress) { C.drawInProgress = !0; for (var a = 0; a < C.currentNodes.length; a++)C.currentNodes[a].textureLoaded && (<PERSON>.bindBuffer(D.ARRAY_BUFFER, O), <PERSON><PERSON>bufferData(D.ARRAY_BUFFER, new Float32Array(C.currentNodes[a].vertices), D.STATIC_DRAW), D.vertexAttribPointer(C.vertPosLocation, 3, D.FLOAT, !1, 0, 0), D.bindBuffer(D.ARRAY_BUFFER, P), D.vertexAttribPointer(C.texCoordLocation, 2, D.FLOAT, !1, 0, 0), D.bindTexture(D.TEXTURE_2D, C.currentNodes[a].texture), D.drawElements(D.TRIANGLES, 6, D.UNSIGNED_SHORT, 0)); C.drawInProgress = !1 } } function m(a, b, c, d, e, f) { this.vertices = a, this.side = b, this.level = c, this.x = d, this.y = e, this.path = f.replace("%s", b).replace("%l", c).replace("%x", d).replace("%y", e) } function n(a, b, c, d, e) { if (A(a, b.vertices)) { var f = b.vertices, g = f[0] + f[3] + f[6] + f[9], h = f[1] + f[4] + f[7] + f[10], i = f[2] + f[5] + f[8] + f[11], j = Math.sqrt(g * g + h * h + i * i), k = Math.asin(i / j), l = Math.atan2(h, g), o = l - d; o += o > Math.PI ? -2 * Math.PI : o < -Math.PI ? 2 * Math.PI : 0, o = Math.abs(o), b.diff = Math.acos(Math.sin(c) * Math.sin(k) + Math.cos(c) * Math.cos(k) * Math.cos(o)); for (var p = !1, q = 0; q < C.nodeCache.length; q++)if (C.nodeCache[q].path == b.path) { p = !0, C.nodeCache[q].timestamp = C.nodeCacheTimestamp++ , C.nodeCache[q].diff = b.diff, C.currentNodes.push(C.nodeCache[q]); break } if (p || (b.timestamp = C.nodeCacheTimestamp++ , C.currentNodes.push(b), C.nodeCache.push(b)), b.level < C.level) { var r = K.cubeResolution * Math.pow(2, b.level - K.maxLevel), s = Math.ceil(r * K.invTileResolution) - 1, t = r % K.tileResolution * 2, u = 2 * r % K.tileResolution; 0 === u && (u = K.tileResolution), 0 === t && (t = 2 * K.tileResolution); var v = .5; b.x != s && b.y != s || (v = 1 - K.tileResolution / (K.tileResolution + u)); var w, x, y = 1 - v, z = [], B = v, D = v, E = v, F = y, G = y, H = y; u < K.tileResolution && (b.x == s && b.y != s ? (D = .5, G = .5, "d" != b.side && "u" != b.side || (E = .5, H = .5)) : b.x != s && b.y == s && (B = .5, F = .5, "l" != b.side && "r" != b.side || (E = .5, H = .5))), t <= K.tileResolution && (b.x == s && (B = 0, F = 1, "l" != b.side && "r" != b.side || (E = 0, H = 1)), b.y == s && (D = 0, G = 1, "d" != b.side && "u" != b.side || (E = 0, H = 1))), w = [f[0], f[1], f[2], f[0] * B + f[3] * F, f[1] * v + f[4] * y, f[2] * E + f[5] * H, f[0] * B + f[6] * F, f[1] * D + f[7] * G, f[2] * E + f[8] * H, f[0] * v + f[9] * y, f[1] * D + f[10] * G, f[2] * E + f[11] * H], x = new m(w, b.side, b.level + 1, 2 * b.x, 2 * b.y, K.fullpath), z.push(x), b.x == s && t <= K.tileResolution || (w = [f[0] * B + f[3] * F, f[1] * v + f[4] * y, f[2] * E + f[5] * H, f[3], f[4], f[5], f[3] * v + f[6] * y, f[4] * D + f[7] * G, f[5] * E + f[8] * H, f[0] * B + f[6] * F, f[1] * D + f[7] * G, f[2] * E + f[8] * H], x = new m(w, b.side, b.level + 1, 2 * b.x + 1, 2 * b.y, K.fullpath), z.push(x)), b.x == s && t <= K.tileResolution || b.y == s && t <= K.tileResolution || (w = [f[0] * B + f[6] * F, f[1] * D + f[7] * G, f[2] * E + f[8] * H, f[3] * v + f[6] * y, f[4] * D + f[7] * G, f[5] * E + f[8] * H, f[6], f[7], f[8], f[9] * B + f[6] * F, f[10] * v + f[7] * y, f[11] * E + f[8] * H], x = new m(w, b.side, b.level + 1, 2 * b.x + 1, 2 * b.y + 1, K.fullpath), z.push(x)), b.y == s && t <= K.tileResolution || (w = [f[0] * v + f[9] * y, f[1] * D + f[10] * G, f[2] * E + f[11] * H, f[0] * B + f[6] * F, f[1] * D + f[7] * G, f[2] * E + f[8] * H, f[9] * B + f[6] * F, f[10] * v + f[7] * y, f[11] * E + f[8] * H, f[9], f[10], f[11]], x = new m(w, b.side, b.level + 1, 2 * b.x, 2 * b.y + 1, K.fullpath), z.push(x)); for (var I = 0; I < z.length; I++)n(a, z[I], c, d, e) } } } function o() { return [-1, 1, -1, 1, 1, -1, 1, -1, -1, -1, -1, -1, 1, 1, 1, -1, 1, 1, -1, -1, 1, 1, -1, 1, -1, 1, 1, 1, 1, 1, 1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1, -1, 1, 1, 1, 1, -1, 1, 1, -1, -1] } function p() { return [1, 0, 0, 0, 1, 0, 0, 0, 1] } function q(a, b, c) { var d = Math.sin(b), e = Math.cos(b); return "x" == c ? [a[0], e * a[1] + d * a[2], e * a[2] - d * a[1], a[3], e * a[4] + d * a[5], e * a[5] - d * a[4], a[6], e * a[7] + d * a[8], e * a[8] - d * a[7]] : "y" == c ? [e * a[0] - d * a[2], a[1], e * a[2] + d * a[0], e * a[3] - d * a[5], a[4], e * a[5] + d * a[3], e * a[6] - d * a[8], a[7], e * a[8] + d * a[6]] : "z" == c ? [e * a[0] + d * a[1], e * a[1] - d * a[0], a[2], e * a[3] + d * a[4], e * a[4] - d * a[3], a[5], e * a[6] + d * a[7], e * a[7] - d * a[6], a[8]] : void 0 } function r(a) { return [a[0], a[1], a[2], 0, a[3], a[4], a[5], 0, a[6], a[7], a[8], 0, 0, 0, 0, 1] } function s(a) { return [a[0], a[4], a[8], a[12], a[1], a[5], a[9], a[13], a[2], a[6], a[10], a[14], a[3], a[7], a[11], a[15]] } function t(a, b, c, d) { var e = 2 * Math.atan(Math.tan(a / 2) * B.height / B.width), f = 1 / Math.tan(e / 2); return [f / b, 0, 0, 0, 0, f, 0, 0, 0, 0, (d + c) / (c - d), 2 * d * c / (c - d), 0, 0, -1, 0] } function u(a, b) { D.bindTexture(D.TEXTURE_2D, b), D.texImage2D(D.TEXTURE_2D, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, a), D.texParameteri(D.TEXTURE_2D, D.TEXTURE_MAG_FILTER, D.LINEAR), D.texParameteri(D.TEXTURE_2D, D.TEXTURE_MIN_FILTER, D.LINEAR), D.texParameteri(D.TEXTURE_2D, D.TEXTURE_WRAP_S, D.CLAMP_TO_EDGE), D.texParameteri(D.TEXTURE_2D, D.TEXTURE_WRAP_T, D.CLAMP_TO_EDGE), D.bindTexture(D.TEXTURE_2D, null) } function v(a) { a.textureLoad || (a.textureLoad = !0, R(encodeURI(a.path + "." + K.extension), function (b) { a.texture = b, a.textureLoaded = !0 })) } function w(a) { for (var b = 1; b < K.maxLevel && B.width > K.tileResolution * Math.pow(2, b - 1) * Math.tan(a / 2) * .707;)b++; C.level = b } function x(a, b) { return [a[0] * b[0], a[0] * b[1], a[0] * b[2], 0, a[5] * b[4], a[5] * b[5], a[5] * b[6], 0, a[10] * b[8], a[10] * b[9], a[10] * b[10], a[11], -b[8], -b[9], -b[10], 0] } function y(a, b) { return [a[0] * b[0] + a[1] * b[1] + a[2] * b[2], a[4] * b[0] + a[5] * b[1] + a[6] * b[2], a[11] + a[8] * b[0] + a[9] * b[1] + a[10] * b[2], 1 / (a[12] * b[0] + a[13] * b[1] + a[14] * b[2])] } function z(a, b) { var c = y(a, b), d = c[0] * c[3], e = c[1] * c[3], f = c[2] * c[3], g = [0, 0, 0]; return d < -1 && (g[0] = -1), d > 1 && (g[0] = 1), e < -1 && (g[1] = -1), e > 1 && (g[1] = 1), (f < -1 || f > 1) && (g[2] = 1), g } function A(a, b) { var c = z(a, b.slice(0, 3)), d = z(a, b.slice(3, 6)), e = z(a, b.slice(6, 9)), f = z(a, b.slice(9, 12)), g = c[0] + d[0] + e[0] + f[0]; if (-4 == g || 4 == g) return !1; var h = c[1] + d[1] + e[1] + f[1]; return -4 != h && 4 != h && 4 != c[2] + d[2] + e[2] + f[2] } var B = b.createElement("canvas"); B.style.width = B.style.height = "100%", d.appendChild(B); var C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q; this.init = function (a, g, k, l, m, n, p, q) { if (typeof g === c && (g = "equirectangular"), "equirectangular" != g && "cubemap" != g && "multires" != g) throw console.log("Error: invalid image type specified!"), { type: "config error" }; if (L = g, K = a, M = k, C) { if (E && (D.detachShader(C, E), D.deleteShader(E)), F && (D.detachShader(C, F), D.deleteShader(F)), D.bindBuffer(D.ARRAY_BUFFER, null), D.bindBuffer(D.ELEMENT_ARRAY_BUFFER, null), C.texture && D.deleteTexture(C.texture), C.nodeCache) for (var r = 0; r < C.nodeCache.length; r++)D.deleteTexture(C.nodeCache[r].texture); D.deleteProgram(C), C = c } J = c; var s; if ("cubemap" == L && 0 != (K[0].width & K[0].width - 1) && (navigator.userAgent.toLowerCase().match(/(iphone|ipod|ipad).* os 8_/) || navigator.userAgent.toLowerCase().match(/(iphone|ipod|ipad).* os 9_/) || navigator.userAgent.match(/Trident.*rv[ :]*11\./)) || D || (D = B.getContext("experimental-webgl", { alpha: !1, depth: !1 })), !D && ("multires" == L && K.hasOwnProperty("fallbackPath") || "cubemap" == L) && ("WebkitAppearance" in b.documentElement.style || navigator.userAgent.match(/Trident.*rv[ :]*11\./) || -1 !== navigator.appVersion.indexOf("MSIE 10"))) { H && d.removeChild(H), H = b.createElement("div"), H.className = "pnlm-world"; var t; t = K.basePath ? K.basePath + K.fallbackPath : K.fallbackPath; var u = ["f", "r", "b", "l", "u", "d"], v = 0, w = function () { var a = b.createElement("canvas"); a.className = "pnlm-face pnlm-" + u[this.side] + "face", H.appendChild(a); var c = a.getContext("2d"); a.style.width = this.width + 4 + "px", a.style.height = this.height + 4 + "px", a.width = this.width + 4, a.height = this.height + 4, c.drawImage(this, 2, 2); var e, f, g = c.getImageData(0, 0, a.width, a.height), h = g.data; for (e = 2; e < a.width - 2; e++)for (f = 0; f < 4; f++)h[4 * (e + a.width) + f] = h[4 * (e + 2 * a.width) + f], h[4 * (e + a.width * (a.height - 2)) + f] = h[4 * (e + a.width * (a.height - 3)) + f]; for (e = 2; e < a.height - 2; e++)for (f = 0; f < 4; f++)h[4 * (e * a.width + 1) + f] = h[4 * (e * a.width + 2) + f], h[4 * ((e + 1) * a.width - 2) + f] = h[4 * ((e + 1) * a.width - 3) + f]; for (f = 0; f < 4; f++)h[4 * (a.width + 1) + f] = h[4 * (2 * a.width + 2) + f], h[4 * (2 * a.width - 2) + f] = h[4 * (3 * a.width - 3) + f], h[4 * (a.width * (a.height - 2) + 1) + f] = h[4 * (a.width * (a.height - 3) + 2) + f], h[4 * (a.width * (a.height - 1) - 2) + f] = h[4 * (a.width * (a.height - 2) - 3) + f]; for (e = 1; e < a.width - 1; e++)for (f = 0; f < 4; f++)h[4 * e + f] = h[4 * (e + a.width) + f], h[4 * (e + a.width * (a.height - 1)) + f] = h[4 * (e + a.width * (a.height - 2)) + f]; for (e = 1; e < a.height - 1; e++)for (f = 0; f < 4; f++)h[e * a.width * 4 + f] = h[4 * (e * a.width + 1) + f], h[4 * ((e + 1) * a.width - 1) + f] = h[4 * ((e + 1) * a.width - 2) + f]; for (f = 0; f < 4; f++)h[f] = h[4 * (a.width + 1) + f], h[4 * (a.width - 1) + f] = h[4 * (2 * a.width - 2) + f], h[a.width * (a.height - 1) * 4 + f] = h[4 * (a.width * (a.height - 2) + 1) + f], h[4 * (a.width * a.height - 1) + f] = h[4 * (a.width * (a.height - 1) - 2) + f]; c.putImageData(g, 0, 0), 6 == ++v && (G = this.width, d.appendChild(H), p()) }; for (s = 0; s < 6; s++) { var x = new Image; x.crossOrigin = "anonymous", x.side = s, x.onload = w, x.src = "multires" == L ? encodeURI(t.replace("%s", u[s]) + "." + K.extension) : encodeURI(K[s].src) } } else { if (!D) throw console.log("Error: no WebGL support detected!"), { type: "no webgl" }; K.basePath ? K.fullpath = K.basePath + K.path : K.fullpath = K.path, K.invTileResolution = 1 / K.tileResolution; var y = o(); for (I = [], s = 0; s < 6; s++)I[s] = y.slice(12 * s, 12 * s + 12), y = o(); var z, A; if ("equirectangular" == L) { if (z = Math.max(K.width, K.height), A = D.getParameter(D.MAX_TEXTURE_SIZE), z > A) throw console.log("Error: The image is too big; it's " + z + "px wide, but this device's maximum supported width is " + A + "px."), { type: "webgl size error", width: z, maxWidth: A } } else if ("cubemap" == L && (z = K[0].width, A = D.getParameter(D.MAX_CUBE_MAP_TEXTURE_SIZE), z > A)) throw console.log("Error: The cube face image is too big; it's " + z + "px wide, but this device's maximum supported width is " + A + "px."), { type: "webgl size error", width: z, maxWidth: A }; q === c || q.horizonPitch === c && q.horizonRoll === c || (J = [q.horizonPitch == c ? 0 : q.horizonPitch, q.horizonRoll == c ? 0 : q.horizonRoll]); var R = D.TEXTURE_2D; D.viewport(0, 0, B.width, B.height), E = D.createShader(D.VERTEX_SHADER); var S = e; "multires" == L && (S = f), D.shaderSource(E, S), D.compileShader(E), F = D.createShader(D.FRAGMENT_SHADER); var T = i; if ("cubemap" == L ? (R = D.TEXTURE_CUBE_MAP, T = h) : "multires" == L && (T = j), D.shaderSource(F, T), D.compileShader(F), C = D.createProgram(), D.attachShader(C, E), D.attachShader(C, F), D.linkProgram(C), D.getShaderParameter(E, D.COMPILE_STATUS) || console.log(D.getShaderInfoLog(E)), D.getShaderParameter(F, D.COMPILE_STATUS) || console.log(D.getShaderInfoLog(F)), D.getProgramParameter(C, D.LINK_STATUS) || console.log(D.getProgramInfoLog(C)), D.useProgram(C), C.drawInProgress = !1, C.texCoordLocation = D.getAttribLocation(C, "a_texCoord"), D.enableVertexAttribArray(C.texCoordLocation), "multires" != L) { if (N || (N = D.createBuffer()), D.bindBuffer(D.ARRAY_BUFFER, N), D.bufferData(D.ARRAY_BUFFER, new Float32Array([-1, 1, 1, 1, 1, -1, -1, 1, 1, -1, -1, -1]), D.STATIC_DRAW), D.vertexAttribPointer(C.texCoordLocation, 2, D.FLOAT, !1, 0, 0), C.aspectRatio = D.getUniformLocation(C, "u_aspectRatio"), D.uniform1f(C.aspectRatio, B.width / B.height), C.psi = D.getUniformLocation(C, "u_psi"), C.theta = D.getUniformLocation(C, "u_theta"), C.f = D.getUniformLocation(C, "u_f"), C.h = D.getUniformLocation(C, "u_h"), C.v = D.getUniformLocation(C, "u_v"), C.vo = D.getUniformLocation(C, "u_vo"), C.rot = D.getUniformLocation(C, "u_rot"), D.uniform1f(C.h, l / (2 * Math.PI)), D.uniform1f(C.v, m / Math.PI), D.uniform1f(C.vo, n / Math.PI * 2), "equirectangular" == L) { C.backgroundColor = D.getUniformLocation(C, "u_backgroundColor"); var U = q.backgroundColor ? q.backgroundColor : [0, 0, 0]; D.uniform4fv(C.backgroundColor, U.concat([1])) } C.texture = D.createTexture(), D.bindTexture(R, C.texture), "cubemap" == L ? (D.texImage2D(D.TEXTURE_CUBE_MAP_POSITIVE_X, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K[1]), D.texImage2D(D.TEXTURE_CUBE_MAP_NEGATIVE_X, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K[3]), D.texImage2D(D.TEXTURE_CUBE_MAP_POSITIVE_Y, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K[4]), D.texImage2D(D.TEXTURE_CUBE_MAP_NEGATIVE_Y, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K[5]), D.texImage2D(D.TEXTURE_CUBE_MAP_POSITIVE_Z, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K[0]), D.texImage2D(D.TEXTURE_CUBE_MAP_NEGATIVE_Z, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K[2])) : D.texImage2D(R, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K), D.texParameteri(R, D.TEXTURE_WRAP_S, D.CLAMP_TO_EDGE), D.texParameteri(R, D.TEXTURE_WRAP_T, D.CLAMP_TO_EDGE), D.texParameteri(R, D.TEXTURE_MIN_FILTER, D.LINEAR), D.texParameteri(R, D.TEXTURE_MAG_FILTER, D.LINEAR) } else C.vertPosLocation = D.getAttribLocation(C, "a_vertCoord"), D.enableVertexAttribArray(C.vertPosLocation), O || (O = D.createBuffer()), P || (P = D.createBuffer()), Q || (Q = D.createBuffer()), D.bindBuffer(D.ARRAY_BUFFER, P), D.bufferData(D.ARRAY_BUFFER, new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]), D.STATIC_DRAW), D.bindBuffer(D.ELEMENT_ARRAY_BUFFER, Q), D.bufferData(D.ELEMENT_ARRAY_BUFFER, new Uint16Array([0, 1, 2, 0, 2, 3]), D.STATIC_DRAW), C.perspUniform = D.getUniformLocation(C, "u_perspMatrix"), C.cubeUniform = D.getUniformLocation(C, "u_cubeMatrix"), C.level = -1, C.currentNodes = [], C.nodeCache = [], C.nodeCacheTimestamp = 0; if (0 !== D.getError()) throw console.log("Error: Something went wrong with WebGL!"), { type: "webgl error" }; p() } }, this.destroy = function () { if (d !== c && (B !== c && d.removeChild(B), H !== c && d.removeChild(H)), D) { var a = D.getExtension("WEBGL_lose_context"); a && a.loseContext() } }, this.resize = function () { var b = a.devicePixelRatio || 1; B.width = B.clientWidth * b, B.height = B.clientHeight * b, D && (D.viewport(0, 0, B.width, B.height), "multires" != L && D.uniform1f(C.aspectRatio, B.width / B.height)) }, this.resize(), this.render = function (b, d, e, f) { var h, i, j, o = 0; if (f === c && (f = {}), f.roll && (o = f.roll), J !== c) { var u = J[0], y = J[1], z = b, A = d, E = Math.cos(y) * Math.sin(b) * Math.sin(u) + Math.cos(b) * (Math.cos(u) * Math.cos(d) + Math.sin(y) * Math.sin(u) * Math.sin(d)), F = -Math.sin(b) * Math.sin(y) + Math.cos(b) * Math.cos(y) * Math.sin(d), N = Math.cos(y) * Math.cos(u) * Math.sin(b) + Math.cos(b) * (-Math.cos(d) * Math.sin(u) + Math.cos(u) * Math.sin(y) * Math.sin(d)); b = Math.asin(Math.max(Math.min(N, 1), -1)), d = Math.atan2(F, E); var O = [Math.cos(z) * (Math.sin(y) * Math.sin(u) * Math.cos(A) - Math.cos(u) * Math.sin(A)), Math.cos(z) * Math.cos(y) * Math.cos(A), Math.cos(z) * (Math.cos(u) * Math.sin(y) * Math.cos(A) + Math.sin(A) * Math.sin(u))], P = [-Math.cos(b) * Math.sin(d), Math.cos(b) * Math.cos(d)], Q = Math.acos(Math.max(Math.min((O[0] * P[0] + O[1] * P[1]) / (Math.sqrt(O[0] * O[0] + O[1] * O[1] + O[2] * O[2]) * Math.sqrt(P[0] * P[0] + P[1] * P[1])), 1), -1)); O[2] < 0 && (Q = 2 * Math.PI - Q), o += Q } if (D || "multires" != L && "cubemap" != L) { if ("multires" != L) { var R = 2 * Math.atan(Math.tan(.5 * e) / (B.width / B.height)); h = 1 / Math.tan(.5 * R), D.uniform1f(C.psi, d), D.uniform1f(C.theta, b), D.uniform1f(C.rot, o), D.uniform1f(C.f, h), !0 === M && "equirectangular" == L && (D.bindTexture(D.TEXTURE_2D, C.texture), D.texImage2D(D.TEXTURE_2D, 0, D.RGB, D.RGB, D.UNSIGNED_BYTE, K)), D.drawArrays(D.TRIANGLES, 0, 6) } else { var S = t(e, B.width / B.height, .1, 100); w(e); var T = p(); T = q(T, -o, "z"), T = q(T, -b, "x"), T = q(T, d, "y"), T = r(T), D.uniformMatrix4fv(C.perspUniform, !1, new Float32Array(s(S))), D.uniformMatrix4fv(C.cubeUniform, !1, new Float32Array(s(T))); var U = x(S, T); if (C.nodeCache.sort(g), C.nodeCache.length > 200 && C.nodeCache.length > C.currentNodes.length + 50) for (var V = C.nodeCache.splice(200, C.nodeCache.length - 200), i = 0; i < V.length; i++)D.deleteTexture(V[i].texture); C.currentNodes = []; var W = ["f", "b", "u", "d", "l", "r"]; for (j = 0; j < 6; j++) { n(U, new m(I[j], W[j], 1, 0, 0, K.fullpath), b, d, e) } for (C.currentNodes.sort(k), i = 0; i < C.currentNodes.length; i++)if (!C.currentNodes[i].texture) { setTimeout(v(C.currentNodes[i]), 0); break } l() } if (f.returnImage !== c) return B.toDataURL("image/png") } else { j = G / 2; var X = { f: "translate3d(-" + (j + 2) + "px, -" + (j + 2) + "px, -" + j + "px)", b: "translate3d(" + (j + 2) + "px, -" + (j + 2) + "px, " + j + "px) rotateX(180deg) rotateZ(180deg)", u: "translate3d(-" + (j + 2) + "px, -" + j + "px, " + (j + 2) + "px) rotateX(270deg)", d: "translate3d(-" + (j + 2) + "px, " + j + "px, -" + (j + 2) + "px) rotateX(90deg)", l: "translate3d(-" + j + "px, -" + (j + 2) + "px, " + (j + 2) + "px) rotateX(180deg) rotateY(90deg) rotateZ(180deg)", r: "translate3d(" + j + "px, -" + (j + 2) + "px, -" + (j + 2) + "px) rotateY(270deg)" }; h = 1 / Math.tan(e / 2); var Y = h * B.width / (a.devicePixelRatio || 1) / 2 + "px", Z = "perspective(" + Y + ") translateZ(" + Y + ") rotateX(" + b + "rad) rotateY(" + d + "rad) ", $ = Object.keys(X); for (i = 0; i < 6; i++) { var _ = H.querySelector(".pnlm-" + $[i] + "face").style; _.webkitTransform = Z + X[$[i]], _.transform = Z + X[$[i]] } } }, this.isLoading = function () { if (D && "multires" == L) for (var a = 0; a < C.currentNodes.length; a++)if (!C.currentNodes[a].textureLoaded) return !0; return !1 }, this.getCanvas = function () { return B }; var R = function () { function a() { var a = this; this.texture = this.callback = null, this.image = new Image, this.image.crossOrigin = "anonymous", this.image.addEventListener("load", function () { u(a.image, a.texture), a.callback(a.texture), c(a) }) } function b(a, b, c) { this.src = a, this.texture = b, this.callback = c } function c(a) { if (f.length) { var b = f.shift(); a.loadTexture(b.src, b.texture, b.callback) } else e[d++] = a } var d = 4, e = {}, f = []; a.prototype.loadTexture = function (a, b, c) { this.texture = b, this.callback = c, this.image.src = a }; for (var g = 0; g < d; g++)e[g] = new a; return function (a, c) { var g = D.createTexture(); return d ? e[--d].loadTexture(a, g, c) : f.push(new b(a, g, c)), g } }() } var e = ["attribute vec2 a_texCoord;", "varying vec2 v_texCoord;", "void main() {", "gl_Position = vec4(a_texCoord, 0.0, 1.0);", "v_texCoord = a_texCoord;", "}"].join(""), f = ["attribute vec3 a_vertCoord;", "attribute vec2 a_texCoord;", "uniform mat4 u_cubeMatrix;", "uniform mat4 u_perspMatrix;", "varying mediump vec2 v_texCoord;", "void main(void) {", "gl_Position = u_perspMatrix * u_cubeMatrix * vec4(a_vertCoord, 1.0);", "v_texCoord = a_texCoord;", "}"].join(""), g = ["precision mediump float;", "uniform float u_aspectRatio;", "uniform float u_psi;", "uniform float u_theta;", "uniform float u_f;", "uniform float u_h;", "uniform float u_v;", "uniform float u_vo;", "uniform float u_rot;", "const float PI = 3.14159265358979323846264;", "uniform sampler2D u_image;", "uniform samplerCube u_imageCube;", "varying vec2 v_texCoord;", "uniform vec4 u_backgroundColor;", "void main() {", "float x = v_texCoord.x * u_aspectRatio;", "float y = v_texCoord.y;", "float sinrot = sin(u_rot);", "float cosrot = cos(u_rot);", "float rot_x = x * cosrot - y * sinrot;", "float rot_y = x * sinrot + y * cosrot;", "float sintheta = sin(u_theta);", "float costheta = cos(u_theta);", "float a = u_f * costheta - rot_y * sintheta;", "float root = sqrt(rot_x * rot_x + a * a);", "float lambda = atan(rot_x / root, a / root) + u_psi;", "float phi = atan((rot_y * costheta + u_f * sintheta) / root);"].join("\n"), h = g + ["float cosphi = cos(phi);", "gl_FragColor = textureCube(u_imageCube, vec3(cosphi*sin(lambda), sin(phi), cosphi*cos(lambda)));", "}"].join("\n"), i = g + ["lambda = mod(lambda + PI, PI * 2.0) - PI;", "vec2 coord = vec2(lambda / PI, phi / (PI / 2.0));", "if(coord.x < -u_h || coord.x > u_h || coord.y < -u_v + u_vo || coord.y > u_v + u_vo)", "gl_FragColor = u_backgroundColor;", "else", "gl_FragColor = texture2D(u_image, vec2((coord.x + u_h) / (u_h * 2.0), (-coord.y + u_v + u_vo) / (u_v * 2.0)));", "}"].join("\n"), j = ["varying mediump vec2 v_texCoord;", "uniform sampler2D u_sampler;", "void main(void) {", "gl_FragColor = texture2D(u_sampler, v_texCoord);", "}"].join(""); return { renderer: function (a, b, c, e) { return new d(a, b, c, e) } } }(window, document), window.requestAnimationFrame || (window.requestAnimationFrame = function () { return window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame || function (a, b) { window.setTimeout(a, 1e3 / 60) } }()), window.jdpanorama = function (a, b, c) {
        "use strict"; function d(d, e) {
            function f(b) { a.removeEventListener("deviceorientation", f), b && null !== b.alpha && null !== b.beta && null !== b.gamma ? (Ta.container.appendChild(Ta.orientation), Ua = !0, Va && fa()) : Ua = !1 } function g() {
                var e = b.createElement("div"); if (e.innerHTML = "\x3c!--[if lte IE 9]><i></i><![endif]--\x3e", 1 == e.getElementsByTagName("i").length) return void k(); oa = ia.hfov, pa = ia.pitch; var f, g; if ("cubemap" == ia.type) { for (la = [], f = 0; f < 6; f++)la.push(new Image), la[f].crossOrigin = "anonymous"; Ra.load.lbox.style.display = "block", Ra.load.lbar.style.display = "none" } else if ("multires" == ia.type) { var l = JSON.parse(JSON.stringify(ia.multiRes)); ia.basePath && ia.multiRes.basePath && !/^(?:[a-z]+:)?\/\//i.test(ia.multiRes.basePath) ? l.basePath = ia.basePath + ia.multiRes.basePath : ia.multiRes.basePath ? l.basePath = ia.multiRes.basePath : ia.basePath && (l.basePath = ia.basePath), la = l } else if (!0 === ia.dynamic) la = ia.panorama; else { if (ia.panorama === c) return void k("No panorama image was specified."); la = new Image } if ("cubemap" == ia.type) { var m = 6, n = function () { 0 === --m && i() }, o = function (a) { var c = b.createElement("a"); c.href = a.target.src, c.innerHTML = c.href, k("The file " + c.outerHTML + " could not be accessed.") }; for (f = 0; f < la.length; f++)la[f].onload = n, la[f].onerror = o, g = ia.cubeMap[f], ia.basePath && !h(g) && (g = ia.basePath + g), la[f].src = encodeURI(g) } else if ("multires" == ia.type) i(); else if (g = "", ia.basePath && (g = ia.basePath), !0 !== ia.dynamic) {
                g = h(ia.panorama) ? ia.panorama : g + ia.panorama, la.onload = function () { a.URL.revokeObjectURL(this.src), i() }; var p = new XMLHttpRequest; p.onloadend = function () { if (200 != p.status) { var a = b.createElement("a"); a.href = encodeURI(g), a.innerHTML = a.href, k("The file " + a.outerHTML + " could not be accessed.") } j(this.response), Ra.load.msg.innerHTML = "" },
                    p.onprogress = function (a) { if (a.lengthComputable) { var b = a.loaded / a.total * 100; Ra.load.lbarFill.style.width = b + "%"; var c, d, e; a.total > 1e6 ? (c = "MB", d = (a.loaded / 1e6).toFixed(2), e = (a.total / 1e6).toFixed(2)) : a.total > 1e3 ? (c = "kB", d = (a.loaded / 1e3).toFixed(1), e = (a.total / 1e3).toFixed(1)) : (c = "B", d = a.loaded, e = a.total), Ra.load.msg.innerHTML = d + " / " + e + " " + c } else Ra.load.lbox.style.display = "block", Ra.load.lbar.style.display = "none" }; try { p.open("GET", g, !0) } catch (a) { k("There is something wrong with the panorama URL.") } p.responseType = "blob", p.setRequestHeader("Accept", "image/*,*/*;q=0.9"), p.send()
                } d.classList.add("jdpano-grab"), d.classList.remove("jdpano-grabbing")
            } function h(a) { return new RegExp("^(?:[a-z]+:)?//", "i").test(a) || "/" == a[0] || "blob:" == a.slice(0, 5) } function i() { ja || (ja = new libpanorama.renderer(Pa)), Da || (Da = !0, Qa.addEventListener("mousedown", n, !1), b.addEventListener("mousemove", q, !1), b.addEventListener("mouseup", r, !1), ia.mouseZoom && (d.addEventListener("mousewheel", y, !1), d.addEventListener("DOMMouseScroll", y, !1)), ia.doubleClickZoom && Qa.addEventListener("dblclick", o, !1), d.addEventListener("mozfullscreenchange", Y, !1), d.addEventListener("webkitfullscreenchange", Y, !1), d.addEventListener("msfullscreenchange", Y, !1), d.addEventListener("fullscreenchange", Y, !1), a.addEventListener("resize", G, !1), a.addEventListener("orientationchange", G, !1), d.addEventListener("keydown", z, !1), d.addEventListener("keyup", B, !1), d.addEventListener("blur", A, !1), b.addEventListener("mouseleave", r, !1), Qa.addEventListener("touchstart", s, !1), Qa.addEventListener("touchmove", t, !1), Qa.addEventListener("touchend", u, !1), Qa.addEventListener("pointerdown", v, !1), Qa.addEventListener("pointermove", w, !1), Qa.addEventListener("pointerup", x, !1), Qa.addEventListener("pointerleave", x, !1), a.navigator.pointerEnabled && (d.style.touchAction = "none")), O(), setTimeout(function () { Ca = !0 }, 500) } function j(b) { var d = new FileReader; d.addEventListener("loadend", function () { var e = d.result; if (navigator.userAgent.toLowerCase().match(/(iphone|ipod|ipad).* os 8_/)) { var f = e.indexOf("ÿÂ"); (f < 0 || f > 65536) && k("Due to iOS 8's broken WebGL implementation, only progressive encoded JPEGs work for your device (this panorama uses standard encoding).") } var g = e.indexOf("<x:xmpmeta"); if (g > -1 && !0 !== ia.ignoreGPanoXMP) { var h = e.substring(g, e.indexOf("</x:xmpmeta>") + 12), i = function (a) { var b; return h.indexOf(a + '="') >= 0 ? (b = h.substring(h.indexOf(a + '="') + a.length + 2), b = b.substring(0, b.indexOf('"'))) : h.indexOf(a + ">") >= 0 && (b = h.substring(h.indexOf(a + ">") + a.length + 1), b = b.substring(0, b.indexOf("<"))), b !== c ? Number(b) : null }, j = { fullWidth: i("GPano:FullPanoWidthPixels"), croppedWidth: i("GPano:CroppedAreaImageWidthPixels"), fullHeight: i("GPano:FullPanoHeightPixels"), croppedHeight: i("GPano:CroppedAreaImageHeightPixels"), topPixels: i("GPano:CroppedAreaTopPixels"), heading: i("GPano:PoseHeadingDegrees"), horizonPitch: i("GPano:PosePitchDegrees"), horizonRoll: i("GPano:PoseRollDegrees") }; null !== j.fullWidth && null !== j.croppedWidth && null !== j.fullHeight && null !== j.croppedHeight && null !== j.topPixels && (Ka.indexOf("haov") < 0 && (ia.haov = j.croppedWidth / j.fullWidth * 360), Ka.indexOf("vaov") < 0 && (ia.vaov = j.croppedHeight / j.fullHeight * 180), Ka.indexOf("vOffset") < 0 && (ia.vOffset = -180 * ((j.topPixels + j.croppedHeight / 2) / j.fullHeight - .5)), null !== j.heading && Ka.indexOf("northOffset") < 0 && (ia.northOffset = j.heading, !1 !== ia.compass && (ia.compass = !0)), null !== j.horizonPitch && null !== j.horizonRoll && (Ka.indexOf("horizonPitch") < 0 && (ia.horizonPitch = j.horizonPitch), Ka.indexOf("horizonRoll") < 0 && (ia.horizonRoll = j.horizonRoll))) } la.src = a.URL.createObjectURL(b) }), d.readAsBinaryString !== c ? d.readAsBinaryString(b) : d.readAsText(b) } function k(a) { a === c && (a = "Your browser does not have the necessary WebGL support to display this panorama."), Ra.errorMsg.innerHTML = "<p>" + a + "</p>", Ta.load.style.display = "none", Ra.load.box.style.display = "none", Ra.errorMsg.style.display = "table", Ba = !0, Pa.style.display = "none", ha("error", a) } function l() { Ba && (Ra.load.box.style.display = "none", Ra.errorMsg.style.display = "none", Ba = !1, ha("errorcleared")) } function m(a) { var b = d.getBoundingClientRect(), c = {}; return c.x = a.clientX - b.left, c.y = a.clientY - b.top, c } function n(a) { if (a.preventDefault(), d.focus(), Aa && ia.draggable) { var b = m(a); if (ia.hotSpotDebug) { var c = p(a); console.log("Pitch: " + c[0] + ", Yaw: " + c[1] + ", Center Pitch: " + ia.pitch + ", Center Yaw: " + ia.yaw + ", HFOV: " + ia.hfov) } ba(), ea(), ia.roll = 0, Ea.hfov = 0, ra = !0, sa = Date.now(), ta = b.x, ua = b.y, wa = ia.yaw, xa = ia.pitch, d.classList.add("jdpano-grabbing"), d.classList.remove("jdpano-grab"), ha("mousedown", a), H() } } function o(a) { if (ia.minHfov === ia.hfov) qa.setHfov(oa, 1e3); else { var b = p(a); qa.lookAt(b[0], b[1], ia.minHfov, 1e3) } } function p(b) { var c = m(b), d = ja.getCanvas(), e = d.width / (a.devicePixelRatio || 1), f = d.height / (a.devicePixelRatio || 1), g = c.x / e * 2 - 1, h = (1 - c.y / f * 2) * f / e, i = 1 / Math.tan(ia.hfov * Math.PI / 360), j = Math.sin(ia.pitch * Math.PI / 180), k = Math.cos(ia.pitch * Math.PI / 180), l = i * k - h * j, n = Math.sqrt(g * g + l * l); return [180 * Math.atan((h * k + i * j) / n) / Math.PI, 180 * Math.atan2(g / n, l / n) / Math.PI + ia.yaw] } function q(b) { if (ra && Aa) { sa = Date.now(); var c = ja.getCanvas(), d = c.width / (a.devicePixelRatio || 1), e = c.height / (a.devicePixelRatio || 1), f = m(b), g = 180 * (Math.atan(ta / d * 2 - 1) - Math.atan(f.x / d * 2 - 1)) / Math.PI * ia.hfov / 90 + wa; Ea.yaw = (g - ia.yaw) % 360 * .2, ia.yaw = g; var h = 2 * Math.atan(Math.tan(ia.hfov / 360 * Math.PI) * e / d) * 180 / Math.PI, i = 180 * (Math.atan(f.y / e * 2 - 1) - Math.atan(ua / e * 2 - 1)) / Math.PI * h / 90 + xa; Ea.pitch = .2 * (i - ia.pitch), ia.pitch = i } } function r(a) { ra && (ra = !1, Date.now() - sa > 15 && (Ea.pitch = Ea.yaw = 0), d.classList.add("jdpano-grab"), d.classList.remove("jdpano-grabbing"), sa = Date.now(), ha("mouseup", a)) } function s(a) { if (Aa && ia.draggable) { ba(), ea(), ia.roll = 0, Ea.hfov = 0; var b = m(a.targetTouches[0]); if (ta = b.x, ua = b.y, 2 == a.targetTouches.length) { var c = m(a.targetTouches[1]); ta += .5 * (c.x - b.x), ua += .5 * (c.y - b.y), va = Math.sqrt((b.x - c.x) * (b.x - c.x) + (b.y - c.y) * (b.y - c.y)) } ra = !0, sa = Date.now(), wa = ia.yaw, xa = ia.pitch, H() } } function t(a) { if (a.preventDefault(), Aa && (sa = Date.now()), ra && Aa) { var b = m(a.targetTouches[0]), c = b.x, d = b.y; if (2 == a.targetTouches.length && -1 != va) { var e = m(a.targetTouches[1]); c += .5 * (e.x - b.x), d += .5 * (e.y - b.y); var f = Math.sqrt((b.x - e.x) * (b.x - e.x) + (b.y - e.y) * (b.y - e.y)); aa(ia.hfov + .1 * (va - f)), va = f } var g = ia.hfov / 360, h = (ta - c) * g + wa; Ea.yaw = (h - ia.yaw) % 360 * .2, ia.yaw = h; var i = (d - ua) * g + xa; Ea.pitch = .2 * (i - ia.pitch), ia.pitch = i } } function u() { ra = !1, Date.now() - sa > 150 && (Ea.pitch = Ea.yaw = 0), va = -1, sa = Date.now() } function v(a) { "touch" == a.pointerType && (Xa.push(a.pointerId), Ya.push({ clientX: a.clientX, clientY: a.clientY }), a.targetTouches = Ya, s(a), a.preventDefault()) } function w(a) { if ("touch" == a.pointerType) for (var b = 0; b < Xa.length; b++)if (a.pointerId == Xa[b]) return Ya[b] = { clientX: a.clientX, clientY: a.clientY }, a.targetTouches = Ya, void t(a) } function x(a) { if ("touch" == a.pointerType) { for (var b = !1, d = 0; d < Xa.length; d++)a.pointerId == Xa[d] && (Xa[d] = c), Xa[d] && (b = !0); b || (Xa = [], Ya = [], u()), a.preventDefault() } } function y(a) { Aa && ("fullscreenonly" != ia.mouseZoom || za) && (a.preventDefault(), ba(), sa = Date.now(), a.wheelDeltaY ? (aa(ia.hfov - .05 * a.wheelDeltaY), Ea.hfov = a.wheelDelta < 0 ? 1 : -1) : a.wheelDelta ? (aa(ia.hfov - .05 * a.wheelDelta), Ea.hfov = a.wheelDelta < 0 ? 1 : -1) : a.detail && (aa(ia.hfov + 1.5 * a.detail), Ea.hfov = a.detail > 0 ? 1 : -1), H()) } function z(a) { ba(), sa = Date.now(), ea(), ia.roll = 0; var b = a.which || a.keycode; Oa.indexOf(b) < 0 || (a.preventDefault(), 27 == b ? za && X() : C(b, !0)) } function A() { for (var a = 0; a < 10; a++)ya[a] = !1 } function B(a) { var b = a.which || a.keycode; Oa.indexOf(b) < 0 || (a.preventDefault(), C(b, !1)) } function C(a, b) { var c = !1; switch (a) { case 109: case 189: case 17: case 173: ya[0] != b && (c = !0), ya[0] = b; break; case 107: case 187: case 16: case 61: ya[1] != b && (c = !0), ya[1] = b; break; case 38: ya[2] != b && (c = !0), ya[2] = b; break; case 87: ya[6] != b && (c = !0), ya[6] = b; break; case 40: ya[3] != b && (c = !0), ya[3] = b; break; case 83: ya[7] != b && (c = !0), ya[7] = b; break; case 37: ya[4] != b && (c = !0), ya[4] = b; break; case 65: ya[8] != b && (c = !0), ya[8] = b; break; case 39: ya[5] != b && (c = !0), ya[5] = b; break; case 68: ya[9] != b && (c = !0), ya[9] = b }c && b && (ma = "undefined" != typeof performance && performance.now() ? performance.now() : Date.now(), H()) } function D() { if (Aa) { var a, b = !1, d = ia.pitch, e = ia.yaw, f = ia.hfov; a = "undefined" != typeof performance && performance.now() ? performance.now() : Date.now(), ma === c && (ma = a); var g = (a - ma) * ia.hfov / 1700; g = Math.min(g, 1), ya[0] && !0 === ia.keyboardZoom && (aa(ia.hfov + (.8 * Ea.hfov + .5) * g), b = !0), ya[1] && !0 === ia.keyboardZoom && (aa(ia.hfov + (.8 * Ea.hfov - .2) * g), b = !0), (ya[2] || ya[6]) && (ia.pitch += (.8 * Ea.pitch + .2) * g, b = !0), (ya[3] || ya[7]) && (ia.pitch += (.8 * Ea.pitch - .2) * g, b = !0), (ya[4] || ya[8]) && (ia.yaw += (.8 * Ea.yaw - .2) * g, b = !0), (ya[5] || ya[9]) && (ia.yaw += (.8 * Ea.yaw + .2) * g, b = !0), b && (sa = Date.now()); Date.now(); if (ia.autoRotate) { if (a - ma > .001) { var h = (a - ma) / 1e3, i = (Ea.yaw / h * g - .2 * ia.autoRotate) * h; i = (-ia.autoRotate > 0 ? 1 : -1) * Math.min(Math.abs(ia.autoRotate * h), Math.abs(i)), ia.yaw += i } ia.autoRotateStopDelay && (ia.autoRotateStopDelay -= a - ma, ia.autoRotateStopDelay <= 0 && (ia.autoRotateStopDelay = !1, Ha = ia.autoRotate, ia.autoRotate = 0)) } if (Ia.pitch && (E("pitch"), d = ia.pitch), Ia.yaw && (E("yaw"), e = ia.yaw), Ia.hfov && (E("hfov"), f = ia.hfov), g > 0 && !ia.autoRotate) { var j = .85; ya[4] || ya[5] || ya[8] || ya[9] || Ia.yaw || (ia.yaw += Ea.yaw * g * j), ya[2] || ya[3] || ya[6] || ya[7] || Ia.pitch || (ia.pitch += Ea.pitch * g * j), ya[0] || ya[1] || Ia.hfov || aa(ia.hfov + Ea.hfov * g * j) } if (ma = a, g > 0) { Ea.yaw = .8 * Ea.yaw + (ia.yaw - e) / g * .2, Ea.pitch = .8 * Ea.pitch + (ia.pitch - d) / g * .2, Ea.hfov = .8 * Ea.hfov + (ia.hfov - f) / g * .2; var k = ia.autoRotate ? Math.abs(ia.autoRotate) : 5; Ea.yaw = Math.min(k, Math.max(Ea.yaw, -k)), Ea.pitch = Math.min(k, Math.max(Ea.pitch, -k)), Ea.hfov = Math.min(k, Math.max(Ea.hfov, -k)) } ya[0] && ya[0] && (Ea.hfov = 0), (ya[2] || ya[6]) && (ya[3] || ya[7]) && (Ea.pitch = 0), (ya[4] || ya[8]) && (ya[5] || ya[9]) && (Ea.yaw = 0) } } function E(a) { var b = Ia[a], c = Math.min(1, Math.max((Date.now() - b.startTime) / 1e3 / (b.duration / 1e3), 0)), d = b.startPosition + ia.animationTimingFunction(c) * (b.endPosition - b.startPosition); if (b.endPosition > b.startPosition && d >= b.endPosition || b.endPosition < b.startPosition && d <= b.endPosition) { d = b.endPosition, Ea[a] = 0; var e = Ia[a].callback, f = Ia[a].callbackArgs; delete Ia[a], "function" == typeof e && e(f) } ia[a] = d } function F(a) { return a < .5 ? 2 * a * a : (4 - 2 * a) * a - 1 } function G() { Y() } function H() { Fa || (Fa = !0, I()) } function I() { if (J(), na && clearTimeout(na), ra || Ga) requestAnimationFrame(I); else if (ya[0] || ya[1] || ya[2] || ya[3] || ya[4] || ya[5] || ya[6] || ya[7] || ya[8] || ya[9] || ia.autoRotate || Ia.pitch || Ia.yaw || Ia.hfov || Math.abs(Ea.yaw) > .01 || Math.abs(Ea.pitch) > .01 || Math.abs(Ea.hfov) > .01) D(), ia.autoRotateInactivityDelay >= 0 && Ha && Date.now() - sa > ia.autoRotateInactivityDelay && !ia.autoRotate && (ia.autoRotate = Ha, qa.lookAt(pa, c, oa, 3e3)), requestAnimationFrame(I); else if (ja && (ja.isLoading() || !0 === ia.dynamic && La)) requestAnimationFrame(I); else { Fa = !1, ma = c; var a = ia.autoRotateInactivityDelay - (Date.now() - sa); a > 0 ? na = setTimeout(function () { ia.autoRotate = Ha, qa.lookAt(pa, c, oa, 3e3), H() }, a) : ia.autoRotateInactivityDelay >= 0 && Ha && (ia.autoRotate = Ha, qa.lookAt(pa, c, oa, 3e3), H()) } } function J() { var a; if (Aa) { ia.yaw > 180 ? ia.yaw -= 360 : ia.yaw < -180 && (ia.yaw += 360), a = ia.yaw; var b = ia.maxYaw - ia.minYaw, c = -180, d = 180; b < 360 && (c = ia.minYaw + ia.hfov / 2, d = ia.maxYaw - ia.hfov / 2, b < ia.hfov && (c = d = (c + d) / 2)), ia.yaw = Math.max(c, Math.min(d, ia.yaw)), !1 !== ia.autoRotate && a != ia.yaw && (ia.autoRotate *= -1); var e = ja.getCanvas(), f = 2 * Math.atan(Math.tan(ia.hfov / 180 * Math.PI * .5) / (e.width / e.height)) / Math.PI * 180, g = ia.minPitch + f / 2, h = ia.maxPitch - f / 2; ia.maxPitch - ia.minPitch < f && (g = h = (g + h) / 2), isNaN(g) && (g = -90), isNaN(h) && (h = 90), ia.pitch = Math.max(g, Math.min(h, ia.pitch)), ja.render(ia.pitch * Math.PI / 180, ia.yaw * Math.PI / 180, ia.hfov * Math.PI / 180, { roll: ia.roll * Math.PI / 180 }), U(), ia.compass && (Wa.style.transform = "rotate(" + (-ia.yaw - ia.northOffset) + "deg)", Wa.style.webkitTransform = "rotate(" + (-ia.yaw - ia.northOffset) + "deg)") } } function K(a, b, c, d) { this.w = a, this.x = b, this.y = c, this.z = d } function L(a, b, c) { var d = [b ? b * Math.PI / 180 / 2 : 0, c ? c * Math.PI / 180 / 2 : 0, a ? a * Math.PI / 180 / 2 : 0], e = [Math.cos(d[0]), Math.cos(d[1]), Math.cos(d[2])], f = [Math.sin(d[0]), Math.sin(d[1]), Math.sin(d[2])]; return new K(e[0] * e[1] * e[2] - f[0] * f[1] * f[2], f[0] * e[1] * e[2] - e[0] * f[1] * f[2], e[0] * f[1] * e[2] + f[0] * e[1] * f[2], e[0] * e[1] * f[2] + f[0] * f[1] * e[2]) } function M(b, c, d) { var e = L(b, c, d); e = e.multiply(new K(Math.sqrt(.5), -Math.sqrt(.5), 0, 0)); var f = a.orientation ? -a.orientation * Math.PI / 180 / 2 : 0; return e.multiply(new K(Math.cos(f), 0, -Math.sin(f), 0)) } function N(a) { var b = M(a.alpha, a.beta, a.gamma).toEulerAngles(); ia.pitch = b[0] / Math.PI * 180, ia.roll = -b[1] / Math.PI * 180, ia.yaw = -b[2] / Math.PI * 180 + ia.northOffset } function O() { try { var a = {}; ia.horizonPitch !== c && (a.horizonPitch = ia.horizonPitch * Math.PI / 180), ia.horizonRoll !== c && (a.horizonRoll = ia.horizonRoll * Math.PI / 180), ia.backgroundColor !== c && (a.backgroundColor = ia.backgroundColor), ja.init(la, ia.type, ia.dynamic, ia.haov * Math.PI / 180, ia.vaov * Math.PI / 180, ia.vOffset * Math.PI / 180, P, a), !0 !== ia.dynamic && (la = c) } catch (a) { if ("webgl error" == a.type || "no webgl" == a.type) k(); else { if ("webgl size error" != a.type) throw k("Unknown error. Check developer console."), a; k("This panorama is too big for your device! It's " + a.width + "px wide, but your device only supports images up to " + a.maxWidth + "px wide. Try another device. (If you're the author, try scaling down the image.)") } } } function P() { if (ia.sceneFadeDuration && ja.fadeImg !== c) { ja.fadeImg.style.opacity = 0; var a = ja.fadeImg; delete ja.fadeImg, setTimeout(function () { Pa.removeChild(a), ha("scenechangefadedone") }, ia.sceneFadeDuration) } ia.compass ? Wa.style.display = "inline" : Wa.style.display = "none", R(), Ra.load.box.style.display = "none", ka !== c && (Pa.removeChild(ka), ka = c), Aa = !0, ha("load"), ia.ready && ia.ready(), H() } function Q(a) { a.pitch = Number(a.pitch) || 0, a.yaw = Number(a.yaw) || 0; var c = b.createElement("div"); c.className = "jdpano-hotspot-base", a.cssClass ? c.className += " " + a.cssClass : c.className += " jdpano-hotspot jdpano-sprite jdpano-" + ga(a.type); var d = b.createElement("span"); a.text && (d.innerHTML = ga(a.text)); var e; if (a.video) { var f = b.createElement("video"), g = a.video; ia.basePath && !h(g) && (g = ia.basePath + g), f.src = encodeURI(g), f.controls = !0, f.style.width = a.width + "px", Pa.appendChild(c), d.appendChild(f) } else if (a.image) { var g = a.image; ia.basePath && !h(g) && (g = ia.basePath + g), e = b.createElement("a"), e.href = encodeURI(a.URL ? a.URL : g), e.target = "_blank", d.appendChild(e); var i = b.createElement("img"); i.src = encodeURI(g), i.style.width = a.width + "px", i.style.paddingTop = "5px", Pa.appendChild(c), e.appendChild(i), d.style.maxWidth = "initial" } else a.URL ? (e = b.createElement("a"), e.href = encodeURI(a.URL), e.target = "_blank", Pa.appendChild(e), c.style.cursor = "pointer", d.style.cursor = "pointer", e.appendChild(c)) : (a.sceneId && (c.onclick = c.ontouchend = function () { return c.clicked || (c.clicked = !0, da(a.sceneId, a.targetPitch, a.targetYaw, a.targetHfov)), !1 }, c.style.cursor = "pointer", d.style.cursor = "pointer"), Pa.appendChild(c)); a.createTooltipFunc ? a.createTooltipFunc(c, a.createTooltipArgs) : (a.text || a.video || a.image) && (c.classList.add("jdpano-tooltip"), c.appendChild(d), d.style.width = d.scrollWidth - 20 + "px", d.style.marginLeft = -(d.scrollWidth - c.offsetWidth) / 2 + "px", d.style.marginTop = -d.scrollHeight - 12 + "px"), a.clickHandler && (c.addEventListener("click", function (b) { a.clickHandler(a.clickHandlerArgs) }, "false"), c.style.cursor = "pointer", d.style.cursor = "pointer"), a.div = c } function R() { Ma || (ia.hotSpots ? (ia.hotSpots = ia.hotSpots.sort(function (a, b) { return a.pitch < b.pitch }), ia.hotSpots.forEach(Q)) : ia.hotSpots = [], Ma = !0, U()) } function S() { if (ia.hotSpots) for (var a = 0; a < ia.hotSpots.length; a++) { for (var b = ia.hotSpots[a].div; b.parentNode != Pa;)b = b.parentNode; Pa.removeChild(b), delete ia.hotSpots[a].div } Ma = !1, delete ia.hotSpots } function T(b) { var c = Math.sin(b.pitch * Math.PI / 180), d = Math.cos(b.pitch * Math.PI / 180), e = Math.sin(ia.pitch * Math.PI / 180), f = Math.cos(ia.pitch * Math.PI / 180), g = Math.cos((-b.yaw + ia.yaw) * Math.PI / 180), h = c * e + d * g * f; if (b.yaw <= 90 && b.yaw > -90 && h <= 0 || (b.yaw > 90 || b.yaw <= -90) && h <= 0) b.div.style.visibility = "hidden"; else { var i = Math.sin((-b.yaw + ia.yaw) * Math.PI / 180), j = Math.tan(ia.hfov * Math.PI / 360); b.div.style.visibility = "visible"; var k = ja.getCanvas(), l = k.width / (a.devicePixelRatio || 1), m = k.height / (a.devicePixelRatio || 1), n = [-l / j * i * d / h / 2, -l / j * (c * f - d * g * e) / h / 2], o = Math.sin(ia.roll * Math.PI / 180), p = Math.cos(ia.roll * Math.PI / 180); n = [n[0] * p - n[1] * o, n[0] * o + n[1] * p], n[0] += (l - b.div.offsetWidth) / 2, n[1] += (m - b.div.offsetHeight) / 2; var q = "translate(" + n[0] + "px, " + n[1] + "px) translateZ(9999px) rotate(" + ia.roll + "deg)"; b.div.style.webkitTransform = q, b.div.style.MozTransform = q, b.div.style.transform = q } } function U() { ia.hotSpots.forEach(T) } function V(a) { ia = {}; var b, c = ["haov", "vaov", "vOffset", "northOffset", "horizonPitch", "horizonRoll"]; Ka = []; for (b in Na) Na.hasOwnProperty(b) && (ia[b] = Na[b]); for (b in e.defaults) e.defaults.hasOwnProperty(b) && (ia[b] = e.defaults[b], c.indexOf(b) >= 0 && Ka.push(b)); if (null !== a && "" !== a && e.scenes && e.scenes[a]) { var d = e.scenes[a]; for (b in d) d.hasOwnProperty(b) && (ia[b] = d[b], c.indexOf(b) >= 0 && Ka.push(b)); ia.scene = a } for (b in e) e.hasOwnProperty(b) && (ia[b] = e[b], c.indexOf(b) >= 0 && Ka.push(b)) } function W(a) { if ((a = a || !1) && "preview" in ia) { var d = ia.preview; ia.basePath && !h(d) && (d = ia.basePath + d), ka = b.createElement("div"), ka.className = "jdpano-preview-img", ka.style.backgroundImage = "url('" + encodeURI(d) + "')", Pa.appendChild(ka) } var e = ia.title, f = ia.author; a && ("previewTitle" in ia && (ia.title = ia.previewTitle), "previewAuthor" in ia && (ia.author = ia.previewAuthor)), ia.hasOwnProperty("title") || (Ra.title.innerHTML = ""), ia.hasOwnProperty("author") || (Ra.author.innerHTML = ""), Ra.container.style.display = "none"; for (var i in ia) if (ia.hasOwnProperty(i)) switch (i) { case "title": Ra.title.innerHTML = ga(ia[i]), Ra.container.style.display = "inline"; break; case "author": Ra.author.innerHTML = "by " + ga(ia[i]), Ra.container.style.display = "inline"; break; case "fallback": Ra.errorMsg.innerHTML = '<p>Your browser does not support WebGL.<br><a href="' + encodeURI(ia[i]) + '" target="_blank">Click here to view this panorama in an alternative viewer.</a></p>'; break; case "hfov": aa(Number(ia[i])); break; case "autoLoad": !0 === ia[i] && ja === c && (Ra.load.box.style.display = "inline", Ta.load.style.display = "none", g()); break; case "showZoomCtrl": ia[i] && 0 != ia.showControls ? Ta.zoom.style.display = "block" : Ta.zoom.style.display = "none"; break; case "showFullscreenCtrl": ia[i] && 0 != ia.showControls && ("fullscreen" in b || "mozFullScreen" in b || "webkitIsFullScreen" in b || "msFullscreenElement" in b) ? Ta.fullscreen.style.display = "block" : Ta.fullscreen.style.display = "none"; break; case "hotSpotDebug": ia[i] ? Sa.style.display = "block" : Sa.style.display = "none"; break; case "showControls": ia[i] || (Ta.orientation.style.display = "none", Ta.zoom.style.display = "none", Ta.fullscreen.style.display = "none"); break; case "orientationOnByDefault": ia[i] && (Ua === c ? Va = !0 : !0 === Ua && fa()); break; case "loadButtonLabel": Ta.load.innerHTML = "<p>" + ga(ia[i]) + "</p>" }a && (e ? ia.title = e : delete ia.title, f ? ia.author = f : delete ia.author) } function X(a) { if (Aa && !Ba || a) if (za) b.exitFullscreen ? b.exitFullscreen() : b.mozCancelFullScreen ? b.mozCancelFullScreen() : b.webkitCancelFullScreen ? b.webkitCancelFullScreen() : b.msExitFullscreen && b.msExitFullscreen(), a && (za = !1); else { try { d.requestFullscreen ? d.requestFullscreen() : d.mozRequestFullScreen ? d.mozRequestFullScreen() : d.msRequestFullscreen ? d.msRequestFullscreen() : d.webkitRequestFullScreen && d.webkitRequestFullScreen() } catch (a) { } a && (za = !0) } } function Y() { b.fullscreen || b.mozFullScreen || b.webkitIsFullScreen || b.msFullscreenElement ? (Ta.fullscreen.classList.add("jdpano-fullscreen-toggle-button-active"), ia.onFullScreen && ia.onFullScreen(), za = !0) : (Ta.fullscreen.classList.remove("jdpano-fullscreen-toggle-button-active"), ia.onCancelFullScreen && ia.onCancelFullScreen(), za = !1), ja.resize(), aa(ia.hfov), H() } function Z() { Aa && (aa(ia.hfov - 5), H()) } function $() { Aa && (aa(ia.hfov + 5), H()) } function _(a) { var b = ia.minHfov; return "multires" == ia.type && ja && (b = Math.min(b, ja.getCanvas().width / (ia.multiRes.cubeResolution / 90 * .9))), b > ia.maxHfov ? (console.log("HFOV bounds do not make sense (minHfov > maxHfov)."), ia.hfov) : a < b ? b : a > ia.maxHfov ? ia.maxHfov : a } function aa(a) { ia.hfov = _(a) } function ba() { Ia = {}, Ha = ia.autoRotate ? ia.autoRotate : Ha, ia.autoRotate = !1 } function ca() { l(), Ta.load.style.display = "none", Ra.load.box.style.display = "inline", g() } function da(a, b, d, f, g) { Aa = !1, Ia = {}; var h, i, j, k; if (ia.sceneFadeDuration && !g) { h = new Image, h.className = "jdpano-fade-img", h.style.transition = "opacity " + ia.sceneFadeDuration / 1e3 + "s", h.style.width = "100%", h.style.height = "100%", h.onload = function () { da(a, b, d, f, !0) }; var l = ja.render(ia.pitch * Math.PI / 180, ia.yaw * Math.PI / 180, ia.hfov * Math.PI / 180, { returnImage: !0 }); return l !== c && (h.src = l), Pa.appendChild(h), void (ja.fadeImg = h) } i = "same" === b ? ia.pitch : b, j = "same" === d ? ia.yaw : "sameAzimuth" === d ? ia.yaw + ia.northOffset - e.scenes[a].northOffset : d, k = "same" === f ? ia.hfov : f, S(), V(a), Ea.yaw = Ea.pitch = Ea.hfov = 0, W(), i !== c && (ia.pitch = i), j !== c && (ia.yaw = j), k !== c && (ia.hfov = k), ha("scenechange", a), ca() } function ea() { a.removeEventListener("deviceorientation", N), Ta.orientation.classList.remove("jdpano-orientation-button-active"), Ga = !1 } function fa() { Ga = !0, a.addEventListener("deviceorientation", N), Ta.orientation.classList.add("jdpano-orientation-button-active"), requestAnimationFrame(I) } function ga(a) { return e.escapeHTML ? String(a).split(/&/g).join("&amp;").split('"').join("&quot;").split("'").join("&#39;").split("<").join("&lt;").split(">").join("&gt;").split("/").join("&#x2f;").split("\n").join("<br>") : String(a).split("\n").join("<br>") } function ha(a) { if (a in Ja) for (var b = 0; b < Ja[a].length; b++)Ja[a][b].apply(null, [].slice.call(arguments, 1)) } var ia, ja, ka, la, ma, na, oa, pa, qa = this, ra = !1, sa = Date.now(), ta = 0, ua = 0, va = -1, wa = 0, xa = 0, ya = new Array(10), za = !1, Aa = !1, Ba = !1, Ca = !1, Da = !1, Ea = { yaw: 0, pitch: 0, hfov: 0 }, Fa = !1, Ga = !1, Ha = 0, Ia = {}, Ja = {}, Ka = [], La = !1, Ma = !1, Na = { hfov: 100, minHfov: 50, maxHfov: 120, pitch: 0, minPitch: c, maxPitch: c, yaw: 0, minYaw: -180, maxYaw: 180, roll: 0, haov: 360, vaov: 180, vOffset: 0, autoRotate: !1, autoRotateInactivityDelay: -1, autoRotateStopDelay: c, type: "equirectangular", northOffset: 0, showFullscreenCtrl: !0, dynamic: !1, doubleClickZoom: !0, keyboardZoom: !0, mouseZoom: !0, showZoomCtrl: !0, autoLoad: !1, showControls: !0, orientationOnByDefault: !1, hotSpotDebug: !1, backgroundColor: [0, 0, 0], animationTimingFunction: F, loadButtonLabel: "Click to\nLoad\nPanorama", draggable: !0 }, Oa = [16, 17, 27, 37, 38, 39, 40, 61, 65, 68, 83, 87, 107, 109, 173, 187, 189]; d = "string" == typeof d ? b.querySelector(d) : d, d.classList.add("jdpano-container"), d.tabIndex = 0; var Pa = b.createElement("div"); Pa.className = "jdpano-render-container", d.appendChild(Pa); var Qa = b.createElement("div"); Qa.className = "jdpano-dragfix", d.appendChild(Qa); var Ra = {}, Sa = b.createElement("div"); Sa.className = "jdpano-sprite jdpano-hot-spot-debug-indicator", d.appendChild(Sa), Ra.container = b.createElement("div"), Ra.container.className = "jdpano-panorama-info", Ra.title = b.createElement("div"), Ra.title.className = "jdpano-title-box", Ra.container.appendChild(Ra.title), Ra.author = b.createElement("div"), Ra.author.className = "jdpano-author-box", Ra.container.appendChild(Ra.author), d.appendChild(Ra.container), Ra.load = {}, Ra.load.box = b.createElement("div"), Ra.load.box.className = "jdpano-load-box", Ra.load.box.innerHTML = "<p>Loading...</p>", Ra.load.lbox = b.createElement("div"), Ra.load.lbox.className = "jdpano-lbox", Ra.load.lbox.innerHTML = '<div class="jdpano-loading"></div>', Ra.load.box.appendChild(Ra.load.lbox), Ra.load.lbar = b.createElement("div"), Ra.load.lbar.className = "jdpano-lbar", Ra.load.lbarFill = b.createElement("div"), Ra.load.lbarFill.className = "jdpano-lbar-fill", Ra.load.lbar.appendChild(Ra.load.lbarFill), Ra.load.box.appendChild(Ra.load.lbar), Ra.load.msg = b.createElement("p"), Ra.load.msg.className = "jdpano-lmsg", Ra.load.box.appendChild(Ra.load.msg), d.appendChild(Ra.load.box), Ra.errorMsg = b.createElement("div"), Ra.errorMsg.className = "jdpano-error-msg jdpano-info-box", d.appendChild(Ra.errorMsg); var Ta = {}; Ta.container = b.createElement("div"), Ta.container.className = "jdpano-controls-container", d.appendChild(Ta.container), Ta.load = b.createElement("div"), Ta.load.className = "jdpano-load-button", Ta.load.addEventListener("click", function () { W(), ca() }), d.appendChild(Ta.load), Ta.zoom = b.createElement("div"), Ta.zoom.className = "jdpano-zoom-controls jdpano-controls", Ta.zoomIn = b.createElement("div"), Ta.zoomIn.className = "jdpano-zoom-in jdpano-sprite jdpano-control", Ta.zoomIn.addEventListener("click", Z), Ta.zoom.appendChild(Ta.zoomIn), Ta.zoomOut = b.createElement("div"), Ta.zoomOut.className = "jdpano-zoom-out jdpano-sprite jdpano-control", Ta.zoomOut.addEventListener("click", $), Ta.zoom.appendChild(Ta.zoomOut), Ta.container.appendChild(Ta.zoom), Ta.fullscreen = b.createElement("div"), Ta.fullscreen.addEventListener("click", X), Ta.fullscreen.className = "jdpano-fullscreen-toggle-button jdpano-sprite jdpano-fullscreen-toggle-button-inactive jdpano-controls jdpano-control", (b.fullscreenEnabled || b.mozFullScreenEnabled || b.webkitFullscreenEnabled || b.msFullscreenEnabled) && Ta.container.appendChild(Ta.fullscreen), Ta.orientation = b.createElement("div"), Ta.orientation.addEventListener("click", function (a) { Ga ? ea() : fa() }), Ta.orientation.addEventListener("mousedown", function (a) { a.stopPropagation() }), Ta.orientation.addEventListener("touchstart", function (a) { a.stopPropagation() }), Ta.orientation.addEventListener("pointerdown", function (a) { a.stopPropagation() }), Ta.orientation.className = "jdpano-orientation-button jdpano-orientation-button-inactive jdpano-sprite jdpano-controls jdpano-control"; var Ua, Va = !1; a.DeviceOrientationEvent ? a.addEventListener("deviceorientation", f) : Ua = !1; var Wa = b.createElement("div"); Wa.className = "jdpano-compass jdpano-controls jdpano-control", d.appendChild(Wa), V(e.firstScene ? e.firstScene : e.defaults && e.defaults.firstScene ? e.defaults.firstScene : null), W(!0); var Xa = [], Ya = []; K.prototype.multiply = function (a) { return new K(this.w * a.w - this.x * a.x - this.y * a.y - this.z * a.z, this.x * a.w + this.w * a.x + this.y * a.z - this.z * a.y, this.y * a.w + this.w * a.y + this.z * a.x - this.x * a.z, this.z * a.w + this.w * a.z + this.x * a.y - this.y * a.x) }, K.prototype.toEulerAngles = function () { return [Math.atan2(2 * (this.w * this.x + this.y * this.z), 1 - 2 * (this.x * this.x + this.y * this.y)), Math.asin(2 * (this.w * this.y - this.z * this.x)), Math.atan2(2 * (this.w * this.z + this.x * this.y), 1 - 2 * (this.y * this.y + this.z * this.z))] }, this.isLoaded = function () { return Aa }, this.getPitch = function () { return ia.pitch }, this.setPitch = function (a, b, d, e) { return b = b == c ? 1e3 : Number(b), b ? Ia.pitch = { startTime: Date.now(), startPosition: ia.pitch, endPosition: a, duration: b, callback: d, callbackArgs: e } : ia.pitch = a, H(), this }, this.getPitchBounds = function () { return [ia.minPitch, ia.maxPitch] }, this.setPitchBounds = function (a) { return ia.minPitch = Math.max(-90, Math.min(a[0], 90)), ia.maxPitch = Math.max(-90, Math.min(a[1], 90)), this }, this.getYaw = function () { return ia.yaw }, this.setYaw = function (a, b, d, e) { for (; a > 180 || a < -180;)a > 0 ? a -= 360 : a += 360; var f = Math.abs(a - ia.yaw); f = f < 180 ? f : 360 - f; var g; return g = a > ia.yaw + 180 ? ia.yaw - f : a < ia.yaw - 180 ? ia.yaw + f : a, b = b == c ? 1e3 : Number(b), b ? Ia.yaw = { startTime: Date.now(), startPosition: ia.yaw, endPosition: g, duration: b, callback: d, callbackArgs: e } : ia.yaw = a, H(), this }, this.getYawBounds = function () { return [ia.minYaw, ia.maxYaw] }, this.setYawBounds = function (a) { return ia.minYaw = Math.max(-180, Math.min(a[0], 180)), ia.maxYaw = Math.max(-180, Math.min(a[1], 180)), this }, this.getHfov = function () { return ia.hfov }, this.setHfov = function (a, b, d, e) { return b = b == c ? 1e3 : Number(b), b ? Ia.hfov = { startTime: Date.now(), startPosition: ia.hfov, endPosition: _(a), duration: b, callback: d, callbackArgs: e } : aa(a), H(), this }, this.getHfovBounds = function () { return [ia.minHfov, ia.maxHfov] }, this.setHfovBounds = function (a) { return ia.minHfov = Math.max(0, a[0]), ia.maxHfov = Math.max(0, a[1]), this }, this.lookAt = function (a, b, d, e, f, g) { return e = e == c ? 1e3 : Number(e), a !== c && (this.setPitch(a, e, f, g), f = c), b !== c && (this.setYaw(b, e, f, g), f = c), d !== c && this.setHfov(d, e, f, g), this }, this.getNorthOffset = function () { return ia.northOffset }, this.setNorthOffset = function (a) { return ia.northOffset = Math.min(360, Math.max(0, a)), H(), this }, this.startAutoRotate = function (a) { return a = a || Ha || 1, ia.autoRotate = a, qa.lookAt(pa, c, oa, 3e3), H(), this }, this.stopAutoRotate = function () { return Ha = ia.autoRotate ? ia.autoRotate : Ha, ia.autoRotate = !1, ia.autoRotateInactivityDelay = -1, this }, this.getRenderer = function () { return ja }, this.setUpdate = function (a) { return La = !0 === a, ja === c ? i() : H(), this }, this.mouseEventToCoords = function (a) { return p(a) }, this.loadScene = function (a, b, c, d) { return Aa && da(a, b, c, d), this }, this.getScene = function () { return ia.scene }, this.addScene = function (a, b) { return e.scenes[a] = b, this }, this.removeScene = function (a) { return !(ia.scene === a || !e.scenes.hasOwnProperty(a)) && (delete e.scenes[a], !0) }, this.toggleFullscreen = function (a) { return X(a), this }, this.stopOrientation = function () { ea() }, this.startOrientation = function () { fa() }, this.getConfig = function () { return ia }, this.addHotSpot = function (a, b) { if (b === c || ia.scene == b) Q(a), ia.hotSpots.push(a), T(a); else { if (!e.scenes.hasOwnProperty(b)) throw "Invalid scene ID!"; e.scenes[b].hotSpots.push(a) } return this }, this.removeHotSpot = function (a) { if (!ia.hotSpots) return !1; for (var b = 0; b < ia.hotSpots.length; b++)if (ia.hotSpots[b].hasOwnProperty("id") && ia.hotSpots[b].id === a) { for (var c = ia.hotSpots[b].div; c.parentNode != Pa;)c = c.parentNode; return Pa.removeChild(c), delete ia.hotSpots[b].div, ia.hotSpots.splice(b, 1), !0 } return !1 }, this.resize = function () { G() }, this.isLoaded = function () { return Aa }, this.on = function (a, b) { return Ja[a] = Ja[a] || [], Ja[a].push(b), this }, this.off = function (a, b) { if (!a) return Ja = {}, this; if (b) { var c = Ja[a].indexOf(b); c >= 0 && Ja[a].splice(c, 1), (Ja[a].length = 0) && delete Ja[a] } else delete Ja[a]; return this }, this.destroy = function () { ja && ja.destroy(), Da && (Qa.removeEventListener("mousedown", n, !1), Qa.removeEventListener("dblclick", o, !1), b.removeEventListener("mousemove", q, !1), b.removeEventListener("mouseup", r, !1), d.removeEventListener("mousewheel", y, !1), d.removeEventListener("DOMMouseScroll", y, !1), d.removeEventListener("mozfullscreenchange", Y, !1), d.removeEventListener("webkitfullscreenchange", Y, !1), d.removeEventListener("msfullscreenchange", Y, !1), d.removeEventListener("fullscreenchange", Y, !1), a.removeEventListener("resize", G, !1), a.removeEventListener("orientationchange", G, !1), d.removeEventListener("keydown", z, !1), d.removeEventListener("keyup", B, !1), d.removeEventListener("blur", A, !1), b.removeEventListener("mouseleave", r, !1), Qa.removeEventListener("touchstart", s, !1), Qa.removeEventListener("touchmove", t, !1), Qa.removeEventListener("touchend", u, !1), Qa.removeEventListener("pointerdown", v, !1), Qa.removeEventListener("pointermove", w, !1), Qa.removeEventListener("pointerup", x, !1), Qa.removeEventListener("pointerleave", x, !1)), d.innerHTML = "", d.classList.remove("jdpano-container"), d.classList.remove("jdpano-grab"), d.classList.remove("jdpano-grabbing") }
        } return { viewer: function (a, b) { return new d(a, b) } }
    }(window, document);
    module.exports={
        build3D:build3D
    };
})