define('MOD_ROOT/common/follow',function(require,exports,module){
	var jdLogin = require('JDF_UNIT/login/1.0.0/login.js');
	var Tools = require('MOD_ROOT/common/tools/tools');
	require('JDF_UI/dialog/1.0.0/dialog.js');
	/**
	* @关注商品
	* @依赖 dialog组件
	* @举例
		<button data-id="1027342" class="coll">关注商品</button>
		seajs.use('jdf/1.0.0/unit/follow/follow.js',function(follow){
			follow.init($('.coll'));
			//或者 follow.add(1027342);
		})
	*/

	function init(el, callback, options){
		 bind(el, callback, options);
	}

	function bind(el, callback, options){
		 el.live("click", function(e) {
		 	e.preventDefault();
			var current = $(this);
			var productId = current.attr("data-id");
			var followed = el.hasClass("followed");
			toggleFollow(!followed, productId, callback, el)
		});
	}
	// 收藏商品（添加关注）
	function add(productId, callback, options, el){
		 //var serviceUrl = "//t."+ pageConfig.FN_getDomain() +"/regard/follow.action?callback=?";
		if ( !productId ) return false;
		var serviceUrl = '//t.jd.com/product/followProduct.action?productId=' + productId + "&t=" + Math.random();
		if ( !options || !$.isPlainObject(options) ) {
			options = {isShowInfo:true};
		}
        callback = callback || function() {};
		jdLogin({
			clstag1: "login|keycount|5|3",
			clstag2: "login|keycount|5|4",
			id: productId,
			modal: true,
			complete: function() {
				if ( options.isShowInfo ) {
					$('body').dialog({
						title:'提示',
						width:530,
						height : 440,
						type:'iframe',
						autoIframe: false,
						fixed : !($.browser.msie && $.browser.version == 6),
						source: serviceUrl,
						mainId:'attboxr',
						contentId:'attconr',
						onReady: callback
					});
					var followed = el.hasClass("followed")
					if(!followed){
						el.addClass("followed").html("<i></i>已收藏");
					}
				} else {
					new Image().src = serviceUrl;
					callback();
				}
			}
		})
        
	}
	// 收藏商品/取消收藏商品
	function toggleFollow(isFollowing, productId, callback, el) {
		var followStatus = isFollowing ? 'follow' : 'unfollow';
		if ( !productId ) return false;
		Tools.checkLogin(function (r) {
			if(!(r && r.IsAuthenticated)){// 未登录
				window.login && window.login()
			} else {
				var host = '//api.m.jd.com/api'
				$.ajax({
					url: host,
					data: {
						appid: 'item-v3',
						functionId: 'pctradesoa_product_' + followStatus,
						client: 'pc',
						clientVersion: '1.0.0',
						uuid: Tools.getCookieNew("__jda") || '',
						body: JSON.stringify({"productId": productId, "sysName": "item.jd.com"}),
						t: Date.now(),
					},
					dataType: 'json',
					contentType: "application/json;charset=gbk",
					xhrFields: {
						withCredentials: true,
					},
					success: function(r) {
						if ( r && r.success ) {
							callback();
							if ( el ) {
								if ( isFollowing ) {
									el.addClass("followed").html("<i></i>已收藏");
								} else {
									el.removeClass("followed").html("<i></i>收藏");
								}
							}
						} else {
							$('body').dialog({
								title: '提示',
								content: r.message || '操作失败，请稍后再试！',
								width: 300,
								height: 150,
								type: 'alert'
							});
						}
					}
				});
			}
		})
	}
    /*--- Usage:
    follow.check([1379839787,1379839794,1379838583], function (sku, isFollowed) {
        console.log(sku + '----' + isFollowed);
    });
    @ 接品人 申浩亮 <EMAIL>
    */
    function check(skus, callback) {
        var skuids = skus || [];

        callback = callback || function() {};

        // test case
        //if ( /isdebug/.test(location.href) ) {
            //skuids = [1379839787,1379839794,1379838583];
        //}

        $.ajax({
            url: '//fts.jd.com/follow/product/batchIsFollow?',
            data: {
                productIds: skuids.join(','),
                sysName: 'misc'
            },
            dataType: 'jsonp',
            success: function(r) {
                function loopCallback(result) {
                    for ( var k in result) {
                        if ( result.hasOwnProperty(k) ) {
                            callback(k, result[k]);
                        }
                    }
                }

                if ( r && r.success ) {
					loopCallback(r.data)
				}
            }
        });
    }

	exports.init = init;
	// exports.add = add;
	exports.check = check;
});
