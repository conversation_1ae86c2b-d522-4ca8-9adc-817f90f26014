@charset "utf-8";

@import "__sprite";
// Variables & Mixin ..
@import "lib";
// elements
@import "button";
@import "product";
// components
@import "../ETab/ETab";
@import "../EDropdown/EDropdown";
@import "../ETooltips/ETooltips";
@import "../ELazyload/ELazyload";
@import "../sidereco/sidereco";
body{
    background-color: #F6F7FB;
}
.clb {
    clear: both;
}
.invisible{ visibility:hidden; }
s {
    text-decoration: none;
}
blockquote, q {
    quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}
.lh {
    overflow: hidden;
    zoom: 1;
}
.lh li {
    float: left;
    *display: inline;
}
.arr {
    font-family: $font-st;
}
.hl_red {
    color: $colorPriceRed;
}
.hl_blue {
    color: $colorLinkBlue;
}
.loading-style1 {
    width: 126px;
    margin: 0 auto;
    text-align: center;
    color: #999;
     b {
        display: block;
        width: 43px;
        height: 24px;
        margin: 0 auto 5px;
        background: url(//misc.360buyimg.com/lib/skin/e/i/loading-jd.gif) no-repeat 50% 50%;
    }
}

// layout
.aside {
    float: left;
    display: inline;
    width: 210px;
    margin-bottom: 20px;
}
.detail {
    // min-height: 220px;
}

.root61 {
    .detail {
        width: 850px;
    }
}
.building{
    .detail{
        width: 100%;
    }
}
@include mContent();
@include mAside();

.tag-list {
    li {
        @include inline-block;
        padding: 2px 4px;
        background-color: #eef1ff;
        margin: 0 10px 10px 0;
    }
}

//itemInfo
.itemInfo-wrap {
    ins {
        text-decoration: none;
    }

    .s-arrow {
        // font-family: simsun;
        text-decoration: none;
    }

    .summary {
        #day-sales-rank{
            margin-bottom: 0;
        }
        // position: relative;
        // padding-bottom: 5px;
        _zoom: 1;
        .competition{
            border-bottom: 1px dotted #dfdfdf;
            padding-bottom: 5px;
            margin-bottom: 18px;
            .sprite-map{
                display: inline-block;
                @include sprite-map;
                margin-left: 5px;
                vertical-align: -2px;
                &:hover{
                    @include sprite-map-hover;
                }
            }
        }
    }

    .summary-first {
        *z-index: 5;
    }

    .dt {
        float: left;
        // font-family: simsun;
        color: #888B94;
        width: 60px;
        height: 18px;
        line-height: 18px;
        font-size: $baseFontSize;
        font-weight: 400;
        // max-height: 32px;
        overflow: hidden;
        &.size-type {
            line-height:16px;
            margin-right:5px;
        }
    }

    .dd {
        margin-left: 80px; // 间距16
    }
    .blue {
        color:#005aa0;
    }
}

#day-sales-rank .dd a{
    color: #5e69ad;
    margin-left: 20px;
}

.li {
    margin-bottom: 3px;
    line-height: 32px;

    @include clearfix;
    .hl_blue {
        cursor: pointer;
    }
    &.p-choose {
        margin-bottom: 4px;
        .dt {
            height: 34px;
            display: flex;
            align-items: center;
        }
        .item {
            margin-bottom: 8px; // 设计要求改为8px
        }
    }

    .item {
        float: left;
        color: #666;
        background-color: #FFF;
        margin-right: 7px;
        margin-bottom: 4px;
        position: relative;

        a {
            border: 1px solid #fff;
            padding: 0 13px;
            display: block;
            white-space: nowrap;
            background: #F7F8FC;
            border-radius: 4px;
            color: #1A1A1A;
            *display: inline;
            *zoom: 1;
            font-size: $baseFontSize;
        }

        &:hover, &.hover, &.selected {
            a {
                border: 1px solid #FF0F23;
                color: #FF0F23;
                background: #FFEBF1;
                border-radius: 4px;
            }
        }

        &.disabled {
            a {
                border: 1px dashed #ccc;
                //cursor: not-allowed;
                color: #999;
            }
        }
    }
}
.p-choose-wrap {
    .item-hl-bg {
        padding-top: 4px;
        background-color: #ffe6e6;
    }
    .open-plusgift {
        line-height: 32px;
        padding-bottom: 5px;
        .icon-plus {
            width:59px;
            height: 16px;
            display: inline-block;
            background: url(./i/icon-plusx1.png) center center no-repeat;
            vertical-align: top;
            margin: 8px 5px 0 0;
        }
        .a-topluspage {
            color: $colorLinkBlue;
        }
    }
}

/* 兼容新头热词 */
#hotwords {
    float: left;
    width: 462px;
    height: 20px;
    line-height: 20px;
    overflow: hidden;

    strong {
        display: none;
    }
    a {
        margin-right: 10px
    }
}
.root61 #hotwords {
    width: 518px
}
/*导航角标*/
#nav-2014{
    .new-tab{
        position: relative;
        .icon-new{
             position: absolute;
             min-width: 24px;
             height: 15px;
             line-height: 15px;
             text-align: center;
             padding: 0 3px;
             display: block;
             right: -10px;
             top:-3px;
             font-size: 12px;
            //  font-family: simsun;
             color: #fff;
             background: #529f57;
            span{
                width: 0;
                height: 0;
                border-top: 3px solid #529f57;
                border-right: 3px solid transparent;
                position: absolute;
                left: 0;
                bottom: -3px;
            }
         }
    }
}
/*二手*/
.ershou{
    .li{
        .item {

            &:hover, &.hover, &.selected {
                a {
                    border: 1px solid $baseColorErshou;
                    color: #666;
                }
             }

            &.disabled {
                a {
                    border: 1px dashed #ccc;
                    //cursor: not-allowed;
                    color: #999;
                }
            }
        }
    }
}

/*二手头尾*/
.ershou{
    #search-2014 .text{
        border-color: $baseColorErshou;
    }
    #search-2014 .button{
        background: $baseColorErshou;
    }
    #nav-2014{
        border-color: $baseColorErshou;
    }
    #categorys-2014{
        background: $baseColorErshou;
    }
    #categorys-2014 .dt a{
        background: $baseColorErshou;
    }
    #categorys-2014 .dd{
        background: $baseColorErshou;
        a:hover{
            color: $baseColorErshou;
        }
    }
    #categorys-2014 .dorpdown-layer{
        border-color: $baseColorErshou;
    }
    #categorys-2014 .dd-inner .item{
        border-color: $baseColorErshou;
    }
    #categorys-2014 .dd-inner .hover a{
        color: $baseColorErshou;
    }
    #service-2014 .slogen .item{
        display: none;
    }
    #service-2014 .slogen{
        background: #F5F5F5 url(i/service-bg.png) no-repeat center 25px;
    }
    #logo-2014 {
        height: 40px;
        padding: 30px 0;
    }
    #logo-2014 .logo{
        width: 82px;
        height: 36px;
        // background: url(//img30.360buyimg.com/devfe/jfs/t15118/224/857107058/3296/b5edc83/5a3b237cN12a3e7fb.png) 50% 50% no-repeat;
        background: url(i/paipai-logo.png) 50% 50% no-repeat;
        background-size: 82px 36px;
    }
    #settleup-content .smb a{
        background: $baseColorErshou;
    }
    #settleup-2014 .ci-count{
        background-color: $baseColorErshou;
    }
    #settleup-2014 .ci-left {
        background: url(i/cart.png) no-repeat;
        top: 9px;
        left: 18px;
        width: 18px;
        height: 16px;
        display: block;
        position: absolute;
        overflow: hidden;
    }
    #settleup-2014 .cw-icon a:hover {
        color: $baseColorErshou;
    }
}
.root61{
    .ershou{
        #service-2014 .slogen{
            background: #F5F5F5 url(i/service-bg-root61.png) no-repeat center 25px;
        }
    }
}

.car-filter {
    display: none;
}

.m-itemover-title {
    height: 38px;
    line-height: 38px;
    border: 1px solid #ddd;
    background: #f5f5f5;
    margin-bottom: 15px;
    h3 {
        padding-left: 10px;
    }
}
/*医药*/
.y-beian {
    margin-left: 10px;
    width: 147px;
}
.y-copyright {
    width: 337px;
}
.y-zige {
    width: 270px;
}
.y-beian, .y-copyright, .y-zige {
    float: left;
    margin-right: 20px;
    line-height: 1.8em;
}
.root61 {
    .y-beian {
        margin: 0 80px 0 80px;
    }
    .y-copyright {
        margin-right: 80px;
    }
}

// 大药房页尾部

.dyf-footer {
    background-color: #fff;
    a, span, img {
        vertical-align: middle;
        color: #333;
    }
    a:hover {
        color: #f30213;
    }
    .logo {
        padding: 27px 0 24px 0;
        text-align: center;
    }
    .links {
        text-align: center;
        line-height: 22px;
    }
    .divide { 
        @include inline-block;
        width: 1px;
        height: 12px;
        background-color: #333;
        margin: 0 10px;
        vertical-align: middle;
    }
    .record {
        text-align: center;
        padding: 10px 0;
    }
}

/* 店铺星级 */
.contact,
.pop-score-summary {
    .star {
        padding-top: 3px;
        .star-bg {
            width:94px;
            height:16px;
            border-radius: 8px;
            background:#e6e6e6;
            overflow:hidden;
        }
        .star-gray {
            position:relative;
            width: 80px;
            height:14px;
            margin: 1px 0 0 7px;
            background:url(./i/star-gray.png) left center repeat-x;
        }
        .star-light {
            position: absolute;
            top:0;
            left:0;
            height:14px;
            background:url(./i/star-light.png) left center repeat-x;
        }
    }
    // 左侧店铺的星级样式
    .dianpu-star-box {
        height:24px;
        line-height:24px;
        color:#666;
        margin-bottom: 8px;
    }
    .dianpu-star-tit {
        width:56px;
        font-size:12px;
        float:left;
    }
    .dianpu-star-box .star {
        margin-top: 2px;
        float:left;
    }

    // 颜色
    .red {
        color: #e2231a!important;
    }
    .orange {
        color: #ff6602!important;
    }
    .green {
        color: #33bb44!important;
    }
}

/* 主图浮层内置字体 */
@font-face {
    font-family: "JDZHENGHEI";
    src: url("https://storage.360buyimg.com/retail-mall/fontFamily_lib/JDZhengHei-Bold.ttf")
        format("truetype");
}
@font-face {
    font-family: "SourceHanSansSC-Medium";
    src: url("https://storage.360buyimg.com/retail-mall/fontFamily_lib/siyuan-Medium.ttf")
        format("truetype");
}

// 推荐位到手价字体
.price-dsj{
    font-size: 12px;
}


// 大改版整体样式调整
.root61 {
    .w{
        width: 1552px;
    }
}

.aside{
    display: none;
}

.detail{
    float: left;
    margin-top: 16px;
    background: #fff;
    border-radius: 8px;

    .tab-con {
      padding: 0 16px;
    }

    .tab-main.large {
      border: none;
      border-bottom: 1px solid rgba(194, 196, 204, 0.6);
      background: none;

      li {
        padding: 0;
        margin: 16px;
        font-size: 16px;
        font-weight: 600;
        margin-right: 12px;
        color: #1A1A1A;
        
        &.current {
          position: relative;
          background: none;
          color: rgba(255, 15, 35, 1);

          &::after {
            content: ' ';
            width: 100%;
            height: 2px;
            background: rgba(255, 15, 35, 1);
            position: absolute;
            bottom: -17px;
            left: 0;
          }
        }
      }
    }
}


div.ui-dialog {
    border: 0;
    /* border: 4px solid #ccc\9; */
    border-radius: 12px;
    -moz-border-radius: 12px;
    -webkit-border-radius: 12px;
    padding: 24px;
    background-color: #fff;
    font-size: 14px;
    color: #1A1A1A;
    font-family: "PingFang SC";
  }
  .ui-dialog .ui-dialog-title {
    position: relative;
    padding-right: 12px;
    height: 18px;
    line-height: 18px;
    background: #fff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  .ui-dialog .ui-dialog-close {
    cursor: pointer;
    display: block;
    position: absolute;
    z-index: 100000;
    top: 21px;
    right: 18px;
    overflow: hidden;
    width: 18px;
    height: 18px;
    background: url(//img11.360buyimg.com/imagetools/jfs/t1/256984/14/11782/626/6785399aF817f840c/aab7c4fe5017e887.png) center / contain no-repeat;
    font-size: 0;
  }
  .ui-dialog .ui-dialog-content {
    /* padding: 10px; */
    overflow: auto;
    background: #fff;
    line-height: 20px;
  }
  .ui-dialog .ui-dialog-btn {
    padding: 0px;
    /* padding-bottom: 15px; */
    background-color: #fff;
    text-align: right;
  }
  .ui-dialog-btn .ui-dialog-btn-submit,
  .ui-dialog-btn .ui-dialog-btn-cancel {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    height: 32px;
    min-width: 80px;
    cursor: pointer;
    padding: 0 12px;
    border-radius: 6px;
    -moz-border-radius: 6px;
    -webkit-border-radius: 6px;
  
    /* background: -moz-linear-gradient(
      top,
      rgba(221, 221, 221, 0.3) 0%,
      rgba(243, 242, 242, 1) 100%
    ); FF3.6+
    background: -webkit-gradient(
      linear,
      left top,
      left bottom,
      color-stop(0%, rgba(221, 221, 221, 0.3)),
      color-stop(100%, rgba(243, 242, 242, 1))
    ); Chrome,Safari4+
    background: -webkit-linear-gradient(
      top,
      rgba(221, 221, 221, 0.3) 0%,
      rgba(243, 242, 242, 1) 100%
    ); Chrome10+,Safari5.1+
    background: -o-linear-gradient(
      top,
      rgba(221, 221, 221, 0.3) 0%,
      rgba(243, 242, 242, 1) 100%
    ); Opera 11.10+
    background: -ms-linear-gradient(
      top,
      rgba(221, 221, 221, 0.3) 0%,
      rgba(243, 242, 242, 1) 100%
    ); IE10+ */
    /* background: linear-gradient(
      to bottom,
      rgba(221, 221, 221, 0.3) 0%,
      rgba(243, 242, 242, 1) 100%
    ); W3C */
    /* filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4ddddddd', endColorstr='#f3f2f2',GradientType=0 ); IE6-9 */
  }
  
  .ui-dialog-btn .ui-dialog-btn-submit:hover{
    color: #fff;
    text-decoration: none;
  }
  .ui-dialog-btn .ui-dialog-btn-cancel:hover {
    color: #1A1A1A;
    text-decoration: none;
  }
  .ui-dialog-btn .ui-dialog-btn-cancel {
    border: 0.5px solid #C2C4CC;
    background: #fff;
  }
  .ui-dialog-btn .ui-dialog-btn-submit {
    margin-left: 8px;
    background: linear-gradient(90deg, rgba(255, 71, 93, 1) 0%, rgba(255, 15, 35, 1) 100%);
    color: #fff;
  }
  .ui-dialog-autoclose {
    padding: 10px 10px 10px;
    background-color: #fff;
  }