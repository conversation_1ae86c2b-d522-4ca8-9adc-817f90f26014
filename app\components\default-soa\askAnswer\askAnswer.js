define('MOD_ROOT/askAnswer/askAnswer', function(require, exports, module) {
    var login = require('JDF_UNIT/login/1.0.0/login')
    require('JDF_UI/pager/1.0.0/pager')
    var Tools = require('MOD_ROOT/common/tools/tools');
    var template = {}
    template.list =
        '\
        {for question in questionList}\
        <div class="askAnswer-item" data-questionId="${question.id}" data-answerCount="${question.answerCount}" data-currPage=0>\
             <div class="ask">\
                <i class="icon-ask">问</i>\
                <div class="item-con"><p>${question.content}</p><span>{if question.userInfo}${question.userInfo.nickName}{/if}  {if question.created}${question.created.slice(0, -3)}{/if} {if question.location}${question.location}{/if}</span></div>\
            </div>\
            <div class="answer {if question.answerCount == 0} disabled{/if}">\
                <i class="icon-answer">答</i>\
                <div class="item-con">\
                {if question.answerCount && question.answerList.length}\
                    <ul class="answer-list">\
                        <li>\
                            <p>${question.answerList[0].content}</p>\
                            <span class="item-info">${question.answerList[0].userInfo.nickName}&nbsp;{if question.answerList[0].created}${question.answerList[0].created.slice(0, -3)}{/if} {if question.answerList[0].location}${question.answerList[0].location}{/if}</span>\
                        </li>\
                    </ul>\
                    {if question.answerCount > 1}\
                        <div class="more">\
                            <a href="#none" class="J-spread spread" clstag="shangpin|keycount|product|wendagengduo">继续查看<em class="J-rest-answer-count">${question.answerCount - 1}</em>条回答<i class="sprite-arrowDown"></i></a>\
                            <a href="#none" class="J-retract retract">收起回答<i class="sprite-arrowUp"></i></a>\
                        </div>\
                    {/if}\
                {else}\
                    <ul class="answer-list">\
                        <li><p>暂无回答</p></li>\
                    </ul>\
                {/if}\
                </div>\
            </div>\
        </div>\
        {/for}\
        <div class="askAnswer-tips">\
            <i class="sprite-tips"></i><p>温馨提示：因厂家更改产品包装、产地或者更换随机附件等没有任何提前通知，且每位咨询者购买情况、提问时间等不同，为此以下回复仅对提问者3天内有效，其他网友仅供参考~若由此给您带来不便请多多谅解，谢谢~</p>\
        </div>\
        <div class="ui-page-wrap clearfix">\
            <div class="ui-page" clstag="shangpin|keycount|product|tiwenfanye"></div>\
        </div>'
    template.answerList =
        '\
        {for answer in answers}\
        <li class="{if !(isFirstPage && (answer_index == 0))}more-answer{/if}">\
            <p>${answer.content}</p>\
            <span class="item-info">${answer.userInfo.nickName}&nbsp;{if answer.created}${answer.created.slice(0, -3)}{/if} {if answer.location}${answer.location}{/if}</span>\
        </li>\
        {/for}'

    var AskAnswer = {
        init: function(cfg) {
            var _this = this
            _this.cfg = cfg

            _this.$askAnswer = $('#askAnswer')
            _this.$askAnswerList = _this.$askAnswer.find('.askAnswer-list')
            _this.askLayer = null

            // _this.$askAnswer.show()
            _this.bindEvent()
            _this.getQuestionData(0)

            var locname = window.location.hostname
            if(locname == "item.jdh.com"  || locname == "item.jingdonghealth.cn"){// 健康域名屏蔽关注
                _this.$askAnswer.find(".J-btn-ask").hide()
            }
        },
        bindEvent: function() {
            var _this = this
            _this.$askAnswerList.delegate('.J-spread', 'click', function() {
                var $this = $(this)
                var $itemCon = $this.parents('.item-con')
                var $askAnswerItem = $itemCon.parents('.askAnswer-item')

                var currPage =
                    parseInt($askAnswerItem.attr('data-currPage')) + 1
                $askAnswerItem.attr('data-currPage', currPage)
                _this.getAnswerData($askAnswerItem, currPage)
            })

            _this.$askAnswerList.delegate('.J-retract', 'click', function() {
                var $this = $(this)
                var $itemCon = $this.parents('.item-con')
                var $askAnswerItem = $itemCon.parents('.askAnswer-item')
                $itemCon.find('.answer-list').removeClass('unfold')
                $itemCon.find('.J-spread').show()
                $askAnswerItem.attr('data-currPage', 0)
                $itemCon
                    .find('.J-rest-answer-count')
                    .html($askAnswerItem.attr('data-answercount') - 1)
                $this.hide()

                $('html,body').scrollTop($askAnswerItem.offset().top)
            })

            _this.$askAnswer.delegate('.J-btn-ask', 'click', function() {
                if (_this.askLayer) {
                    _this.askLayer.init(_this.cfg)
                } else {
                    _this.askLayer = new AskLayer(_this.cfg)
                }
            })
        },
        getQuestionData: function(page) {
            var _this = this
            _this.page = page

            var time = new Date().getTime()
            var colorParm = {
                appid: 'item-v3',
                functionId: 'getQuestionAnswerList',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                page: _this.page + 1,
                productId: _this.cfg.skuid
            }

            try{
                colorParm['uuid'] = Tools.getCookieNew("__jda")
            }catch(e){
                colorParm['uuid'] = ''
            }

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }

            $.ajax({
                // url: '//question.jd.com/question/getQuestionAnswerList.action',
                // data: {
                //     page: _this.page + 1,
                //     productId: _this.cfg.skuid
                //     // productId: 111320
                // },
                // dataType: 'jsonp',
                // cache: true,
                url: host,
                data: colorParm,
                dataType: 'json',
                contentType: "application/json;charset=gbk",
                xhrFields: {
                    withCredentials: true,
                },
                headers: Tools.getUrlSdx(),
                success: function(r) {
                    if (r && r.resultCode == 41) {  // `resultCode`41是黑名单
                        _this.$askAnswer.remove();
                    } else {
                        var ispj = $('#detail .tab-main').find(".current").is('[data-anchor="#comment"]')
                        if(ispj){// 只有评价tab才展示问答楼层，避免本店好评点击加载问答数据
                            _this.$askAnswer.show(); 
                        }
                        _this.setQuestionData(r);
                    }
                }
            })
        },
        setQuestionData: function(r) {
            var _this = this
            if (r.totalItem == 0) {
                return
            }

            var html = template.list.process(r)
            _this.$askAnswerList.html(html)

            _this.setPageNav(r)
        },
        setPageNav: function(data) {
            var _this = this
            var $pageNav = _this.$askAnswer.find('.ui-page')
            _this.pager = $pageNav.pager({
                total: data['totalItem'],
                pageSize: 10,
                currentPageClass: 'ui-page-curr',
                currentPage: _this.page + 1,
                pageHref: '#askAnswer',
                prevClass: 'ui-pager-prev',
                nextClass: 'ui-pager-next',
                prevText: '上一页',
                nextText: '下一页',
                callback: function(pageId) {
                    var page = pageId - 1
                    removeLastPage(_this.pager)
                    _this.getQuestionData(page)
                }
            })

            function removeLastPage(pager) {
                if (!pager) {
                    return false
                }
                var $span = pager.el.find('span:last')

                if ($span.index() > 5) {
                    $span.next().remove()
                }
            }
        },
        getAnswerData: function($askAnswerItem, page) {
            var _this = this
            var questionId = $askAnswerItem.attr('data-questionId')
            
            var time = new Date().getTime()
            var colorParm = {
                appid: 'item-v3',
                functionId: 'getAnswerListById',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                page: page,
                questionId: questionId
            }
            try{
                colorParm['uuid'] = Tools.getCookieNew("__jda")
            }catch(e){
                colorParm['uuid'] = ''
            }

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                // url: '//question.jd.com/question/getAnswerListById.action',
                // data: {
                //     page: page,
                //     questionId: questionId
                //     // questionId: 10565
                // },
                // dataType: 'jsonp',
                // cache: true,
                url: host,
                data: colorParm,
                dataType: 'json',
                contentType: "application/json;charset=gbk",
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function(r) {
                    _this.setAnswerData(r, $askAnswerItem, page)
                }
            })
        },
        setAnswerData: function(r, $askAnswerItem, page) {
            r.isFirstPage = page == 1

            var html = template.answerList.process(r),
                $restAnswerCount = $askAnswerItem.find('.J-rest-answer-count')

            if (r.isFirstPage) {
                $askAnswerItem
                    .find('.answer-list')
                    .addClass('unfold')
                    .html(html)
            } else {
                $askAnswerItem
                    .find('.answer-list')
                    .addClass('unfold')
                    .append(html)
            }

            if (r.moreCount > 0) {
                $restAnswerCount.html(r.moreCount)
            } else {
                $restAnswerCount.html(0)
                $askAnswerItem.find('.J-spread').hide()
                $askAnswerItem.find('.J-retract').show()
            }
        }
    }

    // 提问弹窗
    var AskLayer = function(cfg) {
        this.init(cfg)
    }

    AskLayer.prototype = {
        template: {
            questionLayer: '\
                <div class="askAnswer-layer J-ask-layer">\
                    <div id="warning-info"></div>\
                    <p class="textarea-tips">您的问题将推送给已购用户，TA们会帮您解答</p>\
                    <div class="ask-textarea">\
                        <textarea class="J-txa" cols="30" rows="10" placeholder="请输入您的问题吧~"></textarea>\
                        <span><em class="J-txtnum">0</em> / 50</span>\
                        <div class="textarea-error J-textarea-error"><i class="sprite-error"></i><em class="J-txt"></em></div>\
                    </div>\
                    <div class="bt-wrap">\
                        <input id="askAnswerAnonymous" class="pj J-anonymous" type="checkbox" checked="checked"/><label for="askAnswerAnonymous">匿名提问</label>\
                        <a href="#none" class="btn-confirm J-btn-ok" clstag="shangpin|keycount|product|tiwenfabu">发布</a>\
                        <a href="#none" class="btn-cancel J-btn-cancel">取消</a>\
                    </div>\
                </div>',
            success_layer: '\
                <div class="askAnswer-layer J-ask-success-layer asked">\
                    <i class="sprite-success"></i><p><strong>提问成功</strong><br/>您的问题将推送给已购用户</p>\
                    <div class="bt-wrap"><a href="#none" class="btn-confirm J-btn-ok">确定</a></div>\
                </div>',
            fail_layer: '\
                <div class="askAnswer-layer J-ask-fail-layer warning">\
                    <i class="sprite-failure"></i><p><strong>${errorInfo}</strong></p>\
                    <div class="bt-wrap"><a href="#none" class="btn-confirm J-btn-ok">关闭</a></div>\
                </div>',
            bindPhoneNum_layer: '\
                <div class="askAnswer-layer J-ask-fail-layer warning">\
                    <i class="sprite-failure"></i>\
                    <p><strong>您未绑定手机号，无法提问 ～</strong></p>\
                    <p>据《网络安全法》和相关规定，为保障您正常发布，请验证手机号</p>\
                    <div class="bt-wrap"><a href="https://aq.jd.com/process/bindMobile?s=3" class="btn-confirm J-btn-ok">绑定手机号</a>\
                    <a href="#none" class="btn-cancel J-btn-cancel">取消</a></div>\
                </div>',
            violation_layer: '\
                <div class="askAnswer-layer J-ask-fail-layer warning">\
                    <i class="sprite-failure"></i>\
                    <p><strong>抱歉，你有违规发言历史，暂不能发布信息 ～</strong></p>\
                    <div class="bt-wrap"><a href="#none" class="btn-confirm J-btn-ok">关闭</a></div>\
                </div>'
        },
        init: function(cfg) {
            var _this = this
            _this.cfg = cfg
            _this.dialog = null
            login({
                modal: true,
                complete: function(r) {
                    if (r && r.Identity && r.Identity.IsAuthenticated) {
                        // _this.showQuestionLayer()
                        //  在这里处理登录成功后的方法
                        _this.getUserQueryRisk("ask");
                    }
                }
            })
        },
        /**
        *   获取用户风险等级，执行相应操作
        *   @param {string} type  是哪个按钮调用本方法  ask代表我要提问按钮，submit代表发布按钮
        */
        getUserQueryRisk: function(type) {
          var _this = this;
          var time = new Date().getTime()
          var colorParm = {
              appid: 'item-v3',
              functionId: 'queryRisk',
              client: 'pc',
              clientVersion: '1.0.0',
              t: time,//生成当前时间毫秒数
              loginType: '3',
            //   uuid: Tools.getCookieNew("__jda") || '',
          }

          try{
            colorParm['uuid'] = Tools.getCookieNew("__jda")
        }catch(e){
            colorParm['uuid'] = ''
        }

          var host = '//api.m.jd.com'
          if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
          }
          $.ajax({
            //   url: '//question.jd.com/question/queryRisk.action',
            //   dataType: 'jsonp',
            //   cache: true,
            url: host,
            data: colorParm,
            dataType: 'json',
            contentType: "application/json;charset=gbk",
            xhrFields: {
                withCredentials: true,
            },
            success: function(r) {

                var userRisk = r.resultCode;
                _this.printfMsg(userRisk);

                if (type === "ask") {
                    if (r.success) {
                    if (userRisk === "1000") {
                        _this.printfMsg("这个用户是正常用户");
                        _this.showQuestionLayer();
                    }
                    }else {
                    if (userRisk === "1001") {
                        _this.printfMsg("这个用户没有绑定手机号");
                        _this.showBindPhoneNum();
                    }else if (userRisk === "1002") {
                        _this.printfMsg("这个用户有违规发言历史");
                        _this.showVolation();
                    }
                    }
                }

                if (type === "submit") {
                    if (r.success) {
                    if (userRisk === "1000") {
                        _this.printfMsg("这个用户是正常用户");
                        _this.submitQuestion($('.J-txa').val());
                    }
                    }else{
                    if (userRisk === "1001") {
                        _this.printfMsg("这个用户没有绑定手机号");
                        var html = '<p class="layer-warning"><i class="sprite-failsmall"></i><font>据《网络安全法》和相关规定，为保障您正常发布，请<a href="https://aq.jd.com/process/bindMobile?s=3">验证手机号</a></font></p>';
                        $("#warning-info").html(html);
                    }else if (userRisk === "1002") {
                        _this.printfMsg("这个用户有违规发言历史");
                        var html = '<p class="layer-warning"><i class="sprite-failsmall"></i><font>抱歉，你有违规发言历史，暂不能发布信息 ～</font></p>';
                        $("#warning-info").html(html);
                    }else if (userRisk === "1") {
                        _this.printfMsg("这个用户没有登陆");
                        login({
                            modal: true,
                            complete: function(res) {
                                if (res && res.Identity && res.Identity.IsAuthenticated) {
                                    //  在这里处理登录成功后的方法

                                }
                            }
                        })
                    }
                    }


                }

            }
          })
        },

        showQuestionLayer: function() {
            var _this = this
            $('body').dialog({
                title: '提问',
                width: 420,
                type: 'html',
                source: _this.template.questionLayer,
                onReady: function() {
                    _this.dialog = this
                    var $dialog = _this.dialog.el,
                        $JTextArea = $dialog.find('.J-txa'),
                        $txtError = $dialog.find('.J-textarea-error')

                    $JTextArea.keyup(checkError)

                    function checkError(isShowErrorTip) {
                        var txtlen = $JTextArea.val().length
                        $dialog
                            .find('.J-txtnum')
                            .html(txtlen)
                            .removeClass('error')

                        $txtError.hide() //当编辑事件发生的时候 隐藏掉错误提示

                        var tip = ''
                        if (txtlen < 4) {
                            tip = '最少输入4个字哦~'
                        } else if (txtlen > 50) {
                            tip = '最多输入50个字哦~'
                        }

                        if (isShowErrorTip != true) {
                            if (tip) {
                                $dialog.find('.J-txtnum').addClass('error')
                            } else {
                                $dialog.find('.J-txtnum').removeClass('error')
                            }
                            return
                        }

                        $txtError.find('.J-txt').html(tip)
                        if (tip) {
                            $txtError.show()
                            return true
                        } else {
                            $txtError.hide()
                            return false
                        }
                    }



                    $dialog.find('.J-btn-ok').click(function() {
                        if (!checkError(true)) {
                            _this.getUserQueryRisk("submit");
                        }
                    })

                    $dialog.find('.J-btn-cancel').click(function() {
                        _this.dialog.close()
                    })
                }
            })
        },
        showSuccess: function() {
            var _this = this
            $('body').dialog({
                title: '提示',
                width: 420,
                type: 'html',
                source: _this.template.success_layer,
                onReady: function() {
                    _this.dialog = this
                    var $dialog = _this.dialog.el
                    $dialog.find('.J-btn-ok').click(function() {
                        _this.dialog.close()
                    })
                }
            })
        },
        showFail: function(info) {
            var _this = this
            var html = _this.template.fail_layer.process({ errorInfo: info })
            $('body').dialog({
                title: '提示',
                width: 420,
                type: 'html',
                source: html,
                onReady: function() {
                    _this.dialog = this
                    var $dialog = _this.dialog.el
                    $dialog.find('.J-btn-ok').click(function() {
                        _this.dialog.close()
                    })
                }
            })
        },
        submitQuestion: function(content) {
            var _this = this
            var $dialog = _this.dialog.el

            var time = new Date().getTime()
            var colorParm = {
                appid: 'item-v3',
                functionId: 'saveQuestion',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                loginType: '3',
                // uuid: Tools.getCookieNew("__jda") || '',
                content: encodeURIComponent(content),
                productId: _this.cfg.skuid,
                anonymous: $dialog.find('.J-anonymous').is(':checked') ? 1: 0
            }
            try{
                colorParm['uuid'] = Tools.getCookieNew("__jda")
            }catch(e){
                colorParm['uuid'] = ''
            }

            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }

            $.ajax({
                // url: '//question.jd.com/question/saveQuestion.action',
                // data: {
                //     content: encodeURIComponent(content),
                //     productId: _this.cfg.skuid,
                //     //                    productId: 10000009,
                //     anonymous: $dialog.find('.J-anonymous').is(':checked')
                //         ? 1
                //         : 0
                // },
                // dataType: 'jsonp',
                // cache: true,
                // scriptCharset: 'gbk',
                url: host,
                data: colorParm,
                dataType: 'json',
                contentType: "application/json;charset=gbk",
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function(r) {
                    var $txtError = $dialog.find('.J-textarea-error')

                    if (r.status == 0) {
                        _this.dialog.close()
                        _this.showSuccess()
                    } else if (
                        r.status == 2 ||
                        r.status == 5 ||
                        r.status == 6 ||
                        r.status == 7
                    ) {
                        $dialog.find('.J-txt').html(r.info)
                        $txtError.show()
                    } else {
                        _this.dialog.close()
                        _this.showFail(r.info)
                    }
                }
            })
        },
        /**
        *   展示提示用户绑定手机号浮层
        */
        showBindPhoneNum: function() {
          var _this = this
          var html = _this.template.bindPhoneNum_layer;
          $('body').dialog({
              title: '',
              width: 420,
              type: 'html',
              source: html,
              onReady: function() {
                  _this.dialog = this
                  var $dialog = _this.dialog.el
                  $dialog.find('.J-btn-cancel').click(function() {
                      _this.dialog.close()
                  })
              }
          })
        },
        /**
        *   展示提示用户有违规发言历史的浮层
        */
        showVolation: function() {
          var _this = this
          var html = _this.template.violation_layer;
          $('body').dialog({
              title: '',
              width: 420,
              type: 'html',
              source: html,
              onReady: function() {
                  _this.dialog = this
                  var $dialog = _this.dialog.el
                  $dialog.find('.J-btn-ok').click(function() {
                      _this.dialog.close()
                  })
              }
          })
        },
        /**
        *   调试信息打印接口    根据debug的值可以控制打不打印信息
        *   @param {object}  msg  要打印的信息
        */
        printfMsg: function(msg) {
          var debug = false;
          if (debug) {
            console.log(msg);
          }
        }
    }

    function init(cfg) {
        AskAnswer.init(cfg)
    }

    module.exports.__id = 'comment'
    module.exports.init = init
    module.exports.AskAnswer = AskAnswer
})
