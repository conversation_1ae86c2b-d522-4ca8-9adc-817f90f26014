.J-cal {
    font: 12px arial;
    background: #fff;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    ul, li {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .J-cal-body span, .J-cal-weeks span {
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        display: inline-block;
        *zoom: 1;
    }
    .J-cal-switch {
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        display: inline-block;
        *zoom: 1;
        cursor: pointer;
    }
    .J-prev-month-date, .J-next-month-date {
        color: #999;
    }
    .J-cal-weeks span {
        border: 1px solid #fff;
    }
    .J-cal-body span {
        border: 1px solid #eee;
        &:hover {
            background: #eee;
            cursor: pointer;
        }
    }
    .J-cal-switch {
        background: #eee;
    }
    .J-cal-hover {
        background: #eee;
        color: #fff;
    }
    .J-cal-body .J-cal-active {
        background: #fc0;
        border-color: #fc0;
        &:hover {
            background: #fc0;
            border-color: #fc0;
        }
    }
}

.J-footer span {
    display: block;
    padding: 4px 0;
    text-align: center;
    &:hover {
        background: #eee;
        cursor: pointer;
    }
}
