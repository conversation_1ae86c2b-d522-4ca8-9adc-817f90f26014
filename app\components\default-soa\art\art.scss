.itemInfo-wrap{
    .li .item.hover a,.li .item.selected a,.li .item:hover a {
        border: 1px solid #FF0F23;
        color: #FF0F23;
        background: #FFEBF1;
        border-radius: 4px;
    }
    .summary-price-wrap .summary-price .p-price {
        color: #ad8954;
        .price{
            color: #ad8954;
        }
    }
    
}
.btn-primary, .btn-special1 {
    background-color: #000;
    color: #fff;
}
.ETab {
    .tab-main {
        border-bottom: 1px solid #000;
        li.current {
            background-color: #000;
            color: #fff;
            cursor: default;
        }
    }
}

.preview {
    .spec-items ul li.img-hover img, .preview .spec-items ul li:hover img {
        border: 2px solid #000;
    }
    .preview-info .left-btns .follow i {
        width: 14px;
        height: 13px;
        background-image: url(//static.360buyimg.com/item/art/1.0.68/components/preview/i/__sprite.png);
        background-position: -31px -57px;
    }
    .preview-info .left-btns .share i {
        width: 15px;
        height: 14px;
        background-image: url(//static.360buyimg.com/item/art/1.0.68/components/preview/i/__sprite.png);
        background-position: -50px -57px;
    }
}
#choose-service {
    .service-type-yb .yb-item-cat.selected .yb-item {
        border: 1px solid #000;
    }
   .service-type-yb .yb-item-cat .yb-item:hover {
        border: 1px solid #000
    }
}
