define('PUBLIC_ROOT/modules/address/address', function(require, exports, module) {
    //var Area = require('PUBLIC_ROOT/modules/address/area')
    require('//static.360buyimg.com/item/assets/address/area');
    var Area =  common_getAreaMap();
    var Stock = require('./stock')
    var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event
    var tools = require('PUBLIC_ROOT/modules/common/tools/tools')

    require('PUBLIC_ROOT/modules/EDropdown/EDropdown')
    require('PUBLIC_ROOT/modules/ETab/ETab')
    require('PUBLIC_ROOT/modules/address/address.css')

    /**
     * 地址选择
     */
    function Address($el, cfg) {
        this.$el = $el
        this.cfg = cfg

        if (!this.$el.length) {
            return
        }
        this.init()
    }
    Address.prototype = {
        init: function() {
            this.VAL_KEY = 'data-value'
            this.RES_ATTR = 'data-res'
            this.AREA_CLICKED = 'clicked'
            this.COMMON_COUNT = 0

            this.$tabItems = null
            this.$tabTrigs = null
            this.$areaTab = this.$el.find('.J-address-tab')
            this.$commArea = this.$el.find('.J-common-address')

            this.$stockAddressInner = this.$el.find('.inner')
            this.$stockAddressArr = this.$el.find('.head .arrow')
            this.$stockAddressUsed = this.$el.find('.address-used')
            this.$stockAddressSelect = this.$el.find('.address-select')

            // 默认
            this.level = 0
            this.MAX_LEVEL = 3
            this.area = {
                id: [1, 72, 55653, 0],
                uid: null,
                name: ['北京', '', '', '']
            }

            //this.setDefaultCookie();

            this.checkCookie()
            this.bindEvent()
            this.setProvince()

            this.areaId = tools.getAreaId();
            // 从cookie中同步下数据
            this.area.uid = this.areaId.commonAreaId;
            this.area.id = this.areaId.areaIds;
            if (readCookie('ipLocation')) {
                this.area.name[0] = unescape(readCookie('ipLocation'));
            }


            //读取cookie设置配送至显示内容
            this.setDeliverToAddress();
        },
        setDeliverToAddress: function(){
            var _this = this;
            var detailedShowAddress= readCookie('detailedShowAddress');
            if(detailedShowAddress){
                setTimeout(function(){
                    _this.setResultAddress(decodeURIComponent(detailedShowAddress))
                },2000);
            }
        },
        onOpen: function() {
            var _this = this
            var pid = this.areaId.areaIds[0]
            this.$stockAddressInner.addClass('border')
            this.$stockAddressArr.addClass('arr-open')

            if (!this.hasSetSelectedAddress) {
                this.getCommonAddress()

                if (pid === 53283) {
                    this.getHYArea(Area.data[0][pid])
                } else {
                    // 从二级地址开始取下级地址数据更新
                    this.setSelectedAddress(function() {
                        // 使用三级地区取库存查看是否有四级地址
                        var ids = [
                            _this.area.id[0],
                            _this.area.id[1],
                            _this.area.id[2],
                            0
                        ]
                        new Stock(
                            {
                                area: ids.join('_')
                            },
                            function(res) {
                                if (_this.hasLevel4(res.stock)) {
                                    _this.loadLevel4Areas()
                                }
                            },
                            null,
                            true
                        )
                    })
                }

                this.hasSetSelectedAddress = true
            } else {
                this.selectCommonAddress()
            }
        },
        onCommonAddressOpen: function() {
            if (this.COMMON_COUNT > 0 && this.COMMON_COUNT < 5) {
                this.$stockAddressUsed.addClass('auto').removeClass('clicked')
            }
            if (!this.$usedDD.isOpen) {
                this.$selectDD.close()
            }
        },
        onCommonAddressClose: function() {
            if (this.COMMON_COUNT > 0 && this.COMMON_COUNT < 5) {
                this.$stockAddressUsed.removeClass('auto')
            }

            if (this.$usedDD.isOpen) {
                this.$selectDD.open()
            }
        },
        getAreaCookieStr: function() {
            return this.area.id.join('-') + '.' + this.area.uid
        },
        selectCommonAddress: function() {
            var areaStr = this.getAreaCookieStr()
            var $items = this.$el.find('.J-common-address li')

            $items.each(function() {
                var value = $(this).data('value')
                if (value.indexOf(areaStr) > -1) {
                    $(this).addClass('selected')
                } else {
                    $(this).removeClass('selected')
                }
            })
        },
        loadLevel4Areas: function() {
            // 如果有四级地址
            var aids = this.areaId.areaIds

            //if (aids.length === 4 && aids[3] !== 0) {
            if (aids.length === 4) {
                this.level = 2
                this.getNextArea(
                    aids[this.level],
                    $.proxy(this.setLevel4Name, this)
                )
            }
        },
        setLevel4Name: function(data) {
            if (!data || !data.length) return false

            var area = null
            var id = tools.getAreaId().areaIds[3]

            // 如果有四级地址但是 cookie 四级 id 为 0，默认选第一个四级地址
            // 否则遍历四级地址数据找与四级 id 匹配的地区信息
            if (id === 0) {
                area = data[0]
            } else {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].id === id) {
                        area = data[i]
                        break
                    }
                }
            }
            this.$tabTrigs.eq(3).html(area.name)
            this.updateSelected(area.id, area.name, 3)
        },
        onClose: function() {
            this.$el.removeClass('hover')
            this.$stockAddressInner.removeClass('border')
            this.$stockAddressArr.removeClass('arr-open')
            this.$el.removeAttr('data-disable')
        },
        bindEvent: function() {
            var _this = this

            // 选择过弹层不关闭
            this.$el
                .undelegate('click.disable_close')
                .delegate(
                    '[data-drop="head"],[data-drop="content"]',
                    'click.disable_close',
                    function() {
                        _this.$el.attr('data-disable', true)
                    }
                )

            // 空白处点击关闭弹出层
            $(document)
                .unbind('click.address')
                .bind('click.address', function(e) {
                    if ($(e.target).parents('#stock-address').length < 1) {
                        if (!$.browser.isIE7()) {
                            _this.onClose()
                        }
                    }
                })

            this.$areaTab.ETab({
                onSwitch: function(n) {
                    _this.onSwitch(n)
                }
            })

            pageConfig.ADDRESS_TAB = this.$areaTab.data('ETab')

            this.$tabItems = this.$areaTab.data('ETab').items
            this.$tabTrigs = this.$areaTab.data('ETab').triggers

            this.$el.EDropdown({
                onOpen: function() {
                    _this.onOpen()
                },
                onClose: function() {
                    _this.onClose()
                }
            })
            this.$dropdown = this.$el.data('EDropdown')
            this.$stockAddressUsed.EDropdown({
                event: 'click',
                current: 'more clicked',
                onOpen: function() {
                    _this.onCommonAddressOpen()
                },
                onClose: function() {
                    _this.onCommonAddressClose()
                }
            })
            this.$usedDD = this.$stockAddressUsed.data('EDropdown')

            this.$stockAddressSelect.data('open', true).EDropdown({
                event: 'click',
                current: 'clicked'
            })

            this.$selectDD = this.$stockAddressSelect.data('EDropdown')

            var targetSelector = '[' + this.VAL_KEY + ']'

            // 地址选择
            this.$tabItems.delegate(
                targetSelector,
                'click.area',
                $.proxy(this.handleAreaClick, this)
            )

            // 常用地址选择
            this.$commArea.delegate(
                targetSelector,
                'click.area',
                $.proxy(this.handleCommonAddressClick, this)
            )
        },
        renderHYItem: function(data) {
            var tpl =
                '\
            <div class="hw-tab-con">\
                <ul class="hw-letters J-hw-area-letters">\
                    <li><a href="#none" data-letter="A">A</a></li>\
                    <li><a href="#none" data-letter="B">B</a></li>\
                    <li><a href="#none" data-letter="C">C</a></li>\
                    <li><a href="#none" data-letter="D">D</a></li>\
                    <li><a href="#none" data-letter="E">E</a></li>\
                    <li><a href="#none" data-letter="F">F</a></li>\
                    <li><a href="#none" data-letter="G">G</a></li>\
                    <li><a href="#none" data-letter="H">H</a></li>\
                    <li><a href="#none" data-letter="J">J</a></li>\
                    <li><a href="#none" data-letter="K">K</a></li>\
                    <li><a href="#none" data-letter="L">L</a></li>\
                    <li><a href="#none" data-letter="M">M</a></li>\
                    <li><a href="#none" data-letter="N">N</a></li>\
                    <li><a href="#none" data-letter="P">P</a></li>\
                    <li><a href="#none" data-letter="R">R</a></li>\
                    <li><a href="#none" data-letter="S">S</a></li>\
                    <li><a href="#none" data-letter="T">T</a></li>\
                    <li><a href="#none" data-letter="W">W</a></li>\
                    <li><a href="#none" data-letter="X">X</a></li>\
                    <li><a href="#none" data-letter="Y">Y</a></li>\
                    <li><a href="#none" data-letter="Z">Z</a></li>\
                </ul>\
                <div class="J-hw-area-wrap hw-area-wrap">\
                    <div class="hw-area">{0}</div>\
                </div>\
            </div>'
            var result = ''

            for (var key in data) {
                if (data.hasOwnProperty(key)) {
                    var letter = '<div class="area-letter" id="hw-letter-{0}" data-letter="{0}">{0}</div>'.format(
                        key
                    )
                    var html = '<ul>' + this.renderItem(data[key]) + '</ul>'
                    result += letter + html
                }
            }
            return tpl.format(result)
        },
        /**
         * 渲染地区数据
         * @param {object} data - 地区数据
         * @return {string} html - 拼合完的 html 片段
         */
        renderItem: function(data) {
            var result = ''
            var item = null

            for (var i = 0; i < data.length; i++) {
                item = data[i]
                if (item.name.length > 6) {
                    data.push(data.splice(i, 1)[0])
                }
            }
            for (var i = 0; i < data.length; i++) {
                item = data[i]
                if (item.name.length > 12) {
                    data.push(data.splice(i, 1)[0])
                }
            }
            for (var i = 0, len = data.length; i < len; i++) {
                item = data[i]
                var cName = ''
                if (item.name.length > 6) {
                    cName = 'long-area'
                }
                if (item.name.length > 12) {
                    cName = 'longer-area'
                }

                // prettier-ignore
                result += '<li data-name="' + item.name + '" data-value="' + item.id + '" class="' + cName + '"><a href="#none">' + item.name + '</a></li>'
            }

            return result
        },
        setProvince: function() {
            this.$tabItems.eq(0).html(Area.provinceHtml)
        },
        /**
         * 如果库存接口返回地址异常
         * 重新回溯 ipLoc-djd 三级地址 cookie
         */
        validateCookie: function(stock) {
            // 只判断三级地址名称为空，表示 cookie 没有验证通过
            var area = stock.area
            var ids = tools.getAreaId().areaIds

            if (
                area &&
                (area.provinceName === '' ||
                    area.cityName === '' ||
                    (area.countyName === '' && ids[0] !== 53283))
            ) {
                this.checkCookie()
            }
        },
        hasCityId: function(pid, cid) {
            var data = Area.data['0'][pid]
            var len = data.length

            for (var i = 0; i < len; i++) {
                var area = data[i]
                if (area.id == cid) return true
            }
            return false
        },
        hasCountyId: function(data, countyId) {
            var len = data.length

            for (var i = 0; i < len; i++) {
                var area = data[i]
                if (area.id == countyId) return true
            }
            return false
        },
        checkCookie: function() {
            var _this = this
            var id = tools.getAreaId()
            var ids = id.areaIds
            var uid = id.commonAreaId

            var pid = ids[0]
            var cityId = ids[1]
            var countyId = ids[2]

            if (pid === 53283) {
                return false
            }
            
            // 如果 cookie 一级地址非数字 或者不在一级 id 列表中
            if (isNaN(pid) || !Area.provinceMap[pid]) {
                this.setCookie(this.area.id, Area.provinceMap[pid], uid)
                getStock(_this)
                return false
            }

            var shouldWriteCookie = false
            // 如果当前的二级地址id不存在，重置成二级地址下面第一个三级地址
            if (!this.hasCityId(pid, cityId)) {
                cityId = Area.data['0'][pid][0]['id']
                shouldWriteCookie = true
            }

            this.getNextArea(cityId, function(data) {
                if (!_this.hasCountyId(data, countyId)) {
                    countyId = data[0].id
                    shouldWriteCookie = true
                }

                if (shouldWriteCookie) {
                    _this.setCookie(
                        [pid, cityId, countyId, 0],
                        Area.provinceMap[pid],
                        uid
                    )
                    _this.areaId = tools.getAreaId()
                    // 写完默认 cookie 后再请求库存数据
                    getStock(_this)
                }
            })
        },
        handleAreaClick: function(event) {
            var $this = $(event.target).parent()
            var id = $this.data('value')
            var text = $this.text()
            var level = $this.parents('[data-tab="item"]').eq(0).data('level')

            this.area.uid = null
            this.setArea(id, level, text)
        },
        /**
         * 重新设置当前选择地区之前的地区信息
         * @param {number} level - 地区级数
         */
        setPreviousAreasData: function(level) {
            for (var i = 0; i < level; i++) {
                var $currItem = this.$tabTrigs.eq(i)
                this.setAreaData($currItem.attr('data-id'), $currItem.text(), i)

                //this.updateSelected($currItem.attr('data-id'), $currItem.text(), i);
            }
        },
        /**
         * 设置地区信息
         * @param {number} id - 当前选中的地区 id
         * @param {number} level - 地区级数
         * @param {string} text - 选中的地区名称
         */
        setArea: function(id, level, text) {
            this.level = Number(level)

            this.reset(this.level)
            // 选择二、三级地区时，需要查找选择过的前面的几级地区并更新cookie，常用地址切换的场景需要此功能
            this.setPreviousAreasData(level)
            this.setAreaData(id, text, level)
            this.updateSelected(id, text, level)

            if (this.level === this.MAX_LEVEL) {
                
                //删除detailedAdd cookie
                var domain='jd.com';
                try{
                    if (window.pageConfig &&
                        typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
                        domain = pageConfig.FN_getDomainNew();
                    }else if (window.pageConfig &&
                        typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
                        domain = pageConfig.FN_getDomain();
                    }else {
                        domain = document.domain;
                    }
                }catch(e){
                    domain = document.domain;
                }
                createCookie('detailedAdd', '', -1, '/;domain=' + domain);
                createCookie('detailedShowAddress', '', -1, '/;domain=' + domain);
                createCookie('detailedAdd_coord', '', -1, '/;domain=' + domain);
                createCookie('detailedAdd_areaid', '', -1, '/;domain=' + domain);
                // 选择四级地址
                this.getStock($.proxy(this.setResultAddress, this))
            } else if (this.level === this.MAX_LEVEL - 1) {
                // 点到三级地址时先判断库存字段是否存在四级地址再渲染
                this.checkLevel4(id)
            } else {
                // 全球售
                if (this.area.id[0] === 53283) {
                    if (level === 1) {
                        this.getStock($.proxy(this.setResultAddress, this))
                    } else {
                        this.getHYArea(Area.data[0][this.area.id[0]])
                    }
                } else {
                    this.getNextArea(id)
                }
            }
        },
        getNextArea: function(id, callback) {
            var _this = this
            callback = callback || function() {}

            this.getNextAreaData(
                id,
                function success(data) {
                    if (data && data.length) {
                        _this.onSuccess(data, id)
                    }
                    callback(data)
                },
                function error() {
                    // 二级地址获取失败的时候取对应 mapping 里面的三级地址 id 回调库存事件
                    if (_this.level === 1) {
                        console.log('address server error.')
                        _this.setAreaData(Area.area[id], '请选择', ++_this.level)
                        _this.setResultAddress()
                    }
                }
            )
        },
        hasLevel4: function(stock) {
            return (
                stock &&
                (stock.areaLevel === 4 ||
                    (stock.level === 4 && stock.code === 3))
            )
        },
        done: function (res) {
            this.__stock.set(res)
            this.setResultAddress()
            this.triggerStockEvent(res)
            this.hideTabTrigger()
        },
        /**
         * 检测是否有四级地址
         * @param {number} id - 地区id
         */
        checkLevel4: function(id) {
            var _this = this
            //删除detailedAdd cookie
            var domain='jd.com';
            try{
                if (window.pageConfig &&
                    typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
                    domain = pageConfig.FN_getDomainNew();
                }else if (window.pageConfig &&
                    typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
                    domain = pageConfig.FN_getDomain();
                }else {
                    domain = document.domain;
                }
            }catch(e){
                domain = document.domain;
            }
            createCookie('detailedAdd', '', -1, '/;domain=' + domain);
            createCookie('detailedShowAddress', '', -1, '/;domain=' + domain);
            createCookie('detailedAdd_coord', '', -1, '/;domain=' + domain);
            createCookie('detailedAdd_areaid', '', -1, '/;domain=' + domain);
            this.__stock = new Stock(
                {
                    area: this.area.id.join('_')
                },
                function(res) {
                    if (_this.hasLevel4(res.stock)) {
                        _this.getNextArea(id, function (d) {
                            // 四级地址无数据
                            if (!d || !d.length) { _this.done(res) }
                        })
                    } else {
                        // 确定没有4级地址时重新触发 Stock.set() 方法及库存完成事件
                        _this.done(res)
                    }
                },
                null,
                true
            )
        },
        hideTabTrigger: function() {
            this.$tabTrigs.eq(3).hide()
        },
        bindHWEvent: function() {
            this.$stockAddressSelect
                .find('.J-hw-area-wrap')
                .unbind('scroll')
                .bind('scroll', $.proxy(this.handleHWScroll, this))

            // 海外字母排序
            this.$stockAddressSelect
                .undelegate('click.letter')
                .delegate(
                    '.J-hw-area-letters li a',
                    'click.letter',
                    $.proxy(this.handleCommonLetterClick, this)
                )
        },
        handleCommonLetterClick: function(event) {
            var $tar = $(event.currentTarget)
            var letter = $tar.data('letter')

            this.$stockAddressSelect
                .find('.J-hw-area-letters a')
                .removeClass('clicked')
            $tar.addClass('clicked')

            var $wrap = this.$stockAddressSelect.find('.J-hw-area-wrap')
            var $childs = $wrap.find('.hw-area').children()
            var height = 0
            $childs.each(function() {
                var currLetterStr = $(this).data('letter')
                if (currLetterStr && currLetterStr.indexOf(letter) > -1) {
                    $wrap.scrollTop(height)
                    return false
                } else {
                    height += $(this).outerHeight()
                }
            })
        },
        handleHWScroll: function(event) {
            var $tar = $(event.currentTarget)

            function isScrolledIntoView(elem, scrollElement, offset) {
                var $elem = $(elem)
                var $window = $(scrollElement)
                var docViewTop = $window.scrollTop()
                var docViewBottom = docViewTop + $window.height()
                var elemTop = $elem.offset().top
                var elemBottom = elemTop + $elem.height()

                return (
                    (elemBottom + offset >= docViewBottom &&
                        elemTop - offset <= docViewTop) ||
                    (elemBottom - offset <= docViewBottom &&
                        elemTop + offset >= docViewTop)
                )
            }

            var $letterWrap = this.$stockAddressSelect.find('.J-hw-area-letters')
            var scrollEl = this.$stockAddressSelect.find('.hw-area')[0]
            $letterWrap.find('li a').removeClass('clicked')

            // this.$letterWrap.find('.area-letter').each(function () {
            //     if (isScrolledIntoView(this, scrollEl, 0)) {
            //         var letters = $(this).data('letter').split('')
            //         for (var i = 0; i < letters.length; i++) {
            //             var letter = letters[i]
            //             $letterWrap.find('[data-letter="'+ letter +'"]').addClass('clicked')
            //         }
            //     }
            // })
        },
        onHYSuccess: function () {
            this.setResultAddress()
        },
        /**
         * 取到海外全球售数据
         * @param data
         * @param id
         */
        getHYArea: function(data) {
            var result = this.renderHYItem(data)
            // Area.data[this.level][id] = data

            // 三级地址选择完请求库存接口确定是否存在四级地址
            this.$tabTrigs.eq(this.level + 1).trigger('click.ETab').show()
            this.$tabItems.eq(this.level + 1).html(result)

            this.setTabName(0, 53283, '海外')
            this.reset(0)

            this.bindHWEvent()
        },
        /**
         * 获取下级地址成功回调
         * @param {object} data 地区对应的下级地址数据
         * @param {number} id - 点击地区 id
         */
        onSuccess: function(data, id) {
            var result = this.renderItem(data)
            Area.data[this.level][id] = data

            // 三级地址选择完请求库存接口确定是否存在四级地址
            this.$tabTrigs.eq(this.level + 1).trigger('click.ETab').show()
            this.$tabItems.eq(this.level + 1).html(result)

            if (this.level >= this.MAX_LEVEL) {
                this.setResultAddress()
            }
        },
        /**
         * 写入地区相关 cookie
         */
        setCookie: function(aids, pName, uid) {
            aids = aids || this.area.id
            pName = pName || this.area.name[0]
            uid = uid || this.area.uid

            //console.log('aids: %s | pName: %s | uid: %s', aids, pName, uid);

            var domain='jd.com';
            try{
                if (window.pageConfig &&
                    typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
                    domain = pageConfig.FN_getDomainNew();
                }else if (window.pageConfig &&
                    typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
                    domain = pageConfig.FN_getDomain();
                }else {
                    domain = document.domain;
                }
            }catch(e){
                domain = document.domain;
            }
            var ipLocVal = trimMinus(aids.join('-'))

            if (this.area.uid) {
                ipLocVal += '.' + uid
            }

            function trimMinus(str) {
                return str.replace(/^-+|-+$/g, '')
            }

            createCookie('ipLocation', escape(pName), 30, '/;domain=' + domain)
            createCookie('areaId', aids[0], 10, '/;domain=' + domain)
            createCookie('ipLoc-djd', ipLocVal, 30, '/;domain=' + domain)
        },
        /**
         * 获取下一级地区数据
         * @param {number} id - 上级地址 id
         * @param {function} success - 获取成功回调方法
         * @param {function} error - 获取失败回调方法
         */
        getNextAreaData: function(id, success, error) {
            error = error || function() {}

            // 缓存数据
            if (Area.data[this.level][id]) {
                return success(Area.data[this.level][id])
            }
            $.ajax({
                url: '//d.jd.com/area/get',
                data: { fid: id },
                timeout: 1000,
                dataType: 'jsonp',
                cache: true,
                jsonpCallback: 'getAreaListCallback',
                success: $.proxy(success, this),
                error: $.proxy(error, this)
            })
        },
        truncateTabName: function(text) {
            return text.length > 5 ? text.substr(0, 5) : text
        },
        /**
         * 更新选择地区后的 ui 状态
         * @param {number} id - 地区 id
         * @param {string} text - 地区名称
         */
        updateSelected: function(id, text, level) {
            var cName = this.AREA_CLICKED

            this.setTabName(level, id, text)
            this.$tabItems
                .eq(level)
                .find('a')
                .removeClass(cName)
                .filter('[' + this.VAL_KEY + '=' + id + ']')
                .addClass(cName)
        },
        setTabName: function(n, id, name) {
            var subText = this.truncateTabName(name)
            var attr = {}

            if (id) attr['data-id'] = id
            if (name.length > 5) attr['title'] = name

            this.$tabTrigs.eq(n).text(subText).attr(attr)
        },
        /**
         * 设置最终选择地区地址
         * @param {string} text - 地区文字
         */
        setResultAddress: function(text) {
            text = text || this.area.name.join(' ')
            this.$el.find('[' + this.RES_ATTR + ']').text(text)

            this.close()
        },
        /**
         * 选择地区后记录数据
         * @param {number} id - 地区 id
         * @param {string} text - 地区名称
         * @param {number} level - 地区级别
         */
        setAreaData: function(id, text, level) {
            this.area.id[level] = Number(id)
            this.area.name[level] = text
        },
        handleCommonAddressClick: function(event) {
            var $this = $(event.currentTarget)
            // {aids[0]},{aids[1]},{aids[2]},{aids[3]}.{usualId}|{addressName}
            var value = $this.data('value').split('|')
            var val = value[0]
            var ids = val.split('.')[0]
            var uid = val.split('.')[1]
            var areaId = ids.split('-')
            var areaName = value[1]
            var coord = $this.data('coordinate');

            for (var i = 0; i < areaId.length; i++) {
                areaId[i] = Number(areaId[i])
            }

            this.area.id = areaId
            this.area.uid = Number(uid)
            this.area.name[0] = Area.provinceMap[areaId[0]]

            //获得用户点击的常用地址
            if($this && $this.length){
                var clickedAddress = $this.find('span.address');
                var fullAddress = clickedAddress.text();
                if(fullAddress){
                    //拼凑显示带省略号的详细地址
                    var showAddress = this.getShowAddressString(fullAddress,areaName) || areaName;
                    //将用户点击的常用地址保存至cookie，用户点击的常用地址在调用cd.jd.com/stock接口时将作为入参传入
                    this.saveCommonAddress2Cookie(fullAddress,showAddress,coord,ids);
                }
            }
            var _this = this;
            setTimeout(function(){
                _this.setResultAddress(showAddress)
            },2000);
            this.setCookie()
            this.getStock($.proxy(this.close, this), {coord: coord})
        },
        getCommonAddress: function() {
            var _this = this
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/usual/address',
                dataType: 'jsonp',
                data: {
                    appid: 'item-v3',
                    functionId: "pc_usual_address"
                },
                scriptCharset: 'gbk',
                success: function(r) {
                    if (r && r.length) {
                        _this.setCommonAddress(r)
                    }
                }
            })
        },
        /**
         * 更新上次选中过的地址信息
         */
        setSelectedAddress: function(callback) {
            var area = pageConfig._CURR_AREA
            var $currProvince = this.$tabItems
                .eq(0)
                .find('[data-name="' + area.provinceName + '"]')
            var pid = $currProvince.data('value')
            var city = Area.data[0][pid]

            var result = this.renderItem(city)

            this.$tabItems.eq(1).html(result)

            this.setTabName(0, pid, area.provinceName)
            this.setTabName(1, this.areaId.areaIds[1], area.cityName)
            this.setTabName(2, this.areaId.areaIds[2], area.countyName)
            this.setTabName(3, this.areaId.areaIds[3], '')

            this.area.id = this.areaId.areaIds

            // 取三级地址
            this.level = 1
            this.getNextArea(this.areaId.areaIds[this.level], callback)
        },
        setCommonAddress: function(data) {
            var result = ''
            var key = this.VAL_KEY
            var tpl =
                '\
            <li {0}="{1}-{2}-{3}-{4}.{5}|{6}" data-coordinate="{9},{10}">\
                <a title="{8} {7}" href="#none">\
                    <span class="name">{8}</span>\
                    <span class="address">{7}</span>\
                </a>\
            </li>'

            function getHtml(res) {
                res.addressName = res.addressName
                    .replace('null', '')
                    .substring(0, 8)

                // if (res.addressDefault) {
                //     res.addressName = '❤' + res.addressName
                // }

                return tpl.format(
                    key,
                    res.provinceId,
                    res.cityId,
                    res.countyId,
                    res.townId,
                    res.id,
                    res.areaName,
                    res.fullAddress,
                    res.name,
                    res.gcLng,
                    res.gcLat
                )

                //return '<li '+ key +'="'+ res.provinceId +','+ res.cityId +','+ res.countyId +','+ res.townId +'|'+ res.areaName +'"><a title="'+ res.fullAddress +'" href="#none">'+ res.addressName +'</a></li>'
            }

            result += '<ul>'
            for (var i = 0; i < data.length; i++) {
                var currAdd = data[i]
                if (currAdd.addressName) {
                    result += getHtml(currAdd)
                    this.COMMON_COUNT++
                }
            }
            result += '</ul>'

            if (this.COMMON_COUNT > 0) {
                this.$stockAddressUsed.data('open', false).show()

                if (this.COMMON_COUNT === 1) {
                    this.$stockAddressUsed.find('.arrow').hide()
                }
            } else {
                this.$stockAddressUsed.hide()
            }
            this.$commArea.html(result).removeClass('hide')
            this.selectCommonAddress()
        },
        /**
         * 重复选择地区清除下级地区选择数据
         * @param {number} level - 地区 id
         */
        reset: function(level) {
            /**
             level: 0   remove: 1,2,3
             level: 1   remove: 2,3
             level: 2   remove: 3
             level: 3   remove: none
             */

            if (level == 0) {
                this.resetItem(1)
                this.resetItem(2)
                this.resetItem(3)
            }
            if (level == 1) {
                this.resetItem(2)
                this.resetItem(3)
            }
            if (level == 2) {
                this.resetItem(3)
            }

            // if (level == 3) {

            //     this.resetItem(4);

            // }
        },
        resetItem: function(n) {
            this.area.id[n] = 0
            this.area.name[n] = ''
            this.$tabTrigs.eq(n).text('请选择')

            if (this.$tabTrigs.eq(n + 1).length) {
                this.$tabTrigs.eq(n + 1).hide()
            }
        },
        /**
         * 关闭地址选择弹层
         */
        close: function() {
            this.$dropdown.close()
            this.setCookie()
        },
        /**
         * 触发获取库存数据事件
         */
        //fireStock: function () {
        //    if (/debug=stock/.test(location.href)) {
        //        console.log('fire stock event with area ids: [%s]', this.area.id.join('-'));
        //    }
        //    Event.fire({
        //        type: 'onAreaChange',
        //        area: this.area
        //    });
        //},
        /**
         * 地区 tab 切换后触发事件
         * @param {number} 地区级别
         */
        onSwitch: function(n) {
            this.level = n

            //this.reset(n);
        },
        triggerStockEvent: function(r) {
            // 地区切换后 & 页面加载完库存请求完成后 触发
            Event.fire({
                type: 'onStockReady',
                area: this.area,
                stock: r
            })
            // 「仅」地区切换后触发，页面加载完不请求
            Event.fire({
                type: 'onAreaChange',
                area: this.area,
                stock: r
            })
        },

        /**
         * 库存接口 改造函数使外部函数调用时可以传递更多的参数
         * @param {any} callback 
         * @param {any} options 
         */
        getStock: function(callback, options) {
            var _this = this;

            if ($.isPlainObject(options)) {
                options = $.extend({
                    area: this.area.id.join('_')
                }, options);
            } else {
                options = {
                    area: this.area.id.join('_')
                };
            }

            new Stock(options, function(r) {
                callback.call(_this, r)
                _this.triggerStockEvent(r)
            });
        },
        saveCommonAddress2Cookie: function(fullAddress,showAddress,coord, ids){
                var domain='jd.com';
                try{
                    if (window.pageConfig &&
                        typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
                        domain = pageConfig.FN_getDomainNew();
                    }else if (window.pageConfig &&
                        typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
                        domain = pageConfig.FN_getDomain();
                    }else {
                        domain = document.domain;
                    }
                }catch(e){
                    domain = document.domain;
                }
                //将fullAddress里的空格替换成^ 王琨在后端再将^换成空格。 空格在请求时会导致后台报错，所以这样处理
                fullAddress = fullAddress.replace(/ /g,'^');

                createCookie('detailedAdd', encodeURIComponent(fullAddress), 10, '/;domain=' + domain);
                createCookie('detailedShowAddress', encodeURIComponent(showAddress), 10, '/;domain=' + domain);
                createCookie('detailedAdd_coord', coord, 10, '/;domain=' + domain);
                createCookie('detailedAdd_areaid', ids, 10, '/;domain=' + domain);
        },
        getShowAddressString: function(fullAddress,areaName){
            var reg = new RegExp('^('+areaName+')(.*)');
            var partAddress = fullAddress.replace(reg,'$2'); 
            if(partAddress){
                if(partAddress.length>10){
                    partAddress = partAddress.substr(0,10)+'...';
                }
            }
            return areaName+' '+partAddress;
        }
    }

    function getStock(address) {
        var defaultOptions = {};
        // 如果页面载入时显示的是详细地址，把坐标信息传递个库存接口
        if (readCookie('detailedAdd') && readCookie('detailedAdd_coord')) {
            defaultOptions.coord = readCookie('detailedAdd_coord')
        }
        new Stock(defaultOptions, function(r) {
            Event.fire({
                type: 'onStockReady',
                area: { id: this.areas },
                stock: r
            })
            Event.fire({
                type: 'onDefaultStockReady',
                area: { id: this.areas },
                stock: r
            })
            if (address) {
                setDefaultFourthId(r.stock, this.areas)
                // 按库接口返回值确定是否需要验证cookie正确性
                // address.validateCookie(r.stock)
            }
        })

        function setDefaultFourthId(stock, aids) {
            // 如果当前有四级地址且cookie中四级地址为0，取对应的四级地址重新写入cookie，然后请求库存地址
            if (address.hasLevel4(stock) && aids[3] === 0) {
                address.getNextArea(aids[2], function(data) {
                    if (!data || !data.length) return
                    aids[3] = data[0].id
                    address.setCookie(aids)
                    getStock()
                })
            }
        }
    }

    function getDefaultStock(cfg, address) {
        getStock(address)
        // 数量变更重新请求库存接口
        Event.addListener('onNumChange', function() {
            getStock(address)
        })
    }

    function checkipLocJdj() {
        var ipLoc = tools.getAreaId().areaIds
        var domain='jd.com';
        try{
            if (window.pageConfig &&
                typeof window.pageConfig.FN_getDomainNew === "function") { // 模版配置
                domain = pageConfig.FN_getDomainNew();
            }else if (window.pageConfig &&
                typeof window.pageConfig.FN_getDomain === "function") { // 公共文件base内配置
                domain = pageConfig.FN_getDomain();
            }else {
                domain = document.domain;
            }
        }catch(e){
            domain = document.domain;
        }
        if (ipLoc.length == 3) {
            ipLoc.push(0)
            var ipLocStr = ipLoc.join('-')
            createCookie('ipLoc-djd', ipLocStr, 30, '/;domain=' + domain)
        }
    }

    // pv现货率 埋点
    function stockLog(cfg) {
        function sendLog(data) {
            var price = cfg.jp || ''
            var stock = data.stock.stock
            var pvstock = {
                webstate: 1,
                delivestate: 5,
                stock: 33
            }
            var state = stock.code == 2 ? -1 : stock.StockState
            var stateMap = {
                '33': 1,
                '34': 4,
                '0': 4,
                '39': 2,
                '40': 2,
                '36': 3
            }
            var deliveMap = { '-1': 6 }
            if (stateMap[state]) pvstock.webstate = stateMap[state]
            if (deliveMap[state]) pvstock.delivestate = deliveMap[state]
            pvstock.stock = state

            var param = {
                area: data.area.id.join('_'),
                sku: [
                    [
                        cfg.skuid,
                        price,
                        pvstock.webstate,
                        pvstock.delivestate,
                        pvstock.stock
                    ]
                ]
            }

            if (typeof logJSON === 'function') {
                logJSON('pv_stock', 'sku', param)
            }
        }
        Event.addListener('onStockReady', function(data) {
            setTimeout(function() {
                sendLog(data)
            }, 500)
        })
    }

    // 续重
    function setWeight(cfg) {
        var $cw = $('#summary-weight')
        Event.addListener('onStockReady', function(r) {
            var s = r.stock.stock

            if (s.weightValue) {
                $cw.show().find('.dd').text(s.weightValue)
            } else {
                $cw.hide().find('.dd').text('')
            }
        })
    }

    function init(cfg) {
        checkipLocJdj() //如果是三级地址，就改成四级(结算页会写入三级地址的cookie)

        stockLog(cfg)

        var address = new Address($('#stock-address'), cfg)

        pageConfig.__address = address

        // 获取默认库存信息
        getDefaultStock(cfg, address)

        setWeight(cfg)
    }

    module.exports.__id = 'address'
    module.exports.init = init
})
