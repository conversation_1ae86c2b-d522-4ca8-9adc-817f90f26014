define('MOD_ROOT/detail/detail', function(require, exports, module) {
    var Event = require('MOD_ROOT/common/tools/event').Event
    var Nav = require('MOD_ROOT/detail/nav')
    var G = require('MOD_ROOT/common/core')
    var Tools = require('MOD_ROOT/common/tools/tools')
    var QualityLife = require('MOD_ROOT/detail/qualitylife')

    require('MOD_ROOT/ETab/ETab')
    require('MOD_ROOT/EDropdown/EDropdown')
    require('MOD_ROOT/ELazyload/ELazyload')
    require('PLG_ROOT/jQuery.scroller')
    require('JDF_UI/elevator/1.0.0/elevator')
    require('JDF_UI/dialog/1.0.0/dialog')
    require('JDF_UI/pager/1.0.0/pager')
    //获取视频
    var videoBox = require('MOD_ROOT/videoBox/videoBox')

    function miniCart(cfg) {
        var $addCartMini = $('.J-addcart-mini')

        // $addCartMini.EDropdown({ // 迷你购物车按钮hover下线
        //     onOpen: function() {
        //         $('.J-buy-num').html(G.getNum())
        //     }
        // })

        Event.addListener('onStockReady', function() {
            if (cfg.havestock) {
                $addCartMini.removeAttr('data-disable')
            } else {
                $addCartMini.attr('data-disable', 'true')
            }
        })
    }
    function setFixedNav($detail) {
        var $floatnav = $('#placeholder-floatnav-stop')
        var $club = $('#club')
        var $consult = $('#consult')
        var $footmark = $('#footmark')

        var $end = null

        if ($consult.length) {
            $end = $consult
        }
        if ($club.length) {
            $end = $club
        }
        if ($footmark.length) {
            $end = $footmark
        }
        if ($floatnav.length) {
            $end = $floatnav
        }
        var html = '<div class="pro-detail-hd-fixed-div"></div>'
        $(html).appendTo('body')
        // 跟随详情tab
        $detail.scroller({
            delay: 0,
            end: $end,
            onStart: function() {
                this.$el.find('[data-fixed]').addClass('pro-detail-hd-fixed')
                $('.pro-detail-hd-fixed-div').show()
            },
            onEnd: function() {
                this.$el.find('[data-fixed]').removeClass('pro-detail-hd-fixed')
                $('.pro-detail-hd-fixed-div').hide()
            }
        })

        //        $detail.delegate('[data-tab]', 'click', function () {
        //            var anchor = $(this).attr('data-anchor');
        //            var fixed = $detail.find('[data-fixed]').hasClass('pro-detail-hd-fixed');
        //
        //            if ( !anchor && fixed ) {
        //                setTimeout(function () {
        //                    $('html,body').scrollTop( $detail.offset().top );
        //                }, 100);
        //            }
        //        });

        $('.detail').elevator({
            floorClass: 'detail-elevator-floor', //楼层className
            elevatorClass: 'detail-elevator', //电梯主体className
            handlerClass: 'detail-elevator-handler', //电梯按钮className
            selectClass: 'current' //电梯按钮被选中的className
        })
    }

    function setDetailNav($detail) {
        // 跟随效果
        $detail.ELazyload({
            type: 'module',
            onAppear: function() {
                if (G.wideVersion) {
                    // 跟随快速导航
                    new Nav({ $el: $('#J-detail-nav') })
                }
            }
        })
    }

    var eBookTPL =
        '\
    {if list.editorPick}\
    <div class="formwork_bt" id="editorPick" name="detail-tag-id-0" text="编辑推荐">\
        <div class="formwork_bt_it" ><span>编辑推荐</span></div>\
        <div class="con">${list.editorPick}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}\
    {if list.contentInfo}\
    <div class="formwork_bt" id="contentInfo" name="detail-tag-id-1" text="内容简介">\
        <div class="formwork_bt_it"><span>内容简介</span></div>\
        <div class="con">${list.contentInfo}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}\
    {if list.authorInfo}\
    <div class="formwork_bt" id="authorInfo" name="detail-tag-id-2" text="作者简介">\
        <div class="formwork_bt_it"><span>作者简介</span></div>\
        <div class="con">${list.authorInfo}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}\
    {if list.catalog}\
    <div class="formwork_bt" id="catalog" name="detail-tag-id-3" text="目录">\
        <div class="formwork_bt_it"><span>目录</span></div>\
        <div class="con">${list.catalog}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}\
    {if list.mediaComments}\
    <div class="formwork_bt" id="mediaComments" name="detail-tag-id-4" text="媒体评论">\
        <div class="formwork_bt_it"><span>媒体评论</span></div>\
        <div class="con">${list.mediaComments}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}\
    {if list.preface}\
    <div class="formwork_bt" id="preface" name="detail-tag-id-5" text="前言">\
        <div class="formwork_bt_it"><span>前言</span></div>\
        <div class="con">${list.preface}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}\
    {if list.digest}\
    <div class="formwork_bt" id="digest" name="detail-tag-id-6" text="精彩书摘">\
        <div class="formwork_bt_it"><span>精彩书摘</span></div>\
        <div class="con">${list.digest}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}\
    {if list.illustration}\
    <div class="formwork_bt" id="illustration" name="detail-tag-id-7" text="精彩插图">\
        <div class="formwork_bt_it"><span>精彩插图</span></div>\
        <div class="con">${list.illustration}</div>\
        <div class="more"><a href="javascript:void(0)">查看全部↓</a></div>\
    </div>\
    {/if}'

    // 电子书评
    var BookComment = {
        inited: false,
        init: function(cfg) {
            this.cfg = cfg

            this.ITEMS_PER_PAGE = 6
            this.page = 1

            this.$el = $('#e-comment')
            this.$trigger = $('#detail [data-anchor="#e-comment"]')

            if (!this.inited) {
                this.bindEvent()
                this.get(this.page)
                this.inited = true
            }
        },
        bindEvent: function() {
            this.$el.delegate(
                '.J-more',
                'click',
                $.proxy(this.handleClick, this)
            )
        },
        handleClick: function(e) {
            var $this = $(e.currentTarget)
            var $cont = $this.prev()

            if ($cont.is('.close')) {
                $cont.removeClass('close').addClass('open')
                $this.addClass('hover').find('s').text('收起')
            } else {
                $cont.removeClass('open').addClass('close')
                $this.removeClass('hover').find('s').text('展开全文')
            }
        },
        getTPL: function() {
            var template =
                '\
            <div class="book-review">\
                <div class="J-book-review">\
                    {for item in reviews.list}\
                    <div class="review-item">\
                        <div class="user-line">\
                            <span class="avatar"><img src="${item.userHeadFullUrl}" alt=""/></span>\
                            <span class="name">${item.nickname}</span>\
                            <span class="star star${item.score}"></span>\
                            <span class="time">${item.creationTime|getTime}</span>\
                        </div>\
                        <div class="review-con">\
                            <i class="arrowUp"></i>\
                            <div class="review-content">${item.contents|stripUnsafeTags|lineToBr}</div>\
                            <div class="more EDropdown J-more hide">\
                                <a href="#none"><s>展开全文</s> <i class="arrow arr-close"></i></a>\
                            </div>\
                        </div>\
                    </div>\
                    {/for}\
                </div>\
                <div class="com-table-footer">\
                    <div class="ui-page-wrap clearfix">\
                        <div class="ui-page"></div>\
                    </div>\
                </div>\
            </div>'

            return template
        },
        get: function(page) {
            var body =
                '{"currentPage":' +
                page +
                ',"pageSize":' +
                this.ITEMS_PER_PAGE +
                ',"eBookId":' +
                this.cfg.skuid +
                '}'
            this.page = page || 1
            // //gw-e.jd.com/client.action?functionId=greatComments&body={"currentPage":1,"pageSize":10,"eBookId":30295088}
            $.ajax({
                url: '//gw-e.jd.com/client.action',
                data: {
                    functionId: 'greatJsonPComments',
                    body: body
                },
                cache: true,
                dataType: 'jsonp',
                jsonpCallback: 'func',
                scriptCharset: 'utf-8',
                success: $.proxy(this.set, this)
            })
        },
        set: function(r) {
            if (
                !this.$el.length || !r || !r.reviews || !r.reviews.list.length
            ) {
                this.$trigger.hide()
                this.$el.hide()
                return false
            }
            this.$trigger.show()
            this.$el.show()

            r._MODIFIERS = {
                getTime: function(str) {
                    return str.substring(0, str.lastIndexOf(' '))
                },
                lineToBr: function(str) {
                    return str.replace(/\n/ig, '<br>')
                },
                stripUnsafeTags: function(str) {
                    return str.replace(
                        /<script>|<\/script>|<style>|<\/style>|<iframe>|<\/iframe>/g,
                        ''
                    )
                }
            }

            this.$el.find('.mc').html(this.getTPL().process(r))
            this.$trigger.find('s').html('(' + r.greatCount + ')')

            this.setPager(r.greatCount)
            this.setMore()
        },
        setMore: function() {
            var $els = this.$el.find('.review-item .review-content')
            $els.each(function() {
                var height = $(this).outerHeight()
                if (height > 120) {
                    $(this)
                        .attr('data-open', height)
                        .addClass('close')
                        .parents('.review-item')
                        .eq(0)
                        .addClass('has-more')
                }
            })
        },
        setPager: function(total) {
            var _this = this
            var $pager = this.$el.find('.ui-page')
            $pager.pager({
                total: total,
                pageSize: this.ITEMS_PER_PAGE,
                currentPageClass: 'ui-page-curr',
                currentPage: this.page,
                pageHref: '#e-comment',
                callback: function(pageId) {
                    _this.get(pageId)
                }
            })
        }
    }

    function getOld(cfg){
        setTecentVideo(cfg,"#tencent-video")
        if (cfg.isPop) {
            // $.ajax({
            //     url: '//cd.jd.com/template/channel',
            //     data: {
            //         productId: cfg.mainSkuId || '',
            //         venderId: cfg.venderId || '',
            //         keys: "headContent,bottomContent"
            //     },
            //     dataType: 'jsonp',
            //     scriptCharset: 'gbk',
            //     success: function(r) {
            //         var $topNew = $("#J-detail-pop-tpl-top-new");
            //         var $topBottom = $("#J-detail-pop-tpl-bottom-new");
            //         if (r && r.headContent) {
            //             $topNew.html(r.headContent);
            //         }
            //         if (r && r.bottomContent) {
            //             $topBottom.html(r.bottomContent);
            //         }
            //     }
            // });
        }
    }

    function getDetail(cfg) {
        // 关联板式接口调用
        if(cfg.shopSwitch){
            var body = JSON.stringify({
            });
            var time = new Date().getTime()
            // 加固start
            var colorParm = {
                appid: 'item-v3',
                functionId: 'pc_structured',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                            getStructuredData(colorParm);
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getStructuredData(colorParm);
                        //烛龙上报
                        Tools.getJmfe(colorParm, e, "detail文件pc_structured接口设备指纹异常", 250)
                    }
                })
            }catch(e){
                colorParm['loginType'] = '3';
                colorParm['uuid'] = '';
                getStructuredData(colorParm);
                //烛龙上报
                Tools.getJmfe(colorParm, e, "detail文件pc_structured接口加固异常", 250)
            }            
            // 加固end
            function getStructuredData(colorParm){
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }
                colorParm['productId'] = cfg.mainSkuId || '',
                $.ajax({
                    url: host + '/structured',
                    data: colorParm,
                    dataType: 'json',
                    xhrFields: {
                        withCredentials: true,
                    },
                    success: function(r) {
                        var $topNew = $("#J-detail-pop-tpl-top-new");
                        var $topBottom = $("#J-detail-pop-tpl-bottom-new");
                        
                        if (r[0] && r[0].template && r[0].template.length>0) {
                            r[0].template.forEach(function(e){
                                if(e.moduleCode=="DA"){
                                    if(e.moduleNumber && e.moduleNumber.substring(0,1)==1){
                                        // $topNew.append("<div class='pcDA'></div>");
                                        $topNew.append(e.htmlContent);
                                    }else if(e.moduleNumber && e.moduleNumber.substring(0,1)==2){
                                        // $topBottom.append("<div class='pcDA'></div>");
                                        $topBottom.append(e.htmlContent);
                                    }
                                    // setTecentVideo(cfg,".pcDA")
                                    setTecentVideo(cfg,".J_formatDynamic")
                                }else{
                                    if(e.moduleNumber && e.moduleNumber.substring(0,1)==1 && e.htmlContent && e.htmlContent!=null){
                                        $topNew.append(e.cssContent+"<script>"+e.jsContent+"</script>"+e.htmlContent);
                                    }else if(e.moduleNumber && e.moduleNumber.substring(0,1)==2 && e.htmlContent && e.htmlContent!=null){
                                        $topBottom.append(e.cssContent+"<script>"+e.jsContent+"</script>"+e.htmlContent);
                                    }
                                }
                            })
                        }else{
                            getOld(cfg)
                        }
                    },
                    error:function(){
                        getOld(cfg)
                    }
                });
            }
        }else{
            getOld(cfg)
        }

        // 商品介绍接口调用
        desc(cfg)
        
    }

    function desc(cfg){
        if (/debug=disable_detail/.test(location.href)) {
            return false
        }
        if(cfg.desc && cfg.desc.indexOf("pc_description_channel") > 0){
            var body = JSON.stringify({
                "mainSkuId": G.serializeUrl(cfg.desc).param.mainSkuId || '',
                "skuId": G.serializeUrl(cfg.desc).param.skuId || ''
            });
            var time = new Date().getTime()
            // 加固start
            var colorParm = {
                appid: cfg.desc.indexOf("item-v3") > 0 ? 'item-v3' : 'jdw_pc', // 兼容全球购appid
                functionId: 'pc_description_channel',
                client: 'pc',
                clientVersion: '1.0.0',
                t: time,//生成当前时间毫秒数
                body: body,
            }
            try{
                var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                window.PSign.sign(colorParmSign).then(function(signedParams){
                    colorParm['h5st']  = encodeURI(signedParams.h5st)
                    try{
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                            getDescriptionData(colorParm);
                        }, 600);
                    }catch(e){
                        colorParm['loginType'] = '3';
                        colorParm['uuid'] = '';
                        getDescriptionData(colorParm);
                        //烛龙上报
                        Tools.getJmfe(colorParm, e, "detail文件pc_structured接口设备指纹异常", 250)
                    }
                })
            }catch(e){
                colorParm['loginType'] = '3';
                colorParm['uuid'] = '';
                getDescriptionData(colorParm);
                //烛龙上报
                Tools.getJmfe(colorParm, e, "detail文件pc_structured接口加固异常", 250)
            }            
            // 加固end
            
            function getDescriptionData(colorParm){
                var host = '//api.m.jd.com'
                if(pageConfig.product && pageConfig.product.colorApiDomain){
                    host = pageConfig.product && pageConfig.product.colorApiDomain
                }
                colorParm['mainSkuId'] = G.serializeUrl(cfg.desc).param.mainSkuId || ''
                colorParm['skuId'] = G.serializeUrl(cfg.desc).param.skuId || ''
                colorParm['charset'] = 'utf-8'
                colorParm['cdn'] = '2'
                $.ajax({
                    url:  host + "/description/channel",
                    data: colorParm,
                    dataType: 'json',
                    xhrFields: {
                        withCredentials: true,
                    },
                    success: function(r) {
                        var wrap = $('#J-detail-content')

                        if (cfg.isEBook) {
                            wrap.html(eBookTPL.process({ list: r }))
                            // 增加监听事件，当图片加载完毕之后再进行图片折叠
                            $('#illustration img').load(function() {
                                appendMoreBtn($('#illustration'))
                            })
                            appendMoreBtn(wrap.find('.formwork_bt'))
                        } else {
                            wrap.html(r.content.replace(/data-lazyload="done"/g, ''))
                            if (cfg.cat[0] == 1713) {
                                if ( cfg.pType === 3 || cfg.pType === 4 ) {
                                    bookReadMore.bookInit();
                                }
                            }
                        }

                        wrap.ELazyload({
                            source: 'data-lazyload'
                        })
                        // setDetailNav($('#detail'))
                    }
                })
            }
        }else if(cfg.desc){
            $.ajax({
                url: cfg.desc,
                dataType: 'jsonp',
                cache: true,
                jsonpCallback: 'showdesc',
                success: function(r) {
                    var wrap = $('#J-detail-content')

                    if (cfg.isEBook) {
                        wrap.html(eBookTPL.process({ list: r }))
                        // 增加监听事件，当图片加载完毕之后再进行图片折叠
                        $('#illustration img').load(function() {
                            appendMoreBtn($('#illustration'))
                        })
                        appendMoreBtn(wrap.find('.formwork_bt'))
                    } else {
                        wrap.html(r.content.replace(/data-lazyload="done"/g, ''))
                        if (cfg.cat[0] == 1713) {
                            if ( cfg.pType === 3 || cfg.pType === 4 ) {
                                bookReadMore.bookInit();
                            }
                        }
                    }

                    wrap.ELazyload({
                        source: 'data-lazyload'
                    })
                    // setDetailNav($('#detail'))
                }
            })
        }
    }

    function onPriceReady(data) {
        //拼购不显示其他价格
        if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && pageConfig.product.skuMarkJson && pageConfig.product.skuMarkJson.pg) return false;
        if (data.price) {
            var $addCartMini = $('.J-addcart-mini')
            var jp = parseFloat(data.price.p)
            var selector = '.J-p-'
            var sku = data.price.id.replace('J_', '')
            if (jp) {
                if (jp > 0) {
                    $addCartMini.find(selector + sku).html('￥' + data.price.p)
                } else {
                    $addCartMini.find(selector + sku).html('暂无报价')
                }
            }
        }
    }

    function stayPos($this) {
        var oTop = $this.parents('.formwork_bt').eq(0).offset().top
        if ($('body').scrollTop()) {
            $('body').scrollTop(oTop)
        } else {
            $('html').scrollTop(oTop)
        }
    }

    function appendMoreBtn($formworkBt) {
        $formworkBt.each(function(i) {
            var more = $(this).find('.more'), con = $(this).find('.con')

            if (more.length != 0 && con.length != 0) {
                var h
                if (con.height() > 440) {
                    h = 440
                    con.css({ height: h, overflow: 'hidden' })
                    more.show().toggle(
                        function() {
                            $(this).html(
                                '<a href="javascript:void(0)">收起全部↑</a>'
                            )
                            con.css({ height: 'auto', overflow: 'hidden' })
                            stayPos($(this))
                        },
                        function() {
                            $(this).html(
                                '<a href="javascript:void(0)">查看全部↓</a>'
                            )
                            con.css({ height: h, overflow: 'hidden' })
                            stayPos($(this))
                        }
                    )
                }
            }
        })
    }

    function setQualityLife(cfg) {
        var $ql = $('#quality-life')
        var imgMap = {
            '10': [
                '//img30.360buyimg.com/popshop/jfs/t2629/66/1341832506/243792/5e7d3e33/573c1169Nf355ce80.jpg',
                '//img30.360buyimg.com/popshop/jfs/t2875/2/1360087619/1270729/ff0fec44/573c1155N841cd68e.jpg',
                '//img30.360buyimg.com/popshop/jfs/t2617/247/1362535868/81405/51362531/573c0ffaNda15a3a1.jpg'
            ]
        }
        if (G.isJd) {
            imgMap[10] = [
                '//img30.360buyimg.com/poprx/s800x562_jfs/t2782/356/3137767914/5139488/a206435a/578317b1Na33c0ca9.jpg',
                '//img30.360buyimg.com/poprx/s800x562_jfs/t2797/286/3196750150/3418108/b81920dc/578317ccN045f8ee6.jpg',
                '//img30.360buyimg.com/poprx/s800x1100_jfs/t2977/314/1467566065/1044633/a5a43571/578317e2N2cc82a8e.jpg'
            ]
        }

        function getIframeUrl(callback) {
            $.ajax({
                url: '//cd.jd.com/quality',
                data: {
                    skuId: G.sku,
                    cat: G.cat.join(','),
                    brand: pageConfig.product.brand,
                    venderId: pageConfig.product.venderId || -1
                },
                dataType: 'jsonp',
                success: function(r) {
                    if (r && r.success && r.url) {
                        callback(r.url)
                    }
                }
            })
        }

        function setIframe() {
            var $qlIframe = $('.J-ql-iframe')
            var title = $qlIframe.attr('data-title')

            if ($qlIframe.length < 1) {
                return false
            }

            getIframeUrl(function() {
                $ql.show()
                $qlIframe.show()
            })

            $qlIframe.bind('click', function() {
                getIframeUrl(function(url) {
                    $qlIframe.dialog({
                        width: 300,
                        height: 300,
                        title: title,
                        type: 'iframe',
                        autoIframe: false,
                        source: url
                    })
                })
            })
        }

        function setIcon() {
            $.ajax({
                url: pageConfig.product.qualityLife,
                dataType: 'jsonp',
                success: function(r) {
                    showIcon(r)
                }
            })
        }
        // 二手售后保障
        function setErShouTab(img) {
            var tab = $('#detail').data('ETab')
            tab.triggers.eq(2).show()
            tab.items
                .eq(2)
                .html(
                    '<div style="padding: 10px 0" class="ac"><img src="' +
                        img +
                        '" /></div>'
                )
        }

        // 资质类型   1.其他资质   2.3C认证   3.吊牌  4.第三方资质  5.水洗标
        function showIcon(r) {
            var $icon = $ql.find('li')

            if (r && r.objs && r.objs.length) {
                for (var i = 0; i < r.objs.length; i++) {
                    var obj = r.objs[i]
                    if (obj.images && obj.images.length) {
                        imgMap[obj.type] = obj.images
                        $icon.filter('.ql-ico-' + obj.type).show()
                        if ($icon.filter('.ql-ico-' + obj.type).length) {
                            $ql.show()
                        }
                        if (obj.type === 6) {
                            setErShouTab(obj.images[0])
                        }
                    }
                }
            }
        }
        function popUp(t, text) {
            var images = imgMap[t]
            var html =
                '<p style="padding: 10px 0" class="ac">卖家承诺：以下为实物拍摄，购买时仅供参考</p>\
                    <div style="width: 520px;height:720px;margin: 0 auto;overflow:hidden;overflow-y:auto;">'

            if (!images || !images.length) return false

            for (var i = 0; i < images.length; i++) {
                var img = images[i]
                var src = /^http:|https:|\/\//.test(img)
                    ? img
                    : '//img20.360buyimg.com/cms/s500x2000_' + img

                html +=
                    '<img style="display:block" width="500" src="' +
                    src +
                    '" />'
            }
            html += '</div>'

            $('body').dialog({
                title: text,
                width: 600,
                height: 750,
                type: 'html',
                source: html
            })
        }
        function bindClick() {
            $ql.delegate('li', 'click', function() {
                var type = $(this).data('type')
                var text = $(this).data('text')

                // 质检承诺弹出iframe不弹图片
                if (type !== 1) {
                    popUp(type, text)
                }
            })
        }
        /* 品质溯源
        <li class="ql-ico-yuan" clstag="shangpin|keycount|product|pinzhisuyuan">
            <a href="http://jd.com/" target="_blank"><i></i><span>品质溯源</span></a>
        </li>
         */
        function showVideo() {
            var $videoIcon = $ql.find('.ql-ico-yuan')

            if ($videoIcon.length) $ql.show()
        }

        setIframe()
        bindClick()
        showVideo()

        if (pageConfig.product.qualityLife) {
            setIcon()
            if (pageConfig.product.shangjiazizhi) {
                $ql.show()
                $ql.find('.ql-ico-10').show()
            }
        }
    }

    // 家居家装 loc 地图
    function setMap(cfg) {
        var $el = $('#loc-map')

        if (!$el.length) {
            return false
        }
        function getProvinceName() {
            var ipLocation = readCookie('ipLocation')
            return ipLocation ? ipLocation : '%u5317%u4EAC'
        }
        function getProvinceId() {
            return cfg.decorationCurrentCity || Tools.getAreaId().areaIds[0]
        }
        function getUrl() {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            return (
                // '//cd.jd.com/store/template?' +
                host + '/store/template?'+
                $.param({
                    storeGroupId: cfg.storeGroupId || '',
                    venderId: cfg.venderId,
                    pName: getProvinceName(),
                    isNarrow: !(pageConfig.wideVersion &&
                        pageConfig.compatible),
                    provinceId: getProvinceId(),
                    appid: 'item-v3',
                    functionId: "pc_store_template"

                })
            )
        }
        var iframe =
            '<iframe id="loc-map-iframe" src="{0}" style="width:{1}px;height:{2}px" marginheight="0" frameborder="0" scrolling="no"></iframe>'
        var iHTML = iframe.format(
            getUrl(),
            pageConfig.wideVersion && pageConfig.compatible ? 990 : 750,
            526
        )
        $el.html(iHTML)

        Event.addListener('onAreaChange', function() {
            $('#loc-map-iframe').attr('src', getUrl())
        })
    }

    // 大家电异步加载售后保障
    function getJDContent(cfg, $item) {
        if (cfg.cat[0] !== 737) return

        $.ajax({
            url: '//dx.3.cn/afterSaleDesc/' + (cfg.mainSkuId || cfg.skuid),
            jsonpCallback: 'showAfterSaleDesc',
            dataType: 'jsonp',
            cache: true,
            success: function(r) {
                if (r && r.content) {
                    $item.html(r.content).ELazyload({
                        source: 'data-lazyload'
                    })
                }
            }
        })
    }

    function setBookComment(cfg) {
        var $eComment = $('#e-comment')

        if ($eComment.length && cfg.isEBook) {
            BookComment.init(cfg)
        }
    }

    // 生鲜溯源 详情视频
    function setVideo(cfg) {
        Event.addListener('onVideoData', function(d) {
            require.async(['MOD_ROOT/detail/video'], function(Video) {
                Video.init(d.data, cfg)
            })
        })
    }

    function setTecentVideo(cfg,r) {

        var $el = $('#detail').find(r)
        if (!$el.length || !cfg.imageAndVideoJson.infoVideoId) return false

        // var iframe = '<iframe id="tencent-video-iframe" src="{src}" frameborder="0" scrolling="no" style=""></iframe>'

        function setData(data) {
            if (data && data.playUrl) {
                cfg.tecentVideoData = data.playUrl
                // $el.html(iframe.replace('{src}', cfg.tecentVideoData))
                videoBox.getVideojs({callback:callback})
                function callback() {
                    var videoTPL =
                        '\
                    <video id="detail-video-player"\
                        class="video-js vjs-default-skin"\
                        poster="{1}"\
                        width="750"\
                        height="422"\
                        controls>\
                        <source src="{0}" type="video/mp4"> </source>\
                        <p class="vjs-no-js"> 您的浏览器不支持 HTML 5 Video 标签，请升级浏览器。</p>\
                    </video>\
                    '//<style>#detail-video-player{display:block;margin: 0 auto;}</style>

                    var vOptions = {
                        autoplay: false,
                        controls: true,
                        preload: 'auto'
                    }
                    $el.html(
                        videoTPL.format(
                            data.playUrl,
                            data.imageUrl||'misc.360buyimg.com/lib/img/e/blank.gif'
                        )
                    )
                    var player  = videojs('detail-video-player',vOptions)
                    player.on('ready', function() {
                        this.addClass('vjs-has-started');
                    });
                    player.on('error', function (r) {
                        if (typeof console !== 'undefined') {
                            console.info('Video loaded Error.')
                        }
                    })
                }
            }
        }
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        $.ajax({
            url: host + '/tencent/video_v2',
            dataType: 'jsonp',
            data: {
                vid: cfg.imageAndVideoJson.infoVideoId,
                type: 1,
                from: 1,
                // appid: 16,
                appid: 'item-v3',
                functionId: "pc_tencent_video_v2"
            },
            success: setData
        })
    }

    function init(cfg) {
        // Event.addListener('onPriceReady', onPriceReady)

        var $detail = $('#detail')

        $detail.ETab({
            onBeforeSwitch: function(n) {
                hideSwitch(this.triggers.eq(n))
            },
            onSwitch: function(n) {
                var $currTrigger = this.triggers.eq(n)
                var $currItem = this.items.eq(n)

                changeTo($currTrigger)
                if ($currTrigger.is('[data-anchor="#comment"]')) {
                    var lazyload = $('#comment').data('ELazyload')
                    $.each(lazyload, function(i, n) {
                        n.check(n.$targets)
                    })
                }
                if (
                    $currTrigger.is('[data-anchor="#shop-similar-promotion"]')
                ) {
                    var lazyload = $('#shop-similar-promotion').data(
                        'ELazyload'
                    )
                    $.each(lazyload, function(i, n) {
                        n.check(n.$targets)
                    })
                }
                if ($currTrigger.is('[data-anchor="#club"]')) {
                    var lazyload = $('#club').data('ELazyload')
                    $.each(lazyload, function(i, n) {
                        n.check(n.$targets)
                    })
                }
                // if ($currTrigger.is('[data-anchor="#e-comment"]')) {
                //     BookComment.init(cfg);
                // }

                if ($currTrigger.text() === '售后保障') {
                    getJDContent(cfg, $currItem)
                }
            }
        })


        // 切换到锚点tab的时候按顺序隐藏目标元素
        function hideSwitch($el) {
            var $targets = $(
                '#consult,#comment,#e-comment,#guarantee,#club,#try-report,#askAnswer'
            )
            $targets.show()

            if ($el.is('[data-anchor="#comment"]')) {
                $('#guarantee,#e-comment').hide()
            } else if ($el.is('[data-anchor="#club"]')) {
                $('#consult,#comment,#guarantee,#try-report').hide()
            } else if ($el.is('[data-anchor="#try-holder"]')) {
                $('#comment,#guarantee').hide()
            } else if ($el.is('[data-anchor="#shop-similar-promotion"]')) {
                $('#consult,#comment,#guarantee,#askAnswer').hide()
            } else {
                $targets.show()
            }
        }
        function changeTo($el) {
            var id = $el.attr('data-anchor')
            var $target = $(id)
            var isFloating = $('#detail .tab-main').hasClass(
                'pro-detail-hd-fixed'
            )
            function goToEle() {
                setTimeout(function() {
                    $('html,body').scrollTop($target.offset().top)
                }, 10)
            }

            if (!$target.length) {
                return false
            }

            if (isFloating) {
                goToEle()
            }
        }

        $('.J-more-param').bind('click', function() {
            $detail.data('ETab').go(1)
        })

        $detail.ELazyload({
            source: 'data-lazyload'
        })

        setBookComment(cfg)

        miniCart(cfg)

        getDetail(cfg)

        setFixedNav($detail)

        // setTecentVideo(cfg)

        setMap(cfg)

        setVideo(cfg)

        QualityLife.init(cfg)

    }

    module.exports.__id = 'detail'
    module.exports.init = init
})
