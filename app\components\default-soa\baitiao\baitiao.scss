@import '../common/lib';

.choose-baitiao {
    .item {
        position: relative;
        a {
            text-align: center;
        }
        .a_test{
            min-width: 130px;
            //width: 130px;
            height: 62px;
            border-radius: 6px;
            //border: 0.5px;
            background: #F7F8FC;
            font-size: 14px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            line-height: 22px;
            text-align: start;
            border: 1px solid #fff;
            color: #1A1A1A;
        }
    }
    .strong_test{
        //font-weight: 600;
        font-size: 16px;
        font-family: JDZhengHeiVRegular2-1;

    }
    .strong_tests{
        font-size: 14px;
        font-family: JDZhengHeiVRegular2-1;
    }
    .service-charge{
        width: 50px;
        height: 14px;
        background-color: #ff0f23;
        font-size: 10px;
        color: #fff !important;
        position: absolute;
        right: 0;
        top: -10px;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .hover .baitiao-tips {
        display: block;
    }
    .icon {
        @include icons(16px, 16px);
        margin-top: 8px;
        margin-right: 5px;
    }
    .prom {
        background-image: url(i/hui.png);
    }
    .question {
        background-image: url(i/question.png);
    }
}

.baitiao-list {
    float: left;
    strong {
        font-weight: normal;
    }
    span {
        color:#999;
        em {
            display: inline-block;
            *zoom: 1;
            padding:1px 2px;
            color:#fff;
            background:#fc1d3c;
        }

    }
    .disabled em {
        background:#ccc;
    }
    .baitiao-tips {
        position: absolute;
        z-index: 6;
        top: 32px;
        left: 0px;
        width: 250px;
        padding: 5px 10px;
        background:#fefff7;
        border: 1px solid #ccc;
        color: #666;
        ul {
            padding-bottom: 3px;
        }
        li {
            margin-top: 5px;
            line-height: 18px;
        }
    }
}
.bt-opens{
    margin-top: 4px;
    display: flex;
    align-items: center;
}
.bt-opens-span{
    //position: absolute;
    //top: 0;
    //left: 0;
    cursor: pointer;
    margin-left: 80px;
    font-size: $baseFontSize;
    color: #1a1a1a;
}
.bt-opens-span-rotate{
    transform: rotate(180deg)
}
.baitiao-disabled{
    display: none;
}

.baitiao-text-wrap {
    clear: both;
}

.baitiao-text {
    display: inline;
    clear: both;
    em {
        color:#999;
    }
    a {
        color:#005aa0;
        display: inline;
    }
}
.bt-info-tips {
    float: left;
    height: 39px;
    display: none;
}

.bt-dd{
    height: 70px;
    overflow: hidden;
}
.bt-dd-hidden{
    height: auto;
    overflow: hidden;
}
