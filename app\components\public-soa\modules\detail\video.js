define('PUBLIC_ROOT/modules/detail/video', function(require, exports, module) {
    // require('//static.360buyimg.com/item/assets/sewise-player/player/sewise.player.min')
    require('PUBLIC_ROOT/plugins/jQuery.imgScroll')

    //获取视频
    var videoBox = require('PUBLIC_ROOT/modules/videoBox/videoBox')

    function SuyuanVideo(data, cfg) {
        this.cfg = cfg
        this.init(data)
    }

    SuyuanVideo.prototype = {
        init: function(data) {
            var _this = this
            // 去掉播放按钮，在suyuan-video-player-introduce这个div下
            // <a class="play-btn J-play-btn" href="#none" target="_self" clstag="shangpin|keycount|product|suyuanshipin1"></a>
            _this.template =
                '\
            <div class="detail-video-con">\
                <div class="video-wrap">\
                    <div id="suyuan-video-player-con" class="video-player J-video-player" data-index="0"></div>\
                    <div id="suyuan-video-player-introduce" class="video-introduce">\
                        <img src="${videos[0].introduce}" width="750" height="422" />\
                    </div>\
                </div>\
                {if videos.length>1}\
                <div class="video-list J-video-list">\
                    {if videos.length>3}\
                    <a href="#none" class="list-prev ui-switchable-prev"><i class="sprite-prev"></i></a>\
                    <a href="#none" class="list-next ui-switchable-next"><i class="sprite-next"></i></a>\
                    {/if}\
                    <ul class="ui-switchable-panel-main J-video-ul">\
                        {for video in videos}\
                        <li class="play ui-switchable-panel" data-type="video" data-index="${video_index}" clstag="shangpin|keycount|product|suyuanshipin${parseInt(video_index)+1}">\
                            <a href="#none">\
                                {if video.type=="alive"}\
                                <span class="tips live">溯源直播</span>\
                                {elseif video.type=="on_command"}\
                                <span class="tips playback">溯源回放</span>\
                                {/if}\
                                <span class="mask"></span>\
                                <i class="video-play"></i>\
                                <img src="//img11.360buyimg.com/n1/s182x111_${video.thumbnail}" alt="${video.name}" width="182" height="111" />\
                                <span class="title">${video.name}</span>\
                            </a>\
                        </li>\
                        {/for}\
                    </ul>\
                </div>\
                {/if}\
            </div>'

            _this.$suyuanVideo = $('#suyuan-video')

            if (!data.videos){
                return false;
            }

            //切换图片尺寸 n1/s755x422_
            for(var i=0;i<data.videos.length;i++){
                data.videos[i].introduce = data.videos[i].introduce.replace(/\/sy\/|\/live\//,'/n1/s755x422_');
            }
            _this.data = data
            _this.videos = data.videos

            // 去除iframe，应用videoBox，将介绍视频也加入列表中
            if (_this.cfg.tecentVideoData) {
                _this.videos.unshift({name:'商品介绍',introduce:'//img11.360buyimg.com/n1/s750x422_'+_this.data.adThumbnail,link:_this.cfg.tecentVideoData,thumbnail:_this.data.adThumbnail,position:0})
            }
            _this.videos.sort(function(a, b) {
                return parseInt(a.position) - parseInt(b.position)
            })

            _this.$suyuanVideo.html(_this.template.process(data))
            if (_this.videos && _this.videos.length) {
                //将详情介绍播放器拿过来，防止报错
                _this.$suyuanVideo.find('#suyuan-video-player-con').html( $('#tencent-video').remove())
            }

            _this.bindEvent()

            var $list = _this.$suyuanVideo.find('.J-video-list')
            var $prev = $list.find('.list-prev')
            var $next = $list.find('.list-next')

            $list.imgScroll({
                width: 189,
                height: 144,
                visible: 4,
                step: 4,
                showControl: true,
                prev: $prev,
                next: $next
            })
        },
        bindEvent: function() {
            var _this = this
            _this.$suyuanVideo.delegate(
                'li[data-type="video"]',
                'click',
                function() {
                    var $this = $(this)
                    var index = $this.attr('data-index')
                    
                    if($(this).hasClass('playing')){
                        return;
                    }
					_this.disposePlayer()
                    _this.initPlayer(index)
                }
            )

            _this.$suyuanVideo.delegate(
                'li[data-type="video"]',
                'mouseenter',
                function() {
                    var $this = $(this)
                    var index = $this.attr('data-index')
                    _this.initIntroduce(index)
                }
            ).delegate(
                'li[data-type="video"]',
                'mouseleave',
                function() {
                    var $suyuanVideo = _this.$suyuanVideo.find(
                        '#suyuan-video-player-introduce'
                    )
                    var $playerCon = _this.$suyuanVideo.find(
                        '#suyuan-video-player-con'
                    )
                    if($playerCon.html().length > 0){
                        $suyuanVideo.html('')
                        $suyuanVideo.hide()
                    }
                }
            )
        },
        initIntroduce: function(index) {
            var _this = this

            var template = '<img src="${introduce}" width="750" height="422" />'
            var $suyuanVideo = _this.$suyuanVideo.find(
                '#suyuan-video-player-introduce'
            )
            $suyuanVideo.show()
            $suyuanVideo.attr('data-index', index)
            $suyuanVideo.html(template.process(_this.videos[index]))
        },
        disposePlayer:function () {
            //移除播放器
            if((typeof videojs != 'undefined') && !!videojs.players['detail-video-player']){
                videojs.players['detail-video-player'].dispose();
            }
            if((typeof videojs != 'undefined') && !!videojs.players['videojs-player-suyuan']){
                videojs.players['videojs-player-suyuan'].dispose();
            }
        },
        initPlayer: function(index) {
            var _this = this

            var playerCon = _this.$suyuanVideo.find('#suyuan-video-player-con')
            playerCon.empty()

            var $suyuanVideo = _this.$suyuanVideo.find(
                '#suyuan-video-player-introduce'
            )
            $suyuanVideo.html('')
            $suyuanVideo.hide()

            var $list = _this.$suyuanVideo.find('.J-video-list')
            $list.find('li').removeClass('playing');
            $list.find('li[data-type=video]').eq(index).addClass('playing')

 			videoBox.getVideojs({callback:callback})
            function callback() {
                var vOptions = {
                    autoplay: true,
                    controls: true,
                    preload: 'auto',
                    inactivityTimeout: 1000,
                }
                $('#suyuan-video-player-con').html('');
                // display:block; width:750px; height:422px; margin:0 auto;
                var html = '<video id="videojs-player-suyuan" class="video-js vjs-default-skin" controls width="750px" height="422px" style="display:block; width:750px; height:422px; margin:0 auto;"></video>';
                $('#suyuan-video-player-con').html(html);
                var player = videojs('videojs-player-suyuan',vOptions);
                player.on('ready', function() {
                    this.addClass('vjs-has-started');
                });
                player.src(videoBox.setVideoType(_this.videos[index].link));
                player.play()
            }
        }
    }

    function init(data, cfg) {
        new SuyuanVideo(data, cfg)
    }

    exports.init = init
})
