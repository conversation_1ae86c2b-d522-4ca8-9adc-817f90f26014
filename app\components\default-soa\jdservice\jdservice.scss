@import "../common/lib";
@import "./__sprite";
#choose-service {
    margin-bottom: 18px;
    line-height: 18px;
    // .dt {
    //     line-height: 30px;
    // }

    .dd {
        *zoom: 1;
    }

    .service-type-yb-zz {
        position: relative;
        .yb-item-cat {
            // float: left;
            position: relative;
            //white-space: nowrap;
            _border: none;
            .yb-item {
                padding: 0px 5px 4px 6px;
                margin: 0 7px 2px 0;
                // border: 1px solid #ccc;
                line-height: 20px;
                position: relative;
                font-size: 0;
                display: flex;
                align-items: center;
                padding-left: 0;
                & .after {
                    position: absolute;
                    left: 0;
                    bottom: -1px;
                    z-index: -1;
                    width: 100%;
                    height: 1px;
                    content: '';
                    background-color: #fff;
                }
                &:hover{
                    // border: 1px solid #e3393c;
                }
                .icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 4px;
                    display: inline-block;
                    *display: inline;
                    *zoom: 1;
                }
                .name {
                    font-size: 15px;
                    margin-right: 8px;
                    vertical-align: middle;
                    // max-width: 60px;
                    max-width: 10em;
                    width: 60px\9;
                    *width: 60px;
                    height: 20px;
                    overflow: hidden;
                    display: inline-block;
                    *display: inline;
                    *zoom: 1;
                    color:rgba(26, 26, 26, 1);
                    font-family: "PingFang SC";
                }
                .name-tip{
                    font-size: 15px;
                    font-weight: 400;
                    line-height: 15px;
                    text-align: left;
                    text-underline-position: from-font;
                    text-decoration-skip-ink: none;
                    color: #888B94;
                }
                .price {
                    font-size: 12px;
                    margin-right: 5px;
                    vertical-align: middle;
                    font: 12px/150% Arial,Verdana,"\5b8b\4f53";
                }
                .arrow-icon {
                    width: 15px;
                    height: 10px;
                    display: inline-block;
                    *display: inline;
                    *zoom: 1;
                    vertical-align: middle;
                    background: url(i/service-icon.png) -2px -1px;
                    _background: url(i/service-icon-8.png) -2px -1px;
                }
            }
            .more-jd-item{
                display: flex;
                flex-wrap: wrap;
                margin-top: 6px;

                    .more-item {
                        // display: none;
                        // position: absolute;
                        top: 29px;
                        background-color: #F7F8Fc;
                        cursor: pointer;
                        border: 0.5px solid #F7F8Fc;
                        //padding: 10px 0 5px 10px;
                        padding: 0 12px;
                        min-width: 146px;
                        overflow: hidden;
                        border-radius: 6px;
                        //z-index: 1;
                        margin-right: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 38px;
                        margin-bottom: 12px;
                        color: #1A1A1A;

                        &:hover {
                            color: #ff0f23;
                            border: 0.5px solid #FF0F23;
                            background-color: #FFEBF1;
                        }

                        &.selected {
                            color: #ff0f23;
                            border: 0.5px solid #FF0F23;
                            background-color: #FFEBF1;
                        }

                        ul li {
                            //margin-bottom: 10px;
                            .title {
                                //height: 16px;
                                a {
                                    _border: none;
                                }

                                .sprite-checkbox {
                                    display: inline-block;
                                    vertical-align: -3px;
                                    margin-right: 6px;
                                    @include sprite-checkbox;
                                }

                                .detail-more {
                                    display: none;
                                    margin-left: 10px;
                                    color: #5e69ad;

                                    &:hover {
                                        color: #ff0f23;
                                    }
                                }

                                .tips {
                                    display: inline-block;
                                    padding: 0 4px;
                                    margin-right: 6px;
                                    background: #f19c9d;
                                    color: #fff;
                                }

                                .name {
                                    display: inline-block;
                                    // max-width:60px;
                                    max-width: 10em;
                                    *width: 60px;
                                    height: 16px;
                                    vertical-align: -3px;
                                    overflow: hidden;
                                    font-size: 15px;
                                    font-family: "PingFang SC";
                                    //color: rgba(26, 26, 26, 1);
                                }

                                .price {
                                    margin-right: 0;
                                    margin-left: 6px;
                                    font-size: 15px;
                                    font-family: JDZhengHeiVRegular2-1;
                                    //color:rgba(26, 26, 26, 1);
                                }

                                .hui-icon {
                                    width: 16px;
                                    height: 16px;
                                    display: inline-block;
                                    *display: inline;
                                    *zoom: 1;
                                    vertical-align: middle;
                                    background: url(i/service-icon.png) -61px -3px;
                                    _background: url(i/service-icon-8.png) -61px -3px;
                                }

                                .cancel-btn {
                                    margin-left: 10px;
                                    display: none;
                                    _border: none;
                                }
                            }

                            .title:hover, .title:hover label {
                                color: #ff0f23;
                                cursor: pointer;
                            }

                            .title:hover .detail-more {
                                display: inline-block;
                            }

                            .cdetail {
                                width: 240px;
                                height: 16px;
                                overflow: hidden;
                                color: #999;
                            }

                            &.selected {
                                _border: none;
                                color: #E4393C;

                                .title {
                                    .sprite-checkbox {
                                        @include sprite-checked;
                                    }
                                }

                                .choose-btn {
                                    _border: none;
                                    color: #E4393C;
                                }

                                .cancel-btn {
                                    display: inline;
                                }
                            }
                        }

                        .btns {
                            margin-top: -5px;

                            .more-btn {
                                color: #005AA0;
                                float: right;
                                *zoom: 1;
                                _border: none;

                                .s-arrow {
                                    font: normal 8px simsun;
                                    text-decoration: none;
                                    -webkit-text-size-adjust: none;
                                }
                            }
                        }

                        &.hover {
                            color: #ff0f23;
                        }
                    }
            }
            &.selected {
                .yb-item {
                    //border: 1px solid #E3393C;
                }
            }

            &.hover {
                .yb-item .after {
                    z-index: 2;
                }
                ul li {
                    width: auto;
                    _border: none;
                }
                .yb-item {
                    border-bottom-color: #FFF;
                }
                .arrow-icon {
                    background-position: -2px -38px;
                }
                .more-item {
                    display: block;
                }
            }
        }
        &.hover{
            z-index:3;
        }
    }
    .service-tips{
        position: relative;
        float: left;
        display: none;
    }
    .sprite-question{
        display: inline-block;
        vertical-align: -10px;
        @include sprite-question;
    }
    .service-tips .tips {
        z-index: 2;
        width: 270px;
        position: absolute;
        left: -215px;
        top: 35px;
        display: none;

        .content {
            padding: 10px;
            background: #fff;
            border: 1px solid #cecbce;
            color: #666;
            -moz-box-shadow: 0 0 2px 2px #eee;
            -webkit-box-shadow: 0 0 2px 2px #eee;
            box-shadow: 0 0 2px 2px #eee;
            dt{
                font-weight: bold;
                margin-bottom: 3px;
            }
            dd{
                line-height: 170%;
            }
            p{
                border-top: 1px dotted #999;
                margin-top: 7px;
                padding-top: 7px;
                a{
                    color: #5e69ad;
                    margin: 0 5px;
                    &:hover{
                        color: #ff0f23;
                    }
                }
            }
        }
        .sprite-arrow {
            @include sprite-arrow;
            position: absolute;
            overflow: hidden;
            left: 218px;
            top: -5px;
            _bottom: -1px;
        }
    }
    .hover .tips{
        display: block;
    }
}
/*二手*/
.ershou{
    #choose-service{
        .service-type-yb-zz{
            .yb-item-cat{
                .yb-item:hover{
                    border: 1px solid $baseColorErshou;
                }
            }
            .yb-item-cat.selected{
                .yb-item{
                    border: 1px solid $baseColorErshou;
                }
            }
        }
    }
}
.btn-open{
    //padding-top: 12px;
    cursor: pointer;
    font-size: 15px;
    color: #1A1A1A;
    //margin-left: 40px;
}
.bt-open-span-rotate{
    transform: rotate(180deg)
}

.btn-open-div{
    display: flex;
    align-items: center;
}