define('MOD_ROOT/videoBox/videoBox', function(require, exports, module) {
    require('./modernizr.min.js')

    //加载播放器类型
    //每次上线更改版本号时，记着修改这个
    //目的：不让播放器依赖seajs等文件，拿来即用
    var path = '//newbuz.360buyimg.com/video/4.2/';
    var html5 = [path + 'video.hls.min.js',path+'video-js.min.css'];
    // var flash = [path + 'videojs-flashls/video.js,videojs-flashls/videojs.flashls.js',path + 'videojs-flashls/video-js.css'];
    var flash = [path + 'video.min.js',path + 'video-js.min.css'];
    var green = path + 'video-js.green.min.css';
    var swf = path + 'video-js.swf';
    var ie8 = path + 'ie8/videojs-ie8.min.js'
    var options = {
        type: 'html5',//flash，html5
        videoType:'rtmp,flv,m3u8,mp4',
        qulitySelectMenu:false,
        playBackRatesMenu:false,
        language:'zh-CN',//none:没有语言
        customzie:'common',//none：则不加载css，common||'':加载默认,green：加载绿色主题
        callback:function () {},
        callBackList:[]
    }
    var count = 0;
    /**
     *添加js
     * @param url
     * @param callback
     */
    function prependHtml(url,callback) {
        if(options.callBackList.length >= 2) return;
        if(typeof url =='object' && url instanceof Array){
            for(var i = 0;i<url.length;i++){
                createHtml(url[i])
            }
        }else{
            createHtml(url)
        }
        function createHtml(url) {
            url = url || '';
            if(url.indexOf('.js') > 0){
                loadScript(url,callback)
            }else if(url.indexOf('.css') > 0){
                if(options.customzie == 'none') return;
                var style = document.createElement('link');
                if(options.customzie == 'green'){
                    url = green;
                }
                style.href = url;
                style.rel = 'stylesheet';
                style.type = 'text/css';
                document.getElementsByTagName('head')[0].appendChild(style);
            }
        }

    }
    function loadScript(url, callback) {
        var heads = document.getElementsByTagName('head');
        if (heads.length == 0) {
            alert("page must have one head element");
        }
        var head = heads[0];
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
// most browsers
        script.onload = function (ev) {
            if(url && url.indexOf('ie8') >= 0) return;
            (options.language == 'zh-CN') &&supportCN()
            callback()
        }

// IE 6 & 7
        script.onreadystatechange = function () {
            if (this.readyState == 'complete' || this.readyState  == 'loaded') {
                if(url && url.indexOf('ie8') >= 0) return;

                (options.language == 'zh-CN') &&supportCN()
                callback();
            }
        }
        head.appendChild(script);
    }
    /**
     * 判断浏览器版本
     * @returns {*}
     * @constructor
     */
    function BrowserType(){
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isOpera = userAgent.indexOf("Opera") > -1; //判断是否Opera浏览器
        var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //判断是否IE浏览器
        var isEdge = userAgent.toLowerCase().indexOf("edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        var isIE11 = (userAgent.toLowerCase().indexOf("trident") > -1 && userAgent.indexOf("rv") > -1);

        var isFF = userAgent.indexOf("Firefox") > -1; //判断是否Firefox浏览器
        var isSafari = userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") == -1; //判断是否Safari浏览器
        var isChrome = userAgent.indexOf("Chrome") > -1 && userAgent.indexOf("Safari") > -1; //判断Chrome浏览器
        var isWechat = userAgent.indexOf("MicroMessenger") > -1 ; //判断是否微信

        if (isEdge) { return "Edge";}
        if (isIE11) { return "IE11";}

        if (isIE)
        {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if(fIEVersion == 7)
            { return "IE7";}
            else if(fIEVersion == 8)
            { return "IE8";}
            else if(fIEVersion == 9)
            { return "IE9";}
            else if(fIEVersion == 10)
            { return "IE10";}
            else if(fIEVersion == 11)
            { return "IE11";}
            else
            { return "0"}//IE版本过低
        }//isIE end

        if (isWechat) { return "Wechat";}
        if (isFF) {  return "FF";}
        if (isOpera) {  return "Opera";}
        if (isSafari) {  return "Safari";}
        if (isChrome) { return "Chrome";}
        return "0";
    }//myBrowser() end

    /**
     * 获取播放器类型flash or html5
     * @param url 视频地址
     * @param callback  回调
     */
    function getVideojs(obj) {

        obj = obj || {};
        options.type = obj.type || 'html5';
        options.videoType = obj.videoType || 'mp4';
        options.customzie = obj.customzie || 'common';
        options.language = obj.language || 'zh-CN';
        options.callback = obj.callback || function () {};
        if(typeof videojs != "undefined"){
            options.callback();
        }else{
            options.callBackList.push(obj.callback);
            var videoType = getVideoType(options.videoType);
            var playType = getPlayType(videoType);
            setVideojs(playType,function (){
                if(count === 0){
                    videojsOption(playType);
                }
                count++;
                // options.callback()
                for (var i = 0;i<options.callBackList.length;i++){
                    typeof options.callBackList[i] == 'function' && options.callBackList[i]();
                }
                options.callBackList = [];
            });
        }
    }
    options.a = 'JDVISEC';
    /*
  * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message
  * Digest Algorithm, as defined in RFC 1321.
  * Version 2.2 Copyright (C) Paul Johnston 1999 - 2009
  * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet
  * Distributed under the BSD License
  * See http://pajhome.org.uk/crypt/md5 for more info.
  */
    var hexcase=0;function hex_md5(a){return rstr2hex(rstr_md5(str2rstr_utf8(a)))}function hex_hmac_md5(a,b){return rstr2hex(rstr_hmac_md5(str2rstr_utf8(a),str2rstr_utf8(b)))}function md5_vm_test(){return hex_md5("abc").toLowerCase()=="900150983cd24fb0d6963f7d28e17f72"}function rstr_md5(a){return binl2rstr(binl_md5(rstr2binl(a),a.length*8))}function rstr_hmac_md5(c,f){var e=rstr2binl(c);if(e.length>16){e=binl_md5(e,c.length*8)}var a=Array(16),d=Array(16);for(var b=0;b<16;b++){a[b]=e[b]^909522486;d[b]=e[b]^**********}var g=binl_md5(a.concat(rstr2binl(f)),512+f.length*8);return binl2rstr(binl_md5(d.concat(g),512+128))}function rstr2hex(c){try{hexcase}catch(g){hexcase=0}var f=hexcase?"0123456789ABCDEF":"0123456789abcdef";var b="";var a;for(var d=0;d<c.length;d++){a=c.charCodeAt(d);b+=f.charAt((a>>>4)&15)+f.charAt(a&15)}return b}function str2rstr_utf8(c){var b="";var d=-1;var a,e;while(++d<c.length){a=c.charCodeAt(d);e=d+1<c.length?c.charCodeAt(d+1):0;if(55296<=a&&a<=56319&&56320<=e&&e<=57343){a=65536+((a&1023)<<10)+(e&1023);d++}if(a<=127){b+=String.fromCharCode(a)}else{if(a<=2047){b+=String.fromCharCode(192|((a>>>6)&31),128|(a&63))}else{if(a<=65535){b+=String.fromCharCode(224|((a>>>12)&15),128|((a>>>6)&63),128|(a&63))}else{if(a<=2097151){b+=String.fromCharCode(240|((a>>>18)&7),128|((a>>>12)&63),128|((a>>>6)&63),128|(a&63))}}}}}return b}function rstr2binl(b){var a=Array(b.length>>2);for(var c=0;c<a.length;c++){a[c]=0}for(var c=0;c<b.length*8;c+=8){a[c>>5]|=(b.charCodeAt(c/8)&255)<<(c%32)}return a}function binl2rstr(b){var a="";for(var c=0;c<b.length*32;c+=8){a+=String.fromCharCode((b[c>>5]>>>(c%32))&255)}return a}function binl_md5(p,k){p[k>>5]|=128<<((k)%32);p[(((k+64)>>>9)<<4)+14]=k;var o=1732584193;var n=-271733879;var m=-1732584194;var l=271733878;for(var g=0;g<p.length;g+=16){var j=o;var h=n;var f=m;var e=l;o=md5_ff(o,n,m,l,p[g+0],7,-680876936);l=md5_ff(l,o,n,m,p[g+1],12,-389564586);m=md5_ff(m,l,o,n,p[g+2],17,606105819);n=md5_ff(n,m,l,o,p[g+3],22,-1044525330);o=md5_ff(o,n,m,l,p[g+4],7,-176418897);l=md5_ff(l,o,n,m,p[g+5],12,1200080426);m=md5_ff(m,l,o,n,p[g+6],17,-1473231341);n=md5_ff(n,m,l,o,p[g+7],22,-45705983);o=md5_ff(o,n,m,l,p[g+8],7,1770035416);l=md5_ff(l,o,n,m,p[g+9],12,-1958414417);m=md5_ff(m,l,o,n,p[g+10],17,-42063);n=md5_ff(n,m,l,o,p[g+11],22,-1990404162);o=md5_ff(o,n,m,l,p[g+12],7,1804603682);l=md5_ff(l,o,n,m,p[g+13],12,-40341101);m=md5_ff(m,l,o,n,p[g+14],17,-1502002290);n=md5_ff(n,m,l,o,p[g+15],22,1236535329);o=md5_gg(o,n,m,l,p[g+1],5,-165796510);l=md5_gg(l,o,n,m,p[g+6],9,-1069501632);m=md5_gg(m,l,o,n,p[g+11],14,643717713);n=md5_gg(n,m,l,o,p[g+0],20,-373897302);o=md5_gg(o,n,m,l,p[g+5],5,-701558691);l=md5_gg(l,o,n,m,p[g+10],9,38016083);m=md5_gg(m,l,o,n,p[g+15],14,-660478335);n=md5_gg(n,m,l,o,p[g+4],20,-405537848);o=md5_gg(o,n,m,l,p[g+9],5,568446438);l=md5_gg(l,o,n,m,p[g+14],9,-1019803690);m=md5_gg(m,l,o,n,p[g+3],14,-187363961);n=md5_gg(n,m,l,o,p[g+8],20,1163531501);o=md5_gg(o,n,m,l,p[g+13],5,-1444681467);l=md5_gg(l,o,n,m,p[g+2],9,-51403784);m=md5_gg(m,l,o,n,p[g+7],14,1735328473);n=md5_gg(n,m,l,o,p[g+12],20,-1926607734);o=md5_hh(o,n,m,l,p[g+5],4,-378558);l=md5_hh(l,o,n,m,p[g+8],11,-2022574463);m=md5_hh(m,l,o,n,p[g+11],16,1839030562);n=md5_hh(n,m,l,o,p[g+14],23,-35309556);o=md5_hh(o,n,m,l,p[g+1],4,-1530992060);l=md5_hh(l,o,n,m,p[g+4],11,1272893353);m=md5_hh(m,l,o,n,p[g+7],16,-155497632);n=md5_hh(n,m,l,o,p[g+10],23,-1094730640);o=md5_hh(o,n,m,l,p[g+13],4,681279174);l=md5_hh(l,o,n,m,p[g+0],11,-358537222);m=md5_hh(m,l,o,n,p[g+3],16,-722521979);n=md5_hh(n,m,l,o,p[g+6],23,76029189);o=md5_hh(o,n,m,l,p[g+9],4,-640364487);l=md5_hh(l,o,n,m,p[g+12],11,-421815835);m=md5_hh(m,l,o,n,p[g+15],16,530742520);n=md5_hh(n,m,l,o,p[g+2],23,-995338651);o=md5_ii(o,n,m,l,p[g+0],6,-198630844);l=md5_ii(l,o,n,m,p[g+7],10,1126891415);m=md5_ii(m,l,o,n,p[g+14],15,-1416354905);n=md5_ii(n,m,l,o,p[g+5],21,-57434055);o=md5_ii(o,n,m,l,p[g+12],6,1700485571);l=md5_ii(l,o,n,m,p[g+3],10,-1894986606);m=md5_ii(m,l,o,n,p[g+10],15,-1051523);n=md5_ii(n,m,l,o,p[g+1],21,-2054922799);o=md5_ii(o,n,m,l,p[g+8],6,1873313359);l=md5_ii(l,o,n,m,p[g+15],10,-30611744);m=md5_ii(m,l,o,n,p[g+6],15,-1560198380);n=md5_ii(n,m,l,o,p[g+13],21,1309151649);o=md5_ii(o,n,m,l,p[g+4],6,-145523070);l=md5_ii(l,o,n,m,p[g+11],10,-1120210379);m=md5_ii(m,l,o,n,p[g+2],15,718787259);n=md5_ii(n,m,l,o,p[g+9],21,-343485551);o=safe_add(o,j);n=safe_add(n,h);m=safe_add(m,f);l=safe_add(l,e)}return Array(o,n,m,l)}function md5_cmn(h,e,d,c,g,f){return safe_add(bit_rol(safe_add(safe_add(e,h),safe_add(c,f)),g),d)}function md5_ff(g,f,k,j,e,i,h){return md5_cmn((f&k)|((~f)&j),g,f,e,i,h)}function md5_gg(g,f,k,j,e,i,h){return md5_cmn((f&j)|(k&(~j)),g,f,e,i,h)}function md5_hh(g,f,k,j,e,i,h){return md5_cmn(f^k^j,g,f,e,i,h)}function md5_ii(g,f,k,j,e,i,h){return md5_cmn(k^(f|(~j)),g,f,e,i,h)}function safe_add(a,d){var c=(a&65535)+(d&65535);var b=(a>>16)+(d>>16)+(c>>16);return(b<<16)|(c&65535)}function bit_rol(a,b){return(a<<b)|(a>>>(32-b))};

    function videojsOption(playType) {
        videojs.options.flash.swf = swf;
        if(playType == 'html5'){
            videojs.options.techOrder = ['html5', 'flvjs', 'flash'];;
        }else {
            if(!isSupportFlash()){
                var flashDiv = document.createElement('div');
                flashDiv.id = 'no-flash'
                var html = '<div style="width:100%;height:60px;position: absolute;top:0;left:0;line-height: 60px;font-size:14px;text-align: center;background: #c4636f;color:#fff;z-index: 9999"><a href="https://helpx.adobe.com/cn/flash-player/kb/installation-problems-flash-player-windows.html#" target="_blank">播放器无法正常运行，要想获得完整体验，建议您更新或者开启Flash，点击该链接获得支持！</a></div>'
                flashDiv.innerHTML = html;
                document.body.appendChild(flashDiv);

            }
            videojs.options.techOrder = ['flash', 'html5'];
        }
        if(options.customzie != 'green') return;
        videojs.options.children = {
            controlBar:{
                children:{
                    progressControl:true,
                    playToggle:true,
                    currentTimeDisplay:true,
                    timeDivider:true,
                    durationDisplay:true,
                    liveDisplay:true,
                    customControlSpacer:true,
                    playbackRateMenuButton:options.playBackRatesMenu,
                    volumeMenuButton: {
                        inline: false
                    },
                    fullscreenToggle: true,
                    textTracks:false,
                }
            },
            mediaLoader:true,
            posterImage:true,
            textTrackDisplay:true,
            loadingSpinner:true,
            errorDisplay:true,
            textTrackSettings:true,
            bigPlayButton:true
        }
    }
    /**
     *选择播放器类型
     * @param videoType 视频格式
     * @returns {string} 播放器类型 flash or html5
     */
    function getPlayType(videoType){
        var browser = BrowserType();
        var val = 'html5';
        if((videoType == 'm3u8' || videoType == 'flv') && (browser == 'IE11' || browser == 'IE10'||browser == 'IE9' || browser == 'IE8')){
            val = 'flash'
        }
        // 暴力些，ie系列兼容解决
        if(browser == 'IE8' || videoType == 'rtmp'){
            val = 'flash'
        }
        if(browser == 'IE8' ){
            loadScript(ie8, function () {})
        }
        //微信强制使用h5
        if(browser == 'Wechat'){
            val = 'html5';
        }
        options.type = val;
        return val;
    }
    /**
     * 加载播放器
     * @param playType 播放器类型
     * @param callback 播放器加载后回调,可以使用videojs的函数
     */
    function setVideojs(playType,callback) {
        var videoUrl = html5;
        if(playType == 'flash'){
            videoUrl = flash;
        }

        if(typeof videojs != "undefined"){
            callback();
        }else{
            prependHtml(videoUrl,callback);
        }
    }

    /**
     *获取视频格式
     * @param url 视频地址
     * @returns {*} 视频格式mp4、m3u8
     */
    function getVideoType(url) {
        if(!url) return ;
        if(url.indexOf('rtmp')>=0) return 'rtmp';
        if(url.indexOf('flv')>=0) return 'flv';
        if(url.indexOf('m3u8')>=0) return 'm3u8';
        if(url.indexOf('mp4')>=0) return 'mp4';
    }

    /**
     * 设置videojs需要的视频格式
     * @param url 视频地址
     */
    function setVideoType(url) {

        //协议一致
        var  protocol = document.location.protocol;
        url = url.replace(/^https?:/,protocol);

        var videoType = getVideoType(url);
        // var playType = getPlayType(videoType);
        var playType = options.type;
        if(playType == 'html5'){
            if(videoType == 'm3u8'){
                return {
                    src:url,
                    type:"application/x-mpegURL"
                }
            }else if(videoType == 'mp4'){
                return {
                    src:url,
                    type:'video/mp4'
                }
            }else if(videoType == 'rtmp'){
                return {
                    src:url,
                    type:'rtmp/flv'
                }
            }else if(videoType == 'flv'){
                return {
                    src:url,
                    type:'video/x-flv'
                }
            }
        }else if(playType == 'flash'){
            return url;
        }

    }

    /**
     * 销毁播放器实例
     * @param playerId
     */
    function disposePlayer(playerId) {
        if((typeof videojs != "undefined") && !!videojs.players[playerId]){
            videojs.players[playerId].dispose()
        }
    }

    /**
     * 校验是否支持flash
     *
     */
    function isSupportFlash() {
        if(typeof window.ActiveXObject != "undefined"){
            return (new ActiveXObject("ShockwaveFlash.ShockwaveFlash")) && true;
        }else{
            return navigator.plugins['Shockwave Flash'] && true;
        }
    }

    var videoBox = {
        options:options,
        getVideojs:getVideojs,
        setVideoType:setVideoType,
        disposePlayer:disposePlayer,
        getBrowserType:BrowserType
    }
    //清晰度选择插件
    // 一些通用的方法
    var tools = {
        // 添加class
        addClass: function (eleList, cls){
            eleList = Array.prototype.slice.apply(eleList);
            var add = function (obj) {
                var obj_class = obj.className; //获取 class 内容.
                var blank = (obj_class != '') ? ' ' : '';//判断获取到的 class 是否为空, 如果不为空在前面加个'空格'.
                var added = obj_class + blank + cls;//组合原来的 class 和需要添加的 class.
                obj.className = added;//替换原来的 class.
            }
            if (eleList instanceof Array) {
                for (var i=0; i<eleList.length; i++) {
                    add(eleList[i]);
                }
            } else {
                add(eleList);
            }
        },
        // 移除class
        removeClass: function (eleList, cls) {
            eleList = Array.prototype.slice.apply(eleList);
            var remove = function (obj) {
                var obj_class = ' '+obj.className+' ';
                obj_class = obj_class.replace(/(\s+)/gi, ' ');
                var removed = obj_class.replace(' '+cls+' ', ' ');
                removed = removed.replace(/(^\s+)|(\s+$)/g, '');
                obj.className = removed;//替换原来的 class.
            }
            if (eleList instanceof Array) {
                for (var i=0; i<eleList.length; i++) {
                    remove(eleList[i]);
                }
            } else {
                remove(eleList)
            }

        },
        // 类选择器
        getElementsByClassName: function (className,context,tagName) {

            if(typeof context == 'string'){
                tagName = context;
                context = document;
            }else{
                context = context || document;
                tagName = tagName || '*';
            }
            if(context.getElementsByClassName){
                return context.getElementsByClassName(className);
            }
            var nodes = context.getElementsByTagName(tagName);
            var results= [];
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                var classNames = node.className.split(' ');
                for (var j = 0; j < classNames.length; j++) {
                    if (classNames[j] == className) {
                        results.push(node);
                        break;
                    }
                }
            }
            return results;
        },
        // 获取数据
        getData: function (options) {
            //格式化参数
            var formatParams = function (data) {
                var arr = [];
                for (var name in data) {
                    arr.push(encodeURIComponent(name) + '=' + encodeURIComponent(data[name]));
                }
                return arr.join('&');
            };
            var jsonp = function (options) {
                options = options || {};
                if (!options.url || !options.callback) {
                    throw new Error("参数不合法");
                }
                //创建 script 标签并加入到页面中
                var callbackName = ('jsonp_' + Math.random()).replace(".", "");
                var oHead = document.getElementsByTagName('head')[0];
                options.data[options.callback] = callbackName;
                var params = formatParams(options.data);
                var _oScript = document.createElement('script');
                oHead.appendChild(_oScript);

                //创建jsonp回调函数
                window[callbackName] = function (json) {
                    oHead.removeChild(_oScript);
                    clearTimeout(_oScript.timer);
                    window[callbackName] = null;
                    options.success && options.success(json);
                };

                //发送请求
                _oScript.src = options.url + '?' + params;

                //超时处理
                if (options.time) {
                    _oScript.timer = setTimeout(function () {
                        window[callbackName] = null;
                        oHead.removeChild(_oScript);
                        options.fail && options.fail({ message: "超时" });
                    }, options.time);
                }
            }
            jsonp(options);
        },

        //videojs版本兼容
        getVideoElement:function (player,elClass) {

            // || videojs.players["video-player"].controlBar.progressControl.seekBar.b
            //    找属性，不具有公用性，废弃
            if( typeof player.$ == 'function'){
                return player.$(elClass);
            }else{
                return this.getElementsByClassName(elClass)[0];
            }
        },
        ajax:function(options){
            var xhr = null;
            var params = formsParams(options.data);
            //创建对象
            if(window.XMLHttpRequest){
                xhr = new XMLHttpRequest()
            } else {
                xhr = new ActiveXObject("Microsoft.XMLHTTP");
            }
            // 连接
            if(options.type == "GET"){
                xhr.open(options.type,options.url + "?"+ params,options.async);
                xhr.send(null)
            } else if(options.type == "POST"){
                xhr.open(options.type,options.url,options.async);
                xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded");
                xhr.send(params);
            }
            xhr.onreadystatechange = function(){
                if(xhr.readyState == 4 && xhr.status == 200){
                    options.success(JSON.parse(xhr.responseText));
                }
            }
            function formsParams(data){
                var arr = [];
                for(var prop in data){
                    arr.push(prop + "=" + data[prop]);
                }
                return arr.join("&");
            }

        },
        getUrlParams: function() {
            var p = {
                src:'',//播放地址
                appid:'',
                fileid:'',
                appId:'',
                fileId:'',
                controls: true,//是否显示控制条
                preload: 'auto',//是否预加载 'auto''metadata''none'
                autoplay: false,//是否自动播放
                loop:false,//是否循环
                mute:false,//是否静音
                fuScrnEnabled:true,//是否允许全屏
                coverpic:'',//封面图
                playbackRateEnabled:false,//是否展示倍速按钮
                live:'',
                videoType:'mp4,m3u8',//默认格式if
                revolution:[],//清晰度
            };
            var search =  (location.search && location.search.split('?')[1]) || '';
            var params = search.split('&');
            for(var i=0;i<params.length;i++){
                var k = params[i].split('=');
                if(k[1] == 'true'){
                    p[k[0]] = true;
                }else if(k[1] == 'false'){
                    p[k[0]] = false;
                }else{
                    p[k[0]] = k[1]
                }
            }
            return p;
        }

    };
    videoBox.getUrlParams = tools.getUrlParams;
    videoBox.initResolutionSwitcher = function(player,resolutionArrs){
        if(!resolutionArrs || (resolutionArrs && resolutionArrs.length <= 0)) return;
        // resolutionArrs = [
        //     {label:'SD',src:'/bbb.flv'},
        //     {label:'HD',src:'/a.mp4'},
        // ]
        var ifHasSwitcher = player.resolutionArrs && true;
        player.resolutionArrs = resolutionArrs;
        player.resolutioTimer = 0;
        //已经有了切换，就不再绑定了
        if(!ifHasSwitcher){
            player.changeVideo = function(type){
                player.pause();

                var src = player.resolutionArrs[type].src;
                var label = player.resolutionArrs[type].label;

                clearTimeout(player.resolutioTimer);
                player.resolutioTimer = 0;

                player.resolutioTimer = setTimeout(function () {

                    var ctime = player.currentTime();
                    var playbackRate = 1;
                    if(options.type == 'html5') {
                        playbackRate = player.playbackRate();
                    }
                    player.resolutionCurTime = ctime;
                    player.resolutionPlaybackRate = playbackRate;
                    var el = tools.getElementsByClassName('vjs-resolution-switcher-value',player.el_,'div');
                    el && (el[0].innerHTML = label);
                    player.currentVideo = src;

                    player.src(videoBox.setVideoType(src));
                    player.play();
                },400)
                return false;
            }
            if(options.type == 'html5'){
                player.on('ratechange',function () {
                    var playbackRate = player.playbackRate();
                    player.resolutionPlaybackRate = playbackRate;
                });
            }

            player.on('playing',function () {
                if(player.resolutioTimer){
                    player.currentTime(player.resolutionCurTime||0);
                    if(options.type == 'html5') {
                        player.playbackRate(player.resolutionPlaybackRate || 1);
                    }
                    player.removeClass('vjs-seeking')
                    player.play();
                    player.resolutioTimer = 0;
                }
            })
            player.on('ended',function () {
                player.resolutioTimer = 0;
                player.resolutionCurTime = 0;
            })
        }
        //每次都重置切换
        var before = tools.getElementsByClassName("vjs-resolution-switcher",player.el_,'div')[0];
        if(before) before.parentNode.removeChild(before);

        var html = '';
        var label = resolutionArrs[0].label||'';
        var currentSrc = player.src();
        for (var i = 0;i<resolutionArrs.length;i++){
            if(resolutionArrs[i].src == currentSrc){
                label = resolutionArrs[i].label;
            }
            html += '<li class="vjs-menu-item" tabindex="-1" role="menuitemcheckbox"  onclick="videojs.players[\''+ player.id_ +'\'].changeVideo('+ i +')">'+ resolutionArrs[i].label + ' </li>';
        }
        var videoPanelMenu = tools.getElementsByClassName("vjs-fullscreen-control",player.el_,'div')[0];
        var controlBar = tools.getElementsByClassName("vjs-control-bar",player.el_,'div')[0];
        var domSwitch = document.createElement('div');
        domSwitch.className = 'vjs-resolution-switcher  vjs-menu-button vjs-menu-button-popup vjs-control vjs-button';
        domSwitch.setAttribute('aria-live', 'polite');
        domSwitch.setAttribute('aria-haspopup', 'true');
        domSwitch.setAttribute('aria-expanded', 'false');
        controlBar.insertBefore(domSwitch,videoPanelMenu);
        var videoPanelSwitcher= tools.getElementsByClassName("vjs-resolution-switcher",player.el_,'div')[0];
        videoPanelSwitcher.innerHTML =(
            '<div class="vjs-menu" role="presentation">'
            + '<ul class="vjs-menu-content" role="menu">'
            + html
            + '</ul></div>'
            +'<span class="vjs-control-text">Resolution Switcher</span>'
            + '<div class="vjs-resolution-switcher-value">'+ label +'</div>');
    }
    videoBox.getResolution = function(params,callback){
        var k = params.fileId || params.fileid ||'';
        if(k){
            var sign = hex_md5(options.a+k);
            tools.ajax({
                url:'//live.jd.com/l/getHighAndStandardMp4Address',
                type : "POST",
                async : true,
                data:{
                    fileId:k,
                    appId:params.appId,
                    sign:sign
                },
                success:function (data) {
                    if(data && data.code == 0){
                        params.revolution = data.data;
                    }
                    typeof callback == "function" && callback(params)
                },
                fail:function () {
                    typeof callback == "function" && callback(params)
                }
            })
        }else{
            typeof callback == "function" && callback(params)
        }
    };
    videoBox.initPlayer = function (id,opt) {
        //已经初始化的，不要执行以下方法了。
        if(typeof videojs == 'function' && videojs.players[id]) {
            (typeof opt.callback == 'function') && opt.callback(videojs.players[id]);
            return;
        };
        var params = {
            src:opt.src||'',//播放地址
            appId:opt.appId|| opt.appid||'',
            fileId:opt.fileId||opt.fileid||'',
            controls: Boolean(opt.controls),//是否显示控制条
            preload: opt.preload||'auto',//是否预加载 'auto''metadata''none'
            autoplay: Boolean(opt.autoplay),//是否自动播放
            loop:Boolean(opt.loop),//是否循环
            mute:Boolean(opt.mute),//是否静音
            fuScrnEnabled:Boolean(opt.fuScrnEnabled),//是否允许全屏
            coverpic:opt.coverpic||'',//封面图
            playbackRateEnabled:opt.playbackRateEnabled || false,//是否展示倍速按钮
            sw:opt.sw,
            sh:opt.sh,
            live:Boolean(opt.live),
            videoType:opt.videoType || 'mp4,m3u8',//默认格式
            revolution:opt.revolution || [],//清晰度
            customzie:opt.customzie || 'common',
            callback:opt.callback || ''
        };
        if(params.revolution.length <= 0 ){
            videoBox.getResolution(params,function (data) {
                params = data
                setVideo();
            })
        }else{
            setVideo();
        }

        function setVideo() {
            // 加载完毕后，直接调用videojs相关方法就可以
            videoBox.getVideojs({
                //暂时支持这四种格式rtmp,flv,mp4,m3u8，rtmp和flv强制flash播放，根据需要加载视频格式
                videoType:params.videoType,
                playbackRateMenuButton:true,//是否展示倍速播放组件，只支持h5
                customzie:params.customzie,//none：则不加载css，common||'':加载默认,green：加载绿色主题
                callback:callback //播放器首次加载后，回调函数
            })
        }
        function callback() {
            // var html = '\ <video id="video-player"\ class="video-js"\ style="position:absolute;top:0;left:0"\ poster="//misc.360buyimg.com/lib/img/e/blank.gif"\ width="500" height="500" controls>\ <source src="rtmp://live.hkstv.hk.lxdns.com/live/hks" type="rtmp/flv"> </source>\ <p class="vjs-no-js"> 您的浏览器不支持 HTML 5 Video 标签，请升级浏览器。</p>\ </video>';
            // $('#video-container').append(html);
            var o = {
                autoplay: params.autoplay,
                controls: params.controls,
                preload: params.preload,
                inactivityTimeout: 1000,
                loop:params.loop,
                muted:params.mute,
                poster:decodeURIComponent(params.coverpic),//封面图
            }
            params.sw && params.sh && (o.width = params.sw,o.height = params.sh);
            params.playbackRateEnabled && (o.playbackRates = [0.5, 1, 1.5, 2])
            !params.fuScrnEnabled && (o.controlBar = {},o.controlBar.fullscreenToggle = false)
            var isHasInit  = videojs.players[id];
            var player = videojs(id,o);
            //videoBox.setVideoType 必须调用，为了识别视频格式
            if(params.src){
                player.currentVideo = params.src;
                player.src(videoBox.setVideoType(decodeURIComponent(params.src)));
            }else if(params.revolution.length){
                player.currentVideo = params.revolution[0].src;
                player.src(videoBox.setVideoType(decodeURIComponent(params.revolution[0].src)));
                //清晰度选择插件,demo实例
                videoBox.initResolutionSwitcher(player,params.revolution)
            }
            // else if(!player.src() ){
            //     player.error(4);
            // }
            if(!isHasInit){
                player.on('loadstart',function(){
                    params.autoplay && player.play();
                });
                player.on('ready',function(){
                    player.addClass('vjs-has-started');
                });
                player.on('playing',function () {
                    player.removeClass('vjs-seeking');
                    player.removeClass('vjs-wating');
                });
                //以下是主要事件
                player.on(player.tech_,'tap', function(){
                    if(player.paused()){
                        player.play()
                    }else{
                        player.pause()
                    }
                });
            }

            //直播时画面同步
            if(params.live){
                //直播时，可以保证同步
                var resync = 0,resync_flag = 1;
                player.resync = 0;
                player.resync_flag = 1;

                params.autoplay && (player.resync_flag = 0);

                player.on('play',function () {

                    !player.resync && player.resync_flag && (setTimeout(function () {
                        player.src(videoBox.setVideoType(decodeURIComponent(player.currentVideo)));
                        player.play()
                    },1));
                    player.resync = 1;
                    player.resync_flag = 1;
                });
                player.on('playing',function () {
                    clearTimeout(player.resync_timer );
                    player.resync_timer = setTimeout(function () {
                        player.resync = 0;
                    },2000)
                });
            }


            (typeof params.callback == 'function') && params.callback(player);
        }


    }
    //中文支持
    function supportCN(){
        (typeof videojs != "undefined") && videojs.addLanguage("zh-CN",{
            "Play": "播放",
            "Pause": "暂停",
            "Current Time": "当前时间",
            "Duration Time": "时长",
            "Remaining Time": "剩余时间",
            "Stream Type": "媒体流类型",
            "LIVE": "直播",
            "Loaded": "加载完毕",
            "Progress": "进度",
            "Fullscreen": "全屏",
            "Non-Fullscreen": "退出全屏",
            "Mute": "静音",
            "Unmute": "取消静音",
            "Playback Rate": "播放速度",
            "Subtitles": "字幕",
            "subtitles off": "关闭字幕",
            "Captions": "内嵌字幕",
            "captions off": "关闭字幕",
            "Chapters": "节目段落",
            "Close Modal Dialog": "关闭弹窗",
            "Descriptions": "描述",
            "descriptions off": "关闭描述",
            "Audio Track": "音轨",
            "You aborted the media playback": "视频播放被终止",
            "A network error caused the media download to fail part-way.": "无法获取播放内容",
            "The media could not be loaded, either because the server or network failed or because the format is not supported.": "无法获取播放内容",
            "The media playback was aborted due to a corruption problem or because the media used features your browser did not support.": "无法获取播放内容",
            "No compatible source was found for this media.": "无法获取播放内容。",
            "The media is encrypted and we do not have the keys to decrypt it.": "无法获取播放内容",
            "Play Video": "播放视频",
            "Close": "关闭",
            "Modal Window": "弹窗",
            "This is a modal window": "这是一个弹窗",
            "This modal can be closed by pressing the Escape key or activating the close button.": "可以按ESC按键或启用关闭按钮来关闭此弹窗。",
            ", opens captions settings dialog": ", 开启标题设置弹窗",
            "captions settings": "字幕设置",
            ", opens subtitles settings dialog": ", 开启字幕设置弹窗",
            ", opens descriptions settings dialog": ", 开启描述设置弹窗",
            ", selected": ", 选择"
        });
    }
    window.videoBox = videoBox;
    module.exports = videoBox
})
