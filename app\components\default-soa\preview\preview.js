define('MOD_ROOT/preview/preview', function(require, exports, module) {
    var follow = require('MOD_ROOT/common/follow')
    var Contrast = require('MOD_ROOT/preview/contrast')
    var G = require('MOD_ROOT/common/core')
    var Tools = require('MOD_ROOT/common/tools/tools')
    var Event = require('MOD_ROOT/common/tools/event').Event
    var ABTest = require('MOD_ROOT/common/tools/abtest')
    var Panorama = require('MOD_ROOT/preview/panorama').Panorama
    //获取视频
    var videoBox = require('MOD_ROOT/videoBox/videoBox')
    var videoTrack = require('MOD_ROOT/videoBox/videoTrack')
    var videoAnchorPoint = require('MOD_ROOT/videoBox/videoAnchorPoint')
    var videoTrailer = require('MOD_ROOT/videoBox/videoTrailer')
    var Conf = require('PUBLIC_ROOT/conf');
    require('MOD_ROOT/preview/contrast.css')

    require('PLG_ROOT/jQuery.imgScroll')
    require('PLG_ROOT/jQuery.jqueryzoom')
    require('PLG_ROOT/jQuery.zoom')
    require('JDF_UI/dialog/1.0.0/dialog')
    require('MOD_ROOT/ETab/ETab')

    var mainImageLitmusTest = {} // 主图试金石信息
    var isFetchVideo = false // 是否初始化过视频处理


    function initScroll($el, config) {
        // var step = G.wideVersion ? 6 : 3;
        // var step = 6;

        $el.imgScroll({
            width: config.width,
            height: config.height,
            visible: config.step,
            direction: 'y',
            showControl: true,
            step: config.step,
            loop: false,
            prev: '#spec-forward',
            next: '#spec-backward'
        })

        if ($el.find('li').length <= config.step) {
          $('#spec-forward').css('display','');
          $('#spec-backward').css('display','');

          $el.attr('style', '').children('.lh').attr('style','')
        }
    }
    
    // 1.中版 2.宽版
    function getWidthType(width) {
      var w = window.innerWidth;

      if (w < 1680 && w >= 0) {
        return 1
      }

      if (w >= 1680) {
        return 2
      }

      return 2
    }

    // 获取左侧缩略图插件元素宽高以及每次多少个
    function getImgWidthAndHeight(type) {
      if (type === 1) {
        return { width: 88, height: 95, step: 6 }
      }

      if (type === 2) {
        return { width: 114, height: 121, step: 6 }
      }

      return { width: 114, height: 121, step: 6 }
    }

    function imgZoom(cfg, playUrl) {
        var imgDomain = pageConfig.FN_GetImageDomain(cfg.skuid)
        // 是否是海外购商品
        var isOversea = /isOverseaPurchase-2/.test(cfg.specialAttrs)
        var imgSid = isOversea ? '/popWaterMark/' : '/n0/'
        // 缩略图hover
        var $specList = $('#spec-list')
        var items = $specList.find('li')

        // popWaterMark无水印图片//img12.360buyimg.com/popWaterMark/jfs/t307/33/1803665566/455187/91e45bee/5441ecedN42cc10ac.jpg

        var specN1Html = $('#spec-n1').html()
        var currIndex = -1

        if (cfg.ctCloth) {
            $('body').addClass('ctCloth')
        }

        function hover($this) {
            var $items = $specList.find('li')
            var img = $this.find('img')
            var src = img.attr('src') //img
            var dataUrl = img.attr('data-url') //data-url
            var index = $items.index($this)

            if (currIndex == index) {
                return
            }
            currIndex = index

            Tools.landmine({
              functionName: 'PC_Productdetail_ReducedPicture_Click',
              exposureData: ['mainskuid'],
              extraData: {
                media_type: window.closeVideo && index === 0 ? '视频' : '图片',
                picture_sort: currIndex + ''
              },
              errorTips: '商品主图区-缩略图hover-上报错误'
            })

            //图片
            $('#spec-n1').addClass('jqzoom')
            if (index == 0) {
                Watermark.show();
                $("#main-img-"+cfg.skuid) && $("#main-img-"+cfg.skuid).find(".center-layer").show();

                $('.J-video-icon').show()
                window.renderVideo && window.renderVideo()
            } else {
                Watermark.hide();
                $("#main-img-"+cfg.skuid) && $("#main-img-"+cfg.skuid).find(".center-layer").hide();
                $('.J-video-icon').hide()
                window.closeVideo && window.closeVideo()
            }

            // src = src.replace(/228x228/g, '1440x1440')
            // src = src.replace(/178x228/g, '1125x1440')
            // src = src.replace(/172x228/g, '1090x1440')
            // src = src.replace(/\/n5/g, '/n1')

            src = src.replace(/114x114/g, '720x720')

            var resJqimg = ''
            // if (cfg.ctCloth && src.indexOf("350x449") != -1 ) {//长图标识&旧长图
            //     resJqimg = src.replace(/350x449/g, '800x1026')
            // }else if (cfg.ctCloth && src.indexOf("353x469") != -1 ) {//长图标识&新长图
            //     resJqimg = src.replace(/353x469/g, '752x1000')
            // } else {
            //     resJqimg = imgDomain + imgSid + dataUrl
            // }
            if (cfg.ctCloth) {//长图标识
                resJqimg = src.replace(/350x449/g, '800x1026')//旧长图
                resJqimg = src.replace(/350x467/g, '750x1000')//新长图
            }else {
                resJqimg = imgDomain + imgSid + dataUrl
            }
            cfg.currentMainImage = src

            $('#spec-img')
                .eq(0)
                .attr({
                    src: src,
                    jqimg: resJqimg
                })
                .show()
            $items.removeClass('img-hover')
            $this.addClass('img-hover')
        }

        $specList.undelegate().delegate('li', 'mouseenter', function (e) {
            hover($(e.currentTarget))
        })
        // items.mouseenter(function() {
        //     hover($(this))
        // })
        try {
          if (playUrl && mainImageLitmusTest.label === 'Pre1') {
            hover(items.eq(1))
          } else if (playUrl && mainImageLitmusTest.label === 'pre2' && location.hash === '#switch-sku') {
            hover(items.eq(1))
          } else {
            hover(items.eq(0))
          }
        } catch (e) {
           hover(items.eq(0))
           console.error('检查是否有视频异常', e)
        }

        // 放大镜初始化
        function initMagnifier(widthType) {
          var typeMapWidth = {
            1: 552,
            2: 720
          }
          
          var magnifierSize = $('#spec-n1').width() || typeMapWidth[widthType];

          if (!pageConfig.product.magnifierSwitch) {
            return;
          }
          
          try {
            if (pageConfig.product.isEBook) {
              // 选择父元素
              var $parent = $('#spec-n1');

              // 创建新容器
              var $newContainer = $('<div></div>');

              // 将子元素追加到新容器
              $parent.children().appendTo($newContainer);

              // 用新容器替换原父元素的内容
              $parent.append($newContainer);
            }
          } catch (e) {
            console.error('电子书主图追加外层异常')
          }

          var $jqzoom = $('.jqzoom').children('div').eq(0);

          if (!!$jqzoom.find('img').attr('src')) {
              $jqzoom.jqueryzoom({
                  xzoom: magnifierSize,
                  yzoom: magnifierSize,
                  offset: 10,
                  position: 'left',
                  lens: 1,
              })
              if ($jqzoom.is(":hover")) {
                 $jqzoom.trigger('mouseenter');
              }
          }
        }

        var prevWidthType = getWidthType();

        $(document).ready(function () {
          initMagnifier(prevWidthType)
        })

         var configImg = getImgWidthAndHeight(prevWidthType)
         // 监听窗口变化重新执行初始化滚动图片
         window.onresize = function () {
           var widthType = getWidthType();
           
           // 改变
           if ( widthType !== prevWidthType) {
             prevWidthType = widthType
             initScroll($specList, getImgWidthAndHeight(widthType))

             initMagnifier(widthType)
           }
         }

        // 图片滚动【一般页面】
        initScroll($specList, configImg)

        $('.spec-list a').click(function () {
          if ($(this).attr('id') === 'spec-forward') {
            Tools.landmine({
              functionName: 'PC_Productdetail_PictureRollBtn_Click',
              exposureData: ['mainskuid'],
              extraData: {
                buttonName: 'forward'
              },
              errorTips: '商品主图区-缩略图上翻页按钮点击-异常'
            })
          }
          if ($(this).attr('id') === 'spec-backward') {
            Tools.landmine({
              functionName: 'PC_Productdetail_PictureRollBtn_Click',
              exposureData: ['mainskuid'],
              extraData: {
                buttonName: 'next'
              },
              errorTips: '商品主图区-缩略图下翻页按钮点击-异常'
            })
          }
        })

        // 大图点击 > data-big == 1 的时候才有
        var $specN1 = $('#spec-n1')
        if ($specN1.attr('data-big') === '1') {
            $specN1.bind('click', function(e) {
                var $target = $(e.target)
                if (
                    !$target.is('.J-preview-btn') &&
                    $target.parents('.J-preview-btn').length < 1
                ) {
                    // window.open('//item.jd.com/bigimage.aspx?id=' + cfg.skuid);
                    showBigImage(0)
                }
            })
        }
        $specN1.delegate('#three-d-show', 'click', function() {
            showBigImage(1)
        })
        $specN1.delegate('#over-all-images-show', 'click', function() {
            showBigImage(2)
        })
    }

    function showBigImage(tabIndex) {
        // 屏蔽查看大图
        return;
        
        //判断是不是pop租赁商品
        var isPopRent = pageConfig && pageConfig.product && (pageConfig.product.specialAttrs instanceof Array) && (pageConfig.product.specialAttrs.join(',').indexOf('LeaseType-3') > -1)
        //判斷是不是预售
        // var isYuShouSku = pageConfig && pageConfig.product && (pageConfig.product.specialAttrs instanceof Array) && (pageConfig.product.specialAttrs.join(',').indexOf('YuShou') > -1) && (pageConfig.product.specialAttrs.join(',').indexOf('YuShouNoWay') > -1);
        //预售判断方式更改
        var isYuShouSku = pageConfig && pageConfig.product && (pageConfig.product.isYuShou || pageConfig.product.isPinGou);

        //如果下发图片数量为1则为实景展示，否则为全景展示
        // var displayTxt = '全景展示';
        // if(pageConfig.product.is3DHome){
        //     displayTxt = '3D家';
        // }
        var html =
            '\
        <div class="preview-layer J-preview-layer">\
            <div class="layer-tab">\
                <ul>\
                    <li data-tab="trigger" class="current"><a href="#none">大图</a></li>\
                    <li data-tab="trigger" class="{if !has3D}hide{/if}"><a href="#none">3D</a></li>\
                </ul>\
            </div>\
            <div data-tab="item" class="layer-con">\
                <div class="layer-img">\
                    <span></span>\
                    <img id="popup-big-img" width="546" data-big="//img14.360buyimg.com/n0/${src}" src="//img14.360buyimg.com/n1/s546x546_${src}" alt=""/>\
                </div>\
                <div class="layer-side">\
                    <h3 class="pro-name">${name}</h3>\
                    <div class="pro-price" style="display:'+(isYuShouSku?'none':'block')+'">'+
            (isPopRent?' 租   金 ':(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && pageConfig.product.skuMarkJson && pageConfig.product.skuMarkJson.pg?' 拼 购 价 ': ' 京 东 价 ')) + '<span>￥${price}</span>'+

                    '</div>\
                    <div class="side-list">\
                        <div class="J-side-list">\
                            <ul>\
                                {for list in imgs}\
                                <li>\
                                    {for item in list.tabs}\
                                    <a href="#none">\
                                        <img data-url="${item}" src="http://img14.360buyimg.com/n5/s54x54_${item}" alt=""/>\
                                    </a>\
                                    {/for}\
                                </li>\
                                {/for}\
                            </ul>\
                        </div>\
                        <div class="list-page J-list-page">\
                            <a href="#none" class="prev"><i></i></a>\
                            <span class="scroll-status-wrap">1/1页</span>\
                            <a href="#none" class="next"><i></i></a>\
                        </div>\
                    </div>\
                </div>\
            </div>\
            <div data-tab="item" class="layer-con">\
                <iframe src="${iframe}" \
                    id="dialogIframe" \
                    name="dialogIframe" \
                    marginwidth="0" marginheight="0" frameborder="0" scrolling="no" \
                    style="border: 0px; width: 100%; height: 100%;"></iframe>\
            </div>\
            <div data-tab="item" class="layer-con" style="display:'+((pageConfig.product.is3DHome&&pageConfig.notSupportWebGL)?'none':'')+';">\
                <div id="over-all-images" class="panorama-show p-tips">\
                    <div class="panorama-img">\
                        <img src="" alt="">\
                        <div class="panorama-tips"></div>\
                    </div>\
                </div>\
            </div>\
        </div>'

        var $threeDShow = $('#three-d-show')
        var $overAllImagesShow = $('#over-all-images-show')
        var iframe = $threeDShow.attr('data-href')

        //处理价格，如果价格空，或者小于等于0，则展示暂无报价
        var bigImgPrice = '暂无报价';

        if (Conf.get("BASE.PRICE.5G") &&
            [0, 1, 1][G.specialAttrs["tsop"]]) {
            bigImgPrice = "免费办理";
        }
        
        if(pageConfig.product.jp){
            var jpprice = parseFloat(pageConfig.product.jp).toFixed(2);
            if(!isNaN(jpprice)){
                if(jpprice>0){
                    bigImgPrice=jpprice;
                }
            }
        }
        // 预约预售商品是否隐藏价格
        if (pageConfig.product.isHidePrice) {
            bigImgPrice = '待发布';
        }

        var data = {
            has3D: $threeDShow.length > 0,
            hasOverAllImagesShow: $overAllImagesShow.length > 0,
            iframe: iframe || 'about:blank',
            price: bigImgPrice,
            name: pageConfig.product.name,
            src: pageConfig.product.src,
            imgs: Tools.reBuildJSON(pageConfig.product.imageList || [], 8)
        }

        function setZoomImg() {
            var $imgLayer = $('.J-preview-layer .layer-img')
            var url = $imgLayer.find('img').attr('data-big')
            $imgLayer.trigger('zoom.destroy').zoom({ url: url })
        }

        function onReady(idx) {
            var $layer = $('.J-preview-layer')

            $layer.ETab({
                defaultIndex: idx,
                onSwitch:function(number){
                     //当切换为后再加载图片
                     if(number==0)return;
                    if(pageConfig.product.imageAndVideoJson && pageConfig.product.imageAndVideoJson.overAllPath) {
                        new Panorama({'pics':pageConfig.product.imageAndVideoJson.overAllPath, 'element':$('#over-all-images')})
                    }
                }
            })

            setZoomImg()

            if(tabIndex!=0){
                // new Panorama({'pics':pageConfig.product.overAllImages || [], 'element':$('#over-all-images')})
                if(pageConfig.product.imageAndVideoJson && pageConfig.product.imageAndVideoJson.overAllPath) {
                    new Panorama({'pics':pageConfig.product.imageAndVideoJson.overAllPath, 'element':$('#over-all-images')})
                }
            }

            $layer.delegate('.J-side-list li img', 'mouseenter', function() {
                var url = $(this).data('url')
                $layer.find('#popup-big-img').attr({
                    src: '//img14.360buyimg.com/n1/s546x546_' + url,
                    'data-big': '//img14.360buyimg.com/n0/' + url
                })
                $('.J-side-list li a').removeClass('current')
                $(this).parent().addClass('current')
                setZoomImg()
            })

            $('.J-side-list').imgScroll({
                // width: 58 * 2,
                // height: 58 * 4,
                showControl: true,
                loop: false,
                status: true,
                prev: $('.J-list-page .prev'),
                next: $('.J-list-page .next')
            })
        }

        $('body').dialog({
            mainId: 'big-image-3d',
            width: 960,
            maskClose: true,
            autoUpdate: true,
            height: 580,
            title: '大图',
            type: 'html',
            source: html.process(data),
            onReady: function() {
                onReady(tabIndex)
            }
        })
    }
    // 窄版且标题超过一行，收藏竖向显示
    function changeFollow($followBtn) {
        if (window.innerWidth < 1680 && $('.sku-name-title').height() > 28) {
            $followBtn.addClass('vertical')
        } else {
            $followBtn.removeClass('vertical')
        }
    }
    function setFollow(cfg) {
        //主图下的关注
        var $followBtn = $('.J-follow')

        follow.init($followBtn, function() {
             // 埋点
            Tools.landmine({
                functionName: 'PC_Productdetail_CollectBtn_Click',
                exposureData: ['mainskuid'],
                extraData: {
                    buttonStatus: $('.J-follow').text() == '已收藏' ? '1' : '0'
                },
                errorTips: '收藏埋点出错'
            })
        })

        changeFollow($followBtn)
        
        $(window).resize(function() {
            changeFollow($followBtn)
        })

        if (follow.check) {
            follow.check([cfg.skuid], function(sku, isFollowed) {
                var buttonStatus = 0
                if (isFollowed) {
                    $followBtn.addClass('followed').html('<i></i>已收藏')
                    buttonStatus = 1
                }
                // 曝光
                Tools.exposure({
                    functionName: 'PC_Productdetail_CollectBtn_Expo',
                    exposureData: ['mainskuid'],
                    extraData: {
                        buttonStatus: String(buttonStatus)
                    },
                    errorTips: '收藏曝光出错'
                })
            })
        }
        // $followBtn.click(function(){
        //     var followed = $(this).hasClass("followed")
        //     var attboxrL = $("#attboxr").length
        //     setTimeout(function() {
        //         if(!followed && attboxrL.length > 0){
        //             $(this).addClass("followed").html("<i></i>已收藏");
        //         }
        //     }, 100);
           
        // })
    }
    // function combineShare(cfg) {
    //     //主图下的分享
    //     var $el = $('.J-share,#btn-pg-share,.J-btn-invite,.J-yuyue-share')

    //     $el.bind('click', function() {
    //         require.async('MOD_ROOT/preview/combineShare', initShare)
    //     })

    //     function initShare(CombineShare) {
    //         var thumbnailList = $('#preview .spec-items li img')
    //         var imgs = []
    //         var shareBody = $('.J-share')

    //         thumbnailList.each(function() {
    //             var src = $(this).attr('src')
    //             imgs.push(src)
    //         })

    //         var text =
    //             '我在@京东 发现了一个非常不错的商品： ' +
    //             cfg.name +
    //             '\u3000京东价：￥ {PRICE}。 感觉不错，分享一下'
    //         var cs = new CombineShare({
    //             sid: 3,
    //             rid: 986951,
    //             $el: shareBody,
    //             title: text,
    //             content: text,
    //             url: location.href,
    //             imgs: imgs,
    //             onbeforeOpen: function(cs) {
    //                 if (cfg.jp > 0) {
    //                     var price = cfg.jp;
    //                 } else {
    //                     var price = "暂无报价";
    //                     if (Conf.get("BASE.PRICE.5G") &&
    //                         [0, 1, 1][G.specialAttrs["tsop"]]) {
    //                         price = "免费办理";
    //                     }
    //                 }
    //                 cs.jp = price;
    //             }
    //         }).popUp()
    //     }
    // }

    function isTestSku(sku) {
        var testSkuMap = {
            '1666577': 1,
            '1397351': 1,
            '897523': 1,
            '1339416': 1,
            '3586940': 1,
            '3812632': 1,
            '2676072': 1,
            '3668041': 1,
            '3740498': 1,
            '3381353': 1,
            '1521124': 1,
            '10297885284': 1,
            '4229138': 1,
            '1084054': 1,
            '4390182': 1,
            '1879150': 1,
            '1689327': 1,
            '2972067': 1,
            '3754251': 1,
            '2873418': 1,
            '4140704': 1,
            '4368426': 1,
            '2799682': 1,
            '2539514': 1,
            '3773968': 1,
            '3812632': 1,
            '2129121': 1,
            '966962': 1,
            '4401892': 1,
            '2963900': 1,
            '3327010': 1,
            '1042114': 1,
            '1727233': 1,
            '4460756': 1,
            '10477349622': 1,
            '2379171': 1,
            '1041464': 1,
            '1280920': 1,
            '1847341': 1,
            '549543': 1,
            '1834943': 1,
            '4168838': 1,
            '3829536': 1,
            '3755130': 1,
            '3017823': 1,
            '2676648': 1,
            '1242645': 1,
            '**********': 1,
            '3796674': 1,
            '1218558': 1
        }
        return testSkuMap[sku] === 1
    }
    // video start
    function setVideo(cfg) {
        if (isFetchVideo) {
          return;
        }

        isFetchVideo = true;

        if(cfg.imageAndVideoJson == undefined) {
          // 曝光
          Tools.exposure({
            functionName: 'PC_Productdetail_MainPicture_Expo',
            exposureData: ['mainskuid'],
            extraData: {
              media_type: '图片',
              pic_num: $('.spec-items').find('li').length
            },
            errorTips: '商品主图区-首帧主图曝光异常'
          });
          imgZoom(cfg)
          return
        };

        var data = null
        var videoTPL =
            '\
        <video id="video-player"\
            class="video-js vjs-default-skin"\
            style="position:absolute;top:0;left:0"\
            poster="//misc.360buyimg.com/lib/img/e/blank.gif"\
            width="{1}" height="{2}"  controls>\
            <source src="{0}" type="video/mp4"> </source>\
            <p class="vjs-no-js"> 您的浏览器不支持 HTML 5 Video 标签，请升级浏览器。</p>\
        </video>'

        var $preview = $('#preview')
        var $video = $('#v-video')
        var $videoWrap = $video.find('.J-v-player')
        var $close = $video.find('.J-close')
        var $btn = $('.J-preview-btn .J-video-icon')
        var size = getVideoSize()

        var vu = cfg.imageAndVideoJson.mainVideoId

        var isExcludeBrowser = $.browser.msie && Number($.browser.version) < 9
        var canPlay = document.createElement('video').canPlayType
        var isValidBrowser = vu && !isExcludeBrowser && canPlay

        if (/deubg/.test(location.href)) {
            console.log(isValidBrowser)
        }

        //现在播放器兼容ie8，不必要检测浏览器版本，只检测是否有视频即可
        if (!isValidBrowser) {
            hideVideoBtn()
            imgZoom(cfg)

            // 曝光
            Tools.exposure({
              functionName: 'PC_Productdetail_MainPicture_Expo',
              exposureData: ['mainskuid'],
              extraData: {
                media_type: '图片',
                pic_num: $('.spec-items').find('li').length
              },
              errorTips: '商品主图区-首帧主图曝光异常'
            });
            return false
        }

        if ($('#three-d-show').length) {
            $btn.addClass('has-3d-show')
        }

        if ($.browser.msie && Number($.browser.version) < 11) {
            $video.addClass('ie9')
        }

        $preview.delegate('.J-video-icon', 'click', function (params) {
          renderVideo()

          Tools.landmine({
            functionName: 'PC_Productdetail_MainPicture_Click',
            exposureData: ['mainskuid'],
            extraData: {
              media_type: '视频',
              buttonStatus: '播放'
            },
            errorTips: '商品主图区-主图视频的点击-异常'
          });
        })
        // $preview.delegate('.video-play-icon', 'click', renderVideo)
        $video.delegate('.J-close', 'click', closeVideo)

        function isTargetVersion() {
            pageConfig.__videoAutoPlayTest = new ABTest(Tools.getUUID(), 0.5)
            return pageConfig.__videoAutoPlayTest.isHitVersion() === 'A'
        }

        var isTargetSku = isTestSku(cfg.skuid) && isTargetVersion()

        if (isTargetSku) {
            renderVideo()
        }

        function showVideoBtn() {
             $btn.show()
        }
        function hideVideoBtn() {
            $btn.hide()
        }

        function setData(r) {
            var $items = $('.spec-items').find('li');
            try {
              if (!r || !r.playUrl) {
                hideVideoBtn()

                Tools.exposure({
                  functionName: 'PC_Productdetail_MainPicture_Expo',
                  exposureData: ['mainskuid'],
                  extraData: {
                    media_type: '图片',
                    pic_num: $items.length || 0
                  },
                  errorTips: '商品主图区-首帧主图曝光异常'
                })
              } else {
                  cfg.videoData = data = r
                  setVideoLayerSize()
                  showVideoBtn()
                  window.closeVideo = closeVideo

                  var $first =  $items.first().removeClass('img-hover');
                  var $cloneFirst = $first.clone();
                  $first.before($cloneFirst); 

                  $cloneFirst.eq(0).append('<div class="video-play-icon" />');
                  
                  Tools.exposure({
                    functionName: 'PC_Productdetail_MainPicture_Expo',
                    exposureData: ['mainskuid'],
                    extraData: {
                      media_type: '视频',
                      pic_num: $items.length + 1 || 0
                    },
                    errorTips: '商品主图区-首帧主图曝光异常'
                  });
              }
            } catch (error) {}

            imgZoom(cfg, r.playUrl)
        }
        function setVideoLayerSize() {
            $close.removeClass('hide')
            $video.hide()
            $video.css({
                // width: 720,
                // height: 720,
                zIndex:6
            })
        }

        function getVideoSize() {
            var isWideVer = pageConfig.compatible && pageConfig.wideVersion
            var size = {
                width: isWideVer ? 450 : 300,
                height: isWideVer ? 450 : 300
            }
            if (!cfg.twoColumn) {
                size = {
                    width: isWideVer ? 350 : 300,
                    height: isWideVer ? 350 : 300
                }
            }
            if (cfg.ctCloth) {
                size = {
                    width: isWideVer ? 350 : 300,
                    height: isWideVer ? 467 : 385
                }
            }
            return size
        }

        function renderVideo(e) {
            if(typeof videojs != 'undefined' && videojs.players['video-player']) return;

            var vOptions = {
                autoplay: true,
                controls: true,
                preload: 'auto',
                fuScrnEnabled: true, //是否允许全屏
            }
            // if (cfg.ctCloth) {
            //     vOptions.controls = false
            // }

            if (data) {
                $videoWrap.html(
                    videoTPL.format(
                        data.playUrl,
                        size.width + 2,
                        size.height + 2
                    )
                )
                vOptions.callback = callback;
                videoBox.initPlayer('video-player',vOptions)
            }
            function callback(player) {
                $('.preview-btn').hide()
                $video.show()
                cfg.videoPlayer = player
                videoTrack.init(cfg.videoPlayer,cfg.skuid,cfg.imageAndVideoJson.mainVideoId,data.playUrl)
                //测试锚点数据
                // data.videoMarkList =[{startTime:5,mark:'啊'},{startTime:10,mark:'哦'}];
                videoAnchorPoint.init(cfg.videoPlayer,{list:data.videoMarkList,limitLabelNum:3})
                //主视频结束，或者片尾结束，然后关闭视频
                cfg.videoPlayer.on('ended', function () {
                    if(!videoTrailer.ifTrailer || (videoTrailer.ifTrailer && videoTrailer.trailerFlag == 1)){
                        closeVideo();
                    }
                })
                videoTrailer.addCloseBtn(cfg.videoPlayer,closeVideo)
                //视频片尾，data.extInfo
                videoTrailer.init(cfg.videoPlayer,data.extInfo)
                cfg.videoPlayer.on('error', function (r) {
                    if (typeof console !== 'undefined') {
                        console.info('Video loaded Error.')
                    }
                })
                setTimeout(function () {
                    cfg.videoPlayer.play()
                    var $v = $('#video-player_html5_api');
                    var video = $v[0];

                    video.muted = true // 自动播放静音

                    $v.bind('volumechange', function(){
                      if (video.muted) {
                        video.volume = 0.3;
                      }
                    });

                    setTimeout(function () {
                      $v.bind('play', function(){
                        Tools.landmine({
                          functionName: 'PC_Productdetail_MainPicture_Click',
                          exposureData: ['mainskuid'],
                          extraData: {
                            media_type: '视频',
                            buttonStatus: '播放'
                          },
                          errorTips: '商品主图区-主图视频的点击-异常'
                        });
                      });
                    }, 200)
                    
                    $v.bind('pause', function(){
                      Tools.landmine({
                        functionName: 'PC_Productdetail_MainPicture_Click',
                        exposureData: ['mainskuid'],
                        extraData: {
                          media_type: '视频',
                          buttonStatus: '暂停'
                        },
                        errorTips: '商品主图区-主图视频的点击-异常'
                      });
                    });
                }, 0)

            }

            if (e) {
                e.stopPropagation()
            }
        }
        function closeVideo() {
      
            $video.hide()
            $('.preview-btn').show()
            //折中的办法，videojs不允许自我事件中自我销毁
            disposePlayer()
        }
        function disposePlayer() {
            if(typeof videojs != 'undefined' && !!videojs.players['video-player']) {
                videojs.players['video-player'].dispose()
            }
            if(typeof cfg != 'undefined' && typeof cfg.videoPlayer == 'object'){
                cfg.videoPlayer = ''
            }
            $videoWrap.html('');
        }
        window.renderVideo = renderVideo
        
        var host = '//api.m.jd.com'
        if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
        $.ajax({
            url: host + '/tencent/video_v3',
            dataType: 'jsonp',
            data: {
                vid: vu,
                type: 1,
                from: 1,
                appid: 24,
                appid: 'item-v3',
                functionId: "pc_tencent_video_v3" 
            },
            success: setData,
            error: function() {
              imgZoom(cfg)
            }
        })
    }
    // video end
    // function setPlusImage(cfg) {
    //     if ($('body').hasClass('yyp')) return

    //     getImage()

    //     function getImage() {
    //         var host = '//api.m.jd.com'
    //         if(pageConfig.product && pageConfig.product.colorApiDomain){
    //         host = pageConfig.product && pageConfig.product.colorApiDomain
    //     }
    //         $.ajax({
    //             url: host + '/img/channel',
    //             data: {
    //                 skuId: cfg.skuid,
    //                 appid: 'item-v3',
    //                 functionId: "pc_img_channel"
    //             },
    //             dataType: 'jsonp',
    //             success: resetImageList
    //         })
    //     }

    //     function resetImageList(r) {
    //         var $list = $('#spec-list')
    //         var $main = $('#spec-img,#spec-n1 img')

    //         if (!$list.length) return
    //         if (!r || !r.imageList || !r.imageList.length) return

    //         var imgs = r.imageList
    //         var splitPoint = 'jfs/'

    //         if (cfg.imageList) cfg.imageList = imgs
    //         if (r.imageUrl) resetMainImage(r.imageUrl)
    //         if (r.imageList) resetListImage(r.imageList)

    //         initScroll($list)

    //         function resetMainImage(img) {
    //             var src = $main.attr('src')
    //             var jqsrc = $main.attr('jqimg')
    //             var segs = src.split(splitPoint)
    //             var jqsegs = jqsrc.split(splitPoint)

    //             if (segs.length < 2 || jqsegs.length < 2) {
    //                 console.log('主图片地址异常：%s', src)
    //             } else {
    //                 $main.attr({
    //                     'data-replace': true,
    //                     'src': segs[0] + img,
    //                     'data-origin': segs[0] + img,
    //                     'jqimg': jqsegs[0] + img
    //                 })
    //             }
    //         }

    //         function resetListImage(data) {
    //             var $imgItem = $list.find('li img')
    //             var len = $imgItem.length

    //             if (!$imgItem.length) return

    //             var $item = $list.find('li').eq(0)

    //             // 补齐 DOM 元素
    //             if (data.length > len) {
    //                 var n = data.length - len
    //                 do {
    //                     $list.find('ul').append($item.clone())
    //                     n--
    //                 } while( n > 0)
    //             }
    //             $list.find('li').not($item).removeClass('img-hover')
    //             $list.find('li img').each(function(i) {
    //                 var src = $(this).attr('src')
    //                 var segs = src.split(splitPoint)

    //                 if (segs.length < 2) {
    //                     console.log('图片地址异常：%s', src)
    //                 } else {
    //                     if (i < imgs.length) {
    //                         $(this).attr({
    //                             'data-replace': true,
    //                             'src': segs[0] + imgs[i],
    //                             'data-url': imgs[i]
    //                         })
    //                     } else {
    //                         $(this).parent().remove()
    //                     }
    //                 }
    //             })
    //         }
    //     }
    // }

    var Watermark = {
        init: function () {
            this.$el= $('#spec-n1 .p-watermark')
            this.bindEvent()
        },
        bindEvent: function () {
            Event.addListener('onStockReady', $.proxy(this.show, this))
        },
        isValidatedArea: function () {
            var aids = Tools.getAreaId().areaIds
            return aids[0] !== 53283 && aids[0] !== 52993 && aids[0] !== 32
        },
        show: function () {
            if (this.isValidatedArea()) {
                this.$el.show()
            } else {
                this.hide()
            }
        },
        hide: function () {
            this.$el.hide()
        }
    }
    function toCheckIsShow3DHome(){
        //一期需求 如果下发图片数量为1则为实景展示(3D家)，否则为全景展示
        if(pageConfig.product.imageAndVideoJson && pageConfig.product.imageAndVideoJson.overAllPath && pageConfig.product.imageAndVideoJson.overAllPath.length==1){
            pageConfig.product.is3DHome = true;
        }
    }
  function toCheckWhetherSupportWebGL(){
        var canvas = document.createElement('canvas');    
        canvas.style.width = canvas.style.height = '100%';
	   if(!canvas.getContext || !canvas.getContext('experimental-webgl', {alpha: false, depth: false})){
	       //隐藏掉入口
           $('.btn-3d.vr-single-pic').hide();
           pageConfig.notSupportWebGL = true;
	   }
  }

  function setWxcEnterpriseExpo() {
    try {
        expLogJSON('smb_pc', 'Wxc_Enterprise', '{"categoryId":' + pageConfig.product.cat[0] + '}')
    } catch (e) {
        if (typeof console !== 'undefined') {
            console.log('企业购更优惠曝光埋点错误');
        }
    }
  }
  
  function handleSotckWatch(cfg) {
    Event.addListener('onStockReady', function(data) {
      if (!data || !data.stock || !data.stock.data) {
        return console.warn('融合接口错误');
      }

      data = data.stock.data
      var abData = data.abData || {} // 埋点信息
      var main_image = abData.main_image || {} // 主图试金石

      mainImageLitmusTest = main_image

      setVideo(cfg)
    })
    Event.addListener('onStockError', function() {
      setVideo(cfg)
    })
  }

  function init(cfg) {
    handleSotckWatch(cfg)

    toCheckIsShow3DHome();
    toCheckWhetherSupportWebGL();
    Watermark.init()
    // imgZoom(cfg)
    setFollow(cfg)
    var locname = window.location.hostname
    if(locname == "item.jdh.com" || locname == "item.jingdonghealth.cn"){// 健康域名屏蔽收藏和降价通知
        $(".J-follow").remove()
        $(".J-notify-sale").remove()
        $("#comment-count").css("border-right","none")
    }
    // combineShare(cfg)
    // setVideo(cfg)
    Contrast.init(null, null, 'utf-8')
    // setPlusImage(cfg)
    setWxcEnterpriseExpo()
    try{
        var domId =  $("#main-img-"+cfg.skuid).length
        if(domId > 0 && pageConfig.product  && pageConfig.product.floatLayerSwitch){
            Event.addListener('onStockReady', function(data) {
                var width= parseInt($("#spec-img").width()) 
                var height= parseInt($("#spec-img").height())
                var layerList = []
                var layerInfo = data && data.stock && data.stock.data  && data.stock.data.layerInfo
                if(layerInfo){
                    layerList.push(layerInfo)
                    Tools.getPcSkuLayers(layerList, width, height, "main-img-", "6")
                } 
            });
        }
        
    }catch(e){
        console.log("大图主图浮层初始化渲染",e)
    }
  }
  
  module.exports.__id = 'preview'
  module.exports.init = init
})