// 定制服务批量加车样式
.other-styles-container {
	.ellipsis {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.other-styles {
		// width: 320px;
		font: 12px 'Microsoft YaHei';
		dd {
			// padding: 17px 0 0 20px;
			> ul {
				padding-bottom: 5px;
			}
			li > div {
				float:left;
				&:first-child {
					// width: 270px;
					// margin-right: 130px;
					a {
						color: #666;
						text-decoration: none;
					}
				}
			}
			.title, .content {
				// display: flex;
				.center-column {
					// flex: 1;
					// width: 215px;
                    max-width: 350px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					float: left;
                    color: #1A1A1A;
				}
				.tip-color {
					width: 124px;
					height: 32px;
					line-height: 32px;
					color: red;
					float: right;
					right: -23px;
					font-size: 12px;
				}
			}
			.title {
				color:#999;
				margin-bottom: 18px;
				height: 16px;
				[title] {
					display: inline-block;
					width: 100%;
					span{
						display: inline-block;
						max-width: 140px;
						@extend .ellipsis;
					}
					i{
						vertical-align: 3px !important;
					}
				}
			}
			.content {
                position: relative;
				color:#666;
                padding-bottom: 12px;
                border-bottom: 1px dashed rgba(0, 0, 0, 0.12);
				margin-bottom: 12px;
				line-height: 32px;
				.center-column {
					@extend .ellipsis;
				}
			}
      .content:last-child {
        border: none;
      }
			.last {
				width: 108px;
				height: 32px;
				text-align: center;
				position: absolute;
                right: 100px;
				top: 0px;
				.count-set {
					> * {
						float:left;
						width: 32px;
						height: 32px;
						line-height: 32px;
						box-sizing: border-box;
						border: 0.4px solid rgba(0, 0, 0, 0.02);
                        border-radius: 4px;
						vertical-align: middle;
						text-align: center;
						background: #F7F8FC;

					}
					> input {
						width: 42px;
						font: 14px 'Arial';
						color:#000;
						background-color: #fff;
						font-weight: 400;
						border: none;
						outline: 0;
						padding: {
							top: 6px;
							bottom: 6px;
						}
						&.disabled {
							color: #ccc;
							cursor: not-allowed;
						}
					}
				}
				i {
					width: 30px;
					font: normal 20px/30px 'Arial';
					color:#666;
					cursor: pointer;
					-webkit-user-select:none;
					-moz-user-select:none;
					-o-user-select:none;
					user-select: none;
					&.disabled {
						cursor: not-allowed;
						color: #ccc;
					}
				}
				.i-reduce {
					// line-height: 17px;
				}
			}
		}
	}
	.i-drop-down, .i-drop-up {
		// margin-left: 3px;
		width: 8px;
		height: 8px;
		background: url(https://img13.360buyimg.com/imagetools/jfs/t1/264309/16/8775/261/677b7baeF34afad1e/6590a37f7ad68def.png) no-repeat;
    background-size: 8px 8px;
		vertical-align: middle;
	}
	.i-drop-down {
		display: none;
	}
	.i-drop-up {
		background: url('./i/i-drop-up.png') no-repeat;
	}

	/*batch-add-to-cart*/
	.batch-add-to-cart {
		margin-top: 20px;
		width: 100%;
		line-height: 46px;
		ul {
			float:right;
			li {
				float: left;
				margin-left: 15px;
			}
		}
		button {
			width: 142px;
			height: 46px;
			background-color: #e4393c;
			border: 0;
			color: #fff;
			font-size: 18px;
			font-weight: bold;
			cursor: pointer;
			outline: 0;
			margin-left: 3px;
			&.disabled {
				background-color: #ccc;
				cursor: not-allowed;
			}
		}
		.line {
			color: #eee;
		}
		[class*='total'] {
			font-size: 12px;
		}
		.total-count {
			color: #666;
		}
		big {
			font-size: 20px;
		}
		.total-money {
			color: #e2231a;
		}
	}
	.li-prompt {
		display: none;
		color: #e2231a;
		.i-prompt {
			display: inline-block;
			vertical-align: -3px;
			margin-right: 3px;
			width: 16px;
			height: 16px;
			background: url('./i/i-prompt.png') no-repeat;
		}
	}
	.center-column {
		position: relative;
	}
	.price-list, .count-tip, .data-list {
		// box-shadow: 0 2px 4px 0 rgba(217, 217, 217, 0.5);
		// background-color: #fff;
		// border: 1px solid #f8f8f8;
		// z-index: 10;
		position: absolute;
	}
	.price-list {
		top: 22px;
		line-height: 20px;
		padding: 10px;
		left: -30px;
		display: none;
	}
	.count-tip {
		top: -3px;
		right: 83px;
		color: #e2231a;
		padding: 5px 10px;
		line-height: normal;
		white-space: nowrap;
	}
	.data-list {
		top: 0;
		left: 0;
		min-width: 110px;
		display: none;
		ul{
			max-height:200px;
			overflow-y: auto;
		}
		li {
			line-height: 40px;
			padding-left: 10px;
			color: #999;
			cursor: pointer;
			white-space: nowrap;
			&:hover {
				background-color: #f8f8f8;
				color: #317a8d;
			}
		}
		.i-drop-up {
			position: absolute;
			top: 10px;
			right: 20px;
			z-index: 20;
		}
	}
	.show-or-hide-other-styles {
		width: 100%;
		.drop-down {
            width: 69px;
            height: 32px;
            font-size: 14px;
            line-height: 32px;
            border: 0.5px solid #888B94;
            padding: 0 12px;
            border-radius: 4px;
			float:left;
            a {
                color: #1A1A1A;
            }
		}
		.drop-down-link {
			float:right;
			a {
				color: #005EA7;
			}
			
		}
	}
	#stylesList {
		height: 180px;
    font-size: 15px;
		overflow: hidden;
	}
}
.count-tip-bybt {
	top: 0 !important;
}
#saleProps {
	overflow: visible;
}

// 定制服务楼层样式
#choose-custom {
	.dt{
		line-height: 34px;
    	height: 34px;
	}
	.dd {
		border-radius: 6px;
		// padding: 0 16px;
		font-size: 14px;
		margin-bottom: 28px;
		&.after-custom {
			border: 1px solid rgba(0, 0, 0, 0.06);
			.custom-main{
				height: 47px;
				line-height: 47px;
				background: #F7F8FC;
				border: 1px solid rgba(0, 0, 0, 0.02);
			}
		}
			
	}
	.custom-main{
		height: 34px; // 定制前行高 34 定制后行高 47 
		padding: 0 12px;
		line-height: 34px;
		
    
		// margin-top: 6px;
		.custom-tip{
			position: absolute;
			right: 0;
			top: -5px;
			background-color: #df3033;
			padding: 3px 4px;
			height: 9px;
			line-height: 9px;
			border-bottom-left-radius: 3px;
			border-top-right-radius: 3px;
			color: #fff;
			font-size: 10px;
		}
		.custom-check {
			a {
				border: 1px solid #df3033; 
			}
		}
		.custom-update{
			width: 44px;
			height: 14px;
			padding: 6px 8px;
			line-height: 14px;
			border: 0.5px solid #888B94;
			border-radius: 4px;
			cursor: pointer;
			float: left;
			margin-right: 5px;
      		margin-top: 10px;
			color: #1A1A1A;
			background: url(https://img11.360buyimg.com/imagetools/jfs/t1/255243/36/9549/283/677bc49eF9de957f3/7a4a9cdd695c1113.png) no-repeat;
			background-position: 42px center;
		}
		.custom-cancel{
			cursor: pointer;
			color: #505259;
			float: right;
		}
		.item {
			margin-bottom: 0px;
			background: none;
			a{
				border: none;
				padding: 0;
				// color:  rgba(227, 57, 60, 1);
                background: none; // 覆盖通用样式
				cursor: auto;
				color: #1a1a1a;
			}
			a:hover{
				border: none;
				padding: 0;
                background: none; // 覆盖通用样式
				cursor: auto;
				color: #1a1a1a;
			}
			.minServicePrice{
        		font-size: 15px;
				position: relative;
				float: left;
			}
			.customServiceOriginPrice{
				color: #666;
				margin-left: 5px;
			}
		}
	}
	.custom-list{
		line-height: 26px;
    	margin: 10px 0;
		padding: 0 16px;
		ul{
			width: 100%;
			height: 45px;
			overflow: hidden;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            li {
                width: 262px;
                height: 26px;
                line-height: 26px;
                display: flex;
                span {
                    height: 20px;
                }
				span {
					overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap; /* 文本不换行 */
					flex-shrink: 0;
				}
                span:first-child {
                    width: 126px;
                }
				span:nth-child(2) {
					flex-shrink: 1;
				}
                // span:last-child {
                //     flex-shrink: 0;
                // }
            }
		}
		.down{
			height: auto;
		}
		.custom-down{
			cursor: pointer;
			margin-top: 5px;
			.i-drop-down {
				margin-left: 3px;
				width: 10px;
				height: 7px;
				background: url('./i/i-drop-down.png') no-repeat;
				background-size: 10px;
				vertical-align: middle;
				display: inline-block;
			}
		}
	}
    
}

// 工服定制批量采购tab切换
.batch-purchase {
	background: #f3f3f3;
    margin-bottom: 10px;
    border-bottom: 1px solid #df3033;
	.purchase-tab {
		padding: 7px 0;
		display: inline-block;
		width: 218px;
		text-align: center;
		margin: 0;
		cursor: pointer;
		
	}
	.curr {
		background: #DF3033;
		color: #fff;
	}
	
}

// tab切换
.common-plan {
    position: relative; // 窄屏时需要此属性
	background: linear-gradient(90deg, #F2F3F7 23.94%, #FFFFFF 81.42%);
    border-top: 1px solid #fff; // 上方有一像素白条
    // display: -webkit-box; //block
	display: -webkit-box;      /* 旧版 Safari, iOS 6- */
	display: -webkit-flex;     /* 过渡版 Safari, iOS 7+ */
	display: flex;             /* 标准语法 */
    height: 48px;
    border-radius: 8px 8px 0 0;
    // margin-top: -8px;
	overflow: hidden;
	.pre{
		height: auto;
		background: #fff;

		i{
			background-image: url(i/i-drop-down.png);
			width: 14px;
			height: 10px;
			transform: rotate(90deg);
			background-repeat: no-repeat;
		}
	}
	.preFlex{
		display: flex;
		align-items: center;
	}
	.next{
		height: 48px;
		background: #fff;
		position: absolute;
		top: 4%;
		right: 0;
		i{
			background-image: url(i/i-drop-down.png);
			width: 14px;
			height: 10px;
			transform: rotate(-90deg);
			background-repeat: no-repeat;
		}
	}
	.purchase-tab {
		//padding: 7px 0;
		//padding: 0 2px !important;
		width: 219px;
		text-align: center;
		margin: 0;
		cursor: pointer;
		float: left;
		color: #1A1A1A;
		font-size: 20px;
		//padding: 0;
		line-height: 48px;
		.jhgtip{
			height: 12px;
			color: rgba(255, 15, 35, 1);
			font-size: 14px;
			border: solid rgba(255, 173, 190, 1) 1px;
			line-height: 12px;
			padding: 2px;
			border-radius: 2px;
			margin-left: 2px;
		}
	}
	.curr {
		//background: #fff;
		color: #FF0F23;
		display: block;
		height: 48px;

		align-items: center;
		justify-content: center;
		padding: 0;
		font-size: 20px;
		font-weight: 600;
	}
	.curr-left{
		background-image: url(i/left-img.png);
		width: 218px;
	}
	.curr-right{
		background-image: url(i/right-img.png) ;
		background-repeat: no-repeat;
		background-size: 100%;
	}
	.curr-right-one{
		background-image: url(i/right-img.png) ;
		background-repeat: no-repeat;
		padding-left: 15px;
	}
	.curr-right-one::after{
		content: ' ';
		position: absolute;
		background-color: #fff;
		width: 318px;
		height: 48px;
	}
	.curr-content{
		background-image: url(i/content-img.png);
		width: 268px;
	}
}
// 工服定制去定制按钮
.J-custom-stock{
	height: 44px; 
	background: #fff;
	// border: 1px solid #e4393c; 
	// color: #e4393c;
	background-color: #ff475d;
    color: #fff;
	&:hover{
		// border: 1px solid #e4393c; 
		// color: #e4393c;
		background-color: #ff475d;
    	color: #fff;
	}
}
.btn-disable-new{
	border: none;
    color: #fff;
    background-color: #adb0bc;
    cursor: not-allowed;
}
.btn-disable-new:hover {
    background-color: #adb0bc;
    color: #fff
}
.custom-tips{
	position: relative;
    float: left;
	margin-left: 5px;
	cursor: pointer;
	.question{
		.sprite-question{
			display: inline-block;
			vertical-align: -3px;
			width: 16px;
			height: 16px;
			background-image: url(https://img11.360buyimg.com/imagetools/jfs/t1/265713/6/12921/978/678a4853Fb877b897/479338c16704e1f8.png);
			background-position: 0 0;
			background-size: 14px;
			background-repeat: no-repeat;
			cursor: pointer;
		}
	}
	
	.tips {
		z-index: 2;
		width: 302px;
		position: absolute;
		left: -135px;
		// top: 35px;
		top: -344px;
        transform: scale(1.3);
        transform-origin: center bottom;
		display: none;
		.sprite-arrow {
			width: 11px;
			height: 6px;
			background-image: url(//storage.jd.com/retail-mall/item/pc/unite/1.0.223-bybtdsj/components/default-soa/jdservice/i/__sprite.png);
			background-position: -14px -16px;
			position: absolute;
			overflow: hidden;
			left: 139px;
			top: -5px;
			_bottom: -1px;
			display: none;
			
		}
		.content {
			// padding: 10px;
			background: #fff;
			border: 1px solid #cecbce;
			color: #666;
			-moz-box-shadow: 0 0 2px 2px #eee;
			-webkit-box-shadow: 0 0 2px 2px #eee;
			box-shadow: 0 0 2px 2px #eee
		}
	}
}
.hover .tips{
	display: block;
}

.ui-dialog .ui-dialog-content {
    padding: 0; // 覆盖 dialog 样式
}