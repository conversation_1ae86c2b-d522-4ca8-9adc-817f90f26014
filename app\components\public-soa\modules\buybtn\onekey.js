define('PUBLIC_ROOT/modules/buybtn/onekey', function(require, exports, module) {
    var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event
    var Login = require('JDF_UNIT/login/1.0.0/login')
    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools')
    var ABTest = require('PUBLIC_ROOT/modules/common/tools/abtest');

    require('JDF_UI/dialog/1.0.0/dialog')
    require('PUBLIC_ROOT/modules/ETooltips/ETooltips')

    var oneKeyBuy = {
        init: function(cfg) {
            this.cfg = cfg
            this.$el = $('#btn-onkeybuy')
            this.href = this.$el.attr('href')
            this.ybParam = ''
            this.giftParam = ''
            // 落地配 id
            this.did = ''
            this.cfg = cfg

            this.deps = {
                isPriceMoreThan10: false,
                havestock: false,
                isLogin: false,
                isNotYuShou: !cfg.isYuShou,
                isNotBiGouMa: !cfg.isBiGouMa,
                isNotHeYue: !cfg.isHeYue,
                isNotO2O: !(cfg.__chooseShop && cfg.__chooseShop.selected),
                isNotHW: true,
                isNotJinCai: !cfg.isJinCaiSelected
            };

            if (this.$el.length) {
                this.bindEvent(cfg)
            }

            cfg.__reLoadOneKeyBuy = $.proxy(function() {
                this.get(true)
            }, this)

            return this
        },
        isEnabled: function(cfg) {
            this.deps.havestock = cfg.havestock
            this.deps.isNotYuShou = !cfg.isYuShou
            this.deps.isNotHeYue = !cfg.isHeYue
            this.deps.isNotJinCai = !cfg.isJinCaiSelected;
            this.deps.isNotO2O = !(
                cfg.__chooseShop && cfg.__chooseShop.selected
            )

            //console.log('Stock: %s, IsNotYuShou: %s, IsNotHeYue: %s', cfg.havestock, !cfg.isYuShou, !cfg.isHeYue);
            for (var key in this.deps) {
                if (this.deps.hasOwnProperty(key)) {
                    if (!this.deps[key]) {
                        return false
                    }
                }
            }
            return true
        },
        bindEvent: function() {
            var _this = this
            var cfg = this.cfg

            this.$el.unbind('click').bind('click', function() {
                _this.handleClick()
            })

            Event.addListener('onGiftSelected', function(data) {
                if (_this.isEnabled(cfg)) {
                    if (data.skus && data.skus.length) {
                        _this.giftParam = data.skus.join(',')
                    } else {
                        _this.giftParam = ''
                    }
                }
            })
            Event.addListener('onLDPSelected', function(data) {
                if (_this.isEnabled(cfg)) {
                    if (data.did) {
                        _this.did = data.did
                    } else {
                        _this.did = ''
                    }
                }
            })
            Event.addListener('onYBSelected', function(data) {
                if (_this.isEnabled(cfg)) {
                    if (data.skus && data.skus.length) {
                        _this.ybParam = data.skus.join(',')
                    } else {
                        _this.ybParam = ''
                    }
                }
            })

            Event.addListener('onPriceReady', function(data) {
                _this.deps.isPriceMoreThan10 = Number(data.price.p) > 10
                    ? true
                    : false
                if (_this.isEnabled(cfg)) {
                    _this.show()
                }
            })
            Event.addListener('onLogin', function(data) {
                _this.deps.isLogin = data.login
                if (_this.isEnabled(cfg)) {
                    _this.show()
                }
            })

            // 切换地区无货不显示
            Event.addListener('onStockReady', function(data) {
                var ids = Tools.getAreaId().areaIds
                // 海外、港澳、台湾不支持一键购
                if (ids[0] === 53283 || ids[0] === 52993 || ids[0] === 32) {
                    _this.deps.isNotHW = false
                }
                _this.isPlusType = (data && data.stock && data.stock.stock && data.stock.stock.isPlus) || false;
                
                if (_this.isPlusType) { // plus会员不展示“银牌及以上用户开通PLUS可享限时特惠”引导文案
                    $(".J-plus-price .text").next("a").remove();
                }
                /**
                 * 针对PLUS秒杀商品，对开通PLUS做ABtest
                 * A方案，切量10%，去掉PLUS会员专享价右侧的引导开通文案及链接，在“加入购物车”按钮旁加上“开通PLUS”按钮，去掉一键购
                 * B方案，切量90%, 保持线上不动
                 */
                if (cfg.seckillType == 6) {
                    cfg.__plusKoABTest = new ABTest(Tools.getUUID(), 0.1);
                    var version = cfg.__plusKoABTest.isHitVersion();
                    if (version === 'A' && !_this.isPlusType) {
                        var $btnOpenPlus = $('<a href="//plus.jd.com/index" class="btn-buyplus btn-special1 btn-lg" clstag="shangpin|keycount|product|plusopen">开通PLUS</a>');
                        if (!$(".btn-buyplus").length) {
                            $btnOpenPlus.insertAfter($("#InitCartUrl"));
                        }
                        $(".J-plus-price .text").next("a").remove();
                        _this.deps.showOneKey = false;
                    }
                }

                if (_this.isEnabled(cfg)) {
                    _this.show()
                } else {
                    _this.hide()
                }
            })
            // 合约切到非合约机时判断条件显示一键购
            Event.addListener('onHeYueReady', function(data) {
                var isNonHeYue = !cfg.isHeYue

                if (isNonHeYue && _this.isEnabled(cfg)) {
                    _this.show()
                } else {
                    _this.hide()
                }
            })
            Event.addListener('onYBSelected', function(data) {
                if (!cfg.baiTiaoFenQi) {
                    return false
                }
                if (!cfg.baiTiaoFenQi.hasSelectedItem && _this.isEnabled(cfg)) {
                    _this.show()
                }
            })
            Event.addListener('onBaiTiaoSelect', function(data) {
                var isSelect = data.isSelect

                if (!isSelect && _this.isEnabled(cfg)) {
                    _this.show()
                } else {
                    _this.hide()
                }
            })

            if (this.$el.length) {
                if (cfg.isEBook) {
                    return
                }
                this.$el
                    .ETooltips({
                        close: false,
                        autoHide: true,
                        content:
                            '<div id="easy-buy-tips"><div class="loading-style1"><b></b>加载中，请稍候...</div></div>',
                        width: 265,
                        zIndex: 10
                    })
                    .mouseover(function() {
                        if ($(this).data('loaded') !== 'true') {
                            _this.getCommonAddress(cfg)
                            $(this).data('loaded', 'true')
                        }
                    })
            }
        },
        handleClick: function() {
            var _this = this

            var hasSelectedGifts =
                typeof pageConfig.giftSelectedSkuids !== 'undefined' &&
                pageConfig.giftSelectedSkuids.length > 0
            if (pageConfig.haveGift) {
                if (hasSelectedGifts) {
                    jumpUrl()
                } else {
                    $('#choose-gift').addClass('item-hl-bg')
                    return false
                }
            } else {
                jumpUrl()
            }

            function jumpUrl() {
                _this.checkLogin(function(isLogin) {
                    if (isLogin) {
                        _this.get()
                    }
                })
            }
        },
        checkLogin: function(cb) {
            Login({
                modal: true,
                complete: function(r) {
                    cb(r && r.Identity && r.Identity.IsAuthenticated)
                }
            })
        },
        get: function(useOtherAddr) {
            var _this = this
            var url = this.cfg.easyBuyUrl

            useOtherAddr = useOtherAddr || false

            // 有车管家赠品时，促销接口会屏蔽普通赠品池，此时gids为pageConfig中的giftSelectedSkuids（下发车管家赠品时会写入pageConfig）
            // if(pageConfig.giftSelectedSkuids){
            //     this.giftParam = pageConfig.giftSelectedSkuids.join(',');
            // }
            
            $.ajax({
                url: url,
                data: {
                    skuId: this.cfg.skuid,
                    num: $('#buy-num').val(),
                    gids: this.giftParam,
                    ybIds: this.ybParam,
                    did: this.did,
                    useOtherAddr: useOtherAddr
                },
                dataType: 'jsonp',
                jsonpCallback: 'easybuysubmit',
                success: function(r) {
                    if (r) {
                        _this.set(r)
                    }
                }
            })
        },
        set: function(r) {
            if (r.success && r.jumpUrl) {
                if (/debug=onekeybuy/.test(location.href)) {
                    prompt('测试用', r.jumpUrl)
                } else {
                    window.location = r.jumpUrl
                }
            }
            if (!r.success && r.message) {
                $('body').dialog({
                    title: '提示',
                    width: 400,
                    height: 200,
                    type: 'text',
                    source: r.message
                })
            }
        },
        showBtn: function() {
            if (this.isEnabled(this.cfg)) {
                this.show()
            } else {
                this.hide()
            }
        },
        show: function() {
            if (!pageConfig.hasCarGift) { // 如果有车管家赠品，屏蔽一键购，因为结算页中不能识别车管家赠品
                this.$el.show()
            }
        },
        hide: function() {
            this.$el.hide()
        },
        getCommonAddress: function(cfg) {
            var _this = this
            $.ajax({
                url: '//cd.jd.com/easybuy',
                data: { skuId: cfg.skuid },
                dataType: 'jsonp',
                scriptCharset: 'gbk',
                success: function(r) {
                    if (!r) {
                        r = { empty: true }
                    } else {
                        r.empty = false
                    }
                    _this.setCommonAddress(r)
                }
            })
        },
        setCommonAddress: function(r) {
            var content =
                '\
            <div class="easy-buy-tips easy-buy-tips-ab" style="line-height:24px;">\
                {if !empty}\
                <img src="//img13.360buyimg.com/devfe/jfs/t5995/298/8888084024/16675/6f7ff6b/598c2979N216f9dab.png" />\
                <p><strong>${name} </strong><span class="hl_gray">${mobile}</span></p>\
                <p class="hl_gray">${fullAddress}</p>\
                <p>\
                    {if pickName}\
                        <strong>${paymentId} | 上门自提</strong>\
                    {else}\
                        <strong>${paymentId} | 快递上门</strong>\
                    {/if}\
                </p>\
                <p>\
                    <a class="mod-btn" onclick="pageConfig.product.__reLoadOneKeyBuy()" href="#none">修改地址/支付方式/收货方式下单</a>\
                </p>\
                {else}\
                <p><strong>一键购，帮您快速一键下单</strong></p>\
                <p class="hl_gray" style="line-height:1.5em;">初次使用需设置收货地址和支付、配送方式，继续使用即可完成一键购买！</p>\
                {/if}\
            </div>'
            $('#easy-buy-tips').html(content.process(r))
        }
    }

    module.exports = oneKeyBuy
    module.exports.__id = 'oneKeyBuy'
})
