define('PUBLIC_ROOT/modules/common/common', function(require, exports, module) {
    var Event = require('PUBLIC_ROOT/modules/common/tools/event').Event
    var tools = require('PUBLIC_ROOT/modules/common/tools/tools')
    var G = require('PUBLIC_ROOT/modules/common/core')

    /**
     * addListener - 添加监听订阅事件必须引入 common 包，通过 common 包中的事件实例统一添加
     *   var Event = require('PUBLIC_ROOT/modules/common/common').Event;
     *   Event.addListener('onAreaChange', function (data) {
     *       console.log(data.area);
     *   });
     * fire - 异步的请求完成后触发**已经注册**过的事件句柄
     *   Event.fire({
     *       type: 'onAreaChange',
     *       area: 'some data'
     *   });
     */

    // 设置特殊属性 及 pageConfig 全局变量
    function setGlobals(cfg) {
        // 是否有货 stock.js 发送请求后会修改状态
        cfg.havestock = null
        cfg.isHeYue = null
        //cfg.isFeeType   = '-655-6881-6882-'.indexOf('-' + cfg.cat[2] + '-') > -1;
        cfg.isKO = G.onAttr('isKO')
        cfg.isBiGouMa = G.onAttr('isJMa')
        cfg.isTuanGou = G.onAttr('isGroupgoods')
        // 赛式报名
        cfg.isLOC3 = G.onAttr('isLOC-3')
        // RL Shop
        cfg.isRLShop = cfg.venderId === 686083
    }

    function getLoginStatus() {
        tools.checkLogin(function(r) {
            Event.fire({
                type: 'onLogin',
                login: r.IsAuthenticated
            })
        })
    }

    // 页面加载完默认定位到导航处
    function setDefaultScrollTop() {
        var win = window
        // 页面加载默认定位到面包屑的位置
        var $rootNav = $('.crumb-wrap')
        var $shopHeader = $('#shop-head')
        var $catNav = $('#nav-2014')
        var $ysNav = $('#discover-nav')
        var $tuanCatNav = $('#nav-2015')
        var sTop = 0
        var hashIsNone = win.location.hash === '#none'

        if (
            !$rootNav.length || win.location.hash === '#comment' || hashIsNone
        ) {
            return false
        }
        if ($rootNav.length) {
            sTop = $rootNav.offset().top
        }
        if ($ysNav.length) {
            sTop = $ysNav.offset().top
        }
        if ($catNav.length) {
            sTop = $catNav.offset().top
        }
        if ($tuanCatNav.length) {
            sTop = $tuanCatNav.offset().top
        }

        if ($shopHeader.length) {
            sTop = $shopHeader.offset().top
        }
        function jumpToCrumb() {
            $(win).scrollTop(sTop)
        }

        // 如果是刷新页面
        if (win.name) {
            // 如果页面在刷新前scrollTop有值，chrome会在刷新后自动定位到刷新前的位置
            $(win).bind('beforeunload', jumpToCrumb)
        } else {
            try {
                win.name = G.name + '__scrollTop__flag'
                jumpToCrumb()
            } catch (err) {}
        }
    }

    // 设置大家电分区提示
    function setDJDTips(cfg) {
        if (!/jt=/.test(location.href)) return false

        var val = G.serializeUrl(location.href).param.jt
        var result = ''

        if (val === '10')
            // result = '由于您选择的地区京东自营暂时无货或不支持配送，已为您切换为第三方商家的相似商品，请关注。'
        if (val === '11' || val === '00')
            // result = '已为您切换在该地区售卖的相似商品，请关注。'
        if (val === '01')
            // result = '由于您选择的地区第三方商家暂时无货或不支持配送，已为您切换为京东自营相似商品，请关注。 '

        if (result) {
            $('.itemInfo-wrap').prepend(
                '<div class="DJD-tips">{0} <i onclick="$(\'.DJD-tips\').remove()" class="sprite-close">&times;</i></div>'.format(
                    result
                )
            )
        }
    }

    // 扩展语言功能
    function extendLang() {
        // String.format
        if (!String.prototype.format) {
            String.prototype.format = function() {
                var args = arguments
                return this.replace(/{(\d+)}/g, function(match, number) {
                    return typeof args[number] != 'undefined'
                        ? args[number]
                        : match
                })
            }
        }
    }

    function init(cfg) {
        extendLang()
        setGlobals(cfg)
        getLoginStatus()
        // setDefaultScrollTop()
        setDJDTips(cfg)
    }
    module.exports.__id = 'common'
    module.exports.init = init
})
