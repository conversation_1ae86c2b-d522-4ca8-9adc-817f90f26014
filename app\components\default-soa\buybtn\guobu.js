/**
 * 【0625迭代】PC国补资格领取-子PRD
 * 弹窗UI：https://relay.jd.com/file/design?id=1916737231653969921&page_id=0%3A2&mode=dev
 * 需求文档：https://joyspace.jd.com/pages/lyQyplK7JsCpUZSXVJtg
 * 接口文档：
 * AUTHOR: Henry
 **/

define(function (require, exports, module) {

    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Tools = require('MOD_ROOT/common/tools/tools');
    var isTest = location.href.indexOf('debug=1') > -1

    // 定义全局变量，用于存储地址信息
    var addressInfo = {
        source: "pc_details", // 来源
        skuId: "",  // 商品ID
        categoryId: '', // 三级品类ID
        provinceId: 1,
        provinceName: "北京",
        cityId: 72,
        cityName: "朝阳区",
        countyId: 55653,
        townId: 0,
    }

    // 定义全局变量，用于存储地址字段映射关系
    var addressMap = [
        "addressId",
        "provinceId",
        "provinceName",
        "cityId",
        "cityName",
        "countyId",
        "townId",
    ]

    // 监听iframe地址Orange
    var iframeOrigin = ''
    // iframe加载状态，是否加载完成，默认false
    var iframeLoadStatus = false;
    // 商品id
    var skuId = '';

    // 通信过期时间
    var delayTime = 500; 
    // 延迟定时器
    var delayTimer = null; 

    /**
     * 初始化函数
     *
     * @param {Object} config - 配置对象
     */
    function init(config) {
        // 获取商品id
        skuId = config.skuid;

        // 监听融合接口数据、地址信息
        onStockData()

        // 监听postMessage消息
        onMessages();

        // 设置地址信息
        setAddressInfo(config);

    }


    /**
     * 获取融合数据并监听地区信息
     */
    function onStockData() {

        // 监听融合接口数据
        Event.addListener('onStockReady', function (data) {
            var res = data && data.stock && data.stock.data
            var govSupportInfo = res && res.govSupportInfo || {}; // 国补数据

            // 兼容服务端未返回地址
            if(!govSupportInfo.noSubsidyUrl) {
                govSupportInfo.noSubsidyUrl = '//gov-subsidy.jd.com'
            }

            // 兼容服务端返回地址携带路径场景
            var oA = document.createElement('a')
            oA.href = govSupportInfo.noSubsidyUrl;

            // 获取origin、兼容服务端返回地址携带路径场景
            iframeOrigin = oA.origin || govSupportInfo.noSubsidyUrl;

            oA = null;

        });


        // 监听地区信息
        Event.addListener("onAreaInfo", function (local, area) {

            // 存储地址信息
            if (local && local.area) {
                
                // 遍历地址字段映射关系，存储地址信息
                for (var i = 0; i < addressMap.length; i++) {
                    if (local.area[addressMap[i]]) {
                        addressInfo[addressMap[i]] = local.area[addressMap[i]]
                    }
                }

                // 处理经纬度 "longitude", "latitude",
                if (local.area.commonAreaData) {
                    addressInfo.longitude = local.area.commonAreaData.longitudeString || '';
                    addressInfo.latitude = local.area.commonAreaData.latitudeString || '';
                }
            }
        });
    }

    /**
     * 监听postMessage消息
     *
     * @returns 无返回值
     */
    function onMessages() {

        // 监听postMessage消息
        window.addEventListener('message', function (event) {

            // console.log('监听postMessage消息', event.data, event.origin);
            try {

                // 校验来源域名是否正确
                if (
                    event && event.origin == iframeOrigin || isTest
                ) {

                    // 解析消息数据对象
                    if (event.data) {
                        useMessage(event.data)
                    }
                }

            } catch (e) {
                console.error('监听postMessage消息出错了', e);
            }
        });
    }

    /**
     * 使用消息处理函数
     *
     * @param {Object} data 消息数据对象
     * @param {Object} data.data 消息的具体数据
     * @param {string} data.type 消息类型
     */
    function useMessage(data) {
        if (!data) return;
        if (!data.type) return;

        var messageData = data.data || {};
        
        switch (data.type) {
            // iframe加载完成
            case 'pcDetailsLoadFinish':

                // iframe加载完成
                iframeLoadStatus = true;
                // 曝光埋点
                Tools.exposure({
                    functionName: 'GuobuLayer_Expo',
                    exposureData: ['mainskuid'],
                    extraData: {
                        getstatus: messageData.receive || '0',
                        mainskuid: String(skuId),
                        position_name: '0'
                    },
                    errorTips: '国补领取弹层曝光出错'
                })
                break;

            // 关闭弹窗
            case 'pcDetailsClose':

                // 关闭弹窗
                hideDialog()
                // 更新地址数据
                updateAddress(messageData)
                // 清除延迟定时器
                if (delayTimer) {
                    clearTimeout(delayTimer);
                    delayTimer = null;
                }
                // 点击埋点
                Tools.landmine({
                    
                    functionName: 'GuobuLayer_CloseClick',
                    exposureData: ['mainskuid'],
                    extraData: {
                        getstatus: messageData.receive || '0',
                        mainskuid: String(skuId),
                        position_name: '0'
                    },
                    errorTips: '国补领取弹层点击关闭埋点出错'
                })
                break;

            // 跳转新建地址
            case 'pcDetailsCreateAddress':

                // 跳转新建地址
                window.open('https://easybuy.jd.com/address/getEasyBuyList.action')
                break;
        }
    }

    /**
     * 发送postMessage消息
     *
     * @param {string} type 消息类型
     * @param {Object} [data] 消息数据，默认为空对象
     */
    function sendMessage(type, data) {

        var oIframe = document.querySelector('#guobu-iframe')

        if (oIframe && oIframe.contentWindow) {
            var message = {
                type: type,
                data: data || {}
            };
            // 发送消息
            oIframe.contentWindow.postMessage(message, '*');

        } else {
            console.error('sendMessage未找到iframe元素或未检测到oIframe.contentWindow')
        }

    }

    /**
     * 更新地址数据
     *
     * @param {Object} messageData - 包含地址信息的对象
     */
    function updateAddress(messageData) {
        seajs.use('MOD_ROOT/address/address', function (Address) {
            
            var addressData = messageData.address;
            
            if(addressData && Object.keys(addressData).length > 0) {
                // 更新地址
                Address.updateArea(addressData)
            } else {
                // 调用融合接口，更新页面信息
                Address.callStock()
            }
            
        });
    }

    /**
     * 对外暴露方法：添加提示条
     * @param {String} text
     */
    function addTipBar(text) {
        // 创建提示条，并插入到页面中
        var goBtn = "<span class='receive'>立即领取<img src='https://img12.360buyimg.com/imagetools/jfs/t1/284952/20/27180/366/680f70cfF077f3af0/d1e80428664ebb09.png'></span>"
        var __html = "<div class='DJD-tips state-subsidy' id='J_TipBar' style='cursor: pointer'>" + text + goBtn + "</div>";
        $(".choose-btns-wrapper").prepend(__html);
    }


    /**
     * 对外暴露方法：绑定提示栏的点击事件
     *
     * 当点击提示栏时，会弹出一个对话框显示指定URL的内容
     */
    function bindTipBarEvent(noSubsidyUrl) {

        if (!noSubsidyUrl) return console.error('绑定提示栏的点击事件出错，未找到noSubsidyUrl');

        // 绑定点击事件
        $("#J_TipBar")
            .unbind('click')
            .bind('click', function () {
                showDialog(noSubsidyUrl)
            });
    }


    /**
     * 对外暴露方法：显示弹窗
     *
     * @param url 要显示的页面的URL
     */
    function showDialog(url) {
        $('body').dialog({
            width: 480,
            title: '',
            left: 'auto',
            right: 0,
            height: '100vh',
            type: 'html',
            // maskClose: true,
            closeButton: false,
            extendMainClass: "right-drawer-dialog",
            mainId: "right-drawer-dialog",
            source: createIframeHtml(url),
            onReady: function () {

                // 显示弹窗后，修改遮罩层、滚动条
                $('#right-drawer-dialog').addClass('show-dialog')
                $('.ui-mask').css({ opacity: '0.7' })
                document.documentElement.style.overflow = 'hidden';

                // 点击蒙层，通信给iframe页面
                $('.ui-mask')
                    .unbind('click')
                    .bind('click', function () {

                        // 判断iframe是否加载完成，如果已经加载完成，则发送关闭消息给iframe页面
                        if (iframeLoadStatus) {
                            sendMessage('pcDetailsParentClose', {})

                            // 防止因通信失败，造成弹窗无法关闭问题
                            delayTimer = setTimeout(function () {
                                hideDialog()
                                // 整站状态更新
                                location.reload()
                            }, delayTime)
                        } else {
                            hideDialog()
                        }
                    })
            },
            onBeforeClose: function () {

                // 恢复遮罩层、滚动条
                $('#right-drawer-dialog').removeClass('show-dialog')
                document.documentElement.style.overflow = 'unset';
            }
        })
    }


    /**
     * 关闭弹窗和遮罩层
     */
    function hideDialog() {
        // 关闭弹窗、隐藏遮罩层
        $('#right-drawer-dialog').removeClass('show-dialog')
        $('.ui-mask').css({ opacity: '0' })

        // 隐藏滚动条，恢复页面滚动状态
        document.documentElement.style.overflow = 'unset';

        // 延迟关闭弹窗，防止动画未完成就关闭了
        setTimeout(function () {
            $.closeDialog()
        }, 500)
    }


    /**
     * 根据给定的URL生成包含iframe的HTML字符串
     *
     * @param {string} url - 要嵌入iframe的URL
     * @returns {string} 包含iframe的HTML字符串
     */
    function createIframeHtml(url) {
        var iframeTpl = ''

        if(isTest){
            url = 'https://gov-subsidy-dev.jd.com'
        }

        if (url) {
            url = url + "/pages/details/index"
            // 处理iframe url 拼接逻辑
            url = urlForamt(url, addressInfo);

            // 生成dialog内容
            iframeTpl = '<iframe id="guobu-iframe" width="100%" height="100%" allow="geolocation" src="' + url + '" frameborder="0" ></iframe>'
        } else {
            iframeTpl = '<div>未检测到url信息</div>'
        }

        // 生成dialog模板
        return iframeTpl
    }

    /**
     * 将URL和查询数据格式化为完整的URL字符串
     *
     * @param {string} url 基础URL地址
     * @param {Object} queryData 查询数据对象
     * @returns {string} 完整的URL字符串
     */
    function urlForamt(url, queryData) {
        var result = url

        if (url && queryData && Object.keys(queryData).length > 0) {
            var queryString = Object.keys(queryData).map(function (key) {
                return encodeURIComponent(key) + '=' + encodeURIComponent(queryData[key]);
            }).join('&');

            result += url.indexOf('?') > -1 ? '&' + queryString : '?' + queryString;
        }

        return result;
    }

    /**
     * 设置地址信息
     *
     * @param config 配置对象
     */
    function setAddressInfo(config) {
        
        // 设置商品ID
        addressInfo.skuId = skuId

        // 设置品类ID, 示例：cid1_cid2_cid3
        if (
            typeof pageConfig != 'undefined'
            && pageConfig.product
            && Array.isArray(pageConfig.product.cat)) {

            addressInfo.categoryId = pageConfig.product.cat.join('_')
        }
    }


    exports.init = init;
    exports.addTipBar = addTipBar;
    exports.bindTipBarEvent = bindTipBarEvent;
    exports.showDialog = showDialog;


});




