/*
 * author:<EMAIL>
 * date:2016-12-13
 * ver:v1.0.0
 */
!function(a) {
        var c = "J_imgLazyload",
            d = "original",
            e = [];
        function f() {
            e = a("body ." + c)
        }
        function g() {
            a(window).scrollTop() + a(window).height();
            for (var g = 0,
                     h = e.length; h > g; g++) {
                var i = a(e[g]);
                // try {
                    i.removeClass(c),
                        i.attr("src", i.attr(d)),
                        i.removeAttr(d)
                // } catch() {}
            }
        }
        function h() {
            f(),
                g()
        }
        a(function() {
            h()
        })
    } (jQuery, window);
function thick_login(a, b) {
    if ("function" != typeof a) throw new Error("Self-defined login arguments should be a function!");
    seajs.use("jdf/1.0.0/unit/login/1.0.0/login",
        function(c) {
            c({
                modal: !0,
                complete: function(c) {
                    a.call(b || this, c)
                }
            })
        })
} !
    function(a, b) {
        b.jshop = {},
            b.jshop.module = {},
            a.extend(b.jshop.module, {
                /**
                 * @function：让窗口大小发生变化的时候，指定的html内容水平居中
                 * @author：2016-8-22 cdluxy
                 */
                contentAutoCenter: function(){

                    var target = this;

                    //先判断这个方法有没有初始化过，没有则先初始化
                    if(!$(window).data('bindContentAutoCenter')){

                        $(window).data('bindContentAutoCenter', true);
                        window.arrContentAutoCenterTarget = [];

                        $(window).bind('resize.autoCenter', function(){
                            var arr = window.arrContentAutoCenterTarget;
                            //html片段宽度自适应居中代码
                            for(i = 0; i < arr.length; i++){
                                var jItem = $(arr[i]),
                                    hasUnload = false;

                                jItem.find('img').each(function(){
                                    if(this.complete !== true){
                                        hasUnload = true;
                                        this.onload = function(){
                                            $(window).trigger('resize.autoCenter');
                                        };
                                    }
                                });

                                //如果内部没有图片需要等待加载完成，则直接获取内部宽度然后执行居中设置
                                if(!hasUnload){
                                    jItem.addClass('fn-left');
                                    var itemWidth = jItem.width(),
                                        wrapWidth = jItem.parent().width();
                                    jItem.css('margin-left', ((wrapWidth < 990? 990: wrapWidth) - itemWidth) / 2);
                                    jItem.removeClass('fn-left');
                                }
                            };
                        });
                    }

                    //判断当前模块是否已经处在带处理的数组中，没有则添加进去
                    var has = false;
                    for(var i = 0, arr = window.arrContentAutoCenterTarget; i < arr.length; i++){
                        if(arr[i] === target){
                            has = true;
                            break;
                        }
                    }
                    if(!has){
                        arr.push(target);
                    }

                    //触发一次自动居中
                    $(window).trigger('resize.autoCenter');
                },
                /**
                 * @function：自定义内容区的居中方法
                 * @description：根据用户配置的节点信息，主动为用户居中
                 * @param：示例模板写法： <div class="j-module" module-function="autoCenter" module-param="{}"></div>
                 * @Author：<EMAIL> 2016/12/9
                 */
                autoCenter : function(args){
                    var _this = $(this),
                        param = $.extend({autoMiddleNode:'.userDefinedArea'} , args || {}),
                        node = _this.find(param.autoMiddleNode);

                    alignCenter();
                    $(window).resize(function(){
                        alignCenter();
                    });

                    function alignCenter(){
                        var extra = node.width()-_this.width();
                        if(extra>0){
                            node.css({'margin-left':-extra/2});
                        }else{
                            node.css('margin','0 auto');
                        }
                    }
                },
                shopSearch: function(a) {
                    jshop.module.search.shopSearch.call(this, a)
                },
                follow: function() {
                    var b = {
                        shopApi: "//follow.soa.jd.com/vender/follow",
                        actApi: "//follow.soa.jd.com/activity/follow",
                        shopCountApi: "//follow.soa.jd.com/vender/queryForCount",
                        actCountApi: "//follow.soa.jd.com/activity/queryForCount",
                        goodsApi: "",
                        type: "shopId"
                    };
                    function c() {
                        var a = jQuery("#newTag").val();
                        a = a.trim(),
                        a == jQuery("#newTag").attr("placeholder") && jQuery("#newTag").val("")
                    }
                    function d(a) {
                        a.value.length > 10 && (a.value = a.value.substring(0, 10))
                    }
                    function e(b, c) {
                        this.config = jQuery.extend({},
                            b);
                        var d = jQuery.extend({},
                            this.config);
                        this.container = a(d.node, c),
                            this.get = function(a) {
                                return d[a]
                            },
                            this.set = function(a, b) {
                                d[a] = b
                            },
                            this.init()
                    }
                    return e.FollowVMContent = '<div id="whole"><div id="followSuccessDiv"><div class="tips" id="success"> <h2>\u5173\u6ce8\u6210\u529f\uff01</h2><p><em id="followNum"></em><a target="_blank" href="//t.jd.com/vender/followVenderList.action">\u67e5\u770b\u6211\u7684\u5173\u6ce8&gt;&gt;</a></p></div><div id="attention-tags"><div class="mt"><h4>\u9009\u62e9\u6807\u7b7e<em>\uff08\u6700\u591a\u53ef\u90093\u4e2a\uff09</em></h4><div class="extra"></div></div><div class="mc"><div id="followTags"></div><div class="att-tag-btn"><a href="javascript:void(0);" class="att-btn-ok">\u786e\u5b9a</a><a class="att-btn-cancal" href="javascript:$.closeDialog()">\u53d6\u6d88</a><span id="follow_error_msg"  class="att-tips fl"></span></div></div></div></div><div id="followTopicSuccessDiv"><div id="att-mod-success"><div class="att-img fl"><img src="//misc.360buyimg.com/201007/skin/df/i/icon_correct.jpg" alt=""></div><div class="att-content"><h2>\u5173\u6ce8\u6210\u529f</h2><p><em id="followTopicNum" ></em><a target="_blank" href="//t.jd.com/activity/followActivityList.action">\u67e5\u770b\u6211\u7684\u5173\u6ce8 &gt;&gt;</a></p></div><div class="att-tag-btn"><a class="att-btn-cancal" href="javascript:;" onclick="$.closeDialog()">\u5173\u95ed</a></div></div></div><div id="followFailDiv" ><div id="att-mod-again"><div class="att-img fl"><img src="//misc.360buyimg.com/201007/skin/df/i/icon_sigh.jpg" alt=""></div><div class="att-content"><h2>\u5173\u6ce8\u5931\u8d25</h2><p><a id=\'followFailSeeFollowUrl\' target="_blank" href="//t.jd.com/vender/followVenderList.action">\u67e5\u770b\u6211\u7684\u5173\u6ce8 &gt;&gt;</a></p></div><div class="att-tag-btn"><a class="att-btn-cancal" href="javascript:$c.closeDialog()">\u5173\u95ed</a></div></div></div><div id="followMaxDiv"><div id="att-mod-again"><div class="att-img fl"><img src="//misc.360buyimg.com/201007/skin/df/i/icon_sigh.jpg" alt=""></div><div class="att-content"><h2>\u5173\u6ce8\u6570\u91cf\u8fbe\u5230\u6700\u5927\u9650\u5236</h2><p><a id=\'followMaxSeeFollowUrl\' target="_blank" href="//t.jd.com/vender/followVenderList.action">\u67e5\u770b\u6211\u7684\u5173\u6ce8 &gt;&gt;</a></p></div><div class="att-tag-btn"><a class="att-btn-cancal" href="javascript:$.closeDialog()">\u5173\u95ed</a></div></div></div><div id="followedDiv"><div id="att-mod-again"><div class="att-img fl"><img src="//misc.360buyimg.com/201007/skin/df/i/icon_sigh.jpg" alt=""></div><div class="att-content"><h2 id="followedTitle"></h2><p><em id="followedNum"></em><a id=\'followedSeeFollowUrl\' target="_blank" href="">\u67e5\u770b\u6211\u7684\u5173\u6ce8 &gt;&gt;</a></p></div><div class="att-tag-btn"><a class="att-btn-cancal" href="javascript:$.closeDialog()">\u5173\u95ed</a></div></div></div></div>',
                        e.prototype = {
                            init: function() {
                                var a = this;
                                a.bindEvent()
                            },
                            bindEvent: function() {
                                var a = this;
                                a.container.click(function() {
                                    thick_login(function() {
                                        a.addFollow()
                                    })
                                }),
                                    jQuery("body").delegate(".att-btn-ok", "click",
                                        function() {
                                            a.doSubmit()
                                        })
                            },
                            addFollow: function() {
                                var b, a = this;
                                "shopId" == a.get("type") ? (b = a.get("shopApi"), b += "?venderId=" + a.get("id")) : (b = a.get("actApi"), b += "?activityId=" + a.get("id"), b += "&srcType=0"),
                                    e.followVM = jQuery(e.FollowVMContent),
                                    jQuery.ajax({
                                        async: !1,
                                        url: b,
                                        dataType: "jsonp",
                                        success: function(b) {
                                            a.followSuccess(b)
                                        },
                                        error: function() {
                                            a.followShopFail()
                                        }
                                    })
                            },
                            followSuccess: function(a) {
                                this.checkResult(a, this.followSuccessCallBack, this.followed, this.followShopMax)
                            },
                            checkResult: function(a, b, c, d) {
                                switch (a.code) {
                                    case "F10000":
                                        b && b.call(this);
                                        break;
                                    case "F0409":
                                        c && c.call(this);
                                        break;
                                    case "F0410":
                                        d && d.call(this);
                                        break;
                                    default:
                                        this.followShopFail.call(this)
                                }
                            },
                            followSuccessCallBack: function() {
                                var b, a = this,
                                    c = a.get("type");
                                b = a.get("shopId" == c ? "shopCountApi": "actCountApi"),
                                    a.getFollowNum(b,
                                        function(b) {
                                            if ("F10000" == b.code) {
                                                var d;
                                                "shopId" == c ? (d = "\u60a8\u5df2\u5173\u6ce8" + b.data + "\u4e2a\u5e97\u94fa", e.followVM.find("#followNum").html(d)) : (d = "\u60a8\u5df2\u5173\u6ce8" + b.data + "\u4e2a\u6d3b\u52a8", e.followVM.find("#followTopicNum").html(d))
                                            }
                                            "shopId" == c ? a.getFollowTags() : a.ShowFollowTopicSuc()
                                        })
                            },
                            getFollowNum: function(a, b) {
                                var c = this;
                                jQuery.ajax({
                                    async: !1,
                                    url: a,
                                    dataType: "jsonp",
                                    success: function(a) {
                                        b && b(a)
                                    },
                                    error: function() {
                                        c.followShopFail()
                                    }
                                })
                            },
                            getFollowTags: function() {
                                var a = this;
                                jQuery.ajax({
                                    async: !1,
                                    url: "//follow.soa.jd.com/vender/queryTagForListByCount?count=5",
                                    dataType: "jsonp",
                                    success: function(b) {
                                        a.fillInTags(b),
                                            a.ShowFollowSuc()
                                    },
                                    error: function() {
                                        a.followShopFail()
                                    }
                                })
                            },
                            fillInTags: function(a) {
                                var c = a,
                                    d = c.data.length,
                                    f = "";
                                f += "<ul id='oldTags' class='att-tag-list'>";
                                var g;
                                for (var h = 0; d > h; h++) g = c.data[h],
                                    g = decodeURIComponent(g),
                                    f += "<li><a href='javascript:void(0)' class='J_ChooseTagOne'>" + g + "</a></li>";
                                f += "</ul>",
                                    f += "<ul id='newTags' class='att-tag-list att-tag-list-save'>",
                                    f += "<li id='att-tag-new' class='att-tag-new'><input id='newTag' type='text' placeholder='\u81ea\u5b9a\u4e49' maxlength='10'><span id='J_SaveTagOneBtn'>\u6dfb\u52a0</span></li>",
                                    f += "</ul>",
                                    f += "",
                                    e.followVM.find("#followTags").html(f)
                            },
                            ShowFollowSuc: function() {
                                var a = this,
                                    b = "\u63d0\u793a",
                                    f = jQuery("#dialogDiv");
                                f.html('<a id="dialogA" href="#"></a>'),
                                    $("body").dialog({
                                        width: 510,
                                        height: 260,
                                        title: b,
                                        type: "html",
                                        source: e.followVM.find("#followSuccessDiv").html(),
                                        onReady: function() {
                                            {
                                                var b = jQuery("#btn_coll_shop_pop");
                                                jQuery("#attention-tags").find(".mc")
                                            }
                                            b.find(".thickcon").css("height", "auto"),
                                                b.css("height", "auto"),
                                                jQuery("#newTag").val(jQuery("#newTag").attr("placeholder")),
                                                jQuery("#newTag").unbind("focus").focus(function() {
                                                    c()
                                                }),
                                                jQuery("#newTag").unbind("keyup").keyup(function() {
                                                    d(this)
                                                }),
                                                jQuery(".J_ChooseTagOne").unbind("click").click(function() {
                                                    a.chooseTag.call(a, this)
                                                }),
                                                jQuery("#J_SaveTagOneBtn").unbind("click").click(function() {
                                                    a.saveNewTag.call(a)
                                                })
                                        }
                                    })
                            },
                            ShowFollowTopicSuc: function() {
                                var a = "\u63d0\u793a",
                                    b = jQuery("#dialogDiv");
                                b.html('<a id="dialogA" href="#"></a>'),
                                    $("body").dialog({
                                        width: 300,
                                        height: 80,
                                        title: a,
                                        type: "html",
                                        source: e.followVM.find("#followTopicSuccessDiv").html()
                                    })
                            },
                            followShopFail: function() {
                                var a = jQuery("#dialogDiv");
                                a.html('<a id="dialogA" href="#"></a>'),
                                    "shopId" == this.get("type") ? e.followVM.find("#followFailSeeFollowUrl").attr("href", "//t.jd.com/vender/followVenderList.action") : e.followVM.find("#followFailSeeFollowUrl").attr("href", "//t.jd.com/activity/followActivityList.action"),
                                    $("body").dialog({
                                        width: 300,
                                        height: 80,
                                        type: "html",
                                        title: "\u63d0\u793a",
                                        source: e.followVM.find("#followFailDiv").html()
                                    })
                            },
                            followShopMax: function() {
                                var a = jQuery("#dialogDiv");
                                a.html('<a id="dialogA" href="#"></a>'),
                                    "shopId" == this.get("type") ? e.followVM.find("#followMaxSeeFollowUrl").attr("href", "//t.jd.com/vender/followVenderList.action") : e.followVM.find("#followMaxSeeFollowUrl").attr("href", "//t.jd.com/activity/followActivityList.action"),
                                    $("body").dialog({
                                        width: 300,
                                        height: 80,
                                        type: "html",
                                        title: "\u63d0\u793a",
                                        source: e.followVM.find("#followMaxDiv").html()
                                    })
                            },
                            followed: function() {
                                var c, b = "",
                                    d = this.get("type");
                                "shopId" == d ? (b = "\u5df2\u5173\u6ce8\u8fc7\u8be5\u5e97\u94fa", c = "//follow.soa.jd.com/vender/queryForCount", e.followVM.find("#followedSeeFollowUrl").attr("href", "//t.jd.com/vender/followVenderList.action")) : (b = "\u5df2\u5173\u6ce8\u8fc7\u8be5\u6d3b\u52a8", c = "//follow.soa.jd.com/activity/queryForCount", e.followVM.find("#followedSeeFollowUrl").attr("href", "//t.jd.com/activity/followActivityList.action")),
                                    e.followVM.find("#followedTitle").html(b),
                                    this.getFollowNum(c,
                                        function(a) {
                                            if ("F10000" == a.code) {
                                                var b;
                                                b = "shopId" == d ? "\u60a8\u5df2\u5173\u6ce8" + a.data + "\u4e2a\u5e97\u94fa": "\u60a8\u5df2\u5173\u6ce8" + a.data + "\u4e2a\u6d3b\u52a8",
                                                    e.followVM.find("#followedNum").html(b)
                                            }
                                            var c = jQuery("#dialogDiv");
                                            c.html('<a id="dialogA" href="#"></a>'),
                                                $("body").dialog({
                                                    width: 300,
                                                    height: 80,
                                                    type: "html",
                                                    title: "\u63d0\u793a",
                                                    source: e.followVM.find("#followedDiv").html()
                                                })
                                        })
                            },
                            doSubmit: function() {
                                var b = this,
                                    c = "",
                                    d = 0;
                                if (jQuery("#oldTags").find("a").each(function() {
                                        "true" == jQuery(this).attr("isCheck") && (d++, c = "" == c ? jQuery(this).html() : c + "," + jQuery(this).html())
                                    }), jQuery("#newTags").find("a").each(function() {
                                        "true" == jQuery(this).attr("isCheck") && (d++, c = "" == c ? jQuery(this).html() : c + "," + jQuery(this).html())
                                    }), "" == c) return void b.showErrorMsg("\u8bf7\u81f3\u5c11\u63d0\u4f9b1\u4e2a\u6807\u7b7e");
                                if (d > 3) return void b.showErrorMsg("\u6700\u591a\u53ef\u9009\u62e93\u4e2a\u6807\u7b7e");
                                c = encodeURIComponent(c);
                                var e = "//follow.soa.jd.com/vender/editTag";
                                jQuery.ajax({
                                    async: !1,
                                    url: e,
                                    dataType: "jsonp",
                                    data: {
                                        venderId: b.get("id"),
                                        tagNames: c
                                    },
                                    success: function(c) {
                                        "F10000" == c.code ? (jQuery("#follow_error_msg").removeClass(), jQuery("#follow_error_msg").addClass("hl_green fl"), jQuery("#follow_error_msg").html("\u8bbe\u7f6e\u6210\u529f"), jQuery("#follow_error_msg").show(), setTimeout(function() {
                                                a.closeDialog()
                                            },
                                            5e3)) : b.showErrorMsg("F0410" == c.code ? "\u8bbe\u7f6e\u7684\u6807\u7b7e\u6570\u8d85\u8fc7\u6700\u5927\u9650\u5236": "\u8bbe\u7f6e\u5931\u8d25")
                                    },
                                    error: function() {
                                        b.showErrorMsg("\u8bbe\u7f6e\u5931\u8d25")
                                    }
                                })
                            },
                            showErrorMsg: function(a) {
                                jQuery("#follow_error_msg").removeClass(),
                                    jQuery("#follow_error_msg").addClass("att-tips fl"),
                                    jQuery("#follow_error_msg").html(a),
                                    jQuery("#follow_error_msg").show(),
                                    setTimeout(function() {
                                            jQuery("#follow_error_msg").hide()
                                        },
                                        3e3)
                            },
                            saveNewTag: function() {
                                var a = this,
                                    b = jQuery("#newTag").val();
                                if (b = b.trim(), b.trim().length > 10) return void this.showErrorMsg("\u957f\u5ea6\u4e0d\u80fd\u8d85\u8fc710\u4e2a\u5b57\u7b26");
                                var c = this.validateNewTag(b);
                                if (!c) return void this.showErrorMsg("\u6807\u7b7e\u6570\u5b57\u3001\u5b57\u6bcd\u3001\u6c49\u5b57\u7ec4\u6210");
                                if ("" == b || b == jQuery("#newTag").attr("placeholder")) return this.showErrorMsg("\u8bf7\u8f93\u5165\u81ea\u5b9a\u4e49\u540d\u79f0\uff01"),
                                    void jQuery("#newTag").val(jQuery("#newTag").attr("placeholder"));
                                jQuery("<li isNewAdd='true' ><a class='current' href='javascript:void(0)' isCheck='true' >" + b + "</a></li>").insertBefore(jQuery("#att-tag-new"));
                                var d = jQuery("li[isNewAdd]"),
                                    e = jQuery("li[isNewAdd] > .current");
                                d.length >= 3 && jQuery("#att-tag-new").attr("style", "display:none"),
                                    e.unbind("click").click(function() {
                                        a.chooseTag(this)
                                    }),
                                    jQuery("#newTag").val(jQuery("#newTag").attr("placeholder"))
                            },
                            chooseTag: function(a) {
                                var b = jQuery(a),
                                    c = b.attr("isCheck");
                                "undefined" == typeof c || "false" == c ? (b.attr("isCheck", "true"), b.addClass("current")) : (b.attr("isCheck", "false"), b.removeClass("current"))
                            },
                            validateNewTag: function(a) {
                                var b = /[\u4e00-\u9fa5]|[0-9]|[a-z]|[A-Z]/g;
                                var c = a.match(b);
                                var d = 0;
                                return null != c && (d = c.length),
                                    d != a.length ? !1 : !0
                            },
                            getFollowedCount: function(a) {
                                jQuery.ajax({
                                    async: !1,
                                    url: "//follow.soa.jd.com/vender/queryForCountByVid",
                                    dataType: "jsonp",
                                    data: {
                                        venderId: a
                                    },
                                    success: function(a) {
                                        var b = a.data;
                                        b > 500 && (b > 1e4 && (b = parseInt(b / 1e4), b += "\u4e07"), jQuery("#followedCount").html(b))
                                    },
                                    error: function() {}
                                })
                            }
                        },
                        function(c) {
                            var d = a.extend({},
                                b, c);
                            new e(d, a(this))
                        }
                } ()
            })
    } (jQuery, window),
    function(a) {
        jshop.module.search = {},
            a.extend(jshop.module.search, {
                shopSearch: function(b) {
                    var c = {
                            priceDefaultClass: "input.inputSmall",
                            newKeyWord: "input.inputMiddle",
                            searchButton: "button",
                            jHotwords: ".jHotwords a"
                        },
                        d = {
                            defKeyword: "",
                            keyword: "",
                            isShowPriceSift: "",
                            cmsModId: "",
                            appId: "",
                            venderId: "",
                            categoryId: "",
                            shopId: ""
                        },
                        f = a(this).find(c.priceDefaultClass),
                        h = a(this),
                        i = "-1-1-24.html";
                    for (var j in d) if ("undefined" == typeof b[j]) return void alert("\u5e97\u5185\u641c\u7d22\u6a21\u5757module-param\u4e2d\u7f3a\u5c11\u53c2\u6570\uff1a" + j);
                    a.extend(d, b),
                        a.extend(c, d);
                    var k = c.defKeyword;
                    var l = c.prefixUrl + "/view_search-" + c.appId + "-" + c.venderId + "-" + c.shopId + "-" + c.categoryId + "-5";
                    h.find(c.searchButton).click(function() {
                        q(null, c.cmsModId)
                    });
                    function m() {
                        h.find(c.newKeyWord).focus(function() {
                            var b = a(this).val();
                            k == b && a(this).val("")
                        }).keydown(function(a) {
                            n(a)
                        })
                    }
                    function n(a) {
                        var a = window.event || a,
                            b = a.keyCode;
                        13 == b && q(null, c.cmsModId)
                    }
                    function o() {
                        h.find(c.jHotwords).each(function(b, d) {
                            var e = a(d).html();
                            a(d).removeAttr("onclick").click(function() {
                                q(e, c.cmsModId)
                            })
                        })
                    }
                    function p() {
                        f.each(function(b, c) {
                            a(c).keydown(function(b) {
                                a(c).val();
                                n(b)
                            }).blur(function() {
                                if (2 == f.length) {
                                    var a = f.eq(0).val(),
                                        b = f.eq(1).val();
                                    "" == a && f.eq(0).val("\uffe5"),
                                    "" == b && f.eq(1).val("\uffe5")
                                }
                            }).change(function() {
                                var b = a(c).val();
                                var d = b.replace(/\D/g, "");
                                a(c).val(d)
                            }).keyup(function() {
                                var b = a(c).val();
                                var d = b.replace(/\D/g, "");
                                a(c).val(d)
                            }).afterpaste(function() {
                                var b = a(c).val();
                                var d = b.replace(/\D/g, "");
                                a(c).val(d)
                            })
                        })
                    }
                    function q(a, b) {
                        var d, e = null,
                            g = null,
                            j = null;
                        if (2 == f.length && (g = f.eq(0).val(), j = f.eq(1).val()), d = jQuery("#isGlobalSearch" + b).attr("checked") ? 1 : 0, "true" == c.isShowPriceSift) null != g && null != j && (g = g.replace("\uffe5", ""), ("" == g || "\uffe5" == g) && (g = 0), j = j.replace("\uffe5", ""), ("" == j || "\uffe5" == j) && (j = 0));
                        else {
                            var g = 0;
                            var j = 0
                        }
                        null == g && null == j && (g = 0, j = 0);
                        var k = l + "-" + g + "-" + j + i;
                        if (null != a) e = a;
                        else {
                            var m = h.find(c.newKeyWord).attr("d");
                            newKeywordVal = h.find(c.newKeyWord).val(),
                                e = newKeywordVal != m && "" != newKeywordVal ? newKeywordVal: ""
                        }
                        e = encodeURIComponent(e),
                            e = encodeURIComponent(e),
                            k = k + "?keyword=" + e + "&isGlobalSearch=" + d,
                            window.location.href = k
                    }
                    m(),
                        p(),
                        o()
                }
            })
    } (jQuery),
    function($, w) {
        function _execute(module) {
            var _function = $(module).attr("module-function"),
                _module_name = $(module).parents("[module-name]").attr("module-name"),
                _param;
            if ("undefined" != typeof _function) {
                // try {
                    _param = eval("(" + $(module).attr("module-param") + ")")
                // } catch() {
                //     _param = {}
                // }
                var _functions = _function.split(",");
                $.each(_functions,
                    function(a, b) {
                        jshop.module[_module_name] && jshop.module[_module_name][b] ? _param.subObj ? $(module).find(_param.subObj).each(function(a, c) {
                            jshop.module[_module_name][b].call(c, _param)
                        }) : jshop.module[_module_name][b].call(module, _param) : jshop.module[b] && (_param.subObj ? $(module).find(_param.subObj).each(function(a, c) {
                            jshop.module[b].call(c, _param)
                        }) : jshop.module[b].call(module, _param))
                    })
            }
        }
        $(function() {
            $("div.j-module").each(function(a, b) {
                _execute(b)
            })
        })
    } (jQuery, window);

var S_ifollow = S_ifollow||{};
(function(j, $) {

    var hasPinpai = false;

    //植入关注品牌功能，只适用于店铺
    function followPinpai(shopId){

        $.ajax({
            url : "//follow.soa.jd.com/brand/batchfollow?brandId=" + JSON.stringify([{shopId: shopId}]),
            //url : INTERFACE.batchfollow + JSON.stringify([{shopId: 43858}]),
            dataType : 'jsonp',
            success : function(data){
                if(data.code == 'F10000'){
                    //console.log("关注品牌成功");
                    //if(data.data){
                    //	state = 1;
                    //	domOperate();
                    //}
                }else if(data.code == 'F0402'){
                    //if(!data.data){
                    //	state = 2;
                    //	domOperate();
                    //}
                    //console.log("该品牌已关注");
                }else{
                    //state = 3;
                    //domOperate();
                    //console.log("关注成功失败");
                }
            }
        });

    }

    //不是10000，都是关注失败  by李志博
    //临时状态state ：0未关注；1关注成功；2已经关注；3关注失败
    function domOperate(){

        var attentInfo = {
                mall : {
                    msgOk : '关注成功',
                    msgRepeat : '您已经关注过了',
                    msgError : '关注失败',
                    msgOverMax : '已达到关注店铺数量上限',
                    msgOther : '查看我关注的<a href="//t.jd.com/vender/followVenderList.action" target="_blank">店铺</a>和<a href="//t.jd.com/follow/brand/list.action" target="_blank">品牌</a>'
                }
            },
            state = j.state;

        //取消关注
        if(state == 0){
            node.html('\u5173\u6ce8\u54c1\u724c');
            eventTag = true;
            return;
        }

        var jAttWrap = $(".j-attent-dialog-wrap"),
            mask = jAttWrap.find('.attent-dialog-mask'),
            con = jAttWrap.find('.attent-con'),
            msg = jAttWrap.find('.attent-msg'),
            other = jAttWrap.find('.attent-other'),
            close = jAttWrap.find('.attent-close');

        //关注成功
        if(state == 1){
            msg.html(attentInfo.mall.msgOk);
            other.html(attentInfo.mall.msgOther);
            con.addClass('attent-ok');
        }
        //已经关注
        else if(state == 2){
            msg.html(attentInfo.mall.msgRepeat);
            other.html(attentInfo.mall.msgOther);
            con.addClass('attent-repeat');
        }
        //关注失败
        else if(state == 3){
            msg.html(attentInfo.mall.msgError);
            other.html(attentInfo.mall.msgOther);
            con.addClass('attent-error');
        }
        //达到关注数量上限
        else if(state == 4){
            msg.html(attentInfo.mall.msgOverMax);
            other.html(attentInfo.mall.msgOther);
            con.addClass('attent-error');
        }
        jAttWrap.show();
        eventTag = true;
    }

    j.follow = function(param){

        param = jQuery.extend({
            node : '#shop-attention', //关注点击元素
            shopId: '#shop_id'
        }, param || {});

        var _this = this;

        this.jNode = $(param.node);
        this.followWhat = 1;//关注店铺

        j.state = 0; //0:unfollowed，1:followed
        j.id = $(param.shopId).val();

        setTimeout(function(){
            //全局事件初始化
            event(_this);
        }, 100);

        //绑定点击事件
        this.jNode.bind("click", function(){
            //$(param.pageIdValue).val()
            jdModelCallCenter.settings.fn = function() {
                j.addFollow();//登录后回调函数 。增加关注
            };
            $.login({
                modal: true,
                complete: function(result) {
                    if (result != null && result.IsAuthenticated != null && result.IsAuthenticated) {
                        jdModelCallCenter.settings.fn();//已经登陆后。增加关注
                    }
                }
            });
        });

    };

    //发送关注请求
    j.addFollow = function(){

        var url = "//follow.soa.jd.com/vender/follow";
        url+="?venderId=" + j.id;

        //这里调用关注接口
        $.ajax({
            async: false,//同步调用
            url:url,
            dataType:"jsonp",
            success:function(data){
                j.requestSuccess(data);
            },
            error: function(reques,msg){
                //console.log("关注店铺，请求响应失败");
                //j.followShopFail();
            }
        });

    };

    //关注请求响应成功处理函数
    j.requestSuccess = function(data){
        if( data.code == 'F10000' ){//F10000 成功
            j.followSuccessCallBack();

            var pin = encodeURIComponent(getCookie("pin"));
            if(pin == null){
                return;
            }
            var jda = getCookie("__jda");
            if(jda == null){
                return;
            }
            var uuid = jda.split(".")[1];
            var img = new Image();
            var imgsrc = "//mercury.jd.com/log.gif?t=shop.100001&v=src=shop$"+"shopid="+ j.id +"$action=0&pin=" +pin + "&uid="+ uuid + "&ver=1&m=UA-J2011-1&ref=" + encodeURIComponent(document.referrer) + "&rid=" + Math.random();
            img.setAttribute('src', imgsrc);
        }else if( data.code == 'F0402' ){//F0402 已关注过，不能加关注
            j.followed();
        }else if( data.code == 'F0410' ){//F0410关注达到最大数量，不能加关注
            j.followShopMax();
        }else{			//关注失败
            j.followErrorCallBack();
        }

        //如果此店铺有品牌，则同时关注品牌
        if(hasPinpai){
            followPinpai(j.id);
        }
    };

    //关注成功处理函数
    j.followSuccessCallBack = function(){
        j.state = 1;
        domOperate();
    };

    //关注失败处理函数
    j.followErrorCallBack = function(){
        j.state = 3;
        domOperate();
    }

    /**
     * 获取关注数量
     */
    j.getFollowNum = function(url,followNumSuccessCallBack){
        $.ajax({
            async: false,//同步调用
            url:url,
            dataType:"jsonp",
            success:function(data){
                followNumSuccessCallBack(data);
            },
            error: function(reques,msg){
                //弹出关注失败；
                //console.log("获取关注数量接口响应失败");
            }
        });
    };


    /*$('#dialogA').jdThickBox({
     width: 300,
     height: 80,
     title: '提示',
     source: j.followVM.find('#followFailDiv').html()
     }); */


    //弹出关注达到最大限制；
    j.followShopMax = function(){
        j.state = 4;
        domOperate();
    };


    //弹出已关注
    j.followed = function(){
        j.state = 2;
        domOperate();
    };

    //初始化入口
    !function init(){
        cssInit();
    }();

    function cssInit(){
        var styleStr = "<style>"
            +".j-attent-dialog-wrap{display: none;}"
            +".attent-tip-wrap{display: none; z-index: 1001; position: absolute; top: 0; left: 0; width: 189px; height: 131px; background: url(//img11.360buyimg.com/cms/jfs/t904/96/974597196/7702/c65fa11b/5562e781N10be5ec3.png) no-repeat;}"
            +".attent-tip-wrap i{position: absolute; right: 5px; top: 9px; width: 15px; height: 15px; background: url(//img14.360buyimg.com/cms/jfs/t1087/118/953149406/1062/b1c27ba1/5562e785Ndd770a39.png) no-repeat; cursor: pointer;}"
            +".attent-dialog-mask{position: fixed; _positon: absolute; left:0; top:0; right:0; bottom:0; background:#000; opacity:0.3; z-index:100;}"
            +".attent-dialog{position: fixed; _positon: absolute; width:310px; height:185px; border:solid 5px rgba(8,1,3,0.3); background:#fff; left:50%; top:50%; margin:-92px 0 0 -155px; z-index:1001;}"
            +".attent-dialog.current{display:block;}"
            +".attent-dialog .attent-mt{height:32px; line-height:32px; background:#f5f5f5; font-size:16px; color:#222; text-indent:10px; overflow:hidden;}"
            +".attent-dialog .attent-close{float:right; width:32px; height:32px; text-indent:-9999px; background:url(//img10.360buyimg.com/cms/jfs/t1420/84/156758085/1080/d48a39fe/555e9e79N85386290.png) center center no-repeat; cursor:pointer;}"
            +".attent-dialog .attent-ok, .attent-dialog .attent-repeat, .attent-dialog .attent-error{margin:48px 0 0 55px; height:40px; padding-left:48px;}"
            +".attent-dialog .attent-ok{background:url(//img12.360buyimg.com/cms/jfs/t1435/352/153421548/1347/d377c92d/555e9e71Nb767e906.png) left center no-repeat;}"
            +".attent-dialog .attent-repeat, .attent-dialog .attent-error{background:url(//img14.360buyimg.com/cms/jfs/t1516/358/164942511/1418/e0c25f0c/555e9e75Nfca9da16.png) left center no-repeat;}"
            +".attent-dialog .attent-ok .attent-msg{font-size:14px; color:#009900; font-weight:bold;}"
            +".attent-dialog .attent-repeat .attent-msg, .attent-dialog .attent-error .attent-msg{font-size:14px; color:#ff771e; font-weight:bold;}"
            +".attent-dialog .attent-other{color:#6f6363; display:block; margin-top:3px;}"
            +".attent-dialog .attent-other a{color:#004499; padding: 0 5px;}"
            +".attent-dialog.attent-mall .attent-other a{margin:0 5px;}"
            +"</style>";
        $("head").append(styleStr);
    }

    function event(that){

        var	attentHtml = '<div class="j-attent-dialog-wrap">'
            +'<div class="attent-dialog-mask"></div>'
            +'<div class="attent-dialog">'
            +	'<div class="attent-mt">'
            +		'<span class="attent-close"  title="关闭">关闭</span>'
            +		'<span class="attent-title">提示</span>'
            +	'</div>'
            +	'<div class="attent-mc">'
            +		'<div class="attent-con">'
            +			'<span class="attent-msg"></span>'
            +			'<span class="attent-other"></span>'
            +		'</div>'
            +	'</div>'
            +'</div>'
            +'</div><div class="j-attent-tip-wrap attent-tip-wrap"><i></i></div>';

        var jAttWrap = $(".j-attent-dialog-wrap");

        if(jAttWrap.length === 0){
            jAttWrap = $(attentHtml).appendTo("body");
        }

        jAttWrap.find('.attent-close').click(function(){
            jAttWrap.hide();
        });

        //有品牌的时候需要增加引导提示操作
        var hasPinpaiId = $("#pinpai_brandId").val();
        if(hasPinpaiId !== "0" && hasPinpaiId !== undefined){
            //if(true){

            hasPinpai = true;

            var offset = that.jNode.offset();

            //定位关注提示框的位置
            var jTip =  $(".j-attent-tip-wrap").css({"left": offset.left - 50, "top": offset.top + 28}).fadeIn();
            jTip.find("i").bind("click", function(){
                jTip.hide();
            });
            $("body").bind("click", function(e){
                var target = e.target || e.srcElement;
                if(target !== jTip[0]){
                    jTip.hide();
                }
            });

            setTimeout(function(){
                jTip.fadeOut();
            }, 3000);

        }

    }

})(S_ifollow, jQuery);