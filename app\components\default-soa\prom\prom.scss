@import '../common/lib';
@import './__sprite.scss';

// 累计评价 和 选购指数 同时出现
.hasmore {
    .comment-count {
        border-left: none;
    }
}

.left-mask-line {
  position: fixed;
  top: 0;
  width: 100%;
  height: 16px;
  background: #f6f7fb;
  z-index: 40;
}

.itemInfo-wrap {
    .sku-name {
        font-size: 20px;
        font-weight: 600;
        color: #1a1a1a;
        padding-top: 20px;
        margin-bottom: 6.5px; // 与温馨提示间距12 - 标题行高 4 - 温馨提示行高 1.5
        display: inline-block;
        width: 100%;
        img {
            vertical-align: -2px;
        }
        .sku-name-title{
            float: left;
            width: 88.2%;
            line-height: 28px;
        }
        .collect{
            float: right;
            font-size: 14px;
            height: 16px;
            display: flex;
            align-items: center;
            margin-top: 6px;
            color: #505259;
            font-weight: 400;
            cursor: pointer;
            i{
                display: inline-block;
                width: 16px;
                height: 16px;
                margin-right: 5px;
                vertical-align: middle;
                background: url(https://img11.360buyimg.com/imagetools/jfs/t1/253639/6/4849/689/676d54e9Fb62dabdb/40c829a236acc4a0.png);
                background-size: 100%;
                margin-top: -2px;
            }
        }
        .followed{
            i{
                background: url(https://img11.360buyimg.com/imagetools/jfs/t1/257871/30/8557/350/677b6c0eFb8a553c0/3d16f1c8c7ec2fa4.png);
                background-size: 100%;
            }
        }
    }
   

    .news {
        display: none;
        color: $colorPriceRed;
        margin-bottom: 5px;

        .item {
            margin-bottom: 5px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 618px;
            max-height: 1.5em;
            line-height: 1.5em;
            .clothing & {
                max-height: 3em;
                white-space: normal;
            }
            a {
                text-decoration: underline;
            }
            .root61 & {
                width: 738px;
            }
            .root61 .clothing & {
                width: 590px;
            }


            .cdetail {
                color: #666;
                margin-left: 8px;

                span {
                    font-family: $font-st;
                }

                &:hover {
                    color: #5E69AD;
                }
            }

            em {
                color: #5E69AD;
            }
        }

        a{
            color:#5e69ad;
        }
    }
    .summary-first, #pingou { // 预约、预售
        position: sticky;
        z-index: 2; // 要低于腰带和 tab
        background: #fff;
    }
    
    .summary-price-wrap {
        // padding: 10px 0 0 0;
        //background: #efefef url(../common/i/price-bg.png) repeat-x;
        position: sticky;
        z-index: 2; // 要低于腰带和 tab
        background: #fff;
        padding-top: 19px; // 价格区缺少高度 4px + 上间距 15px
        padding-bottom: 12px;
        // height: 22px; // 暂时高度统一为22px, 阶梯价需取消高度限制
        .summary-price {
            position: relative;
            // margin-bottom: 5px;
            .dt {
                line-height: 22px;
            }
            .p-price {// 作为唯一价格 10036461116852
                color: #FF0F23;
                // font-family: $font-yahei;
                margin-right: 10px;
                vertical-align: middle;
                span {// 此处有时会作为主价格 10036461116852 ，有时会作为京东价 6004683，需要做一下区分
                    font-family: 'JDZhengHeiVBold2';
                    font-weight: 700;
                    &:first-child { // 羊角标 10036461116852
                        display: inline-block;
                        font-size: 20px;
                        transform: translateY(2px);// 
                    }
                }
                .price {
                    font-size: 28px;
                }

            }
            .p-price.jdPrice { // 作为京东价 10056741107340
                span { // 覆盖 .p-price span 的样式
                    font-weight: 400;
                    font-size: 14px;
                }
                span:first-child { // 羊角标 10056741107340
                    font-family: 'JDZhengHeiVRegular2-1';
                    transform: none;
                }
            }
            .pricing { // 划线价/定价
                font-size: 14px;
                #page_hx_price { // 划线价
                    margin-right: 6px;
                    text-decoration: line-through;
                }
            }
            .qi-icon-linkwrap{
                background: #ff0f23;
                color: #fff;
                padding: 0 4px;
                border-radius: 2px;
                font-size: 13px;
                display: inline-block;
                vertical-align: middle;
                cursor: auto;
                &:hover{
                    color: #fff;
                }
                
            }
            .qi-linkwarp-ys{
                background: none;
                color: #666;
                padding: 0;
                border-radius: 0px;
                font-size: 13px;
                display: inline-block;
                vertical-align: middle;
                cursor: auto;
                &:hover{
                    color: #666;
                }
            }
            .finalPrice {
                color: #FF0F23;
                // font-family: $font-yahei;
                // margin-right: 2px;
                font-weight: 400;
                vertical-align: middle;
                // font-family: 'JDZhengHeiVHeavy2';
                .symbol {
                    font-size: 20px;
                    font-weight: 600;
                    font-family: "JDZhengHeiVHeavy2";
                    vertical-align:bottom;
                    // margin-bottom: -3px; // 10056741107340 需要去掉
                    display: inline-block;
                }
                .price {
                    font-family: 'JDZhengHeiVBold2';
                    font-size: 28px;
                    // margin-right: 3px;
                    font-weight: 600;
                    vertical-align: middle;
                }
                .priceContent{
                    font-size: 14px;
                    font-weight: 600;
                    vertical-align: middle;
                    
                }
                .priceContentText{
                    display: inline-block; // 默认 inline 会导致高度变高
                    transform: translateY(-2px);
                    background: #FF0F23;
                    color: #fff;
                    padding: 4px;
                    border-radius: 2px;
                    font-size: 13px;
                    line-height: 14px;
                    height: 14px;
                    margin-left: 8px;
                    margin-right: 6px;
                }
                .priceNew,.priceNewContent{
                    color: #888B94;
                    font-size: 14px;
                    vertical-align: middle;
                }
                .priceNew { // 100057834508
                    font-family: "JDZhengHeiVRegular2-1";
                }
            }
            .msbtPrice {
                color: #FF0F23;
                // font-family: $font-yahei;
                font-weight: 400;
                margin-right: 0px;
                // font-family: 'JDZhengHeiVHeavy2';
                .symbol {
                    font-size: 16px;
                    font-weight: 600;
                    vertical-align: middle;
                }
                .price {
                    font-size: 28px;
                    // margin-right: 3px;
                    font-weight: 600;
                    // vertical-align: middle;
                }
                .priceContent{
                    font-size: 14px;
                    font-weight: 500;
                    vertical-align: middle;
                }
                .priceContentText{// 补贴价 10099325656251
                    display: inline-block;
                    height: 14px;
                    line-height: 14px;
                    background: #FF0F23;
                    color: #fff;
                    padding: 4px;
                    border-radius: 2px;
                    font-size: 13px;
                    transform: translateY(-5px);
                    font-family: 'PingFang SC';
                }
            }
            .dailyPrice{
                // font-family: $font-yahei;
                font-size: 12px;
                .price {
                    font-family: JDZhengHeiVRegular2-1;
                }
            }
            .finalPriceLs{
                color: #FF0F23; //#006AFF;
                .priceContentText{
                    color: #006AFF;
                    background: #E5F5FF;
                }
            }
            .finalPriceIcon{
                .priceContent{
                    background-size: 100%;
                    // width: 82px;
                    // height: 22px;
                    display: inline-block;
                }
                
            }
            .jdPrice{
                color: #888B94;
                font-size: 14px;
                margin-right: 0px;
                vertical-align: middle;
                // font-family: 'JDZhengHeiVHeavy2';
                span{
                    font-size: 14px;
                }
                .price{
                    font-size: 14px;
                    font-weight: normal;
                    font-family: "JDZhengHeiVRegular2-1";
                    margin-right: 3px;
                    color: #888B94;
                }
                
            }
            #J_JdContent.has-tips {
                position: relative;
                cursor: pointer;
                padding-right: 20px;
                background: url(https://img11.360buyimg.com/imagetools/jfs/t1/265713/6/12921/978/678a4853Fb877b897/479338c16704e1f8.png) no-repeat;
                background-position: right center;
                background-size: 14px 14px;
            }
            #J_JdContent.has-tips:hover {
                background-image: url(https://img10.360buyimg.com/imagetools/jfs/t1/255898/37/13478/762/678a4853F37d10aa5/3467e3bf704b3340.png);
                color: rgba(255, 15, 35, 1);
                .store-price-tips{ 
                    display: block;
                    color: rgba(80, 82, 89, 1);
                }
            }
            .store-price-tips{
                position: absolute;
                left: -100px;
                bottom: -44px;
                display: none;
                padding: 12px;
                height: 14px;
                word-break: keep-all;
                line-height: 1;
                text-align: center;
                font-size: 14px;
                border: 0.5px solid rgba(0, 0, 0, 0.06);
                background-color: white;
                border-radius: 8px;
                z-index: 2;
            }

            // .notice {
            //     color: $colorLinkBlue;
            //     // vertical-align: 2px;
            //     &:hover{
            //         color:#e4393c;
            //     }
            // }
            .yijia {
                margin-left: 10px;
                vertical-align: 2px;
                .hl_red_bg,.car_red_bg {
                    color: #fff;
                    background: rgba(255, 15, 35, 1);
                    padding: 4px;
                    border-radius: 2px;
                    margin-right: 2px;
                    display: inline-block;
                    line-height: 18px;
                    font-size: 13px;
                    font-weight: 600;
                }
                .sprite-question{
                    display: inline-block;
                    vertical-align: -4px;
                    @include sprite-question;
                }
            }

            .bluk-buy{
                color: #999;
                font-size: 12px;
                margin-left: 5px;
                vertical-align: 2px;
                &.zIndex{
                    position: relative;
                    z-index: 9999;
                }
                .price-tips{
                    position: relative;
                    z-index: 1;
                    display: inline-block;
                    *display: inline;
                    *zoom: 1;
                    color: #666;
                    margin-left: 5px;
                    vertical-align: -3px;
                    &:hover{
                        .tips{
                            display: block;
                        }
                    }
                    .sprite-question{
                        cursor: pointer;
                        display: inline-block;
                        vertical-align: 0px;
                        @include sprite-question;
                    }
                    .tips{
                        display: none;
                        position: absolute;
                        left: -12px;
                        top: 24px;
                        width: 215px;
                        .content{
                            padding: 0 10px 8px;
                            background: #fff;
                            border: 1px solid #cecbce;
                            box-shadow: 0 0 2px 2px #eee;
                            h3{
                                margin-bottom: 5px;
                                line-height: 36px;
                                color: #666;
                                border-bottom: 1px dotted #cdcbce;
                            }
                            p{
                                // font-family: "microsoft yahei";
                                color: #999;
                                line-height: 220%;
                                i{
                                    float: right;
                                }
                            }
                        }
                        .sprite-arrow {
                            @include sprite-arrow;
                            position: absolute;
                            overflow: hidden;
                            left: 15px;
                            top: -5px;
                        }
                    }
                }
            }

            .commentNotice{
                // width: 200px;
                height: 22px;
                line-height: 22px; // 与左侧双价格对齐
                float: right;
                text-align: left;
                .summary-info{
                    .comment-count{
                        color: #888B94;
                        a {
                            color: #888B94;
                        }
                    }
                    .notice {
                        color: #505259;
                        border-left: 0.5px solid #0000001F;
                        padding-left: 8px;
                        margin-left: 8px;
                        i{
                            display: inline-block;
                            width: 16px;
                            height: 16px;
                            margin-right: 5px;
                            vertical-align: middle;
                            background: url(https://img11.360buyimg.com/imagetools/jfs/t1/252894/14/10346/442/677cc340Fa9d500a7/3935ddf0b0291c48.png);
                            background-size: 100%;
                            margin-top: -2px;
                        }
                    }
                }
            }
            
        }
        .paipai-price {
            position: relative;
            margin-bottom: 5px;
            .dt {
                line-height: 22px;
            }
            .p-price {
                color: #E4393C;
                // font-family: $font-yahei;
                // margin-right: 10px;
                span {
                    font-size: 16px;
                }
                .price {
                    font-size: 22px;
                }

            }
            .p-price-color{
                color: #666666;
                // font-family: $font-yahei;
                // margin-right: 10px;
                span {
                    font-size: 16px;
                }
                .price {
                    font-size: 22px;
                    color: #666666;
                }
            }

            .notice {
                color: $colorLinkBlue;
                vertical-align: 2px;
                margin-left: 10px;
                &:hover{
                    color:#e4393c;
                }
            }
            .price-tips{
               display: inline-block;
            }
            .sprite-question{
                display: inline-block;
                vertical-align: 0px;
                @include sprite-question;
            }
            .price-tips .tips {
                z-index: 4;
                width: 270px;
                position: absolute;
                top: 25px;
                display: none;
                .content {
                    padding: 10px;
                    background: #fff;
                    border: 1px solid #cecbce;
                    color: #666;
                    -moz-box-shadow: 0 0 2px 2px #eee;
                    -webkit-box-shadow: 0 0 2px 2px #eee;
                    box-shadow: 0 0 2px 2px #eee;
                    dt{
                        font-weight: bold;
                        margin-bottom: 3px;
                    }
                    dd{
                        line-height: 170%;
                    }
                    p{
                        border-top: 1px dotted #999;
                        margin-top: 7px;
                        padding-top: 7px;
                        a{
                            color: #5e69ad;
                            margin: 0 5px;
                            &:hover{
                                color: #e4393c;
                            }
                        }
                    }
                }
                .sprite-arrow {
                    @include sprite-arrow;
                    position: absolute;
                    overflow: hidden;
                    left: 5px;
                    top: -5px;
                    _bottom: -1px;
                }
            }
            .hover .tips{
                display: block;
            }
        }

        // .summary-info {
        //     position: absolute;
        //     z-index: 1;
        //     right: 10px;
        //     top: 8px;
        //     color: #999;
        //     a {
        //         color: $colorLinkBlue;
        //         margin-left: 5px;
        //         &:hover{
        //             color:#e4393c
        //         }
        //     }
        //     .item {
        //         border-left: solid 1px #e6e6e6;
        //         text-align: center;
        //         padding: 0 10px;
        //         line-height: 15px;
        //     }
        //     p {
        //         color: #999;
        //     }

        //     .count {
        //         font: 14px 'verdana';
        //         color: #005ea7;
        //     }
        //     .buy-rate .count:hover {
        //         text-decoration: none;
        //     }
        // }
        .buy-rate {
            border-left: 1px solid #ccc;
            padding-left: 10px;
            margin-left: 5px;
        }

        .summary-top {
            @include clearfix;
            display: none;
            .summary-promotion {
                // background: url(../common/i/price-bg.png) repeat-x #efefef 0 -12px;
                // *background: url(../common/i/price-bg.png) repeat-x #efefef 0 -12px;
                padding-bottom: 5px;

            }

            .p-promotions-wrap {
                position: relative;
                overflow: hidden;
            }

            .p-promotions {
                position: relative;
                line-height: 30px;
                padding-right: 90px;
                min-height: 35px;
                *zoom:1;
                a:link{
                    color: #666;
                }
                 a:hover{
                     color: #C81623;
                 }

                .hl_red_bg,.car_red_bg {
                    color: #df3033;
                    background: transparent;
                    border: 1px solid #df3033;
                    padding: 2px 3px;
                    margin-right: 5px;
                    display: inline-block;
                    line-height: 16px;
                    *zoom:1;
                }
                
                .yellow-bg,.yellow{
                  background: transparent;
                  border: 1px solid #d3b679;
                  color: #cfae68;
                  padding: 2px 3px;
                  margin-right: 5px;
                  display: inline-block;
                  line-height: 16px;
                  *zoom:1;
                }

                .hl_red {
                    color: #666;
                    line-height: 18px;
                    .sprite-tips{
                        display: inline-block;
                        margin-right: 6px;
                        vertical-align: -3px;
                        @include sprite-tips;
                    }
                }
                .sprite-question-con{
                    display: inline-block;
                    vertical-align: -1px;
                    width: 12px;
                    height: 12px;
                    background-image: url(https://img11.360buyimg.com/imagetools/jfs/t1/207760/8/59446/230/67566050F8be82471/60e1a9aee7d9d398.png);
                }
                .mj_red{
                    color: #e4393c;
                    line-height: 18px; 
                }
                #pickOneTip{
                    .hl_red{
                        color: #999;
                    }
                }

                .detail-btn {
                    white-space: nowrap;
                }

                .prom-tags {
                    width: 658px;
                    height: 26px;
                    display: none;
                }

                .view-all-promotions {
                    position: absolute;
                    right: 10px;
                    top: 30px;
                    width: 81px;
                    text-align: right;
                    cursor: pointer;

                    .prom-sum {
                        color: #999
                    }

                    .sprite-arr-close {
                        @include inline-block;
                        vertical-align: middle;
                        margin-right: 3px;
                        @include sprite-arr-close;
                        _overflow:hidden;
                    }

                    .sprite-arr-open {
                        @include sprite-arr-open;
                    }
                }
                /// PLUS品牌联盟9.5折
                .prom-plus .prom-item{
                    .hl_red_bg {
                        border: none;
                        padding: 0;
                        line-height: normal;
                        img {vertical-align: top;}
                    }
                }
            }

            .z-promotions-all-show {
                *zoom:1;
                position: absolute;
                z-index: 5;
                width: 100%;
                .prom-item,
                .prom-quan{
                    height: auto;
                    white-space: normal;
                    text-overflow: clip;
                    overflow: visible;
                    *zoom: 1;
                }
                .p-promotions-wrap {
                    height: auto;
                    _position: relative;
                    *zoom: 1;
                    padding-bottom: 5px;
                    .more-prom-ins,.view-all-promotions {
                        display: none;
                        *zoom:1;
                    }
                }
            }
        }

        .z-has-more-promotion {
            position: relative;
            height: 60px;
            z-index: 4;
            *z-index: 5;
            margin-bottom: 5px;

            .prom-item,
            .prom-quan {
                height: 30px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }

            .p-promotions-wrap {
                height: 60px;
                *height:52px;
                _width:560px;
                _position: relative;
                _zoom: 1;
                .more-prom-ins,.view-all-promotions {
                    display: block;
                }
            }
        }
    }
}

.rate-layer{
    width: 208px;
    //position: absolute;
    //left: 20px;
    //top: 25px;
    //padding: 5px 15px 12px;
    background: #fff;
    //border: 1px solid #cecbce;
    color: #666;
    //box-shadow: 0 0 2px 2px #eee;
    //display: none;
    .sprite-arrow{
        width: 11px;
        height: 6px;
        position: absolute;
        overflow: hidden;
        left: 20px;
        top: -6px;
        @include sprite-arrow;
    }
    .layer-tit{
        border-bottom: 1px dotted #cecbce;
        line-height: 32px;
        h3{
            font-size: 12px;
            color: #666;
        }
        span{
            float: right;
            color: #999;
            margin-top: -32px;
        }
    }
    .layer-con{
        a{
            margin: 0;
        }
        .total{
            line-height: 32px;
            border-bottom: 1px dotted #cecbce;
        }
        .score-list{
            padding-top: 4px;
            li{
                line-height: 22px;
            }
        }
        .label{
            display: inline-block;
            width: 60px;
            color: #999;
        }
        .trend{
            float: right;
        }
        .sprite-down,.sprite-up{
            display: inline-block;
            margin-left: 5px;
        }
        .sprite-down{
            @include sprite-down;
        }
        .sprite-up{
            @include sprite-up;
        }
        .tips{
            border-top: 1px dotted #cecbce;
            margin-top: 8px;
            padding-top: 8px;
            dt{
                font-weight: bold;
                margin-bottom: 2px;
            }
        }
    }
}

#prom-mbuy {
    *z-index: 6;
    .qrcode-wrap {
        *z-index: 6;
        *zoom: 1;
    }
}

#p-ad-phone {
    height: 18px;
    text-overflow: ellipsis;
}

.summary-promotion .dt {
    line-height: 30px;
}

.prom-gifts {
    //width: 364px;
    padding-left: 40px
}

.prom-gift-label {
    max-width: 70px;
    margin-left: -40px;
    display: inline;
    float: left;
    em {
        padding: 1px 2px;
        color: #fff;
        background-color: #e4393c
    }
}
.prom-plus-gift-label {
    float: left;
    width: 70px;
    margin-left: -40px;
    display: inline;
    em {
        padding: 1px 2px;
    }
}
.prom-plus-gift-list {
    width: 90%;
    float: left;

    .prom-gift-item {
        padding: 0 5px;
        float: left;
        height: 30px;
        white-space: nowrap
    }

    .gift-limit {
        color: #cfae68;
    }

    a {
        float: left;
        width: 25px;
        height: 25px;
        margin-right: 3px
    }

    .gift-img {
        display: block;
        width: 25px;
        height: 25px
    }

    .gift-number {
        font-size: 12px;
        font-family: arial;
        color: #e4393c
    }
}

.prom-gift-list {
    width: 90%;
    float: left;

    .prom-gift-item {
        padding: 0 5px;
        float: left;
        height: 30px;
        white-space: nowrap
    }

    .gift-limit {
        color: #999;
    }

    a {
        float: left;
        width: 25px;
        height: 25px;
        margin-right: 3px
    }

    .gift-img {
        display: block;
        width: 25px;
        height: 25px
    }

    .gift-number {
        font-size: 12px;
        font-family: arial;
        color: #e4393c
    }
}


/* 手机专享 */
#summary-mbuy {
    position: absolute;
    top: 114px;
    left: 220px;
    _left: 224px;
    z-index: 5;

    width: 185px;
    height: 165px;
    border: 1px solid #eee;
    border-top: none;

    background: #fff;
    -moz-box-shadow: 0 0 10px rgba(0, 0, 0, .12);
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, .12);
    box-shadow: 0 0 10px rgba(0, 0, 0, .12);

    .loading-style1 {
        margin: 40px 0 0 10px;
    }

    .qrcode {
        margin-left: 20px;
    }
    p {
        height: 25px;
        margin-left: 20px;

        line-height: 25px;
    }

    i {
        position: absolute;
        left: 1px;
        top: -6px;
        *top: -8px;

        display: inline-block;
        *zoom: 1;
        width: 183px;
        height: 8px;
        background: #fff;
        z-index: 5;
    }
}
.mob-buy {
    position: relative;
    *width: 100%;
    .qrcode-wrap {
        position: absolute;
        left: 150px;
        top: 0px;

        width: 185px;
        height: 35px;
        display: inline-block;
        *zoom: 1;
        *z-index: 8;
    }
    .icon {
        s, b {
            display: inline-block;
            *zoom: 1;
            overflow: hidden;
            vertical-align: middle;
        }
        s {
            width: 19px;
            height: 22px;
            margin: 0 10px 0 10px;

            background: url(i/mbuy.png) 0 0 no-repeat;
        }
        b {
            width: 7px;
            height: 7px;
            margin-left: 3px;
            background: url(//misc.360buyimg.com/lib/skin/2013/i/20130330A_2.png) -95px -55px no-repeat;
        }
    }
}
.mob-buy-curr {
    .qrcode-wrap {
        border: 1px solid #eee;
        border-bottom: none;

        background: #fff;
        -moz-box-shadow: 0 -5px 10px rgba(0, 0, 0, .12);
        -webkit-box-shadow: 0 -5px 10px rgba(0, 0, 0, .12);
        box-shadow: 0 -5px 10px rgba(0, 0, 0, .12);
    }
    .icon {
        s {
            background-position: 0 -22px;
        }
    }
}


/*[家电\图书]券*/
#summary-quan {
    position: sticky;
    z-index: 2; // 要低于腰带和 tab
    top: 53px;
    background: #fff;
    padding-bottom: 12px;
    display: none;
    // height: 30px;
    .dt{
        line-height: 32px;
    }
    .dd{
        padding-top: 8px;
    }
    .lh{
        height: 30px;
    }
    .quan-item {
        position: relative;
        float: left;
        height: 14px;
        padding: 8px;
        line-height: 15px;
        text-align: center;
        background: #FFEBEF;
        font-size: 14px;
        white-space: nowrap;
        margin-right: 8px;
        cursor: pointer;
        color: #FF0F23;
        border-radius: 4px;
        font-weight: 600;
    }
    .quan-item-img {
        position: relative;
        float: left;
        margin-right: 13px;
        cursor: pointer;
        border-radius: 4px;
        img{
            // height: 30px;
            vertical-align: inherit;
        }
    }
    .text {
        // padding: 0 10px;
        color: #FF0F23;
        font-size: $baseFontSize;
        font-weight: 600;
        font-family: 'JDZhengHeiVRegular2-1';
        img { // 国家补贴竖线
            margin: 0 4px;
            height: 15px;
        }
    }
    .quan-item s, .quan-item b {
        position: absolute;
        top: -1px;
        display: block;
        height: 18px;
        width:2px;
        overflow:hidden;
        background: url(i/quan-arr.gif) 0 0 no-repeat;
    }
    .quan-item s {
        left: 0;
        background-position: -2px 0;
    }
    .quan-item b {
        right: -2px;
    }
    .quan-item:hover{
        background: #fff4f4;
        s,b{
            background: url(i/quan-arr-hover.gif) no-repeat;
        }
        s{
            background-position: -2px 0;
        }
    }
    .more-btn {
        cursor: pointer;
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-right: 5px;
        vertical-align: middle;
        background: url(https://img13.360buyimg.com/imagetools/jfs/t1/268329/4/5140/307/67713025F1b4d312d/d607de0c281b0358.png);
        background-size: 100%;
        margin-top: 9px;
        position: absolute;
    }
}

// 拉新
#lachine {
    min-height: 32px;
    margin-bottom: 10px;
    // margin-right: 18px;
    position: relative;
    overflow: hidden;
    .arrow {
        cursor: pointer;
        position: absolute;
        right: 0px;
        top: 0px; 
        width: 32px;
        height: 32px;
        background: url(https://img12.360buyimg.com/imagetools/jfs/t1/266539/18/14151/476/678f5c60F3358f69f/ce4c94df51709093.png) no-repeat;
        background-size: 10px 10px;
        background-position: center;
        background-color: #fff;
        &.right-arrow {
            transform: rotate(-90deg);
        }
    }
    .ddnew {
        width: max-content;
        display: inline-flex;
        flex-direction: row;
        // width: 270px;
        // padding: 4px 0px;
        // height: 30px;
        position: absolute;
        overflow: hidden;
        li {
            width: max-content;
            border-radius: 4px;
            // background-color: #F2F4FA;
            display: inline-flex;
            // width: 254px;
            height: 16px;
            padding: 8px 8px;
            padding-right: 26px;
            background: #F2F4FA url(https://img13.360buyimg.com/imagetools/jfs/t1/259181/16/14667/489/6790bd3aF60de783b/8ed74ff7b6ef4ed6.png) no-repeat;
            background-position: right 8px center;
            background-size: 10px;
            align-items: center;
            font-size: $baseFontSize;
            font-weight: 500;
            color: rgba(80, 82, 89, 1);
            margin-right: 4px;
            img {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }
            &:hover {
                a {
                    color: #FF0F23;
                }
                background-image: url(https://img14.360buyimg.com/imagetools/jfs/t1/276452/7/17223/329/67f37bd2F5d6b429e/cb4f837e6c161ee5.png);
                background-color: #FFEBF1;
            }

        }
    }
    &.expand {
        background-color: #F2F4FA;
        .ddnew{
            position: static;
            height: auto;
            flex-direction: column;
            li:hover {
                background-color: #F2F4FA;
            }
        }
        .arrow {
            background-color: #F2F4FA;
            transform: rotate(180deg);
            &.right-arrow {
                transform: rotate(-90deg);
            }
        }
    }
    
}

/**
 * #time 活动剩余时间
 */
.top-cd-banner{
    height: 32px;
    line-height: 32px;
    background: #e4393c;
    padding: 0 14px;
    color: #fff;
    // font-family: "Microsoft YaHei";
    .act-type{
        float: left;
        font-size: 16px;
        color: #ffe134;
        _margin: 8px 0;
        i{
            width: 16px;
            height: 16px;
            display: inline-block;
            vertical-align: middle;
            background: url(i/act-icon.png) no-repeat;
            _background: url(i/act-icon-8.png) no-repeat;
            background-position: 0 0;
        }
        span{
            margin-left: 8px;
            font-size: 14px;
            color: #fff;
        }


    }
    .time-remain{
        float: right;
        font-size: 14px;
        _margin: 8px 0;
        i{
            width: 16px;
            height: 16px;
            display: inline-block;
            vertical-align: middle;
            background: url(i/act-icon.png) no-repeat;
            _background: url(i/act-icon-8.png) no-repeat;
            background-position: 0 -20px;
            margin-right: 5px;
        }
        span{
            color: #ffe134;
        }
    }
}
/*book*/
.itemInfo-wrap {
    .author {
        // padding: 8px 0;
        a{
            color: #1a1a1a;
            &:hover{
                color: #e4393c;
            }
        }
    }
    .original-price{
        color: #999;
        margin-left: 60px;
        vertical-align: 2px;
    }
    .del-price{
        margin-left: 12px;
        // font-family: "microsoft yahei";
        span{
            font-weight: bold;
        }
    }
    .summary-price-wrap{
        .summary-price{
            .dd{
                margin: 0;
            }
            .del{
                text-decoration: line-through;
                font-size: 12px;
                color: #999;
                .price{
                    font-size: 12px;
                }
                span{
                    font-size: 12px;
                }
            }
        }
    }
}
/*book*/
.ebook{
    .itemInfo-wrap{
        .news{
            .item{
                width: auto;
                max-height: 3em;
            }
        }
    }
    .summary-price-wrap {
        padding: 19px 0 12px 0;
    }
}
/*book root61*/
// .root61{
//     .ebook{
//         .itemInfo-wrap{
//             width: 590px;
//             float: left;
//             margin-left: 30px;
//             .news{
//                 .item{
//                     width: 580px;
//                 }
//             }
//         }
//     }
// }


.plus-price {
    .p-price-plus {
        font: 16px $font-yahei;
        position: relative;
        bottom: -2px;
    }
    strong {
        font-weight: normal;
    }
    .text {
        margin-left: 5px;
        margin-right: 8px;
    }
    .p-price-plus {
        color: #333;
    }
    a {
        // font-family: simsun;
        color: $colorLinkBlue;
    }
}

.firm-price {
    .p-price-firm {
        font: 16px $font-yahei;
        position: relative;
        bottom: -2px;
        color: #4965F3;
    }
    strong {
        font-weight: normal;
    }
    .text {
        margin-left: 5px;
        margin-right: 8px;
    }
    a {
        // font-family: simsun;
        color: $colorLinkBlue;
    }
}

@font-face{
    font-family:'iconfont';
    src:url('//storage.360buyimg.com/channel2022/jd_home/0.0.52-alpha.5/fonts/ba43574760fe93a35fca3c6f9b9e73f4.eot');
    src:url('//storage.360buyimg.com/channel2022/jd_home/0.0.52-alpha.5/fonts/ba43574760fe93a35fca3c6f9b9e73f4.eot#iefix') format("embedded-opentype"),
    url('//storage.360buyimg.com/channel2022/jd_home/0.0.52-alpha.5/fonts/a84f5777e97992c6fb6d423c003f187b.woff') format("woff"),
    url('//storage.360buyimg.com/channel2022/jd_home/0.0.52-alpha.5/fonts/43f6b5fd284d120fd59582a14e3e0844.ttf') format("truetype"),
    url('//storage.360buyimg.com/channel2022/jd_home/0.0.52-alpha.5/fonts/7e3a7b715cf938242acf0fe64a0e1817.svg#iconfont') format("svg")
}

.firmbuy-price {
    margin-top: 5px;
    .buy-tips {
        padding: 13px 10px;
        background: #fff;
        border-radius: 10px;
        display: inline-block;
        height: 36px;
        position: relative;
        margin-top: 10px;
        border: 1px solid rgba(0,83,253,.6);
        .arrow{
            position: absolute;
            display: inline-block;
            width: 16px;
            height: 6px;
            background-image: url(//img12.360buyimg.com/imagetools/jfs/t1/89617/24/37900/647/65e7d539Fc22700cd/44bdb3ccc965cdde.png);
            background-position: 0 0;
            background-size: 100%;
            left: 22px;
            top: -6px;
        }
        .buy-content{
            .left{
                width: 36px;
                height: 36px;
                float: left; 
            }
            .right{
                float: left;
                margin-left: 10px;
                .buy-one{
                    margin: 9px 0;
                    height: 18px;
                    line-height: 18px; 
                    .discount{
                        float: left;
                        display: inline-block;
                        font-size: 14px;
                        span{
                            font-size: 18px;
                            color: #0053FD;
                            font-weight: 400;  
                        }
                       
                    }
                    .question{
                        display: inline-block;
                        vertical-align: -5px;
                        width: 16px;
                        height: 16px;
                        background-image: url(//img11.360buyimg.com/imagetools/jfs/t1/122241/40/41954/516/65e6d664F0b3c757a/5fa38e93538e12b3.png);
                        background-position: 0 0;
                        margin-right: 20px;
                        background-size: 100%;
                        margin-left: 3px;
                    }
                    .go-bargaining{
                        float: right;
                        a{
                            font-size: 14px;
                            color: #0053FD;
                            font-weight: 400;  
                        }
                    }
                } 
                .buy-multiple{
                    display: inline-block;
                    font-size: 12px;
                    color: rgba(26, 26, 26, 1);
                    margin-top: -3px;
                    .top{
                        padding: 0 10px 8px 10px;
                        height: 12px;
                        line-height: 12px;
                        div{
                            float: left;
                            
                            span{
                                width: 45px;
                                display: inline-block;
                                text-align: left;
                                font-family: 'iconfont';
                                margin-right: 2px;
                                overflow: hidden;
                            }
                        }
                        .num,.more{
                            height: 12px;
                            line-height: 12px;
                        }
                        .num{
                            width: 40px;
                            margin-right: 20px;
                        }
                    }
                    .bottom{
                        padding: 8px 10px 0px;
                        height: 12px;
                        line-height: 12px;
                        div{
                            float: left;
                            span{
                                width: 45px;
                                display: inline-block;
                                text-align: left;
                                margin-right: 2px;
                                // overflow: hidden; // 数字显示不全，去掉
                                i{
                                    color: #0053FD;
                                    font-size: 16px;
                                    font-family: JDZhengHeiVRegular2-1;
                                }
                            }
                        }
                        .num,.go-bargaining{
                            height: 14px;
                            line-height: 14px;
                        }
                        .num{
                            width: 40px;
                            margin-right: 20px;
                            .question{
                                display: inline-block;
                                vertical-align: -1px;
                                width: 12px;
                                height: 12px;
                                background-image: url(//img11.360buyimg.com/imagetools/jfs/t1/122241/40/41954/516/65e6d664F0b3c757a/5fa38e93538e12b3.png);
                                background-position: 0 0;
                                background-size: 100%;
                                margin-left: 2px;
                            }
                        }
                        .go-bargaining{
                            a{
                                font-size: 12px;
                                color: #0053fd;
                                font-weight: 400;
                            }
                            
                        }
                        
                    }
                }
            }
        }

    }
}
.summary-info{
    font-size: 14px;
    float: right;
}
.meet-price {
    .p-price-meet {
        font: 16px $font-yahei;
        position: relative;
        bottom: -2px;
        color: #8A2EE6;
    }
    strong {
        font-weight: normal;
    }
    .text {
        margin-left: 5px;
        margin-right: 8px;
    }
    a {
        // font-family: simsun;
        color: $colorLinkBlue;
    }
}

.fans-price {
    .p-price-fans {
        font: 16px $font-yahei;
        position: relative;
        vertical-align: middle;
        color: #7049ff;
    }
    strong {
        font-weight: normal;
    }
    .text {
        margin-left: 5px;
        margin-right: 8px;
        vertical-align: middle;
    }
}

.sam-price {
    padding-top: 5px;
    .p-price-sam {
        position: relative;
        bottom: -2px;
        color: $colorLinkBlue;
        font-size: 16px;
        // font-family: $font-yahei;
    }
    .sam-icon {
        margin-right: 5px;
    }
    .text {
        margin-left: 20px;
        color: #999;
    }
    a {
        color: $colorLinkBlue;
        font-family: $font-st;
    }
    .sprite-question{
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        @include sprite-question;
    }
}

.user-price {
    padding-top: 5px;
    .p-price-user {
        position: relative;
        bottom: -2px;
        color:#ff911c;
        font-size: 16px;
        // font-family: $font-yahei;
    }
    .user-icon {
        margin-right: 5px;
    }
    .text {
        margin-left: 20px;
        color: #999;
    }
    a {
        color: $colorLinkBlue;
        font-family: $font-st;
    }
    .sprite-question{
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        @include sprite-question;
    }
}
.newcomer-price {
    padding-top: 5px;
    line-height: normal;
    color: #666;
    font-size: 12px;
    span, i ,em {vertical-align: middle}
    em {
        margin-right: 3px;
        color: #ff911c;
        font-size: 16px;
    }
    i {
        @include inline-block;
        width: 43px;
        height: 16px;
        margin: 0 8px 0 3px;
        background: url(./i/icon-newcomer.png) no-repeat left center;
    }
}

.enterprise em {
    margin-right: 3px;
    color: #4965F2;
    font-size: 16px;
}

.enterprise i {
    @include inline-block;
    width: 74px;
    height: 16px;
    margin: 0 8px 0 3px;
    background: url(./i/enterprise.png) no-repeat left center;
}

.student-price {
    padding-top: 5px;
    line-height: normal;
    color: #666;
    font-size: 12px;
    span, i ,em {vertical-align: middle}
    em {
        margin-right: 3px;
        color: #0c8;
        font-size: 16px;
    }
    i {
        @include inline-block;
        width: 43px;
        height: 16px;
        margin: 0 8px 0 3px;
        background: url(./i/icon-student.png) no-repeat left center;
    }
}
.double-price{
    margin-left: 5px;
    line-height: normal;
    color: #e4393c;
    font-size: 12px;
    display: inline-block;
    vertical-align: text-bottom;
    span,em {vertical-align: middle}
    em {
        margin-right: 3px;
        color: #e4393c;
        font-size: 16px;
    }
}
.watsons {
    padding-top: 5px;
    font-size: 0;
    &__logo {
        *zoom: 1;
        *display: inline;
        display: inline-block;
        width: 20px;
        height: 16px;
        margin-right: 5px;
        background: url(//img30.360buyimg.com/devfe/jfs/t19393/271/1724621772/1669/8878db54/5ad4975eNcddb09a7.png) no-repeat;
        vertical-align: middle;
    }
    &__text {
        color: #999;
        font-size: 12px;
        vertical-align: middle;
    }
    &__link {
        color: #0099a8;
        font-size: 12px;
        vertical-align: middle;
    }
    &__link:hover {
        color: #e4393c
    }
    &__link--mr18 {
        margin-right: 18px;
    }
    &__link--mr3 {
        margin-right: 3px;
    }
    .sprite-question{
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        @include sprite-question;
    }
}

/*building*/
.itemInfo-wrap{
    .summary-price-wrap{
        .building-price{
            .p-price{
                font-size: 12px;
                color: #666;
                line-height: 190%;
            }
        }
    }
}

.activity-banner{
    background-color: #fff; // banner 未加载时，显示默认背景色
    height: 52px;
    line-height: 52px;
    padding: 0 24px;
    overflow: hidden;
    zoom: 1;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    // i{
    //     display: inline-block;
    // }
    .pay-icon{
        float: right;
        // margin: 8px 0;
        // background: #FFE6BA;
        border-radius: 3px;
        color: #ffffff;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        // padding: 0 3px;
        text-align: center;
        height: 52px;
        text-align: center;
        line-height: 52px;
    }
    .pay-icon-left{
        margin-top: -4px;
        float: left;
    }
    .leftArrowIcon{
        float: left;
        margin-top: -4px;
        margin-left: 8px;
    }
    // .bybt-title-icon a{
    //     height: 16px;
    //     margin: 8px 0;
    //     width: 107px;
    //     display: inline-block;
    // }
    .pay-icon-right{
        float: right;
        margin-top: -4px;
        display: flex;
        align-items: center;
        .pay-right-text{
            color: #ffffff;
            font-size: 14px;
            font-family: JDZhengHeiVRegular2-1, PingFang SC, microsoft yahei;
        }
    }
    .pay-icon-right-Arrow{
        float: right;
        margin-top: -4px;
        margin-right: 160px;
    }
}
.activity-type{
    float: left;
    strong{
        font-size: 16px;
        color: #ffe134;
        font-weight: normal;
    }
    span{
        margin-left: 8px;
        font-size: 12px;
        color: #fff;
        font-weight: bold;
    }
}
.activity-price{
    float: left;
    padding: 0 10px;
    strong{
        font-size: 16px;
        color: #666666;
        font-weight: normal;
    }
    span{
        font-size: 16px;
        color: #666666;
        font-weight: normal;
        padding-left: 10px;
    }
}
.left-message-info{
    float: right;
    margin-top: -6px;
    .pay-right-img{
        padding-right: 8px;
    }
    .middleIcon{
        display: inline-block;
    }
    .activity-message{
        color: #fff;
        font-size: 14px;
        margin-top: -3px;
        display: inline-block;
        font-family: 'JDZhengHeiVRegular2-1';
        .item {
            margin-left: 30px;
        }
        em{
            color:#ffe134;
        }
        .J-count{
            font-weight: bold;
        }
    }
    
}
// #banner-shangou.seckill {
//     background: #f0f0f0 url(i/shangou.png) no-repeat;
//     .sprite-seckill {
//         background:none;
//     }
// }
.seckill-yg{
    background: #f0f0f0 url(i/seckill.png) no-repeat;
    .sprite-seckill-yg{
        @include sprite-seckill;
        vertical-align: -4px;
        margin-right: 5px;
    }
    .activity-type{
        strong{
            color: #e4393c;
        }
    }
    .activity-message{
        color: #999;
        font-size: 12px;
        span{
            font-size: 14px;
            color: #fff;
            display: inline-block;
            width: 22px;
            line-height: 24px;
            text-align: center;
            background: #999999;
            margin: 0 4px;
            border-radius: 2px;
        }
    }
}


.seckill{
    background: #f0f0f0 url(i/seckill.png) no-repeat;
    .sprite-seckill{
        @include sprite-seckill;
        vertical-align: -4px;
        margin-right: 5px;
    }
    .activity-type{
        strong{
            color: #e4393c;
        }
    }
    .activity-message{
        color: #666;
        font-size: 12px;
    }
}

#banner-clothdiscount{
    .activity-message{
        color: #FFFFFF;
        font-size: 12px;
        span{
            font-size: 14px;
            color: #352D9F;
            display: inline-block;
            width: 22px;
            line-height: 24px;
            text-align: center;
            background: #FFFFFFCC;
            margin: 0 4px;
            border-radius: 2px;
        }
    }
}

//通用腰带样式
#common_banner{
    clip-path: path('M 8 0 H 678 A 8 8 0 0 1 686 8 V 52 A 8 8 0 0 0 678 44 H 8 A 8 8 0 0 0 0 52 V 8 A 8 8 0 0 1 8 0 Z');
    .activity-message{
        color: #FFFFFF;
        font-size: 12px;
        span{
            font-size: 14px;
            color: #352D9F;
            display: inline-block;
            width: 22px;
            line-height: 24px;
            text-align: center;
            background: rgba(255, 255, 255, .8);
            margin: 0 4px;
            border-radius: 2px;
        }
    }
}

// #banner-shangou.seckilling {
//     background: #c208aa url(i/shangou_ing.png) no-repeat;
//     .sprite-seckilling {
//         background:none;
//     }
// }

.seckilling{
    background: url(i/seckilling.jpg) no-repeat #fe0851;

    .sprite-seckilling{
        @include sprite-seckilling;
        vertical-align: -4px;
        margin-right: 5px;
    }
    .activity-type{
        strong{
            color: #fff;
        }
    }
    .activity-message{
        color: #fbe2e2;
        font-size: 12px;
        span{
            font-size: 14px;
            color: #fff;
            display: inline-block;
            width: 22px;
            line-height: 24px;
            text-align: center;
            background: #443b3b;
            margin: 0 4px;
            border-radius: 2px;
        }
    }
}
.qfxr{
    overflow: hidden;
    zoom:1;
    background: #ff5c68 url(i/qfxr-banner.png) no-repeat;
    .icon-qfxr{
        display: inline-block;
        width: 133px;
        height: 24px;
        margin-top: 4px;
        background: url(i/icon-qfxr.png) no-repeat;
    }
    .activity-type{
        strong{
            color: #e4393c;
        }
    }
    .activity-message{
        color: #fbe2e2;
        font-size: 12px;
        span{
            font-size: 14px;
            color: #fff;
            display: inline-block;
            width: 22px;
            line-height: 24px;
            text-align: center;
            background: #443b3b;
            margin: 0 4px;
            border-radius: 2px;
        }
    }
}
/*decoration*/
.decoration{
    .itemInfo-wrap{
        .summary-price-wrap{
            .summary-price{
                .p-price{
                    .discount{
                        font-size: 12px;
                        color: #666;
                        margin-left: 3px;
                    }
                    .proposed-price{
                        color: #666;
                        font-size: 12px;
                        // margin-left: 60px;
                        // font-family: simsun;
                    }
                    .price-tips{
                        position: relative;
                        z-index: 1;
                        display: inline-block;
                        *display: inline;
                        *zoom: 1;
                        color: #666;
                        margin-left: 5px;
                        vertical-align: -3px;
                        &:hover{
                            .tips{
                                display: block;
                            }
                        }
                        .sprite-question{
                            vertical-align: -1px;
                        }
                    }
                    .sprite-question{
                        display: inline-block;
                        @include sprite-question;
                    }
                    .tips{
                        display: none;
                        position: absolute;
                        left: -12px;
                        top: 24px;
                        width: 215px;
                        .content{
                            padding: 10px;
                            background: #fff;
                            border: 1px solid #cecbce;
                            box-shadow: 0 0 2px 2px #eee;
                        }
                        .sprite-arrow {
                            @include sprite-arrow;
                            position: absolute;
                            overflow: hidden;
                            left: 15px;
                            top: -5px;
                        }
                    }
                }
                .store-price-tips{
                    // margin-left: 70px;
                    // color: #999;
                }
            }
        }
    }

}

//  腰带项目css
.belt-bg{
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background: url(i/belt-bg.png) no-repeat;
    background-size: 100%;
    z-index: 5;
    p{
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        text-align: center;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, 0);
        -ms-transform:translate(-50%, 0); 	/* IE 9 */
        -moz-transform:translate(-50%, 0);	/* Firefox */
        -webkit-transform:translate(-50%, 0); /* Safari 和 Chrome */
        -o-transform:translate(-50%, 0); 	/* Opera */
    }
}
//  分期价css
.summary-service{
  .p-price{
      color: #FF0F23;
    //   font-family: "microsoft yahei";
      margin-left: 5px;
  }
}
/*xianshitehui*/
.J-xsth-sale{
  margin-left: 10px;
  vertical-align: 2px;
  .J-xsth-panel {
    color: #fff;
    background: #ff8b00;
    padding: 2px 5px 2px 6px;
    margin-right: 2px;
    display: inline-block;
    line-height: 18px;
    font-weight: 600;
    .s-arrow {
      padding-left: 2px;
      font-size: 10px;
    }
  }
  .sprite-question{
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    @include sprite-question;
  }
}

 #pingou-banner-new .sprite-pingou{
     background: url('//img13.360buyimg.com/devfe/jfs/t21118/118/117736536/1383/3fbd777b/5afc35e5Nec3ac600.png') 0 0 no-repeat;
     display: inline-block;
     width: 23px;
     height: 20px;
     position: relative;
     top: 5px;
     margin-right: 10px;
     margin-right: 10px;
}
#pingou-banner-new .activity-type strong{
    color:#fff
  }
 .btn-pingou{
      line-height: 20px;
      font-size: 14px;
  }
.btn-pingou span{
    position: relative;
    font-family: JDZhengHeiVRegular2-1;
    // top:5px;
    &:first-child { // 羊角标
        font-size: 14px;
        margin-left: 6px;
    }
}
 .summary-price > .dt span{
     color:#E4393C;
     display: inline-block;
     width: 16px;
}
 #InitCartUrl.btn-pingou{
    background-color: #ff475d;
    color: #fff;
 }
#InitCartUrl.btn-pingou.btn-disable{
    background: rgba(172, 176, 189, 1);
}
 .line-thro {
    text-decoration: line-through;
    font-size: 14px;
    font-family: JDZhengHeiVRegular2-1; // 划线价字体
    color: #888B94;
 }
 .tuanMemberCount{
    background: #ff0f23;
    color: #fff;
    padding: 0 4px;
    border-radius: 2px;
    font-size: 13px;
    display: inline-block;
    margin-right: 5px;

 }
 .choose-btns .btn-lg.btn-pingou {
    position: relative;// 拼团购二维码相对此按钮定位
 }
 .qrcode-pingou{
    text-align: center;
    top: -220px;
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
    background-color: #fff;
    padding: 10px;
    border: rgba(0, 0, 0, .1019607843);
    border-radius: 8px;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, .1);
 }
 .qrcode-pingou p{
   line-height: 32px;
   font-size: 16px;
   background: #f2f2f2;
   text-align: left;
   padding-left: 20px;
   margin-bottom: 10px;
 }


//  国补楼层
#gb-support{
    .dt{
        line-height: 32px;
        height: 32px;
    }
    .dd{
        font-size: 15px;
        a {
            color: #1A1A1A;
            display: inline-block;
            padding-right: 18px;
            background: url(https://img13.360buyimg.com/imagetools/jfs/t1/259181/16/14667/489/6790bd3aF60de783b/8ed74ff7b6ef4ed6.png) no-repeat;
            background-size: 10px;
            background-position: right center;
        }
        a:hover {
            color: #FF0F23;
        }
    }
    .gb-money,.gb-link{
        color: #FF0F23;
    }
}
#summary-yushou-ship { // 发货时间楼层
    margin-bottom: 30px;
    height: 18px;
    line-height: 18px;
    .dd {
        font-size: 15px;
        font-weight: 400;
        color: #1A1A1A;
        padding: 0;
    }
}
#yiyao-expiryDate {
    margin-bottom: 16px;
    .dt{
        line-height: 32px;
        height: 32px;
    }
    .dd {
        font-size: 15px; // 药品有效期
        color: #1A1A1A;
    }
}

#ttbar-home .logo-icon{
    background: url(https://img12.360buyimg.com/img/jfs/t1/109080/21/58367/14698/675171baFb8b7a6d2/22ad515c81c73261.png);
    display: inline-block;
    height: 17px;
    margin-right: 10px;
    vertical-align: text-top;
    width: 55px;
    background-size: 100%;
    margin-top: -3px;
}
