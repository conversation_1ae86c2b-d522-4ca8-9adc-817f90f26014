@import '../common/lib';
#yajin{
    margin-bottom: 0px;
    line-height: 32px;
}
.choose-days {
    margin-top: 7px;
    .choose-amount {
        width: 62px;
        height: 28px;
        overflow: hidden;
        border: 1px solid #ccc;
        position: relative;
        margin-right: 10px;
        float: left;
        input {
            display: block;
            width: 48px;
            height: 28px;
            line-height: 28px;
            position: absolute;
            top: 0px;
            left: 0;
            border: none;
            border: 0;
            text-align: center;
        }
        a.disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        a {
            display: block;
            width: 15px;
            text-align: center;
            height: 14px;
            line-height: 14px;
            overflow: hidden;
            background: #f1f1f1;
            color: #666;
            position: absolute;
            right: -1px;
            border: 1px solid #ccc;

            &.btn-add {
                top: -1px;
            }

            &.btn-reduce {
                bottom: -1px;
            }
        }
    }
}

