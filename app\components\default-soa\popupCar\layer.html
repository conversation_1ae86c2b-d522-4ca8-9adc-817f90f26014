
<!DOCTYPE HTML>
<html lang="zh-CN" class="root61">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <meta name="keywords" content=""/>
    <meta name="description" content="" />
	<style>
		*{
			padding: 0;
			margin: 0;
		}
		ul,li{
			list-style: none;
		}
		a{
			text-decoration: none;
		}


		.layer-wrap{
			border-radius: 5px;
			border: 5px solid #d6d6d6;
			background: #fff;
		}
		.layer-tit{
			height: 30px;
			line-height: 30px;
			padding: 0 10px;
			background: #f3f3f3;
		}
		.layer-tit h3{
			font-size: 14px;
			color: #666;
			font-family: "Microsoft YaHei";
			float: left;
		}
		.layer-tit .icon-close{
			width: 13px;
			height: 14px;
			float: right;
			background: url(i/icon-close.png) no-repeat;
			text-indent: -9999px;
			margin-top: 7px;
		}
		.layer-con{
			padding: 10px;
			font-size: 12px;
		}
		.car-list{
			overflow: hidden;
			zoom:1;
		}
		.car-list li{
			position: relative;
			width: 198px;
			min-height: 50px;
			margin: 10px;
			padding: 8px 15px;
			float: left;
			border: 1px solid #f2f2f2;
		}
		.car-list .hover:hover,.car-list .current{
			border-color: #e4393c;
		}
		.car-list img{
			width: 43px;
			height: 43px;
			float: left;
		}
		.car-list .car-info{
			margin-left: 55px;
			line-height: 140%;
		}
		.car-list .car-info h4{
			color: #666;
			margin-bottom: 3px;
		}
		.car-list .car-info p{
			color: #968b8b;
		}
		.car-list .item-op{
			display: none;
			float: right;
			color: #e4393c;
		}
		.car-list .hover:hover .item-op{
			display: block;
		}
		.car-list .item-mask{
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background: rgba(0,0,0,.7);
		}
		.car-list .item-layer{
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			text-align: center;
		}
		.car-list .item-layer p{
			padding: 10px 0;
			color: #fff;
		}
		.car-list .item-bt a{
			display: inline-block;
			line-height: 25px;
			padding: 0 20px;
			background: #fff;
			color: #666;
			margin: 0 5px;
		}
		.car-list .item-add{
			display: block;
			line-height: 50px;
			color: #968b8b;
			text-align: center;
		}
		.car-list .item-add:hover{
			color: #e4393c;
		}
		.layer-con .tips{
			color: #999;
			padding: 0 10px;
		}
		.layer-con .layer-bt{
			padding: 10px 0;
			text-align: center;
		}
		.layer-con .layer-bt a{
			display: inline-block;
			line-height: 28px;
			padding: 0 30px;
			color: #fff;
			border: 1px solid #ccc;
			margin: 0 10px;
		}
		.layer-con .layer-bt .bt-confirm{
			background: #e4393c;
			border-color: #e4393c;
		}
		.layer-con .layer-bt .bt-cancel{
			color: #666;
		}
		.layer-con .layer-bt .disabled{
			background: #ccc;
			border-color: #ccc;
		}
		.layer-con .add-car{
			padding: 30px 0 20px;
			text-align: center;
		}
		.layer-con .icon-car{
			display: block;
			width: 79px;
			height: 55px;
			margin: 0 auto 30px;
			background: url(i/car.png) no-repeat;
		}
		.layer-con .add-bt{
			display: inline-block;
			width: 98px;
			height: 28px;
			line-height: 28px;
			border: 1px solid #e4393c;
			color: #e4393c;
		}
	</style>
</head>
	<body>
    	<div class="layer-wrap" style="width:520px;">
    		<div class="layer-tit">
    			<h3>选择车型</h3>
    			<a href="#" class="icon-close">关闭</a>
    		</div>
    		<div class="layer-con">
    			<ul class="car-list">
    				<li>
    					<div class="item-mask"></div>
    					<div class="item-layer">
    						<p>确定删除该车型？</p>
    						<div class="item-bt"><a href="#">删除</a><a href="#">取消</a></div>
    					</div>
    					<a href="#" class="item-op">删除</a>
						<img src="http://img30.360buyimg.com/car/jfs/t2872/8/38123643/22329/f674d372/56fcbadfNc858ae47.png" alt="" />
						<div class="car-info">
							<h4>大众 Golf</h4>
							<p>2012款 1.4TSI 手自一体 舒适型</p>
						</div>
    				</li>
    				<li class="hover">
    					<a href="#" class="item-op">删除</a>
						<img src="http://img30.360buyimg.com/car/jfs/t2611/201/33421171/12810/d8b51852/56fcbb49Ncd48e52c.png" alt="" />
						<div class="car-info">
							<h4>大众 Golf</h4>
							<p>2012款 1.4TSI 手自一体 舒适型</p>
						</div>
    				</li>
    				<li class="current">
    					<a href="#" class="item-op">删除</a>
						<img src="http://img30.360buyimg.com/car/jfs/t2872/8/38123643/22329/f674d372/56fcbadfNc858ae47.png" alt="" />
						<div class="car-info">
							<h4>大众 Golf</h4>
							<p>2012款 1.4TSI 手自一体 舒适型</p>
						</div>
    				</li>
    				<li class="null">
						<a href="#" class="item-add">+ 添加爱车</a>
    				</li>
    			</ul>
    			<p class="tips">最多添加4辆爱车，如需新增，请先删除部分信息</p>
    			<div class="layer-bt">
    				<a href="#" class="bt-confirm disabled">确定</a><a href="#" class="bt-cancel">取消</a>
    			</div>
    		</div>
    	</div>
    	<div class="layer-wrap" style="width:250px;">
    		<div class="layer-tit">
    			<h3>选择车型</h3>
    			<a href="#" class="icon-close">关闭</a>
    		</div>
    		<div class="layer-con">
    			<div class="add-car">
    				<i class="icon-car"></i>
    				<a href="#" class="add-bt">+ 添加爱车</a>
    			</div>
    		</div>
    	</div>
   </body>
</html>
