define('MOD_ROOT/ebook/ebook', function(require, exports, module) {
    var G = require("MOD_ROOT/common/core");
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var dialog = require('JDF_UI/dialog/1.0.0/dialog');
    var Tools = require('MOD_ROOT/common/tools/tools');
    /**
     * 获取购物车数量-接口已下线需要删除功能
     */
    // function getEbookeCart() {
    //     $.ajax({
    //         url: '//gw-e.jd.com/shoppingCart/shoppingCart_shoppingCart.action',
    //         data: {
    //             pin: readCookie('pin')
    //         },
    //         dataType: 'jsonp',
    //         cache: true,
    //         success: function (data) {
    //             if(data.code == 0) {
    //                 if(data.result.map.result && data.result.map.result.count) {
    //                     $("#ebook-cart .cw-icon").append('<i class="ci-count" id="ebook-cart-amount">' + data.result.map.result.count + '</i>');
    //                 }
    //             }
    //         }
    //     });
    // }

    function getPrice(cfg, data) {
        var price = data && data.stock && data.stock.data && data.stock.data.price
        var cat = cfg.cat;
        var p = price.p;
        // 价格设置
        if(cat[0] == 5272 && (cat[1] == 10941 || cat[1] == 14155)) {  //男生原创 女生原创 5272,10941 5272,14155
            var _ebookPrice = '<em style="color:#e4393c;">¥ </em><span style="color:#e4393c;font-size:16px;">0.05/千字</span>';
            $('.J-summary-price .p-price').html(_ebookPrice);
            $('.extra .p-info .p-price').html(_ebookPrice);
        } else if (p === 0) {
            $('.J-summary-price .p-price span').html('');
            $('.J-summary-price .p-price .price').html('免费');
        } else {
            getJDPrice(cfg, p);
        }
        // 按钮屏蔽、温馨提示
        if (p === 0 ) {
            $('#J_read').show();
            $("#InitCartUrl, #InitCartUrl-mini").remove();
            $("#btn-buynow").remove();
            $('#J_freeTxt').show();
        } else {
            $('#J_read').remove();
            $("#InitCartUrl, #InitCartUrl-mini").show();
            $("#btn-buynow").show();
            $('#J_jingDouTxt').show();
        }
        
    }

    function getJDPrice(cfg, p3cnPrice) {
        // 获取京东原始价格
        $.ajax({
            url: '//gw-e.jd.com/forBookCode/forBookCode_getEbookInFoAndOrginPrices4JSONP.action?bookCodes=' + cfg.skuid,
            dataType: 'jsonp',
            scriptCharset: 'utf-8',
            success: function (data) {
                var $pPirce = $('.J-summary-price .p-price');
                var rList = data.result.resultList
                if (rList.length) {
                    var jdPrice = rList[0].jdPrice;
                    if (p3cnPrice < jdPrice && p3cnPrice > 0) {
                        $('.J-prom-price .price').html(p3cnPrice);
                        $pPirce.find('.price').html(jdPrice.toFixed(2));
                        $('.J-summary-price .p-price').addClass("del");
                        $('.J-prom-price').show();
                    } else {
                        //京东价
                        $('.J-summary-price .p-price .price').html(jdPrice.toFixed(2));
                    }
                    if(jdPrice < 2)
                    {
                        $("#choose-btns").remove();
                        $("#closePCShow").show();
                        
                    }
                }
            }
        });
    }

    function addEvents(cfg) {
        // “加入购物车”
        $("#InitCartUrl, #InitCartUrl-mini").click(function () {
            location.href = cfg.buyurl;
            // getBoughtOrNot(cfg, function (isBought) {
            //     if(isBought) {
            //         openIsBoughtEBookDialog();
            //     } else {
            //         location.href = cfg.buyurl;
            //     }
            // });
        });

        // “阅读”
        var $read = $('#J_read');
        if ($read.length > 0) {
            $read.click(function () {
                openFreeEBookDialog(cfg);
            });
        }

        // "在线畅读"
        var $onlineread = $("#btn-onlineread");
        if ($onlineread.length > 0) {
            $onlineread.click(function () {
                openEBookVipDialog(cfg);
            });
        }
        // “立即购买”
        var $buynow = $("#btn-buynow");
        if ($buynow.length > 0) {
            $buynow.click(function () {
                Tools.checkLogin(function (r) {
                    if(!(r && r.IsAuthenticated)){// 未登录
                        window.login && window.login()
                    } else {
                        location.href = cfg.buynowlink;
                    }
                })
                // getBoughtOrNot(cfg, function (isBought) {
                //     if(isBought) {
                //         openIsBoughtEBookDialog();
                //     } else {
                //         location.href = cfg.buynowlink;
                //     }
                // });
            });
        }

        // "客户端下载"
        if (!G.wideVersion) {
            $(".download").hover(function () {
                $(this).addClass('download-hover');
            }, function () {
                $(this).removeClass('download-hover');
            });
        }
    }



    function openEBookVipDialog(cfg) {
        var noCardtpl= '\
        <div class="bookLayer">\
            <p>很遗憾，您目前没有可用的“畅读VIP”服务</p>\
            <div class="btn-wrap">\
                <a href="//sale.jd.com/act/MypqiIJPYx.html" class="red" target="_blank">立即开通畅读</a>\
                <a class="cancel-btn" href="javascript:;">暂不开通</a>\
            </div>\
        </div>';

        // var haveCardtpl = '\
        // <div class="bookLayer">\
        //     <p>请使用<strong>京东阅读客户端</strong>下载阅读！</p>\
        //     <div class="btn-wrap">\
        //         <a href="//sale.jd.com/act/W5hugLDc1R.html" class="red" target="_blank">立即安装</a>\
        //         <a href="LEBK://bought/">启动客户端</a>\
        //         <a class="cancel-btn" href="javascript:;">取消下载</a>\
        //     </div>\
        // </div>';


        seajs.use('jdf/1.0.0/unit/login/1.0.0/login.js',function(login){
            login({
                modal: true,
                complete: function(result) {
                    var dialog;
                    if (result != null && result.Identity.IsAuthenticated != null && result.Identity.IsAuthenticated) {
                        $.ajax({
                            url: "//cread.jd.com/openread/openRead.action",
                            data: {
                                bookId: cfg.skuid,
                                readType: 0
                            },
                            dataType: "jsonp",
                            success: function (r) {
                                var isVIP = r.code != null && r.code == "0";
                                // var tplStr = isVIP ? haveCardtpl : noCardtpl;

                                if (isVIP) {
                                    location.href = 'https://cread.jd.com/read/startRead.action?bookId='+ cfg.skuid +'&readType=0'
                                } else {
                                    dialog = $('body').dialog({
                                        width: 442,
                                        height: 120,
                                        title: '温馨提示',
                                        type: 'html',
                                        source: noCardtpl,
                                        onReady: function() {
                                            var $bookLayer = $(".bookLayer");
                                            $bookLayer.find(".cancel-btn").click(function () {
                                                dialog.close();
                                            });
                                        }
                                    });
                                }
                            }
                        });
                    }
                }
            });
        });
    }


    function openFreeEBookDialog(cfg) {

        seajs.use('jdf/1.0.0/unit/login/1.0.0/login.js',function(login){
            login({
                modal: true,
                complete: function(result) {
                    // var dialog;
                    if (result != null && result.Identity.IsAuthenticated != null && result.Identity.IsAuthenticated) {
                        $.ajax({
                            url: '//gw-e.jd.com/downrecord/downrecord_insert.action?ebookId=' + cfg.skuid,
                            dataType: 'jsonp',
                            cache: true,
                            success: function (r) {
                                if(r.code == 1) {
                                    location.href = 'https://cread.jd.com/read/startRead.action?bookId='+ cfg.skuid +'&readType=3';
                                } else if (r.code == 0) {
                                    alert("系统错误，稍后再试");
                                }
                            }
                        });
                    }
                }
            });
        });
    }




    /**
     * 侧边栏广告位
     */
    function getAd() {
        var ads = ['0_0_8249', '0_0_8250'];
        $.ajax({
            url: '//nfa.jd.com/loadFa_toJson.js?aid=' + ads.join('-') + '&ver=20131107',
            dataType: 'script',
            cache: true,
            success: function () {
            }
        });
    }

    /**
     * 首屏右侧二维码相关文案
     * @param {*} cfg 
     */
    function getTxt(cfg) {
        $.ajax({
            url: '//gw-e.jd.com/client.action?functionId=getScanDesc&body={ebookId:' + cfg.skuid + ',sourceType:0}',
            dataType: "jsonp",
            cache: true,
            success: function (data) {
                if(data.resultCode == 0) {
                    $(".download .download-qrcode-tit h3").html(data.map.scanBook.scanTypeStr);
                    $(".download .qr-code p").html(data.map.scanBook.scanDesc);
                }
            }
        });
    }

    function init(cfg) {
        // 由于stock模块会有修改价格的行为，所以getPrice需要在onStockReady后调用从而保证我们的功能
        Event.addListener('onStockReady', function (data) {
            getPrice(cfg, data);
        });
        // getEbookeCart();
        addEvents(cfg);
        getAd();
        getTxt(cfg);
    }

    module.exports.__id = 'ebook';
    module.exports.init = init;
});
