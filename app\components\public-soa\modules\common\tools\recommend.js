/**
 * 推荐系统公用模型
 */

define('PUBLIC_ROOT/modules/common/tools/recommend', function(require, exports, module) {
    require('JDF_UNIT/trimPath/1.0.0/trimPath');

    var Tools = require('PUBLIC_ROOT/modules/common/tools/tools');

    var Recommend = function(opts) {
        // 推荐位参数
        this.param = $.extend({
            lid   : Tools.getAreaId().areaIds[0] || '',
            lim   : 6,
            ec    : 'utf-8',
            uuid  : -1,
            // pin   : readCookie('pin') || ''
        }, opts.param);

        this.$el       = opts.$el;
        this.template  = opts.template;
        this.showOnData  = typeof opts.showOnData === 'undefined' ? true : opts.showOnData;
        this.loadPrice = typeof opts.loadPrice === 'undefined' ? true : opts.loadPrice;
        this.url = opts.url || '//api.m.jd.com';

        // 是否重组JSON结果集合，每组 n 条数据
        /*
        >> Before
        data: [
            {},
            {},
            {},
            {},
            {},
            {}
            ...
        ]
        >> After
        data: [
            {
                tabs: [{}, {}, {}]
            },
            {
                tabs: [{}, {}, {}]
            },
            ...
        ]
         */
        this.reBuildJSON = opts.reBuildJSON;
        this.autoInit    = typeof opts.autoInit === 'undefined' ? true : false;

        // sku集合，挂载到pageConfig上的哪个变更
        this.skuHooks    = opts.skuHooks || 'SKUS_recommend';
        this.ext         = opts.ext || {};
        this.hasEmptyMsg = opts.hasEmptyMsg || false;

        this.onPriceLoad = opts.onPriceLoad || function() {};
        this.callback    = opts.callback || function() {};
        this.debug       = opts.debug;

        // 判断是否是新的Mixer接口---3c与百货达人选购
        this.isNewMixer = !!opts.isNewMixer;

        if ( !this.param.p ) {
            // throw new Error('The param [p] is not Specificed');
        }

        if ( this.autoInit && this.param.p ) {
            this.init();
        }

        return this;
    };
    Recommend.prototype = {
        init: function() {
            var __jda = readCookie('__jda');


            //if ( this.param.lid.indexOf('-') > 0 ) {
            //    this.param.lid = this.param.lid.split('-')[0];
            //} else {
            //    this.param.lid = '1';
            //}

            // split uuid
            if ( __jda ) {
                if ( __jda.split('.')[1] == '-' ) {
                    this.param.uuid = -1;
                } else {
                    this.param.uuid = __jda.split('.')[1];
                }
            } else {
                this.param.uuid = -1;
            }

            this.get(this.rid);
        },
        get: function(rid, skus) {
            var _this = this;
            var i;
            var queryParam = pageConfig.queryParam;
            var extParam = [];

            // 1，2，3级分类
            if ( pageConfig.product ) {
                for ( i = 0; i < pageConfig.product.cat.length; i++ ) {
                    this.param['c'+(i+1)] = pageConfig.product.cat[i];
                }
            }

            if ( queryParam ) {
                for ( var k in queryParam ) {
                    if ( queryParam.hasOwnProperty(k) ) {
                        if ( k == 'c1' || k == 'c2' || k == 'c3' ) {
                            _this.param[k] = queryParam[k];
                        } else {
                            extParam.push(k + ':' + queryParam[k]);
                        }
                    }
                }
                _this.param.hi = extParam.join(',');
            }
            if ( this.debug ) {
                console.info( this.url + decodeURIComponent($.param(this.param)) );
            }
            // 判断是否是新的Mixer接口---3c与百货达人选购，如果是，重新拼接参数
            if (this.isNewMixer) {
                
                this.param.securityToken = readCookie("shshshfpb") || '';
                this.param.clientChannel = '3';
                this.param.clientPageId = 'item.jd.com';
                
                var body = JSON.stringify(this.param);
                var time = new Date().getTime()
                // 加固start
                var colorParm = {
                    appid: 'item-v3',
                    functionId: 'mixerOut',
                    client: 'pc',
                    clientVersion: '1.0.0',
                    t: time,//生成当前时间毫秒数
                    body: body,
                }
                try{
                    var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                    colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                    window.PSign.sign(colorParmSign).then(function(signedParams){
                        colorParm['h5st']  = encodeURI(signedParams.h5st)
                        getJsToken(function (res) {
                            if(res && res.jsToken){
                                colorParm['x-api-eid-token'] = res.jsToken;
                            }
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                            getMixerData(colorParm);
                        }, 600);
                    })
                }catch(e){
                    colorParm['loginType'] = '3';
                    colorParm['uuid'] = '';
                    getMixerData(colorParm);
                    // try {
                    //     jmfe.jsagentReport(
                    //         jmfe.JSAGENT_EXCEPTION_TYPE.net, //固定值不变
                    //         250,  //固定值: 异常码
                    //         '推荐接口调用错误mixer',  // 异常信息
                    //         {
                    //             fid: 'mixer' , // 网关对应的functionid
                    //             resp: e, 
                    //             body: body 
                    //         }
                    //     )
                    // } catch(er) {
                    //     console.log('上报mixer错误',er)
                    // }
                     //烛龙上报
                    Tools.getJmfe(colorParm, e, "推荐接口调用错误mixer", 300) 
                }            
                // 加固end

                function getMixerData(colorParm){ 
                    var host = '//api.m.jd.com'
                    if(pageConfig.product && pageConfig.product.colorApiDomain){
                        host = pageConfig.product && pageConfig.product.colorApiDomain
                    }
                    $.ajax({
                        url: host,
                        data: colorParm,
                        dataType: 'json',
                        xhrFields: {
                            withCredentials: true,
                        },  
                        success: function(r) {
                            var hasData = !!(r.success && r && r.data && r.data.length);

                            if ( hasData ) {
                                _this.set(r);
                            } else {
                                if ( _this.hasEmptyMsg ) {
                                    _this.$el.html('<div class="ac">「暂无数据」</div>');
                                }
                            }
                            if ( this.debug ) {
                                console.log(r);
                            }

                            _this.callback.apply(_this, [hasData, r]);
                        },
                        error: function (e) {
                            // try {
                            //     jmfe.jsagentReport(
                            //         jmfe.JSAGENT_EXCEPTION_TYPE.net, //固定值不变
                            //         250,  //固定值: 异常码
                            //         '推荐接口调用错误mixer' ,  // 异常信息
                            //         {
                            //             fid: 'mixer' , // 网关对应的functionid
                            //             resp: e, 
                            //             body: colorParm.body 
                            //         }
                            //     )
                            // } catch(er) {
                            //     console.log('上报mixer错误',er)
                            // }
                             //烛龙上报
                            Tools.getJmfe(colorParm, e, "推荐接口调用错误mixer", 300) 
                        }
                    });
                }
            } else {
                this.param.securityToken = readCookie("shshshfpb") || '';
                this.param.clientChannel = '3';
                this.param.clientPageId = 'item.jd.com';
                var body = JSON.stringify(this.param);
                var time = new Date().getTime()
                // 加固start
                var colorParm = {
                    appid: 'item-v3',
                    functionId: 'recDivinerApi',
                    client: 'pc',
                    clientVersion: '1.0.0',
                    t: time,//生成当前时间毫秒数
                    body: body,
                }
                try{
                    var colorParmSign =JSON.parse(JSON.stringify(colorParm))
                    colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
                    window.PSign.sign(colorParmSign).then(function(signedParams){
                        colorParm['h5st']  = encodeURI(signedParams.h5st)
                        try{
                            getJsToken(function (res) {
                                if(res && res.jsToken){
                                    colorParm['x-api-eid-token'] = res.jsToken;
                                    colorParm['loginType'] = '3';
                                    colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                                    getDivinerData(colorParm);
                                }else{
                                    colorParm['loginType'] = '3';
                                    colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
                                    getDivinerData(colorParm);
                                }
                            }, 600);
                        }catch(e){
                            colorParm['loginType'] = '3';
                            colorParm['uuid'] = '';
                            getDivinerData(colorParm);
                        }
                    })
                }catch(e){
                    // try {
                    //     jmfe.jsagentReport(
                    //         jmfe.JSAGENT_EXCEPTION_TYPE.net, //固定值不变
                    //         250,  //固定值: 异常码
                    //         '推荐接口调用错误recDivinerApi',  // 异常信息
                    //         {
                    //             fid: 'recDivinerApi' , // 网关对应的functionid
                    //             resp: e, 
                    //             body: body 
                    //         }
                    //     )
                    // } catch(er) {
                    //     console.log('上报recDivinerApi错误',er)
                    // }
                     //烛龙上报
                     Tools.getJmfe(colorParm, e, "推荐接口调用错误recDivinerApi", 250) 
                }            
                // 加固end
                function getDivinerData(colorParm){ 
                    var host = '//api.m.jd.com'
                    if(pageConfig.product && pageConfig.product.colorApiDomain){
                        host = pageConfig.product && pageConfig.product.colorApiDomain
                    }
                    $.ajax({
                        url: host,
                        data: colorParm,
                        dataType: 'json',
                        xhrFields: {
                            withCredentials: true,
                        },    
                        success: function(r) {
                            var hasData = !!(r.success && r && r.data && r.data.length);

                            if ( hasData ) {
                                _this.set(r);
                            } else {
                                if ( _this.hasEmptyMsg ) {
                                    _this.$el.html('<div class="ac">「暂无数据」</div>');
                                }
                                try {
                                    if(parseInt(r.code) < 10 && r.echo){
                                        var echoCode = r.echo.length > 1000 ? r.echo.substring(0,999) : r.echo;
                                        // jmfe.jsagentReport(
                                        //     jmfe.JSAGENT_EXCEPTION_TYPE.net, //固定值不变
                                        //     250,  //固定值: 异常码
                                        //     '推荐接口调用错误recDivinerApi'  ,  // 异常信息
                                        //     {
                                        //         fid: 'recDivinerApi' , // 网关对应的functionid
                                        //         resp: echoCode, // 只上报code<10(目前有-1,1,2)的数据，上报echo字段
                                        //         body: colorParm.body // body序列化后的字符；由于浏览器对url有长度限制，body约定限定在1000字符内是绝对满足上报条件的，超过部分前端自行截断。
                                        //     }
                                        // )
                                         //烛龙上报
                                        Tools.getJmfe(colorParm, echoCode, "推荐接口调用错误recDivinerApi", 250) 
                                    }
                                } catch(e) {
                                    console.log('上报recDivinerApi错误',e)
                                }
                            }
                            if ( this.debug ) {
                                console.log(r);
                            }

                            _this.callback.apply(_this, [hasData, r]);
                        },
                        error: function (e) {
                            // try {
                            //     jmfe.jsagentReport(
                            //         jmfe.JSAGENT_EXCEPTION_TYPE.net, //固定值不变
                            //         250,  //固定值: 异常码
                            //         '推荐接口调用错误recDivinerApi' ,  // 异常信息
                            //         {
                            //             fid: 'recDivinerApi' , // 网关对应的functionid
                            //             resp: e, 
                            //             body: colorParm.body 
                            //         }
                            //     )
                            // } catch(er) {
                            //     console.log('上报recDivinerApi错误',er)
                            // }
                             //烛龙上报
                             Tools.getJmfe(colorParm, e, "推荐接口调用错误recDivinerApi", 250) 
                        }
                    });
                }
            }

           
        },
        set: function(data) {
            var _this = this;

            pageConfig[this.skuHooks] = [];

            // 挂载sku全局变量钩子
            data.skuHooks = this.skuHooks;

            // 扩展字段调用
            data.ext = this.ext;

            // this.reBuildJSON 结果数据分多少组
            if ( this.reBuildJSON && this.reBuildJSON > 0 ) {
                data.data = Tools.reBuildJSON(data.data, this.reBuildJSON);
            }

            if ( this.debug ) {
                console.log(data);
                alert(this.template.process(data));
            }
            if (this.showOnData) {
                this.$el.show();
            }
            this.$el.html(this.template.process(data));

            if(this.loadPrice) {
                Tools.priceNum({
                    skus: pageConfig[this.skuHooks],
                    $el: this.$el,
                    callback: function (sku, r) {
                        _this.onPriceLoad.call(_this, sku, r);
                    }
                });
            }
            this.setTrackCode(data.impr);
        },
        setTrackCode: function(str) {
            var list = this.$el.find('li');
            var _this = this;
            var exParam = '&m=UA-J2011-1&ref=' + encodeURIComponent(document.referrer);

            list.each(function() {
                var clk = $(this).attr('data-clk');

                $(this).bind('click', function(e) {
                    var currTagName = $(e.target);
                    if (currTagName.is('a') || currTagName.is('img') || currTagName.is('span')) {
                        _this.newImage(clk + exParam, true);
                    }
                    if (currTagName.is('input') && currTagName.attr('checked') == true ) {
                        _this.newImage(clk + exParam, true);
                    }
                });
            });

            this.newImage(str + exParam, true);
        },
        newImage: function(src, random, callback) {
            var img = new Image();
            src = random ? (src + '&random=' + Math.random()+''+(new Date)) : src;

            img.onload = function() {
                if ( typeof callback !== 'undefined' ) {
                    callback(src);
                }
            };

            img.setAttribute('src', src);
        }
    };

    module.exports = Recommend;
});
