@import '../common/lib';
.bookInfo{
    padding: 5px 0 6px;
    color: #999;
    // border-bottom: 1px dotted #dfdfdf;
    .li{
        line-height: 31px;
        margin-bottom: 0;
        p {
            color: #888B94;
            font-size: 15px;
            line-height: 15px;
            height: 15px;
            margin-top: 32px;
            margin-bottom: 12px;
        }
    }
    .author .dd { // 为了右侧箭头
        display: inline-block;
        margin-left: 0px;
    }
    .author .dd,.publishing a{// 出版社
        color: #1a1a1a;
        padding-right: 18px;
        background: url(https://img13.360buyimg.com/imagetools/jfs/t1/259181/16/14667/489/6790bd3aF60de783b/8ed74ff7b6ef4ed6.png) no-repeat;
        background-size: 10px;
        background-position: right center;
       
    }
    .publishing a:hover {
        color: #e4393c;
    }
    .column{
        float: left;
        width: 50%;
        overflow: hidden;
        .infoItem{
            padding-top: 3px;
            margin-top: -3px;
            line-height: 24px;
        }
    }
    .category{
        .dd{
            color: #666;
        }
    }
    .dt{
        float: left;
        color: #888b94;
        width: 80px; // 与普通商品保持一致
        height: 31px;
        line-height: 31px;
        font-size: $baseFontSize;
        font-weight: 400;
        max-height: 48px;
        overflow: hidden; 
    }
    .dd{
        color: #1A1A1A;
        font-size: $baseFontSize;
    }
}
