{% import '../../views/maco/user.html' as wow %}

<div class="m m-aside yuyue-reco hide" id="yuyue-reco">
    <div class="mt"><h3>火热预售</h3></div>
    <div class="mc"></div>
</div>


<div class="m m-aside pingou-reco hide" id="pingou-reco">
    <div class="mt"><h3>火热拼</h3></div>
    <div class="mc"></div>
</div>

<div id="sp-search" class="m m-aside sp-search" clstag="shangpin|keycount|product|pop-03">
    <div class="mt">
        <h3>店内搜索</h3>
    </div>
    <div class="mc">
        <p class="sp-form-item1"><label for="sp-keyword">关键字：</label><span><input type="text" id="sp-keyword" onkeydown="javascript:if(event.keyCode==13){pageConfig.searchClick(1);}"></span></p>
        <p class="sp-form-item2"><label for="sp-price">价　格：</label><span><input type="text" id="sp-price" onkeyup="changeSpPrice('sp-price');" onkeydown="javascript:if(event.keyCode==13){pageConfig.searchClick(1);}"> 到 <input type="text" id="sp-price1" onkeyup="changeSpPrice('sp-price1');" onkeydown="javascript:if(event.keyCode==13){pageConfig.searchClick(1);}"></span></p>
        <p class="sp-form-item3"><label for="">　　　</label><span><input type="submit" value="搜索" id="btnShopSearch" data-url="//sop-mobile.jd.com/view_shop_search-378980.html"></span></p>
    </div>
</div>

<div id="sp-category" class="m m-aside sp-category" clstag="shangpin|keycount|product|pop-04">
    <div class="mt">
        <h3>店内分类</h3>
    </div>
    <div class="mc no-padding">
        <dl class="sp-single">
            <dt class="open"><s></s><a href="//mall.jd.com/view_search-378980-2396722-1-0-20-1.html" target="_blank">全部宝贝</a></dt>
        </dl>
        <dl class="sp-single">
            <dt class="open"><s></s><a href="//mall.jd.com/view_search-378980-2396723-1-0-20-1.html" target="_blank">智能手机系列</a></dt>
        </dl>
    </div>
</div>

<div class="m m-aside pop-hot" id="pop-hot">
    <div class="mc no-padding">
        <div class="ETab">
            <div class="tab-main large">
                <ul>
                    <li data-tab="trigger" class="current">店铺热销</li>
                    <li data-tab="trigger">店铺人气</li>
                </ul>
            </div>
            <div class="tab-con">
                <div id="sp-hot-sale" data-tab="item">
                    {{ wow.plist_pop(id='', class='plist plist-pop', source="src", length=2, width=180, height=180) }}
                </div>
                <div id="sp-hot-fo" data-tab="item" class="hide"></div>
            </div>
        </div>
    </div>
</div>

<div class="m m-aside" id="view-buy">
    <div class="mt">
        <h3>达人选购</h3>
    </div>
    <div class="mc">
        {{ wow.plist(id='', class='plist', source="src", length=2, width=180, height=180) }}
    </div>
</div>


<div class="m m-aside" id="view-view">
    <div class="mt">
        <h3>看了又看</h3>
    </div>
    <div class="mc">
        {{ wow.plist(id='', class='plist', source="src", length=2, width=180, height=180) }}
    </div>
</div>

<div class="m m-aside" id="rank">
    <div class="mt">
        <h3>手机热门销榜</h3>
    </div>
    <div class="mc no-padding">
        <div class="ETab">
            <div class="tab-main medium">
                <ul>
                    <li data-tab="trigger" class="current">同价位<sup>new<b>◤</b></sup></li>
                    <li data-tab="trigger">同品牌</li>
                    <li data-tab="trigger">总排行</li>
                </ul>
            </div>
            <div class="tab-con">
                <div data-tab="item">
                    {{ wow.plist(id='', class='plist-1', length=2, source="data-lazyload", width=85, height=85) }}
                </div>
                <div data-tab="item" class="hide">请选择1</div>
                <div data-tab="item" class="hide">请选择2</div>
            </div>
        </div>
    </div>
</div>


<div id="sp-new" class="m m-aside"></div>

<div class="m side-ads"><div class="mc" id="Ad2_100_2272"></div></div>
<div id="miaozhen7886" class="m" clstag="shangpin|keycount|product|ad_{* pType *}"></div>
<div id="miaozhen10767" class="m" clstag="shangpin|keycount|product|ad_{* pType *}"></div>
