define('MOD_ROOT/book/book', function(require, exports, module) {
    var Tools = require('MOD_ROOT/common/tools/tools');
    var Event = require('MOD_ROOT/common/tools/event').Event;
    var Recommend = require('MOD_ROOT/common/tools/recommend');

    // var trimPath      = require('JDF_UNIT/trimPath/1.0.0/trimPath');
    var lazyload      = require('JDF_UI/lazyload/1.0.0/lazyload');
    var switchable    = require('JDF_UI/switchable/1.0.0/switchable');
    var FittingSuit   = require('MOD_ROOT/fittingSuit/fittingSuit');
    var CombineBuy    = require('MOD_ROOT/combine/combine');
    var G = require('MOD_ROOT/common/core');
    var imgScroll     = require('MOD_ROOT/common/plugins/jQuery.imgScroll');

    var template = '\
        <div class="mt">\
            <h2>${ext.title}</h2>\
        </div>\
        <div class="mc">\
            <ul>\
            {for item in data}\
            <li class="fore${Number(item_index)+1}" data-clk="${item.clk}" onclick=\'reClick("${G.mdPerfix}2","${G.sku}","${item.sku}#${item.jp}",${item_index});\' data-push="${pageConfig[skuHooks].push(item.sku)}">\
                <div class="p-img template-sale">\
                    <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                        <img height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" alt="${item.t}" data-lazy-img="${pageConfig.FN_GetImageDomain(item.sku)}{if ext.imgSize}${ext.imgSize}{else}n4{/if}/${item.img}" />\
                    </a>\
                </div>\
                <div class="p-name"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
                <div class="p-price"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
            </li>\
            {/for}\
            </ul>\
        </div>';

    var TPL_hot_sale = '\
        <div class="mt">\
            <h2>${ext.title}</h2>\
        </div>\
        <div class="mc">\
            <ul>\
            {for item in data}\
                <li class="fore${Number(item_index)+1}" data-clk="${item.clk}" data-push="${pageConfig[skuHooks].push(item.sku)}">\
                    <div class="p-img tpl-hot-sale">\
                        <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                            <img \
                                height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" \
                                width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" \
                                alt="${item.t}" \
                                data-lazy-img="${pageConfig.FN_GetImageDomain(item.sku)}{if ext.imgSize}${ext.imgSize}{else}n4{/if}/${item.img}" />\
                        </a>\
                    </div>\
                    <div class="p-name"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
                    <div class="p-info p-bfc">\
                        <div class="p-price fr"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
                    </div>\
                </li>\
            {/for}\
            </ul>\
        </div>';


    var TPL_hot_fo = '\
        <div class="mt">\
            <h2>${ext.title}</h2>\
        </div>\
        <div class="mc">\
            <ul>\
            {for item in data}\
                <li class="fore${Number(item_index)+1}" \
                    data-clk="${item.clk}" \
                    data-push="${pageConfig[skuHooks].push(item.sku)}">\
                    <div class="p-img tpl-hot-fo">\
                        <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                            <img \
                                height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" \
                                width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" \
                                alt="${item.t}" \
                                data-lazy-img="${pageConfig.FN_GetImageDomain(item.sku)}{if ext.imgSize}${ext.imgSize}{else}n4{/if}/${item.img}" />\
                        </a>\
                    </div>\
                    <div class="p-name"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
                    <div class="p-info p-bfc">\
                        <div class="p-price fr"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
                    </div>\
                </li>\
            {/for}\
            </ul>\
        </div>';

    var TPL_pop_book_side = '\
        <div class="mt">\
            <h2>${ext.title}</h2>\
        </div>\
        <div class="mc">\
            <ul>\
            {for item in data}\
            <li class="fore${Number(item_index)+1}" data-clk="${item.clk}" data-push="${pageConfig[skuHooks].push(item.sku)}">\
                <div class="p-img tpl-pop-book-side">\
                    <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                        <img height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" alt="${item.t}" \
                        data-lazy-img="${pageConfig.FN_GetImageDomain(item.sku)}{if ext.imgSize}${ext.imgSize}{else}n4{/if}/${item.img}" />\
                    </a>\
                </div>\
                <div class="p-name ac"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
                <div class="p-price"><strong class="J-p2-${item.sku}">￥${item.jp}</strong></div>\
            </li>\
            {/for}\
            </ul>\
        </div>';

    var TPL_pop_book_hot = '\
         <div class="m-box-hd">\
            <h3 class="title">${ext.title}</h3>\
        </div>\
        <div class="m-box-bd">\
            <div class="m-switch book-rec-switch" >\
                <div class="m-switch-main">\
                    <ul class="m-switch-panel book-rec-list">\
                        {for item in data}\
                        <li class="m-sw-item fore${Number(item_index)+1}" data-clk="${item.clk}" data-push="${pageConfig[skuHooks].push(item.sku)}">\
                            <div class="p-img tpl-pop-book-hot">\
                                <a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html" id="{if ext.divId}${ext.divId}${item.sku}{else}sku${item.sku}{/if}" style="position: relative;display: block">\
                                    <img height="{if ext.imgHeight}${ext.imgHeight}{else}100{/if}" width="{if ext.imgWidth}${ext.imgWidth}{else}100{/if}" alt="${item.t}" data-lazy-img="${pageConfig.FN_GetImageDomain(item.sku)}{if ext.imgSize}${ext.imgSize}{else}n4{/if}/${item.img}" />\
                                </a>\
                            </div>\
                            <div class="p-name ac"><a target="_blank" title="${item.t}" href="//item.jd.com/${item.sku}.html">${item.t}</a></div>\
                            <div class="p-comment J-comm-${item.sku}">\
                                <a target="_blank" href="//item.jd.com/${item.sku}.html#comment" class="number"><em>0</em>条</a>\
                                <span class="good">(<em>97</em>%好评)</span>\
                            </div>\
                            <div class="p-price">\
                                <strong class="number J-p2-${item.sku}">￥${item.jp}</strong>\
                            </div>\
                        </li>\
                        {/for}\
                    </ul>\
                </div>\
                <div class="m-switch-page">\
                    <span class="m-switch-prev">上一页</span>\
                    <span class="m-switch-next">下一页</span>\
                </div>\
            </div>\
        </div>';

    function mLazyload($el, cb) {
        var $document = $(document);

        if ( $el.length > 0 ) {
            $document.lazyload({
                type: 'fn',
                source: $el,
                space: 200,
                onchange: cb
            });
        }
    }

    function loadImage(hasData, r) {
        this.$el.lazyload({
            type: 'img'
        });
        try{
            if(hasData){
                // 主图浮层初始化渲染
                // var skuArrs = []
                // for(i = 0;i < r.data.length; i++){
                //     skuArrs.push(r.data[i].sku)
                // }
                // Tools.getMainPic(r.ext.imgWidth, r.ext.imgHeight, r.ext.divId, "2", skuArrs)
                var layerList = r.floatLayerList
                if(layerList.length > 0){
                    Tools.getPcSkuLayers(layerList, r.ext.imgWidth, r.ext.imgHeight, r.ext.divId, "2")
                }
            }
        }catch(e){
            console.log("主图浮层初始化渲染",e)
        }
    }

    function sideReco(pType) {
        var cfg = (window &&
            window.pageConfig &&
            window.pageConfig.product) || {};

        var shopId = pageConfig.product.shopId;

        var browseBrowse     = $('#browse-browse');
        var browseBrowsePop  = $('#browse-browse-pop');
        var buyBuy           = $('#buy-buy');
        var viewBuy          = $('#view-buy');

        var popHotSale       = $('#sp-hot-sale');
        var popHotFo         = $('#sp-hot-fo');

        /* POP 图书推荐位 */
        // 上新宝贝
        var $reco_610006 = $('#reco_610006');
        // 销售排行
        var $reco_610007 = $('#reco_610007');

        // 达人选购
        var $reco_104002 = $('#reco_104002');
        // 看了又看
        var $reco_104000 = $('#reco_104000');
        // POP店铺新品
        var $reco_610008 = $('#reco_610008');

        // 人气好书
        var $reco_610005 = $('#reco_610005');

        // 音响看了又看
        var $reco_104022 = $('#reco_104022');

        // 音响达人选购
        var $reco_104023 = $('#reco_104023');


        var viewBuyPid = null;
        var browseBrowsePid = null;
        var buybuyPid = null;

        var url = null;

        if ( pType === 1 ) {
            viewBuyPid = 103001;
            browseBrowsePid = 105000;
        }

        if ( pType === 2 ) {
            viewBuyPid = 102003;
            browseBrowsePid = 102004;
        }
        if ( pType === 3 ) {
            buybuyPid = 104002;
            browseBrowsePid = 104000;
            template = TPL_pop_book_side;
            buyBuy = $reco_104002;
            browseBrowse = $reco_104000;
        }

        if ( pType === 4 ) {
            buybuyPid = 104023;
            browseBrowsePid = 104022;
            template = TPL_pop_book_side;
            buyBuy = $reco_104023;
            browseBrowse = $reco_104022;
        }

        if (viewBuyPid) {
            mLazyload(viewBuy, function () {
                new Recommend({
                    url: '//api.m.jd.com',
                    $el: viewBuy,
                    skuHooks: 'SKUS_viewbuy',
                    template: template,
                    ext: {
                        title: '达人选购',
                        imgHeight: '100',
                        imgWidth: '100',
                        divId: 'bookdrxg2-'
                    },
                    param: {
                        p: viewBuyPid,
                        sku: pageConfig.product.skuid,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    isNewMixer: true,
                    callback: loadImage
                });
            });
            if(viewBuy.length > 0){
                var skuId = pageConfig && pageConfig.product && pageConfig.product.skuid || '-';
                try {
                    expLogJSON('smb_pc', 'exposure_darenxuangou', '{"sku": ' + skuId + '}')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('达人选购曝光埋点错误');
                    }
                }
            }
        }

        if (browseBrowsePid && !pageConfig.product.closeRecom) {
            if ( pType === 3 ) {
                mLazyload(browseBrowse, function () {
                    new Recommend({
                        url: url,
                        $el: browseBrowse,
                        skuHooks: 'SKUS_browsebrowse',
                        template: template,
                        ext: {
                            title: '看了又看',
                            imgHeight: '100',
                            imgWidth: '100',
                            divId: 'bookklyk1-'
                        },
                        param: {
                            p: browseBrowsePid,
                            sku: pageConfig.product.skuid,
                            ck: 'pin,ipLocation,atw,aview'
                        },
                        callback: loadImage
                    });
                });
            } else if ( pType === 4 ) {
                mLazyload(browseBrowse, function () {
                    new Recommend({
                        url: url,
                        $el: browseBrowse,
                        skuHooks: 'SKUS_browsebrowse',
                        template: template,
                        ext: {
                            title: '看了又看',
                            imgHeight: '100',
                            imgWidth: '100',
                            divId: 'bookklyk2-'
                        },
                        param: {
                            p: browseBrowsePid,
                            sku: pageConfig.product.skuid,
                            lim: 8,
                            ck: 'pin,ipLocation,atw'
                        },
                        callback: loadImage
                    });
                });
            } else {
                mLazyload(browseBrowse, function () {
                    new Recommend({
                        url: url,
                        $el: browseBrowse,
                        skuHooks: 'SKUS_browsebrowse',
                        template: template,
                        ext: {
                            title: '看了又看',
                            imgHeight: '100',
                            imgWidth: '100',
                            divId: 'bookklyk3-'
                        },
                        param: {
                            p: browseBrowsePid,
                            sku: pageConfig.product.skuid,
                            lim: 8,
                            ck: 'pin,ipLocation,atw'
                        },
                        callback: loadImage
                    });
                });
            }
        }

        if (buybuyPid) {
            mLazyload(buyBuy, function () {
                new Recommend({
                    url: url,
                    $el: buyBuy,
                    skuHooks: 'SKUS_buyBuy',
                    template: template,
                    ext: {
                        title: '达人选购',
                        imgHeight: '100',
                        imgWidth: '100',
                        divId: 'bookdrxg1-'
                    },
                    param: {
                        p: buybuyPid,
                        sku: pageConfig.product.skuid,
                        lim: 8,
                        ck: 'pin,ipLocation,atw'
                    },
                    callback: loadImage
                });
            });
        }

        var isOnSale = cfg.warestatus == 1 ? true : false;

        if ( pType === 3 || pType === 4 ) {

            // 图书音响排行榜
            var $hotCommodity = $('#hot-commodity');
            // 七日
            var $reco_rank_0 = $('#reco-rank-0');
            // 新书
            var $reco_rank_1 = $('#reco-rank-1');

            if ( pageConfig.product.isPop ) {
                // 只有pop图书才有
                mLazyload($reco_610006, function () {
                    var reco_610006 = new Recommend({
                        $el: $reco_610006,
                        skuHooks: 'SKUS_610006',
                        template: TPL_pop_book_side,
                        ext: {
                            title: '上新宝贝',
                            imgHeight: '100',
                            imgWidth: '100',
                            divId: 'booksxbb-'
                        },
                        param: {
                            p: 610006,
                            sku: shopId,
                            lim: 5,
                            ck: 'pin,ipLocation,atw'
                        },
                        callback: loadImage
                    });
                });
                mLazyload($reco_610007, function () {
                    var reco_610007 = new Recommend({
                        $el: $reco_610007,
                        skuHooks: 'SKUS_610007',
                        template: TPL_pop_book_side,
                        // template: TPL_hot_fo,
                        ext: {
                            title: '销售排行',
                            imgHeight: '100',
                            imgWidth: '100',
                            divId: 'bookxsph-'
                        },
                        param: {
                            p: 610007,
                            sku: shopId,
                            lim: 5,
                            ck: 'pin,ipLocation,atw'
                        },
                        callback: loadImage
                    });
                });
                isOnSale && mLazyload($reco_610005, function () {
                    var reco_610005 = new Recommend({
                        $el: $reco_610005,
                        skuHooks: 'SKUS_610005',
                        template: TPL_pop_book_hot,
                        ext: {
                            title: '人气好书',
                            divId: 'bookklyk4-'
                        },
                        param: {
                            p: 610005,
                            sku: pageConfig.product.skuid,
                            lim: 18,
                            ck: 'pin,ipLocation,atw'
                        },
                        callback: function (hasData, r) {
                            var $target = this.$el;
                            var num = pageConfig.wideVersion ? 6 : 4;
                            this.$el.lazyload({
                                type: 'img'
                            });

                            var $scroller = this.$el.find('.m-switch-main');
                            var $prev = this.$el.find('.m-switch-prev');
                            var $next = this.$el.find('.m-switch-next');

                            $scroller.imgScroll({
                                disableClass: 'disable',
                                disableClassPerfix: 'm-switch',
                                prev: $prev,
                                next: $next,
                                visible: num,
                                step: num
                            });

                            Tools.commentMeta({
                                skus: pageConfig['SKUS_610005'],
                                $el: $target,
                                onlyData: true,
                                callback: function(sku, r) {
                                    $target.find('.J-comm-'+sku+' .number em').html(r.CommentCount);
                                    $target.find('.J-comm-'+sku+' .good em').html(r.GoodRateShow);
                                }
                            });
                            try{
                                if(hasData){
                                    // 主图浮层初始化渲染
                                    // var skuArrs = []
                                    // for(i = 0;i < r.data.length; i++){
                                    //     skuArrs.push(r.data[i].sku)
                                    // }
                                    // Tools.getMainPic(100, 100, 'bookklyk4-', "2", skuArrs)
                                    var layerList = r.floatLayerList
                                    if(layerList.length > 0){
                                        Tools.getPcSkuLayers(layerList, 100, 100, 'bookklyk4-', "2")
                                    }
                                    
                                }
                            }catch(e){
                                console.log("主图浮层初始化渲染",e)
                            }
                            
                        }
                    });
                });
            }

            // 排行榜tab切换
            $hotCommodity.switchable({
                navSelectedClass: 'curr',
                callback: function (i) {
                    var panel = this.main.eq(i);

                    if ( i == 1 && !panel.attr('data-loaded') ) {
                        loadHotRank();
                        panel.attr('data-loaded', 1);
                    }
                }
            });

            var rank0Rid = null;
            var rank1Rid = null;

            if ( pType === 3 ) {
                rank0Rid = 104004;
                rank1Rid = 104005;
            }
            if ( pType === 4 ) {
                rank0Rid = 104020;
                rank1Rid = 104021;
            }

        }

        // pop 闭环详情页
        if ( pageConfig.product.isPop) {
            // 看了又看
            if ( !pageConfig.product.closeRecom) {
                mLazyload(browseBrowsePop, function () {
                    var rec_902029 = new Recommend({
                        $el: browseBrowsePop,
                        skuHooks: 'SKUS_browsebrowse',
                        template: template,
                        ext: {
                            title: '看了又看',
                            imgSize: 'n2',
                            imgHeight: '160',
                            imgWidth: '160',
                            divId: 'bookklyk4-'
                        },
                        param: {
                            p: 902029,
                            sku: pageConfig.product.skuid,
                            ck: 'pin,ipLocation,atw,aview'
                        },
                        callback: loadImage
                    });
                });
            }
            // 店铺新品
            mLazyload($reco_610008, function () {
                var rec_610008 = new Recommend({
                    $el: $reco_610008,
                    skuHooks: 'SKUS_new_rank',
                    template: template,
                    ext: {
                        title: '店铺新品',
                        imgSize: 'n2',
                        imgHeight: '160',
                        imgWidth: '160',
                        divId: 'bookdpxp-'
                    },
                    param: {
                        p: 610008,
                        sku: shopId,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    callback: loadImage
                });
                var skuId = pageConfig && pageConfig.product && pageConfig.product.skuid || '-';
                try {
                    expLogJSON('smb_pc', 'exposure_dianpuxinpin', '{"sku": ' + skuId + '}')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('曝光埋点错误');
                    }
                }  
            });

            mLazyload(popHotSale, function () {
                var rec_509001 = new Recommend({
                    $el: popHotSale,
                    skuHooks: 'SKUS_popHotSale',
                    template: TPL_hot_sale,
                    ext: {
                        title: '店铺热销',
                        imgSize: 'n2',
                        imgHeight: '160',
                        imgWidth: '160',
                        divId: 'bookdprx-'
                    },
                    param: {
                        p: 509001,
                        sku: shopId,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    callback: loadImage
                });
            });

            isOnSale && mLazyload(popHotFo, function () {
                var rec_509002 = new Recommend({
                    $el: popHotFo,
                    skuHooks: 'SKUS_popHotFo',
                    template: TPL_hot_fo,
                    ext: {
                        title: '店铺人气',
                        imgSize: 'n2',
                        imgHeight: '160',
                        imgWidth: '160',
                        divId: 'bookdprq-'
                    },
                    param: {
                        p: 509002,
                        sku: shopId,
                        ck: 'pin,ipLocation,atw,aview'
                    },
                    callback: loadImage
                });
            });
        }

        /*
        推荐位id	推荐位名称	前台显示数量
        104001	经常一起购买	lim=12
        610005	人气好书	lim=18
        610006	上新宝贝	lim=5
        610007	销售排行	lim=5
        104002	达人选购	lim=8
        104000	看了又看	lim=8
        */
        // 图书闭环详情页

        var $sideCategory = $('#sp-category');

        $sideCategory.delegate('dt s', 'click', function () {
            var $this = $(this);
            var $parent = $(this).parents('dl').eq(0);

            $parent.toggleClass('open');
        });
    }

    var cfg = (window.pageConfig &&
        window.pageConfig.product) || {};

    var fittingSuit   = $('#fitting-suit'); 
    // 配件套装tab
    if( fittingSuit.length!==0 ){
        var fittingSuitTab = fittingSuit.switchable({
            event: 'click',
            hasSetup: true,
            delay: 0,
            navSelectedClass: 'curr',
            callback: function (i) {
                this.main.hide();
                this.main.eq(i).show();
            }
        });

        // 配件、组合、套装 tab 切换逻辑
        pageConfig.fittingSuitTab = {
            // 优先显示哪个tab: null > 速度优先 0 > 推荐配件 1 > 优惠套装 3 > 最佳组合
            top: -1,
            // 停止检测tab状态
            stopDetect: false,
            $el: fittingSuit,
            nav: fittingSuitTab.nav,
            show: function (n) {
                // 大家电分类
                var isDjd = G.cat[2] === 798;
                // 数码分类
                var isDigit = G.cat[1] === 654 || G.cat[1] === 829;
                // 手机大家电数码
                if ( pageConfig.isPhoneCat || isDjd || isDigit ) {
                    this.top = 0;
                }
                // 空调分类优先显示优惠套装> 1282288
                // 只有最佳组合> 1699666721
                if ( G.cat[2] === 870 ) {
                    this.top = 1;
                }

                if (this.nav.eq(n).data('disabled') !== 1) {
                    this.nav.eq(n).show();
                    this.$el.show();
                }

                if ( /debug=seq/.test(location.href) ) {
                    console.log( (this.top === -1 ? '速度优先': 'Tab索引优先('+ this.top +') > ') + n);
                }

                if ( !this.stopDetect ) {
                    this.display(n);
                }
            },
            display: function (n) {
                // 如果是速度优先，只要display被调用过一次，下一次就不用切换选项卡
                this.nav.eq(n).trigger('click');
                if ( n === this.top || this.top === -1 ) {
                    this.stopDetect = true;
                }

                // if ( this.top === -1 ) {
                //     this.nav.eq(n).trigger('click');
                //     this.stopDetect = true;
                // } else {
                //     this.nav.eq(n).trigger('click');

                //     // 置顶的已显示
                //     if ( n === this.top ) {
                //         this.stopDetect = true;
                //     }
                // }
            }
        };

        
    }   
    /// 首屏右侧边栏扩展信息
    // function bookRightMessage(cfg) {
    //     var spuSort = {
    //         "1620": "1-家居家装",
    //         "5025": "1-钟表",
    //         "6219": "2-水具酒具",
    //         "6233": "1-玩具乐器",
    //         "6994": "1-宠物生活",
    //         "6196": "1-厨具",
    //         "1319": "1-母婴",
    //         "1320": "1-食品饮料、保健食品",
    //         "1315": "1-服饰内衣",
    //         "4837": "3-办公文具",
    //         "1466": "2-体育娱乐",
    //         "1467": "2-成人用品",
    //         "1463": "2-运动器械",
    //         "6728": "1-汽车用品",
    //         "1713": "1-图书"
    //     };
    //     if (
    //         (spuSort[cfg.cat[0]] || spuSort[cfg.cat[1]] || spuSort[cfg.cat[2]]) && 
    //         $.inArray("isPickingGoods-1", cfg.specialAttrs) === -1 &&
    //         $.inArray("isPickingGoods-2", cfg.specialAttrs) === -1 &&
    //         $.inArray("" + cfg.skuid, ["12650902", "12651320", "12547423"]) === -1
    //     ) {
    //         var spuServiceUrl = "//spu.jd.com/json.html?cond=";
    //         var spuPageUrl = "//spu.jd.com/" + cfg.skuid + ".html";
    //         var pType = cfg.pType;

    //         $.ajax({
    //             url: spuServiceUrl + "1_4_1_0_0_" + (cfg.cat[0] == 1713 ? "1" : "0") + "_" + cfg.skuid + "_1",
    //             dataType: 'jsonp',
    //             success: function (r) { 
    //                 if (!r || r.totalCount < 2) return;
    //                 var minPrice = r.minPrice + '';
    //                 var iPoint = minPrice.indexOf('.');
    //                 minPrice += iPoint < 0 ? '.00' : (minPrice.length - iPoint - 1 > 1 ? '' : '0');
    //                 r.minPrice = minPrice;
    //                 if ($("#ypds-list").length == 0) {
    //                     $("#extInfo").append('<div class="pop-store-list" id="ypds-list"></div>');
    //                 }
    //                 var topCount = 0;
    //                 var cutCount = 0;
    //                 var spuVenderInfos = '';
    //                 if (cfg.cat[0] != 1713) {
    //                     spuVenderInfos = '<div class="pop-store-item" clstag="shangpin|keycount|product|' + pType + '_onsale"><div class="c-left"><a href="' + spuPageUrl + '" class="hl_blue" target="_blank">' + (r.totalCount - cutCount) + '个卖家在售</a></div><div class="c-right"><span class="price">\u3000￥' + (r.minPrice + "") + '起</span></div></div>';
    //                 }
    //                 for (var i = 0, j = r.skuStockVenders.length; i < j; i++) {
    //                     if (cfg.skuid + "" != r.skuStockVenders[i].skuId && topCount < 3) {
    //                         if (r.skuStockVenders[i].venderId == 46875) { //屏蔽TJ
    //                             cutCount++;
    //                         } else {
    //                             spuVenderInfos += '<div class="pop-store-item" id="J_' + r.skuStockVenders[i].skuId + '"><div class="c-left"><a class="store-name" href="//item.jd.com/' + r.skuStockVenders[i].skuId + '.html" clstag="shangpin|keycount|product|' + pType + '_maijia' + (topCount + 1) + '_0" target="_blank">' +
    //                                 (r.skuStockVenders[i].venderName ? r.skuStockVenders[i].venderName : '京东商城') + '</a></div><div class="c-right"><span class="price"></span></div></div>';
    //                             topCount++;
    //                         }
    //                     }
    //                 }
    //                 spuVenderInfos += '<div class="btnbox" clstag="shangpin|keycount|product|' + pType + '_allsale_0"><a href="' + spuPageUrl + '" class="btn-def" target="_blank">' + (r.totalCount - cutCount) + '个卖家在售</a></div>';
    //                 $("#ypds-list").html(spuVenderInfos);
    //                 //注释掉一品多商底部模块，重复了，新版没有
    //                 // $('<div id="ypds-info" clstag="shangpin|keycount|product|' + pType + '_yipinduoshang_0"><a href="' + spuPageUrl + '" class="hl_blue" target="_blank">' + (r.totalCount - cutCount) + '个卖家在售</a><span class="hl_red">\u3000￥' + (r.minPrice + "") + '</span> 起</div>').insertAfter("#choose");
    //                 var sellerArray = $("#ypds-list .pop-store-item[id^='J_']");
    //                 var skuIds = [];
    //                 for (var i = 0, j = sellerArray.length; i < j; i++) {
    //                     skuIds.push(sellerArray.eq(i).attr("id"));
    //                 }
    //                 if (skuIds.length > 0) {
    //                     var paramJson = {
    //                         "skuIds": skuIds.join(","),
    //                         "area": readCookie("ipLoc-djd") && readCookie("ipLoc-djd").replace(/-/g,"_") || '',
    //                         "fields": '11100000',
    //                         "pin": readCookie("pin") || '',
    //                         "source": "pc-item"
    //                     };
    //                     var body = JSON.stringify(paramJson);
    //                     var time = new Date().getTime()
    //                     // 加固start
    //                     var colorParm = {
    //                         appid: 'item-v3',
    //                         functionId: 'pctradesoa_getprice',
    //                         client: 'pc',
    //                         clientVersion: '1.0.0',
    //                         t: time,//生成当前时间毫秒数
    //                         body: body,
    //                     }
    //                     try{
    //                         var colorParmSign =JSON.parse(JSON.stringify(colorParm))
    //                         colorParmSign['body'] = SHA256(body).toString() //签名参数body需SHA256加密
    //                         window.PSign.sign(colorParmSign).then(function(signedParams){
    //                             colorParm['h5st']  = encodeURI(signedParams.h5st)
    //                             try{
    //                                 getJsToken(function (res) {
    //                                     if(res && res.jsToken){
    //                                         colorParm['x-api-eid-token'] = res.jsToken;
    //                                         colorParm['loginType'] = '3';
    //                                         colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
    //                                         getPriceData(colorParm);
    //                                     }else{
    //                                         colorParm['loginType'] = '3';
    //                                         colorParm['uuid'] = Tools.getCookieNew("__jda") || '';
    //                                         getPriceData(colorParm);
    //                                     }
    //                                 }, 600);
    //                             }catch(e){
    //                                 colorParm['loginType'] = '3';
    //                                 colorParm['uuid'] = '';
    //                                 getPriceData(colorParm);
    //                                 //烛龙上报
    //                                 Tools.getJmfe(colorParm, e, "book网关设备指纹异常", 751)
    //                             }
    //                         })
    //                     }catch(e){
    //                         colorParm['loginType'] = '3';
    //                         colorParm['uuid'] = '';
    //                         getPriceData(colorParm);
    //                         //烛龙上报
    //                         Tools.getJmfe(colorParm, e, "book网关加固异常",  751)
    //                     }            
    //                     // 加固end
    //                     function getPriceData(colorParm){ 
    //                         var host = '//api.m.jd.com'
    //                         if(pageConfig.product && pageConfig.product.colorApiDomain){
    //                             host = pageConfig.product && pageConfig.product.colorApiDomain
    //                         }
    //                         $.ajax({
    //                             url: host,
    //                             data: colorParm,
    //                             dataType: 'json',
    //                             xhrFields: {
    //                                 withCredentials: true,
    //                             },
    //                             headers: Tools.getUrlSdx(),                
    //                             success: function (r) {
    //                                 if (r) {
    //                                     if(parseInt(r.code) < 10 && r.echo){
    //                                         try {
    //                                             var echoCode = r.echo.length>1000 ? r.echo.substring(0,999) : r.echo;
    //                                             //烛龙上报
    //                                             Tools.getJmfe(colorParm, echoCode, "book网关成功异常", 751)
    //                                         } catch(e) {
    //                                             console.log('上报pctradesoa_getprice错误',e)
    //                                         }
    //                                     }else{
    //                                         for (var i = 0, j = r.length; i < j; i++) {
    //                                             $("#ypds-list #" + r[i].id + " .price").html(new Number(r[i].p) > 0 ? ("￥" + r[i].p) : "暂无报价");
    //                                         }
    //                                     }
    //                                 }
    //                             },
    //                             error: function (e) {
    //                                 //烛龙上报
    //                                 Tools.getJmfe(colorParm, e, "book网关error异常", 751)
    //                             }
    //                         });
    //                     }       
                        
    //                 }
    //             },
    //             error: function (xhr, status, error) { 
    //                 console && console.error('spu.jd.com/json.html接口数据调用失败，具体如下：\n' + error);
    //             }
    //         });
    //     }
    // }

    /// 图书试读与排名
    function sampleRank(cfg,data){
        //if (G.isSelfBook || G.isSelfMvd) { //自营图书和自营MVD
        var ebookInfo = data && data.stock && data.stock.data && data.stock.data.ebookInfo
        var rankInfo = data && data.stock && data.stock.data && data.stock.data.rankInfo
        if(cfg.isSelfBook || cfg.isSelfMvd) {  
            // var cfg = window.pageConfig.product;
            var pType = cfg.pType;
            // var areaCode = Tools.getAreaId().areaIds.join('_');

            // $.ajax({
            //     type: 'GET',
            //     url: '//cd.jd.com/book',
            //     data: {
            //         skuId: cfg.skuid,
            //         cat: cfg.cat.join(),
            //         area: areaCode
            //     },
            //     dataType: 'jsonp',
            //     scriptCharset: "gbk",
            //     success: function (data) {
                    // if (!data) { return; }
                    /// 待优化逻辑
                    // if (data.isNew) {
                    //     pageConfig.product.isNewBookDesc = "新品上架即将到货，请<strong>“</strong>关注商品<strong>”</strong>或订阅<strong>“</strong>到货通知<strong>”</strong>。";
                    //     if (pageConfig.product.isNewBookDescState == "noshow") {
                    //         $("#store-prompt").html($("#store-prompt").html().replace('此商品暂时售完', pageConfig.product.isNewBookDesc));
                    //         $('#store-desc').remove();
                    //     }
                    // }
                    /// 电子书试读
                    var ebookId = ebookInfo && ebookInfo.ebookId;
                    // if (G.isSelfBook && ebookId) { //自营图书
                    if (cfg.isSelfBook && ebookId) { //自营图书
                        if (ebookInfo.yn == 1 || ebookInfo.yn == 2) {
                            $("#preview .i-book-sample")
                            .attr("href", "//cread.jd.com/read/startRead.action?bookId=" + ebookId + "&readType=1")
                            .show();
                        }
                        /// 购买电子书按钮
                        if (ebookInfo.yn == 1) {   // 只有有效时才显示价格
                            var jp = new Number(ebookInfo.jdPrice);
                            if (jp > 0) {
                                var price = "￥" + jp;
                            } else if (jp == 0) { // 只有电子书才有免费
                                var price = "免费";
                            } else {
                                var price = "暂无报价";
                            }
                            $("#choose-btn-ebook .btn-ebook span").html(price);
                            $("#choose-btn-ebook").show().find(".btn-ebook").attr("href", "//e.jd.com/" + ebookId + ".html");
                            $("#choose-btn-ebook").addClass('btn-lg')
                            Event.fire({
                                type: 'onBtnChange',
                            })
                        }
                    }

                    /// 排名
                    var rankDesc = rankInfo && rankInfo.rankDesc;
                    if(rankDesc) {
                        $("#summary-order").show()
                        $("#summary-order .dd").html(rankDesc) 
                    }else{
                        $("#summary-order").hide()
                    }
                    // var sortname = $("#crumb-wrap .crumb").find(".item").eq(2).find("a").eq(0).html();
                    // if (rank && (rank > 0) && sortname) {
                    //     if (cfg.isSelfBook && rank <= 500) {
                    //         $('<div id="summary-order" class="li" clstag="shangpin|keycount|product|' + pType + '_paihangbang_0"><div class="dt">排&#x3000;&#x3000;名 &nbsp;</div><div class="dd">自营 <a href="//book.jd.com/booktop/' + pageConfig.product.cat[1] + '-0-0-0-10001-1.html" target="_blank">' + sortname +
                    //             '销量榜 </a>第 <font style="color:red;">' + rank + '</font> 位</div></div>').insertBefore("#summary-stock");
                    //     } else if (cfg.isSelfMvd && rank <= 100) {
                    //         $('<div id="summary-order" class="li" clstag="shangpin|keycount|product|' + pType + '_paihangbang"><div class="dt">排&#x3000;&#x3000;名 &nbsp;</div><div class="dd">自营 <a href="//mvd.jd.com/mvdtop-' + pageConfig.product.cat[0] + '-1-0-1.html" target="_blank">' + sortname +
                    //             '销量榜 </a> <font style="color:red;">' + rank + '</font> 位</div></div>').insertBefore("#summary-stock");
                    //     }
                    // }
            //     },
            //     error: function (xhr, textStatus, error) { 
            //         console && console.error('//cd.jd.com/bookj接口调用失败，详情如下：\n', error);
            //     }
            // });
        }
    }

    /// 侧边栏“店长推荐”和“店铺广告”模块
    function managerRecommended(cfg){
        /**
         * “店长推荐”数据请求
         * @param {Object} params
         * @param {Function} onSuccess
         * @param {Function} onFail
         * @returns
         */
         // “店长推荐”接口初始化
        function getShopRecommendData(callback) {
            var parameters = {
                skuId: pageConfig.product.skuid,
                venderId: pageConfig.product.venderId 
            };

            var body = JSON.stringify(parameters);

            Tools.getJsTokenSign({
                body: body,
                appid: "item-v3",
                functionid: "pctradesoa_shopRecommend",
                message1: "book店长推荐接口设备指纹异常",
                message2: "book店长推荐接口加固异常",
                render: function (colorParm) {
                    getRecommendData(colorParm,function(res){
                        callback(res)
                    });
                }
            });
        }

        // “店长推荐”数据请求
        function getRecommendData(colorParm, callback) { 
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
                host = pageConfig.product && pageConfig.product.colorApiDomain
            }
            $.ajax({
                url: host,
                data: colorParm,
                dataType: 'json',
                xhrFields: {
                    withCredentials: true,
                }, 
                success: function(res) {
                    callback(res)
                },
                error: function(e) {
                    console.log('店长推荐数据请求错误',e)
                }
            });
        }

        /**
         * “店长推荐”
         * @param {jQuery} $mount
         * @param {Object} data
         */
        function renderRecommendMoudle($mount, data) {
            if (
                $mount.length &&
                data && 
                $.isArray(data.goodList) &&
                data.goodList.length
            ) {
                var __html = '\
                <ul>\
                    {for item in data.goodList}\
                    {if item.extAttributes.sku_status!=0 && item.extAttributes.sku_status!=2 && item.extAttributes.sku_status!=10}\
                        <li class="fore${+item_index+1}" data-sku="${item.skuId}" onclick=\'log("dztj_pc","dztj_all_click",pageConfig.product.venderId, "${+item_index+1}","${item.skuId}",pageConfig.product.skuid, "${data.strategyId}")\'>\
                            <div class="p-img">\
                                <a href="${item.skuId|itemUrl}" title="${item.wname}" target="_blank" id="trackdztj-${item.skuId}">\
                                    <img width="160" height="160" alt="${item.wname}" class="" src="${item.skuId|imgDomain}n2/${item.imageurl}">\
                                </a>\
                            </div>\
                            <div class="p-name">\
                                <a href="${item.skuId|itemUrl}" target="_blank" title="${item.wname}">${item.wname}</a>\
                            </div>\
                            <div class="p-price" style="text-align:center;">\
                                <strong class="J-p2-${item.skuId}">￥</strong>\
                            </div>\
                        </li>\
                    {/if}\
                    {/for}\
                </ul>';

                try {
                    var $container = $('.mc', $mount);
                    $container.html(__html.process({
                        data: data,
                        _MODIFIERS: {
                            // 获取页面 URL
                            itemUrl: function(skuid) {
                                return skuid ? '//item.jd.com/' + skuid + '.html' : ''
                            },
                            // 获取图片分流地址 > http://cf.jd.com/pages/viewpage.action?pageId=61506220
                            imgDomain: function(skuid) {
                                var imgDomains = [10, 11, 12, 13, 14]
                                var num = imgDomains[skuid % 5]
                                if (skuid && num) {
                                    return '//img'+ num +'.360buyimg.com/'
                                } else {
                                    return ''
                                }
                            }
                        }
                    }));

                    // 渲染价格数据
                    var skus = [];
                    $container.find('[data-sku]').each(function() {
                        skus.push($(this).data('sku'));
                    });
                    // Tools.priceNum({
                    //     skus: skus,
                    //     $el: $container
                    // });
                    //价格渲染
                    Tools.priceNumRecommend({
                        priceList: data.priceList,
                        $el: $container
                    });

                    try{
                        var layerList = data.floatLayerList
                        if(layerList.length > 0){
                            Tools.getPcSkuLayers(layerList, 160, 160, 'trackdztj-', "2")
                        }
                    }catch(e){
                        console.log("主图浮层初始化渲染",e)
                    }
                    // 显示店长推荐模块
                    $mount.show();
                    
                } catch (err) {
                    console && console.error(err);
                    $mount.hide();
                }
            } else {
                $mount.hide();
            }
        }

         /**
         * “店铺广告”
         * @param {jQuery} $mount
         * @param {Object} data
         */
        function renderAdMoucle($mount, data) {
            if (
                $mount.length &&
                $.isArray(data) &&
                data.length
            ) {
                var __html = '\
                {for item in data} \
                    <a href="${item.address}" target="_blank" title="${item.name}" onclick=\'log("dngg_pc","dngg_all_click",pageConfig.product.venderId, "${item.id}","${item.address}")\'><img src="${item.image}" alt="${item.name}"/></a> \
                {/for}';

                try {
                    $mount.html(__html.process({data: data}));
                    $mount.show();
                } catch (err) {
                    console && console.log(err);
                }
            } else {
                $mount.hide();
            }
        }

        // getShopRecommendData().
        // done(function(res) {
        //     renderRecommendMoudle($('#sp-reco'), res && res.shopRec);
        //     renderAdMoucle($('#sp-ad'), res && res.shopAds);
        // }).
        // fail(function(xhr, status, error) {
        //     console && console.error('店长推荐模块数据调用失败，具体如下：\n' + error);
        //     // getData();  // 人气配件
        // });
        // getShopRecommendData(function(res){
        //     renderRecommendMoudle($('#sp-reco'), res && res.shopRec);
        //     renderAdMoucle($('#sp-ad'), res && res.shopAds);
        // })

    }

    function init(cfg) {
        // return;
        // bookRightMessage(cfg)
        // managerRecommended(cfg)
        // sideReco(pageConfig.product.pType);
        Event.addListener('onStockReady', function(data){
            // var price = data && data.stock && data.stock.data && data.stock.data.price
            // var isStock = data && data.stock && data.stock.data && data.stock.data.stockInfo && data.stock.data.stockInfo.isStock
            // if (price.m && price.m.length > 0) {
            //     $(".summary-price").find(".p-discount, .pricing").show();
            //     $("#page_maprice").html("￥" + price.m);
            //     $(".summary-price .p-discount").html(price.discount?'['+ price.discount +']':'');
            // } else {
            //     //没有价格不显示折扣和定价
            //     $(".summary-price").find(".p-discount, .pricing").hide();
                
            // }
            // 配件，套装，组合
        // 配件套装依赖主商品库存
            //if ( !isStock && !G.itemDisabled ) {//无库存或者上下柜
            // if (!isStock && !cfg.isOver) {
            //     $('#fitting-suit').hide();
            // } else {
            //     FittingSuit(pageConfig.product.pType);
            //     CombineBuy(pageConfig.product.pType);
            // }
            sampleRank(cfg,data)
        })
        // var $body = $('body');
        // // 图书热门推荐、店铺人气
        // $body.lazyload({
        //     type: 'fn',
        //     source: $('#J-hot-reco'),
        //     onchange: function() {
        //         require.async('MOD_ROOT/contentReco/contentReco', function(contentReco) {
        //             contentReco(pageConfig.product.pType);
        //         });

        //     }
        // });
    }
    module.exports.__id = 'book';
    module.exports.init = init;
});