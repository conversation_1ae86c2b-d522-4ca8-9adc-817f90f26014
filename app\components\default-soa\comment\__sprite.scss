
                    @mixin sprite-arrow {
                        width: 11px;
                        height: 6px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -64px -41px;
                    }
                    @mixin sprite-comment {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -48px -34px;
                    }
                    @mixin sprite-commented {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -16px -34px;
                    }
                    @mixin sprite-down {
                        width: 13px;
                        height: 7px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -0px -66px;
                    }
                    @mixin sprite-next-hover {
                        width: 19px;
                        height: 34px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -0px -0px;
                    }
                    @mixin sprite-next {
                        width: 19px;
                        height: 34px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -19px -0px;
                    }
                    @mixin sprite-praise {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -32px -34px;
                    }
                    @mixin sprite-praised {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -0px -34px;
                    }
                    @mixin sprite-prev-hover {
                        width: 19px;
                        height: 34px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -38px -0px;
                    }
                    @mixin sprite-prev {
                        width: 19px;
                        height: 34px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -57px -0px;
                    }
                    @mixin sprite-turn-left {
                        width: 16px;
                        height: 15px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -32px -50px;
                    }
                    @mixin sprite-turn-right {
                        width: 16px;
                        height: 15px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -48px -50px;
                    }
                    @mixin sprite-up {
                        width: 12px;
                        height: 7px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -64px -34px;
                    }
                    @mixin sprite-zan {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -0px -50px;
                    }
                    @mixin sprite-zaned {
                        width: 16px;
                        height: 16px;
                        background-image: url(i/__sprite.png?v=161726);
                        background-position: -16px -50px;
                    }