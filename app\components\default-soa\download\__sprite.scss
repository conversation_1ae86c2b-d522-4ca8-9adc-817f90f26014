
                    @mixin sprite-android {
                        width: 16px;
                        height: 20px;
                        background-image: url(i/__sprite.png);
                        background-position: -54px -0px;
                    }
                    @mixin sprite-extra {
                        width: 7px;
                        height: 11px;
                        background-image: url(i/__sprite.png);
                        background-position: -54px -20px;
                    }
                    @mixin sprite-ipad {
                        width: 17px;
                        height: 20px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -18px;
                    }
                    @mixin sprite-iphone {
                        width: 17px;
                        height: 20px;
                        background-image: url(i/__sprite.png);
                        background-position: -20px -0px;
                    }
                    @mixin sprite-jdRead {
                        width: 17px;
                        height: 20px;
                        background-image: url(i/__sprite.png);
                        background-position: -37px -0px;
                    }
                    @mixin sprite-pc {
                        width: 20px;
                        height: 18px;
                        background-image: url(i/__sprite.png);
                        background-position: -0px -0px;
                    }