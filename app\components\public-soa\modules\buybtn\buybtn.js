﻿define('PUBLIC_ROOT/modules/buybtn/buybtn', function(require, exports, module) {
    var Event       = require('PUBLIC_ROOT/modules/common/tools/event').Event;
    var tools = require('PUBLIC_ROOT/modules/common/tools/tools');
    var reservation = require('PUBLIC_ROOT/modules/buybtn/reservation');
    var ko          = require('PUBLIC_ROOT/modules/buybtn/ko');
    var bgm         = require('PUBLIC_ROOT/modules/buybtn/bigouma');
    var notif       = require('JDF_UNIT/notif/1.0.0/notif');
    var login       = require('JDF_UNIT/login/1.0.0/login');

    require('JDF_UI/dialog/1.0.0/dialog');

    // 购买数量加减
    window.setAmount = {
        init: function () {
            this.min = 1;
            this.max = 200;
            this.count = 1;

            this.disableAdd = false;
            this.disableReduce = true;

            this.$buyNum = $('#buy-num');
            this.$buyBtn = $("#InitCartUrl");

            this.$add = $('#choose-btns .btn-add');
            this.$reduce = $('#choose-btns .btn-reduce');

            this.matchCountKey = ['pcount', 'pCount', 'num', 'buyNum','number'];

            if (this.$buyNum.length < 1) return false;

            if (/debug=num/.test(location.href)) {
                this.$buyNum.attr('data-min', '5');
                this.$buyNum.attr('data-max', '10');
            }

            var minNum = this.$buyNum.data('min');
            var maxNum = this.$buyNum.data('max');

            if (maxNum) {
                this.max = maxNum;
            }

            // 最小购买数
            if (minNum) {
                this.min = minNum;
                this.count = minNum;
            }

            this.checkLimit();
            this.handleChange();
            this.bindEvent();
        },
        bindEvent: function () {
            var _this = this;

            this.$buyNum.unbind('change keydown keyup')
                .bind('change keydown keyup', tools.throttle($.proxy(this.handleChange, this), 500));

            setTips(this.$reduce, '{0} 最少购买 {1} 件');
            setTips(this.$add, '{0} 最多购买 {1} 件');

            function setTips($el, template) {
                $el.ETooltips({
                    close: false,
                    content: '<div class="min-buy-tips"></div>',
                    width: 150,
                    position: 'bottom',
                    zIndex: 10,
                    onOpen: function () {
                        var img = '<img src="//img20.360buyimg.com/da/jfs/t2734/145/4239060100/1006/b6d0f0d8/57b4240fN9cc48b02.png" />';
                        this.$tooltips.find('.min-buy-tips').html(template.format(img, _this.$buyNum.val()));
                    }
                });
            }
        },
        disabledReduce: function (showTips) {
            this.disableReduce = true;
            this.disableAdd = false;
            this.$reduce.addClass('disabled');
            this.$add.removeClass('disabled');
            this.$add.attr('data-disabled', '1');

            if (showTips) {
                this.$reduce.removeAttr('data-disabled');
            } else {
                this.$reduce.attr('data-disabled', '1');
            }
        },
        disabledAdd: function (showTips) {
            this.disableAdd = true;
            this.disableReduce = false;
            this.$add.addClass('disabled');
            this.$reduce.removeClass('disabled');
            this.$reduce.attr('data-disabled', '1');

            if (showTips) {
                this.$add.removeAttr('data-disabled');
            } else {
                this.$add.attr('data-disabled', '1');
            }
        },
        enabledAll: function () {
            this.disableAdd = false;
            this.disableReduce = false;
            this.$reduce.removeClass('disabled').attr('data-disabled', '1');
            this.$add.removeClass('disabled').attr('data-disabled', '1');
        },
        getVal: function () { return this.$buyNum.val(); },
        setVal: function (val) { this.$buyNum.val(val); },
        checkLimit: function () {
            var min = this.$buyNum.data('min');
            var value = Number(this.getVal());

            // 默认值是1的时候只加禁用样式，不展示tips
            if (value <= 1) this.disabledReduce();
            if (value >= this.max) this.disabledAdd(true);
            if (value > 1 && value < this.max) this.enabledAll();

            if (min) {
                if (value === this.min) {
                    this.disabledReduce(true);
                }
            }
        },
        isEmpty: function (val) { return $.trim(val) == ''; },
        isFloat: function (n) { return Number(n) === n && n % 1 !== 0; },
        add: function() {
            var value = Number(this.getVal());
            if ( this.disableAdd || this.isEmpty(value) ) return false;

            if (value > this.min) {
                this.disableReduce = false;
            }

            if ( value >= this.max ) {
                this.setDisabled(this.$add);
                this.disableAdd = true;
                return false;
            } else {
                this.disableAdd = false;
                this.setEnabled(this.$add);
                this.count++;
            }

            this.setVal(this.count);
            this.checkLimit();
            this.setBuyLink();
        },
        reduce: function() {
            var value = Number(this.getVal());
            if ( this.disableReduce || this.isEmpty(value) ) return false;

            if (value < this.max) {
                this.disableAdd = false;
            }

            if ( value <= this.min ) {
                this.setDisabled(this.$reduce);
                this.disableReduce = true;
                return false;
            } else {
                this.setEnabled(this.$reduce);
                this.disableReduce = false;
                this.count--;
            }

            this.setVal(this.count);
            this.checkLimit();
            this.setBuyLink();
        },
        handleChange: function () {
            var value = this.getVal();
            var result = null;

            // 非法字符
            if (isNaN(Number(value)) || this.isEmpty(value) || this.isFloat(Number(value))) {
                result = this.count;
            } else {
                // 小于最小值
                if (value < this.min) {
                    result = this.min;
                    this.disabledReduce(result!==1);
                }
                // 大于最大值
                if (value > this.max) {
                    result = this.max;
                    this.disabledAdd(true);
                }
            }

            if (result) {
                this.count = result;
                this.$buyNum.val(result);
            } else {
                this.count = Number(value);
            }

            this.checkLimit();
            this.setBuyLink();
        },
        modify: function() {},
        setDisabled: function ($el) { $el.attr('data-disabled', 1); },
        setEnabled: function ($el) { $el.removeAttr('data-disabled'); },
        setBuyLink: function() {
            var _this = this;

            _this.$buyBtn.each(function() {
                var $this = $(this),
                    orginHref = $this.attr("href"),
                    param = orginHref.split("?")[1],
                    res, re;

                (function() {
                    for (var h = 0; h < _this.matchCountKey.length; h++) {
                        re = new RegExp(_this.matchCountKey[h] + "=\\d+");
                        if (re.test(param)) {
                            res = orginHref.replace(re, _this.matchCountKey[h] + "=" + _this.count);
                            $this.attr("href", res);
                            return false
                        }
                    }
                })();
            });

            Event.fire({
                type: 'onNumChange',
                count: this.count
            });
        }
    };
    setAmount.init();

    var addToCartBtn = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#InitCartUrl,#InitCartUrl-mini');
            this.cName = 'btn-disable';
            this.originHref = this.$el.attr('href');
            this.href = this.$el.attr('href');

            this.bindEvent(cfg);
            return this;
        },
        reInit: function ($el) {
            var btnHTML = '<a href="'+ this.cfg.addToCartUrl +'" id="InitCartUrl" class="btn-special1 btn-lg" clstag="shangpin|keycount|product|加入购物车_1">加入购物车</a>';

            if ($('#InitCartUrl').length < 1) {
                $el.before(btnHTML);
            }
            // 用来做按钮降级用
            this.init(this.cfg);
            this.disabled();
            this.enabled();
        },
        bindEvent: function (cfg) {
            var _this = this;

            function detectBuyBtn() {
                if ( cfg.havestock ) {
                    _this.enabled();
                } else {
                    _this.disabled();
                }
            }
            Event.addListener('onStockReady', detectBuyBtn);
        },
        show: function () {
            // 合约机、预售、必购码 不显示加入购物车按钮
            var disabled = this.cfg.isHeYue || this.cfg.isYuShou || this.cfg.isBiGouMa || this.cfg.isKO;

            if (disabled) { return false; }

            this.$el.show();
        },
        hide: function () {
            this.$el.hide();
        },
        updateNum: function (href) {
            var num = $('#buy-num').val();
            if (href) {
                return href.replace(/(nums|num|pcount|buyNum|number)=\d+/g, '$1' + '=' + num)
            }
        },
        disabled: function () {
            var href = this.$el.attr('href');

            this.$el.addClass(this.cName);
            this.$el.attr('href', '#none');

            return href;
        },
        enabled: function (href) {
            href = this.updateNum(href || this.href);

            var isDisabled = this.cfg.isClosePCShow || !this.cfg.havestock;

            if ( isDisabled ) {
                return false;
            }
            this.setEnable(href)
        },
        setEnable: function (href) {
            this.$el.removeClass(this.cName);
            this.href = href;
            var chooseShopShowIndex = $('#choose-shop-show .selected').attr('data-index');
            if (chooseShopShowIndex != '0') {
                this.$el.attr('href', href);
            }
        },
        setDisable: function () {
            return this.disabled()
        }
    };

    var notify = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#btn-notify');

            // 降价通知
            notif({
                el:$('.J-notify-sale')
            });

            // 到货通知
            notif({
                el:$('.J-notify-stock')
            });

            this.bindEvent();

            return this;
        },
        show: function () {
            if (!this.cfg.havestock && !this.cfg.unSupportedArea) {
                this.$el.show();
            } else {
                this.hide();
            }
        },
        hide: function () {
            this.$el.hide();
        },
        bindEvent: function () {
            var _this = this;

            Event.addListener('onStockReady', function () {
                _this.show();
            });
        }
    };

    var inventory = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#btn-inventory');
            this.$buyBtn = $("#InitCartUrl");
            var _this = this;
            var sku = _this.cfg.skuid
            if (this.$buyBtn.length > 0) { // 购物车按钮曝光埋点
                try {
                    expLogJSON('smb_pc', 'AddCart_Expo', '{"SKUID": ' + sku + ',"Pin":' + readCookie('pin') + '}');
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('购物车按钮曝光埋点错误');
                    }
                }
            }
            Event.addListener('onStockReady', function (data) {
               var isCanAddCart = data.stock && data.stock.data && data.stock.data.canAddCart
               if(isCanAddCart && pageConfig.product && pageConfig.product.businessBuySwitch){ // 融合接口下发展示以及开关true
                    _this.$el.show()
                    try {// 加入采购清单曝光埋点
                        expLogJSON('smb_pc', 'EnterpiseCart_Expo', '{"SKUID": ' + sku + '}');
                    } catch (e) {
                        if (typeof console !== 'undefined') {
                            console.log('加入采购清单曝光埋点错误');
                        }
                    }
               }else{
                    _this.$el.hide()
               }
            });

            this.bindEvent();

            return this;
        },
        bindEvent: function () {
            var _this = this;
            var sku = _this.cfg.skuid
            $("#btn-inventory").click(function() {
                try {
                    var getPlugin = window.bproCommonContext.getPlugin;
                    var addToPurchaseListPlugin = getPlugin('addToPurchaseList');
                    // 展示加入采购清单弹窗
                    // addToPurchaseListPlugin.showModal({ skuId: sku})
                    addToPurchaseListPlugin.showModal({ skuList:[{"skuId":sku,"skuNum":$('#buy-num').val()||"1"}]}) 
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('加入采购清单插件报错');
                    }
                }
                try {// 加入采购清单点击埋点
                    log('smb_pc', 'EnterpiseCart_click', '{"SKUID ":' + sku + '}')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log('加入采购清单点击埋点错误');
                    }
                }
            })
            // 后面问号提示
            var timeoutId = 0;
            $("#choose-btns").delegate('.btn-tips', 'mouseenter', function() {
                clearTimeout(timeoutId);
                $(this).addClass('hover');
            });

             $("#choose-btns").delegate('.btn-tips', 'mouseleave', function() {
                var $this = $(this);
                timeoutId = setTimeout(function() {
                    $this.removeClass('hover');
                }, 300);
            });
        }
    };

    var jnbt = {
        init: function (cfg) {
            this.cfg = cfg;
            this.$el = $('#choose-btn-jnbt');

            if (this.$el.length) {
                this.bindEvent();
            }
            return this;
        },
        // 北京才有节能补贴
        isTargetArea: function() {
            var pid = tools.getAreaId().areaIds[0];
            return pid === 1;
        },
        changeArea: function() {
            var $jnbt = this.$el;
            if ($jnbt.length < 1) return;

            this.show();

            if (this.cfg.havestock) {
                this.enabled();
            } else {
                this.disabled();
            }
        },
        bindEvent: function() {
            Event.addListener('onStockReady', $.proxy(this.changeArea, this));

            var $jnbt = this.$el;
            var text = '<p>1. 点击参加节能补贴按钮购买，最高可减免800元！</p> \
                <p>2. 节能补贴面向北京地区的用户，需您收货地址为北京且提供相应证件证明，\
                最高可享商品金额13%的减免（上限800元），并同时支持使用京东优惠券，\
                点击参加节能补贴按钮立即体验！</p> ';

            seajs.use('PUBLIC_ROOT/modules/ETooltips/ETooltips', function () {
                $jnbt
                    .ETooltips({
                        close: false,
                        content: text,
                        width: 265,
                        position: 'bottom',
                        zIndex: 10
                    });
            });
        },
        enabled: function () {
            if (this.cfg.havestock) {
                this.$el.removeClass('btn-disable');
                this.$el.attr('href', this.$el.attr("dataurl"))
            } else {
                this.disabled();
            }
        },
        disabled: function () {
            this.$el.addClass('btn-disable').attr('href', '#none');
        },
        show: function () {
            if (this.isTargetArea()) {
                this.$el.show();
            } else {
                this.hide();
            }
        },
        hide: function () {
            this.$el.hide();
        }
    };

    var dqsBtn = function (cfg) {
        // 定期送iframe
        var $dqsBtn = $('#btn-dqs');
        var url = undefined;

        var isGuangMingMilk = checkIsGuangMingMilk();
        //光明牛奶的商品 定期送的url不一样
        if(!isGuangMingMilk){
            url = '//ding.jd.com/orderPlan/toCreateOrderPlan.action?';
        }else{
            url = '//ding-server.jd.com/period-buy-gm.html?';
        }
        
        Event.addListener('onStockReady', function () {
            if (cfg.havestock) {
                $dqsBtn.removeClass('btn-disable')
            } else {
                $dqsBtn.addClass('btn-disable')
            }
        });

        $dqsBtn.show();
        $dqsBtn.bind('click', function () {
            if (!cfg.havestock) return false;
            var param = null;
            //光明牛奶的商品 定期送的参数不一样
            if(!isGuangMingMilk){
                param = {
                    skuId: cfg.skuid,
                    buyNum: $('.buy-num').val(),
                    r: Math.random()
                };
            }else{
                param = {
                    skuId: cfg.skuid,
                    frequency:5
                };
            }

            function showPopWin() {
                if(!url || !param)return;
                $('body').dialog({
                    type: 'iframe',
                    width: 520,
                    height: 351,
                    title: '定期送',
                    autoIframe: false,
                    iframeTimestamp: false,
                    source: url + $.param(param)
                });
            }

            require.async([
                'JDF_UNIT/login/1.0.0/login',
                'JDF_UI/dialog/1.0.0/dialog'
            ], function (login) {
                login({
                    modal: true,
                    complete: showPopWin
                });
            });
        });

        function checkIsGuangMingMilk(){
            //isTimeOrder=3  且 店铺id=613998 为光明牛奶的商品
            var isTimeOrder = cfg.specialAttrs && cfg.specialAttrs.join(',').indexOf("isTimeOrder-3")!=-1;
            var curShopId = pageConfig.product.shopId;
            return isTimeOrder && curShopId && curShopId=='613998';
        }
    }

    // 租赁服务
    var leaseBtn = function (cfg) {
        var $leaseBtn = $('.choose-btn-fqy')
        var href = $leaseBtn.attr('href')
        if (!$leaseBtn.length) return;

        var $btns = $leaseBtn.add('#InitCartUrl-mini')

        // $btns.each(function () {
        //     var href = $(this).attr('href')
        //     $(this).attr({
        //         'href': '#none',
        //         'data-href': href
        //     })
        // })

        function getLease() {
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }
            $.ajax({
                url: host + '/credit/verify?',
                data: { 
                    pin: readCookie('pin'),
                    appid: 'item-v3',
                    functionId: "pc_credit_verify" 
                 },
                dataType: 'jsonp',
                success: function (r) {
                    if (r && r.accountStatus === '1') {
                        location.href = href
                    } else {
                        $('body').dialog({
                            width: 360,
                            title: '分期用服务提示',
                            type: 'text',
                            maskClose: true,
                            source: '<div class="ac"><h2>抱歉，您无法使用分期用服务</h2></div><br>\
                            <p style="padding:10px 20px;">分期用服务目前仅对开通京东白条的用户提供，请前往 <a target="_blank" class="hl_blue" href="https://bt.jd.com/v3/activity/open">https://bt.jd.com/v3/activity/open</a> 开通京东白条服务</p>\
                            <br>\
                            <div class="ac"><a href="#none" onclick="$.closeDialog()" class="btn-def">好的</a></div>\
                            <br>'
                        })
                    }
                }
            })
        }

        $btns.click(function () {
            if (!$(this).hasClass('btn-disable')) {
                login({
                    modal: true,
                    complete: function() {
                        getLease()
                    }
                });
            }
            return false;
        })
    }

    // 山姆会员专享购
    // ref: http://cf.jd.com/pages/viewpage.action?pageId=88143870
    function samText(cfg) {
        var $text = $('.J-sam-login-text')
        var loginText = '山姆会员专享商品，请<a target="_blank" href="//passport.jd.com/new/login.aspx?ReturnUrl='+ location.href +'">登录</a>确认购买资格'
        var notSamText = '山姆会员专享商品，请<a target="_blank" href="//cartv.jd.com/item/200100420635.html">成为山姆会员</a>后购买'

        if (!$text.length) return

        var isLogin = null
        var isSAM = null

        function isSam(cb) {
            $.ajax({
                url: '//cd.jd.com/sam/info?',
                dataType: 'jsonp',
                success: function(r) {
                    cb(r && r.issam && r.issam === '1')
                }
            })
        }
        function handleLoginData(d) {
            isLogin = d.login
            if (!d.login) {
                $text.html(loginText)
            } else {
                isSam(function(is) {
                    isSAM = is
                    if (!is) {
                        cfg.addToCartBtn.disabled()
                        $text.html(notSamText)
                    }
                })
            }
        }
        function handleStockData() {
            if (isLogin === false) {
                $text.html(loginText)
            } else {
                if (isSAM === false) {
                    cfg.addToCartBtn.disabled()
                    $text.html(notSamText)
                }
            }

        }

        Event.addListener('onLogin', handleLoginData)
        Event.addListener('onStockReady', handleStockData)
    }

    //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  begin
    function goCustomize2(cfg){

        var $btnGoCustomize2 = $('#btn-goCustomize2');
        if ($btnGoCustomize2.length) {
            $btnGoCustomize2.href = '//dz.jd.com/customMade/toCustomizePC.action?pid='+cfg.skuid+'&pcount='+$('#buy-num').val();

            $btnGoCustomize2.unbind('click').bind('click',function(){
                //如果无货则不跳转 去定制页面
                if($(this).hasClass('btn-disable'))return false;
                window.location.href = '//dz.jd.com/customMade/toCustomizePC.action?pid='+cfg.skuid+'&pcount='+$('#buy-num').val();
            });

            function checkDisplay() {
                if (pageConfig.product.havestock) {
                    $btnGoCustomize2.removeClass('btn-disable');
                } else {
                    $btnGoCustomize2.addClass('btn-disable');
                }
            }
            Event.addListener('onStockReady', checkDisplay)
        }
    }
    //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  end

    function init(cfg) {
        // 加入购物车
        cfg.addToCartBtn = addToCartBtn.init(cfg);

        // 到货通知
        cfg.notifyBtn = notify.init(cfg);

        // 加入采购清单
        cfg.inventoryBtn = inventory.init(cfg);

        // 必购码
        cfg.bgmBtn = bgm.init(cfg);

        // 节能补贴
        cfg.jnbtBtn = jnbt.init(cfg);

        // 预约
        cfg.reservationBtn = reservation.init(cfg);

        // 秒杀
        cfg.koBtn = ko.init(cfg);

        // 定期送
        dqsBtn(cfg)

        // 分期用
        leaseBtn(cfg)

        // 山姆会员专享购
        samText(cfg);
        
        //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  begin
        goCustomize2(cfg);
        //定制单品页 Customize=2 支持定制 也支持加车  0207  added by meiling.lu  end

        // 如果有车管家赠品，点击“购物车”按钮时，拼上参数gids
        $("#InitCartUrl").click(function(){
            var $promCarGift = $("#prom-car-gift .J-prom-gift");
            if ($promCarGift.length) {
                var carGiftId = $promCarGift.attr("data-id");
                if (carGiftId) {
                    var carHref = $(this).attr("href") + "&gids=" + carGiftId;
                    $(this).attr("href",carHref);
                }
            }

            /* 车型下传需求 产品：路平 开发：lqf  20181219
             * 文档地址：https://cf.jd.com/pages/viewpage.action?pageId=137886451
             * 简述：当用户选择 无需安装 时 点击 加入购物车 按钮，如果选择了车型 增加 modelid 参数下传
             * 拼接字段为 cm
             */
           var chooseCarBox = $('#choose-car');
           var vehicleSelectedBox = chooseCarBox.find('.vehicle.selected');
           if (chooseCarBox.length > 0 && vehicleSelectedBox.length > 0) {
               var modelId = vehicleSelectedBox.attr('data-modelid');
               if (modelId) {
                   var hasModelIdHref = $(this).attr('href') + '&cm=' + modelId;
                   $(this).attr('href', hasModelIdHref);
               }
           }
        });
    }

    module.exports.__id = 'buybtn';
    module.exports.init = init;
    module.exports.setAmount = setAmount;
    module.exports.addToCartBtn = addToCartBtn;
});
