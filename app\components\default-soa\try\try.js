define('MOD_ROOT/try/try', function(require, exports, module) {
    var Countdown = require('MOD_ROOT/common/tools/tools').Countdown
    var pager = require('JDF_UI/pager/1.0.0/pager')

    var Event = require('MOD_ROOT/common/tools/event').Event
    var G = require('MOD_ROOT/common/core')

    var template = {}

    template.list =
        '\
        <div class="com-try-title">试用报告</div>\
        <div class="com-table-header">\
            <span class="item column1">试用心得</span>\
            <span class="item column2">试用满意度</span>\
            <span class="item column3">上传时间</span>\
            <span class="item column5">评价者</span>\
        </div>\
        <div class="com-table-main">\
            {for list in reports}\
            <div class="comments-item">\
                <table class="com-item-main clearfix">\
                    <tbody>\
                        <tr>\
                            <td class="com-i-column column1" clstag="shangpin|keycount|product|sybg-nr">\
                                <div class="p-comment">\
                                    <span class="desc">\
                                        <div class="J-try-report-detail try-report-detail">\
                                            <strong>商品亮点及建议：</strong><br>\
                                            <div class="content J-thumb-content thumb-content">${list.recommendation}</div>\
                                        </div>\
                                        <div class="J-try-report-detail try-report-detail">\
                                            <div class="J-thumb-descriptions thumb-descriptions">\
                                                <strong>使用过程及感受：</strong><br>\
                                                <div class="content J-thumb-content thumb-content">${list.descriptions[0].content}</div>\
                                            </div>\
                                            <div class="J-all-descriptions all-descriptions">\
                                                <strong>使用过程及感受：</strong><br>\
                                                <div class="content">\
                                                    {for description in list.descriptions}\
                                                        <div>${description.content}</div>\
                                                        <div>\
                                                            <img data-src="//img10.360buyimg.com/trial/s418x418_${description.image}" alt="${description.content}">\
                                                        </div><br>\
                                                    {/for}\
                                                </div>\
                                            </div>\
                                        </div>\
                                        <div class="try-report-show-number J-try-report-show-number">\
                                            <span class="show-number">共${list.descriptions.length}张图片</span>\
                                            <a class="J-hide-more-try-report view-show" href="#none">收起详情 \uFE3D</a>\
                                        </div>\
                                    </span>\
                                </div>\
                                <div class="J-p-show-img p-show-img clearfix">\
                                    <table>\
                                        <tbody>\
                                            <tr>\
                                                {var tryReportDescriptionImagesLen = 0}\
                                                {for description in list.descriptions}\
                                                    {if tryReportDescriptionImagesLen < 3}\
                                                        <td>\
                                                            <div>\
                                                                <img src="//img10.360buyimg.com/trial/s80x80_${description.image}" alt="">\
                                                            </div>\
                                                        </td>\
                                                    {/if}\
                                                    {eval}\
                                                        tryReportDescriptionImagesLen++;\
                                                    {/eval}\
                                                {/for}\
                                            </tr>\
                                        </tbody>\
                                    </table>\
                                    <span class="show-number">共${list.descriptions.length}张图片</span>\
                                    <a class="J-show-more-try-report view-show" href="#none">展开详情 \uFE3E</a>\
                                </div>\
                            </td>\
                            <td class="com-i-column column2">\
                                <div class="grade-star g-star${list.score}">&nbsp;</div>\
                            </td>\
                            <td class="com-i-column column3">\
                                <div class="p-comment">\
                                    <b class="time">${list.created}</b>\
                                </div>\
                            </td>\
                            <td class="com-i-column column5">\
                                <div class="user-item">\
                                {if typeof list.user.img!=="undefined"&&list.user.img!==null}\
                                    <img src="${list.user.img}" width="16px" height="16px" alt="" class="user-ico" />\
                                {else}\
                                    <img src="//img10.360buyimg.com/img/jfs/t8725/71/1440583117/12168/6dd8e72c/59ba365cN959f668b.jpg" width="16px" height="16px" alt="${list.user.nickname}" class="user-ico" />\
                                {/if}\
                                <span class="user-name">${list.user.nickname}</span></div>\
                                <div class="user-item" style="display: none;"><span class="u-vip-level" style="color:#088000">${list.user.newUserLevel}</span></div>\
                            </td>\
                        </tr>\
                    </tbody>\
                </table>\
            </div>\
            {/for}\
        </div>\
        <div class="com-table-footer">\
            <div class="ui-page-wrap clearfix">\
                <div class="ui-page" clstag="shangpin|keycount|product|sybg-fy"></div>\
            </div>\
        </div>'

    var Report = {
        init: function($obj, cfg) {
            this.cfg = cfg

            this.$tryReport = $obj

            this.data = null

            this.bindEvent()
            return this
        },
        getData: function(page) {
            var _this = this

            this.page = page
            var host = '//api.m.jd.com'
            if(pageConfig.product && pageConfig.product.colorApiDomain){
            host = pageConfig.product && pageConfig.product.colorApiDomain
        }

            this.url =
            host + '/tryReport?skuId={skuId}&pageStart={start}&cat={cat}&appid=item-v3&functionId=pc_tryReport'
 
            this.url = this.url
                .replace('{skuId}', _this.cfg.skuid)
                .replace('{cat}', _this.cfg.cat.join(','))
                .replace('{start}', page)

            if (/debug=verderTryReport/.test(location.href)) {
                this.url =
                host + '/tryReport?skuId=1133499&pageStart=7&cat=1316,1387,1425&appid=item-v3&functionId=pc_tryReport'
            }

            $.ajax({
                url: this.url,
                dataType: 'jsonp',
                cache: true,
                jsonpCallback: 'fetchJSON_tryReport',
                scriptCharset: 'gbk',
                success: function(r) {
                    _this.setData(r)
                }
            })
        },
        setData: function(r) {
            var _this = this

            //没有试用报告
            if (!r) {
                this.$tryReport.html('')
                return
            }

            //接口异常
            if (!r.reports) {
                this.$tryReport.html('')
                return
            }

            _this.data = r

            this.setCommentCount(r.count)

            _this.dataFilter(r)

            this.$tryReport.html(template.list.process(r))

            this.pageNav = this.$tryReport.find('.ui-page')
            this.setPageNav(r)
            this.$tryReport.lazyload({
                source: 'data-src'
            })
        },
        dataFilter: function(r) {
            $.each(r.reports, function(i, n) {
                //过滤没有图片的报告
                $.each(n.descriptions, function(ii, nn) {
                    if (nn && !nn.image) {
                        n.descriptions.splice(ii, 1)
                    }
                })

                //获取新等级
                n.user.newUserLevel = G.getNewUserLevel(n.user.userLevel)
            })

            return r
        },
        setPageNav: function(data) {
            var _this = this
            this.pageNav.pager({
                total: data.count,
                pageSize: 5,
                currentPageClass: 'ui-page-curr',
                currentPage: _this.page + 1,
                pageHref: '#try-report',
                prevClass: 'ui-pager-prev',
                nextClass: 'ui-pager-next',
                prevText: '上一页',
                nextText: '下一页',
                callback: function(pageId) {
                    var page = pageId - 1
                    _this.getData(page)
                }
            })
        },
        bindEvent: function() {
            var _this = this
            _this.$tryReport.delegate(
                '.J-show-more-try-report',
                'click',
                _this.showMoreReport
            )
            _this.$tryReport.delegate(
                '.J-hide-more-try-report',
                'click',
                _this.hideMoreReport
            )

            _this.$tryReport.delegate(
                '.J-try-report-detail, .J-p-show-img td',
                'click',
                function() {
                    var $me = $(this),
                        $thisTd = $me.parents('.com-i-column')
                    var isShowMore = $thisTd.attr('data-isShowMore')
                    if (isShowMore === '1') {
                        _this.hideMoreReport.call(this)
                    } else {
                        _this.showMoreReport.call(this)
                    }
                }
            )

            Event.addListener('onCommentTemplateReady', function() {
                if (_this.data) {
                    _this.setCommentCount(_this.data.count)
                }
            })
        },
        showMoreReport: function() {
            var _this = $(this),
                $thisTd = _this.parents('.com-i-column')
            $thisTd.attr('data-isShowMore', 1)
            $thisTd.find('.J-thumb-descriptions').hide()
            $thisTd.find('.J-p-show-img').hide()
            $thisTd.find('.J-all-descriptions').show()
            $thisTd.find('.J-try-report-show-number').show()
            $thisTd.find('.J-thumb-content').removeClass('thumb-content')
            $thisTd.parents('table').lazyload({
                source: 'data-src'
            })
        },
        hideMoreReport: function() {
            var _this = $(this),
                $thisTd = _this.parents('.com-i-column')
            $thisTd.attr('data-isShowMore', 0)
            $thisTd.find('.J-thumb-descriptions').show()
            $thisTd.find('.J-p-show-img').show()
            $thisTd.find('.J-all-descriptions').hide()
            $thisTd.find('.J-try-report-show-number').hide()
            $thisTd.find('.J-thumb-content').addClass('thumb-content')

            var thisTdTop = $thisTd.offset().top,
                JDetailTabHeight = $('#detail .pro-detail-hd-fixed').height(),
                bodyScrollTop = $('html').scrollTop() || $('body').scrollTop()

            if (thisTdTop - JDetailTabHeight < bodyScrollTop) {
                $('html, body').scrollTop(thisTdTop - JDetailTabHeight - 20)
            }
        },
        setCommentCount: function(count) {
            var $tryReportBtn = $('.J-comments-list').find('.J-try-report-btn')
            $tryReportBtn.show().find('em').html('(' + count + ')')
            $tryReportBtn.find('a').attr('href', '#none')
            $tryReportBtn.click(function() {
                $('html,body').scrollTop($('#try-report').offset().top - 80)
            })
        }
    }

    var Entry = {
        init: function($el, cfg) {
            this.$el = $el
            this.cfg = cfg

            if (!$el.length) return false

            this.get()
            this.setPrice()
        },
        get: function() {
            $.ajax({
                url:
                    '//try.jd.com/activity/getBySku?source=&sku=' +
                        this.cfg.skuid,
                dataType: 'jsonp',
                success: $.proxy(this.set, this)
            })
        },
        set: function(r) {
            if (!r || !r.data) return false

            this.$el.show()
            $('[data-anchor="#try-holder"]').show()
            this.$el.find('.J-count').html(r.data.actSupplyCount)
            this.$el.find('.J-a-count').html(r.data.applyCount)

            this.setBtnLink(r.data.actId)
            this.setCountdown(r.data.actEnd)
        },
        setPrice: function () {
            var _this = this
            Event.addListener('onPriceReady', function () {
                //拼购不显示其他价格
                if(!pageConfig.product.isYuShou && $('#banner-miaosha').length <= 0 && _this.cfg.skuMarkJson && _this.cfg.skuMarkJson.pg) return
                _this.$el.find('.p-price strong').html('￥' + _this.cfg.jp.toFixed(2))
            })
        },
        setBtnLink: function (actId) {
            var $btn = this.$el.find('.btn-primary')
            var originHref = $btn.attr('href')

            if (!$btn.attr('data-href')) {
                $btn.attr('data-href', originHref)
            }
            $btn.attr('href', $btn.attr('data-href').replace('{actid}', actId))
        },
        setCountdown: function(et) {
            var now = +new Date()
            if (!et) return false

            if (now <= et) {
                var $cd = this.$el.find('.J-time-left')
                var tpl = '剩余 <strong>{0}</strong>天 <strong>{1}</strong>时 <strong>{2}</strong>分 <strong>{3}</strong>秒'
                new Countdown(parseInt((et - now)), function(res) {
                    $cd.html(tpl.format(res.d, res.h, res.m, res.s))
                })
            }
        }
    }

    module.exports.__id = 'try'
    module.exports.Report = Report
    module.exports.Entry = Entry
})
